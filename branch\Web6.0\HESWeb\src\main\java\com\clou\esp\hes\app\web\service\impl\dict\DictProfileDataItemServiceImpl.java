/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictProfileDataItem{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-02-26 09:36:52
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.dict;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.dict.DictProfileDataItemDao;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;
import com.clou.esp.hes.app.web.model.dict.DictProfileDataItem;
import com.clou.esp.hes.app.web.service.dict.DictProfileDataItemService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@Component
@Service("dictProfileDataItemService")
public class DictProfileDataItemServiceImpl  extends CommonServiceImpl<DictProfileDataItem>  implements DictProfileDataItemService {

	@SuppressWarnings("rawtypes")
	@Resource
	private DictProfileDataItemDao dictProfileDataItemDao;
	
	@SuppressWarnings("unchecked")
	@Autowired
    public void setCommonService() {
        super.setCommonService(dictProfileDataItemDao);
    }
	public DictProfileDataItemServiceImpl() {}
	
	
	@SuppressWarnings("unchecked")
	@Override
	public List<DictProfileDataItem> getProfileDataitemList(
			DictProfileDataItem entity) {
		return dictProfileDataItemDao.getProfileDataitemList(entity);
	}
	
	@Override
	public JqGridResponseTo unBindForJqGrid(JqGridSearchTo jqGridSearchTo) {
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		PageInfo<DictDataitem> pageInfo = new PageInfo<DictDataitem>(this.dictProfileDataItemDao.unBindForJqGrid(jqGridSearchTo));
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}
	
}