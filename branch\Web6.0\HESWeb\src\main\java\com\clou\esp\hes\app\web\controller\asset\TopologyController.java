/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetCommunicator{ } 
 * 
 * 摘    要： GPRS Module
DCU
Gateway
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-06 08:04:54
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.asset;


import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.model.asset.AssetCommunicator;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.data.DataCalcObj;
import com.clou.esp.hes.app.web.model.data.DataCalcObjDto;
import com.clou.esp.hes.app.web.model.data.DataComminicationStatus;
import com.clou.esp.hes.app.web.model.data.DataMdInsMinutely;
import com.clou.esp.hes.app.web.model.data.DataMeterEvent;
import com.clou.esp.hes.app.web.service.asset.AssetCommunicatorService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.data.DataComminicationStatusService;
import com.clou.esp.hes.app.web.service.data.DataMdInsMinutelyService;
import com.clou.esp.hes.app.web.service.data.DataMeterEventService;
import com.clou.esp.hes.app.web.service.report.DictReportService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;

@Controller
@RequestMapping("/topologyController")
public class TopologyController extends BaseController{

	@Resource
	private DataMdInsMinutelyService  dataMdInsMinutelyService;
	@Resource
	private AssetMeterService         meterService;
	@Resource
	private AssetCommunicatorService  commService;
	@Resource
	private DataMeterEventService     eventService;
	@Resource
	private DataComminicationStatusService commStatusService;
	@Resource
	private DictReportService 			dictReportService;
	
	
	@RequestMapping(value  = "topologyView")
    public ModelAndView topologyView(HttpServletRequest request, Model model) {
        return new ModelAndView("/asset/topologyView");
    }
	
	
	@RequestMapping(value = "getTipsInfo")
	@ResponseBody
	public AjaxJson getTipsInfo(String type,String sn,String status,String calcObjId,HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			DataMdInsMinutely  insInfo = new DataMdInsMinutely();
			String deviceId="";
			
			if(!"2".equals(type)&&!"5".equals(type)) {
				AssetMeter meter = new AssetMeter();
				meter.setSn(sn);
				AssetMeter meterTmp=meterService.get(meter);
				if(meterTmp!=null) {
					deviceId=meterTmp.getId();
				}
			}else {
				AssetCommunicator comm = new AssetCommunicator();
				comm.setSn(sn);
				AssetCommunicator commTmp = commService.get(comm);
				if(commTmp!=null) {
					deviceId=commTmp.getId();
				}
			}
			
			
			if(StringUtils.isNotEmpty(deviceId)) {
				//电表信息
				if(!"2".equals(type)&&!"5".equals(type)) {
					//获取电压信息
					insInfo=dataMdInsMinutelyService.getLastInfoByDeviceId(deviceId);
//					String ss=dataMdInsMinutelyService.getLastProfileDaily(deviceId);
//					if(insInfo==null) {
//						insInfo =new DataMdInsMinutely();
//					}
//					insInfo.setValue4(ss);
				}
				
				//获取告警信息
				if("2".equals(status)) {
					List<DataMeterEvent> events= eventService.getTodayEventByDeviceId(deviceId, "300");
					insInfo.setEvents(events);
				}
				//如果是线路终端，显示线损
				if("4".equals(type)&&StringUtils.isNotEmpty(calcObjId)) {
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
					Date yesterday=DateUtils.getDateAdd(sdf.parse(sdf.format(new Date())), Calendar.DATE, -1);
					List<String> ids = Lists.newArrayList();
					ids.add(calcObjId);
					JqGridSearchTo jqGridSearchTo = new JqGridSearchTo();
					Map<String,Object> map=jqGridSearchTo.getMap();
					map.put("calaObjIds", ids);
					map.put("startTime", yesterday);
					map.put("endTime", yesterday);
					List<DataCalcObj> calcObjs = this.dictReportService.getDataCalcObj(jqGridSearchTo);
					if(calcObjs!=null&&calcObjs.size()>0) {
						insInfo.setDataCalcObj(calcObjs.get(0));
					}
				}
			}
			
			j.setObj(insInfo);
		}catch(Exception ex) {
			ex.printStackTrace();
			j.setSuccess(false);
		}
		return j;
	}
	
	
	
	/**
     * 删除dataComminicationStatus信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "getDeviceStatus")
    @ResponseBody
    public AjaxJson getDeviceStatus(HttpServletRequest request,@RequestParam(value = "snType[]") String[] snType) {
        AjaxJson j=new AjaxJson();
        try {
        	
        	Map<String,String> meterMap = Maps.newHashMap();
        	Map<String,String> commMap = Maps.newHashMap();
        	Map<String,String> returnMap = Maps.newHashMap();
        	
        	
        	for(String str:snType) {
        		String [] arr = str.split("_");
        		String sn = arr[0];
        		String type=arr[1];
        		if(!"2".equals(type)&&!"5".equals(type)) {
        			//电表
        			meterMap.put(sn, type);
        		}else {
        			//集中器
        			commMap.put(sn, type);
        		}
        	}
        	
        	//获取离线状态
        	if(commMap.size()>0) {
        		List<DataComminicationStatus> statusList=commStatusService.getCommStatus(new ArrayList<>(commMap.keySet()));
                if(statusList!=null) {
                	for(DataComminicationStatus status:statusList) {
                		String type =commMap.get(status.getCommSn());
                		returnMap.put(status.getCommSn()+"_"+type, "0");
                	}
                }
        	}
        	
        	
        	Map<String,String> deviceIdMap=Maps.newHashMap();
        	
        	//获取告警状态
        	if(meterMap.size()>0) {
        		List<AssetMeter> meters =meterService.getBySns(new ArrayList<>(meterMap.keySet()));
        		if(meters!=null) {
        			for(AssetMeter meter:meters) {
        				deviceIdMap.put(meter.getId(), meter.getSn());
        			}
        		}
        	}
        	
        	
        	if(commMap.size()>0) {
        		List<AssetCommunicator> comms = commService.getBySns(new ArrayList<>(commMap.keySet()));
        		if(comms!=null) {
        			for(AssetCommunicator comm:comms) {
        				deviceIdMap.put(comm.getId(), comm.getSn());
        			}
        		}
        	}
        	
        	List<String> events=null;
        	if(deviceIdMap.size()>0) {
        		events = this.eventService.getTodayEventByDeviceIds(new ArrayList<String>(deviceIdMap.keySet()));
        	}
        	
        	if(events!=null) {
        		for(String str:events) {
            		String [] arr = str.split("_");
            		String type =arr[1];
            		String deviceId = arr[0];
            		String sn =deviceIdMap.get(deviceId);
            		if("1".equals(type)) {
            			//电表
            			if(StringUtils.isNotEmpty(meterMap.get(sn))) {
            				returnMap.put(sn+"_"+meterMap.get(sn), "2");
            			}
            		}else if("2".equals(type)){
            			//集中器
            			if(StringUtils.isNotEmpty(commMap.get(sn))) {
            				returnMap.put(sn+"_"+commMap.get(sn), "2");
            			}
            		}
            	}
        	}
        	j.setObj(returnMap);
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg("Abnormal operation");
        }
        return j;
    }
	
}



