
//第一个饼图
var myChart1 = echarts.init(document.getElementById('main1'));
//曲线图
var myChart2 = echarts.init(document.getElementById('main2'));
//第2个饼图
var myChart3 = echarts.init(document.getElementById('main3'));
var myChart4 = echarts.init(document.getElementById('main4'));
var myChart5 = echarts.init(document.getElementById('main5'));
var myChart6 = echarts.init(document.getElementById('main6'));
var myChart7 = echarts.init(document.getElementById('main7'));
var homeOverallDaily = $('#homeOverallDaily').val();
var supplyStatisticDaily = $('#supplyStatisticDaily').val();
var supplyStatistic15Daily = $('#supplyStatistic15Daily').val();
var supplyStatistic30Daily = $('#supplyStatistic30Daily').val();

var supplyStatistic6Month = $('#supplyStatistic6Month').val();
var supplyStatistic12Month = $('#supplyStatistic12Month').val();

var salesStatisticOfCustomer = $('#salesStatisticOfCustomer').val();

myChart1.resize();
myChart2.resize();
myChart3.resize();
myChart4.resize();
myChart5.resize();
myChart6.resize();
myChart7.resize();
autoHeight();
function autoHeight() {
	var height = document.body.clientHeight;
	$("#main1").height(height - 380);
	$("#main2").height(height - 430);
	$("#main3").height(height - 360);
	$("#main4").height(height - 360);
	$("#main5").height(height - 435);
	$("#main6").height(height - 390);
	$("#main7").height(height - 430);
	if(height <=768 ){
		$("#main1").height(height - 290);
		$("#main2").height(height - 310);
		$("#main3").height(height - 260);
		$("#main4").height(height - 260);
		$("#main5").height(height - 315);
		$("#main6").height(height - 270);
		$("#main7").height(height - 310);
	}
}




function loadCountPie(countList,title) {

	option1 = {
		backgroundColor: '#fff',
		title: {
			text: title ,
			left: 'center',
			top:10,
			left: 30,
			textStyle: {
				color: '#767373',
				fontSize: 14,
			}

		},
		itemStyle: {
			normal: {
				// 设置扇形的阴影
				shadowBlur: 30,
				shadowColor: 'rgba(0,0,0,0.5)',
				shadowOffsetX:10,
				shadowOffsetY: 5
			}
		},
		tooltip: {
			trigger: 'item',
			formatter: "{b} : {c} ({d}%)"
		},
		color: ['rgb(252,157,154)','rgb(249,205,173)','rgb(200,200,169)','rgb(131,175,155)','rgb(254,67,101)'],
		series : [
			{
				type: 'pie',
				radius : '65%',
				center: ['40%', '55%'],
				selectedMode: 'single',
				data:countList,
				labelLine:{
					normal:{
						length:5,  // 改变标示线的长度
						lineStyle: {
							fontWeight: 800
						}
					},
				},
				label: {
					normal: {
						textStyle: {
							fontWeight:  800
						}
					}
				},

				itemStyle: {
					emphasis: {
						shadowBlur: 10,
						shadowOffsetX: 0,
						shadowColor: 'rgba(0, 0, 0, 1)'
					}
				}
			}
		]
	};
	myChart1.setOption(option1);
	myChart1.resize();
}



function loadCountPie4(countList,title) {

	option4 = {
		backgroundColor: '#fff',

		title: {
			text: title ,
			left: 'center',
			top:10,
			left: 30,
			textStyle: {
				color: '#767373',
				fontSize: 14,
			}

		},

		itemStyle: {
			normal: {
				// 设置扇形的阴影
				shadowBlur: 30,
				shadowColor: 'rgba(0,0,0,0.5)',
				shadowOffsetX:10,
				shadowOffsetY: 5
			}
		},
		tooltip: {
			trigger: 'item',
			formatter: "{b} : {c} ({d}%)"
		},
		color: ['rgb(252,157,154)','rgb(249,205,173)','rgb(200,200,169)','rgb(131,175,155)','rgb(254,67,101)'],
		series : [
			{
				type: 'pie',
				radius : '65%',
				center: ['50%', '55%'],
				selectedMode: 'single',
				data:countList,
				labelLine:{
					normal:{
						length:5,  // 改变标示线的长度
						lineStyle: {
							fontWeight: 800
						}
					},
				},
				label: {
					normal: {
						textStyle: {
							fontWeight:  800
						}
					}
				},

				itemStyle: {
					emphasis: {
						shadowBlur: 10,
						shadowOffsetX: 0,
						shadowColor: 'rgba(0, 0, 0, 1)'
					}
				}
			}
		]
	};
	myChart4.setOption(option4);
	myChart4.resize();
}

function getSupplyStatisticByType(type) {
	var startDay;
	var endDay = $("#endDay").val();
	var title = supplyStatisticDaily;
	// type = 1  [前7天]
	// type = 2  [前15天]
	// type = 3  [前30天]
	switch (type) {
		case 1 :
			startDay = $("#startDay").val();
			title = supplyStatisticDaily;
			break;
		case 2 :
			startDay = $("#start15Days").val();
			title = supplyStatistic15Daily;
			break;
		case 3 :
			startDay = $("#start30Days").val();
			title = supplyStatistic30Daily;
			break;
		default:
			startDay = $("#startDay").val();
	}
	getSupplyStatistic(startDay,endDay,title);
}
function getSupplyStatistic(startDay,endDay,charttitle) {

	myChart5.showLoading();
	setTimeout("myChart5.hideLoading(); ", 30000);

	if (startDay == null || startDay == '') {
		return;
	}
	if (endDay == null || endDay == '') {
		return;
	}
	var orgIdValue = $("#selectOrgId").val();

	$.ajax({
		type : 'POST',
		url : getRootPathWeb() + '/systemController/getSupplyStatistic.do',
		data : 'startTv=' + startDay + '&&endTv=' + endDay +'&&defaultOrgId='+orgIdValue,
		dataType : 'json',
		success : function(data) {
			myChart5.hideLoading();
			var tvList = null;
			var importExportDiagramList = null;
			if (data.success) {
				tvList = data.attributes.tvList;
				importExportDiagramList = data.attributes.importExportDiagramList;
			}


			$('#supplyStaticTbody').empty();
			var newTables = "";
			for (var i = 0; i < importExportDiagramList.length; i++) {
				var time = importExportDiagramList[i].time;
				var value = importExportDiagramList[i].value;
				newTables += "<tr><td class='td1'><p>"+ time +"</p></td><td>"+ value +"</td></tr>";
			}

			// 更新Supply Statistic表格
			$('#supplyStaticTbody').html(newTables);

			var option5 = {
				title: {
					text: charttitle,
					top: 0,
					left: 12,
					itemGap: 20,
					textStyle: {
						color: '#767373',
						fontSize: 14,
					}

				},
				color : [ '#33ccdb' ],
				tooltip : {
					trigger : 'axis',
					formatter : function(params) {
						var result = params[0].seriesName + '</br>';
						for (var i = 0, l = params.length; i < l; i++) {
							params.forEach(function(item) {
								result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'
									+ item.color + '"></span>'
									+ params[i].name + ' : ' +thousandBit( params[i].value) + 'kWh';
							});
						}
						return result;
					},
					axisPointer : { // 坐标轴指示器，坐标轴触发有效
						type : 'cross' // 默认为直线，可选为：'line' | 'shadow'
					}
				},
				grid : {
					top : '13%',
					left : '3%',
					right : '4%',
					bottom : '3%',
					containLabel : true
				},
				xAxis : [ {
					type : 'category',
					data : tvList,
					boundaryGap: false,
					/*axisTick : {
                        alignWithLabel : true
                    }*/
				} ],
				yAxis : [ {
					axisLabel : {
						formatter : '{value} '
					},
					type : 'value',
					scale : true,
					min : 0,
					splitArea : {
						show : true
					},
				} ],
				series : [ {
					name : i18n.t('scheduleReadsReport.dMeterInteRate'),
					type : 'line',
					areaStyle: {normal: {}},
					smooth: true,
					itemStyle: {
						normal: {
							color: '#33ccdb',

							areaStyle: {
								type: 'default',
								opacity: 0.5,
							}
						}
					},
					/*barWidth : '60%',*/
					data : importExportDiagramList,
					label : {
						normal : {
							show : true,
							position : 'top',
							formatter : function(c) {
								var f = parseFloat(c.value);
								if (isNaN(f)) {
									return false;
								}
								var f = Math.round(c.value * 100) / 100;

								return thousandBit(f);
							}
						}
					},
					cursor : 'default',
					itemStyle: {
						normal: {
							color: '#c23531',
							shadowBlur: 200,
							shadowColor: 'rgba(0, 0, 0, 0.200)'
						}
					},

				} ]
			};

			myChart5.setOption(option5);
			myChart5.resize();
		},
		error : function(msg) {
		}
	});
}

function getDailyMeterReadsIntegrity5(tvList,importExportDiagramList) {

	if(Array.isArray(tvList) && tvList.length === 0){
		console.log('是空数组');
	}

	var option5 = {
		title: {
			text: supplyStatisticDaily,
			top: 0,
			left: 12,
			itemGap: 20,
			textStyle: {
				color: '#767373',
				fontSize: 14,
			}

		},
		color : [ '#33ccdb' ],
		tooltip : {
			trigger : 'axis',
			formatter : function(params) {
				var result = params[0].seriesName + '</br>';
				for (var i = 0, l = params.length; i < l; i++) {
					params.forEach(function(item) {
						result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'
							+ item.color + '"></span>'
							+ params[i].name + ' : ' +thousandBit( params[i].value) + 'kWh';
					});
				}
				return result;
			},
			axisPointer : { // 坐标轴指示器，坐标轴触发有效
				type : 'cross' // 默认为直线，可选为：'line' | 'shadow'
			}
		},
		grid : {
			left : '3%',
			right : '4%',
			bottom : '10%',
			containLabel : true
		},
		xAxis : [ {
			type : 'category',
			data : tvList,
			boundaryGap: false,
			/*axisTick : {
                alignWithLabel : true
            }*/
		} ],
		yAxis : [ {
			axisLabel : {
				formatter : '{value} '
			},
			type : 'value',
			scale : true,
			min : 0,
			splitArea : {
				show : true
			},
		} ],
		series : [ {
			name : i18n.t('scheduleReadsReport.dMeterInteRate'),
			type : 'line',
			areaStyle: {normal: {}},
			smooth: true,
			itemStyle: {
				normal: {
					color: '#33ccdb',

					areaStyle: {
						type: 'default',
						opacity: 0.5,
					}
				}
			},
			/*barWidth : '60%',*/
			data : importExportDiagramList,
			label : {
				normal : {
					show : true,
					position : 'top',
					formatter : function(c) {
						var f = parseFloat(c.value);
						if (isNaN(f)) {
							return false;
						}
						var f = Math.round(c.value * 100) / 100;

						return thousandBit(f);
					}
				}
			},
			cursor : 'default',
			itemStyle: {
				normal: {
					color: '#c23531',
					shadowBlur: 200,
					shadowColor: 'rgba(0, 0, 0, 0.200)'
				}
			},

		} ]
	};
	myChart5.setOption(option5);
	myChart5.resize();
}

function getDailyMeterReadsIntegrityByType(type) {
	var startDay;
	var endDay = $("#endDay").val();
	// type = 1  [前7天]
	// type = 2  [前15天]
	// type = 3  [前30天]
	switch (type) {
		case 1 :
			startDay = $("#startDay").val();
			break;
		case 2 :
			startDay = $("#start15Days").val();
			break;
		case 3 :
			startDay = $("#start30Days").val();
			break;
		default:
			startDay = $("#startDay").val();
	}
	getDailyMeterReadsIntegrity(startDay,endDay);
}

function getDailyMeterReadsIntegrity(startDay,endDay) {

	console.log('---------------------------------')

	myChart2.showLoading();
	setTimeout("myChart2.hideLoading(); ", 30000);
	//var startDay = $("#startDay").val();
	//var endDay = $("#endDay").val();
	if (startDay == null || startDay == '') {
		return;
	}
	if (endDay == null || endDay == '') {
		return;
	}
	var orgIdValue = $("#selectOrgId").val();
	var profileId = $("#selectProfileCycleType").val();
	console.log('home profileId: ' + profileId)
	$.ajax({
		type : 'POST',
		url : getRootPathWeb() + '/dataIntegrityController/getDailyMeterReadsIntegrity.do',
		data : 'startTv=' + startDay + '&&endTv=' + endDay + '&&type=day&&defaultOrgId='+orgIdValue + '&profileId=' + profileId,
		dataType : 'json',
		success : function(data) {
			myChart2.hideLoading();
			var tvList = null;
			var integrityList = null;
			if (data.success) {
				tvList = data.attributes.tvList;
				integrityList = data.attributes.integrityList;
			}
			var option = {
				// title: {
				// 	text: homeOverallDaily,
				// 	left: 'center',
				// 	top: 0,
				// 	left: 30,
				// 	itemGap: 20,
				// 	textStyle: {
				// 		color: '#767373',
				// 		fontSize: 14,
				// 	}
				//
				// },
				color : [ '#33ccdb' ],
				tooltip : {
					trigger : 'axis',
					formatter : function(params) {
						var result = params[0].seriesName + '</br>';
						for (var i = 0, l = params.length; i < l; i++) {
							params.forEach(function(item) {
								result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'
									+ item.color + '"></span>'
									+ params[i].name + ' : ' + params[i].value .toFixed(2) + '%';
							});
						}
						return result;
					},
					axisPointer : { // 坐标轴指示器，坐标轴触发有效
						type : 'cross' // 默认为直线，可选为：'line' | 'shadow'
					}
				},
				grid : {
					top : '10%',
					left : '1%',
					right : '5%',
					bottom : '2%',
					containLabel : true
				},
				xAxis : [ {
					type : 'category',
					data : tvList,
					boundaryGap: false,
					/*axisTick : {
						alignWithLabel : true
					}*/
				} ],
				yAxis : [ {
					axisLabel : {
						formatter : '{value} %'
					},
					type : 'value',
					scale : true,
					max : 100,
					min : 0,
					splitNumber : 5,
					boundaryGap : [ 0.05, 0.05 ],
					splitArea : {
						show : true
					},
				} ],
				series : [ {
					name : i18n.t('scheduleReadsReport.dMeterInteRate'),
					type : 'line',
					areaStyle: {normal: {}},
					smooth: true,
					itemStyle: {
						normal: {
							color: '#33ccdb',

							areaStyle: {
								type: 'default',
								opacity: 0.5,
							}
						}
					},
					/*barWidth : '60%',*/
					data : integrityList,
					label : {
						normal : {
							show : true,
							position : 'top',
							formatter : function(c) {
								var f = parseFloat(c.value);
								if (isNaN(f)) {
									return false;
								}
								var f = Math.round(c.value * 100) / 100;
								var s = f.toString();
								var rs = s.indexOf('.');
								if (rs < 0) {
									rs = s.length;
									s += '.';
								}
								while (s.length <= rs + 2) {
									s += '0';
								}
								return s + '%';
							}
						}
					},
					cursor : 'default',
					itemStyle: {
						normal: {
							color: '#c23531',
							shadowBlur: 200,
							shadowColor: 'rgba(0, 0, 0, 0.200)'
						}
					},

				} ]
			};
			myChart2.setOption(option);
			myChart2.resize();
		},
		error : function(msg) {
		}
	});
}




function operatingMeterChg() {
	var nSel = $("#meterSelect");
	var checkText = nSel.find("option:selected").text();
	var checkValue = nSel.val(); // 获取Select选择的Value
	var orgIdValue = $("#selectOrgId").val();

	$
		.ajax({
			async : true,
			cache : false,
			traditional : true,
			type : 'POST',
			url : getRootPathWeb()+ '/systemController/meterCount.do',
			data : {
				name : checkValue,
				defaultOrgId : orgIdValue
			},
			dataType : 'json',
			success : function(data) {

				if (data.success) {
					dataStatisticsDeviceListPieAttr = data.attributes.dataStatisticsDeviceListPie;
					deviceInfoAttr = data.attributes.deviceInfo;

					var deviceInfoList = [];
					var deviceInfo = JSON.parse(deviceInfoAttr);

					for ( var i in deviceInfo) {
						console.log(deviceInfo[i].countCurrent);
						deviceInfoList.push({
							value : deviceInfo[i].countCurrent,
							name : deviceInfo[i].name
						});
					}
					var title = i18n.t('home.classifiedStatistcOfMeter');
					loadCountPie(deviceInfoList,title);
					$("#meterCountThead").empty();

					var operatingPicDivHtml = "";

					for ( var j in dataStatisticsDeviceListPieAttr) {
						operatingPicDivHtml += "<tr>";
						operatingPicDivHtml += "<td>"+dataStatisticsDeviceListPieAttr[j].name+"</td>";
						operatingPicDivHtml += "<td>"+dataStatisticsDeviceListPieAttr[j].countCurrentFormatter+"</td>";

						operatingPicDivHtml += "<td><div class='progress'>";
						operatingPicDivHtml += "<div class='progress-bar progress-bar-danger' role='progressbar' aria-valuenow='"+dataStatisticsDeviceListPieAttr[j].percent+"' aria-valuemin='0' aria-valuemax='100' style='width: "+dataStatisticsDeviceListPieAttr[j].percent+"%;'>";

						operatingPicDivHtml += "<span class='sr-only'>"+dataStatisticsDeviceListPieAttr[j].percent+"% 完成</span>";
						operatingPicDivHtml += "</div></div></td></tr>";
					}



					var meterCountDom = $("#meterCountThead");
					meterCountDom.append(operatingPicDivHtml);




				}
			},
			error : function(jqXHR, textStatus, errorThrown) {

				window.parent.layer.msg(
					'<t:mutiLang lanKey="system.requestError" />', {
						icon : 2
					});
			}
		});



}


function operatingTypeChg() {
	var nSel = $("#customerSelect");
	var checkText = nSel.find("option:selected").text();
	var checkValue = nSel.val(); // 获取Select选择的Value

	$
		.ajax({
			async : true,
			cache : false,
			traditional : true,
			type : 'POST',
			url : getRootPathWeb()+ '/systemController/typeCount.do',
			data : {
				name : checkValue
			},
			dataType : 'json',
			success : function(data) {

				if (data.success) {
					importExportReportListByType = data.attributes.importExportReportListByType;
					dataStatisticsCustomerInfoByUserType = data.attributes.importExportReportInfoByType;

					var customerList = [];
					var customerInfo = JSON.parse(dataStatisticsCustomerInfoByUserType);

					for ( var i in customerInfo) {
						console.log(customerInfo[i].countCurrent);
						customerList.push({
							value : customerInfo[i].countCurrent,
							name : customerInfo[i].name
						});
					}

					loadCustomerPie6(customerList,salesStatisticOfCustomer);
					$("#supplyByTypeId").empty();

					var operatingPicDivHtml = "";

					for ( var j in importExportReportListByType) {
						operatingPicDivHtml += "<tr>";
						operatingPicDivHtml += "<td>"+importExportReportListByType[j].name+"</td>";
						operatingPicDivHtml += "<td>"+importExportReportListByType[j].countTotalFormatter+"</td>";
						operatingPicDivHtml += "<td>"+importExportReportListByType[j].countCurrentFormatter+"</td>";

						operatingPicDivHtml += "<td><div class='progress'>";
						operatingPicDivHtml += "<div class='progress-bar progress-bar-danger' role='progressbar' aria-valuenow='"+importExportReportListByType[j].percent+"' aria-valuemin='0' aria-valuemax='100' style='width: "+importExportReportListByType[j].percent+"%;'>";

						operatingPicDivHtml += "<span class='sr-only'>"+importExportReportListByType[j].percent+"% 完成</span>";
						operatingPicDivHtml += "</div></div></td></tr>";
					}



					var meterCountDom = $("#supplyByTypeId");
					meterCountDom.append(operatingPicDivHtml);


				}
			},
			error : function(jqXHR, textStatus, errorThrown) {

				window.parent.layer.msg(
					'<t:mutiLang lanKey="system.requestError" />', {
						icon : 2
					});
			}
		});



}


function loadEventPie(eventList,title) {
	var notSelected = {};
	for (let i = 1; i < eventList.length; i++) {
		if (i > 5){
			notSelected[eventList[i]['name']]= false;
		}
	}

	option3 = {
		backgroundColor: '#fff',
		title: {
			text: title,
			left: 'center',
			left: 10,
			top:5,
			textStyle: {
				color: '#767373',
				fontSize: 14,
			}
		},
		itemStyle: {
			normal: {
				// 设置扇形的阴影
				shadowBlur: 30,
				shadowColor: 'rgba(0,0,0,0.5)',
				shadowOffsetX:10,
				shadowOffsetY: 5
			}
		},
		tooltip: {
			trigger: 'item',
			formatter: "{b} : {c} ({d}%)"
		},
		legend: {
			type: 'scroll',
			data:eventList,
			orient: 'horizontal',
			left:'left',
			height:60,
			top:50,
			right: 10,
			bottom: 10,
			formatter: function (name) {
				return name.length>10?name.substr(0,15)+"...":name;
			},
			tooltip: {
				show: true
			},
			selected: notSelected,

		},

		color: ['rgb(254,67,101)','rgb(252,157,154)','rgb(249,205,173)','rgb(200,200,169)','rgb(131,175,155)'],
		series : [
			{
				type: 'pie',
				radius : '60%',
				center: ['50%', '61%'],
				selectedMode: 'single',
				data:eventList,
				labelLine:{
					normal:{
						length:2,  // 改变标示线的长度
						lineStyle: {
							fontWeight: 800
						}
					},
				},
				label: {
					normal: {
						textStyle: {
							fontWeight:  800
						}
					}
				},
				itemStyle: {
					emphasis: {
						shadowBlur: 10,
						shadowOffsetX: 0,
						shadowColor: 'rgba(0, 0, 0, 0.5)'
					}
				}
			}
		]
	};

	myChart3.setOption(option3);
	myChart3.resize();
}


function loadCustomerPie6(customerList,title) {

	option6 = {
		backgroundColor: '#fff',
		title: {
			text: title,
			left: 'center',
			top:10,
			left: 30,
			textStyle: {
				color: '#767373',
				fontSize: 14,
			}

		},
		itemStyle: {
			normal: {
				// 设置扇形的阴影
				shadowBlur: 30,
				shadowColor: 'rgba(0,0,0,0.5)',
				shadowOffsetX:10,
				shadowOffsetY: 5
			}
		},
		tooltip: {
			trigger: 'item',
			formatter: "{b} : {c} ({d}%)"
		},
		color: ['rgb(254,67,101)','rgb(252,157,154)','rgb(249,205,173)','rgb(200,200,169)','rgb(131,175,155)'],
		series : [
			{
				type: 'pie',
				radius : '65%',
				center: ['50%', '50%'],
				selectedMode: 'single',
				data:customerList,
				labelLine:{
					normal:{
						length:5,  // 改变标示线的长度
						lineStyle: {
							fontWeight: 800
						}
					},
				},
				label: {
					normal: {
						textStyle: {
							fontWeight:  800
						}
					}
				},
				itemStyle: {
					emphasis: {
						shadowBlur: 10,
						shadowOffsetX: 0,
						shadowColor: 'rgba(0, 0, 0, 0.5)'
					}
				}
			}
		]
	};
	myChart6.setOption(option6);
	myChart6.resize();
}
// 使用刚指定的配置项和数据显示图表。
// 默认查询7天
getDailyMeterReadsIntegrityByType(1);
getSupplyStatisticByType(1);

getSupplyStatisticByMonth(6);
var Echart_01 = echarts.init(document.getElementById("main1"));
var Echart_02 = echarts.init(document.getElementById("main2"));
var Echart_03 = echarts.init(document.getElementById("main3"));
var Echart_04 = echarts.init(document.getElementById("main4"));
var Echart_05 = echarts.init(document.getElementById("main5"));
var Echart_06 = echarts.init(document.getElementById("main6"));
var Echart_07 = echarts.init(document.getElementById("main7"));
//指定图表的配置项和数据
//使用刚指定的配置项和数据显示图表。
$(window).resize(function() {
	autoHeight();
	$(myChart1).each(function(index, chart) {
		Echart_01.resize();
		Echart_02.resize();
		Echart_03.resize();
		Echart_04.resize();
		Echart_05.resize();
		Echart_06.resize();
		Echart_07.resize();
	});

});

function resizeHome() {
	var Echart_01 = echarts.init(document.getElementById("main1"));
	var Echart_02 = echarts.init(document.getElementById("main2"));
	var Echart_03 = echarts.init(document.getElementById("main3"));
	var Echart_04 = echarts.init(document.getElementById("main4"));
	var Echart_05 = echarts.init(document.getElementById("main5"));
	var Echart_06 = echarts.init(document.getElementById("main6"));
	var Echart_07 = echarts.init(document.getElementById("main7"));

	autoHeight();
	$(myChart1).each(function(index, chart) {
		Echart_01.resize();
		Echart_02.resize();
		Echart_03.resize();
		Echart_04.resize();
		Echart_05.resize();
		Echart_06.resize();
		Echart_07.resize();
	});
}




function thousandBit(num) {
	//1.先去除空格,判断是否空值和非数   
	num = num + "";
	num = num.replace(/[ ]/g, ""); //去除空格  
	if (num == "") {
		return;
	}
	if (isNaN(num)) {
		return num;
	}
	//2.针对是否有小数点，分情况处理   
	var index = num.indexOf(".");
	if (index == -1) {//无小数点   
		var reg = /(-?\d+)(\d{3})/;
		while (reg.test(num)) {
			num = num.replace(reg, "$1,$2");
		}

	} else {
		var intPart = num.substring(0, index);
		var pointPart = num.substring(index + 1, num.length);
		//如果输入小数位为1位，追加0
		if (pointPart.length == 1) {
			pointPart += "0";
		}
		var reg = /(-?\d+)(\d{3})/;
		while (reg.test(intPart)) {
			intPart = intPart.replace(reg, "$1,$2");
		}
		num = intPart + "." + pointPart;
	}
	return num;
}

function selectDaysChart1(obj) {

	var objVal = $(obj).val();

	switch (objVal) {
		case "30":
			getDailyMeterReadsIntegrityByType(3);
			break;
		case "15":
			getDailyMeterReadsIntegrityByType(2);
			break;
		case "7":
			getDailyMeterReadsIntegrityByType(1);
			break;
		default :
			getDailyMeterReadsIntegrityByType(1);
	}
}

function selectDaysChart2(obj) {
	var objVal = $(obj).val();

	switch (objVal) {
		case "30":
			getSupplyStatisticByType(3);
			break;
		case "15":
			getSupplyStatisticByType(2);
			break;
		case "7":
			getSupplyStatisticByType(1);
			break;
		default :
			getSupplyStatisticByType(1);
	}
}


function selectMonthChart2(obj) {

	var objVal = $(obj).val();

	switch (objVal) {
		case "12":
			getSupplyStatisticByMonth(12);
			break;
		case "6":
			getSupplyStatisticByMonth(6);
			break;
		default :
			getSupplyStatisticByMonth(6);
	}
}



function getSupplyStatisticByMonth(type) {
	var startMonth;
	var endMonth = $("#endMonth").val();
	var title = supplyStatistic6Month;

	switch (type) {
		case 6 :
			startMonth = $("#start6Months").val();
			title = supplyStatistic6Month;
			break;
		case 12 :
			startMonth = $("#start12Months").val();
			title = supplyStatistic12Month;
			break;

		default:
			startMonth = supplyStatistic6Month;
	}
	getSupplyStatisticMonth(startMonth,endMonth,title);
}

function getSupplyStatisticMonth(startMonth,endMonth,charttitle) {

	myChart7.showLoading();
	setTimeout("myChart7.hideLoading(); ", 30000);

	if (startMonth == null || startMonth == '') {
		return;
	}
	if (endMonth == null || endMonth == '') {
		return;
	}
	var orgIdValue = $("#selectOrgId").val();
	$.ajax({
		type : 'POST',
		url : getRootPathWeb() + '/systemController/getSupplyStatisticMonth.do',
		data : 'startMonth=' + startMonth + '&&endMonth=' + endMonth +'&&defaultOrgId='+orgIdValue,
		dataType : 'json',
		success : function(data) {
			myChart7.hideLoading();
			var tvList = null;
			var importExportDiagramList = null;

			if (data.success) {
				tvList = data.attributes.tvList;
				importExportDiagramList = data.attributes.importExportDiagramList;
			}


			var option7 = {
				title: {
					text: charttitle,
					top: 0,
					left: 12,
					itemGap: 20,
					textStyle: {
						color: '#767373',
						fontSize: 14,
					}

				},
				color : [ '#33ccdb' ],
				tooltip : {
					trigger : 'axis',
					formatter : function(params) {
						var result = params[0].seriesName + '</br>';
						for (var i = 0, l = params.length; i < l; i++) {
							params.forEach(function(item) {
								result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'
									+ item.color + '"></span>'
									+ params[i].name + ' : ' +thousandBit( params[i].value) + 'kWh';
							});
						}
						return result;
					},
					axisPointer : { // 坐标轴指示器，坐标轴触发有效
						type : 'cross' // 默认为直线，可选为：'line' | 'shadow'
					}
				},
				grid : {
					top : '13%',
					left : '3%',
					right : '4%',
					bottom : '3%',
					containLabel : true
				},
				xAxis : [ {
					type : 'category',
					data : tvList,
					boundaryGap: false,
					/*axisTick : {
                        alignWithLabel : true
                    }*/
				} ],
				yAxis : [ {
					axisLabel : {
						formatter : '{value} '
					},
					type : 'value',
					scale : true,
					min : 0,
					splitArea : {
						show : true
					},
				} ],
				series : [ {
					name : i18n.t('scheduleReadsReport.dMeterInteRate'),
					type : 'line',
					areaStyle: {normal: {}},
					smooth: true,
					itemStyle: {
						normal: {
							color: '#33ccdb',

							areaStyle: {
								type: 'default',
								opacity: 0.5,
							}
						}
					},
					/*barWidth : '60%',*/
					data : importExportDiagramList,
					label : {
						normal : {
							show : true,
							position : 'top',
							formatter : function(c) {
								var f = parseFloat(c.value);
								if (isNaN(f)) {
									return false;
								}
								var f = Math.round(c.value * 100) / 100;

								return thousandBit(f);
							}
						}
					},
					cursor : 'default',
					itemStyle: {
						normal: {
							color: '#c23531',
							shadowBlur: 200,
							shadowColor: 'rgba(0, 0, 0, 0.200)'
						}
					},

				} ]
			};

			myChart7.setOption(option7);
			myChart7.resize();
		},
		error : function(msg) {
		}
	});
}


