/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeterGroupMap{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-01-25 07:22:09
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.asset;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.asset.AssetMeterGroupMapDao;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroupMap;
import com.clou.esp.hes.app.web.service.asset.AssetMeterGroupMapService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@Component
@Service("assetMeterGroupMapService")
public class AssetMeterGroupMapServiceImpl  extends CommonServiceImpl<AssetMeterGroupMap>  implements AssetMeterGroupMapService {

	@Resource
	private AssetMeterGroupMapDao assetMeterGroupMapDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(assetMeterGroupMapDao);
    }
	public AssetMeterGroupMapServiceImpl() {}
	
	@Override
	public int copyReferenceUpdate(AssetMeterGroupMap assMeterGroupMap) {
		return assetMeterGroupMapDao.copyReferenceUpdate(assMeterGroupMap);
	}
	
	@Override
	public JqGridResponseTo getPlanMeterList_MGU(JqGridSearchTo jqGridSearchTo) {
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		List<AssetMeterGroupMap> list = assetMeterGroupMapDao.getPlanMeterList_MGU_jqgrid(jqGridSearchTo);
		PageInfo<AssetMeterGroupMap> pageInfo = new PageInfo<AssetMeterGroupMap>(list);
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}
	
	@Override
	public JqGridResponseTo getPlanJobList_MGU(JqGridSearchTo jqGridSearchTo) {
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		List<AssetMeterGroupMap> list = assetMeterGroupMapDao.getPlanJobList_MGU(jqGridSearchTo);
		PageInfo<AssetMeterGroupMap> pageInfo = new PageInfo<AssetMeterGroupMap>(list);
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}
	
	@Override
	public List<AssetMeterGroupMap> getPlanMeterList_export(
			AssetMeterGroupMap assMeterGroupMap) {
		return assetMeterGroupMapDao.getPlanMeterList_export(assMeterGroupMap);
	}
	
	@Override
	public List<AssetMeterGroupMap> getPlanJobList_export(
			AssetMeterGroupMap assMeterGroupMap) {
		return assetMeterGroupMapDao.getPlanJobList_export(assMeterGroupMap);
	}
	
	@Override
	public void deleteByMeterAndType(String meterId, String type) {
			this.assetMeterGroupMapDao.deleteByMeterAndType(meterId, type);
	}
	@Override
	public long countMeterByGroupId(String meterGroupId) {
		return this.assetMeterGroupMapDao.countMeterByGroupId(meterGroupId);
	}
	@Override
	public int batchInsert(List<AssetMeterGroupMap> mList) {
		return this.assetMeterGroupMapDao.batchInsert(mList);
	}
	
	
}