update ASSET_METER set src_addr=1;

alter table DATA_VEE_EVENT add (RULE_ID NUMBER(4,0));

CREATE TABLE ASSET_REPORT_TEMPLATE
(	
  ID VARCHAR2(32 BYTE) NOT NULL, 
  REPORT_TYPE NUMBER(4,0) NOT NULL,
  REPORT_NAME VARCHAR2(64 BYTE) NOT NULL, 
  ORG_ID VARCHAR2(32 BYTE) NOT NULL,
  TEMPLATE_FILE VARCHAR2(128 BYTE) NOT NULL,  
  primary key (ID)
);

/*-===========update SYS_SERVICE_ATTRIBUTE=====================-*/
delete from SYS_SERVICE_ATTRIBUTE where Id='20010006';
Insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010006','Calculation.Task.Export','Enable');
Insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010006','Calculation.Export.FtpUrl','ftp://127.0.0.1:7011/Day Data File');
Insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010006','Calculation.Export.StartTime','04:38:00');
Insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010006','Calculation.Export.Cycle','3');
Insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010006','Calculation.Data.StorageCycle','12');
Insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010006','Calculation.Task.CalculationObject','Enable');
Insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010006','Calculation.Task.SalesStatistic','Enable');
Insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010006','Calculation.Integrity.StartTime','00:05:00');
Insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010006','Calculation.Integrity.Cycle','2');

/*-===========insert grid loss=====================-*/
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013301','Add Linked Meter for Line','1013','AddLineLinkedMeter',13,'Add Linked Meter for Line');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013302','Delete Linked Meter for Line','1013','DelLineLinkedMeter',13,'Delete Linked Meter for Line');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013303','Add Linked Transformer for Line','1013','AddLineLinkedTrans',13,'Add Linked Transformer for Line');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013304','Delete Linked Transformer for Line','1013','DelLineLinkedTrans',13,'Delete Linked Transformer for Line');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013305','Add Linked Meter for Transformer','1013','AddTransLinkedMeter',18,'Add Linked Meter for Transformer');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013306','Delete Linked Meter for Transformer','1013','DelTransLinkedMeter',18,'Delete Linked Meter for Transformer');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('102701','Calculation Object Define Page','1027','calculationObjectDefinePage',1,'Calculation Object Define Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('102702','Grid Loss Report Page','1027','gridLossReportPage',2,'Grid Loss Report Page');

Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('2dd2e5a5b12311e79bb9644723333333','29018328bd4011e79bb968f728c516f9',2,'VEE Management',2,'dictReportController/veeReport.do','2dd2e5a5beda11e79bb968f728c53333','1','1025',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('2dd2e5a5z12311e79bb9644723456789','29018328bd4011e79bb968f728c516f9',2,'Grid Loss Management',3,'assetCalationObjectController/gridLossManager.do','2dd2e5a5beda11e79bb968f728c53333','1','1027',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('2dd2e5a5beda11e79bb9644724444444','29018328bd4011e79bb968f728c516f9',3,'Template Reports',5,'dictReportController/designer.do','2dd2e5a5beda11e79bb968f728c53333','1','1030',null);
