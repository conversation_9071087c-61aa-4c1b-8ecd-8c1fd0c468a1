package com.clou.esp.hes.app.web.controller.mobile;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.util.DateTimeFormatterUtil;
import com.clou.esp.hes.app.web.model.asset.AssetCustomer;
import com.clou.esp.hes.app.web.model.report.BillingReport;
import com.clou.esp.hes.app.web.service.asset.AssetCustomerService;
import com.clou.esp.hes.app.web.service.report.DictReportService;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.format.FormatUtil;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;

@Controller
@RequestMapping("/mobile/systemController")
public class MobileSystemController extends BaseController{
	
	@Resource
	private DictReportService dictReportService;
	
	@Resource
	private AssetCustomerService assetCustomerService;

	@RequestMapping(value = "login")
	public ModelAndView login(HttpServletRequest request, Model model) {
		
		return new ModelAndView("/mobile/login");
	}
	
	/**
	 * 登录
	 * 
	 * @param su
	 * @param rememberMe
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "doLogin", method = RequestMethod.POST)
	@ResponseBody
	public AjaxJson doLogin(String sn,HttpServletRequest request, HttpServletResponse response) {
		AjaxJson j = new AjaxJson();
		
		// 判断客户是否存在
		AssetCustomer entity = new AssetCustomer();
		entity.setSn(sn);
		AssetCustomer customer = assetCustomerService.get(entity);
		
        if (null == customer) {
			j.setSuccess(false);
			j.setErrorMsg("Customer is not exist!");
			return j;
		}
		
		// 记录客户信息
		String url = null;
		if (StringUtil.isBlank(url)) {
			url = request.getContextPath() + "/mobile/systemController/index.do?sn=" + sn;
		}
		
		j.setObj(url);
		return j;
	}
	
	
	/**
	 * 根据sn查询客户用电量
	 * @param sn 客户sn
	 * @param type [1:日,2:月]
	 * @param timeCount 当type=1时，timeCount代表从当天至timeCount天前的日用电量。当type=2时，timeCount代表从当月至timeCount月前的数据
	 * @param jqGridSearchTo
	 * @param request
	 * @return
	 */						 
	@RequestMapping(value = "findBillingBySN",method = RequestMethod.POST)
    @ResponseBody
    public AjaxJson findBillingBySN(String sn,
    		String timeType,int timeCount,HttpServletRequest request) {
		
		AjaxJson j = new AjaxJson();
		
		try {
			// 查询出客户实体
			AssetCustomer entity = new AssetCustomer();
			entity.setSn(sn);
			AssetCustomer customer = assetCustomerService.get(entity);
			
            if (null == customer) {
				j.setSuccess(false);
				j.setErrorMsg("Customer is not exist!");
				return j;
			}
			JqGridSearchTo jqGridSearchTo = new JqGridSearchTo();
			jqGridSearchTo.put("sn", sn);
        	jqGridSearchTo.put("timeType", timeType); 
        	// DateTimeFormatterUtil
        	// 结束时间
        	String endTime = null;
        	String startTime = null;
//        	timeCount += 1;
        	Date endDate = new Date();
        	
        	if ("1".equals(timeType)) {
        		endTime = DateUtils.date2Str(FormatUtil.addDaysToDate(endDate, -1),new SimpleDateFormat("MM-dd-yyyy"));
        		startTime = DateUtils.date2Str(FormatUtil.addDaysToDate(endDate, -timeCount),new SimpleDateFormat("MM-dd-yyyy"));
        	} else {
        		endTime = DateUtils.date2Str(FormatUtil.addMonthsToDate(endDate, -1),new SimpleDateFormat("MM-01-yyyy"));
        		startTime = DateUtils.date2Str(FormatUtil.addMonthsToDate(endDate, -timeCount),new SimpleDateFormat("MM-01-yyyy"));
        	}
        	
        	System.out.println("startTime = " + startTime);
        	System.out.println("endTime = " + endTime);
        	
        	jqGridSearchTo.put("startTime", startTime);
        	jqGridSearchTo.put("endTime", endTime);
        	
        	jqGridSearchTo.put("timePattern", "1");
        	// 用于mysql 时间转换
        	String datePattern = convertDatePattern("1");
        	jqGridSearchTo.put("datePattern", datePattern);
        	
        	// 数据库查询
        	List<BillingReport> list = dictReportService.findBillingOrganizationMobile(jqGridSearchTo);
        	
        	// tvList不管有没有数据都进行填充
        	List<String> tvList = new ArrayList<String>();
        	
        	List<String> energyList = new ArrayList<String>();
        	if (list.size() > 0) {
        		BigDecimal currentMonthPower = new BigDecimal(0);
        		// 对数据进行拆分
            	for (BillingReport billingReport : list) {
            		tvList.add(billingReport.getTv());
            		energyList.add(billingReport.getEnergy().toString());
            		currentMonthPower = currentMonthPower.add(billingReport.getEnergy());
    			}
            	
            	j.put("tvList",tvList);
            	j.put("energyList",energyList);
            	// 存放当前月的用电量
            	// 如果有月数据，则使用月的数据
            	j.put("currentMonthPower", currentMonthPower.intValue());
        	} else { // 没有数据则填充假数据进去
        		String emptyDate = null;
        		if ("1".equals(timeType)) {
        			// 从开始时间到结束时间，将每段填充一条记录
        			emptyDate = endTime;
        		} else {
        			emptyDate = endTime;
        		}
        		tvList.add(emptyDate);
        		energyList.add("0");
        		j.put("tvList",tvList);
            	j.put("energyList",energyList);
        	}
		} catch (Exception ex) {
			 ex.printStackTrace();
		}
		
		return j;
	}
	
	/**
	 * @Title: convertDatePattern
	 * @Description: TODO
	 * @param timePattern
	 * @return
	 * @return String
	 * @throws
	 */
	private String convertDatePattern(String timePattern) {
		// TODO Auto-generated method stub
		switch(timePattern){
		case "1":return "%m/%d/%Y";
		case "2":return "%Y/%m/%d";
		case "3":return "%d/%m/%Y";
		case "4":return "%d-%m-%Y";
		case "5": return "%Y-%m-%d";
		default: return "%d-%m-%Y";
		}
	}
	
	/**
	 * 默认主页
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "index")
	public ModelAndView index(HttpServletRequest request, Model model) {
		model.addAttribute("sn", request.getParameter("sn"));
		
		return new ModelAndView("/mobile/index");
	}
}
