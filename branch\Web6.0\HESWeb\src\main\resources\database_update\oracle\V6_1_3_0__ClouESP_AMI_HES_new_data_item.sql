insert into dict_dataitem_group(ID , NAME , APP_TYPE , PROTOCOL_ID , SORT_ID) values('1003006' , 'Module','3' , '100' , 6) ;
			 

insert into dict_dataitem(id ,protocol_id,name,protocol_code,unit,op_type,show_unit,parse_type , parse_len ) values('3.1.1.2.0.2.0.255.2', 100, 'Module Firmware version', '1#1.2.0.2.0.255#2', null, 'R', 1, 'visible-string',4);
insert into dict_dataitem_parse_dlms values('1#1.2.0.2.0.255#2', 'visible-string', null, null);

insert into dict_dataitem(id ,protocol_id,name,protocol_code,unit,op_type,show_unit,parse_type , parse_len ) values('3.1.0.0.96.51.7.255.2', 100, 'Module signal strength', '1#0.0.96.51.7.255#2', null, 'R', 1, 'unsigned',null);
insert into dict_dataitem_parse_dlms values('1#0.0.96.51.7.255#2', 'unsigned', 1, null);
 
insert into dict_dataitem_group_map(GROUP_ID,DATAITEM_ID,SORT_ID) values('1003006' , '3.1.1.2.0.2.0.255.2' , 1) ;
insert into dict_dataitem_group_map(GROUP_ID,DATAITEM_ID,SORT_ID) values('1003006' , '3.1.0.0.96.51.7.255.2' , 2) ;

ALTER TABLE ASSET_COMMUNICATOR ADD(LISTEN_MODE number(2,0));
ALTER TABLE ASSET_COMMUNICATOR ADD(CREATE_TIME DATE);

CREATE TABLE DICT_DATAITEM_INTERVAL_MAP(
    DATAITEM_ID VARCHAR2(64) NOT NULL,
    INTERVAL_DATAITEMID VARCHAR2(64) NOT NULL
);

create table sys_dataitem_export_hx
(
   dataitemId           VARCHAR2(64) not null,
   dataitemType          number(4,0),
   ioDataitemId         VARCHAR2(64),
   timeType              number(4,0),
   primary key (dataitemId)
);

COMMENT ON COLUMN sys_dataitem_export_hx.dataitemType IS '1:data, 2:event';
COMMENT ON COLUMN sys_dataitem_export_hx.timeType IS '数据项时间类型： 1： 月冻结  2：日冻结 ； 3：负荷数据';

CREATE TABLE DATA_DI_EXPORT_PROGRESS_HX 
(	
	DATAITEM_ID VARCHAR2(64), 
	TV DATE, 
	EXPORT_TV DATE, 
	EXPORT_RESULT VARCHAR2(20), 
	EXPORT_REASON VARCHAR2(512),
	primary key (DATAITEM_ID)	
);

delete from dict_dateitem where id='40.0.7.52';
insert into dict_dataitem(id ,protocol_id,name,protocol_code,unit,op_type,show_unit,parse_type , parse_len ) values('40.0.7.52', 100, 'Number Of Reading Meter Day Curves', '1#0.1.96.1.189.255#2', null, 'RW', 1, 'long-unsigned'，4);;
delete from dict_dateitem where id='40.0.7.53';
insert into dict_dataitem(id ,protocol_id,name,protocol_code,unit,op_type,show_unit,parse_type , parse_len ) values('40.0.7.53', 100, 'Number Of Reading Meter Month Curves', '1#0.1.96.1.189.255#3', null, 'RW', 1, 'long-unsigned'，4);
delete from dict_dateitem where id='40.0.7.54';
insert into dict_dataitem(id ,protocol_id,name,protocol_code,unit,op_type,show_unit,parse_type , parse_len ) values('40.0.7.54', 100, 'Number Of Reading Meter Load Curves', '1#0.1.96.1.189.255#4', null, 'RW', 1, 'long-unsigned'，4);
delete from dict_dateitem where id='38.0.0.67';
insert into dict_dataitem(id ,protocol_id,name,protocol_code,unit,op_type,show_unit,parse_type , parse_len ) values('38.0.0.67', 100, 'Relay control status(0:Disable; 1:Enable)', '1#0.0.96.4.135.255#2', null, 'RW', 1, 'special'，null);
delete from dict_dataitem_group_map where GROUP_ID='1004002' and DATAITEM_ID='38.0.0.67';
insert into DICT_DATAITEM_GROUP_MAP values('1004002', '38.0.0.67', 108);
drop table dict_dataitem_parse_dlms;

update dict_dataitem set parse_type='double-long-unsigned' where protocol_code = '71#0.0.17.0.0.255#6' or protocol_code = '71#0.0.17.0.0.255#7';
update dict_dataitem set operation_type='A' where id='40.0.7.104';
update dict_operation set function_id='1027' where function_id='1022';

update dict_dataitem set name='Relay related function control filter (0:Disable; 1:Enable)' where id='38.0.0.67';

insert into dict_dataitem(id ,protocol_id,name,protocol_code,unit,op_type,show_unit,parse_type , parse_len ) values('38.0.0.68', 100, 'Status of relay1 control signals - current status(0:Disable; 1:Enable)', '1#0.0.96.4.0.255#2', null, 'RW', 1, 'special'，null);
insert into DICT_DATAITEM_GROUP_MAP values('1004002', '38.0.0.68', 109);
