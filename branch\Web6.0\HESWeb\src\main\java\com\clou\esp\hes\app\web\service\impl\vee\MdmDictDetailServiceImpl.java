/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class MdmDictDetail{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2020-04-21 07:32:26
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.vee;

import com.clou.esp.hes.app.web.dao.vee.MdmDictDetailDao;
import com.clou.esp.hes.app.web.model.vee.MdmDictDetail;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.clou.esp.hes.app.web.service.vee.MdmDictDetailService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.power7000g.core.util.json.RoletoJson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Component
@Service("mdmDictDetailService")
public class MdmDictDetailServiceImpl extends CommonServiceImpl<MdmDictDetail> implements MdmDictDetailService {

	@Resource
	private MdmDictDetailDao mdmDictDetailDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(mdmDictDetailDao);
    }
	@SuppressWarnings("rawtypes")
	public MdmDictDetailServiceImpl() {}
	
	@Override
	public String getDictReplace(String dictId) {
		MdmDictDetail detail = new MdmDictDetail();
		detail.setDictId(dictId);
		List<MdmDictDetail> dataItems= this.mdmDictDetailDao.getList(detail);
		return RoletoJson.listToReplaceStr(dataItems, "innerValue", "guiDisplayName");
	}
	@Override
	public Map<String, String> getDictReplaces(List<String> dictIds) {
		Map<String,String> rest = Maps.newHashMap();
		MdmDictDetail detail = new MdmDictDetail();
		detail.setDictIdList(dictIds);
		List<MdmDictDetail> dataItems= this.mdmDictDetailDao.getList(detail);
		Map<String,List<MdmDictDetail>> map = Maps.newHashMap();
		for(MdmDictDetail d:dataItems) {
			String dictId = d.getDictId();
			List<MdmDictDetail> list = null;
			if(map.get(dictId)==null) {
				list = Lists.newArrayList();
			}else {
				list = map.get(dictId);
			}
			
			list.add(d);
			map.put(dictId, list);
		}
		if(map.size()>0) {
			for(String key :map.keySet()) {
				rest.put(key, RoletoJson.listToReplaceStr(map.get(key), "innerValue", "guiDisplayName"));
			}
		}
		return rest;
	}
	
	
	@Override
	public Map<String, Map<String,String>> getDictMap(List<String> dictIds) {
		Map<String,Map<String,String>> rest = Maps.newHashMap();
		MdmDictDetail detail = new MdmDictDetail();
		detail.setDictIdList(dictIds);
		List<MdmDictDetail> dataItems= this.mdmDictDetailDao.getList(detail);
		for(MdmDictDetail d:dataItems) {
			String dictId = d.getDictId();
			String value = d.getInnerValue();
			String name = d.getGuiDisplayName();
			Map<String,String> map = rest.get(dictId);
			if(map==null) {
				map=Maps.newHashMap();
			}
			map.put(value, name);
			rest.put(dictId, map);
		}
	
		return rest;
	}
	
}