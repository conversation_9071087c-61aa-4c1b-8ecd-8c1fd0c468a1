ALTER TABLE dict_dataitem ADD parse_type varchar2(32) NULL;
ALTER TABLE dict_dataitem ADD parse_len number(3,0) NULL;
ALTER TABLE dict_dataitem ADD scale number(3,0) NULL;

update dict_dataitem set dict_dataitem.parse_type = (select parse_type from dict_dataitem_parse_dlms where dict_dataitem.PROTOCOL_CODE=dict_dataitem_parse_dlms.CODE);
update dict_dataitem set dict_dataitem.parse_len = (select parse_len from dict_dataitem_parse_dlms where dict_dataitem.PROTOCOL_CODE=dict_dataitem_parse_dlms.CODE);
update dict_dataitem set dict_dataitem.scale = (select scale from dict_dataitem_parse_dlms where dict_dataitem.PROTOCOL_CODE=dict_dataitem_parse_dlms.CODE);