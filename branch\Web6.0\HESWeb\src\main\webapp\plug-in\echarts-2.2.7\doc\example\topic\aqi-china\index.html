﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>中国城市空气质量实况</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="China">
    <meta name="author" content="<EMAIL>">

    <link href="../../../asset/css/bootstrap.css" rel="stylesheet">
    <link href="../../../asset/css/bootstrap-responsive.css" rel="stylesheet">
    <link rel="shortcut icon" href="../../../asset/ico/favicon.png">
    <style type="text/css">
        * {
            font-family: "Microsoft YaHei" !important;
        }
        body{
            background-image: url(../../../asset/img/groovepaper.png);
            background-repeat: repeat;    
        }
        header {
            background-image: url(../../../asset/img/tweed.png);
            background-repeat: repeat;
        }
        h1 {
            color: #FFF;
            font-weight : bolder;
            margin:20px 0 5px 0;
        }
        header p {
            color: #FFF;
            margin-bottom: 20px;
        }
        section {
            background-image: url(../../../asset/img/ticks.png);
            background-repeat: repeat;
            padding: 10px;
        }
        footer {
            height: 100px;
            background-image: url(../../../asset/img/tweed.png);
            background-repeat: repeat;
            font-size: 14px;
            color: #CCC;
            text-align: center;
            padding-top: 15px;
            margin-top:15px;
        }
        .nav.nav-tabs.nav-justified {
            margin-bottom:0;
        }
        .ctrl-wrap {
            padding:20px 20px 0 20px;
            text-align: center;
            border-left: 1px solid #dddddd;
            border-right: 1px solid #dddddd;
        }
        .ctrl-content .btn{
            width: 7%;
        }
        .tab-content {
            padding:20px;
            border: 1px solid #dddddd;
            border-top: 0px;
        }
        .g2wrap {
            height:300px;
            width:33%;
            float:left;
        }
        input[type="radio"] {
            margin: -5px 5px 0;
        }
        label {
            display: inline-block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        footer a:hover {
            color:#62C462
        }
    </style>
</head>

<body>
    <!-- HEADER -->
    <header>
        <div class="container">
            <h1>中国主要城市空气质量实况</h1>
            <p>数据更新时间：<span id="time">****-**-** --:--:--</span></p>
        </div>
    </header>
    <section>
        <div class="container">
            <strong>引言：</strong>你关注或者不关注，它就在我们身边...
            <div style="text-align: right"><i>忘记从什么时候开始，用颜色表示空气；不知道什么时候可以，用甜度描述空气。</i> from <a href="http://www.pm25.in/" target="_blank">pm25.in</a></div>
        </div>
    </section>
    <div class="container">
        <!-- CONTAINER -->
        <div class="row">
            <div id="overview" class="span12">
                <h3>概况：今天哪里又爆表了？</h3>
                <!-- Nav tabs -->
                <ul class="nav nav-tabs nav-justified">
                    <li class="active">
                        <a id = "o-aqi" href="#main0" data-toggle="tab">AQI<br/>（空气质量）</a></li>
                    <li><a id = "o-pm25" href="#main0" data-toggle="tab">PM 2.5<br/>（细颗粒物）</a></li>
                    <li><a id = "o-pm10" href="#main0" data-toggle="tab">PM 10<br/>（可吸入颗粒物）</a></li>
                    <li><a id = "o-co" href="#main0" data-toggle="tab">CO<br/>（一氧化碳）</a></li>
                    <li><a id = "o-no2" href="#main0" data-toggle="tab">NO2<br/>（二氧化氮）</a></li>
                    <li><a id = "o-o3" href="#main0" data-toggle="tab">O3<br/>（臭氧）</a></li>
                    <li><a id = "o-so2" href="#main0" data-toggle="tab">SO2<br/>（二氧化硫）</a></li>
                </ul>
                <div class="tab-content" id="main0">
                    <div class="span8" style="margin:0 30px 0 0">
                        <div id="g0" style="height:430px"></div>
                    </div>
                    <div style="min-height:430px;">
                        <h4 id="overview-head"></h4><hr>
                        <p id="overview-content"></p>
                        <p style="text-align: right">更多详见 <a id="overview-link" target="_blank">百度百科 »</a></p>
                    </div>
                </div>
            </div>
            <div class="span12" style="margin-top:15px;">
                <h3>重点城市对比</h3>
                <div id="g1" style="height:400px"></div>
                <h4 style="text-align: center">“PM2.5 vs 经济 vs 人口” 关联分析</h4>
                <div id="g20" class="g2wrap"></div>
                <div id="g21" class="g2wrap"></div>
                <div id="g22" class="g2wrap"></div>
            </div>
            <div class="span12" style="margin-top:15px;">
                <h3>空气质量排行榜</h3>
                <!-- Nav tabs -->
                <ul class="nav nav-tabs nav-justified">
                    <li class="active">
                        <a id = "r-aqi" href="#main3" data-toggle="tab">AQI<br/>（空气质量）</a></li>
                    <li><a id = "r-pm25" href="#main3" data-toggle="tab">PM 2.5<br/>（细颗粒物）</a></li>
                    <li><a id = "r-pm10" href="#main3" data-toggle="tab">PM 10<br/>（可吸入颗粒物）</a></li>
                    <li><a id = "r-co" href="#main3" data-toggle="tab">CO<br/>（一氧化碳）</a></li>
                    <li><a id = "r-no2" href="#main3" data-toggle="tab">NO2<br/>（二氧化氮）</a></li>
                    <li><a id = "r-o3" href="#main3" data-toggle="tab">O3<br/>（臭氧）</a></li>
                    <li><a id = "r-so2" href="#main3" data-toggle="tab">SO2<br/>（二氧化硫）</a></li>
                </ul>
                <div class="tab-content" id="main3">
                    <div id="g3" style="height:700px"></div>
                </div>
            </div><!--/span-->
        </div><!--/row-->
    </div>
    <!-- FOOTER -->
    <footer>
      <p>&copy; 2014 <a href="http://weibo.com/wfsr" target="_blank">大佛</a>（百度）  &middot; <a href="http://weibo.com/kenerlinfeng" target="_blank">林峰</a>（百度） 特别鸣谢 <a href="http://www.pm25.in/" target="_blank">PM25.in</a> &middot; <a href="http://www.iconpng.com/" target="_blank">iconpng</a></p>
      <p><a href="http://echarts.baidu.com" target="_blank">Data Visualization by ECharts</a></p>
    </footer>

    <script src="../../../asset/js/jquery.js"></script>
    <script src="../../../asset/js/bootstrap-transition.js"></script>
    <script src="../../../asset/js/bootstrap-alert.js"></script>
    <script src="../../../asset/js/bootstrap-modal.js"></script>
    <script src="../../../asset/js/bootstrap-dropdown.js"></script>
    <script src="../../../asset/js/bootstrap-scrollspy.js"></script>
    <script src="../../../asset/js/bootstrap-tab.js"></script>
    <script src="../../../asset/js/bootstrap-tooltip.js"></script>
    <script src="../../../asset/js/bootstrap-popover.js"></script>
    <script src="../../../asset/js/bootstrap-button.js"></script>
    <script src="../../../asset/js/bootstrap-collapse.js"></script>
    <script src="../../../asset/js/bootstrap-carousel.js"></script>
    <script src="../../../asset/js/bootstrap-typeahead.js"></script>
    <!-- core -->
    <script src="../../www/js/echarts.js"></script>
    <script src="./js/option0.js"></script>
    <script src="./js/option1.js"></script>
    <script src="./js/option2.js"></script>
    <script src="./js/option3.js"></script>
    <script src="./js/china_city_geo.js"></script>
    <script src="./js/data.js"></script>
    <script src="./js/main.js"></script>
</body>
</html>