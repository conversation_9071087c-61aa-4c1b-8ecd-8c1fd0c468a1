/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class MdmReportFolder{ } 
 * 
 * 摘    要： mdmReportFolder
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2020-06-23 04:02:24
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.report;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

import java.util.List;

public class MdmReportFolder extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public MdmReportFolder() {
	}

	/**name*/
	private String name;
	/**parentId*/
	private String parentId;

	
	/**
	 * 是否展开
	 */
	private boolean expanded;

	
	private List<MdmReportFolder> mdmReportFolderList;
	
	
	
	
	public boolean isExpanded() {
		return expanded;
	}

	public void setExpanded(boolean expanded) {
		this.expanded = expanded;
	}

	public List<MdmReportFolder> getMdmReportFolderList() {
		return mdmReportFolderList;
	}

	public void setMdmReportFolderList(List<MdmReportFolder> mdmReportFolderList) {
		this.mdmReportFolderList = mdmReportFolderList;
	}

	/**
	 * name
	 * @return the value of MDM_ASSET_REPORT_FOLDER.NAME
	 * @mbggenerated 2020-06-23 04:02:24
	 */
	public String getName() {
		return name;
	}

	/**
	 * name
	 * @param name the value for MDM_ASSET_REPORT_FOLDER.NAME
	 * @mbggenerated 2020-06-23 04:02:24
	 */
    	public void setName(String name) {
		this.name = name;
	}
	/**
	 * parentId
	 * @return the value of MDM_ASSET_REPORT_FOLDER.PARENT_ID
	 * @mbggenerated 2020-06-23 04:02:24
	 */
	public String getParentId() {
		return parentId;
	}

	/**
	 * parentId
	 * @param parentId the value for MDM_ASSET_REPORT_FOLDER.PARENT_ID
	 * @mbggenerated 2020-06-23 04:02:24
	 */
    	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public MdmReportFolder(String name
	, String parentId ) {
		super();
		this.name = name;
		this.parentId = parentId;
	}

}