/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetVeeRuleDataitem{ } 
 * 
 * 摘    要： assetVeeRuleDataitem
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2019-03-04 09:38:36
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.vee;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class AssetVeeRuleDataitem  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public AssetVeeRuleDataitem() {
	}
	/**
	 * 实体编号（唯一标识）
	 */
	protected String ruleId;
	/**dataitemKey*/
	private java.lang.String dataitemKey;
	/**cycleCount*/
	private java.math.BigDecimal cycleCount;
	/**cycleType*/
	private java.lang.String cycleType;

	
	
	
	public String getRuleId() {
		return ruleId;
	}

	public void setRuleId(String ruleId) {
		this.ruleId = ruleId;
	}

	/**
	 * dataitemKey
	 * @return the value of ASSET_VEE_RULE_DATAITEM.DATAITEM_KEY
	 * @mbggenerated 2019-03-04 09:38:36
	 */
	public java.lang.String getDataitemKey() {
		return dataitemKey;
	}

	/**
	 * dataitemKey
	 * @param dataitemKey the value for ASSET_VEE_RULE_DATAITEM.DATAITEM_KEY
	 * @mbggenerated 2019-03-04 09:38:36
	 */
    	public void setDataitemKey(java.lang.String dataitemKey) {
		this.dataitemKey = dataitemKey;
	}
	/**
	 * cycleCount
	 * @return the value of ASSET_VEE_RULE_DATAITEM.CYCLE_COUNT
	 * @mbggenerated 2019-03-04 09:38:36
	 */
	public java.math.BigDecimal getCycleCount() {
		return cycleCount;
	}

	/**
	 * cycleCount
	 * @param cycleCount the value for ASSET_VEE_RULE_DATAITEM.CYCLE_COUNT
	 * @mbggenerated 2019-03-04 09:38:36
	 */
    	public void setCycleCount(java.math.BigDecimal cycleCount) {
		this.cycleCount = cycleCount;
	}
	/**
	 * cycleType
	 * @return the value of ASSET_VEE_RULE_DATAITEM.CYCLE_TYPE
	 * @mbggenerated 2019-03-04 09:38:36
	 */
	public java.lang.String getCycleType() {
		return cycleType;
	}

	/**
	 * cycleType
	 * @param cycleType the value for ASSET_VEE_RULE_DATAITEM.CYCLE_TYPE
	 * @mbggenerated 2019-03-04 09:38:36
	 */
    	public void setCycleType(java.lang.String cycleType) {
		this.cycleType = cycleType;
	}

	public AssetVeeRuleDataitem(java.lang.String dataitemKey 
	,java.math.BigDecimal cycleCount 
	,java.lang.String cycleType ) {
		super();
		this.dataitemKey = dataitemKey;
		this.cycleCount = cycleCount;
		this.cycleType = cycleType;
	}

}