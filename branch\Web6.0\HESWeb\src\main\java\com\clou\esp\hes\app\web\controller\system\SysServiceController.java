/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysService{ } 
 * 
 * 摘    要： sysService
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-01-29 08:53:20
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.system;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.system.SysServer;
import com.clou.esp.hes.app.web.model.system.SysService;
import com.clou.esp.hes.app.web.model.system.SysServiceAttribute;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.system.SysServerService;
import com.clou.esp.hes.app.web.service.system.SysServiceAttributeService;
import com.clou.esp.hes.app.web.service.system.SysServiceService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;

/**
 * <AUTHOR>
 * @时间：2018-01-29 08:53:20
 * @描述：sysService类
 */
@Controller
@RequestMapping("/sysServiceController")
public class SysServiceController extends BaseController{

 	@Resource
    private SysServiceService sysServiceService;
 	@Resource
    private SysServerService sysServerService;
 	@Resource
    private SysServiceAttributeService sysServiceAttributeService;
 	
 	@Resource
    private DataUserLogService dataUserLogService;

	/**
	 * 跳转到sysService列表页面
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/system/sysServiceList");
    }
	
	/**
     * 根据条件，查询Measurement Data数据
     * @param id
     * @return
     */
	@ResponseBody
    @RequestMapping(value = "getServers")
    public AjaxJson getServers(HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
        	List<SysServer> list = sysServerService.getAllList();
        	j.setObj(list);
        }   catch (Exception e) {
            e.printStackTrace();
        }
        return j;
	}

	/**
	 * 跳转到sysService新增界面
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "toAddSysService")
	public ModelAndView sysService(SysService sysService,HttpServletRequest request, Model model) {
		List<SysServer> list = sysServerService.getAllList();
		if(StringUtil.isNotEmpty(sysService.getId())){
			try {
                sysService = sysServiceService.getEntity(sysService.getId());
            }
            catch (Exception e) {
                e.printStackTrace();
            }
			model.addAttribute("sysService", sysService);
		}
		String sysServerList = RoletoJson.listToReplaceStr(list, "id", "introduction", ";");
		model.addAttribute("sysServerList", sysServerList);
		model.addAttribute("sysServiceList", "1:" + MutiLangUtil.doMutiLang("deploymentAndClusterManagement.channel")
				+ ";2:" + MutiLangUtil.doMutiLang("deploymentAndClusterManagement.messageBus")
				+ ";3:" + MutiLangUtil.doMutiLang("deploymentAndClusterManagement.schedule")
				+";4:" + MutiLangUtil.doMutiLang("deploymentAndClusterManagement.UCI")
				+";5:" + MutiLangUtil.doMutiLang("deploymentAndClusterManagement.calculation")
				+";6:" + MutiLangUtil.doMutiLang("deploymentAndClusterManagement.application"));
		return new ModelAndView("/system/deploymentAndCluster/addService");
	}


	/**
	 * sysService查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=sysServiceService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除sysService信息
     * @param id
     * @return
     */
    @RequestMapping(value = "delService")
    @ResponseBody
    public AjaxJson delService(SysService sysService, HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        try {
            SysUser su = TokenManager.getToken();
            SysService entity = sysServiceService.getEntity(sysService.getId());
            if(sysServiceService.deleteById(sysService.getId()) > 0){
            	int result = sysServiceAttributeService.deleteById(sysService.getId());
            	//添加操作日志
	            dataUserLogService.insertDataUserLog(su.getId(), 
	            		"Deployment & Cluster Mgmt", "Delete Service", "Delete Service (Name="+ entity.getIntroduction() +")");
            	System.out.println("删除的service properties 数量为---->" + result);
                j.setMsg(MutiLangUtil.doMutiLang("system.delSuccess"));
            }else{
                j.setSuccess(false);
                j.setMsg(MutiLangUtil.doMutiLang("system.delFail"));
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
    
    /**
     * 保存sysService信息
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })SysService sysService,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        SysService t=new  SysService();
        try {
        	SysUser su = TokenManager.getToken();
        	/**
        	 * 判断条件：
        	 * 		1.整个系统只能有一个application、UCI、calculation
        	 * 		2.一个server下面相同的service只能有一个
        	 */
        	List<String> strList = new ArrayList<>();
        	strList.add("4");
        	strList.add("5");
        	strList.add("6");
        	if(strList.contains(sysService.getServiceType())){
        		SysService temp1 = new SysService();
        		temp1.setServiceType(sysService.getServiceType());
        		List<SysService> listTemp1 = sysServiceService.getList(temp1);
        		if (listTemp1.size() > 0) {
        			j.setSuccess(false);
                    j.setMsg(MutiLangUtil.doMutiLang("deploymentAndClusterManagement.serviceExistSystem"));
                    return j;
				}
        	}
        	/**
        	 * 一个server下面相同的service只能有一个
        	 */
        	SysService temp2 = new SysService();
    		temp2.setServiceType(sysService.getServiceType());
    		temp2.setServerId(sysService.getServerId());
    		List<SysService> listTemp2 = sysServiceService.getList(temp2);
    		if (listTemp2.size() > 0) {
    			j.setSuccess(false);
                j.setMsg(MutiLangUtil.doMutiLang("deploymentAndClusterManagement.serviceExistServer"));
                return j;
			}
    		/**
        	 * service的端口号不能重复
        	 */
        	SysService temp3 = new SysService();
    		temp3.setHostId(sysService.getHostId());
    		temp3.setServerId(sysService.getServerId());
    		List<SysService> listTemp3 = sysServiceService.getList(temp3);
    		if (listTemp3.size() > 0) {
    			j.setSuccess(false);
                j.setMsg(MutiLangUtil.doMutiLang("deploymentAndClusterManagement.hostIdExistServer"));
                return j;
			}
	        if(StringUtil.isNotEmpty(sysService.getId())){
	        	t = sysServiceService.getEntity(sysService.getId());
				MyBeanUtils.copyBeanNotNull2Bean(sysService, t);
				sysServiceService.update(t);
				j.setMsg(MutiLangUtil.doMutiLang("system.updateSucc"));
				
			}else{
				sysService.setIsOnline("1");
	            sysServiceService.save(sysService);
	            //添加操作日志
	            dataUserLogService.insertDataUserLog(su.getId(), 
	            		"Deployment & Cluster Mgmt", "Add Service", "Add Service (Name="+ sysService.getIntroduction() +")");
	            j.setMsg(MutiLangUtil.doMutiLang("system.addSucc"));
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
    
    /**
     * 保存sysService 的channel list信息
     * @param id
     * @return
     */
    @Transactional
    @RequestMapping(value = "saveServiceChannelList")
    @ResponseBody
    public AjaxJson saveServiceChannelList(String serviceId, String channelServiceList, HttpServletRequest request) {
        AjaxJson json = new AjaxJson();
        json.setMsg(MutiLangUtil.doMutiLang("system.saveSucc"));
        List<SysServiceAttribute> saveList = new ArrayList<SysServiceAttribute>();
        try {
//        	SysUser su = TokenManager.getToken();
        	//获取界面的数据
    		JSONArray listJsons = null;
	        if(StringUtil.isNotEmpty(channelServiceList)){
	        	listJsons = JSONArray.fromObject(channelServiceList);
	        }else{
	        	json.setSuccess(false);
	    		json.setMsg(MutiLangUtil.doMutiLang("deploymentAndClusterManagement.pleaseAddChannel"));
	    		return json;
	        }
	        //删除
        	if(serviceId != null){
        		SysServiceAttribute entity = new SysServiceAttribute();
        		entity.setId(serviceId);
        		int deleteRes = sysServiceAttributeService.delete(entity);
        		System.out.println("删除Channel List, ID为---->" + serviceId + "， 记录长度为： " + deleteRes);
        	}
        	/*
    		 * 查询service下属的channel数据，
    		 * 获取现在的数据，然后覆盖（先删除旧数据，然后保存新数据）
    		 */
	        for (int i = 0; i < listJsons.size(); i++) {
	        	JSONObject jsonObj = listJsons.getJSONObject(i);
	        	SysServiceAttribute entity = new SysServiceAttribute();
	        	entity.setId(serviceId);
	        	entity.setAttributeName(jsonObj.getString("attributeName"));
	        	entity.setAttributeValue(jsonObj.getString("attributeValue"));
	        	saveList.add(entity);
			}
	        if(saveList.size() > 0){
	        	sysServiceAttributeService.batchSaveAttr(saveList);
	        	json.setMsg(MutiLangUtil.doMutiLang("system.saveSucc"));
	        }
        }catch (Exception e) {
            e.printStackTrace();
            json.setSuccess(false);
            json.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return json;
    }
	
    
    /**
     *更新serverId 
     */
    @Transactional
    @RequestMapping(value = "changeServer")
    @ResponseBody
    public AjaxJson changeServer(String serviceId, String serverId, HttpServletRequest request) {
        AjaxJson json = new AjaxJson();
        try {
        	SysService  sysService = sysServiceService.getEntity(serviceId);
        	if(sysService!=null) {
        		sysService.setServerId(serverId);
        		sysServiceService.update(sysService);
        	}else {
        		json.setSuccess(false);
    	        json.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        	}
	    }catch (Exception e) {
	        e.printStackTrace();
	        json.setSuccess(false);
	        json.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
	    }
	    return json;
	}
}