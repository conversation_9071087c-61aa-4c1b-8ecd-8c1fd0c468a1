
package ch.iec.tc57._2011.meterreadings_;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the ch.iec.tc57._2011.meterreadings_ package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _MeterReadings_QNAME = new QName("http://iec.ch/TC57/2011/MeterReadings#", "MeterReadings");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: ch.iec.tc57._2011.meterreadings_
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link ReadingQuality }
     * 
     */
    public ReadingQuality createReadingQuality() {
        return new ReadingQuality();
    }

    /**
     * Create an instance of {@link ch.iec.tc57._2011.meterreadings_.PendingCalculation }
     * 
     */
    public ch.iec.tc57._2011.meterreadings_.PendingCalculation createPendingCalculation() {
        return new ch.iec.tc57._2011.meterreadings_.PendingCalculation();
    }

    /**
     * Create an instance of {@link MeterReading }
     * 
     */
    public MeterReading createMeterReading() {
        return new MeterReading();
    }

    /**
     * Create an instance of {@link MeterReading.Readings }
     * 
     */
    public MeterReading.Readings createMeterReadingReadings() {
        return new MeterReading.Readings();
    }

    /**
     * Create an instance of {@link MeterReading.Meter }
     * 
     */
    public MeterReading.Meter createMeterReadingMeter() {
        return new MeterReading.Meter();
    }

    /**
     * Create an instance of {@link MeterReading.Meter.Names }
     * 
     */
    public MeterReading.Meter.Names createMeterReadingMeterNames() {
        return new MeterReading.Meter.Names();
    }

    /**
     * Create an instance of {@link MeterReading.Meter.Names.NameType }
     * 
     */
    public MeterReading.Meter.Names.NameType createMeterReadingMeterNamesNameType() {
        return new MeterReading.Meter.Names.NameType();
    }

    /**
     * Create an instance of {@link MeterReading.IntervalBlocks }
     * 
     */
    public MeterReading.IntervalBlocks createMeterReadingIntervalBlocks() {
        return new MeterReading.IntervalBlocks();
    }

    /**
     * Create an instance of {@link MeterReading.IntervalBlocks.PendingCalculation }
     * 
     */
    public MeterReading.IntervalBlocks.PendingCalculation createMeterReadingIntervalBlocksPendingCalculation() {
        return new MeterReading.IntervalBlocks.PendingCalculation();
    }

    /**
     * Create an instance of {@link MeterReading.IntervalBlocks.IntervalReadings }
     * 
     */
    public MeterReading.IntervalBlocks.IntervalReadings createMeterReadingIntervalBlocksIntervalReadings() {
        return new MeterReading.IntervalBlocks.IntervalReadings();
    }

    /**
     * Create an instance of {@link MeterReading.IntervalBlocks.IntervalReadings.ReadingQualities }
     * 
     */
    public MeterReading.IntervalBlocks.IntervalReadings.ReadingQualities createMeterReadingIntervalBlocksIntervalReadingsReadingQualities() {
        return new MeterReading.IntervalBlocks.IntervalReadings.ReadingQualities();
    }

    /**
     * Create an instance of {@link ch.iec.tc57._2011.meterreadings_.Meter }
     * 
     */
    public ch.iec.tc57._2011.meterreadings_.Meter createMeter() {
        return new ch.iec.tc57._2011.meterreadings_.Meter();
    }

    /**
     * Create an instance of {@link ch.iec.tc57._2011.meterreadings_.Meter.Names }
     * 
     */
    public ch.iec.tc57._2011.meterreadings_.Meter.Names createMeterNames() {
        return new ch.iec.tc57._2011.meterreadings_.Meter.Names();
    }

    /**
     * Create an instance of {@link ch.iec.tc57._2011.meterreadings_.Meter.Names.NameType }
     * 
     */
    public ch.iec.tc57._2011.meterreadings_.Meter.Names.NameType createMeterNamesNameType() {
        return new ch.iec.tc57._2011.meterreadings_.Meter.Names.NameType();
    }

    /**
     * Create an instance of {@link EndDeviceEvent }
     * 
     */
    public EndDeviceEvent createEndDeviceEvent() {
        return new EndDeviceEvent();
    }

    /**
     * Create an instance of {@link EndDeviceEvent.Names }
     * 
     */
    public EndDeviceEvent.Names createEndDeviceEventNames() {
        return new EndDeviceEvent.Names();
    }

    /**
     * Create an instance of {@link EndDeviceEvent.Names.NameType }
     * 
     */
    public EndDeviceEvent.Names.NameType createEndDeviceEventNamesNameType() {
        return new EndDeviceEvent.Names.NameType();
    }

    /**
     * Create an instance of {@link MeterReadings }
     * 
     */
    public MeterReadings createMeterReadings() {
        return new MeterReadings();
    }

    /**
     * Create an instance of {@link DateTimeInterval }
     * 
     */
    public DateTimeInterval createDateTimeInterval() {
        return new DateTimeInterval();
    }

    /**
     * Create an instance of {@link EndDeviceEventDetail }
     * 
     */
    public EndDeviceEventDetail createEndDeviceEventDetail() {
        return new EndDeviceEventDetail();
    }

    /**
     * Create an instance of {@link ch.iec.tc57._2011.meterreadings_.EndDeviceEventType }
     * 
     */
    public ch.iec.tc57._2011.meterreadings_.EndDeviceEventType createEndDeviceEventType() {
        return new ch.iec.tc57._2011.meterreadings_.EndDeviceEventType();
    }

    /**
     * Create an instance of {@link Name }
     * 
     */
    public Name createName() {
        return new Name();
    }

    /**
     * Create an instance of {@link ch.iec.tc57._2011.meterreadings_.NameType }
     * 
     */
    public ch.iec.tc57._2011.meterreadings_.NameType createNameType() {
        return new ch.iec.tc57._2011.meterreadings_.NameType();
    }

    /**
     * Create an instance of {@link RationalNumber }
     * 
     */
    public RationalNumber createRationalNumber() {
        return new RationalNumber();
    }

    /**
     * Create an instance of {@link ReadingInterharmonic }
     * 
     */
    public ReadingInterharmonic createReadingInterharmonic() {
        return new ReadingInterharmonic();
    }

    /**
     * Create an instance of {@link ch.iec.tc57._2011.meterreadings_.ReadingQualityType }
     * 
     */
    public ch.iec.tc57._2011.meterreadings_.ReadingQualityType createReadingQualityType() {
        return new ch.iec.tc57._2011.meterreadings_.ReadingQualityType();
    }

    /**
     * Create an instance of {@link ch.iec.tc57._2011.meterreadings_.ReadingType }
     * 
     */
    public ch.iec.tc57._2011.meterreadings_.ReadingType createReadingType() {
        return new ch.iec.tc57._2011.meterreadings_.ReadingType();
    }

    /**
     * Create an instance of {@link ch.iec.tc57._2011.meterreadings_.Status }
     * 
     */
    public ch.iec.tc57._2011.meterreadings_.Status createStatus() {
        return new ch.iec.tc57._2011.meterreadings_.Status();
    }

    /**
     * Create an instance of {@link UsagePoint }
     * 
     */
    public UsagePoint createUsagePoint() {
        return new UsagePoint();
    }

    /**
     * Create an instance of {@link ReadingQuality.ReadingQualityType }
     * 
     */
    public ReadingQuality.ReadingQualityType createReadingQualityReadingQualityType() {
        return new ReadingQuality.ReadingQualityType();
    }

    /**
     * Create an instance of {@link ch.iec.tc57._2011.meterreadings_.PendingCalculation.ReadingType }
     * 
     */
    public ch.iec.tc57._2011.meterreadings_.PendingCalculation.ReadingType createPendingCalculationReadingType() {
        return new ch.iec.tc57._2011.meterreadings_.PendingCalculation.ReadingType();
    }

    /**
     * Create an instance of {@link MeterReading.Readings.ReadingType }
     * 
     */
    public MeterReading.Readings.ReadingType createMeterReadingReadingsReadingType() {
        return new MeterReading.Readings.ReadingType();
    }

    /**
     * Create an instance of {@link MeterReading.Meter.Status }
     * 
     */
    public MeterReading.Meter.Status createMeterReadingMeterStatus() {
        return new MeterReading.Meter.Status();
    }

    /**
     * Create an instance of {@link MeterReading.Meter.Names.NameType.NameTypeAuthority }
     * 
     */
    public MeterReading.Meter.Names.NameType.NameTypeAuthority createMeterReadingMeterNamesNameTypeNameTypeAuthority() {
        return new MeterReading.Meter.Names.NameType.NameTypeAuthority();
    }

    /**
     * Create an instance of {@link MeterReading.IntervalBlocks.ReadingType }
     * 
     */
    public MeterReading.IntervalBlocks.ReadingType createMeterReadingIntervalBlocksReadingType() {
        return new MeterReading.IntervalBlocks.ReadingType();
    }

    /**
     * Create an instance of {@link MeterReading.IntervalBlocks.PendingCalculation.ReadingType }
     * 
     */
    public MeterReading.IntervalBlocks.PendingCalculation.ReadingType createMeterReadingIntervalBlocksPendingCalculationReadingType() {
        return new MeterReading.IntervalBlocks.PendingCalculation.ReadingType();
    }

    /**
     * Create an instance of {@link MeterReading.IntervalBlocks.IntervalReadings.ReadingQualities.ReadingQualityType }
     * 
     */
    public MeterReading.IntervalBlocks.IntervalReadings.ReadingQualities.ReadingQualityType createMeterReadingIntervalBlocksIntervalReadingsReadingQualitiesReadingQualityType() {
        return new MeterReading.IntervalBlocks.IntervalReadings.ReadingQualities.ReadingQualityType();
    }

    /**
     * Create an instance of {@link ch.iec.tc57._2011.meterreadings_.Meter.Names.NameType.NameTypeAuthority }
     * 
     */
    public ch.iec.tc57._2011.meterreadings_.Meter.Names.NameType.NameTypeAuthority createMeterNamesNameTypeNameTypeAuthority() {
        return new ch.iec.tc57._2011.meterreadings_.Meter.Names.NameType.NameTypeAuthority();
    }

    /**
     * Create an instance of {@link EndDeviceEvent.EndDeviceEventType }
     * 
     */
    public EndDeviceEvent.EndDeviceEventType createEndDeviceEventEndDeviceEventType() {
        return new EndDeviceEvent.EndDeviceEventType();
    }

    /**
     * Create an instance of {@link EndDeviceEvent.Names.NameType.NameTypeAuthority }
     * 
     */
    public EndDeviceEvent.Names.NameType.NameTypeAuthority createEndDeviceEventNamesNameTypeNameTypeAuthority() {
        return new EndDeviceEvent.Names.NameType.NameTypeAuthority();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MeterReadings }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://iec.ch/TC57/2011/MeterReadings#", name = "MeterReadings")
    public JAXBElement<MeterReadings> createMeterReadings(MeterReadings value) {
        return new JAXBElement<MeterReadings>(_MeterReadings_QNAME, MeterReadings.class, null, value);
    }

}
