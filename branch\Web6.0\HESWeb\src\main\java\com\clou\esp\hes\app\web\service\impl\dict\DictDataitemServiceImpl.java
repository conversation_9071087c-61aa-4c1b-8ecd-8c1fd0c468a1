/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictDataitem{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.dict;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.dao.dict.DictDataitemDao;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;
import com.clou.esp.hes.app.web.model.dict.DictDataitemGroup;
import com.clou.esp.hes.app.web.service.dict.DictDataitemService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dictDataitemService")
public class DictDataitemServiceImpl extends CommonServiceImpl<DictDataitem>
		implements DictDataitemService {

	@Resource
	private DictDataitemDao dictDataitemDao;

	@Autowired
	public void setCommonService() {
		super.setCommonService(dictDataitemDao);
	}

	public DictDataitemServiceImpl() {
	}

	@Override
	public List<DictDataitem> getDictDataitemByGroupId(String groupId) {
		return dictDataitemDao.getDictDataitemByGroupId(groupId);
	}

	@Override
	public List<DictDataitem> getMeterDataEventExportList(DictDataitem temp) {
		return dictDataitemDao.getMeterDataEventExportList(temp);
	}

	@Override
	public List<DictDataitem> getList(DictDataitem entity){
		return this.dictDataitemDao.getList(entity);
	}
	
	@Override
	public List<DictDataitem> getListByIds(List<String> Ids){
		return this.dictDataitemDao.getListByIds(Ids);
	}

	@Override
	public String getDataitemBySubId(String subId, String protoId) {
		return this.dictDataitemDao.getDataitemBySubId(subId,protoId);
	}
}