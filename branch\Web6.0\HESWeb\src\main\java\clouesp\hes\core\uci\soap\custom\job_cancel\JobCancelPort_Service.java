package clouesp.hes.core.uci.soap.custom.job_cancel;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import javax.xml.ws.Service;

/**
 * This class was generated by Apache CXF 3.1.14
 * 2018-05-07T14:23:05.651+08:00
 * Generated source version: 3.1.14
 * 
 */
@WebServiceClient(name = "JobCancelPort", 
                  wsdlLocation = "http://10.8.165.49:8080/UCI-1.0-SNAPSHOT/services/JobCancel?wsdl",
                  targetNamespace = "http://job_cancel.custom.soap.uci.core.hes.clouesp/") 
public class JobCancelPort_Service extends Service {

    public final static URL WSDL_LOCATION;

    public final static QName SERVICE = new QName("http://job_cancel.custom.soap.uci.core.hes.clouesp/", "JobCancelPort");
    public final static QName JobCancelPortPort = new QName("http://job_cancel.custom.soap.uci.core.hes.clouesp/", "JobCancelPortPort");
    static {
        URL url = null;
        try {
            url = new URL("http://10.8.165.49:8080/UCI-1.0-SNAPSHOT/services/JobCancel?wsdl");
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(JobCancelPort_Service.class.getName())
                .log(java.util.logging.Level.INFO, 
                     "Can not initialize the default wsdl from {0}", "http://10.8.165.49:8080/UCI-1.0-SNAPSHOT/services/JobCancel?wsdl");
        }
        WSDL_LOCATION = url;
    }

    public JobCancelPort_Service(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public JobCancelPort_Service(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public JobCancelPort_Service() {
        super(WSDL_LOCATION, SERVICE);
    }
    
    public JobCancelPort_Service(WebServiceFeature ... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }

    public JobCancelPort_Service(URL wsdlLocation, WebServiceFeature ... features) {
        super(wsdlLocation, SERVICE, features);
    }

    public JobCancelPort_Service(URL wsdlLocation, QName serviceName, WebServiceFeature ... features) {
        super(wsdlLocation, serviceName, features);
    }    




    /**
     *
     * @return
     *     returns JobCancelPort
     */
    @WebEndpoint(name = "JobCancelPortPort")
    public JobCancelPort getJobCancelPortPort() {
        return super.getPort(JobCancelPortPort, JobCancelPort.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns JobCancelPort
     */
    @WebEndpoint(name = "JobCancelPortPort")
    public JobCancelPort getJobCancelPortPort(WebServiceFeature... features) {
        return super.getPort(JobCancelPortPort, JobCancelPort.class, features);
    }

}
