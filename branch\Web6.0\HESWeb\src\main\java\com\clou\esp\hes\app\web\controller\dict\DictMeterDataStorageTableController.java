/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictMeterDataStorageTable{ } 
 * 
 * 摘    要： dictMeterDataStorageTable
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.dict;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.dict.DictMeterDataStorageTable;
import com.clou.esp.hes.app.web.model.dict.DictProfile;
import com.clou.esp.hes.app.web.service.dict.DictMeterDataStorageInfoService;
import com.clou.esp.hes.app.web.service.dict.DictMeterDataStorageTableService;
import com.clou.esp.hes.app.web.service.dict.DictProfileService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;

/**
 * <AUTHOR>
 * @时间：2017-11-22 03:30:03
 * @描述：dictMeterDataStorageTable类
 */
@Controller
@RequestMapping("/dictMeterDataStorageTableController")
public class DictMeterDataStorageTableController extends BaseController{

 	@Resource
    private DictMeterDataStorageTableService dictMeterDataStorageTableService;
 	@Resource
 	private DictMeterDataStorageInfoService dictMeterDataStorageInfoService;
 	@Resource
 	private DictProfileService   			 dictProfileService;

	/**
	 * 跳转到dictMeterDataStorageTable列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/dict/dictMeterDataStorageTableList");
    }

	/**
	 * 跳转到dictMeterDataStorageTable新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dictMeterDataStorageTable")
	public ModelAndView dictMeterDataStorageTable(DictMeterDataStorageTable dictMeterDataStorageTable,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dictMeterDataStorageTable.getId())){
                dictMeterDataStorageTable=dictMeterDataStorageTableService.getEntity(dictMeterDataStorageTable.getId());
		}
		
		List<DictProfile>  list = this.dictProfileService.getAllList();
		String profileReplace=RoletoJson.listToReplaceStr(list, "id", "name", ",");
		
		model.addAttribute("profileReplace",profileReplace);
		model.addAttribute("dictMeterDataStorageTable", dictMeterDataStorageTable);
		return new ModelAndView("/dict/dictMeterDataStorageTable");
	}


	/**
	 * dictMeterDataStorageTable查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request
    		,String id,String name,Boolean select) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        if(select==null||!select) {
        	return j;
        }
        try {
        	 jqGridSearchTo.put("id", id);
        	 jqGridSearchTo.put("name", name);
             j=dictMeterDataStorageTableService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除dictMeterDataStorageTable信息
     * 
     * @param id
     * @return
     */
	@Transactional
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DictMeterDataStorageTable dictMeterDataStorageTable,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            dictMeterDataStorageTable = this.dictMeterDataStorageTableService.getEntity(dictMeterDataStorageTable.getId());
            long count =0l;
            try {
            	count= this.dictMeterDataStorageTableService.getCountTableName(dictMeterDataStorageTable.getName());
            }catch(Exception ex) {
            	
            }
            if(count==0) {
            	dictMeterDataStorageInfoService.deleteByTableId(dictMeterDataStorageTable.getId());
            	dictMeterDataStorageTableService.deleteById(dictMeterDataStorageTable.getId());
            	try {
            		dictMeterDataStorageTableService.deleteTable(dictMeterDataStorageTable.getName());
            	}catch(Exception ex) {};
		        j.setSuccess(true);
		        j.setMsg(MutiLangUtil.doMutiLang("system.delSuccess"));
            }else {
            	j.setSuccess(false);
            	j.setMsg(MutiLangUtil.doMutiLang("storeTable.delHasData"));
	        }
	    } catch (Exception e) {
	    	e.printStackTrace();
	    	j.setSuccess(false);
	    	j.setMsg(MutiLangUtil.doMutiLang("system.delFail"));
	    }
    return j;
}
    /**
     * 保存dictMeterDataStorageTable信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DictMeterDataStorageTable dictMeterDataStorageTable,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DictMeterDataStorageTable t=new  DictMeterDataStorageTable();
        try {
        if(StringUtil.isNotEmpty(dictMeterDataStorageTable.getOldId())){
        	t=dictMeterDataStorageTableService.getEntity(dictMeterDataStorageTable.getOldId());
			MyBeanUtils.copyBeanNotNull2Bean(dictMeterDataStorageTable, t);
			dictMeterDataStorageTableService.update(t);
			j.setMsg(MutiLangUtil.doMutiLang("system.updateSucc"));
		}else{
			//新增表
			this.dictMeterDataStorageTableService.createTable(dictMeterDataStorageTable.getName());
            this.dictMeterDataStorageTableService.save(dictMeterDataStorageTable);
            j.setMsg(MutiLangUtil.doMutiLang("system.addSucc"));
		}
    }catch (Exception e) {
        e.printStackTrace();
        j.setSuccess(false);
        j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
    }
        return j;
    }
	
	@RequestMapping(value = "isExist")
  	@ResponseBody
  	public AjaxJson isExist(String id,String oldId,String name,HttpServletRequest request) {
  		AjaxJson j = new AjaxJson();
  		StringBuffer sb = new StringBuffer();
  		if(!id.equals(oldId)) {
  			DictMeterDataStorageTable tmp = this.dictMeterDataStorageTableService.getEntity(id);
  			if(tmp!=null) {
  				sb.append(MutiLangUtil.doMutiLang("storeTable.idExist")+"<br/>");
  			}
  		}
  		//判断name
  		DictMeterDataStorageTable entity = new DictMeterDataStorageTable();
  		entity.setName(name);
  		List<DictMeterDataStorageTable> list = this.dictMeterDataStorageTableService.getList(entity);
  		if(list!=null&&list.size()>0) {
  			if(StringUtils.isNotEmpty(oldId)) {//编辑的时候存在,看是不是同一条
	  			entity = list.get(0);
	  			if(list.size()>1||!oldId.equals(entity.getId())) {
	  				sb.append(MutiLangUtil.doMutiLang("storeTable.nameExist"));
	  			}
  			}else {
  				sb.append(MutiLangUtil.doMutiLang("storeTable.nameExist"));
  			}
  		}
  		if(sb.length()>0) {  
  			j.setSuccess(false);
  			j.setMsg(sb.toString());
  		}
  		return j;
  	}
	@RequestMapping(value = "canDel")
	@ResponseBody
	public AjaxJson canDel(String tableName,HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			long count = this.dictMeterDataStorageTableService.getCountTableName(tableName);
			j.setObj(count);
		}catch(Exception e) {
			 //e.printStackTrace();
			 j.setObj(0);//有可能是表不存在
		}
		return j;
	}
	
	
	
}