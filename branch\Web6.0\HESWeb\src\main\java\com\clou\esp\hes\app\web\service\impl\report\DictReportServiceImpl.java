/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictReport{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-05-22 04:14:25
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.report;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.report.DictReportDao;
import com.clou.esp.hes.app.web.model.data.DataCalcObj;
import com.clou.esp.hes.app.web.model.report.BillingReport;
import com.clou.esp.hes.app.web.model.report.DictReport;
import com.clou.esp.hes.app.web.model.report.LineLossDetailReport;
import com.clou.esp.hes.app.web.model.report.LineLossReport;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.clou.esp.hes.app.web.service.report.DictReportService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@Component
@Service("dictReportService")
public class DictReportServiceImpl  extends CommonServiceImpl<DictReport>  implements DictReportService {

	@Resource
	private DictReportDao dictReportDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dictReportDao);
    }
	@SuppressWarnings("rawtypes")
	public DictReportServiceImpl() {}
	
	@Override
	public JqGridResponseTo findLineLossSingleJqGrid(
			JqGridSearchTo jqGridSearchTo) {
		// TODO Auto-generated method stub
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		PageInfo<LineLossReport> pageInfo = new PageInfo<LineLossReport>(dictReportDao.findLineLossSingleJqGrid(jqGridSearchTo));
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}
	
//	@Override
//	public JqGridResponseTo findLineLossOrganizationJqGrid(
//			JqGridSearchTo jqGridSearchTo) {
//		// TODO Auto-generated method stub
//		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
//		PageInfo<LineLossReport> pageInfo = new PageInfo<LineLossReport>(dictReportDao.findLineLossOrganizationJqGrid(jqGridSearchTo));
//		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
//	}
	
	@Override
	public List<LineLossReport> findLineLossSingleReport(
			JqGridSearchTo jqGridSearchTo) {
		// TODO Auto-generated method stub
		return dictReportDao.findLineLossSingleJqGrid(jqGridSearchTo);
	}
	
	@Override
	public List<LineLossReport> findLineLossOrgReport(
			JqGridSearchTo jqGridSearchTo) {
		// TODO Auto-generated method stub
		return dictReportDao.findLineLossOrganizationJqGrid(jqGridSearchTo);
	}
	@Override
	public JqGridResponseTo findBillingOrganizationJqGrid(
			JqGridSearchTo jqGridSearchTo) {
		// TODO Auto-generated method stub
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		PageInfo<BillingReport> pageInfo = new PageInfo<BillingReport>(dictReportDao.findBillingOrganizationJqGrid(jqGridSearchTo));
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}
	@Override
	public List<BillingReport> findBillingOrgReport(
			JqGridSearchTo jqGridSearchTo) {
		// TODO Auto-generated method stub
		return dictReportDao.exportBillingOrganizationJqGrid(jqGridSearchTo);
	}
	@Override
	public JqGridResponseTo findLineLossDetailReport(
			JqGridSearchTo jqGridSearchTo) {
		// TODO Auto-generated method stub
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		PageInfo<LineLossDetailReport> pageInfo = new PageInfo<LineLossDetailReport>(dictReportDao.findLineLossDetailJqGrid(jqGridSearchTo));
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}
	@Override
	public List<LineLossDetailReport> findLineLossDetailExcel(
			JqGridSearchTo jqGridSearchTo) {
		// TODO Auto-generated method stub
		return dictReportDao.findLineLossDetailJqGrid(jqGridSearchTo);
	}
	
	
	public List<DataCalcObj> getDataCalcObj(JqGridSearchTo jqGridSearchTo){
		return this.dictReportDao.getDataCalcObj(jqGridSearchTo);
	}
	
	public List<BillingReport> findBillingOrganizationMobile(JqGridSearchTo jqGridSearchTo) {
		return this.dictReportDao.findBillingOrganizationMobile(jqGridSearchTo);
	}
}