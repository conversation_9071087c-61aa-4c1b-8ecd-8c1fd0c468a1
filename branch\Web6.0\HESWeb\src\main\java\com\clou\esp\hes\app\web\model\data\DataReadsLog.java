package com.clou.esp.hes.app.web.model.data;

import java.util.Date;

import clouesp.hes.common.logger.loggerquery.LoggerLevel;

import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.excel.Excel;

public class DataReadsLog  {
	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DataReadsLog() {
	}
	private String id;
	@Excel(name = "Device SN", width = 30, groups = ValidGroup1.class)
	private String sn;
	/***时间*/
	@Excel(name = "Request Time", width = 20, groups = ValidGroup1.class)
	private Date date;
	@Excel(name = "Service", width = 15, groups = ValidGroup1.class)
	private String serviceId;
	@Excel(name = "Log Type", width = 15, groups = ValidGroup1.class)
	private String type;
	@Excel(name = "Log Level", width = 15, groups = ValidGroup1.class)
	private LoggerLevel level;
	@Excel(name = "Content", width = 35, groups = ValidGroup1.class)
	private String content;

	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public Date getDate() {
		return date;
	}
	public void setDate(Date date) {
		this.date = date;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	public String getServiceId() {
		return serviceId;
	}
	public void setServiceId(String serviceId) {
		this.serviceId = serviceId;
	}
	public LoggerLevel getLevel() {
		return level;
	}
	public void setLevel(LoggerLevel level) {
		this.level = level;
	}
	public DataReadsLog(String id, Date date, String serviceId, String type,
			String content, LoggerLevel level) {
		super();
		this.id = id;
		this.date = date;
		this.serviceId = serviceId;
		this.type = type;
		this.content = content;
		this.level = level;
	}
	public String getSn() {
		return sn;
	}
	public void setSn(String sn) {
		this.sn = sn;
	}
	
}
