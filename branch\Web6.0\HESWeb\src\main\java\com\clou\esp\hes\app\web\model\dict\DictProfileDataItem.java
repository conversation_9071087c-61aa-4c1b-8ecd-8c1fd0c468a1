/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictProfileDataItem{ } 
 * 
 * 摘    要： dictProfileDataItem
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-02-26 09:36:52
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.dict;

import java.math.BigDecimal;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DictProfileDataItem  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DictProfileDataItem() {
	}
	
	private String 		profileId;
	private String 		dataitemId;
	private Integer		sortId;
	private String 		profileName;
	private String 		profileType;
	private String 		dataitemName;
	
	//item表的属性开始
	private String 		name;
	private String 		protocolId;
	private String 		opType;
	private String 		unit;
	private BigDecimal 	showUnit;
	private String 		protocolCode;
	private String 		dataitemType;
	//item表的属性结束
	

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getOpType() {
		return opType;
	}

	public void setOpType(String opType) {
		this.opType = opType;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public BigDecimal getShowUnit() {
		return showUnit;
	}

	public void setShowUnit(BigDecimal showUnit) {
		this.showUnit = showUnit;
	}

	public String getDataitemType() {
		return dataitemType;
	}

	public void setDataitemType(String dataitemType) {
		this.dataitemType = dataitemType;
	}

	public String getProtocolId() {
		return protocolId;
	}

	public void setProtocolId(String protocolId) {
		this.protocolId = protocolId;
	}

	
	public Integer getSortId() {
		return sortId;
	}

	
    public void setSortId(Integer sortId) {
		this.sortId = sortId;
	}

	public String getProfileId() {
		return profileId;
	}

	public void setProfileId(String profileId) {
		this.profileId = profileId;
	}

	public String getDataitemId() {
		return dataitemId;
	}

	public void setDataitemId(String dataitemId) {
		this.dataitemId = dataitemId;
	}

	public String getDataitemName() {
		return MutiLangUtil.assemblyI18nData(MutiLangUtil.DATAITEM_I18N,dataitemName);
	}

	public void setDataitemName(String dataitemName) {
		this.dataitemName = dataitemName;
	}

	public String getProfileName() {
		return profileName;
	}

	public void setProfileName(String profileName) {
		this.profileName = profileName;
	}

	public String getProtocolCode() {
		return protocolCode;
	}

	public void setProtocolCode(String protocolCode) {
		this.protocolCode = protocolCode;
	}

	public String getProfileType() {
		return profileType;
	}

	public void setProfileType(String profileType) {
		this.profileType = profileType;
	}

}