/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictFunction{ } 
 * 
 * 摘    要： 功能菜单字典
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-01 09:42:07
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.system;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;

import com.clou.esp.hes.app.web.model.system.DictFunction;
import com.clou.esp.hes.app.web.service.system.DictFunctionService;

/**
 * <AUTHOR>
 * @时间：2017-11-01 09:42:07
 * @描述：功能菜单字典类
 */
@Controller
@RequestMapping("/dictFunctionController")
public class DictFunctionController extends BaseController{

 	@Resource
    private DictFunctionService dictFunctionService;

	/**
	 * 跳转到功能菜单字典列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/system/dictFunctionList");
    }

	/**
	 * 跳转到功能菜单字典新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dictFunction")
	public ModelAndView dictFunction(DictFunction dictFunction,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dictFunction.getId())){
			try {
                dictFunction=dictFunctionService.getEntity(dictFunction.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dictFunction", dictFunction);
		}
		return new ModelAndView("/system/dictFunction");
	}


	/**
	 * 功能菜单字典查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=dictFunctionService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除功能菜单字典信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DictFunction dictFunction,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dictFunctionService.deleteById(dictFunction.getId())>0){
                j.setMsg("删除成功");
            }else{
                j.setSuccess(false);
                j.setMsg("删除失败");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
    
    /**
     * 保存功能菜单字典信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DictFunction dictFunction,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DictFunction t=new  DictFunction();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dictFunction.getId())){
        	t=dictFunctionService.getEntity(dictFunction.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dictFunction, t);
				dictFunctionService.update(t);
				j.setMsg("修改成功");
				
			}else{
	            dictFunctionService.save(dictFunction);
	            j.setMsg("创建成功");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
	
	
	
}