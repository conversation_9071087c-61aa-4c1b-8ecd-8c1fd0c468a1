var flag = false;

var calcFlag = false;
var lineId;
var calcOrgId;

function queryLineDataFun(){
	var sn = $("#lineSn").val();
	var name = $("#lineName").val();
	var orgId= $("#line_txt_org_id").val();
	var data = {sn:sn,name:name,flag:flag,orgId:orgId};
	return data;
}

function queryLineDataOnclick() {
	flag = true;
	lineListsearchOnEnterFn();
}

function editUserRowData(rowid,iRow,iCol,e){
	if(rowid == null || rowid == ""){
		//middleErrorMsg(i18n.t("login.usernameNull"));
		alert("rowid is null");
		return;
	}
	var rowData = $("#lineList").jqGrid('getRowData',rowid);
	$("#viewspan").html(rowData.sn +" "+ i18n.t("lineManagementList.properties"));
	
	$("#calcLineId").val(rowData.id);
	$("#lineOrgId").val(rowData.orgId);
	$("#txt_line_sn").val(rowData.sn);
	$("#txt_line_name").val(rowData.name);
	$("#txt_line_organization").val(rowData.orgName);
	$("#txt_line_type").val(rowData.lineType);
	$("#txt_line_vl").val(rowData.voltageLevel);
	
	if(rowData.id != lineId) {
		// 重置 calc Object map 数据项
		$("#calcObjId").val("");
		$("#calcObjProSpan").html(i18n.t("lineManagementList.calculationObjectProperties"));
		
		$("#txt_map_name").val("");
		$("#txt_map_type").val("");
		$("#txt_map_cycle").val("");
		//calculationObjectMapListsearchOnEnterFn();
	}
	
	lineId = rowData.id;
	calcFlag = true;
	calcOrgId = rowData.orgId;
	calculationObjectListsearchOnEnterFn();
	queryLinkedMeterByLineOnclick();
	queryLinkedTransformerByLineOnclick();
}

function onkeypress_Func(){
	if(event.keyCode == 13){
		flag = true;
		lineListsearchOnEnterFn();
	}
}

function queryCalcDataFun(){
	var data = {id:lineId,orgId:calcOrgId,calcFlag:calcFlag};
	return data;
}

function editCalcObjRowData(rowid,iRow,iCol,e){
	if(rowid == null || rowid == ""){
		//middleErrorMsg(i18n.t("login.usernameNull"));
		alert("rowid is null");
		return;
	}
	var rowData = $("#calculationObjectList").jqGrid('getRowData',rowid);
	$("#calcObjProSpan").html(i18n.t("lineManagementList.calculationObjectProperties") +" - " +rowData.name);
	$("#txt_map_name").val(rowData.name);
	$("#calcObjId").val(rowData.id);
	$("#txt_map_type").val(rowData.type);
	$("#txt_map_cycle").val(rowData.tvType);
	
	//calculationObjectMapListsearchOnEnterFn();
}

function queryCalcMapDataFun(){
	var objId = $("#calcObjId").val();
//	if(null == objId || "" == objId){
//		return;
//	}
	
	var data = {objId:objId};
	return data;
}

// options, rowdata, decimal
//1:Line Loss,2:Import,3:Export
function formatCalObjTypeCellValue(cellvalue){
	if(cellvalue == null || cellvalue == ""){
		return;
	}
	
	switch(cellvalue) {
	case 1:return i18n.t("calculationObjectList.lineLoss");
	case 2:return i18n.t("calculationObjectList.import");
	case 3:return i18n.t("calculationObjectList.export");
	}
}

function deleteLine(id){
	var rowData = $("#lineList").jqGrid("getRowData", id);
	
	var close_link = window.parent.layer.confirm(i18n.t("system.aydeltherdate"), 
		{
			btn: [i18n.t("system.determine"), i18n.t("system.cancel")],
			title: i18n.t("system.delete")
		}, function() {
			window.parent.layer.close(close_link);
			layer.load();
			$.ajax({
				async : true,
	            cache : false,
	            traditional: true,
				type: 'POST',
				url: getRootPathWeb()+'/assetLineManagementController/del.do?&id=' + rowData.id,
				dataType: 'json',
				success: function(data) {
					if(data.success) {
						$("#lineList").trigger('reloadGrid');	//刷新界面
						$("#calculationObjectList").trigger('reloadGrid');	//刷新界面
						$("#calculationObjectMapList").trigger('reloadGrid');	//刷新界面
						middleTipMsg(data.msg,true);
						
						if(rowData.id==lineId) {
							lineId = "";
							calcOrgId = "";
							$("#calcObjId").val("");
							
							$("#calcObjProSpan").html(i18n.t("lineManagementList.calculationObjectProperties"));
							$("#viewspan").html("");
							
							$("#txt_map_name").val("");
							$("#txt_map_type").val("");
							$("#txt_map_cycle").val("");
						}
					} else {
						window.parent.layer.msg(data.msg, {icon: 2});
					}
					layer.closeAll('loading');
				},
				error: function(msg) {
					window.parent.layer.msg(i18n.t("system.requestError"), {icon: 2});
					layer.closeAll('loading');
				}
			});
		});
}

function deleteCalcObj(id){
	var rowData = $("#calculationObjectList").jqGrid("getRowData", id);
	
	var close_link = window.parent.layer.confirm(i18n.t("system.aydeltherdate"), 
		{
			btn: [i18n.t("system.determine"), i18n.t("system.cancel")],
			title: i18n.t("system.delete")
		}, function() {
			window.parent.layer.close(close_link);
			layer.load();
			$.ajax({
				async : true,
	            cache : false,
	            traditional: true,
				type: 'POST',
				url: getRootPathWeb()+'/assetLineManagementController/delCalcObj.do?&id=' + rowData.id,
				dataType: 'json',
				success: function(data) {
					if(data.success) {
						$("#calculationObjectList").trigger('reloadGrid');	//刷新界面
						$("#calculationObjectMapList").trigger('reloadGrid');	//刷新界面
						middleTipMsg(data.msg,true);
						
						var calId = $("#calcObjId").val();
						
						if(rowData.id==calId) {
							$("#calcObjId").val("");
							$("#calcObjProSpan").html(i18n.t("lineManagementList.calculationObjectProperties"));
							//$("#viewspan").html("");
							
							$("#txt_map_name").val("");
							$("#txt_map_type").val("");
							$("#txt_map_cycle").val("");
						}
					} else {
						window.parent.layer.msg(data.msg, {icon: 2});
					}
					layer.closeAll('loading');
				},
				error: function(msg) {
					window.parent.layer.msg(i18n.t("system.requestError"), {icon: 2});
					layer.closeAll('loading');
				}
			});
		});
}