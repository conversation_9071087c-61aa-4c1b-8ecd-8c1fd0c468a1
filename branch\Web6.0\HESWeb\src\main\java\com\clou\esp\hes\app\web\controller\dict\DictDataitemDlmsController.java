/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictDataitemDlms{ } 
 * 
 * 摘    要： dlms规约数据项
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.dict;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;

import com.clou.esp.hes.app.web.model.dict.DictDataitemDlms;
import com.clou.esp.hes.app.web.service.dict.DictDataitemDlmsService;

/**
 * <AUTHOR>
 * @时间：2017-11-22 03:30:03
 * @描述：dlms规约数据项类
 */
@Controller
@RequestMapping("/dictDataitemDlmsController")
public class DictDataitemDlmsController extends BaseController{

 	@Resource
    private DictDataitemDlmsService dictDataitemDlmsService;

	/**
	 * 跳转到dlms规约数据项列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/dict/dictDataitemDlmsList");
    }

	/**
	 * 跳转到dlms规约数据项新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dictDataitemDlms")
	public ModelAndView dictDataitemDlms(DictDataitemDlms dictDataitemDlms,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dictDataitemDlms.getId())){
			try {
                dictDataitemDlms=dictDataitemDlmsService.getEntity(dictDataitemDlms.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dictDataitemDlms", dictDataitemDlms);
		}
		return new ModelAndView("/dict/dictDataitemDlms");
	}


	/**
	 * dlms规约数据项查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=dictDataitemDlmsService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除dlms规约数据项信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DictDataitemDlms dictDataitemDlms,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dictDataitemDlmsService.deleteById(dictDataitemDlms.getId())>0){
                j.setMsg("删除成功");
            }else{
                j.setSuccess(false);
                j.setMsg("删除失败");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
    
    /**
     * 保存dlms规约数据项信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DictDataitemDlms dictDataitemDlms,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DictDataitemDlms t=new  DictDataitemDlms();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dictDataitemDlms.getId())){
        	t=dictDataitemDlmsService.getEntity(dictDataitemDlms.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dictDataitemDlms, t);
				dictDataitemDlmsService.update(t);
				j.setMsg("修改成功");
				
			}else{
	            dictDataitemDlmsService.save(dictDataitemDlms);
	            j.setMsg("创建成功");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
	
	
	
}