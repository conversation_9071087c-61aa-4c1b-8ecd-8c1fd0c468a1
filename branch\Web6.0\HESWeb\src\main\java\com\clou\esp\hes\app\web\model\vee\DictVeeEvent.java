/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictVeeEvent{ } 
 * 
 * 摘    要： dictVeeEvent
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-12-21 07:44:42
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.vee;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DictVeeEvent  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DictVeeEvent() {
	}

	/**name*/
	private java.lang.String name;
	/**userDefine*/
	private java.math.BigDecimal userDefine;
	/**method*/
	private java.lang.String method;
	/**dataitemId*/
	private java.lang.String dataitemId;
	/**descr*/
	private java.lang.String descr;
	/**sortId*/
	private java.math.BigDecimal sortId;

	/**
	 * name
	 * @return the value of DICT_VEE_EVENT.NAME
	 * @mbggenerated 2018-12-21 07:44:42
	 */
	public java.lang.String getName() {
		return name;
	}

	/**
	 * name
	 * @param name the value for DICT_VEE_EVENT.NAME
	 * @mbggenerated 2018-12-21 07:44:42
	 */
    	public void setName(java.lang.String name) {
		this.name = name;
	}
	/**
	 * userDefine
	 * @return the value of DICT_VEE_EVENT.USER_DEFINE
	 * @mbggenerated 2018-12-21 07:44:42
	 */
	public java.math.BigDecimal getUserDefine() {
		return userDefine;
	}

	/**
	 * userDefine
	 * @param userDefine the value for DICT_VEE_EVENT.USER_DEFINE
	 * @mbggenerated 2018-12-21 07:44:42
	 */
    	public void setUserDefine(java.math.BigDecimal userDefine) {
		this.userDefine = userDefine;
	}
	/**
	 * method
	 * @return the value of DICT_VEE_EVENT.METHOD
	 * @mbggenerated 2018-12-21 07:44:42
	 */
	public java.lang.String getMethod() {
		return method;
	}

	/**
	 * method
	 * @param method the value for DICT_VEE_EVENT.METHOD
	 * @mbggenerated 2018-12-21 07:44:42
	 */
    	public void setMethod(java.lang.String method) {
		this.method = method;
	}
	/**
	 * dataitemId
	 * @return the value of DICT_VEE_EVENT.DATAITEM_ID
	 * @mbggenerated 2018-12-21 07:44:42
	 */
	public java.lang.String getDataitemId() {
		return dataitemId;
	}

	/**
	 * dataitemId
	 * @param dataitemId the value for DICT_VEE_EVENT.DATAITEM_ID
	 * @mbggenerated 2018-12-21 07:44:42
	 */
    	public void setDataitemId(java.lang.String dataitemId) {
		this.dataitemId = dataitemId;
	}
	/**
	 * descr
	 * @return the value of DICT_VEE_EVENT.DESCR
	 * @mbggenerated 2018-12-21 07:44:42
	 */
	public java.lang.String getDescr() {
		return descr;
	}

	/**
	 * descr
	 * @param descr the value for DICT_VEE_EVENT.DESCR
	 * @mbggenerated 2018-12-21 07:44:42
	 */
    	public void setDescr(java.lang.String descr) {
		this.descr = descr;
	}
	/**
	 * sortId
	 * @return the value of DICT_VEE_EVENT.SORT_ID
	 * @mbggenerated 2018-12-21 07:44:42
	 */
	public java.math.BigDecimal getSortId() {
		return sortId;
	}

	/**
	 * sortId
	 * @param sortId the value for DICT_VEE_EVENT.SORT_ID
	 * @mbggenerated 2018-12-21 07:44:42
	 */
    	public void setSortId(java.math.BigDecimal sortId) {
		this.sortId = sortId;
	}

	public DictVeeEvent(java.lang.String name 
	,java.math.BigDecimal userDefine 
	,java.lang.String method 
	,java.lang.String dataitemId 
	,java.lang.String descr 
	,java.math.BigDecimal sortId ) {
		super();
		this.name = name;
		this.userDefine = userDefine;
		this.method = method;
		this.dataitemId = dataitemId;
		this.descr = descr;
		this.sortId = sortId;
	}

}