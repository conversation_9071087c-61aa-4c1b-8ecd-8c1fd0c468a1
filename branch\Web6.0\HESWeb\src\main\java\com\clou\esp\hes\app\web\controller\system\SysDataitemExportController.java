/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysDataitemExport{ } 
 * 
 * 摘    要： sysDataitemExport
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-04-04 06:11:28
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.system;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.tag.vo.JqTreeUtil;
import com.clou.esp.hes.app.web.core.util.Globals;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;
import com.clou.esp.hes.app.web.model.dict.DictDataitemGroup;
import com.clou.esp.hes.app.web.model.system.SysDataitemExport;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.data.DataDiExportProgressService;
import com.clou.esp.hes.app.web.service.data.DataIntegrityService;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemGroupMapService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemGroupService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemService;
import com.clou.esp.hes.app.web.service.system.SysDataitemExportService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;

/**
 * <AUTHOR>
 * @时间：2018-04-04 06:11:28
 * @描述：sysDataitemExport类
 */
@Controller
@RequestMapping("/sysDataitemExportController")
public class SysDataitemExportController extends BaseController{

 	@Resource
    private SysDataitemExportService sysDataitemExportService;
 	@Resource
    private DictDataitemGroupService dictDataitemGroupService;
 	@Resource
    private DictDataitemGroupMapService dictDataitemGroupMapService;
 	@Resource
    private DictDataitemService dictDataitemService;
 	@Resource
    private DataDiExportProgressService dataDiExportProgressService;
 	
 	@Resource
 	private DataIntegrityService dataIntegrityService;
 	@Resource
    private DataUserLogService dataUserLogService;

	/**
	 * 跳转到sysDataitemExport列表页面
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/system/meterDataAndEventExport/meterDataAndEventExport");
    }

	/**
	 * 跳转到sysDataitemExport新增界面
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "sysDataitemExport")
	public ModelAndView sysDataitemExport(SysDataitemExport sysDataitemExport,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(sysDataitemExport.getId())){
			try {
                sysDataitemExport = sysDataitemExportService.getEntity(sysDataitemExport.getId());
            }
            catch (Exception e) {
                e.printStackTrace();
            }
			model.addAttribute("sysDataitemExport", sysDataitemExport);
		}
		return new ModelAndView("/system/sysDataitemExport");
	}


	/**
	 * sysDataitemExport查询分页, 树结构
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo, HttpServletRequest request) {
		/*//首页SQL测试优化
		dataIntegrityService.statisticsIntegrity();*/
		
		TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j = null;
        DictDataitemGroup group = new DictDataitemGroup();
        //先写死 以后做参数配置 200,300 ==
        group.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
        DictDataitem dataitem = new DictDataitem();
        dataitem.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
        try {
        	List<DictDataitemGroup> grouplist = dictDataitemGroupService.getMeterDataEventExportList(group);
        	List<Map<String, Object>> list = new ArrayList<>();
        	//父级server
    		for (int i = 0; i < grouplist.size(); i++) {
    			Map<String, Object> mapParent = new HashMap<String, Object>();
    			mapParent.put("id", grouplist.get(i).getId());
    			mapParent.put("dataChannel", grouplist.get(i).getName());
    			mapParent.put("parent", "");
    			mapParent.put("isLeaf", false);
    			mapParent.put("expanded", true);
    			//子级service
    			List<Map<String, Object>> listk = new ArrayList<>();
    			dataitem.setGroupId(grouplist.get(i).getId());
    			List<DictDataitem> dataitemlist = dictDataitemService.getMeterDataEventExportList(dataitem);
    			if(dataitemlist.size() > 0){
    				for (int jk = 0; jk < dataitemlist.size(); jk++) {
    					Map<String, Object> mapChild = new HashMap<String, Object>();
    					mapChild.put("id", dataitemlist.get(jk).getId());
    					mapChild.put("dataChannel", dataitemlist.get(jk).getName());
    					mapChild.put("exportProgress", dataitemlist.get(jk).getTv());
    					mapChild.put("exportTime", dataitemlist.get(jk).getExportTv());
    					mapChild.put("exportResult", dataitemlist.get(jk).getExportResult());
    					mapChild.put("parent", dataitemlist.get(jk).getGroupId());
    					mapChild.put("isLeaf", true);
    					mapChild.put("expanded", false);
    					listk.add(mapChild);
    				}
    			}
    			mapParent.put("list", listk);
    			list.add(mapParent);
    		}
    		PageInfo<Map<String, Object>> pageInfo = new PageInfo<Map<String, Object>>();
    		pageInfo.setList(list);
             j= JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
    		JqTreeUtil jtu = new JqTreeUtil();
    		jtu.setFields(jqGridSearchTo.getField());
    		jtu.setIdFieldName("id");
    		jtu.setParentFieldName("parent");
    		jtu.setSubsetFieldName("list");
    		jtu.setExpandedFieldName("expanded");
    		j.setRows(jtu.getTreeGridData(list));
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除sysDataitemExport信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(SysDataitemExport sysDataitemExport,HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        try {
            SysUser su = TokenManager.getToken();
            if(sysDataitemExportService.deleteById(sysDataitemExport.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.reqexc"));
        }
        return j;
    }
    
    /**
     * 保存sysDataitemExport信息
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })SysDataitemExport sysDataitemExport,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        SysDataitemExport t = new SysDataitemExport();
        try {
        	SysUser su = TokenManager.getToken();
	        if(StringUtil.isNotEmpty(sysDataitemExport.getId())){
	        	t = sysDataitemExportService.getEntity(sysDataitemExport.getId());
				MyBeanUtils.copyBeanNotNull2Bean(sysDataitemExport, t);
				sysDataitemExportService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            sysDataitemExportService.save(sysDataitemExport);
	            j.setMsg("Added successfully");
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.reqexc"));
        }
        return j;
    }
    
    /**
     * 保存sysDataitemExport信息
     * @param id
     * @return
     */
    @Transactional
    @RequestMapping(value = "saveMeterDataEventExport")
    @ResponseBody
    public AjaxJson saveMeterDataEventExport(String channelListStr, HttpServletRequest request){
        AjaxJson j = new AjaxJson();
        try {
        	SysUser su = TokenManager.getToken();
        	//获取界面的数据
    		JSONArray listJsons = null;
	        if(StringUtil.isNotEmpty(channelListStr)){
	        	listJsons = JSONArray.fromObject(channelListStr);
	        }
            Map<String , String> map = new HashMap();
	        if(listJsons != null && listJsons.size() > 0){
	        	//清空表数据
	        	sysDataitemExportService.deleteAllData();
	        	for (int i = 0; i < listJsons.size(); i++) {
	        		
	        		JSONObject jsonObj = listJsons.getJSONObject(i);
	        		
	        		if(map.get(jsonObj.getString("id")) != null){
	        			continue;
	        		}else{
	        			map.put(jsonObj.getString("id"), "");
	        		}
	        		
        			SysDataitemExport t = new SysDataitemExport();
        			t.setDataitemId(jsonObj.getString("id"));
        			t.setGroupId(jsonObj.getString("groupId"));
        			sysDataitemExportService.save(t);
				}
	        	j.setSuccess(true);
	            j.setMsg(MutiLangUtil.doMutiLang("system.saveSucc"));
	        }else{
	        	//清空表数据
	        	sysDataitemExportService.deleteAllData();
	        	j.setSuccess(true);
	            j.setMsg(MutiLangUtil.doMutiLang("system.saveSucc"));
	        }
	        //添加操作日志
            dataUserLogService.insertDataUserLog(su.getId(), 
            		"Meter Data & Event Export", "Edit Data Channel", "Modify data channels");
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.reqexc"));
        }
        return j;
    }
	
    /**
     * 获取数据库中保存的说有DATAITEM信息
     */
    @RequestMapping(value = "getAllList")
    @ResponseBody
    public AjaxJson getAllList(HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        try {
        	List<SysDataitemExport> list = sysDataitemExportService.getAllList();
        	if(list.size() > 0){
        		j.setObj(list);
        	}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.reqexc"));
        }
        return j;
    }
}