package com.clou.esp.hes.app.web.service.impl.interfaces;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.xml.datatype.XMLGregorianCalendar;
import javax.xml.ws.Holder;

import org.apache.log4j.Logger;
import org.springframework.util.StringUtils;

import com.clou.esp.hes.app.web.core.pushlet.PushletData;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.model.asset.OnDemandRead;
import com.clou.esp.hes.app.web.model.asset.OnDemandReadSchedulerJob;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;
import com.clou.esp.hes.app.web.service.dict.DictDataitemService;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.redis.JedisUtils;

import net.sf.json.JSONObject;
import ch.iec.tc57._2011.meterreadings.FaultMessage;
import ch.iec.tc57._2011.meterreadings.ReplyMeterReadingsPort;
import ch.iec.tc57._2011.meterreadings_.MeterReading;
import ch.iec.tc57._2011.meterreadings_.MeterReading.Readings;
import ch.iec.tc57._2011.meterreadings_.MeterReadings;
import ch.iec.tc57._2011.meterreadingsmessage.MeterReadingsPayloadType;
import ch.iec.tc57._2011.schema.message.HeaderType;
import ch.iec.tc57._2011.schema.message.ReplyType;
import ch.iec.tc57._2011.schema.message.UserType;

public class ReplyMeterReadings_Port implements ReplyMeterReadingsPort{

	  private static Logger logger = Logger.getLogger(ReplyMeterReadings_Port.class);
	@Resource
	private DictDataitemService dictDataitemService;

	
	@Override
	public void deletedMeterReadings(Holder<HeaderType> header,
			Holder<ReplyType> reply, Holder<MeterReadingsPayloadType> payload)
			throws FaultMessage {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void canceledMeterReadings(Holder<HeaderType> header,
			Holder<ReplyType> reply, Holder<MeterReadingsPayloadType> payload)
			throws FaultMessage {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void changedMeterReadings(Holder<HeaderType> header,
			Holder<ReplyType> reply, Holder<MeterReadingsPayloadType> payload)
			throws FaultMessage {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void closedMeterReadings(Holder<HeaderType> header,
			Holder<ReplyType> reply, Holder<MeterReadingsPayloadType> payload)
			throws FaultMessage {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void createdMeterReadings(Holder<HeaderType> header,
			Holder<ReplyType> reply, Holder<MeterReadingsPayloadType> reqt)
			throws FaultMessage {
		JSONObject data=new JSONObject();
		AjaxJson j=new AjaxJson();
		 if(header.value==null||reqt.value==null||header.value.getUser()==null||reqt.value.getMeterReadings()==null){
			 logger.warn("ucisuichao:"+"is MessageNull");
			 return;
		 }
		 UserType user= header.value.getUser();
		 String msgId=header.value.getMessageID();
		 
		 MeterReadings meterReadings=reqt.value.getMeterReadings();
		 List<MeterReading> list= meterReadings.getMeterReading();
		 if(list==null||list.size()<=0){
			logger.warn("ucisuichao:"+"is list MessageNull");
			return;
		 }
		 
		 logger.warn("createdMeterReadings msgId:"+msgId);
		 
		 //等待页面显示完成处理开始
		 boolean isView=(boolean) JedisUtils.getObject("setReadsIsView"+msgId);
		 while(!isView){
			 try {
				 Thread.sleep(1000);
			 } catch (InterruptedException e) {
				 e.printStackTrace();
			 }  
			isView=(boolean) JedisUtils.getObject("setReadsIsView"+msgId);
		 }
		 j.put("msgId", msgId);
		 if(meterReadings.getReadingType()!=null&&meterReadings.getReadingType().size()>0&&StringUtil.isNotEmpty(meterReadings.getReadingType().get(0).getNames().getName())){
			 String profileId=meterReadings.getReadingType().get(0).getNames().getName();
			 List<OnDemandReadSchedulerJob> sjobList=new ArrayList<OnDemandReadSchedulerJob>();
			 Map<String,Object> map= (Map<String, Object>) JedisUtils.getObject(msgId);
			 for(MeterReading mr:list){
				 MeterReading.Meter meter= mr.getMeter();
				 List<Readings> readings= mr.getReadings();
				 if(readings==null||readings.size()<=0){
					 continue;
				 }
				 for(int i=0;i<readings.size();i++){
					 String channelId=readings.get(i).getReadingType().getRef();
					 XMLGregorianCalendar timeStamp=readings.get(i).getTimeStamp();
					 Date startTime=DateUtils.xmlDate2Date(readings.get(i).getTimePeriod().getStart());
					 Date endTime=DateUtils.xmlDate2Date(readings.get(i).getTimePeriod().getEnd());
					 String mId=profileId + meter.getMRID()+DateUtils.formatDate(startTime, "MM/dd/yyyy HH:mm:ss")+DateUtils.formatDate(endTime, "MM/dd/yyyy HH:mm:ss");
					 OnDemandReadSchedulerJob mj= (OnDemandReadSchedulerJob) map.get(mId);
					 OnDemandReadSchedulerJob job=new OnDemandReadSchedulerJob();
					 if(mj!=null){
						 try {
							 MyBeanUtils.copyBeanNotNull2Bean(mj, job);
						 } catch (Exception e) {
							 e.printStackTrace();
						 }
					 }
					 job.setId(mId+channelId+DateUtils.xmlDate2Date(timeStamp));
					 job.setStartTime(DateUtils.formatDate(startTime, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
					 job.setEndTime(DateUtils.formatDate(endTime, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
					 if(StringUtil.isNotEmpty(channelId)){
						 DictDataitem dd=dictDataitemService.getEntity(channelId);
						 job.setDataChannel(dd.getName());
					 }
					 if(reply.value!=null&&!reply.value.getResult().toUpperCase().equals("OK")){
						 String code=reply.value.getError().get(0).getCode();
						 if(StringUtil.isNotEmpty(code)&&!code.equals("0.0")){
							 String errorMsg=reply.value.getResult();
							 job.setValue(errorMsg);
							 job.setStatus("3");
						 }else{
							 job.setValue("unknown mistake");
							 job.setStatus("3");
						 }
					 }else{
						 job.setReponseTime(DateUtils.xmlDate2Date(timeStamp));
						 job.setReason(readings.get(i).getReason());
						 job.setStatus("2");
						 data.put("status", "2");
						 job.setValue(readings.get(i).getValue().toString());
					 }
					 sjobList.add(job);
					 map.put(job.getId(), job);
				 }
			 }
			 List<JSONObject> jsonList=new ArrayList<JSONObject>();
			 for(OnDemandReadSchedulerJob odrJob:sjobList){
				 JSONObject obj=new JSONObject();
				 obj.put("id", odrJob.getId());
				 obj.put("sn", odrJob.getSn());
				 obj.put("profile", odrJob.getProfile());
				 obj.put("profileName", odrJob.getProfileName());
				 obj.put("communication", odrJob.getCommunication());
				 obj.put("requestTime", DateUtils.formatDate(odrJob.getRequestTime(),ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
				 obj.put("dataChannel", odrJob.getDataChannel());
				 obj.put("value", odrJob.getValue());
				 obj.put("status", odrJob.getStatus());
				 obj.put("reason", odrJob.getReason());
				 obj.put("reponseTime", DateUtils.formatDate(odrJob.getReponseTime(), ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
				 jsonList.add(obj);
			 }
			 JedisUtils.setObject(msgId, map, 0);
			 j.setObj(jsonList);
			 try {
				 PushletData.pushlet("startReads", j, user.getUserID());
			 } catch (UnsupportedEncodingException e) {
				 e.printStackTrace();
			 }
		 }else{
			 MeterReading mr=list.get(0);
			 MeterReading.Meter meter= mr.getMeter();
			 List<Readings> readings= mr.getReadings();
			 if(readings==null||readings.size()<=0){
				 return;
			 }
			 //等待页面显示完成处理结束
			 List<JSONObject> channList=new ArrayList<JSONObject>();
			 JSONObject readChildrenItem = new JSONObject();
			 StringBuffer sb = new StringBuffer();
			 for(int i=0;i<readings.size();i++){
				 String channelId=readings.get(i).getReadingType().getRef();
				 if(!StringUtils.isEmpty(channelId) && channelId.indexOf("-") > -1){
					 String[] channelIds = channelId.split("-");
					 if(channelIds.length == 2){
						 DictDataitem dd=dictDataitemService.getEntity(channelIds[1]);
						 JSONObject read=new JSONObject();
						 String value=readings.get(i).getValue();
						 XMLGregorianCalendar timeStamp=readings.get(i).getTimeStamp();
//						 if(dd!=null&&dd.getShowUnit().doubleValue()!=1){
//							 try {
//								 BigDecimal bval=new BigDecimal(value);
//								 read.put("value", bval.multiply(dd.getShowUnit()).doubleValue());
//							 }catch (Exception e){
//								 read.put("value", value);
//							 }
//							 
//						 }else{
							 read.put("value", value);
					//	 }
						 read.put("channelId", channelIds[0]);
						 read.put("timeStamp", DateUtils.xmlDate2Date(timeStamp));
						 Map<String,Object> map= (Map<String, Object>) JedisUtils.getObject(msgId);
						 if(map!=null&&meter!=null){
							 OnDemandRead odr=(OnDemandRead) map.get(channelIds[0]+meter.getMRID());
							 if(odr!=null){
								 if(reply.value!=null&&!reply.value.getResult().toUpperCase().equals("OK")){
									 String code=reply.value.getError().get(0).getCode();
									 if(StringUtil.isNotEmpty(code)&&!code.equals("0.0")){
										 String errorMsg=reply.value.getResult();
										 odr.setValue(errorMsg);
										 read.put("reason", errorMsg);
										 odr.setStatus("3");
										 data.put("status", "3");
									 }else{
										 odr.setValue("unknown mistake");
										 read.put("reason", "unknown mistake");
										 odr.setStatus("3");
										 data.put("status", "3");
									 }
								 }else{
									 odr.setStatus("2");
									 data.put("status", "22");
									 odr.setReponseTime(DateUtils.xmlDate2Date(timeStamp));
									 odr.setValue(StringUtils.isEmpty(readings.get(i).getValue().toString()) ? "invalid data" : read.getString("value"));
							
								 }
							
								 if(!StringUtils.isEmpty(odr.getValue()) && "2".equals(odr.getStatus())){
									 if(dd == null){
										 sb.append(odr.getValue()+";<br>");	 
									 }else{
										 sb.append(dd.getName()+" : "+ odr.getValue()+";<br>");	
									 }
									// odr.setValue376(sb.toString().replaceAll("<br>", ""));
									 odr.setValue(sb.toString().replaceAll("<br>", "\n"));
									 data.put("childrenItem" , sb.toString());
								 } 		 
								 channList.add(read);
								 map.put(odr.getId(), odr);
								 JedisUtils.setObject(msgId, map, 0);
								 data.put("sn", meter.getMRID());
								 data.put("channelList", channList);
								 j.setObj(data);
							 }
						 }
					 }
					 
					//376 分割线 end
				 }else{
					 DictDataitem dd=dictDataitemService.getEntity(channelId);
					 JSONObject read=new JSONObject();
					 String value=readings.get(i).getValue();
					 XMLGregorianCalendar timeStamp=readings.get(i).getTimeStamp();
//					 if(dd!=null&&dd.getShowUnit().doubleValue()!=1){
//						 try {
//							 BigDecimal bval=new BigDecimal(value);
//							 read.put("value", bval.multiply(dd.getShowUnit()).doubleValue());
//						 }catch (Exception e){
//							 read.put("value", value);
//						 }
//						 
//					 }else{
						 read.put("value", value);
					 //}
					 read.put("channelId", channelId);
					 read.put("timeStamp", DateUtils.xmlDate2Date(timeStamp));
					 Map<String,Object> map= (Map<String, Object>) JedisUtils.getObject(msgId);
					 if(map!=null&&meter!=null){
						 OnDemandRead odr=(OnDemandRead) map.get(channelId+meter.getMRID());
						 if(odr!=null){
							 if(reply.value!=null&&!reply.value.getResult().toUpperCase().equals("OK")){
								 String code=reply.value.getError().get(0).getCode();
								 if(StringUtil.isNotEmpty(code)&&!code.equals("0.0")){
									 String errorMsg=reply.value.getResult();
									 odr.setValue(errorMsg);
									 read.put("reason", errorMsg);
									 odr.setStatus("3");
									 data.put("status", "3");
								 }else{
									 odr.setValue("unknown mistake");
									 read.put("reason", "unknown mistake");
									 odr.setStatus("3");
									 data.put("status", "3");
								 }
							 }else{
								 odr.setStatus("2");
								 data.put("status", "2");
								 odr.setReponseTime(DateUtils.xmlDate2Date(timeStamp));
								 odr.setValue(StringUtils.isEmpty(readings.get(i).getValue().toString()) ? "invalid data" : read.getString("value"));
							 }
						
							 if(!StringUtils.isEmpty(odr.getValue()) && "2".equals(odr.getStatus())){
								 if(dd == null){
									 sb.append(odr.getValue()+";<br>");	 
								 }else{
									 sb.append(dd.getName()+" : "+ odr.getValue()+";<br>");	
								 }
								 data.put("childrenItem" , sb.toString());
								 odr.setValue(odr.getValue());
							 } 
							 
							 
							 channList.add(read);
							 map.put(odr.getId(), odr);
							 JedisUtils.setObject(msgId, map, 0);
							 data.put("sn", meter.getMRID());
							 data.put("channelList", channList);
							 j.setObj(data);
						 }
					 }
				 }
				
			 }
			 
			 try {
				 PushletData.pushlet("startReads", j, user.getUserID());
			 } catch (UnsupportedEncodingException e) {
				 e.printStackTrace();
			 }
		 }
		
	}

}
