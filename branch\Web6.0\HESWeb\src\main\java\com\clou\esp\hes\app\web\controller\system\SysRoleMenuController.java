/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysRoleMenu{ } 
 * 
 * 摘    要： 角色菜单表
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-10-27 08:42:10
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.system;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;

import com.clou.esp.hes.app.web.model.system.SysRoleMenu;
import com.clou.esp.hes.app.web.service.system.SysRoleMenuService;

/**
 * <AUTHOR>
 * @时间：2017-10-27 08:42:10
 * @描述：角色菜单表类
 */
@Controller
@RequestMapping("/sysRoleMenuController")
public class SysRoleMenuController extends BaseController{

 	@Resource
    private SysRoleMenuService sysRoleMenuService;

	/**
	 * 跳转到角色菜单表列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/system/sysRoleMenuList");
    }

	/**
	 * 跳转到角色菜单表新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "sysRoleMenu")
	public ModelAndView sysRoleMenu(SysRoleMenu sysRoleMenu,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(sysRoleMenu.getId())){
			try {
                sysRoleMenu=sysRoleMenuService.getEntity(sysRoleMenu.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("sysRoleMenu", sysRoleMenu);
		}
		return new ModelAndView("/system/sysRoleMenu");
	}


	/**
	 * 角色菜单表查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=sysRoleMenuService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除角色菜单表信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(SysRoleMenu sysRoleMenu,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(sysRoleMenuService.deleteById(sysRoleMenu.getId())>0){
                j.setMsg("删除成功");
            }else{
                j.setSuccess(false);
                j.setMsg("删除失败");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
    
    /**
     * 保存角色菜单表信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })SysRoleMenu sysRoleMenu,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        SysRoleMenu t=new  SysRoleMenu();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(sysRoleMenu.getId())){
        	t=sysRoleMenuService.getEntity(sysRoleMenu.getId());
			MyBeanUtils.copyBeanNotNull2Bean(sysRoleMenu, t);
				sysRoleMenuService.update(t);
				j.setMsg("修改成功");
				
			}else{
	            sysRoleMenuService.save(sysRoleMenu);
	            j.setMsg("创建成功");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
	
	
	
}