package clouesp.hes.core.uci.soap.custom.webservice.abstractt;

import javax.xml.datatype.XMLGregorianCalendar;

import com.clou.esp.hes.app.web.enums.system.TaskState;
import com.power7000g.core.util.json.AjaxJson;

import ch.iec.tc57._2011.schema.message.HeaderType;


/**
 * @ClassName: SoapRecvAbstract
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年7月18日 上午11:41:04
 *
 */
public abstract class SoapRecvAbstract implements SoapInterface {
	protected String verb;
	protected String noun;
	protected XMLGregorianCalendar timestamp;
	protected String source;
	protected boolean asyncReplyFlag;
	protected String replyAddress;
	protected boolean ackRequired;
	protected String userId;
	protected String organization;
	protected String messageId;
	protected String correlationId;
	
	protected String dataItemId;
	protected String sn;
	protected String id;
	
	protected Object object;
	protected String result;
	protected String code;
	protected AjaxJson ajaxJson;
	
	@Override
	public void recv(Object object) {
		// TODO Auto-generated method stub
		this.object = object;
		parseHeader();
		parseReply();
		parsePayload();
		pushlet(ajaxJson);
	}
	
	@Override
	public void send() {
		// TODO Auto-generated method stub
	}

	@Override
	public HeaderType createHeader() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Object createPayload() {
		// TODO Auto-generated method stub
		return null;
	}
	
	@Override
	public void saveToRedis(String id,String dataItemId,TaskState state,Object value){
		// TODO Auto-generated method stub
	}

}
