/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysOrg{ } 
 * 
 * 摘    要： 组织机构表
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-10-27 09:28:59
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.system;

import java.util.List;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class SysOrg  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public SysOrg() {
	}

	/**utilityId*/
	private java.lang.String utilityId;
	/**orgDataCode*/
	private java.lang.String orgDataCode;
	/**部门名称*/
	private java.lang.String name;
	/**部门描述*/
	private java.lang.String description;
	/**上级部门编号*/
	private java.lang.String parentOrgTid;
	/**机构代码（同租户下唯一）*/
	private java.lang.String orgCode;
	/**机构类型；1 国网公司、2 省公司、3 地市公司 、4 区县公司、5 分公司、6 供电所。*/
	private java.lang.String orgType;
	/**手机号*/
	private java.lang.String mobile;
	/**传真*/
	private java.lang.String fax;
	/**地址*/
	private java.lang.String address;
	/**负责人*/
	private java.lang.String headName;
	/**手机号*/
	private java.lang.String mobilePhone;
	/**排序*/
	private java.lang.Integer orderNo;
	
	private List<SysOrg> sysOrgList;
	
	private boolean checked;
	
	private int level = 0;
	
	private java.lang.Integer userCount;
	private java.lang.Integer meterCount;
	
	/**联系人*/
	private String contactMan;
	
	/**
	 * 是否展开
	 */
	private boolean expanded;

	/**
	 * utilityId
	 * @return the value of SYS_ORG.VENDOR_ID
	 * @mbggenerated 2017-10-27 09:28:59
	 */
	public java.lang.String getUtilityId() {
		return utilityId;
	}

	/**
	 * utilityId
	 * @param utilityId the value for SYS_ORG.VENDOR_ID
	 * @mbggenerated 2017-10-27 09:28:59
	 */
    	public void setUtilityId(java.lang.String utilityId) {
		this.utilityId = utilityId;
	}
	/**
	 * orgDataCode
	 * @return the value of SYS_ORG.ORG_DATA_CODE
	 * @mbggenerated 2017-10-27 09:28:59
	 */
	public java.lang.String getOrgDataCode() {
		return orgDataCode;
	}

	/**
	 * orgDataCode
	 * @param orgDataCode the value for SYS_ORG.ORG_DATA_CODE
	 * @mbggenerated 2017-10-27 09:28:59
	 */
    	public void setOrgDataCode(java.lang.String orgDataCode) {
		this.orgDataCode = orgDataCode;
	}
	/**
	 * 部门名称
	 * @return the value of SYS_ORG.NAME
	 * @mbggenerated 2017-10-27 09:28:59
	 */
	public java.lang.String getName() {
		return name;
	}

	/**
	 * 部门名称
	 * @param name the value for SYS_ORG.NAME
	 * @mbggenerated 2017-10-27 09:28:59
	 */
    	public void setName(java.lang.String name) {
		this.name = name;
	}
	/**
	 * 部门描述
	 * @return the value of SYS_ORG.DESCRIPTION
	 * @mbggenerated 2017-10-27 09:28:59
	 */
	public java.lang.String getDescription() {
		return description;
	}

	/**
	 * 部门描述
	 * @param description the value for SYS_ORG.DESCRIPTION
	 * @mbggenerated 2017-10-27 09:28:59
	 */
    	public void setDescription(java.lang.String description) {
		this.description = description;
	}
	/**
	 * 上级部门编号
	 * @return the value of SYS_ORG.PARENT_ORG_TID
	 * @mbggenerated 2017-10-27 09:28:59
	 */
	public java.lang.String getParentOrgTid() {
		return parentOrgTid;
	}

	/**
	 * 上级部门编号
	 * @param parentOrgTid the value for SYS_ORG.PARENT_ORG_TID
	 * @mbggenerated 2017-10-27 09:28:59
	 */
    	public void setParentOrgTid(java.lang.String parentOrgTid) {
		this.parentOrgTid = parentOrgTid;
	}
	/**
	 * 机构代码（同租户下唯一）
	 * @return the value of SYS_ORG.ORG_CODE
	 * @mbggenerated 2017-10-27 09:28:59
	 */
	public java.lang.String getOrgCode() {
		return orgCode;
	}

	/**
	 * 机构代码（同租户下唯一）
	 * @param orgCode the value for SYS_ORG.ORG_CODE
	 * @mbggenerated 2017-10-27 09:28:59
	 */
    	public void setOrgCode(java.lang.String orgCode) {
		this.orgCode = orgCode;
	}
	/**
	 * 机构类型；01 国网公司、02 省公司、03 地市公司 、04 区县公司、05 分公司、06 供电所。
	 * @return the value of SYS_ORG.ORG_TYPE
	 * @mbggenerated 2017-10-27 09:28:59
	 */
	public java.lang.String getOrgType() {
		return orgType;
	}

	/**
	 * 机构类型；01 国网公司、02 省公司、03 地市公司 、04 区县公司、05 分公司、06 供电所。
	 * @param orgType the value for SYS_ORG.ORG_TYPE
	 * @mbggenerated 2017-10-27 09:28:59
	 */
    	public void setOrgType(java.lang.String orgType) {
		this.orgType = orgType;
	}
	/**
	 * 手机号
	 * @return the value of SYS_ORG.MOBILE
	 * @mbggenerated 2017-10-27 09:28:59
	 */
	public java.lang.String getMobile() {
		return mobile;
	}

	/**
	 * 手机号
	 * @param mobile the value for SYS_ORG.MOBILE
	 * @mbggenerated 2017-10-27 09:28:59
	 */
    	public void setMobile(java.lang.String mobile) {
		this.mobile = mobile;
	}
	/**
	 * 传真
	 * @return the value of SYS_ORG.FAX
	 * @mbggenerated 2017-10-27 09:28:59
	 */
	public java.lang.String getFax() {
		return fax;
	}

	/**
	 * 传真
	 * @param fax the value for SYS_ORG.FAX
	 * @mbggenerated 2017-10-27 09:28:59
	 */
    	public void setFax(java.lang.String fax) {
		this.fax = fax;
	}
	/**
	 * 地址
	 * @return the value of SYS_ORG.ADDRESS
	 * @mbggenerated 2017-10-27 09:28:59
	 */
	public java.lang.String getAddress() {
		return address;
	}

	/**
	 * 地址
	 * @param address the value for SYS_ORG.ADDRESS
	 * @mbggenerated 2017-10-27 09:28:59
	 */
    	public void setAddress(java.lang.String address) {
		this.address = address;
	}
	/**
	 * 负责人
	 * @return the value of SYS_ORG.HEAD_NAME
	 * @mbggenerated 2017-10-27 09:28:59
	 */
	public java.lang.String getHeadName() {
		return headName;
	}

	/**
	 * 负责人
	 * @param headName the value for SYS_ORG.HEAD_NAME
	 * @mbggenerated 2017-10-27 09:28:59
	 */
    	public void setHeadName(java.lang.String headName) {
		this.headName = headName;
	}
	/**
	 * 手机号
	 * @return the value of SYS_ORG.MOBILE_PHONE
	 * @mbggenerated 2017-10-27 09:28:59
	 */
	public java.lang.String getMobilePhone() {
		return mobilePhone;
	}

	/**
	 * 手机号
	 * @param mobilePhone the value for SYS_ORG.MOBILE_PHONE
	 * @mbggenerated 2017-10-27 09:28:59
	 */
    	public void setMobilePhone(java.lang.String mobilePhone) {
		this.mobilePhone = mobilePhone;
	}
	/**
	 * 排序
	 * @return the value of SYS_ORG.ORDER_NO
	 * @mbggenerated 2017-10-27 09:28:59
	 */
	public java.lang.Integer getOrderNo() {
		return orderNo;
	}

	/**
	 * 排序
	 * @param orderNo the value for SYS_ORG.ORDER_NO
	 * @mbggenerated 2017-10-27 09:28:59
	 */
    	public void setOrderNo(java.lang.Integer orderNo) {
		this.orderNo = orderNo;
	}

	public SysOrg(java.lang.String utilityId 
	,java.lang.String orgDataCode 
	,java.lang.String name 
	,java.lang.String description 
	,java.lang.String parentOrgTid 
	,java.lang.String orgCode 
	,java.lang.String orgType 
	,java.lang.String mobile 
	,java.lang.String fax 
	,java.lang.String address 
	,java.lang.String headName 
	,java.lang.String mobilePhone 
	,java.lang.Integer orderNo ) {
		super();
		this.utilityId = utilityId;
		this.orgDataCode = orgDataCode;
		this.name = name;
		this.description = description;
		this.parentOrgTid = parentOrgTid;
		this.orgCode = orgCode;
		this.orgType = orgType;
		this.mobile = mobile;
		this.fax = fax;
		this.address = address;
		this.headName = headName;
		this.mobilePhone = mobilePhone;
		this.orderNo = orderNo;
	}

	public List<SysOrg> getSysOrgList() {
		return sysOrgList;
	}

	public void setSysOrgList(List<SysOrg> sysOrgList) {
		this.sysOrgList = sysOrgList;
	}

	public boolean getExpanded() {
		return expanded;
	}

	public void setExpanded(boolean expanded) {
		this.expanded = expanded;
	}

	public int getLevel() {
		return level;
	}

	public void setLevel(int level) {
		this.level = level;
	}

	public boolean getChecked() {
		return checked;
	}

	public void setChecked(boolean b) {
		this.checked = b;
	}

	public java.lang.Integer getUserCount() {
		return userCount;
	}

	public void setUserCount(java.lang.Integer userCount) {
		this.userCount = userCount;
	}

	public java.lang.Integer getMeterCount() {
		return meterCount;
	}

	public void setMeterCount(java.lang.Integer meterCount) {
		this.meterCount = meterCount;
	}

	public String getContactMan() {
		return contactMan;
	}

	public void setContactMan(String contactMan) {
		this.contactMan = contactMan;
	}

}