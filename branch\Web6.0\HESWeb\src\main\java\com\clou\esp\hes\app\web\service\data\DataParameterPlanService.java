/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataParameterPlan{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-04-27 09:13:12
 * 最后修改时间：
 * 
*********************************************************************************************************/

package com.clou.esp.hes.app.web.service.data;

import java.util.List;

import com.clou.esp.hes.app.web.service.common.CommonService;
import com.clou.esp.hes.app.web.service.system.SysServiceAttributeService;
import com.clou.esp.hes.app.web.model.data.DataParameterPlan;
import com.power7000g.core.util.json.AjaxJson;

public interface DataParameterPlanService extends CommonService<DataParameterPlan>{
	/**
	 * 保存电表组升级计划
	 * @Description 
	 * @param plan
	 * @return AjaxJson
	 * <AUTHOR> 
	 * @Time 2018年5月3日 下午2:45:56
	 */
	public AjaxJson saveMGU_Plan(DataParameterPlan plan, SysServiceAttributeService sysServiceAttributeService);
	/**
	 * 获取旧升级计划的未完成的job，生成新的plan和job
	 * @Description 
	 * @param plan
	 * @return AjaxJson
	 * <AUTHOR> 
	 * @Time 2018年5月15日 下午2:45:56
	 */
	public AjaxJson cretePlanAgain(DataParameterPlan plan, String oldPlanId, SysServiceAttributeService sysServiceAttributeService);
	
	public List<DataParameterPlan> exportPlanReportList(DataParameterPlan plan);
}