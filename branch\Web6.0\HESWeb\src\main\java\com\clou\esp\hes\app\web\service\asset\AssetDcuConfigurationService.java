/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetCommunicator{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-06 08:04:54
 * 最后修改时间：
 * 
 *********************************************************************************************************/

package com.clou.esp.hes.app.web.service.asset;

import com.clou.esp.hes.app.web.model.asset.DcuConfiguration;
import com.clou.esp.hes.app.web.service.common.CommonService;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

public interface AssetDcuConfigurationService extends
		CommonService<DcuConfiguration> {

	/**
	 * 
	 * @Title: findMetersForJqGrid
	 * @Description: 分页查询电表信息
	 * @param jqGridSearchTo
	 * @return
	 * @return JqGridResponseTo
	 * @throws
	 */
	JqGridResponseTo findMetersForJqGrid(JqGridSearchTo jqGridSearchTo);
	
	/**
	 * 
	 * @Title: findMetersForJqGrid 300
	 * @Description: 分页查询电表信息
	 * @param jqGridSearchTo
	 * @return
	 * @return JqGridResponseTo
	 * @throws
	 */
	JqGridResponseTo findMetersForJqGrid300(JqGridSearchTo jqGridSearchTo);
	
	/**
	 * 
	 * @Title: queryComType
	 * @Description: 查询通讯类型
	 * @param comType
	 * @return
	 * @return DcuConfiguration
	 * @throws
	 */
	DcuConfiguration queryComType(String comType);
}