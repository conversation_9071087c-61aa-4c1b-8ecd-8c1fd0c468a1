	/*环形水波百分比	*/
	.page {
  height: 120px;
  width: 120px;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  background:#88e6f1;
  border-radius: 100%;
  border: 2px solid rgba(255,255,255,0.4);
  overflow: hidden;
}

.percent {
  color: #fff;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  font-weight: lighter;
  z-index: 10;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.percent__inner {
  display: inline-block;
}

.percent__num {
  display: inline-block;
  font-size: 4rem;
  font-weight: bold;
 /* margin-left: 15px;*/
}
.percent__decimal{
	margin-left:-3px ;
	display: inline-block;
  font-size: 2rem;
  font-weight: bold;
}
.percent__point{
	display: inline-block;
  font-size: 1rem;
  margin-left:-5px ;
  font-weight: bold;
  /*margin:1px*/
}
.percent__sign {
	margin-left:-3px ;
  display: inline-block;
  font-size: 1.5rem;
  font-weight: bold;
}

.water {
  position: absolute;
  height: 100%;
  width: 100%;
  z-index: 5;
  bottom: 0;
  left: 0;
  background: linear-gradient(to bottom, #6ee0ef, #007cab);
  -webkit-transform: translate(0, 100%);
          transform: translate(0, 100%);
 transition:2s all ease-in-out;
}

.water__inner {
  position: absolute;
  width: 100%;
  height: 0%;
  top: 0;
  left: 0;
  overflow: hidden;
  transition:2s all ease-in-out;
}

.water__wave {
  width: 200%;
  position: absolute;
  bottom: 100%;
}

.water__wave_back {
  right: 0;
  fill: #45c3f3;
  -webkit-animation: wave-back 1.4s infinite linear;
          animation: wave-back 1.4s infinite linear;
}

.water__wave_front {
  left: 0;
  fill: #73e0ea;
  margin-bottom: -1px;
  -webkit-animation: wave-front .7s infinite linear;
          animation: wave-front .7s infinite linear;
}

.bubble {
  position: absolute;
  bottom: 80px;
  left: 50%;
  width: 20px;
  height: 20px;
  margin-left: -10px;
  background: #fff;
  border-radius: 50%;
  -webkit-transform: scale(0) translate(0, 0);
          transform: scale(0) translate(0, 0);
  opacity: 0;
}

.bubble_2 {
  margin-left: -20px;
}

@-webkit-keyframes wave-front {
  100% {
    -webkit-transform: translate(-50%, 0);
            transform: translate(-50%, 0);
  }
}

@keyframes wave-front {
  100% {
    -webkit-transform: translate(-50%, 0);
            transform: translate(-50%, 0);
  }
}

@-webkit-keyframes wave-back {
  100% {
    -webkit-transform: translate(50%, 0);
            transform: translate(50%, 0);
  }
}

@keyframes wave-back {
  100% {
    -webkit-transform: translate(50%, 0);
            transform: translate(50%, 0);
  }
}



@-webkit-keyframes bubble-1 {
  0% {
    opacity: 1;
    -webkit-transform: scale(0) translate(0, 0);
            transform: scale(0) translate(0, 0);
  }
  25% {
    -webkit-transform: scale(.25) translate(20px, -25px);
            transform: scale(.25) translate(20px, -25px);
  }
  50% {
    -webkit-transform: scale(.5) translate(40px, -50px);
            transform: scale(.5) translate(40px, -50px);
  }
  75% {
    -webkit-transform: scale(.75) translate(20px, -75px);
            transform: scale(.75) translate(20px, -75px);
  }
  100% {
    -webkit-transform: scale(1) translate(10px, -100px);
            transform: scale(1) translate(10px, -100px);
  }
}

@keyframes bubble-1 {
  0% {
    opacity: 1;
    -webkit-transform: scale(0) translate(0, 0);
            transform: scale(0) translate(0, 0);
  }
  25% {
    -webkit-transform: scale(.25) translate(20px, -25px);
            transform: scale(.25) translate(20px, -25px);
  }
  50% {
    -webkit-transform: scale(.5) translate(40px, -50px);
            transform: scale(.5) translate(40px, -50px);
  }
  75% {
    -webkit-transform: scale(.75) translate(20px, -75px);
            transform: scale(.75) translate(20px, -75px);
  }
  100% {
    -webkit-transform: scale(1) translate(10px, -100px);
            transform: scale(1) translate(10px, -100px);
  }
}


@-webkit-keyframes bubble-2 {
  0% {
    opacity: 1;
    -webkit-transform: scale(0) translate(-20px, -10px);
            transform: scale(0) translate(-20px, -10px);
  }
  25% {
    -webkit-transform: scale(.25) translate(-40px, -45px);
            transform: scale(.25) translate(-40px, -45px);
  }
  50% {
    -webkit-transform: scale(.5) translate(-60px, -70px);
            transform: scale(.5) translate(-60px, -70px);
  }
  75% {
    -webkit-transform: scale(.75) translate(-40px, -95px);
            transform: scale(.75) translate(-40px, -95px);
  }
  100% {
    -webkit-transform: scale(1) translate(-30px, -100px);
            transform: scale(1) translate(-30px, -100px);
  }
}

@keyframes bubble-2 {
  0% {
    opacity: 1;
    -webkit-transform: scale(0) translate(-20px, -10px);
            transform: scale(0) translate(-20px, -10px);
  }
  25% {
    -webkit-transform: scale(.25) translate(-40px, -45px);
            transform: scale(.25) translate(-40px, -45px);
  }
  50% {
    -webkit-transform: scale(.5) translate(-60px, -70px);
            transform: scale(.5) translate(-60px, -70px);
  }
  75% {
    -webkit-transform: scale(.75) translate(-40px, -95px);
            transform: scale(.75) translate(-40px, -95px);
  }
  100% {
    -webkit-transform: scale(1) translate(-30px, -100px);
            transform: scale(1) translate(-30px, -100px);
  }
}

@-webkit-keyframes bubble-3 {
  0% {
    opacity: 1;
    -webkit-transform: scale(0) translate(0, 0);
            transform: scale(0) translate(0, 0);
  }
  25% {
    -webkit-transform: scale(.25) translate(0, -20px);
            transform: scale(.25) translate(0, -20px);
  }
  50% {
    -webkit-transform: scale(.5) translate(-10px, -40px);
            transform: scale(.5) translate(-10px, -40px);
  }
  75% {
    -webkit-transform: scale(.75) translate(10px, -60px);
            transform: scale(.75) translate(10px, -60px);
  }
  100% {
    -webkit-transform: scale(1) translate(0, -100px);
            transform: scale(1) translate(0, -100px);
  }
}

@keyframes bubble-3 {
  0% {
    opacity: 1;
    -webkit-transform: scale(0) translate(0, 0);
            transform: scale(0) translate(0, 0);
  }
  25% {
    -webkit-transform: scale(.25) translate(0, -20px);
            transform: scale(.25) translate(0, -20px);
  }
  50% {
    -webkit-transform: scale(.5) translate(-10px, -40px);
            transform: scale(.5) translate(-10px, -40px);
  }
  75% {
    -webkit-transform: scale(.75) translate(10px, -60px);
            transform: scale(.75) translate(10px, -60px);
  }
  100% {
    -webkit-transform: scale(1) translate(0, -100px);
            transform: scale(1) translate(0, -100px);
  }
}
.ring_wrapper{
	margin: 0 auto;
	width:150px;
	height:150px;
	border-radius: 50%;
	background: #f0f0f0;
	position: relative;
	border:1px solid #ddd;
	box-sizing: border-box;
}
.ring_inner{
	width:130px;
	height:130px;
	border-radius: 50%;
	background: #88e6f1;
	position: absolute;
	top:9px;
	left:9px;
	border:1px solid #ccc;
	   /* text-shadow: 1px 1px 2px rgba(0,0,0,.5);*/
}
	.circle {
    width: 142px;
    height: 142px;
    top: 3px;
    left: 3px;
    position: absolute;
    border-radius: 50%;
    background:#3fe8f9;
    border: 1px solid #ccc;
}
.pie_left, .pie_right {
  width: 142px;
    height: 142px;
    position: absolute;
    top: -1px;
    left: -1px;
}
.left, .right {
    display: block;
    width: 142px;
    height: 142px;
    border-radius: 50%;
    position: absolute;
    top: 0;
    left: 0;
    transform: rotate(0deg);
   
}

.left{
	 background:orange;
	 transition:1s all ease-in-out;
	 transition-delay: 1s;
-moz-transition-delay: 1s; /* Firefox 4 */
-webkit-transition-delay: 1s; /* Safari 和 Chrome */
-o-transition-delay: 1s; /* Opera */
}
.right{
	 background:orange;
	  transition:1s all ease-in-out;
}
.pie_right, .right {
    clip:rect(0,auto,auto,71px);
}
.pie_left, .left {
    clip:rect(0,71px,auto,0);
}
.mask {
    width: 134px;
    height: 134px;
    border-radius: 50%;
    left: 3px;
    top: 3px;
    background: #fff;
    position: absolute;
    text-align: center;
    line-height: 128px;
    font-size: 16px;
}	