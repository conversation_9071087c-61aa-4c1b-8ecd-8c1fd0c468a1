package clouesp.hes.core.uci.soap.custom.webservice.meterdefineconfig.response;

import java.util.Date;
import java.util.List;

import clouesp.hes.core.uci.soap.custom.webservice.common.ReplyError;

import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.enums.system.TaskState;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.redis.JedisUtils;

import ch.iec.tc57._2011.meterdefineconfig_.Arrays;
import ch.iec.tc57._2011.meterdefineconfigmessage.MeterDefineConfigPayloadType;
import ch.iec.tc57._2011.meterdefineconfigmessage.MeterDefineConfigResponseMessageType;


/**
 * @ClassName: MeterDefineConfigReadingResponse
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年7月18日 下午1:40:34
 *
 */
public class MeterDefineConfigReadingWritingResponse extends
		MeterDefineConfigResponseAbstract {

	@Override
	public void parsePayload() {
		// TODO Auto-generated method stub
		MeterDefineConfigResponseMessageType responseMessageType = (MeterDefineConfigResponseMessageType) this.object;
		MeterDefineConfigPayloadType payload = responseMessageType.getPayload();
		if(null != payload) {
			this.sn = (String) payload.getMeterDefineConfig().getMeters().get(0).getMRID();
			this.dataItemId = payload.getMeterDefineConfig().getReadingType().getRef();
			
			this.ajaxJson = createAjaxJson(payload);
		}
	}

	/**
	 * 
	 * @Title: createAjaxJson
	 * @Description: 创建 ajaxJson 对象
	 * @param payload
	 * @return
	 * @return AjaxJson
	 * @throws
	 */
	private AjaxJson createAjaxJson(MeterDefineConfigPayloadType payload) {
		// TODO Auto-generated method stub
		AjaxJson resultJson = new AjaxJson();
		resultJson.put("messageId", messageId);
		resultJson.setMsg(result);
		if(!this.code.equals(ReplyError.SUCCESS)) {
			resultJson.setSuccess(false);
			resultJson.put("status", TaskState.Failed.getState());
		} else {
			resultJson.put("status", TaskState.Success.getState());
		}
		
		if(this.timestamp != null){

			Date responseTime=DateUtils.xmlDate2Date(this.timestamp);
			
			resultJson.put("responseTime", DateUtils.formatDate(responseTime, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
			
		}else{
			resultJson.put("responseTime", DateUtils.formatDate(new Date(), ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
		}
		resultJson.put("webservTask", JedisUtils.getObject(messageId));
		resultJson.put("verb", verb);
		List<Arrays> arrayss = payload.getMeterDefineConfig().getArrays();
		if(null == arrayss || arrayss.isEmpty()) {
			return resultJson;
		}
		
		resultJson.put("arrays", arrayss.get(0));
		
		JedisUtils.delObject(messageId);
		return resultJson;
	}


}
