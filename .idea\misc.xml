<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="FrameworkDetectionExcludesConfiguration">
    <file type="web" url="file://$PROJECT_DIR$/Core/gyuci" />
    <file type="web" url="file://$PROJECT_DIR$/Core/hxuci" />
    <file type="web" url="file://$PROJECT_DIR$/Core/InterFace/gyuci" />
    <file type="web" url="file://$PROJECT_DIR$/Core/InterFace/UCI_S" />
    <file type="web" url="file://$PROJECT_DIR$/Core/jnuci" />
    <file type="web" url="file://$PROJECT_DIR$/Core/syuci" />
    <file type="web" url="file://$PROJECT_DIR$/Core/UCI" />
    <file type="web" url="file://$PROJECT_DIR$/Web/HESWeb7.0" />
    <file type="web" url="file://$PROJECT_DIR$/../Mdm/MdmUCI" />
  </component>
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/Common/Asset/pom.xml" />
        <option value="$PROJECT_DIR$/Common/AssetTrans/pom.xml" />
        <option value="$PROJECT_DIR$/Core/Calculation/pom.xml" />
        <option value="$PROJECT_DIR$/Core/channels/pom.xml" />
        <option value="$PROJECT_DIR$/Common/DbAccess/pom.xml" />
        <option value="$PROJECT_DIR$/Common/Entity/pom.xml" />
        <option value="$PROJECT_DIR$/Core/hxuci/pom.xml" />
        <option value="$PROJECT_DIR$/Common/IntervalCalc/pom.xml" />
        <option value="$PROJECT_DIR$/Common/logger/pom.xml" />
        <option value="$PROJECT_DIR$/Common/loggerquery/pom.xml" />
        <option value="$PROJECT_DIR$/Core/loggerqueryser/pom.xml" />
        <option value="$PROJECT_DIR$/Common/MQBus/pom.xml" />
        <option value="$PROJECT_DIR$/Common/Protocol/pom.xml" />
        <option value="$PROJECT_DIR$/Common/storage/pom.xml" />
        <option value="$PROJECT_DIR$/Common/task/pom.xml" />
        <option value="$PROJECT_DIR$/Core/Schedules/pom.xml" />
        <option value="$PROJECT_DIR$/Core/UCI/pom.xml" />
        <option value="$PROJECT_DIR$/../Mdm/pom.xml" />
        <option value="$PROJECT_DIR$/../Mdm/MdmUCI/pom.xml" />
        <option value="$PROJECT_DIR$/Core/jnuci/pom.xml" />
        <option value="$PROJECT_DIR$/Core/syuci/pom.xml" />
        <option value="$PROJECT_DIR$/Core/InterFace/UCI_S/pom.xml" />
        <option value="$PROJECT_DIR$/Core/InterFace/UCI_S/pom.xml.bak" />
        <option value="$PROJECT_DIR$/Core/hsuci/pom.xml" />
        <option value="$PROJECT_DIR$/Core/gyuci/pom.xml.bak" />
        <option value="$PROJECT_DIR$/Core/gyuci/pom.xml" />
        <option value="$PROJECT_DIR$/Core/InterFace/gyuci/pom.xml" />
        <option value="$PROJECT_DIR$/Web/HESWeb7.0/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_1_8" project-jdk-name="1.8" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
</project>