/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictDataitemGroup{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
*********************************************************************************************************/

package com.clou.esp.hes.app.web.service.dict;

import java.util.List;

import com.clou.esp.hes.app.web.service.common.CommonService;
import com.clou.esp.hes.app.web.model.dict.DictDataitemGroup;

public interface DictDataitemGroupService extends CommonService<DictDataitemGroup>{
	
	/**
	 * 获取app_type=1,2的数据
	 * @Description 
	 * @param temp
	 * @return List<DictDataitemGroup>
	 * <AUTHOR> 
	 * @Time 2018年4月8日 下午3:35:54
	 */
	public List<DictDataitemGroup> getMeterDataEventExportList(DictDataitemGroup temp);
	
	public String getMeterDataEventType(DictDataitemGroup temp) ;
	
	public List<DictDataitemGroup> getList(DictDataitemGroup entity);
}