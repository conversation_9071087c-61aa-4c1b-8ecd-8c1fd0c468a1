/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataStatisticsEvent{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：赵久霆
 * 创建于：2018-09-19 07:26:51
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.data;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.dao.data.DataStatisticsEventDao;
import com.clou.esp.hes.app.web.model.data.DataStatisticsEvent;
import com.clou.esp.hes.app.web.service.data.DataStatisticsEventService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dataStatisticsEventService")
public class DataStatisticsEventServiceImpl  extends CommonServiceImpl<DataStatisticsEvent>  implements DataStatisticsEventService {

	@Resource
	private DataStatisticsEventDao dataStatisticsEventDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dataStatisticsEventDao);
    }
	@SuppressWarnings("rawtypes")
	public DataStatisticsEventServiceImpl() {}
	
	@Override
	public List<DataStatisticsEvent> getList(DataStatisticsEvent entity){
		return this.dataStatisticsEventDao.getList(entity);
	}
	@Override
	public List<DataStatisticsEvent> getListForOrgIds(DataStatisticsEvent dataStatisticsEvent){
		return dataStatisticsEventDao.getListForOrgIds(dataStatisticsEvent);
	}

	
}