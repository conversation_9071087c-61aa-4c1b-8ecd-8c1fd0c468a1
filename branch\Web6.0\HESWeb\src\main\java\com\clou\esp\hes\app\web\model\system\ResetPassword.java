package com.clou.esp.hes.app.web.model.system;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class ResetPassword  extends BaseEntity {
	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public ResetPassword() {
	}

	/**account*/
	private java.lang.String account;
	/**Original Password*/
	private java.lang.String originalPassword;
	/**New Password*/
	private java.lang.String newPassword;
	/**Confirm Password*/
	private java.lang.String confirmPassword;
	public java.lang.String getAccount() {
		return account;
	}
	public void setAccount(java.lang.String account) {
		this.account = account;
	}
	public java.lang.String getOriginalPassword() {
		return originalPassword;
	}
	public void setOriginalPassword(java.lang.String originalPassword) {
		this.originalPassword = originalPassword;
	}
	public java.lang.String getNewPassword() {
		return newPassword;
	}
	public void setNewPassword(java.lang.String newPassword) {
		this.newPassword = newPassword;
	}
	public java.lang.String getConfirmPassword() {
		return confirmPassword;
	}
	public void setConfirmPassword(java.lang.String confirmPassword) {
		this.confirmPassword = confirmPassword;
	}
	
}
