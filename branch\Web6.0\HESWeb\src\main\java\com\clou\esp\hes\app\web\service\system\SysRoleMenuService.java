/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysRoleMenu{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-10-27 08:02:17
 * 最后修改时间：
 * 
*********************************************************************************************************/

package com.clou.esp.hes.app.web.service.system;

import com.clou.esp.hes.app.web.model.system.SysRoleMenu;
import com.clou.esp.hes.app.web.service.common.CommonService;

public interface SysRoleMenuService extends CommonService<SysRoleMenu>{
	
	/**
	 * 根据role实体内的信息获取sysRoleMenu实体
	 * @Description 
	 * @param sysRole
	 * @return SysRoleMenu
	 * <AUTHOR> 
	 * @Time 2018年3月20日 下午5:36:16
	 */
	public SysRoleMenu getEntityByRole(SysRoleMenu sysRoleMenu);
}