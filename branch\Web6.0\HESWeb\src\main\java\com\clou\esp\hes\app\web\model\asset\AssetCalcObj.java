package com.clou.esp.hes.app.web.model.asset;

import com.clou.esp.hes.app.web.model.common.BaseEntity;


/**
 * @ClassName: AssetCalcObj
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年9月5日 上午11:07:35
 *
 */
public class AssetCalcObj extends BaseEntity {

	private static final long serialVersionUID = 1L;
	
	/**
	 * 1:线损  2:供电量  3:售电量
	 */
	private int type;
	
	private String name;
	
	/**
	 * 1:电表 2：集中器 3、线路 4 变压器
	 */
	private int entityType;
	
	private String entityId;
	
	private String entityName;
	
	private String[] ids;
	/**
	 * 1：日 2:月
	 */
	private int tvType;
	
	//参考id
	private String referenceId;
	

	public String getReferenceId() {
		return referenceId;
	}

	public void setReferenceId(String referenceId) {
		this.referenceId = referenceId;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}


	public int getEntityType() {
		return entityType;
	}

	public void setEntityType(int entityType) {
		this.entityType = entityType;
	}

	public String getEntityId() {
		return entityId;
	}

	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}

	public int getTvType() {
		return tvType;
	}

	public void setTvType(int tvType) {
		this.tvType = tvType;
	}

	public String getEntityName() {
		return entityName;
	}

	public void setEntityName(String entityName) {
		this.entityName = entityName;
	}

	public String[] getIds() {
		return ids;
	}

	public void setIds(String[] ids) {
		this.ids = ids;
	}
	
}
