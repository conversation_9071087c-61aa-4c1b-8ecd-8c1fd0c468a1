package com.clou.esp.hes.app.web.service.impl.asset;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.asset.AssetTransformerDao;
import com.clou.esp.hes.app.web.model.asset.AssetCalcObj;
import com.clou.esp.hes.app.web.model.asset.AssetLine;
import com.clou.esp.hes.app.web.model.asset.AssetTransformer;
import com.clou.esp.hes.app.web.service.asset.AssetTransformerService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;


/**
 * @ClassName: AssetTransformerServiceImpl
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年9月20日 上午9:11:26
 *
 */
@Component
@Service("assetTransformerService")
public class AssetTransformerServiceImpl extends
		CommonServiceImpl<AssetTransformer> implements AssetTransformerService{

	@Resource
	private AssetTransformerDao assetTransformerDao;
	
	@Autowired
	public void setCommonService() {
		// TODO Auto-generated method stub
		super.setCommonService(assetTransformerDao);
	}

	public AssetTransformerServiceImpl() {
	}

	@Override
	public List<AssetCalcObj> getListByCalObj(JqGridSearchTo jqGridSearchTo) {
		// TODO Auto-generated method stub
		return assetTransformerDao.getListByCalObj(jqGridSearchTo);
	}


	@Override
	public JqGridResponseTo getListByCalObjJqGrid(JqGridSearchTo jqGridSearchTo) {
		// TODO Auto-generated method stub
		PageHelper
		.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		PageInfo<AssetCalcObj> pageInfo = new PageInfo<AssetCalcObj>(assetTransformerDao.getListByCalObj(jqGridSearchTo));
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}
	

}
