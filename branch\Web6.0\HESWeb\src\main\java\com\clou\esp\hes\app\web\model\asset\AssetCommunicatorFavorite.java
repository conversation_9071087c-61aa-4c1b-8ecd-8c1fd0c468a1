/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetCommunicatorFavorite{ } 
 * 
 * 摘    要： assetCommunicatorFavorite
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-03-09 07:39:35
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.asset;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class AssetCommunicatorFavorite  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public AssetCommunicatorFavorite() {
	}

	/**userId*/
	private java.lang.String userId;

	/**
	 * userId
	 * @return the value of ASSET_COMMUNICATOR_FAVORITE.USER_ID
	 * @mbggenerated 2018-03-09 07:39:35
	 */
	public java.lang.String getUserId() {
		return userId;
	}

	/**
	 * userId
	 * @param userId the value for ASSET_COMMUNICATOR_FAVORITE.USER_ID
	 * @mbggenerated 2018-03-09 07:39:35
	 */
    	public void setUserId(java.lang.String userId) {
		this.userId = userId;
	}

	public AssetCommunicatorFavorite(java.lang.String userId ) {
		super();
		this.userId = userId;
	}

}