/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class TestUser{ } 
 * 
 * 摘    要： 测试用户
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-03-29 08:30:58
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.demo;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.clou.esp.hes.app.web.validation.ValidGroup2;
import com.power7000g.core.excel.Excel;
public class TestUser  extends BaseEntity {
	

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;

    /**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public TestUser() {
	}

	/**用户名称*/
	//@Excel(name = "testUser.name", width = 30,groups=ValidGroup1.class)
	//@Length(min=1,max=20,message="n",groups=ValidGroup1.class) 
	private java.lang.String name;
	/**性别*/
	//@Excel(name = "testUser.sex", width = 30,groups=ValidGroup1.class)
	//@NotEmpty(message="n",groups=ValidGroup1.class) 
	private java.lang.String sex;
	/**联系方式*/
	//@Excel(name = "testUser.tel", width = 30,groups=ValidGroup1.class)
	//@Length(min=11,max=11,message="n",groups=ValidGroup1.class)
	private java.lang.String tel;
	/**生日*/
	//@Excel(name = "testUser.birthday", width = 30,groups=ValidGroup2.class)
	//@NotNull(message="n",groups=ValidGroup1.class) 
	private java.util.Date birthday;
	/**昵称*/
	//@Excel(name = "testUser.nickName", width = 30,groups=ValidGroup2.class)
	//@Length(min=1,max=50,message="n",groups=ValidGroup1.class)
	private java.lang.String nickName;

	/**
	 * This method was generated by MyBatis Generator. This method returns the
	 * value of the database column TEST_USER.NAME
	 * 用户名称
	 * @return the value of TEST_USER.NAME
	 * @mbggenerated 2017-03-29 08:30:58
	 */
	public java.lang.String getName() {
		return name;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the
	 * value of the database column TEST_USER.NAME
	 * 用户名称
	 * @param name the value for TEST_USER.NAME
	 * @mbggenerated 2017-03-29 08:30:58
	 */
    	public void setName(java.lang.String name) {
		this.name = name;
	}
	/**
	 * This method was generated by MyBatis Generator. This method returns the
	 * value of the database column TEST_USER.SEX
	 * 性别
	 * @return the value of TEST_USER.SEX
	 * @mbggenerated 2017-03-29 08:30:58
	 */
	public java.lang.String getSex() {
		return sex;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the
	 * value of the database column TEST_USER.SEX
	 * 性别
	 * @param sex the value for TEST_USER.SEX
	 * @mbggenerated 2017-03-29 08:30:58
	 */
    	public void setSex(java.lang.String sex) {
		this.sex = sex;
	}
	/**
	 * This method was generated by MyBatis Generator. This method returns the
	 * value of the database column TEST_USER.TEL
	 * 联系方式
	 * @return the value of TEST_USER.TEL
	 * @mbggenerated 2017-03-29 08:30:58
	 */
	public java.lang.String getTel() {
		return tel;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the
	 * value of the database column TEST_USER.TEL
	 * 联系方式
	 * @param tel the value for TEST_USER.TEL
	 * @mbggenerated 2017-03-29 08:30:58
	 */
    	public void setTel(java.lang.String tel) {
		this.tel = tel;
	}
	/**
	 * This method was generated by MyBatis Generator. This method returns the
	 * value of the database column TEST_USER.BIRTHDAY
	 * 生日
	 * @return the value of TEST_USER.BIRTHDAY
	 * @mbggenerated 2017-03-29 08:30:58
	 */
	public java.util.Date getBirthday() {
		return birthday;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the
	 * value of the database column TEST_USER.BIRTHDAY
	 * 生日
	 * @param birthday the value for TEST_USER.BIRTHDAY
	 * @mbggenerated 2017-03-29 08:30:58
	 */
    	public void setBirthday(java.util.Date birthday) {
		this.birthday = birthday;
	}
	/**
	 * This method was generated by MyBatis Generator. This method returns the
	 * value of the database column TEST_USER.NICK_NAME
	 * 昵称
	 * @return the value of TEST_USER.NICK_NAME
	 * @mbggenerated 2017-03-29 08:30:58
	 */
	public java.lang.String getNickName() {
		return nickName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the
	 * value of the database column TEST_USER.NICK_NAME
	 * 昵称
	 * @param nickName the value for TEST_USER.NICK_NAME
	 * @mbggenerated 2017-03-29 08:30:58
	 */
    	public void setNickName(java.lang.String nickName) {
		this.nickName = nickName;
	}

	public TestUser(java.lang.String name 
	,java.lang.String sex 
	,java.lang.String tel 
	,java.sql.Date birthday 
	,java.lang.String nickName ) {
		super();
		this.name = name;
		this.sex = sex;
		this.tel = tel;
		this.birthday = birthday;
		this.nickName = nickName;
	}

	@Override
	public String toString() {
		return "TestUser [name=" + name + ", sex=" + sex + ", tel=" + tel
				+ ", birthday=" + birthday + ", nickName=" + nickName + "]";
	}
	
	

}