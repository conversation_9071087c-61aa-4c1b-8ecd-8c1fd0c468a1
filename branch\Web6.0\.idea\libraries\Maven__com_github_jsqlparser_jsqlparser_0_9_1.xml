<component name="libraryTable">
  <library name="Maven: com.github.jsqlparser:jsqlparser:0.9.1">
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/com/github/jsqlparser/jsqlparser/0.9.1/jsqlparser-0.9.1.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/com/github/jsqlparser/jsqlparser/0.9.1/jsqlparser-0.9.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/com/github/jsqlparser/jsqlparser/0.9.1/jsqlparser-0.9.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>