/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictDataitem{ } 
 * 
 * 摘    要： 定炒数据/事件数据
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.dict;

import java.math.BigDecimal;
import java.util.Date;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.power7000g.core.excel.Excel;

public class DictDataitem  extends BaseEntity {

	private static final long serialVersionUID = -8522852410249052630L;

	public DictDataitem() {}

	@Excel(name = "Channel Name", width = 30)
	private String 		name;
	
	@Excel(name = "Protocol ID", width = 30)
	private String 		protocolId;
	
	@Excel(name = "Channel ID", width = 30)
	private String 		id;
	
	@Excel(name = "Operation Type", width = 30)
	private String 		opType;
	
	@Excel(name = "Unit", width = 30)
	private String 		unit;
	
	@Excel(name = "Show Unit", width = 30)
	private BigDecimal 	showUnit;
	
	@Excel(name = "OBIS Code", width = 30)
	private String 		protocolCode;
	
	@Excel(name = "Channel Type", width = 30)
	private String 		dataitemType;
	
	@Excel(name = "Parse Type", width = 30)
	private String 		parseType;
	
	@Excel(name = "Parse Len", width = 30)
	private Integer 		parseLen;
	
	@Excel(name = "Scale", width = 30)
	private Integer 		scale;
	
	
	private String 		isShow;
	private String 		groupId;
	private Date 		tv;
	private Date 		exportTv;
	private String 		exportResult;
	private String 		exportReason;

	private String      oldId;
	
	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getOldId() {
		return oldId;
	}

	public void setOldId(String oldId) {
		this.oldId = oldId;
	}

	public String getName() {
		return MutiLangUtil.assemblyI18nData(MutiLangUtil.DATAITEM_I18N,name);
	}

	public void setName(String name) {
		this.name = name;
	}

    
	public String getProtocolId() {
		return protocolId;
	}

	
	public void setProtocolId(String protocolId) {
		this.protocolId = protocolId;
	}

	
	public String getProtocolCode() {
		return protocolCode;
	}

	
	public void setProtocolCode(String protocolCode) {
		this.protocolCode = protocolCode;
	}

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}

	public Date getTv() {
		return tv;
	}

	public void setTv(Date tv) {
		this.tv = tv;
	}

	public Date getExportTv() {
		return exportTv;
	}

	public void setExportTv(Date exportTv) {
		this.exportTv = exportTv;
	}

	public String getExportResult() {
		return exportResult;
	}

	public void setExportResult(String exportResult) {
		this.exportResult = exportResult;
	}

	public String getExportReason() {
		return exportReason;
	}

	public void setExportReason(String exportReason) {
		this.exportReason = exportReason;
	}

	public String getOpType() {
		return opType;
	}

	public String getUnit() {
		return unit;
	}

	public BigDecimal getShowUnit() {
		return showUnit;
	}

	public void setOpType(String opType) {
		this.opType = opType;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public void setShowUnit(BigDecimal showUnit) {
		this.showUnit = showUnit;
	}

	public DictDataitem(String id,String name) {
		super();
		this.id=id;
		this.name = name;
	}

	public String getDataitemType() {
		return dataitemType;
	}

	public void setDataitemType(String dataitemType) {
		this.dataitemType = dataitemType;
	}

	public String getIsShow() {
		return isShow;
	}

	public void setIsShow(String isShow) {
		this.isShow = isShow;
	}

	public String getParseType() {
		return parseType;
	}

	public void setParseType(String parseType) {
		this.parseType = parseType;
	}

	public Integer getParseLen() {
		return parseLen;
	}

	public void setParseLen(Integer parseLen) {
		this.parseLen = parseLen;
	}

	public Integer getScale() {
		return scale;
	}

	public void setScale(Integer scale) {
		this.scale = scale;
	}

}