/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeter{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-06 08:04:55
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.asset;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.asset.AssetCalcObjDao;
import com.clou.esp.hes.app.web.model.asset.AssetCalcObj;
import com.clou.esp.hes.app.web.model.asset.AssetCalcObjMap;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;
import com.clou.esp.hes.app.web.service.asset.AssetCalcObjService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("assetCalcObjService")
public class AssetCalcObjServiceImpl extends CommonServiceImpl<AssetCalcObj>	implements AssetCalcObjService {

	@Resource
	private AssetCalcObjDao assetCalcObjDao;

	@Autowired
	public void setCommonService() {
		super.setCommonService(assetCalcObjDao);
	}

	public AssetCalcObjServiceImpl() {}

	@Override
	public List<AssetCalcObjMap> getCalObjMapList(AssetCalcObjMap map) {
		return this.assetCalcObjDao.getCalObjMapList(map);
	}

	@Override
	public List<DictDataitem> getDictDataitems(String mearsureId,String dateType) {
		return this.assetCalcObjDao.getDictDataitems(mearsureId,dateType);
	}

	
}