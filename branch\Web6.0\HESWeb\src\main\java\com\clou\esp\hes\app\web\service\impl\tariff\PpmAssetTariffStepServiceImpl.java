/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class PpmAssetTariffStep{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2019-09-25 03:58:35
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.tariff;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.tariff.PpmAssetTariffStepDao;
import com.clou.esp.hes.app.web.model.tariff.PpmAssetTariffStep;
import com.clou.esp.hes.app.web.service.tariff.PpmAssetTariffStepService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("ppmAssetTariffStepService")
public class PpmAssetTariffStepServiceImpl  extends CommonServiceImpl<PpmAssetTariffStep>  implements PpmAssetTariffStepService {

	@Resource
	private PpmAssetTariffStepDao ppmAssetTariffStepDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(ppmAssetTariffStepDao);
    }
	@SuppressWarnings("rawtypes")
	public PpmAssetTariffStepServiceImpl() {}
	
	
}