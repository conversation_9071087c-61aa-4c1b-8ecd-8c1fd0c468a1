package com.clou.esp.hes.app.web.model.demo;

import java.io.Serializable;

/**
 * 文件上传进度展示实体类
 * <AUTHOR>
 * @date 2018/3/12
 */
public class ProgressEntity implements Serializable{
    
    /**
	 * @2018年3月12日 下午6:20:20
	 * @Wangjiale
	 */
	private static final long serialVersionUID = 1L;
	
	private long pBytesRead = 0L;   	//到目前为止读取文件的比特数   
    private long pContentLength = 0L;	//文件总大小   
    private int pItems;					//目前正在读取第几个文件
    private float percentage;			//上传百分比
      
    public long getpBytesRead() {  
        return pBytesRead;  
    }  
    public void setpBytesRead(long pBytesRead) {  
        this.pBytesRead = pBytesRead;  
    }  
    public long getpContentLength() {  
        return pContentLength;  
    }  
    public void setpContentLength(long pContentLength) {  
        this.pContentLength = pContentLength;  
    }  
    public int getpItems() {  
        return pItems;  
    }  
    public void setpItems(int pItems) {  
        this.pItems = pItems;  
    }
    
	public float getPercentage() {
		return percentage;
	}
	public void setPercentage(float percentage) {
		this.percentage = percentage;
	}
	@Override  
    public String toString() {  
        return "ProgressEntity {pBytesRead=" + pBytesRead + ", pContentLength="  
                + pContentLength +", pItems=" + pItems + "}";  
    }
}
