/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeterGroupValue{ } 
 * 
 * 摘    要： assetMeterGroupValue
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-01-16 01:58:30
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.asset;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class AssetMeterGroupValue  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public AssetMeterGroupValue() {
	}
	private java.lang.String groupId;
	/**dataitemId*/
	private java.lang.String dataitemId;
	/**xmlValue*/
	private java.lang.String xmlValue;

	/**
	 * dataitemId
	 * @return the value of ASSET_METER_GROUP_VALUE.DATAITEM_ID
	 * @mbggenerated 2018-01-16 01:58:30
	 */
	public java.lang.String getDataitemId() {
		return dataitemId;
	}

	/**
	 * dataitemId
	 * @param dataitemId the value for ASSET_METER_GROUP_VALUE.DATAITEM_ID
	 * @mbggenerated 2018-01-16 01:58:30
	 */
    	public void setDataitemId(java.lang.String dataitemId) {
		this.dataitemId = dataitemId;
	}
	/**
	 * xmlValue
	 * @return the value of ASSET_METER_GROUP_VALUE.XML_VALUE
	 * @mbggenerated 2018-01-16 01:58:30
	 */
	public java.lang.String getXmlValue() {
		return xmlValue;
	}

	/**
	 * xmlValue
	 * @param xmlValue the value for ASSET_METER_GROUP_VALUE.XML_VALUE
	 * @mbggenerated 2018-01-16 01:58:30
	 */
    	public void setXmlValue(java.lang.String xmlValue) {
		this.xmlValue = xmlValue;
	}

	public java.lang.String getGroupId() {
		return groupId;
	}

	public void setGroupId(java.lang.String groupId) {
		this.groupId = groupId;
	}

	public AssetMeterGroupValue(String groupId, String dataitemId,
			String xmlValue) {
		super();
		this.groupId = groupId;
		this.dataitemId = dataitemId;
		this.xmlValue = xmlValue;
	}

	

}