/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataParameterJob{ } 
 * 
 * 摘    要： dataParameterJob
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-04-27 09:13:12
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.data;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.excel.Excel;

public class DataParameterJob  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DataParameterJob() {
	}

	/**planId*/
	private java.lang.String planId;
	
	/**deviceId*/
	private java.lang.String deviceId;
	
	private java.lang.String meterId;
	private java.util.Date startTime;
	private java.util.Date endTime;
	@Excel(name = "Device SN", width = 30, groups = ValidGroup1.class)
	private java.lang.String sn;
	private java.lang.String groupType;
	private java.lang.String groupName;
	@Excel(name = "Model", width = 30, groups = ValidGroup1.class)
	private java.lang.String model;
	@Excel(name = "Manufacturer", width = 30, groups = ValidGroup1.class)
	private java.lang.String manufacturer;

	/**state 1: Running 2: Done 3: Cancel 4: Waiting */
	@Excel(name = "Status", width = 30, groups = ValidGroup1.class)
	private java.lang.String state;
	/**lastExecTime*/
	@Excel(name = "Last Execute Time", width = 30, groups = ValidGroup1.class)
	private java.util.Date lastExecTime;
	/**failedReason*/
	@Excel(name = "Reason", width = 30, groups = ValidGroup1.class)
	private java.lang.String failedReason;
	/**
	 * planId
	 * @return the value of DATA_PARAMETER_JOB.PLAN_ID
	 * @mbggenerated 2018-04-27 09:13:12
	 */
	public java.lang.String getPlanId() {
		return planId;
	}

	/**
	 * planId
	 * @param planId the value for DATA_PARAMETER_JOB.PLAN_ID
	 * @mbggenerated 2018-04-27 09:13:12
	 */
    	public void setPlanId(java.lang.String planId) {
		this.planId = planId;
	}
	/**
	 * state
	 * @return the value of DATA_PARAMETER_JOB.STATE
	 * @mbggenerated 2018-04-27 09:13:12
	 */
	public java.lang.String getState() {
		return state;
	}

	/**
	 * state
	 * @param state the value for DATA_PARAMETER_JOB.STATE
	 * @mbggenerated 2018-04-27 09:13:12
	 */
    	public void setState(java.lang.String state) {
		this.state = state;
	}
	/**
	 * lastExecTime
	 * @return the value of DATA_PARAMETER_JOB.LAST_EXEC_TIME
	 * @mbggenerated 2018-04-27 09:13:12
	 */
	public java.util.Date getLastExecTime() {
		return lastExecTime;
	}

	/**
	 * lastExecTime
	 * @param lastExecTime the value for DATA_PARAMETER_JOB.LAST_EXEC_TIME
	 * @mbggenerated 2018-04-27 09:13:12
	 */
    	public void setLastExecTime(java.util.Date lastExecTime) {
		this.lastExecTime = lastExecTime;
	}
	/**
	 * failedReason
	 * @return the value of DATA_PARAMETER_JOB.FAILED_REASON
	 * @mbggenerated 2018-04-27 09:13:12
	 */
	public java.lang.String getFailedReason() {
		return failedReason;
	}

	/**
	 * failedReason
	 * @param failedReason the value for DATA_PARAMETER_JOB.FAILED_REASON
	 * @mbggenerated 2018-04-27 09:13:12
	 */
    	public void setFailedReason(java.lang.String failedReason) {
		this.failedReason = failedReason;
	}
	/**
	 * deviceId
	 * @return the value of DATA_PARAMETER_JOB.DEVICE_ID
	 * @mbggenerated 2018-04-27 09:13:12
	 */
	public java.lang.String getDeviceId() {
		return deviceId;
	}

	/**
	 * deviceId
	 * @param deviceId the value for DATA_PARAMETER_JOB.DEVICE_ID
	 * @mbggenerated 2018-04-27 09:13:12
	 */
    	public void setDeviceId(java.lang.String deviceId) {
		this.deviceId = deviceId;
	}

	public DataParameterJob(java.lang.String planId 
	,java.lang.String state 
	,java.util.Date lastExecTime 
	,java.lang.String failedReason 
	,java.lang.String deviceId ) {
		super();
		this.planId = planId;
		this.state = state;
		this.lastExecTime = lastExecTime;
		this.failedReason = failedReason;
		this.deviceId = deviceId;
	}

	public java.util.Date getStartTime() {
		return startTime;
	}

	public void setStartTime(java.util.Date startTime) {
		this.startTime = startTime;
	}

	public java.util.Date getEndTime() {
		return endTime;
	}

	public void setEndTime(java.util.Date endTime) {
		this.endTime = endTime;
	}

	public java.lang.String getSn() {
		return sn;
	}

	public void setSn(java.lang.String sn) {
		this.sn = sn;
	}

	public java.lang.String getGroupType() {
		return groupType;
	}

	public void setGroupType(java.lang.String groupType) {
		this.groupType = groupType;
	}

	public java.lang.String getGroupName() {
		return groupName;
	}

	public void setGroupName(java.lang.String groupName) {
		this.groupName = groupName;
	}

	public java.lang.String getModel() {
		return model;
	}

	public void setModel(java.lang.String model) {
		this.model = model;
	}

	public java.lang.String getManufacturer() {
		return manufacturer;
	}

	public void setManufacturer(java.lang.String manufacturer) {
		this.manufacturer = manufacturer;
	}

	public java.lang.String getMeterId() {
		return meterId;
	}

	public void setMeterId(java.lang.String meterId) {
		this.meterId = meterId;
	}

}