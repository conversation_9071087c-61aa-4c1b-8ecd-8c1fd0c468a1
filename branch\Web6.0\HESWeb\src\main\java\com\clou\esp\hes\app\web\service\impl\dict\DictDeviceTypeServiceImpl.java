/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictDeviceModel{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-18 02:59:36
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.dict;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.dict.DictDeviceTypeDao;
import com.clou.esp.hes.app.web.model.dict.DictDeviceType;
import com.clou.esp.hes.app.web.service.dict.DictDeviceTypeService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dictDeviceTypeService")
public class DictDeviceTypeServiceImpl  extends CommonServiceImpl<DictDeviceType>  implements DictDeviceTypeService {

	@Resource
	private DictDeviceTypeDao dictDeviceTypeDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dictDeviceTypeDao);
    }
	@SuppressWarnings("rawtypes")
	public DictDeviceTypeServiceImpl() {}
	@Override
	public List<DictDeviceType> getListByIds(List<Integer> Ids) {
		return this.dictDeviceTypeDao.getListByIds(Ids);
	}
	
	
}