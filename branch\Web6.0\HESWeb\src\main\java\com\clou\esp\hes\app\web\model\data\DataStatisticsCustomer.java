/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataStatisticsCustomer{ } 
 * 
 * 摘    要： dataStatisticsCustomer
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-11-20 06:36:35
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.data;

import java.math.BigDecimal;
import java.util.Date;

import org.apache.commons.lang.StringUtils;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.clou.esp.hes.app.web.validation.ValidGroup2;
import com.power7000g.core.excel.Excel;

public class DataStatisticsCustomer  extends BaseEntity {

	
	private static final long serialVersionUID = -6727632556805992754L;

	public DataStatisticsCustomer() {}
	@Excel(name = "Organization", width = 30 ,groups = {ValidGroup1.class,ValidGroup2.class})
	private String orgName;
	@Excel(name = "Customer Type", width = 30,groups = {ValidGroup1.class,ValidGroup2.class})
	private String customerType;
	@Excel(name = "Industry Type", width = 30,groups = {ValidGroup1.class,ValidGroup2.class})
	private String industryType;
	@Excel(name = "Time Type", width = 30,groups = {ValidGroup1.class,ValidGroup2.class})
	private String tvType;
	@Excel(name = "Time", width = 30,groups = {ValidGroup1.class,ValidGroup2.class})
	private String time;
	@Excel(name = "Value", width = 30,groups = {ValidGroup1.class,ValidGroup2.class})
	private BigDecimal value;
	@Excel(name = "Miss Data", width = 30,groups = {ValidGroup1.class,ValidGroup2.class})
	private String missData;
	
	
	private String idType;
	private Date tv;
	private BigDecimal percent;
	private BigDecimal countCurrent;
	private String orgId;
	private String name;
	private String countActual;
	private String countTotal;
	private String countCurrentFormatter;
	private String countTotalFormatter;
	
	
	
	
	public String getMissData() {
		return missData;
	}


	public void setMissData(String missData) {
		this.missData = missData;
	}


	public String getCustomerType() {
		return customerType;
	}


	public void setCustomerType(String customerType) {
		this.customerType = customerType;
	}


	public String getIndustryType() {
		return industryType;
	}



	public void setIndustryType(String industryType) {
		this.industryType = industryType;
	}


	public BigDecimal getValue() {
		return value;
	}

	public void setValue(BigDecimal value) {
		this.value = value;
	}

	public String getTime() {
		return time;
	}

	public void setTime(String time) {
		this.time = time;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getCountTotalFormatter() {
		return countTotalFormatter;
	}

	public void setCountTotalFormatter(String countTotalFormatter) {
		this.countTotalFormatter = countTotalFormatter;
	}

	public String getCountCurrentFormatter() {
		return countCurrentFormatter;
	}

	public void setCountCurrentFormatter(String countCurrentFormatter) {
		this.countCurrentFormatter = countCurrentFormatter;
	}

	public String getIdType() {
		return idType;
	}

	public void setIdType(String idType) {
		this.idType = idType;
	}
	
	public Date getTv() {
		return tv;
	}

	public void setTv(Date tv) {
		this.tv = tv;
	}
	
	public String getTvType() {
		String replace="";
		if("1".equals(tvType)) {
			replace=MutiLangUtil.assemblyI18nData("dict", "Daily");
		}else if("2".equals(tvType)) {
			replace=MutiLangUtil.assemblyI18nData("dict", "Monthly");
		}
		return replace;
	}

	public void setTvType(String tvType) {
		this.tvType = tvType;
	}
	
	public BigDecimal getPercent() {
		return percent;
	}

	public void setPercent(BigDecimal percent) {
		this.percent = percent;
	}
	
	public BigDecimal getCountCurrent() {
		return countCurrent;
	}

	public void setCountCurrent(BigDecimal countCurrent) {
		this.countCurrent = countCurrent;
	}
	
	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}
	
	public String getCountActual() {
		return countActual;
	}

	public void setCountActual(String countActual) {
		this.countActual = countActual;
	}
	
	public String getCountTotal() {
		return countTotal;
	}

	public void setCountTotal(String countTotal) {
		this.countTotal = countTotal;
	}


	public String getName() {
		return MutiLangUtil.assemblyI18nData(MutiLangUtil.DICT_I18N,name);
	}

	public void setName(String name) {
		this.name = name;
	}

	public DataStatisticsCustomer(String idType ,Date tv ,String tvType ,BigDecimal percent ,BigDecimal countCurrent 
			,String orgId ,String countActual ,String countTotal ) {
		super();
		this.idType = idType;
		this.tv = tv;
		this.tvType = tvType;
		this.percent = percent;
		this.countCurrent = countCurrent;
		this.orgId = orgId;
		this.countActual = countActual;
		this.countTotal = countTotal;
	}

}