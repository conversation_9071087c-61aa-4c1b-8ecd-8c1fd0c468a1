update DICT_PROFILE set NAME = 'Energy Profile (Minutely Curve)' where ID = '20001';
update DICT_PROFILE set NAME = 'Load Profile (Minutely Curve)' where ID = '20002';
update DICT_PROFILE set NAME = 'Energy Billing Profile (Daily)' where ID = '20003';
update DICT_PROFILE set NAME = 'Energy Billing Profile (Monthly)' where ID = '20004';
update DICT_PROFILE set NAME = 'Time Synchronization (DCU)' where ID = '20041';

truncate table DICT_FUNCTION;
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1001','Meter Data Report','meterDataReportController/list.do','Data Collection');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1002','Meter Event Report','dataMeterEventController/list.do','Data Collection');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1003','Schedule Reads Report','dataIntegrityController/scheduleReadsReport.do','Data Collection');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1004','Miss Data Tracing','dataIntegrityController/list.do','Data Collection');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1005','Collection Schedule Management','assetScheduleSchemeDetailController/list.do','Data Collection');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1006','Meter Group Management','assetMeterGroupController/meterGroupMgmt.do','Provisioning');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1007','Meter Configuration','meterConfigurationController/toMeterConfiguration.do','Provisioning');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1008','DCU Configuration - 376','dcuConfigurationController/toDcuConfiguration.do','Provisioning');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1009','Firmware Upgrade','dataFwuPlanController/list.do','Tools');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1011','On Demand Reads','assetMeterController/onDemandReads.do','Tools');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1012','Connect / Disconnect','meterConnectOrDisconnectController/toConnectOrDisconnect.do','Tools');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1013','Asset Management','assetMeterController/assetManagementList.do','Provisioning');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1014','Deployment Management','sysServerController/list.do','System');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1015','Log Explorer','sysLogController/list.do','System');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1017','Permission','sysUserController/sysUserAndRoleList.do','System');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1018','Data Export Management','sysDataitemExportController/list.do','System Integration');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1019','Reports','dictReportController/list.do','Data Management');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1020','Meter Group Upgrade','dataParameterPlanController/meterGroupUpgradeList.do','Tools');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1021','On Demand Reads - 376','assetMeterController/onDemandReads376.do','Tools');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1022','Calculation Object Define','assetCalationObjectController/assetCalculationObject.do','Data Management');

truncate table DICT_MENU;
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1e858f11beda11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',1,'Data Collection',1,null,'0',null,null,null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('265ddc0cbeda11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',1,'Tools',5,null,'0',null,null,null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('2dd2e5a5beda11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',1,'System',6,null,'0',null,null,null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('2e858f11beda11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',1,'Provisioning',3,null,'0',null,null,null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40422339bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Meter Data Report',3,'meterDataReportController/list.do','1e858f11beda11e79bb968f728c516f9','1','1001',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('404227d7bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Meter Event Report',4,'dataMeterEventController/list.do','1e858f11beda11e79bb968f728c516f9','1','1002',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40422a45bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Schedule Reads Report',1,'dataIntegrityController/scheduleReadsReport.do','1e858f11beda11e79bb968f728c516f9','1','1003',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40422c62bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Miss Data Tracing',2,'dataIntegrityController/list.do','1e858f11beda11e79bb968f728c516f9','1','1004',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40422ec4bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Collection Scheme Management',5,'assetScheduleSchemeDetailController/list.do','1e858f11beda11e79bb968f728c516f9','1','1005',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('4057793ebedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Meter Group Management',2,'assetMeterGroupController/meterGroupMgmt.do','2e858f11beda11e79bb968f728c516f9','1','1006',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40577c5abedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Meter Configuration',3,'meterConfigurationController/toMeterConfiguration.do','2e858f11beda11e79bb968f728c516f9','1','1007',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40577ff3bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Firmware Upgrade',2,'dataFwuPlanController/list.do','265ddc0cbeda11e79bb968f728c516f9','1','1009',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40578309bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'On Demand Reads',1,'assetMeterController/onDemandReads.do','265ddc0cbeda11e79bb968f728c516f9','1','1011',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('4057848ebedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Connect / Disconnect',4,'meterConnectOrDisconnectController/toConnectOrDisconnect.do','265ddc0cbeda11e79bb968f728c516f9','1','1012',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('4057860cbedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Asset Management',1,'assetMeterController/assetManagementList.do','2e858f11beda11e79bb968f728c516f9','1','1013',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40578791bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Deployment Management',3,'sysServerController/list.do','2dd2e5a5beda11e79bb968f728c516f9','1','1014',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('4057890cbedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Log Explorer',2,'sysLogController/list.do','2dd2e5a5beda11e79bb968f728c516f9','1','1015',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40578c0dbedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Permission',1,'sysUserController/sysUserAndRoleList.do','2dd2e5a5beda11e79bb968f728c516f9','1','1017',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40578a88bedc11e79bb968f728c51688','29018328bd4011e79bb968f728c516f9',2,'Data Export Management',4,'sysDataitemExportController/list.do','2dd2e5a5beda11e79bb968f728c516f9','1','1018',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('6f088b1d22b545b78aac411aa5fd2161','29018328bd4011e79bb968f728c516f9',2,'Meter Group Upgrade',3,'dataParameterPlanController/meterGroupUpgradeList.do','265ddc0cbeda11e79bb968f728c516f9','1','1020',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('2dd2e5a5beda11e79bb968f728c53333','29018328bd4011e79bb968f728c516f9',1,'Data Management',2,null,'0',null,null,null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('187c1f57d1b945c1a12fc627358b18ec','29018328bd4011e79bb968f728c516f9',2,'DCU Configuration',4,'dcuConfigurationController/toDcuConfiguration.do','2e858f11beda11e79bb968f728c516f9','1','1020',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('22222239bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'On Demand Reads -376',5,'assetMeterController/onDemandReads376.do','265ddc0cbeda11e79bb968f728c516f9','1','1011',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('2dd2e5a5beda11e79bb968f723333333','29018328bd4011e79bb968f728c516f9',2,'Calculation Object Define',1,'assetCalationObjectController/assetCalculationObject.do','2dd2e5a5beda11e79bb968f728c53333','1','1022',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('2dd2e5a5beda11e79bb9644723333333','29018328bd4011e79bb968f728c516f9',2,'Reports',22,'dictReportController/list.do','2dd2e5a5beda11e79bb968f728c53333','1','1019',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('4057860cbedc11e79bb968f728c51677','29018328bd4011e79bb968f728c516f9',2,'DCU Configuration',5,'dcuConfigurationController/toDcuConfiguration.do','2e858f11beda11e79bb968f728c516f9','1','1008',null);

truncate table SYS_ROLE_MENU;
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('40422339bedc11e79bb968f728c516f9','40422339bedc11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ecaf883ad50744b5a5317143ebab6aac','1e858f11beda11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ecaf883ad50744b5a5317143ebab6aac','40422a45bedc11e79bb968f728c516f9','1003001,1003002,1003003,1003004');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ecaf883ad50744b5a5317143ebab6aac','40422c62bedc11e79bb968f728c516f9','1004001,1004002');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ecaf883ad50744b5a5317143ebab6aac','40422339bedc11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ecaf883ad50744b5a5317143ebab6aac','404227d7bedc11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ecaf883ad50744b5a5317143ebab6aac','40422ec4bedc11e79bb968f728c516f9','1005002,1005003,1005004,1005005');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ecaf883ad50744b5a5317143ebab6aac','2dd2e5a5beda11e79bb968f728c53333',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ecaf883ad50744b5a5317143ebab6aac','2dd2e5a5beda11e79bb968f723333333','1022001,1022002,1022003,1022004,1022005,1022006');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ecaf883ad50744b5a5317143ebab6aac','2dd2e5a5beda11e79bb9644723333333',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ecaf883ad50744b5a5317143ebab6aac','2e858f11beda11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ecaf883ad50744b5a5317143ebab6aac','4057860cbedc11e79bb968f728c516f9','1013002,1013003,1013004,1013005,1013006,1013007,1013008,1013009,1013010,1013011,1013012,1013013,1013014,1013017,1013018,1013020,1013021,1013022,1013023,1013024,1013025,1013026,1013101,1013102,1013103,1013104,1013105');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ecaf883ad50744b5a5317143ebab6aac','4057793ebedc11e79bb968f728c516f9','1006001,1006002,1006003,1006004,1006005,1006006,1006007,1006008,1006009,1006010,1006011,1006012,1006013,1006014,1006015,1006016');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ecaf883ad50744b5a5317143ebab6aac','40577c5abedc11e79bb968f728c516f9','1007004,1007005');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ecaf883ad50744b5a5317143ebab6aac','265ddc0cbeda11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ecaf883ad50744b5a5317143ebab6aac','40578309bedc11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ecaf883ad50744b5a5317143ebab6aac','40577ff3bedc11e79bb968f728c516f9','1009001,1009002,1009101,1009102,1009103');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ecaf883ad50744b5a5317143ebab6aac','6f088b1d22b545b78aac411aa5fd2161','1019001,1019002,1020101,1020102,1020103');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ecaf883ad50744b5a5317143ebab6aac','4057848ebedc11e79bb968f728c516f9','1012001,1012002');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ecaf883ad50744b5a5317143ebab6aac','2dd2e5a5beda11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ecaf883ad50744b5a5317143ebab6aac','4057890cbedc11e79bb968f728c516f9','1015001,1015002,1015003,1015004');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ecaf883ad50744b5a5317143ebab6aac','40578791bedc11e79bb968f728c516f9','1014002,1014003');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ecaf883ad50744b5a5317143ebab6aac','40578a88bedc11e79bb968f728c51688','1018002');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('8dce63f10a7b4b19ac60851362fcb274','1e858f11beda11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('8dce63f10a7b4b19ac60851362fcb274','40422a45bedc11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('8dce63f10a7b4b19ac60851362fcb274','40422c62bedc11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('8dce63f10a7b4b19ac60851362fcb274','40422339bedc11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('8dce63f10a7b4b19ac60851362fcb274','404227d7bedc11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('8dce63f10a7b4b19ac60851362fcb274','40422ec4bedc11e79bb968f728c516f9','1005002,1005003,1005004,1005005');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('8dce63f10a7b4b19ac60851362fcb274','2dd2e5a5beda11e79bb968f728c53333',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('8dce63f10a7b4b19ac60851362fcb274','2dd2e5a5beda11e79bb968f723333333','1022001,1022002,1022003,1022004,1022005,1022006');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('8dce63f10a7b4b19ac60851362fcb274','2dd2e5a5beda11e79bb9644723333333',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('8dce63f10a7b4b19ac60851362fcb274','2e858f11beda11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('8dce63f10a7b4b19ac60851362fcb274','4057860cbedc11e79bb968f728c516f9','1013002,1013003,1013004,1013005,1013006,1013007,1013008,1013009,1013010,1013011,1013012,1013013,1013014,1013017,1013018,1013020,1013021,1013022,1013023,1013024,1013025,1013026,1013101,1013102,1013103,1013104,1013105');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('8dce63f10a7b4b19ac60851362fcb274','4057793ebedc11e79bb968f728c516f9','1006001,1006002,1006003,1006004,1006008,1006009,1006010,1006011,1006012,1006013,1006014,1006015,1006016');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('8dce63f10a7b4b19ac60851362fcb274','40577c5abedc11e79bb968f728c516f9','1007001,1007002,1007004,1007005');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('8dce63f10a7b4b19ac60851362fcb274','265ddc0cbeda11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('9b168c89b6864a7fba7038f59c2b1bba','1e858f11beda11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('9b168c89b6864a7fba7038f59c2b1bba','40422a45bedc11e79bb968f728c516f9','1003001,1003002,1003003,1003004');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('9b168c89b6864a7fba7038f59c2b1bba','40422c62bedc11e79bb968f728c516f9','1004001,1004002');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('9b168c89b6864a7fba7038f59c2b1bba','40422339bedc11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('9b168c89b6864a7fba7038f59c2b1bba','404227d7bedc11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('9b168c89b6864a7fba7038f59c2b1bba','40422ec4bedc11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('9b168c89b6864a7fba7038f59c2b1bba','2dd2e5a5beda11e79bb968f728c53333',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('9b168c89b6864a7fba7038f59c2b1bba','2dd2e5a5beda11e79bb968f723333333',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('9b168c89b6864a7fba7038f59c2b1bba','2dd2e5a5beda11e79bb9644723333333',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('9b168c89b6864a7fba7038f59c2b1bba','2e858f11beda11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('9b168c89b6864a7fba7038f59c2b1bba','4057860cbedc11e79bb968f728c516f9','1013101,1013102,1013103,1013104,1013105');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('9b168c89b6864a7fba7038f59c2b1bba','4057793ebedc11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('9b168c89b6864a7fba7038f59c2b1bba','40577c5abedc11e79bb968f728c516f9','1007001,1007002,1007004,1007005');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('9b168c89b6864a7fba7038f59c2b1bba','265ddc0cbeda11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('9b168c89b6864a7fba7038f59c2b1bba','40578309bedc11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('9b168c89b6864a7fba7038f59c2b1bba','40577ff3bedc11e79bb968f728c516f9','1009101,1009102,1009103');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('9b168c89b6864a7fba7038f59c2b1bba','6f088b1d22b545b78aac411aa5fd2161','1020101,1020102,1020103');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('9b168c89b6864a7fba7038f59c2b1bba','4057848ebedc11e79bb968f728c516f9','1012001,1012002');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('9b168c89b6864a7fba7038f59c2b1bba','2dd2e5a5beda11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('9b168c89b6864a7fba7038f59c2b1bba','4057890cbedc11e79bb968f728c516f9','1015001,1015002,1015003');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('6b9918d8fcba484b90015b6b58725c5e','1e858f11beda11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('6b9918d8fcba484b90015b6b58725c5e','40422a45bedc11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('6b9918d8fcba484b90015b6b58725c5e','40422c62bedc11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('6b9918d8fcba484b90015b6b58725c5e','40422339bedc11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('6b9918d8fcba484b90015b6b58725c5e','404227d7bedc11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('6b9918d8fcba484b90015b6b58725c5e','40422ec4bedc11e79bb968f728c516f9','1005002,1005003,1005004,1005005');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('6b9918d8fcba484b90015b6b58725c5e','2dd2e5a5beda11e79bb968f728c53333',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('6b9918d8fcba484b90015b6b58725c5e','2dd2e5a5beda11e79bb968f723333333',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('6b9918d8fcba484b90015b6b58725c5e','2dd2e5a5beda11e79bb9644723333333','1022001,1022002,1022003,1022004,1022005,1022006');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('6b9918d8fcba484b90015b6b58725c5e','2e858f11beda11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('6b9918d8fcba484b90015b6b58725c5e','4057860cbedc11e79bb968f728c516f9','1013002,1013003,1013004,1013005,1013006,1013007,1013008,1013010,1013014,1013101,1013102,1013103,1013104,1013105');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('6b9918d8fcba484b90015b6b58725c5e','4057793ebedc11e79bb968f728c516f9','1006001,1006002,1006003,1006004,1006005,1006006,1006007,1006008,1006009,1006010,1006011,1006012,1006013,1006014,1006015,1006016');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('6b9918d8fcba484b90015b6b58725c5e','40577c5abedc11e79bb968f728c516f9','1007001,1007002,1007004,1007005');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('6b9918d8fcba484b90015b6b58725c5e','265ddc0cbeda11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('6b9918d8fcba484b90015b6b58725c5e','40578309bedc11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('6b9918d8fcba484b90015b6b58725c5e','40577ff3bedc11e79bb968f728c516f9','1009001,1009002,1009101,1009102,1009103');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('6b9918d8fcba484b90015b6b58725c5e','6f088b1d22b545b78aac411aa5fd2161',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('6b9918d8fcba484b90015b6b58725c5e','4057848ebedc11e79bb968f728c516f9','1012001,1012002');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('6b9918d8fcba484b90015b6b58725c5e','2dd2e5a5beda11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('6b9918d8fcba484b90015b6b58725c5e','4057890cbedc11e79bb968f728c516f9','1015001,1015002,1015003');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('8dce63f10a7b4b19ac60851362fcb274','40578309bedc11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('8dce63f10a7b4b19ac60851362fcb274','40577ff3bedc11e79bb968f728c516f9','1009001,1009002,1009101,1009102,1009103');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('8dce63f10a7b4b19ac60851362fcb274','6f088b1d22b545b78aac411aa5fd2161','1019001,1019002,1020101,1020102,1020103');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('8dce63f10a7b4b19ac60851362fcb274','4057848ebedc11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('8dce63f10a7b4b19ac60851362fcb274','2dd2e5a5beda11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('8dce63f10a7b4b19ac60851362fcb274','40578c0dbedc11e79bb968f728c516f9','1017001,1017002,1017003,1017005,1017006,1017007,1017008,1017009,1017010');
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('8dce63f10a7b4b19ac60851362fcb274','4057890cbedc11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('8dce63f10a7b4b19ac60851362fcb274','40578791bedc11e79bb968f728c516f9',null);
Insert into SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('8dce63f10a7b4b19ac60851362fcb274','40578a88bedc11e79bb968f728c51688',null);

/*----客户表--*/
CREATE TABLE ASSET_CUSTOMER 
   (	
    "ID" VARCHAR2(32 BYTE), 
	"SN" VARCHAR2(32 BYTE), 
	"NAME" VARCHAR2(32 BYTE), 
	"ORG_ID" VARCHAR2(32 BYTE), 
	"CUSTOMER_TYPE" VARCHAR2(32 BYTE), 
	"METER_ID" VARCHAR2(32 BYTE), 
	"INDUSTRY_TYPE" VARCHAR2(32 BYTE), 
	"TELEPHONE_NUM" VARCHAR2(32 BYTE), 
	"ADDR" VARCHAR2(256 BYTE),
	primary key (id)
);

/*----客户行业字典表--*/
CREATE TABLE DICT_CUSTOMER_INDUSTRY
(	"ID" VARCHAR2(32 BYTE), 
	"NAME" VARCHAR2(32 BYTE), 
	"CODE" VARCHAR2(64 BYTE), 
	"INTRODUCTION" VARCHAR2(64 BYTE),
	primary key (id)
);

Insert into DICT_CUSTOMER_INDUSTRY (ID,NAME,CODE,INTRODUCTION) values ('1','Production','001',null);
Insert into DICT_CUSTOMER_INDUSTRY (ID,NAME,CODE,INTRODUCTION) values ('2','Farming','002',null);
Insert into DICT_CUSTOMER_INDUSTRY (ID,NAME,CODE,INTRODUCTION) values ('3','Animal Husbandry','003',null);
Insert into DICT_CUSTOMER_INDUSTRY (ID,NAME,CODE,INTRODUCTION) values ('4','Fishery','004',null);
Insert into DICT_CUSTOMER_INDUSTRY (ID,NAME,CODE,INTRODUCTION) values ('5','Mining','005',null);
Insert into DICT_CUSTOMER_INDUSTRY (ID,NAME,CODE,INTRODUCTION) values ('6','Lodging '||'&'||' Catering','006',null);
Insert into DICT_CUSTOMER_INDUSTRY (ID,NAME,CODE,INTRODUCTION) values ('0','Residential','000',null);

/*----客户行业字典表--*/
CREATE TABLE DICT_CUSTOMER_TYPE
(	"ID" VARCHAR2(32 BYTE), 
	"NAME" VARCHAR2(32 BYTE), 
	"INTRODUCTION" VARCHAR2(64 BYTE),
	primary key (id)
);

Insert into DICT_CUSTOMER_TYPE (ID,NAME,INTRODUCTION) values ('1','Commerce and Industry',null);
Insert into DICT_CUSTOMER_TYPE (ID,NAME,INTRODUCTION) values ('2','Residential',null);

truncate table DICT_SERVICE_ATTRIBUTE;
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (4,'UCI.DittPort','Ditt proxy listen port','Integer','9999',null,null,null,5);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (3,'Schedule.TimeSynchronizationRange','Time synchronization range (Unit: Second)','Integer','180','180','600',null,45);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Export.StartTime','Meter data/event export start time','Date','03:32:00',null,null,'HH:mm:ss',3);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Export.Cycle','Meter data/event export cycle (Unit: Hour)','Integer','4','3','12',null,4);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Data.StorageCycle','Meter data/event storage cycle (Unit: Month)','Integer','12','3','65535',null,5);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (2,'MQBus.IP','Message bus server IP address','String','127.0.0.1','1','1',null,1);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (2,'MQBus.Port','Message bus server listen port','Integer','61616','0','65535',null,2);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (1,'Channel.AssetReloadCycle','Service reload asset from database (Unit: Hour)','Integer','12','6','65535',null,3);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (1,'Channel.DLMS.ListenPort','DLMS meter/DCU listen port','Integer','9800','0','65535',null,1);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (1,'Channel.Hall.ListenPort','Hall service listen port','Integer','0','0','65535',null,2);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (3,'Schedule.AssetReloadCycle','Service reload asset from database (Unit: Hour)','Integer','12','6','65535',null,4);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (3,'Schedule.RequestRetryTimes','The times of resending request after timeout','Integer','0','1','2',null,3);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (3,'Schedule.RequestTimeout','Request timeout (Unit: Second)','Integer','60','30','120',null,2);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (3,'Schedule.Concurrency','The count of concurrency task','Integer','5000','1000','50000',null,1);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (4,'UCI.AssetReloadCycle','Service reload asset from database (Unit: Hour)','Integer','12','6','65535',null,4);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (4,'UCI.RequestRetryTimes','The times of resending request after timeout','Integer','0','1','3',null,3);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (4,'UCI.RequestTimeout','Request timeout (Unit: Second)','Integer','60','30','120',null,2);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (4,'UCI.Concurrency','The count of concurrency task','Integer','1000','1000','50000',null,1);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Task.Export','If execute export task','Enum','Enable',null,null,'Enable;Disable',1);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Export.FtpUrl','Meter data/event export FTP server URL','String','ftp://127.0.0.1/ExportData',null,null,null,2);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (6,'Application.UCI.Interface.Url','UCI interface service URL','String','http://127.0.0.1:8080/UCI-1.0-SNAPSHOT',null,null,null,1);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (1,'Channel.SGPort','SG376 protocol server listen port','Integer','9999',null,null,null,4);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Integrity.StartTime','Schedule calculation task start time','Date','00:05:00',null,null,'HH:mm:ss',8);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Integrity.Cycle','Schedule calculation task cycle (Unit: Hour)','Integer','2',null,null,null,9);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Task.CalculationObject','If execute calculation object task','Enum','Enable',null,null,'Enable;Disable',6);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Task.SalesStatistic','If execute power sales statistic task','Enum','Enable',null,null,'Enable;Disable',7);

truncate table DICT_OPERATION;
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1005002','Add/Edit scheme','1005','/assetScheduleSchemeController/assetScheduleScheme.do',0,'Add/Edit scheme');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006005','Add TOU group','1006','/assetMeterGroupController/assetMeterGroup.do?type=2',0,'Add TOU group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006006','Add Measurement group','1006','/assetMeterGroupController/toMeasurementGroup.do?type=1',0,'Add Measurement group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006007','Add Limiter group','1006','/assetMeterGroupController/assetMeterGroup.do?type=3',0,'Add Limiter group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1014002','Add/Edit server/service','1014','/sysServerController/toAddSysServer.do',0,'Add/Edit server or service');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1014003','Delete server/service','1014','/sysServerController/delServer.do',0,'Delete server or service');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006008','Delete TOU group','1006','deleteTouGroup',0,'Delete TOU group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006009','Delete Limiter group','1006','deleteLimiterGroup',0,'Delete Limiter group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006010','Delete Measurement group','1006','deleteMeasurementGroup',0,'Delete Measurement group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1009001','Add Plan','1009','/dataFwuPlanController/saveFWUPlan.do',0,'Add plan in Plan Tab');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1018002','Save Data '||'&'||' Event','1018','/sysDataitemExportController/saveMeterDataEventExport.do',0,'Save meter data and events to be exported');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017005','Add/Edit user','1017','/sysUserController/toAddSysUser.do',0,'Add user; Edit user; Reset password');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1005003','Delete scheme','1005','/assetScheduleSchemeController/del.do',0,'Delete scheme');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1005004','Add/Edit task','1005','/assetScheduleSchemeDetailController/assetScheduleSchemeDetail.do',0,'Add/Edit task');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1005005','Delete task','1005','/assetScheduleSchemeDetailController/del.do',0,'Delete task');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013202','Edit '||'&'||' Save Meter','1013','/assetMeterController/save.do',0,'Edit '||'&'||' Save Meter');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013203','Delete Meter','1013','/assetMeterController/del.do',0,'Delete Meter');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013204','Add Meter','1013','/assetMeterController/addAssetDevice.do',0,'Add Meter');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1007004','Excute Task in Set Page','1007','/meterConfigurationController/excuteTask.do',0,'Add '||'&'||' execute task in Set Tab');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1007005','Read data in Get Tab','1007','/meterConfigurationController/getTouGroupData.do',0,'Read data in Get Tab');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017007','Add/Edit role','1017','/sysRoleController/toAddSysRole.do',0,'Add/Edit role');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017008','Delete role','1017','/sysRoleController/deleteRole.do',0,'Delete role');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017006','Delete user','1017','/sysUserController/del.do',0,'Delete user');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017009','Add/Edit/Remove organization','1017','/sysOrgController/toAddSysOrg.do',0,'Add, edit or remove organization');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017010','Delete organization','1017','/sysOrgController/del.do',0,'Delete organization');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1009002','Cancel Plan/Job','1009','/dataFwuPlanController/cancel.do',0,'Cancel plan/job in Plan Report Tab; Cancel job in Job Report Tab');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1019001','Add Plan','1020','/dataParameterPlanController/saveMeterGroupUpgradePlan.do',0,'Add plan in Plan/Plan Report Tab');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1019002','Cancel Plan','1020','/dataParameterPlanController/cancelPlan.do',0,'Cancel plan in Plan Report Tab');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006011','Delete Step Tariff group','1006','deleteStepTariffGroup',0,'Delete Step Tariff group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006012','Add Step Tariff group','1006','/assetMeterGroupController/addStepTariffGroup.do?type=4',0,'Add Step Tariff group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013235','Add Calculation Object to Line','1013','AddCalculationObjectToLine',0,'Add Calculation Object to Line');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013255','Add Calculation Object to Transformer','1013','AddCalculationObjectToTransformer',0,'Add Calculation Object to Transformer');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013243','Add Calculation Object to Organization','1013','AddCalculationObjectToOrg',0,'Add Calculation Object to Organization');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1022001','Add Calculation Object','1022','AddCalculationObject',0,'Add Calculation Object');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1022002','Edit Calculation Object','1022','EditCalculationObject',0,'Edit Calculation Object');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1022003','Delete Calculation Object','1022','DeleteCalculationObject',0,'Delete Calculation Object');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1022004','Add Data Channnel in Calculation Object','1022','AddDataChannelInCalcObj',0,'Add Data Channnel in Calculation Object');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1022005','Edit Data Channnel in Calculation Object','1022','EditDataChannelInCalcObj',0,'Edit Data Channnel in Calculation Object');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1022006','Delete Data Channnel in Calculation Object','1022','DelDataChannelInCalcObj',0,'Delete Data Channnel in Calculation Object');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013201','Meter Tab Page','1013','ViewMeterManagementPage',0,'View Meter Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013211','Communicator Tab Page','1013','ViewCommunicatorManagementPage',0,'View Communicator Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013231','Line Tab Page','1013','ViewLineManagementPage',0,'View Line Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013251','Transformer Tab Page','1013','ViewTransformerManagementPage',0,'View Transformer Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013241','Organization Tab Page','1013','ViewOrganizationManagementPage',0,'View Organization Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013212','Edit '||'&'||' Save Commnicator','1013','EditAndSaveCommunicator',0,'Edit '||'&'||' Save Commnicator');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013213','Delete Commnicator','1013','DeleteCommunicator',0,'Delete Commnicator');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013214','Add Commnicator','1013','AddCommunicator',0,'Add Commnicator');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013232','Edit '||'&'||' Save Line','1013','EditAndSaveLine',0,'Edit '||'&'||' Save Line');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013233','Delete Line','1013','DeleteLine',0,'Delete Line');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013234','Add Line','1013','AddLine',0,'Add Line');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013252','Edit '||'&'||' Save Transformer','1013','EditAndSaveTransformer',0,'Edit '||'&'||' Save Transformer');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013253','Delete Transformer','1013','DeleteTransformer',0,'Delete Transformer');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013254','Add Transformer','1013','AddTransformer',0,'Add Transformer');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013242','Edit '||'&'||' Save Organization','1013','EditAndSaveOrganization',0,'Edit '||'&'||' Save Organization');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006001','TOU Tab Page','1006','ViewTouManagementPage',0,'View TOU Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006002','Measurement Tab Page','1006','ViewMeasurementManagementPage',0,'View Measurement Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006003','Limiter Tab Page','1006','ViewLimiterManagementPage',0,'View Limiter Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006004','Step Tariff Tab Page','1006','ViewStepTariffManagementPage',0,'View Step Tariff Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006013','Edit '||'&'||' Save TOU group','1006','EditAndSaveTouGroup',0,'Edit '||'&'||' Save TOU group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006014','Edit '||'&'||' Save Measurement group','1006','EditAndSaveMeasurementGroup',0,'Edit '||'&'||' Save Measurement group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006015','Edit '||'&'||' Save Limiter group','1006','EditAndSaveLimiterGroup',0,'Edit '||'&'||' Save Limiter group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006016','Edit '||'&'||' Save Step Tariff group','1006','EditSaveStepTariffGroup',0,'Edit '||'&'||' Save Step Tariff group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1007001','View Set Tab Page','1007','ViewSetTabPage',0,'View Set Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1007002','View Get Tab Page','1007','ViewGetTabPage',0,'View Get Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1009101','View Plan Tab Page','1009','FirmViewPlanTabPage',0,'View Plan Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1009102','View Plan Report Tab Page','1009','FirmViewPlanReportTabPage',0,'View Plan Report Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1009103','View Job Report Tab Page','1009','FirmViewJobReportTabPage',0,'View Job Report Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1020101','View Plan Tab Page','1020','ViewPlanTabPage',0,'View Plan Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1020102','View Plan Report Tab Page','1020','ViewPlanReportTabPage',0,'View Plan Report Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1020103','View Job Report Tab Page','1020','ViewJobReportTabPage',0,'View Job Report Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1012001','Connect','1012','ExecuteConnectCommandToMeter',0,'Execute a Connect Command to Meter');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1012002','Disconnect','1012','ExecuteDisConnectCommandToMeter',0,'Execute a Disconnect Command to Meter');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017001','View User tab page','1017','ViewUserTabPage',0,'View User tab page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017002','View Role tab page','1017','ViewRoleTabPage',0,'View Role tab page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017003','View Organization tab page','1017','ViewOrganizationTabPage',0,'View Organization tab page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1015001','View Metering Tracing Log tab page','1015','ViewMeteringTracingLogTabPage',0,'View Metering Tracing Log tab page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1015002','View Time Synchronization Log tab page','1015','ViewTimeSynchronizationLogTabPage',0,'View Time Synchronization Log tab page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1015003','View System Integration Log tab page','1015','ViewSystemIntegrationLogTabPage',0,'View System Integration Log tab page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1015004','View User Log tab page','1015','ViewUserLogTabPage',0,'View User Log tab page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013221','Customer Tab Page','1013','ViewCustomerTabPage',0,'View Customer Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013222','Edit '||'&'||' Save Customer','1013','EditSaveCustomer',0,'Edit '||'&'||' Save Customer');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013223','Delete Customer','1013','DeleteCustomer',0,'Delete Customer');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013224','Add Customer','1013','AddCustomer',0,'Add Customer');


CREATE TABLE DATA_USER_EVENT_PROGRESS
(	
	"USER_ID" VARCHAR2(32 BYTE), 
	"PROGRESS" DATE,
	primary key (USER_ID)
);

CREATE TABLE DATA_COMMINICATION_STATUS
(
	"COMMUNICATOR_ID" VARCHAR2(32 BYTE), 
	"UPDATE_TV" DATE, 
	"IP_ADDR" VARCHAR2(32 BYTE), 
	"IP_PORT" NUMBER, 
	"COM_STATUS" NUMBER DEFAULT 0,
	primary key (COMMUNICATOR_ID)
);

CREATE TABLE DATA_SHIPMENT_METER
(
	"SN" VARCHAR2(32 BYTE), 
	"LDN" VARCHAR2(32 BYTE), 
	"STS_SN" VARCHAR2(32 BYTE), 
	"PASSWORD" VARCHAR2(32 BYTE), 
	"HLS_AK" VARCHAR2(32 BYTE), 
	"HLS_EK" VARCHAR2(32 BYTE), 
	"IS_ENCRYPT" VARCHAR2(4 BYTE), 
	"AUTH_TYPE" VARCHAR2(4 BYTE),
	primary key (SN)
);

CREATE TABLE DATA_SHIPMENT_SIMCARD
(
	"SIM_NUM" VARCHAR2(32 BYTE), 
	"COMMUNICATOR_SN" VARCHAR2(32 BYTE), 
	"IP_ADDR" VARCHAR2(20 BYTE),
	primary key (SIM_NUM)
);

truncate table DICT_REPORT;
Insert into DICT_REPORT (ID,REPORTNAME,SORT_ID,FUNCTIONURL,PARENT_ID) values ('100001','Cusomer Billing Report',1,'dictReportController/billingReportList.do','100');
Insert into DICT_REPORT (ID,REPORTNAME,SORT_ID,FUNCTIONURL,PARENT_ID) values ('100002','Line Loss Report',2,'dictReportController/lineLossSingleObjectList.do','100');
Insert into DICT_REPORT (ID,REPORTNAME,SORT_ID,FUNCTIONURL,PARENT_ID) values ('100','Reports',1,null,'0');
Insert into DICT_REPORT (ID,REPORTNAME,SORT_ID,FUNCTIONURL,PARENT_ID) values ('100003','Supply Statistics Report',3,'dictReportController/importAndExportReport.do','100');
Insert into DICT_REPORT (ID,REPORTNAME,SORT_ID,FUNCTIONURL,PARENT_ID) values ('100004','Sales Statistics Report',3,'dictReportController/importAndExportReport.do','100');

Insert into DICT_PROTOCOL (ID,NAME,INTRODUCTION) values ('300','CSG','China Southern Power Grid Standard');