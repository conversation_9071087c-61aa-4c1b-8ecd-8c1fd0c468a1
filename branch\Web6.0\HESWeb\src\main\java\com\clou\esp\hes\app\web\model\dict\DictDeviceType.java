/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictDeviceModel{ } 
 * 
 * 摘    要： 设备类型型号
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-18 02:59:36
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.dict;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DictDeviceType  extends BaseEntity {

    private static final long serialVersionUID = 1L;
    
	public DictDeviceType() {
	}
	private java.lang.String id;
	
	private java.lang.String name;

	public java.lang.String getId() {
		return id;
	}

	public void setId(java.lang.String id) {
		this.id = id;
	}

	public java.lang.String getName() {
		return name;
	}

	public void setName(java.lang.String name) {
		this.name = name;
	}
	
	
}