package com.clou.esp.hes.app.web.controller.asset;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.OrganizationUtils;
import com.clou.esp.hes.app.web.model.asset.AssetCalcObj;
import com.clou.esp.hes.app.web.model.asset.AssetCalcObjMap;
import com.clou.esp.hes.app.web.model.asset.AssetTransformer;
import com.clou.esp.hes.app.web.model.system.SysOrg;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.asset.AssetLineManagementService;
import com.clou.esp.hes.app.web.service.asset.AssetTransformerService;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.system.SysOrgService;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;


/**
 * @ClassName: AssetTransformerController
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年9月20日 上午8:59:14
 *
 */
@Controller
@RequestMapping("/assetTransformerController")
public class AssetTransformerController extends BaseController {
	@Resource
	private AssetTransformerService 	assetTransformerService;
	@Resource
	AssetLineManagementService 			assetLineManagementService;
	@Resource
	private SysOrgService 				sysOrgService;
	@Resource
	private DataUserLogService 			dataUserLogService;
	
	@RequestMapping(value  = "assetTransformer")
    public ModelAndView assetTransformer(HttpServletRequest request, Model model,String id) {
		if(StringUtil.isEmpty(id)) {
			// 新增
		} else {
			// 更新
			AssetTransformer transformer = new AssetTransformer();
			transformer.setId(id);
			transformer = assetTransformerService.get(transformer);
			model.addAttribute("transformer", transformer);
		}
		return new ModelAndView("/asset/assetTransformerEdit");
	}
	
	/**
	 * 
	 * @Title: findTransformerForJqGrid
	 * @Description: 分页查询变压器数据
	 * @param jqGridSearchTo
	 * @param request
	 * @param sn
	 * @param name
	 * @param flag
	 * @return
	 * @return JqGridResponseTo
	 * @throws
	 */
	@RequestMapping(value = "findTransformerForJqGrid")
    @ResponseBody
    public JqGridResponseTo findTransformerForJqGrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request,String sn,String name,boolean flag,String orgId) {
		JqGridResponseTo response = null;
		if(!flag) {
			response = new JqGridResponseTo();
			return response;
		}
		
		jqGridSearchTo.put("sn", sn);
		jqGridSearchTo.put("name", name);
		List<String> orgIdList = OrganizationUtils.getOrgIds(sysOrgService, orgId);
		jqGridSearchTo.put("orgIds", orgIdList);
		response = assetTransformerService.getForJqGrid(jqGridSearchTo);
		return response;
	}
	
	@Transactional
	@RequestMapping(value = "save")
	@ResponseBody
	public AjaxJson save( HttpServletRequest request,AssetTransformer former) {
		AjaxJson j = new AjaxJson();
		SysUser su=TokenManager.getToken();
		try {
			if(StringUtil.isEmpty(former.getId())){
				assetTransformerService.save(former);
				dataUserLogService.insertDataUserLog(su.getId(), "Asset Managerment", "Add Transformer","Add Transformer(SN="+ former.getSn()+",Name="+ (StringUtils.isNotEmpty(former.getName())?former.getName():"")+")");
				j.setMsg(MutiLangUtil.doMutiLang("system.addSucc"));
			} else {
				assetTransformerService.update(former);
				dataUserLogService.insertDataUserLog(su.getId(), "Asset Managerment", "Edit Transformer","Edit Transformer(SN="+ former.getSn()+",Name="+ (StringUtils.isNotEmpty(former.getName())?former.getName():"")+")");
				j.setMsg(MutiLangUtil.doMutiLang("system.updateSucc"));
			}
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			j.setSuccess(false);
			j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
			return j;
		}
		
		return j;
	}
	
	@Transactional
	@RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(AssetTransformer former, HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        try {
        	SysUser su=TokenManager.getToken();
        	former=this.assetTransformerService.get(former);
        	// 查询此线路所有计算对象
        	JqGridSearchTo jqGridSearchTo = new JqGridSearchTo();
        	jqGridSearchTo.put("entityId", former.getId());
    		jqGridSearchTo.put("entityType", 4);
        	List<AssetCalcObj> calcs = assetLineManagementService.findCalObjectsForList(jqGridSearchTo);
        	
        	// 删除计算映射表里边的所有数据
        	for (AssetCalcObj assetCalcObj : calcs) {
        		tr_deleteCalObjMapByEntity(assetCalcObj);
			}
        	
        	// 删除计算对象
        	AssetCalcObj calcEntity = new AssetCalcObj();
        	calcEntity.setEntityId(former.getId());
        	assetLineManagementService.deleteCalcObjByEntityId(calcEntity);
        	
        	// 删除变压器
        	assetTransformerService.deleteById(former.getId());
        	dataUserLogService.insertDataUserLog(su.getId(), "Asset Managerment", "Delete Transformer","Delete Transformer(SN="+ former.getSn()+",Name="+ (StringUtils.isNotEmpty(former.getName())?former.getName():"")+")");
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
        	j.setSuccess(false);
        	j.setMsg(MutiLangUtil.doMutiLang("system.delFail"));
		}
        j.setMsg(MutiLangUtil.doMutiLang("system.delSuccess"));
        return j;
	}
	
	private void tr_deleteCalObjMapByEntity(AssetCalcObj assetCalcObj) {
		// TODO Auto-generated method stub
		AssetCalcObjMap map = new AssetCalcObjMap();
		map.setId(assetCalcObj.getId());
		
		assetLineManagementService.deleteCalcObjMap(map);
	}
	
	/**
	 * AssetCalcObj 增删改查
	 */
	@RequestMapping(value = "findCalObjForJqGrid")
    @ResponseBody
    public JqGridResponseTo findCalObjForJqGrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request,String id,boolean calcFlag) {
		JqGridResponseTo response = null;
		if(!calcFlag) {
			response = new JqGridResponseTo();
			return response;
		}
		
		jqGridSearchTo.put("entityId", id);
		jqGridSearchTo.put("entityType", 4);
		response = assetLineManagementService.findCalObjectsForJqGrid(jqGridSearchTo);
		return response;
	}
	
	@RequestMapping(value  = "assetCalcManagement")
    public ModelAndView assetLineManagement(HttpServletRequest request, Model model,AssetCalcObj obj) {
		String typeReplace = "1:Line Losss,2:Import,3:Export";
		model.addAttribute("typeReplace", typeReplace);
		
		String tvTypeReplace = "1:Daily,2:Monthly";
		model.addAttribute("tvTypeReplace", tvTypeReplace);
		
		if(StringUtil.isEmpty(obj.getId())) {
			//obj.setEntityType(3);
		} else {
			obj = assetLineManagementService.getCalcObj(obj);
		}
		model.addAttribute("assetCalcObj", obj);
		
		return new ModelAndView("/asset/assetCalcManagementEdit");
	}
	
	
}
