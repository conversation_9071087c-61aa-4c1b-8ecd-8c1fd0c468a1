/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictVeeRuleType{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-12-19 07:31:50
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.vee;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.vee.DictVeeRuleTypeDao;
import com.clou.esp.hes.app.web.model.vee.DictVeeRuleType;
import com.clou.esp.hes.app.web.service.vee.DictVeeRuleTypeService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dictVeeRuleTypeService")
public class DictVeeRuleTypeServiceImpl  extends CommonServiceImpl<DictVeeRuleType>  implements DictVeeRuleTypeService {

	@Resource
	private DictVeeRuleTypeDao dictVeeRuleTypeDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dictVeeRuleTypeDao);
    }
	@SuppressWarnings("rawtypes")
	public DictVeeRuleTypeServiceImpl() {}
	
	
}