/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataComminicationStatus{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：赵久霆
 * 创建于：2018-11-19 07:34:34
 * 最后修改时间：
 * 
*********************************************************************************************************/

package com.clou.esp.hes.app.web.service.data;

import com.clou.esp.hes.app.web.service.common.CommonService;

import java.util.List;
import java.util.Map;

import com.clou.esp.hes.app.web.model.data.DataComminicationStatus;

public interface DataComminicationStatusService extends CommonService<DataComminicationStatus>{
	
	//根据集中器sn获取集中器状态
	public List<DataComminicationStatus>  getCommStatus(List<String> sns);
	public List<DataComminicationStatus> getListData(Map<String,Object> map);
	
}