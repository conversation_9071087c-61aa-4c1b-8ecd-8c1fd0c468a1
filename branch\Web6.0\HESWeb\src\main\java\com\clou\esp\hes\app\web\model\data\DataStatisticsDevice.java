/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataStatisticsDevice{ } 
 * 
 * 摘    要： dataStatisticsDevice
 * 版    本：1.0
 * 作    者：赵久霆
 * 创建于：2018-09-19 07:28:24
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.data;

import java.math.BigDecimal;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DataStatisticsDevice  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DataStatisticsDevice() {
	}

	/**idType*/
	private java.lang.String idType;
	/**tv*/
	private java.util.Date tv;
	/**percent*/
	private BigDecimal percent;
	/**countCurrent*/
	private BigDecimal countCurrent;
	/**countCurrent*/
	private String countCurrentFormatter;
	/**tvType*/
	private java.lang.String tvType;
	/**name*/
	private java.lang.String name;
	/** orgId */
	private java.lang.String orgId;
	/**
	 * idType
	 * @return the value of DATA_STATISTICS_DEVICE.ID_TYPE
	 * @mbggenerated 2018-09-19 07:28:24
	 */
	public java.lang.String getIdType() {
		return idType;
	}

	/**
	 * idType
	 * @param idType the value for DATA_STATISTICS_DEVICE.ID_TYPE
	 * @mbggenerated 2018-09-19 07:28:24
	 */
    	public void setIdType(java.lang.String idType) {
		this.idType = idType;
	}
    	
    	
    	
	public java.lang.String getOrgId() {
		return orgId;
	}

	public void setOrgId(java.lang.String orgId) {
		this.orgId = orgId;
	}

	/**
	 * tv
	 * @return the value of DATA_STATISTICS_DEVICE.TV
	 * @mbggenerated 2018-09-19 07:28:24
	 */
	public java.util.Date getTv() {
		return tv;
	}

	/**
	 * tv
	 * @param tv the value for DATA_STATISTICS_DEVICE.TV
	 * @mbggenerated 2018-09-19 07:28:24
	 */
    	public void setTv(java.util.Date tv) {
		this.tv = tv;
	}
	/**
	 * percent
	 * @return the value of DATA_STATISTICS_DEVICE.PERCENT
	 * @mbggenerated 2018-09-19 07:28:24
	 */
	public BigDecimal getPercent() {
		return percent;
	}

	/**
	 * percent
	 * @param percent the value for DATA_STATISTICS_DEVICE.PERCENT
	 * @mbggenerated 2018-09-19 07:28:24
	 */
    	public void setPercent(BigDecimal percent) {
		this.percent = percent;
	}
	/**
	 * countCurrent
	 * @return the value of DATA_STATISTICS_DEVICE.COUNT_CURRENT
	 * @mbggenerated 2018-09-19 07:28:24
	 */
	public BigDecimal getCountCurrent() {
		return countCurrent;
	}

	/**
	 * countCurrent
	 * @param countCurrent the value for DATA_STATISTICS_DEVICE.COUNT_CURRENT
	 * @mbggenerated 2018-09-19 07:28:24
	 */
    	public void setCountCurrent(BigDecimal countCurrent) {
		this.countCurrent = countCurrent;
	}
	/**
	 * tvType
	 * @return the value of DATA_STATISTICS_DEVICE.TV_TYPE
	 * @mbggenerated 2018-09-19 07:28:24
	 */
	public java.lang.String getTvType() {
		return tvType;
	}

	/**
	 * tvType
	 * @param tvType the value for DATA_STATISTICS_DEVICE.TV_TYPE
	 * @mbggenerated 2018-09-19 07:28:24
	 */
    	public void setTvType(java.lang.String tvType) {
		this.tvType = tvType;
	}
    	
    	

	public java.lang.String getName() {
		return name;
	}

	public void setName(java.lang.String name) {
		this.name = name;
	}
	
	

	public String getCountCurrentFormatter() {
		return countCurrentFormatter;
	}

	public void setCountCurrentFormatter(String countCurrentFormatter) {
		this.countCurrentFormatter = countCurrentFormatter;
	}

	public DataStatisticsDevice(java.lang.String idType 
	,java.util.Date tv 
	,BigDecimal percent 
	,BigDecimal countCurrent 
	,java.lang.String tvType ) {
		super();
		this.idType = idType;
		this.tv = tv;
		this.percent = percent;
		this.countCurrent = countCurrent;
		this.tvType = tvType;
	}

}