/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeterGroup{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-01-16 01:58:30
 * 最后修改时间：
 * 
*********************************************************************************************************/

package com.clou.esp.hes.app.web.service.asset;

import com.clou.esp.hes.app.web.service.common.CommonService;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroup;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

public interface AssetMeterGroupService extends CommonService<AssetMeterGroup>{
	
	/**
	 * 根据电表sn号查询电表分组信息
	 * @param sn
	 * @return
	 * Wangjiale
	 * 2018年1月24日 下午4:00:47
	 */
	AssetMeterGroup selectGroupNameByMeterSN(String sn, String channelId);
	
	/**
	 * 阶梯汇率 Step tariff 查询分页
	 * @param JqGridSearchTo
	 * @return String
	 */
	JqGridResponseTo datagridStepTariff(JqGridSearchTo jqGridSearchTo);
	
}