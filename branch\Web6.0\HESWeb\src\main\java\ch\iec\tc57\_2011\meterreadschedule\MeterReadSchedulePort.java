package ch.iec.tc57._2011.meterreadschedule;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.bind.annotation.XmlSeeAlso;

/**
 * This class was generated by Apache CXF 3.1.1
 * 2018-02-01T09:54:34.760+08:00
 * Generated source version: 3.1.1
 * 
 */
@WebService(targetNamespace = "http://iec.ch/TC57/2011/MeterReadSchedule", name = "MeterReadSchedule_Port")
@XmlSeeAlso({ch.iec.tc57._2011.schema.message.ObjectFactory.class, ch.iec.tc57._2011.meterreadschedule_.ObjectFactory.class, ch.iec.tc57._2011.meterreadschedulemessage.ObjectFactory.class})
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface MeterReadSchedulePort {

    @WebResult(name = "MeterReadScheduleResponseMessage", targetNamespace = "http://www.iec.ch/TC57/2011/MeterReadScheduleMessage", partName = "ResponseMessage")
    @WebMethod(operationName = "GetMeterReadSchedule", action = "http://iec.ch/TC57/2011/MeterReadSchedule/MeterReadSchedule")
    public ch.iec.tc57._2011.meterreadschedulemessage.MeterReadScheduleResponseMessageType getMeterReadSchedule(
        @WebParam(partName = "GetMeterReadSchedule", name = "CreateMeterReadSchedule", targetNamespace = "http://www.iec.ch/TC57/2011/MeterReadScheduleMessage")
        ch.iec.tc57._2011.meterreadschedulemessage.MeterReadScheduleRequestMessageType getMeterReadSchedule
    ) throws FaultMessage;
}
