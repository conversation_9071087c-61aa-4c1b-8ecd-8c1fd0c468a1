/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetReportTemplate{ } 
 * 
 * 摘    要： assetReportTemplate
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2019-03-22 01:50:10
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.report;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.clou.esp.hes.app.web.core.util.Globals;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.model.report.AssetReportRoleMap;
import com.clou.esp.hes.app.web.model.system.SysRole;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.report.AssetReportRoleMapService;
import com.clou.esp.hes.app.web.service.system.SysRoleService;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.OrganizationUtils;
import com.clou.esp.hes.app.web.core.util.ReadReportFile;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.model.report.AssetReportTemplate;
import com.clou.esp.hes.app.web.service.report.AssetReportTemplateService;
import com.clou.esp.hes.app.web.service.system.SysOrgService;
import com.clou.esp.hes.app.web.service.vee.MdmDictDetailService;
import com.clou.esp.hes.app.web.service.system.MdmSysRoleService;
/**
 * <AUTHOR>
 * @时间：2019-03-22 01:50:10
 * @描述：assetReportTemplate类
 */
@Controller
@RequestMapping("/assetReportTemplateController")
public class AssetReportTemplateController extends BaseController{


	final static String HESWEB_SYSTEM_NAME =  ResourceUtil.getSessionattachmenttitle("hesweb.system.name");
	@Resource
	private AssetReportTemplateService assetReportTemplateService;
	@Resource
	private SysOrgService  sysOrgService;
	@Resource
	private AssetReportRoleMapService assetReportRoleMapService;


	@Resource
	private SysRoleService sysRoleService;
	@Resource
	private MdmDictDetailService mdmDictDetailService;
	@Resource
	private MdmSysRoleService  mdmSysRoleService;
	@Resource
	private DataUserLogService dataUserLogService;
	/**
	 * 跳转到assetReportTemplate列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/report/assetReportTemplateList");
    }

	/**
	 * 跳转到assetReportTemplate新增界面
	 *
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "assetReportTemplate")
	public ModelAndView assetReportTemplate(AssetReportTemplate assetReportTemplate,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(assetReportTemplate.getId())){
			try {
				assetReportTemplate=assetReportTemplateService.getEntity(assetReportTemplate.getId());
			}
			catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			AssetReportRoleMap assetReportRoleMap = new AssetReportRoleMap();
			assetReportRoleMap.setReportId(assetReportTemplate.getId());

			List<AssetReportRoleMap> assetReportRoleMapList = assetReportRoleMapService.getList(assetReportRoleMap);
			StringBuffer buf = new StringBuffer();
			for(AssetReportRoleMap assetReportRoleMap1 : assetReportRoleMapList){
				buf.append(assetReportRoleMap1.getRoleId()).append(",");
			}

			if (StringUtils.isNotEmpty(buf.toString())) {
				model.addAttribute("roleId", buf.replace(buf.length() - 1, buf.length(), ""));
			} else {
				model.addAttribute("roleId", "");
			}

		}

		model.addAttribute("assetReportTemplate", assetReportTemplate);

		List<AssetReportTemplate> fileNames = ReadReportFile.getFileList();

		List<AssetReportTemplate> newFileNames = new ArrayList<>();
		for(AssetReportTemplate assetReportTemplate1 : fileNames){
			AssetReportTemplate assetReportTemplate2 = new AssetReportTemplate();
			assetReportTemplate2.setTemplateFile(assetReportTemplate1.getReportName());
			List<AssetReportTemplate> newFileNames1 = assetReportTemplateService.getList(assetReportTemplate2);
			if(newFileNames1 != null && newFileNames1.size() > 0){

			}else{
				newFileNames.add(assetReportTemplate1);
			}
		}

		if(assetReportTemplate != null && StringUtils.isNotEmpty(assetReportTemplate.getTemplateFile())){
			AssetReportTemplate assetReportTemplate3 = new AssetReportTemplate();
			assetReportTemplate3.setId(assetReportTemplate.getTemplateFile());
			assetReportTemplate3.setReportName(assetReportTemplate.getTemplateFile());
			newFileNames.add(assetReportTemplate3);
		}

		String assetReportTemplateReplace = RoletoJson.listToReplaceStr(newFileNames, "id", "reportName");
		model.addAttribute("assetReportTemplateReplace", assetReportTemplateReplace);

		// condGroupReplace
		model.addAttribute("condGroupReplace", this.mdmDictDetailService.getDictReplace("1140"));

		String  tvTypeReplace = "1:Daily,2:Monthly";
		model.addAttribute("reportTypeReplace", tvTypeReplace);

		String  isEnableReplace = "0:Disabled,1:Enabled";
		model.addAttribute("isEnableReplace", isEnableReplace);

		List<SysRole> roleList = new ArrayList<>();

		if(HESWEB_SYSTEM_NAME.equals(Globals.MDM)){
			roleList = mdmSysRoleService.getAllList();
		}else{
			roleList = sysRoleService.getAllList();
		}
		String roleReplace = RoletoJson.listToReplaceStr(roleList, "id", "name", ",");
		model.addAttribute("roleReplace", roleReplace);

		return new ModelAndView("/report/assetReportTemplate");
	}




	/**
	 * assetReportTemplate查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
	@ResponseBody
	public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,String folderId,HttpServletRequest request) {
		TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
		JqGridResponseTo j=null;
		try {

			if(StringUtils.isEmpty(folderId)) {
				return j;
			}

			SysUser su = TokenManager.getToken();
			List<String> orgIdList = OrganizationUtils.assembleOrgIds(su.getOrgId(),sysOrgService,false,0);
			jqGridSearchTo.put("orgIds", orgIdList);
			jqGridSearchTo.put("folderId", folderId);

			j= assetReportTemplateService.getForJqGrid(jqGridSearchTo);
		}
		catch (Exception e) {
			e.printStackTrace();
		}
		return j;
	}
	/**
     * 删除assetReportTemplate信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(AssetReportTemplate assetReportTemplate,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(assetReportTemplateService.deleteById(assetReportTemplate.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存assetReportTemplate信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })AssetReportTemplate assetReportTemplate,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        AssetReportTemplate t=new  AssetReportTemplate();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(assetReportTemplate.getId())){
        	t=assetReportTemplateService.getEntity(assetReportTemplate.getId());
			MyBeanUtils.copyBeanNotNull2Bean(assetReportTemplate, t);
			
			AssetReportTemplate assetReportTemplate1 = new AssetReportTemplate();
			assetReportTemplate1.setOrgId(t.getOrgId());
			assetReportTemplate1.setReportName(t.getReportName());
	    	List<AssetReportTemplate> list = assetReportTemplateService.getList(assetReportTemplate1);
	    	if(list.size()>1 || (list.size()>0 && !list.get(0).getId().equals(assetReportTemplate.getId()))){
	    		j.setSuccess(false);
	    		j.setErrorMsg("The Organization Report Template already exists");
	    		return j;
	    	}
			
				assetReportTemplateService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
				
				AssetReportTemplate assetReportTemplate1 = new AssetReportTemplate();
				assetReportTemplate1.setOrgId(assetReportTemplate.getOrgId());
				assetReportTemplate1.setReportName(assetReportTemplate.getReportName());
		    	List<AssetReportTemplate> list = assetReportTemplateService.getList(assetReportTemplate1);
		    	if(list != null && list.size() >0 ){
		    		j.setSuccess(false);
		    		j.setErrorMsg("The Organization Report Template already exists");
		    		return j;
		    	}
				
	            assetReportTemplateService.save(assetReportTemplate);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }

	/**
	 * 跳转到条件模板页
	 *
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "condTemplate")
	public ModelAndView condTemplate(HttpServletRequest request, Model model) {
		String conditionFlag = request.getParameter("conditionFlag");
		String templateFile = request.getParameter("templateFile");
		String orgId = request.getParameter("orgId");
		String reportType = request.getParameter("reportType");

		String basePath=request.getRealPath("/");
		String fileStoreDir=basePath + "/WEB-INF/classes/ureport/"+templateFile;
		File f=new File(fileStoreDir);
		if(!f.exists()){
			String tempfileDir= ResourceUtil.getSessionattachmenttitle("hes.ureport.fileStoreDir") + "/"+templateFile;
			File ft=new File(tempfileDir);
			if(ft.exists()){
				BufferedInputStream bis = null;
				BufferedOutputStream bos = null;
				try {
					bis = new BufferedInputStream(new FileInputStream(ft));
					bos = new BufferedOutputStream(new FileOutputStream(f));

				} catch (FileNotFoundException e) {
					e.printStackTrace();
				}

				try {
					IOUtils.copy(bis,bos);
					bis.close();
					bos.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}

		// 根据字典码判断对应的条件模板
		if ("101".equals(conditionFlag) || "301".equals(conditionFlag)) {
			model.addAttribute("condTemplateName", "reportCondTemplate/defaultCondTemplate.jsp");
		}
		if ("201".equals(conditionFlag) ) {
			model.addAttribute("condTemplateName", "reportCondTemplate/defaultStartTimeTemplate.jsp");
		}

		model.addAttribute("dateType", reportType);
		model.addAttribute("templateFile", templateFile);
		model.addAttribute("orgId", orgId);

		return new ModelAndView("/report/reportCondTemplate");
	}

	@RequestMapping(value = "getReportTemplate")
	@ResponseBody
	public AjaxJson getReportTemplate(String id,
									  HttpServletRequest request, Model model) {
		AjaxJson j = new AjaxJson();

		AssetReportTemplate assetReportTemplate = assetReportTemplateService.getEntity(id);
		SysUser su = new SysUser();
		su.setOrgId(assetReportTemplate.getOrgId());
		List<String> orgIdList = OrganizationUtils.assembleOrgIds(su.getOrgId(),sysOrgService,false,0);
		assetReportTemplate.setOrgId(StringUtils.join(orgIdList.toArray(), ","));
		j.setObj(assetReportTemplate);

		// 查询此报表配置的查询条件信息



		return j;
	}
	
}