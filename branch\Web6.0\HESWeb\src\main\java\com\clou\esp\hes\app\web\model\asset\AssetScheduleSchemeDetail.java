/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetScheduleSchemeDetail{ } 
 * 
 * 摘    要： 采集方案明细
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-03-06 03:10:48
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.asset;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class AssetScheduleSchemeDetail  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public AssetScheduleSchemeDetail() {
	}
	private java.lang.String taskId;
	/**任务类型*/
	private java.lang.Integer taskType;
	/**曲线编号*/
	private java.lang.String profileId;
	/**参考时间*/
	private java.util.Date startTime;
	/**结束时间*/
	private java.util.Date endTime;
	/**任务周期类型*/
	private java.lang.String taskCycleType;
	/**任务周期*/
	private java.lang.Integer taskCycle;

	
	
	
	public java.lang.String getTaskId() {
		return taskId;
	}

	public void setTaskId(java.lang.String taskId) {
		this.taskId = taskId;
	}

	/**
	 * 任务类型
	 * @return the value of ASSET_SCHEDULE_SCHEME_DETAIL.TASK_TYPE
	 * @mbggenerated 2018-03-06 03:10:48
	 */
	public java.lang.Integer getTaskType() {
		return taskType;
	}

	/**
	 * 任务类型
	 * @param taskType the value for ASSET_SCHEDULE_SCHEME_DETAIL.TASK_TYPE
	 * @mbggenerated 2018-03-06 03:10:48
	 */
    	public void setTaskType(java.lang.Integer taskType) {
		this.taskType = taskType;
	}
	/**
	 * 曲线编号
	 * @return the value of ASSET_SCHEDULE_SCHEME_DETAIL.PROFILE_ID
	 * @mbggenerated 2018-03-06 03:10:48
	 */
	public java.lang.String getProfileId() {
		return profileId;
	}

	/**
	 * 曲线编号
	 * @param profileId the value for ASSET_SCHEDULE_SCHEME_DETAIL.PROFILE_ID
	 * @mbggenerated 2018-03-06 03:10:48
	 */
    	public void setProfileId(java.lang.String profileId) {
		this.profileId = profileId;
	}
	/**
	 * 参考时间
	 * @return the value of ASSET_SCHEDULE_SCHEME_DETAIL.START_TIME
	 * @mbggenerated 2018-03-06 03:10:48
	 */
	public java.util.Date getStartTime() {
		return startTime;
	}

	/**
	 * 参考时间
	 * @param startTime the value for ASSET_SCHEDULE_SCHEME_DETAIL.START_TIME
	 * @mbggenerated 2018-03-06 03:10:48
	 */
    	public void setStartTime(java.util.Date startTime) {
		this.startTime = startTime;
	}
	/**
	 * 结束时间
	 * @return the value of ASSET_SCHEDULE_SCHEME_DETAIL.END_TIME
	 * @mbggenerated 2018-03-06 03:10:48
	 */
	public java.util.Date getEndTime() {
		return endTime;
	}

	/**
	 * 结束时间
	 * @param endTime the value for ASSET_SCHEDULE_SCHEME_DETAIL.END_TIME
	 * @mbggenerated 2018-03-06 03:10:48
	 */
    	public void setEndTime(java.util.Date endTime) {
		this.endTime = endTime;
	}
	/**
	 * 任务周期类型
	 * @return the value of ASSET_SCHEDULE_SCHEME_DETAIL.TASK_CYCLE_TYPE
	 * @mbggenerated 2018-03-06 03:10:48
	 */
	public java.lang.String getTaskCycleType() {
		return taskCycleType;
	}

	/**
	 * 任务周期类型
	 * @param taskCycleType the value for ASSET_SCHEDULE_SCHEME_DETAIL.TASK_CYCLE_TYPE
	 * @mbggenerated 2018-03-06 03:10:48
	 */
    	public void setTaskCycleType(java.lang.String taskCycleType) {
		this.taskCycleType = taskCycleType;
	}
	/**
	 * 任务周期
	 * @return the value of ASSET_SCHEDULE_SCHEME_DETAIL.TASK_CYCLE
	 * @mbggenerated 2018-03-06 03:10:48
	 */
	public java.lang.Integer getTaskCycle() {
		return taskCycle;
	}

	/**
	 * 任务周期
	 * @param taskCycle the value for ASSET_SCHEDULE_SCHEME_DETAIL.TASK_CYCLE
	 * @mbggenerated 2018-03-06 03:10:48
	 */
    	public void setTaskCycle(java.lang.Integer taskCycle) {
		this.taskCycle = taskCycle;
	}

	public AssetScheduleSchemeDetail(java.lang.Integer taskType 
	,java.lang.String profileId 
	,java.util.Date startTime 
	,java.util.Date endTime 
	,java.lang.String taskCycleType 
	,java.lang.Integer taskCycle ) {
		super();
		this.taskType = taskType;
		this.profileId = profileId;
		this.startTime = startTime;
		this.endTime = endTime;
		this.taskCycleType = taskCycleType;
		this.taskCycle = taskCycle;
	}

}