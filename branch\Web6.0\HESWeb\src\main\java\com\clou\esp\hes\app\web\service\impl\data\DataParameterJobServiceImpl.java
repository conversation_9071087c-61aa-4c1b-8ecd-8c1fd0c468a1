/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataParameterJob{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-04-27 09:13:12
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.data;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.data.DataParameterJobDao;
import com.clou.esp.hes.app.web.model.data.DataParameterJob;
import com.clou.esp.hes.app.web.service.data.DataParameterJobService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@Component
@Service("dataParameterJobService")
public class DataParameterJobServiceImpl  extends CommonServiceImpl<DataParameterJob>  implements DataParameterJobService {

	@Resource
	private DataParameterJobDao dataParameterJobDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dataParameterJobDao);
    }
	public DataParameterJobServiceImpl() {}
	
	@Override
	public JqGridResponseTo getForJqGrid_ForJobList(JqGridSearchTo jqGridSearchTo) {
		PageHelper
				.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		List<DataParameterJob> list = dataParameterJobDao.getForJqGrid_ForJobList(jqGridSearchTo);
		PageInfo<DataParameterJob> pageInfo = new PageInfo<DataParameterJob>(list);
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}
	
	@Override
	public List<DataParameterJob> exportOrPrintPlanReportJobList(DataParameterJob job) {
		return dataParameterJobDao.exportOrPrintPlanReportJobList(job);
	}
	@Override
	public List<DataParameterJob> exportOrPrintJobReportJobList(
			DataParameterJob job) {
		return dataParameterJobDao.exportOrPrintJobReportJobList(job);
	}
	@Override
	public void cancelByPlanId(String planId) {
		this.dataParameterJobDao.cancelByPlanId(planId);
		
	}
}