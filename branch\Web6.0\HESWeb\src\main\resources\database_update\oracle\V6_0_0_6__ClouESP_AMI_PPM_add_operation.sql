Insert into PPM_DICT_OPERATION (ID,OPER<PERSON>IONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2010001','Add Debt','2010','addDebt',1,'Add Debt');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2010002','Delete Debt','2010','deleteDebt',2,'Delete Debt');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2011001','Add Tariff Group','2011','addTariffGroup',1,'Add Tariff Group');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2011002','Delete Tariff Group','2011','deleteTariffGroup',2,'Delete Tariff Group');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2011003','Modify Tariff Group','2011','modifyTariffGroup',3,'Modify Tariff Group');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2011004','Set Passive Tariff Group','2011','setPassiveTariffGroup',4,'Set Passive Tariff Group');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2011005','Activate Tariff Group','2011','activateTariffGroup',5,'Activate Tariff Group');

drop table SYS_UTILITY;
CREATE TABLE SYS_UTILITY
(	
   ID NVARCHAR2(32) NOT NULL ENABLE, 
	STATE NUMBER(4,0), 
	NAME NVARCHAR2(64) NOT NULL ENABLE, 
	DESCRIPTION VARCHAR2(256), 
  	Email VARCHAR2(64),
  	Mobile_Phone VARCHAR2(32),	
	START_DATE DATE, 
	END_DATE DATE, 
	MAX_NUM VARCHAR2(32), 
	CURRENCY_SYMBOL VARCHAR2(32), 
	SYSTEM_LOGIN_LOGO VARCHAR2(32), 
	SYSTEM_HOME_LOGO VARCHAR2(32), 
	VENDING_LOGO VARCHAR2(32), 
	VENDING_COMPANY_NAME VARCHAR2(256), 
	VENDING_MAKE VARCHAR2(32), 
	DEPARTMENT VARCHAR2(256), 
	POWER_UNIT VARCHAR2(32), 
	SYSTEM_DATE_FORMAT VARCHAR2(32), 
	SYSTEM_TIME_FORMAT VARCHAR2(32), 
	SYSTEM_DATE_TIME_FORMAT VARCHAR2(32), 
	SYSTEM_DATEDIFFERENCE VARCHAR2(2), 
	CREATE_DATE DATE, 
	SAVE_DB_DATE DATE, 
	README VARCHAR2(256), 
	PRIMARY KEY (ID)
	);

delete from sys_utility where id = '1';
INSERT INTO sys_utility VALUES ('1', '1', 'ClouESP PPM Default Utility', 'ClouESP PPM Default Utility', '<EMAIL>', '18675569695', '2018-01-01 00:00:00', '2019-01-01 00:00:00', '3000', '10', 'GHC', null, null, null, null, 'clou.png', 'Shenzhen Clou Electronics Co., Ltd', 'Clou POWER', null, null, 'dd-MM-yyyy', 'HH:mm:ss', 'dd-MM-yyyy HH:mm:ss', '0', '2018-05-22 15:18:57', '2018-05-22 15:18:57', '系统管理使用租户');

alter table asset_customer add CREATE_DATE date default sysdate; 