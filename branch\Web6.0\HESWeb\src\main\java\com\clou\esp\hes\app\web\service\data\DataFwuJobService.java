/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataFwuJob{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-12-29 08:37:48
 * 最后修改时间：
 * 
 *********************************************************************************************************/

package com.clou.esp.hes.app.web.service.data;

import java.util.List;

import com.clou.esp.hes.app.web.service.common.CommonService;
import com.clou.esp.hes.app.web.model.data.DataFwuJob;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

public interface DataFwuJobService extends CommonService<DataFwuJob> {

	public List<DataFwuJob> getListGroupByField(DataFwuJob dfj);

	public JqGridResponseTo getForJqGrids(JqGridSearchTo jqGridSearchTo);

	public List<DataFwuJob> getListNoPage(JqGridSearchTo j);
	
	public void cancelByPlanId(String planId);
	
	public List<DataFwuJob> getListByJq(JqGridSearchTo jqGridSearchTo);

}