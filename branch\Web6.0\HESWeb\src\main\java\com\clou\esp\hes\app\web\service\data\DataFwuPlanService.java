/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataFwuPlan{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-12-29 08:37:48
 * 最后修改时间：
 * 
 *********************************************************************************************************/

package com.clou.esp.hes.app.web.service.data;

import ch.iec.tc57._2011.enddevicecontrols.FaultMessage;

import com.clou.esp.hes.app.web.model.data.DataFwuPlan;
import com.clou.esp.hes.app.web.service.common.CommonService;
import com.clou.esp.hes.app.web.service.system.SysServiceAttributeService;
import com.power7000g.core.util.json.AjaxJson;

public interface DataFwuPlanService extends CommonService<DataFwuPlan> {
	
	/**
	 * 保存固件升级计划
	 * @Description 
	 * @param dfp
	 * @param sysServiceAttributeService
	 * @return
	 * @throws FaultMessage AjaxJson
	 * <AUTHOR> 
	 * @Time 2018年5月14日 下午2:04:50
	 */
	public AjaxJson saveFWUPlan(DataFwuPlan dfp, SysServiceAttributeService sysServiceAttributeService) throws FaultMessage;

	public AjaxJson cretePlanAgain(DataFwuPlan plan, String oldPlanId, SysServiceAttributeService sysServiceAttributeService);

}