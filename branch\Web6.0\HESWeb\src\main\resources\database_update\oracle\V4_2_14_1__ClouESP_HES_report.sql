Insert into DICT_REPORT (ID,REPOR<PERSON><PERSON><PERSON>,SORT_ID,FUNCTIONURL,PARENT_ID) values ('100005','Interval Energy Data',1,'/meterDataReportController/listReport.do','100');

Insert into DICT_OPERATION (ID,OPER<PERSON>IONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1027001','Add Calculation Object','1027','AddCalculationObject',1,'Add Calculation Object');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1027002','Edit Calculation Object','1027','EditCalculationObject',3,'Edit Calculation Object');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1027003','Delete Calculation Object','1027','DeleteCalculationObject',2,'Delete Calculation Object');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1027004','Add Data Channnel in Calculation Object','1027','AddDataChannelInCalcObj',4,'Add Data Channnel in Calculation Object');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1027005','Edit Data Channnel in Calculation Object','1027','EditDataChannelInCalcObj',6,'Edit Data Channnel in Calculation Object');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1027006','Delete Data Channnel in Calculation Object','1027','DelDataChannelInCalcObj',5,'Delete Data Channnel in Calculation Object');

Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1030','Templete Reports','dictReportController/designer.do','Data Management');

Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1030001','Report Designer Tab Page','1030','ReportDesignerTabPage',1,'Report Designer Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1030002','Report Management Tab Page','1030','ReportManagementTabPage',2,'Report Management Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1030003','Report Exploer Tab Page','1030','ReportExploerTabPage',3,'Report Exploer Tab Page');