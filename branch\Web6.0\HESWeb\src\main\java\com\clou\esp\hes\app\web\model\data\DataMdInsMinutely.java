/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataMeterDataEnergyMinutely{ } 
 * 
 * 摘    要： dataMeterDataEnergyMinutely
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-24 03:20:08
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.data;

import java.util.List;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DataMdInsMinutely  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DataMdInsMinutely() {
	}

	private java.util.Date tv;
	private java.lang.String value1;
	private java.lang.String value2;
	private java.lang.String value3;
	private java.lang.String value4;
	private java.lang.String value5;
	private java.lang.String value6;
	private java.lang.String value7;
	private java.lang.String value8;
	private java.lang.String value9;
	private java.lang.String value10;
	private java.lang.String value11;
	private java.lang.String value12;
	private java.lang.String value13;
	private java.lang.String value14;
	private java.lang.String value15;
	private java.lang.String value16;
	private java.lang.String value17;
	private java.lang.String value18;
	private java.lang.String value19;
	private List<DataMeterEvent> events;
	private DataCalcObj dataCalcObj;
	
	public DataCalcObj getDataCalcObj() {
		return dataCalcObj;
	}
	public void setDataCalcObj(DataCalcObj dataCalcObj) {
		this.dataCalcObj = dataCalcObj;
	}
	public List<DataMeterEvent> getEvents() {
		return events;
	}
	public void setEvents(List<DataMeterEvent> events) {
		this.events = events;
	}
	public java.util.Date getTv() {
		return tv;
	}
	public void setTv(java.util.Date tv) {
		this.tv = tv;
	}
	public java.lang.String getValue1() {
		return value1;
	}
	public void setValue1(java.lang.String value1) {
		this.value1 = value1;
	}
	public java.lang.String getValue2() {
		return value2;
	}
	public void setValue2(java.lang.String value2) {
		this.value2 = value2;
	}
	public java.lang.String getValue3() {
		return value3;
	}
	public void setValue3(java.lang.String value3) {
		this.value3 = value3;
	}
	public java.lang.String getValue4() {
		return value4;
	}
	public void setValue4(java.lang.String value4) {
		this.value4 = value4;
	}
	public java.lang.String getValue5() {
		return value5;
	}
	public void setValue5(java.lang.String value5) {
		this.value5 = value5;
	}
	public java.lang.String getValue6() {
		return value6;
	}
	public void setValue6(java.lang.String value6) {
		this.value6 = value6;
	}
	public java.lang.String getValue7() {
		return value7;
	}
	public void setValue7(java.lang.String value7) {
		this.value7 = value7;
	}
	public java.lang.String getValue8() {
		return value8;
	}
	public void setValue8(java.lang.String value8) {
		this.value8 = value8;
	}
	public java.lang.String getValue9() {
		return value9;
	}
	public void setValue9(java.lang.String value9) {
		this.value9 = value9;
	}
	public java.lang.String getValue10() {
		return value10;
	}
	public void setValue10(java.lang.String value10) {
		this.value10 = value10;
	}
	public java.lang.String getValue11() {
		return value11;
	}
	public void setValue11(java.lang.String value11) {
		this.value11 = value11;
	}
	public java.lang.String getValue12() {
		return value12;
	}
	public void setValue12(java.lang.String value12) {
		this.value12 = value12;
	}
	public java.lang.String getValue13() {
		return value13;
	}
	public void setValue13(java.lang.String value13) {
		this.value13 = value13;
	}
	public java.lang.String getValue14() {
		return value14;
	}
	public void setValue14(java.lang.String value14) {
		this.value14 = value14;
	}
	public java.lang.String getValue15() {
		return value15;
	}
	public void setValue15(java.lang.String value15) {
		this.value15 = value15;
	}
	public java.lang.String getValue16() {
		return value16;
	}
	public void setValue16(java.lang.String value16) {
		this.value16 = value16;
	}
	public java.lang.String getValue17() {
		return value17;
	}
	public void setValue17(java.lang.String value17) {
		this.value17 = value17;
	}
	public java.lang.String getValue18() {
		return value18;
	}
	public void setValue18(java.lang.String value18) {
		this.value18 = value18;
	}
	public java.lang.String getValue19() {
		return value19;
	}
	public void setValue19(java.lang.String value19) {
		this.value19 = value19;
	}
	
	

}