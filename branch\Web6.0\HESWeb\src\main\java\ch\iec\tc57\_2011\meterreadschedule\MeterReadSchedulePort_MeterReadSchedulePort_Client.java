
package ch.iec.tc57._2011.meterreadschedule;

/**
 * Please modify this class to meet your needs
 * This class is not complete
 */

import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.bind.annotation.XmlSeeAlso;

/**
 * This class was generated by Apache CXF 3.1.1
 * 2018-02-01T09:54:34.684+08:00
 * Generated source version: 3.1.1
 * 
 */
public final class MeterReadSchedulePort_MeterReadSchedulePort_Client {

    private static final QName SERVICE_NAME = new QName("http://iec.ch/TC57/2011/MeterReadSchedule", "MeterReadSchedule");

    private MeterReadSchedulePort_MeterReadSchedulePort_Client() {
    }

    public static void main(String args[]) throws java.lang.Exception {
        URL wsdlURL = MeterReadSchedule.WSDL_LOCATION;
        if (args.length > 0 && args[0] != null && !"".equals(args[0])) { 
            File wsdlFile = new File(args[0]);
            try {
                if (wsdlFile.exists()) {
                    wsdlURL = wsdlFile.toURI().toURL();
                } else {
                    wsdlURL = new URL(args[0]);
                }
            } catch (MalformedURLException e) {
                e.printStackTrace();
            }
        }
      
        MeterReadSchedule ss = new MeterReadSchedule(wsdlURL, SERVICE_NAME);
        MeterReadSchedulePort port = ss.getMeterReadSchedulePort();  
        
        {
        System.out.println("Invoking getMeterReadSchedule...");
        ch.iec.tc57._2011.meterreadschedulemessage.MeterReadScheduleRequestMessageType _getMeterReadSchedule_getMeterReadSchedule = null;
        try {
            ch.iec.tc57._2011.meterreadschedulemessage.MeterReadScheduleResponseMessageType _getMeterReadSchedule__return = port.getMeterReadSchedule(_getMeterReadSchedule_getMeterReadSchedule);
            System.out.println("getMeterReadSchedule.result=" + _getMeterReadSchedule__return);

        } catch (FaultMessage e) { 
            System.out.println("Expected exception: FaultMessage has occurred.");
            System.out.println(e.toString());
        }
            }

        System.exit(0);
    }

}
