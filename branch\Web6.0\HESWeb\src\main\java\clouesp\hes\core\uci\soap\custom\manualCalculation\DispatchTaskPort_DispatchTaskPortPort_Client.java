package clouesp.hes.core.uci.soap.custom.manualCalculation;


/**
 * Please modify this class to meet your needs
 * This class is not complete
 */

import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

/**
 * This class was generated by Apache CXF 3.1.18
 * 2019-04-18T11:18:00.876+08:00
 * Generated source version: 3.1.18
 * 
 */
public final class DispatchTaskPort_DispatchTaskPortPort_Client {

    private static final QName SERVICE_NAME = new QName("http://dispatch_task.custom.soap.uci.core.hes.clouesp/", "DispatchTaskPort");

    private DispatchTaskPort_DispatchTaskPortPort_Client() {
    }

    public static void main(String args[]) throws java.lang.Exception {
        URL wsdlURL = DispatchTaskPort_Service.WSDL_LOCATION;
        if (args.length > 0 && args[0] != null && !"".equals(args[0])) { 
            File wsdlFile = new File(args[0]);
            try {
                if (wsdlFile.exists()) {
                    wsdlURL = wsdlFile.toURI().toURL();
                } else {
                    wsdlURL = new URL(args[0]);
                }
            } catch (MalformedURLException e) {
                e.printStackTrace();
            }
        }
      
        DispatchTaskPort_Service ss = new DispatchTaskPort_Service(wsdlURL, SERVICE_NAME);
        DispatchTaskPort port = ss.getDispatchTaskPortPort();  
        
        {
        System.out.println("Invoking dispatch...");
        java.util.List<DispatchMessage> _dispatch_msgs = null;
        boolean _dispatch__return = port.dispatch(_dispatch_msgs);
        System.out.println("dispatch.result=" + _dispatch__return);


        }

        System.exit(0);
    }

}
