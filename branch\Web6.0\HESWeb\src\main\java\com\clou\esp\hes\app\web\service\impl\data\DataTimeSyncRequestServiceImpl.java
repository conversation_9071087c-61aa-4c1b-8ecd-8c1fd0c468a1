package com.clou.esp.hes.app.web.service.impl.data;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.data.DataTimeSyncRequestDao;
import com.clou.esp.hes.app.web.model.data.DataTimeSyncRequest;
import com.clou.esp.hes.app.web.service.data.DataTimeSyncRequestService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dataTimeSyncRequestService")
public class DataTimeSyncRequestServiceImpl extends
CommonServiceImpl<DataTimeSyncRequest> implements
DataTimeSyncRequestService {

    private static final Logger LOG = Logger.getLogger(DataTimeSyncRequestServiceImpl.class);
    
    @Resource
    private DataTimeSyncRequestDao dataTimeSyncRequestDao;
    
    @Autowired
    public void setCommonService() {
        // TODO Auto-generated method stub
        super.setCommonService(dataTimeSyncRequestDao);
    }
    
    public DataTimeSyncRequestServiceImpl() {
        // TODO Auto-generated constructor stub
    }

}
