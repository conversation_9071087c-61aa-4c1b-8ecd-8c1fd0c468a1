/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetScheduleScheme{ } 
 * 
 * 摘    要： 采集方案
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-03-06 03:10:17
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.asset;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.json.ValidForm;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroup;
import com.clou.esp.hes.app.web.model.asset.AssetScheduleScheme;
import com.clou.esp.hes.app.web.model.asset.AssetScheduleSchemeDetail;
import com.clou.esp.hes.app.web.model.dict.DictProtocol;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.asset.AssetScheduleSchemeDetailService;
import com.clou.esp.hes.app.web.service.asset.AssetScheduleSchemeService;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.dict.DictProtocolService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.oConvertUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;

/**
 * <AUTHOR>
 * @时间：2018-03-06 03:10:17
 * @描述：采集方案类
 */
@Controller
@RequestMapping("/assetScheduleSchemeController")
public class AssetScheduleSchemeController extends BaseController{

	@Resource
	private DataUserLogService dataUserLogService;
 	@Resource
    private AssetScheduleSchemeService assetScheduleSchemeService;
 	@Resource
    private DictProtocolService dictProtocolService;
 	@Resource
    private AssetScheduleSchemeDetailService assetScheduleSchemeDetailService;

	/**
	 * 跳转到采集方案列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/asset/assetScheduleSchemeList");
    }

	/**
	 * 跳转到采集方案新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "assetScheduleScheme")
	public ModelAndView assetScheduleScheme(AssetScheduleScheme assetScheduleScheme,HttpServletRequest request, Model model) {
		List<DictProtocol> list=dictProtocolService.getAllList();
		String protocolReplace = RoletoJson.listToReplaceStr(list, "id", "name");
		model.addAttribute("protocolReplace", protocolReplace);
		if(StringUtil.isNotEmpty(assetScheduleScheme.getId())){
			try {
                assetScheduleScheme=assetScheduleSchemeService.getEntity(assetScheduleScheme.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("assetScheduleScheme", assetScheduleScheme);
		}
		return new ModelAndView("/asset/assetScheduleScheme");
	}
	
	/**
	 * 验证名称唯一
	 */
	@RequestMapping(value = "checkScheduleSchemeName")
	@ResponseBody
	public ValidForm checkScheduleSchemeName(AssetScheduleScheme assetScheduleScheme, HttpServletRequest request,
			HttpServletResponse response) {
		ValidForm v = new ValidForm();
		String param = oConvertUtils.getString(request.getParameter("param"));
		String groupName = assetScheduleScheme.getName();
		assetScheduleScheme.setName(param);
		List<AssetScheduleScheme> list = assetScheduleSchemeService.getList(assetScheduleScheme);
		if (list.size() > 0 && !param.equals(groupName)) {
			v.setInfo("The Scheme Name already exists");
			v.setStatus("n");
		}
		return v;
	}
	
	/**
	 * 
	 * @param assetMeterGroup
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getReferenceSchemeReplace")
    @ResponseBody
    public AjaxJson getReferenceSchemeReplace(AssetScheduleScheme assetScheduleScheme,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
        	List<AssetScheduleScheme> amgs=assetScheduleSchemeService.getList(assetScheduleScheme);
            j.setObj(amgs);
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg("Abnormal operation!");
        }
        return j;
    }


	/**
	 * 采集方案查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=assetScheduleSchemeService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
	
	/**
	 * 返回AssetScheduleScheme
	 * @param assetScheduleScheme
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getAssetScheduleSchemeList")
    @ResponseBody
    public AjaxJson getAssetScheduleSchemeList(AssetScheduleScheme assetScheduleScheme,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
        	List<AssetScheduleScheme> allList = assetScheduleSchemeService.getAllList();
        	j.setObj(allList);
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 删除采集方案信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(AssetScheduleScheme assetScheduleScheme,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            AssetScheduleSchemeDetail entity=new AssetScheduleSchemeDetail();
            entity.setId(assetScheduleScheme.getId());
            assetScheduleScheme=assetScheduleSchemeService.getEntity(assetScheduleScheme.getId());
			assetScheduleSchemeDetailService.delete(entity);
            if(assetScheduleSchemeService.deleteById(assetScheduleScheme.getId())>0){
            	dataUserLogService.insertDataUserLog(su.getId(), "Collection Scheme Mgmt","Delete Scheme", "Delete scheme(Scheme Name="+assetScheduleScheme.getName()+")");
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存采集方案信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })AssetScheduleScheme assetScheduleScheme,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        AssetScheduleScheme t=new  AssetScheduleScheme();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(assetScheduleScheme.getId())){
        	t=assetScheduleSchemeService.getEntity(assetScheduleScheme.getId());
			MyBeanUtils.copyBeanNotNull2Bean(assetScheduleScheme, t);
				assetScheduleSchemeService.update(t);
				dataUserLogService.insertDataUserLog(su.getId(), "Collection Scheme Mgmt","Edit Scheme", "Edit scheme(Scheme Name="+assetScheduleScheme.getName()+")");
				j.setMsg("Successfully modified");
			}else{
	            String id=(String)assetScheduleSchemeService.save(assetScheduleScheme);
	            dataUserLogService.insertDataUserLog(su.getId(), "Collection Scheme Mgmt","Add Scheme", "Add scheme(Scheme Name="+assetScheduleScheme.getName()+")");
	            if(StringUtil.isNotEmpty(assetScheduleScheme.getReferenceSchemeId())){
	            	AssetScheduleSchemeDetail entity=new AssetScheduleSchemeDetail();
	            	entity.setId(assetScheduleScheme.getReferenceSchemeId());
					List<AssetScheduleSchemeDetail> list = assetScheduleSchemeDetailService.getList(entity);
					for(AssetScheduleSchemeDetail assd:list){
						assd.setId(id);
						assetScheduleSchemeDetailService.save(assd);
					}
	            }
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
	
	
	
}