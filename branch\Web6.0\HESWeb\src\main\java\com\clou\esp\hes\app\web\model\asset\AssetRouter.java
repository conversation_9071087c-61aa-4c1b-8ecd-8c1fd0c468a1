/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetRouter{ } 
 * 
 * 摘    要： assetRouter
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-04-08 03:09:42
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.asset;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class AssetRouter  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public AssetRouter() {
	}
	/**communicatorId*/
	private java.lang.String communicatorId;
	/**channelId*/
	private java.lang.String channelId;
	/**scheduleId*/
	private java.lang.String scheduleId;

	/**
	 * channelId
	 * @return the value of ASSET_ROUTER.CHANNEL_ID
	 * @mbggenerated 2018-04-08 03:09:42
	 */
	public java.lang.String getChannelId() {
		return channelId;
	}

	/**
	 * channelId
	 * @param channelId the value for ASSET_ROUTER.CHANNEL_ID
	 * @mbggenerated 2018-04-08 03:09:42
	 */
    	public void setChannelId(java.lang.String channelId) {
		this.channelId = channelId;
	}
	/**
	 * scheduleId
	 * @return the value of ASSET_ROUTER.SCHEDULE_ID
	 * @mbggenerated 2018-04-08 03:09:42
	 */
	public java.lang.String getScheduleId() {
		return scheduleId;
	}

	/**
	 * scheduleId
	 * @param scheduleId the value for ASSET_ROUTER.SCHEDULE_ID
	 * @mbggenerated 2018-04-08 03:09:42
	 */
    	public void setScheduleId(java.lang.String scheduleId) {
		this.scheduleId = scheduleId;
	}
    	
    	

	public java.lang.String getCommunicatorId() {
		return communicatorId;
	}

	public void setCommunicatorId(java.lang.String communicatorId) {
		this.communicatorId = communicatorId;
	}

	public AssetRouter(java.lang.String channelId 
	,java.lang.String scheduleId ) {
		super();
		this.channelId = channelId;
		this.scheduleId = scheduleId;
	}

}