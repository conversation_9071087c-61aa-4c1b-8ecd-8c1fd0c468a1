/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetReportTemplate{ }
 *
 * 摘    要： assetReportTemplate
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2019-03-22 01:50:10
 * 最后修改时间：
 *
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.report;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class AssetReportTemplate  extends BaseEntity {

	/**
	 * 意义，目的和功能，以及被用到的地方<br>
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public AssetReportTemplate() {
	}

	/**reportType*/
	private java.math.BigDecimal reportType;
	/**reportName*/
	private java.lang.String reportName;
	/**orgId*/
	private java.lang.String orgId;
	/**orgName*/
	private java.lang.String orgName;
	/**templateFile*/
	private java.lang.String templateFile;
	/**folderId*/
	private java.lang.String folderId;
	/**reportMark*/
	private java.lang.String reportMark;
	/**isEnable*/
	private Integer isEnable;

	/**folderName*/
	private java.lang.String folderName;

	/**roleId*/
	private java.lang.String roleId;

	/*条件组*/
	private java.lang.String conditionFlag;

	public java.lang.String getConditionFlag() {
		return conditionFlag;
	}

	public void setConditionFlag(java.lang.String conditionFlag) {
		this.conditionFlag = conditionFlag;
	}

	public java.lang.String getRoleId() {
		return roleId;
	}

	public void setRoleId(java.lang.String roleId) {
		this.roleId = roleId;
	}

	public java.lang.String getFolderName() {
		return folderName;
	}

	public void setFolderName(java.lang.String folderName) {
		this.folderName = folderName;
	}

	public java.lang.String getFolderId() {
		return folderId;
	}

	public void setFolderId(java.lang.String folderId) {
		this.folderId = folderId;
	}

	public java.lang.String getReportMark() {
		return reportMark;
	}

	public void setReportMark(java.lang.String reportMark) {
		this.reportMark = reportMark;
	}

	public Integer getIsEnable() {
		return isEnable;
	}

	public void setIsEnable(Integer isEnable) {
		this.isEnable = isEnable;
	}

	/**
	 * reportType
	 * @return the value of ASSET_REPORT_TEMPLATE.REPORT_TYPE
	 * @mbggenerated 2019-03-22 01:50:10
	 */
	public java.math.BigDecimal getReportType() {
		return reportType;
	}

	/**
	 * reportType
	 * @param reportType the value for ASSET_REPORT_TEMPLATE.REPORT_TYPE
	 * @mbggenerated 2019-03-22 01:50:10
	 */
	public void setReportType(java.math.BigDecimal reportType) {
		this.reportType = reportType;
	}
	/**
	 * reportName
	 * @return the value of ASSET_REPORT_TEMPLATE.REPORT_NAME
	 * @mbggenerated 2019-03-22 01:50:10
	 */
	public java.lang.String getReportName() {
		return reportName;
	}

	/**
	 * reportName
	 * @param reportName the value for ASSET_REPORT_TEMPLATE.REPORT_NAME
	 * @mbggenerated 2019-03-22 01:50:10
	 */
	public void setReportName(java.lang.String reportName) {
		this.reportName = reportName;
	}
	/**
	 * orgId
	 * @return the value of ASSET_REPORT_TEMPLATE.ORG_ID
	 * @mbggenerated 2019-03-22 01:50:10
	 */
	public java.lang.String getOrgId() {
		return orgId;
	}

	/**
	 * orgId
	 * @param orgId the value for ASSET_REPORT_TEMPLATE.ORG_ID
	 * @mbggenerated 2019-03-22 01:50:10
	 */
	public void setOrgId(java.lang.String orgId) {
		this.orgId = orgId;
	}




	public java.lang.String getOrgName() {
		return orgName;
	}

	public void setOrgName(java.lang.String orgName) {
		this.orgName = orgName;
	}

	/**
	 * templateFile
	 * @return the value of ASSET_REPORT_TEMPLATE.TEMPLATE_FILE
	 * @mbggenerated 2019-03-22 01:50:10
	 */
	public java.lang.String getTemplateFile() {
		return templateFile;
	}

	/**
	 * templateFile
	 * @param templateFile the value for ASSET_REPORT_TEMPLATE.TEMPLATE_FILE
	 * @mbggenerated 2019-03-22 01:50:10
	 */
	public void setTemplateFile(java.lang.String templateFile) {
		this.templateFile = templateFile;
	}

	public AssetReportTemplate(java.math.BigDecimal reportType
			,java.lang.String reportName
			,java.lang.String orgId
			,java.lang.String templateFile ) {
		super();
		this.reportType = reportType;
		this.reportName = reportName;
		this.orgId = orgId;
		this.templateFile = templateFile;
	}

}