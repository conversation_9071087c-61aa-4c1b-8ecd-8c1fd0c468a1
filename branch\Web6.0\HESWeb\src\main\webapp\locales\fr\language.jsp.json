﻿{
	"login": {
		"username": "Nom d’utilisateur",
		"pswd": "Mot de passe",
		"usernameNull": "Le nom d’utilisateur ne peut pas être vide!",
		"pswdNull": "Le mot de passe ne peut pas être vide!",
		"remeberMe": "Se souvenir de moi",
		"login": "Connexion",
		"loginOnOtherPlace": "Vous vous êtes connecté ailleurs, veuillez vous reconnecter!",
		"accountDisabled": "Le Compte a été désactivé, vous êtes obligé de vous déconnecter!",
		"world": "Globe",
		"America": "Americain",
		"Nigeria": "Nigeria",
		"Australia": "Australie",
		"ACP": "Plateforme Cloud Américaine",
		"notExistUser": "L’utilisateur n’existe pas!",
		"LockedUser": "Utilisateur bloqué!",
		"errorPwd": "Mot de passe incorrect",
		"disabledUser": "Utilisateur désactivé!",
		"errorUser": "Utilisateur anormal!",
		"loginFirst": "Veuillez vous connecter tout d’abord, SVP !",
		"logoutFailed": "Déconnexion échouée, veuillez réessayer, SVP!",
		"logout": "Déconnexion",
		"tenantIsStop": " L’utilisateur a été désactivé!",
		"accLgedElsewhere": "Ce compte utilisateur est connecté ailleurs",
		"userDontExist": "Le compte utilisateur n’existe pas!",
		"toRevisePwd": "Votre mot de passe a expiré. Veuillez le modifier",
		"firstLoginRevisePwd": " Veuillez changer votre mot de passe pour la première connexion.",
		"firstLoginResetPassword": "Première connexion pour réinitialiser le mot de passe.",
		"incorrectPcCode": "Code PC incorrect, si vous avez des questions, veuillez contacter l’administrateur du système",
		"startTimeNotEffect": "L’heure de début n’a pas encore pris effet, si vous avez des questions, veuillez contacter l’administrateur du système",
		"pcCodeOverdue": "Le code PC a expiré , si vous avez des questions, veuillez contacter l’administrateur du système ",
		"pcCodeUnavailable": "Le code PC n’est pas disponible, si vous avez des questions, veuillez contacter l’administrateur du système ",
		"meterNumUnavailable": "Le numéro de compteur n’est pas disponible, si vous avez des questions, veuillez contacter l’administrateur du système "
	},
	"system": {
		"systemName": "ClouESP",
		"systemVersion": "V6.0",
		"mdmSystemName": "MDM",
		"mdmSystemVersion": "V6.0",
		"copyRight": "Copyright 2020 SHENZHEN CLOU. Tous droits réservés.",
		"determine": "OK",
		"cancel": "Annuler",
		"notifications": "Notifications",
		"information": "Information",
		"message": "Message",
		"aydeltherdate": " Confirmer la suppression de cet enregistrement? ",
		"submit": "OK",
		"pseleData": "Veuillez sélectionner une donnée, SVP! ",
		"pleaseInterNum": "Veuillez introduire un numéro!",
		"reqexc": "Requête anormale!",
		"delete": "Supprimer",
		"add": "Ajouter",
		"edit": "Modifier",
		"change": "Changer de RF en GPRS",
		"inquiryTitle": "Notifications",
		"delSuccess": "Suppression réussie!",
		"delFail": "Suppression échouée",
		"abnoOpera": "Opération anormale!",
		"addSucc": "Ajouter avec succès!",
		"updateSucc": "Mise à jour avec succès!",
		"rtdbOtherSucc": "Mise à jour de règle avec succès!",
		"saveSucc": "Sauvegarde réussie!",
		"saveFail": "Sauvegarde échouée!",
		"cancelSucc": "Annulation réussie!",
		"cancelFail": "Annulation échouée!",
		"requestError": "Erreur de requête!",
		"operSucce": "Opération réussie!",
		"nameExist": "Le compte existe déjà!",
		"systemException": "Système anormal!",
		"UCIException": "UCI anormal!",
		"selectNoData": " Impossible de trouver des données!",
		"operation": "Opération",
		"export": "Exporter",
		"print": "Imprimer",
		"start": "Date de début d’inscription",
		"end": " Date de fin d’inscription",
		"validSucc": "Vérification réussie",
		"refreshTree": "Actualiser le nœud de l’arborescence.",
		"commit": "Déposer ",
		"checkLicense": "Veuillez vérifier que la licence est correcte."
	},
	"home": {
		"devicesStatistic": "Statistiques sur les périphériques",
		"meters": " Compteurs",
		"concentrators": "Concentrateurs",
		"communicators": "Concentrateurs",
		"selectiveCategory": " Catégorie sélective",
		"manufacturer": "Fabricant",
		"model": " Modèle",
		"communication": "Communication",
		"name": " Nom",
		"number": "Nombre",
		"rate": "Taux",
		"date": "Date",
		"collectionIntegrity": "Intégrité de la collection",
		"completeRate": "Taux d’accomplissement",
		"completedNumber": "Nombre complété",
		"partialNumber": "Nombre partiel",
		"failedNumber": "Nombre d’échec",
		"eventsStatistic": "Statistiques des événements",
		"eventsTotal": "Nombre total d’événements",
		"classifiedStatistcOfEvents": "Statistiques classifiées des événements",
		"overallDaily": "Ratio global d’intégrité quotidienne(%)",
		"classifiedStatistcOfMeter": "Statistique classifiées du compteur",
		"supplyStaticOfSubOrgan": "Alimenation d’énergie électrique de la sous-organisation",
		"supplyStatistic": "Statistique d’alimentation d’énergie électrique",
		"salesStatisticOfCustomer": "Statistique de vente d’énergie électrique du client",
		"organization": "Organisation",
		"type": "Type",
		"powerSupply": "Approvisionnement(kWh)",
		"customerNumber": "Numéro",
		"supplyStatisticDaily": " Les 7 derniers jours de statistiques d’approvisionnement (kWh) ",
		"supplyStatistic15Daily": "Les 15 derniers jours de statistiques d’approvisionnement (kWh) ",
		"supplyStatistic30Daily": "Les 30 derniers jours de statistiques d’approvisionnement (kWh) ",
		"supplyStatistic6Month": "Les 6 derniers mois de statistiques d’approvisionnement (kWh) ",
		"supplyStatistic12Month": " Les 12 derniers mois de statistiques d’approvisionnement (kWh) ",
		"Last7days": "7",
		"Last15days": "15",
		"Last30days": "30",
		"supplyCount": "Statistiques d’approvisionnement de la sous-organisation",
		"onlineMeter": "Compteurs en ligne",
		"onlineCommunicators": "Concentrateur en ligne",
		"yesterdayPowerSupply": "Approvisionnement d’hier(kWh)",
		"other": "Autre",
		"noData": "Aucune donnée",
		"Last6months": "6",
		"Last12months": "12"
	},

	"mdmHome": {
		"assetDashboard": "Tableau de bord des actifs",
		"dataAnalysisDashboard": "Tableau de bord d’analyse des données",
		"energyAnalysisDashboard": " Tableau de bord d’analyse d’énergie",
		"provisioningStatistics": "Statistiques d’approvisionnement",
		"dataCollectionProgress": "Progression de la collecte des données",
		"deviceEvents": "Événements de périphérique",
		"veeEvents": "Événements VEE",
		"veeDataStatistics": "Statistiques des données VEE",
		"summaryObjectStatistics": "Statistiques récapitulatives sur les objets",
		"lossObjectStatistics": "Statistiques sur les objets perdus",
		"billingStatistics": "Statistiques de facturation",
		"timeDays": "Heure (jours)",
		"timeMonths": "Heure (mois)",
		"energy": "Énergie (kWh)"
	},

	"error403": {
		"title": "Page introuvable!",
		"content": " Désolé, la page n’est pas autorisée~"
	},
	"error404": {
		"title": " Page introuvable!",
		"content": "Désolé, la page semble aller sur Mars~"
	},
	"error500": {
		"title": "Erreur Interne du Serveur!",
		"content": "Le serveur semble se tromper..."
	},
	"noPermissions": {
		"code": "10002",
		"title": "Pas d’accès!",
		"content": "Si vous avez besoin d’un accès, veuillez contacter l’administrateur..."
	},
	"index": {
		"home": "Page d’accueil",
		"logout": "Déconnexion",
		"search": "Recherche",
		"meter": "Compteur",
		"commnuicator": "Communicateur",
		"serialNumber": "Numéro de série",
		"name": "Nom",
		"mac": "LDN",
		"model": "Modèle",
		"searchFor": "Recherche pour...",
		"favorites": "Favoris",
		"myProfile": "Mon profil",
		"passwordSetting": "Configuration de mot de passe",
		"licenseSetting": "Configuration de license",
		"confCanFavorite": "Confirmer pour annuler le favori?",
		"advancedSearch": "Recherche Avancée",
		"frimwareVersion": " Version de Firmware",
		"measurementGroup": "Groupe de mesure",
		"collectionSchemeGroup": "Groupe de schémas de collecte",
		"line": "Ligne",
		"summaryObject": "Objet récapitulatif ",
		"lineLoss": "Objet de perte de ligne",
		"transformer": "Transformateur",
		"manufacturer": "Fabricant",
		"commuicationType": "Type de Communication ",
		"more": "Plus",
		"selected": "Sélectionné",
		"pleaseChoose": "Veuillez sélectionner",
		"communicatorSn": "Concentrateur NS",
		"organization": "Organisation",
		"headAlarmTitle": "Rapport d’événement d’alarme de compteur",
		"supplyStatisticsReport": "Rapport de statistiques d’approvisionnement",
		"noAlarm": " Pas d’alarme",
		"customer": "Client",
		"telephoneNum": "Numéro de téléphone ",
		"tel": "TEL",
		"voltageLevel": "Niveau de tension",
		"refreshOrgLineTransformer": "Actualiser l’arborescence de l’organisation...",
		"searchOrgLineTransformer": "Recherche de noeud ...",
		"loadTreeNode": "Chargement du nœud d’arborescence ...",
		"dailyReport": "Rapport journalier",
		"monthlyReport": "Rapport mensuel",
		"refreshReportTree": "Actualiser l’arborescence du rapport..."
	},
	"connectOrDisconnect": {
		"function": "Fonction",
		"system": "Systèm",
		"url": "URL",
		"connectDisconnect": "Connecter/Déconnecter",
		"power7000": "Puissance 7000"
	},
	"scheduleReadsReport": {
		"title": "Rapport des lectures d’intégrité",
		"overview": "Aperçu",
		"manufacturer": "Fabricant",
		"model": "Modèle",
		"communication": "Communication",
		"ydayInteRate": "Taux d’intégrité de lecture de données d’hier",
		"ydayCompRate": "Taux d’intégrité de lecture du compteur d’hier",
		"completed": "Terminé",
		"partial": "Partiel",
		"failed": "Échoué",
		"details": "Détails",
		"dMeterInteRate": "Taux d’intégrité des lectures quotidiennes des compteurs",
		"start": "Début",
		"end": "Fin",
		"mmeterInteRate": "Taux d’intégrité des lectures mensuelles des compteurs ",
		"dInteRateManu": "Taux d’intégrité quotidien par fabricant",
		"mInteRateManu": "Taux d’intégrité mensuel par fabricant",
		"dInteRateSMmanu": "Taux d’intégrité quotidien par fabricant(s) unique/multiple(s)",
		"mInteRateSMmanu": "Taux d’intégrité mensuel par fabricant(s) unique/multiple(s)",
		"dInteRateModels": "Taux d’intégrité quotidien par modèle",
		"mInteRateModels": "Taux d’intégrité mensuel par modèle",
		"dInteRateSMModel": "Taux d’intégrité quotidien par modèle(s) unique/multiple(s)",
		"mInteRateSMModel": "Taux d’intégrité mensuel par modèle(s) unique/multiple(s)",
		"dInteRateCommun": "Taux d’intégrité quotidien par communication",
		"mInteRateCommun": "Taux d’intégrité mensuel par communication",
		"dInteRateSMCommun": "Taux d’intégrité quotidien par communication(s) unique/multiple(s)",
		"mInteRateSMCommun": "Taux d’intégrité mensuel par communication(s) unique/multiple(s)",
		"dInteRateOrg": "Taux d’intégrité quotidien par organisation",
		"mInteRateOrg": "Taux d’intégrité mensuel par organisation",
		"dInteRateSMOrg": "Taux d’intégrité quotidien par organisation(s) unique/multiple(s)",
		"mInteRateSMOrg": "Taux d’intégrité mensuel par organisation(s) unique/multiple(s)",
		"dailyInteRate": "Taux d’intégrité quotidien",
		"monthlyInteRate": "Taux d’intégrité mensuel",
		"dmeterInteRadeio": "Taux d’intégrité des lectures quotidiennes du compteur",
		"mmeterInteRadeio": " Taux d’intégrité des lectures mensuelles des compteurs ",
		"dInteRatioManu": "Taux d’intégrité quotidien par les fabricant",
		"calcIntegrityRate": "Calculer le taux d’intégrité",
		"calcLineLoss": "Calculer la perte de ligne",
		"holdManual": "Arrière-plan recalculé, veuillez patienter"
	},
	"assetScheduleSchemeDetailList": {
		"title": "Liste de tâche",
		"taskId": "ID de tâche",
		"id": "ID",
		"taskType": "Type de tâche",
		"profileId": "Nom du profil",
		"taskCycleType": "Type de cycle de tâche",
		"taskCycle": "Cycle de tâche",
		"startTime": "Heure de début",
		"endTime": " Heure de fin",
		"pseleData": "Ce schéma est référencé et ne peut pas être supprimé!",
		"confirmDel": "Confirmer la suppression de cette tâche?",
		"addReadsTask": "Tâche de lecture de données",
		"addEvenTask": "Tâche de lecture d’événements",
		"addTimeTask": "Tâche de synchronisation de temps",
		"addOnline": "Tâche de lecture d’état en ligne",
		"dcuSyncTime": "Synchronisation de temps de DCU ",
		"collSchemeMage": "Gestion des programmes de collecte",
		"daily": "Journalier",
		"hourly": "Horaire",
		"minutely": "Minute",
		"monthly": "Mensuel",
		"pleaseChoose": "---Veuillez sélectionner---",
		"successfullyModified": "Modifié avec succès ",
		"addedSuccessfully": "Ajouté avec succès"
	},
	"assetScheduleSchemeList": {
		"title": "Liste des programmes",
		"id": "Id",
		"name": "Nom du programme",
		"meterStandard": "Compteur standard",
		"protocolId": "ID de protocole",
		"meterNumber": "Compteur de comptage",
		"referenceScheme": " Schéma de référence",
		"description": "Description",
		"pseleData": "Veuillez sélectionner les données de la liste de schémas!",
		"confirmDel": "Confirmer la suppression du schéma?",
		"canBeDeleted": "Le schéma est associé aux compteurs qui ne peuvent pas être supprimés!"
	},
	"dataIntegrityList": {
		"headTitle": "Manque de traçage des données",
		"evenExportTitle": "Exportation des données et des événements du compteur",
		"id": "No.",
		"serizlNo": "Compteur SN",
		"tv": "Date",
		"mfrId": "Fabricant",
		"modelId": "Modèle",
		"communicator": "Concentrateur SN",
		"commId": "Communication",
		"progress": "Progression",
		"lastTask": "Dernière tâche ",
		"taskResult": "Résultat de tâche",
		"failureCause": "Raison d’échec",
		"integrity": "Taux(%)",
		"integrityRate": "Taux d’intégrité (%)",
		"analysis": "Analyse de référence",
		"export": "Exporter",
		"print": "Imprimer",
		"taskResultFailed": "Échoué",
		"taskResultSuccess": "Succès",
		"all": "Tous",
		"delayDay": "Retard de progression (jours)",
		"progressDelayReport": "Rapport de retard de progression",
		"missDataReport": "Manque de rapport des données",
		"integrityRateReport": "Rapport de taux d’intégrité",
		"updateTv": "Temps de mise à jour d’état ",
		"comStatus": "État de Communication",
		"to": "à",
		"ProgressDelayYes": "Oui",
		"ProgressDelayNo": "Non"
	},
	"dataIntegrityDetails": {
		"id": "No.",
		"serizlNo": "SN",
		"tv": "",
		"manufacturer": "Fabricant",
		"model": "Modèle",
		"communication": "Communication",
		"profile": "Profil",
		"readStatus": "État de lecture",
		"noMeasurementGroup": "Aucun groupe de mesure ajouté."
	},
	"dataMeterEventList": {
		"headTitle": "Rapport d’événement de compteur",
		"headAlarmTitle": "Rapport d’événement d’alarme de compteur",
		"deviceId": " ID de périphérique",
		"eventId": " ID d’événement",
		"sn": "SN",
		"export": "Exporter",
		"print": "Imprimer",
		"tv": "",
		"eventType": " Type d’événement",
		"event": "Événement",
		"eventDetail": "Détails d’événement"
	},
	"dayList": {
		"id": "ID",
		"dayName": " ID du jour",
		"startTime": "Temps de début",
		"rate": "Taux",
		"add": "Ajouter",
		"addDay": "Ajouter le jour",
		"hour": "Heure",
		"minute": "Minute",
		"second": "Seconde",
		"delete": "Supprimer",
		"title": "Liste des jours",
		"pleaseSelectData": "Veuillez sélectionner un jour dans la liste des jours!",
		"pleSetRateDayList": "Veuillez configurer le taux dans la liste des jours!",
		"lastOneTimePeriod": "Les données de jour doivent être au moins une période de temps!",
		"areBeAtMost": "255 jours peuvent être créés au maximum!",
		"inteAllAtMost": "8 intervalles sont autorisés au maximum dans une journée!"
	},
	"device_list": {
		"headTitle": "Rapport d’évenement de compteur",
		"pageTitle": " Configuration de compteur",
		"title": "Liste de compteur",
		"id": "Id",
		"sn": "Compteur SN",
		"modelName": "Modèle",
		"communicator": "Concentrateur SN",
		"comType": "Commmunication",
		"set": "Écrire",
		"get": "Lire",
		"delete": "Supprimer",
		"parameterType": "Type de paramètre",
		"operation": "Opération",
		"profiles": "Profils",
		"channels": "Lectures à la demande",
		"seleLeastOne": "Sélectionnez au moins une donnée de table",
		"seleCommunicator": "Veuillez sélectionner un concentrateur ",
		"leastOneChoose": "Choisissez au moins une copie de lecture",
		"communicatorList": "Liste de concentrateur",
		"donotRepeat": "Veuillez ne pas répéter l’opération, SVP."
	},
	"device_read_parameter_list": {
		"title": "Résultats",
		"id": "Id",
		"sn": "Compteur SN",
		"parameterType": "Type de paramètre",
		"parameterItem": "Élément de paramètre",
		"meterGroup": "Groupe de compteurs",
		"requestTime": "Temps de requête",
		"responseTime": "Temps de réponse",
		"status": "État",
		"reason": "Raison",
		"delete": "Supprimer",
		"export": "Exporter",
		"print": "Imprimer",
		"total": "Total",
		"completed": "Terminé",
		"success": "Succès",
		"failed": "Échoué",
		"processing": "Traitement en cours",
		"measurementGroup": "Groupe de mesure",
		"tariffType": "Type de tarif", 
		"active": "Activé",
		"passive": "Passif",
		"touGroup": " Groupe de TOU",
		"limiterGroup": "Group de limiteur ",
		"pleAddTask": "Veuillez ajouter une tâche!",
		"areToExcTask": "Est ce que vous êtes sûr d’éxécuter cette liste de tâche?",
		"stepTariffGroup": "Groupe de tarif d’échelle",
		"prepay": "Prépaiement",
		"friend": "Amical",
		"touTariff": "Tou Tarif"

	},
	"linkedEntityRelationship": {
		"meterSN": "Compteur SN",
		"logicalName": "Nom du logicel ",
		"customerType": " Type de client",
		"organization": "Organisation",
		"linkedMeter": "Compteur associé",
		"linkedMeterList": "Liste de compteurs associés",
		"linkedTransformer": "Transformateur associé",
		"linkedTransformerList": "Liste de transformateurs associés",
		"communicatorSN": "Concentrateur SN",
		"communicatorName": "Nom du concentrateur",
		"transformerName": "Nom du transformateur",
		"transformerSN": "Transformateur SN",
		"selectLeastLine": "Veuillez d’abord sélectionner une ligne.",
		"selectLeastTransformer": "Veuillez sélectionner un transformateur de ligne."
	},


	"importTempGprsMeter": {
		"downFileImport": "Télécharger le fichier modèle",
		"tempFileImport": "Importer un fichier modèle",
		"downCustomerFileImport":"Télécharger le fichier modèle client",
		"tempCustomerFileImport":"Importer un fichier de modèle clien",
		"tempCustomerBatchImport":"Importer des fichiers modèles par lots",
		"tempFile": "Fichier modèle ",
		"meterType": "Type de compteur",
		"DCU": "DCU",
		"importStatus": "État d’importation",
		"importLog": "Journal d’importation",
		"commAddress": "Adresse commune",
		"logicalAddr": "Adresse du logicel ",
		"import": "Importer",
		"finish": "Terminer"
	},

	"deviceList": {
		"abnormalRequestFromUCI": "Requête anormale de l’UCI",
		"title": "Gestion d’actifs",
		"basicTitle": "Attribut de base ",
		"communication": "Attribut de COM ",
		"groupPro": "Attribut de groupe",
		"other": "Autre",
		"addGprsMeter": "Ajouter un compteur GPRS",
		"addMeterCommun": "Ajouter un compteur au concentrateur",
		"relateMeterCommun": "Connecter le compteur au concentrateur",
		"addCommunicatior": "Ajouter un concentrateur",
		"id": "Id",
		"deviceSN": "Compteur SN ",
		"deviceType": "Type de périphérique",
		"name": "Nom",
		"mac": "Nom du logiciel",
		"model": "Modèle",
		"communicationType": " Type de communication",
		"manufacturer": "Fabricant",
		"limiter": "Limiteur",
		"tou": "TOU",
		"stepTariff": "Tarif d’échelle",
		"schemeGroup": "Groupe de schémas de collecte",
		"meaGroup": "Groupe de mesure",
		"encrypt": "Encryptage",
		"ek": "EK",
		"ak": "AK",
		"llsPwd": "Mot de passe LLS",
		"authType": "Type d’authentication ",
		"port": "Port",
		"ip": "IP",
		"organation": "Organisation",
		"channel": "Canal",
		"schedule": "Calendrier",
		"firmVer": " Version de firmware",
		"pleaseChoose": "---Veuillez sélectionner---",
		"meterNullMsg": "Le compteur est nul",
		"commnuicatorNullMsg": "Le communicateur est nul",
		"pleSelDevice": "Veuillez sélectionner la liste des périphériques!",
		"ipValidErrorMsg": "Le format d’adresse de IP est incorrecte",
		"noDeviceFound": "Aucun périphérique trouvé",
		"deviceNumExists": "Le numéro de périphérique existe déjà",
		"pleSeleCOmmun": "Veuillez sélectionner un communicateur!",
		"pleSaveOrDel": "Veuillez sauvegarder les données ou supprimer les données!",
		"tCommunHChild": "Le concentrateur a une sub-donnée!",
		"pleSelOrganation": "Veuillez sélectionner une organisation!",
		"addr": "Adresse",
		"simNum": "Numéro de SIM",
		"meterTitle": "Compteur",
		"commTitle": "Concentrateur",
		"meterInfo": "Compteur",
		"commInfo": "Concentrateur",
		"commSN": "Concentrateur SN",
		"communicator": "Concentrateur ",
		"ct": "CT",
		"pt": "PT",
		"indexDcu": "Index dans DCU",
		"comPort": "Port de COM ",
		"keyMeter": "Compteur principal",
		"archivedMeter": "Compteur archivé",
		"changeSuccess": "Modifier avec succès!",
		"changeTitle": "Changement du type de communication du compteur",
		"longitude": "Longitude",
		"latitude": "Latitude",
		"setLongLat": "Configurer la latitude et la longitude",
		"server": "Serveur",
		"pleaseSelectedProfile": "Veuillez sélectionner le profil!",
		"over100": "Erreur! Le nombre de compteur dans HES atteint maintenant 100% du nombre de compteur dans votre license .",
		"over90": "Avertir. Le nombre de compteur dans HES atteint maintenant 90% du nombre  de compteur dans votre licence",
		"createTime": "",
		"dataCreateTime": "Date"
	},
	"divFMUPlanMeterList": {
		"pleSelDevType": "Veuillez sélectionner le type de périphérique!",
		"pleSelManufac": "Veuillez sélectionner le fabricant !",
		"pleEntPlanDesc": "Veuillez introduire la desciption du projet !",
		"pleSelPlanTime": "Veuillez sélectionner l’heure de début du projet !",
		"pleSelPlanExpTime": "Veuillez sélectionner l’heure d’expiration du projet!",
		"planExpPlanTime": "L’heure d’expiration du projet doit être postérieure à l’heure de début du projet !",
		"pleEntNewVer": "Veuillez saisir la nouvelle version !",
		"pleEntImgeIden": "Veuillez saisir l’identifiant de l’image!",
		"pleSelFirFile": "Veuillez sélectionner le fichier du micrologiciel!",
		"pleSelTaskStartTime": "Veuillez sélectionner l’heure de début de la tâche!",
		"pleSelTaskEndTime": "Veuillez sélectionner l’heure de fin de la tâche!",
		"taskStartLaterSTime": "L’heure de début de la tâche doit être égale ou postérieure à l’heure de fin de la tâche!",
		"pleSelTaskCyc": "Veuillez sélectionner le cycle de tâche !",
		"confCreatePlan": "Confirmer la création de ce projet?",
		"pleSelModel": "Veuillez sélectionner le modèle!",
		"plan": "Projet",
		"planReport": "Rapport de projet",
		"jobReport": "Rapport de tâche",
		"deviceSearch": "Recherche de périphérique",
		"manufacturer": "Fabricant",
		"model": "Modèle",
		"deviceSN": "Périphérique SN",
		"planCreation": "Création de projet",
		"planDesc": "Description de projet",
		"planStartTime": "Date de début de projet",
		"planExpTime": " Date d’expiration de projet",
		"newVersion": "Nouvelle Version",
		"imageIdentifier": "Identifant de l’image",
		"firmwareFile": "Fichier de firmware",
		"taskStartTime": " Heure de début de tâche",
		"taskEndTime": " Heure de fin de tâche",
		"taskCycle": "Cycle de tâche",
		"hour1": "1 heure",
		"hour2": "2 heures",
		"hour3": "3 heures",
		"hour4": "4 heures",
		"hour5": "5 heures",
		"hour6": "6 heures",
		"hour7": "7 heures",
		"hour8": "8 heures",
		"hour9": "9 heures",
		"hour10": "10 heures",
		"hour11": "11 heures",
		"hour12": "12 heures",
		"hour24": "24 heures",
		"deviceType": "Type de projet",
		"firmDeviceType":"Type de périphérique",
		"firmDeviceTypeOther":"Type de périphérique",
		"versionType": "Type de version ",
		"currentVersion": "Version actuelle",
		"expTimeAfterStaTime": "L’heure d’expiration du projet doit être postérieure à l’heure de début de projet!",
		"curTimeAfterExpTime": "L’heure d’expiration de projet doit être postérieur à l’heure actuelle!",
		"pleaEntNewVer": "Veuillez saisir la nouvelle version!",
		"pleaEntImaIdent": "Veuillez saisir l’identifiant de l’image!",
		"pleaSelFirmFile": "Veuillez sélectionner le fichier de firmware!",
		"pleaSelStartTime": "Veuillez sélectionner l’heure de début !",
		"pleaSelEndTime": "Veuillez sélectionner l’heure de fin!",
		"startTimeLaterThanEndTime": "L’heure de début de la tâche doit être égale ou postérieure à l’heure de fin de la tâche!",
		"pleaSelTaskCycle": "Veuillez sélectionner le cycle de tâche !",
		"pleaSelVersionType": "Veuillez sélectionner le type de version !",
		"midFV": "Version MID ",
		"appFV": "Version APP ",
		"confCreaPlan": "Confirmer pour créer ce plan?",
		"information": "Information",
		"commMeter": "Module GPRS du compteur ",
		"commCommunicatior": "Module de Communication de Communicateur",
		"broadcastMeter": "(Diffusion) Compteur",
		"broadcastComm": "(Diffusion) Communicateur ",
		"broadcastCommMeter": "(Diffusion) Module de communication de compteur",
		"broadcastCommCommunicatior": " Module CPL de DCU",
		"broadcastPlcComm": "(Diffusion) Module CPL de Communicateur",
		"broadcastHplcComm": "(Diffusion) Module CPL de compteur",
		"selectFile": "Sélectionner un fichier"
	},
	"addAssetGPRSMeter": {
		"sn": "SN",
		"comSn": "Communicateur SN",
		"refCommun": "Communicateur de référence",
		"referenceMeter": "Compteur de référence",
		"refSnIsEm": "Le numéro de série de compteur de référence est vide !",
		"successMatch": "Appairage réussi",
		"devTypeMis": " Discordance de type de périphérique",
		"pleMatchData": "Veuillez d’abord faire correspondre les données !"
	},
	"FMUPlanMeterList": {
		"title": "Liste de périphérique",
		"sn": "Compteur SN",
		"manufacturerName": "Fabricant",
		"modelName": "Modèle",
		"export": "Exporter",
		"print": "Imprimer",
		"fwVersion": "Version actuelle"
	},
	"FMUPlanMeterList0": {
		"title": "Lliste de périphérique",
		"sn": "Compteur SN",
		"manufacturerName": "Fabricant",
		"modelName": "Modèle",
		"currentVesion": "Version actuelle",
		"newVersion": "Nouvelle version",
		"startTime": "Heure de début",
		"export": "Exporter",
		"print": "Imprimer",
		"expiryTime": "Heure d’expiration"
	},
	"jobReportJobList": {
		"title": "Emplois",
		"opt": "Action",
		"id": "ID",
		"meterIdDfj": " ID de compteur",
		"deviceTypeDfj": " Type de périphérique",
		"snDfj": "Compteur SN",
		"manufacturerIdDfj": "Fabricant",
		"modelNameDfj": "Modèle",
		"currentVesionDfj": "Ancienne version",
		"newVersionDfj": "Nouvelle version",
		"startTimeDfj": " Heure de début",
		"expiryTimeDfj": " Heure d’expiration",
		"lastExecTimeDfj": " Heure de la dernière exécution",
		"stateDfj": "État",
		"blockSizeDfj": "Taille de bloc (octet)",
		"blockCountDfj": "Nombre de blocs transférés",
		"fwuProgressDfj": "Progression",
		"export": "Exporter",
		"print": "Imprimer",
		"confToCancJob": " Confirmer pour annuler ce travail?",
		"cancelled": "Annulé",
		"failedReasonDfj": "Raison",
		"runing": "En cours d’exécution",
		"done": "Fini",
		"cancel": " Annulé",
		"waiting": "En attente",
		"expired": "Expiré",
		"jobNoExcute": "Le travail n’a pas commencé à s’exécuter !"
	},
	"limiterGroupList": {
		"id": "Id",
		"type": "Type",
		"name": "Nom du groupe ",
		"protocolId": "Norme de compteur ",
		"meterNumber": "Quantité de compteur",
		"introduction": "Description",
		"meterCount": "Quantité de compteur ",
		"title": "Liste des groupes de seuil",
		"titleDetails": "Détails du groupe de limitation",
		"add": "Ajouter un groupe de limitation",
		"delete": "Supprimer",
		"pleaseSelectData": "Veuillez sélectionner un groupe dans la liste des groupes de limitation!",
		"dateError": "Veuillez saisir le format de date correct"
	},
	"limiterList": {
		"title": "Configuration du limiteur",
		"id": "Id",
		"name": "Item",
		"value": "Valeur",
		"meterCount": "Quantité de compteur",
		"getProgress": "Obtenir la progression",
		"day": "Jour",
		"week": "Semaine",
		"season": "Saison",
		"upcoming": "Prochain",
		"processing": "Traitement",
		"success": "Succès",
		"failed": "Échoué",
		"timeout": "Timeout",
		"cancelled": "Annulé",
		"meterCanBeEm": "Le compteur ne peut pas être vide",
		"choLeastCopRead": "Choisissez au moins une copie de lecture",
		"seleLeastOneData": "Sélectionnez au moins une donnée de table",
		"pleLimitCofVal": "Veuillez définir la valeur de configuration du limiteur!",
		"pleLimitCofValNo": "Veuillez définir que la valeur de configuration du limiteur est un chiffre!",
		"calGetPtogress": "Obtenir la progression selon le calendrier",
		"speDayGetPtogress": "Obtenir la progression selon les jours spéciaux",
		"stepTariffGetPtogress": "Obtenir la progression selon le tarif d’échelle"
	},
	"measurementGroupList": {
		"title": "Liste des groupes de mesures",
		"titleDetails": "Détails du groupe de mesure",
		"add": "Ajouter un groupe de mesure",
		"delete": "Supprimer",
		"id": "Id",
		"type": "Type",
		"name": "Nom du groupe",
		"protocolId": "Norme de compteur ",
		"referenceGroup": "Groupe de référence",
		"meterNumber": "Numéro de compteur",
		"introduction": "Description",
		"meterCount": "Quantité de compteur ",
		"editProfile": "Modifier le profil",
		"pleaseSelectData": "Veuillez sélectionner un groupe dans la liste des groupes de mesures!",
		"confirmToDel": "Confirmer la suppression de ce groupe?",
		"addProfile": "Ajouter un profil"
	},
	"meterDataReportList": {
		"frequency": "Fréquence",
		"headTitle": "Rapport des données de compteur",
		"intervalEnergy": "Rapport sur la qualité de l’ énergie",
		"times": "",
		"groupId": "Groupe",
		"channel": "Canal des données",
		"serizlName": "SN",
		"export": "Exporter",
		"print": "Imprimer",
		"graphVisualiz": "Visualisation de graphe",
		"noDataChannel": "Aucun canal de données sélectionné",
		"tableView": "Vue de tableau",
		"lineChart": "Graphique linéaire",
		"columnChart": "Histogramme",
		"reduction": "Actualiser",
		"saveAsPicture": "Enregistrer comme image",
		"mustSingTable": "Doit être une seule statistique de table",
		"pleSelColumn": "Veuillez sélectionner une colonne",
		"searchFor": "Rechercher pour...",
		"selectSn": "Veuillez sélectionner SN",
		"selectDeviceName": "Veuillez sélectionner le nom du périphérique.",
		"resultOver": "Plus de 200 jeux de résultats, veuillez resélectionner les critères!",
		"selectAll": "Sélectionner tout",
		"allSelected": "Tous sélectionnés",
		"onlyPleSelOneColumn": "Une seule colonne peut être sélectionnée pour la comparaison"
	},
	"planReportJobList": {
		"title": "Emplois",
		"opt": "Action",
		"id": "ID",
		"meterId": "ID de compteur",
		"sn": "SN de Compteur",
		"manufacturerName": "Fabricant",
		"modelName": "Modèle",
		"currentVesion": "Version actuelle",
		"newVersion": "Nouvelle version",
		"lastExecTime": "Heure de la dernière exécution",
		"state": "État",
		"blockSize": "Taille de bloc (octet)",
		"blockCount": "Nombre de blocs transférés",
		"fwuProgress": "Progression",
		"failedReason": "Raison",
		"export": "Exporter",
		"print": "Imprimer",
		"cancel": "Annuler",
		"confiCanCelJob": " Confirmer pour annuler ce travail ?"
	},
	"planReportList": {
		"title": "Projets",
		"opt": "Action",
		"id": "id",
		"deviceType": "Type de périphérique",
		"introduction": "Description de projet",
		"manufacturerId": "Fabricant",
		"modelName": "Modèle",
		"currentVesion": "Version actuelle",
		"newVersion": "Nouvelle version",
		"taskCycle": "Cycle de tâche",
		"filePath": "Nom du fichier",
		"startTime": "Heure de début",
		"expiryTime": " Heure d’expiration",
		"taskStartTimeStr": " Heure de début de la tâche",
		"taskEndTimeStr": " Heure de fin de la tâche",
		"state": "Si expiré",
		"done": "Fini",
		"expired": "Expiré",
		"running": "En cours d’exécution",
		"export": "Exporter",
		"print": "Imprimer",
		"cancelled": "Annulé",
		"cancel": "Annuler",
		"conToCanPlan": " Confirmer pour annuler ce projet?",
		"waiting": "En attente",
		"meter": "Compteur",
		"communicator": "Concentrateur",
		"valid": "Valide"
	},
	"profileList": {
		"title": "Liste des profils",
		"add": "Ajouter",
		"delete": "Supprimer",
		"id": "Id",
		"mgId": "ID de groupe de mesure",
		"profileId": " ID de profil",
		"profileType": "Type de profil",
		"profileType_view": "Type de profil",
		"profileName": "Nom du profil",
		"profileCycleType": "Type d’intervalle de profil",
		"profileCycle": "Intervalle de profil",
		"protocolCode": "Code OBIS ",
		"pleaseSelectData": "Veuillez sélectionner un profil dans la liste des profils !",
		"pleSelAntOne": "Ce profil a été ajouté, veuillez en sélectionner un autre!",
		"confirmToDel": " Confirmer la suppression de ce profil?"
	},
	"seasonList": {
		"id": "Id",
		"seasonName": " ID Saison",
		"startTime": "Heure de début",
		"weekProfile": "Profil de la semaine",
		"title": "Liste des saisons",
		"add": "Ajouter",
		"delete": "Supprimer",
		"month": "Mois",
		"dayofMonth": "Jour du mois",
		"year": "Année",
		"dayofWeek": "Jour de la semaine",
		"hour": "Heure",
		"minute": "Minute",
		"second": "Seconde",
		"pleaseSelectData": "Veuillez sélectionner une saison dans la liste des saisons!",
		"pleSetSeasonList": "Veuillez configurer le profil de la semaine dans la liste des saisons!",
		"seasonAreBeAtMost": "255 saisons peuvent être créées au maximum!",
		"seleLeastOneData": " Sélectionnez au moins une donnée de table!",
		"noMeterGroup": "Veuillez vérifier que les propriétés du groupe de compteurs ont été configurées ou non! "
	},
	"selectedDataChannelList": {
		"title": "Canaux de données sélectionnés",
		"up": "Haut",
		"down": "Bas",
		"delete": "Supprimer",
		"id": "Id",
		"mgId": "ID de groupe de mesure",
		"profileId": "ID de profil",
		"dataitemId": "ID de l’élément de données",
		"dataitemName": "Canal des données",
		"sortId": "ID de tri"
	},
	"specialDayList": {
		"title": "Liste des jours spéciaux",
		"id": "Id",
		"specialDayName": "ID de jour spécial ",
		"date": "Date",
		"dayProfile": "Profil du jour",
		"month": "Mois",
		"dayofMonth": "Jour du mois",
		"year": "Année",
		"dayofWeek": "Jour de la semaine",
		"pleaseSelectData": "Veuillez sélectionner un jour dans la liste des jours spéciaux!",
		"pleSpeDayDprofile": "Veuillez définir le profil du jour de la liste des jours spéciaux!",
		"daysBeAtMost": "255 jours peuvent être créés au maximum!",
		"dayAssCanBeDel": "Le jour est associé à une liste de jours spéciaux qui ne peut pas être supprimée!"
	},
	"sysIntegrationLogList": {
		"title": "Journal d’intégration du système",
		"detailTitle": "Journal détaillé de l’intégration du système",
		"id": "ID",
		"sn": "SN",
		"fromId": "Source de la requête",
		"tv": "Heure de la requête",
		"requestType": "Type de requête",
		"requestSubType": "Sous-type de requête",
		"responseResult": "Résultat de réponse",
		"requestDetail": "Détail de requête",
		"responseDetail": "Détail de réponse",
		"export": "Exporter",
		"print": "Imprimer",
		"status": "État"
	},
	"timeSynchronizationLog": {
		"title": "Journal de synchronisation de temps",
		"id": "ID",
		"sn": "SN",
		"tv": "Heure de tâche",
		"strSynResult": "Résultat de synchronisation ",
		"meterTv": " Heure de compteur",
		"systemTv": " Heure de système ",
		"synTv": "Synchronisation de temps",
		"failedReason": "Cause échouée ",
		"export": "Exporter",
		"print": "Imprimer"
	},
	"touGroupList": {
		"id": "Id",
		"title": "Gestion de groupe de compteur",
		"touTitle": "Liste de groupe de TOU ",
		"touTitle_": "Détails du groupe de TOU",
		"measurement": "Mesure",
		"tou": "TOU",
		"limiter": "Limiteur",
		"addTouGroup": "Ajouter un groupe de TOU ",
		"calendar": "Calendrier",
		"specialDay": "Jour spécial",
		"delete": "Supprimer",
		"type": "Type",
		"name": "Nom du groupe",
		"protocolId": "Compteur standard",
		"meterNumber": "Numéro du compteur",
		"introduction": "Description",
		"meterCount": "Quantité de compteur",
		"referenceGroup": "Groupe de référence",
		"pleaseSelectData": "Veuillez sélectionner un groupe dans la liste des groupes TOU!",
		"canNotBeDel": "Ne peut pas être supprimé!",
		"confirmToDel": "Confirmer la suppression de ce groupe?"
	},
	"tracingLogList": {
		"headTitle": "Gestion des groupes de compteurs",
		"meterTracingLog": "Journal de suivi de mesure",
		"tracingLog": "Journal de suivi",
		"timeSynLog": "Journal de sychronisation de temps",
		"sysInteLog": "Journal d’intégration de système",
		"userLog": "Journal de l’utilisateur",
		"deviceSn": "Périphérique SN",
		"service": "Service",
		"logLevel": "Niveau de journal",
		"startTime": "Heure de début",
		"endTime": " Heure de fin",
		"reqType": "Type de requête ",
		"user": "Utilisateur",
		"logType": "Type de journal",
		"logSubType": "Sous-type de journal",
		"clikcSearchBut": "Cliquez sur le bouton de recherche pour rechercher",
		"sychResult": "Résultat de synchronisation ",
		"title": "Journal de suivi",
		"export": "Exporter",
		"print": "Imprimer",
		"id": "ID",
		"date": " Heure de requête",
		"serviceId": "Service",
		"type": "Type de journal",
		"level": "Niveau de journal",
		"content": "Contenu",
		"success": "Succès",
		"failed": "Échoué",
		"normal": "Normal"
	},
	"userLogList": {
		"title": "Journal d’utilisateur",
		"userId": "Id d’utilisateur ",
		"tv": "Durée de fonctionnement",
		"name": "Utilisateur",
		"logType": "Type de journal",
		"logSubType": "Sous-type de journal",
		"detail": "Contenu",
		"startTimeCanNot": "L’heure de début ou l’heure de fin ne peut pas être vide!",
		"logTimeBeWDay": " L’heure de requête de journal doit être inférieure à un jour!"
	},
	"unSelectedDataChannelList": {
		"title": "Canaux de données en option",
		"title1": "Lecture des canaux de données",
		"profileInterval": "Intervalle de profil",
		"add": "Ajouter",
		"id": "Id",
		"mgId": "ID de groupe de mesure",
		"profileId": "ID de profil",
		"dataitemId": "ID de l’élément de données",
		"dataitemName": "Canal des données",
		"sortId": "ID de tri",
		"pleaseSelectData": "Veuillez sélectionner un canal de données dans cette liste!",
		"hasMovedToTheTop": "Déjà déplacé en haut!",
		"hasMovedToTheFoot": "Déjà déplacé en bas!"
	},
	"weekList": {
		"id": "Id",
		"weekName": " ID de semaine",
		"dayProfile": "Profil de jour",
		"add": "Ajouter",
		"delete": "Supprimer",
		"title": "Liste de semaine",
		"pleaseSelectData": "Veuillez sélectionner une semaine dans la liste des semaines!",
		"pleSetWeekList": "Veuillez configurer le profil du jour dans la liste des semaines!",
		"youCanSingleDay": "Vous ne pouvez pas supprimer un seul jour!",
		"seasonListCanBeDel": "La semaine est associée à la liste des saisons qui ne peut pas être supprimée!",
		"weeksToBeAtMost": "255 semaines peuvent être créées au maximum!",
		"weekCanBeDel": "Le jour est associé à la liste des semaines qui ne peut pas être supprimée!"
	},
	"sysUserList": {
		"headTitle": "Utilisateur & Rôle",
		"title": "Liste d’utilisateur ",
		"orgPassword": "Mot de passe d’origine",
		"newPassword": "Nouveau mot de passe",
		"confirm": "Confirmer",
		"enterNewPasswordAgain": "Veuillez saisir encore une fois le nouveau mot de passe ",
		"askYouForgotPwd": "Demandez de l’aide à votre administrateur système si vous avez oublié votre mot de passe.",
		"id": "ID",
		"username": "Compte",
		"name": "Nom",
		"email": "Mail",
		"mobilePhone": "No. de téléphone ",
		"userState": "État",
		"userType": " Type d’utilisateur",
		"roleId": "Rôle",
		"orgId": "Organisation",
		"lastLoginTime": "Heure de la dernière connexion",
		"opt": "Action",
		"user": "Utilisateur",
		"role": "Rôle",
		"organization": "Organisation",
		"disable": "Désactiver",
		"enable": "Activer",
		"addUser": "Ajouter un utilisateur",
		"delUser": "Supprimer un utilisateur",
		"resPwd": "Réinitialiser le mot de passe",
		"oldPasswordError": "Erreur du mot de passe d’origine!",
		"newPasswordError": "Le nouveau mot de passe et la confirmation du mot de passe sont incohérents!",
		"resetPassSucc": "Réinitialiser le mot de passe avec succès!",
		"confirmDisable": "Confirmez pour désactiver ce compte utilisateur?",
		"confirmEnable": "Confirmez pour activer ce compte utilisateur?",
		"selectUser": "Veuillez sélectionner un utilisateur!",
		"myProfile": "Mon profil",
		"passSet": "Configuration de mot de passe",
		"licenseSet": "Configuration de licence",
		"administrator": "Admininistrateur",
		"normal": "Normal",
		"password": "Mot de passe",
		"pleaEnterPass": "Veuillez saisir votre mot de passe",
		"roleName": "Rôle",
		"validatePwd": "La longueur du mot de passe est de 8 à 15 bits, qui doit contenir des lettres et des chiffres, les lettres sont sensibles à la casse.",
		"originConfirmRepeat": "Le mot de passe d’origine n’est pas le même que le nouveau mot de passe!",
		"passwordTooLong": "La longueur du mot de passe est trop longue, veuillez saisir moins de 15 bits",
		"originPasswordError": "Erreur du mot de passe d’origine.",
		"accountNotExist": "Le compte existe pas."
	},
	"sysRoleList": {
		"title": " Liste des rôles",
		"id": "Id",
		"name": "Nom",
		"userCount": "Compte d’utilisateur",
		"description": "Description",
		"delete": "Supprimer",
		"addRole": "Ajouter un rôle",
		"rolenameNull": "Le nom ne peut pas être vide!",
		"selectRole": "Veuillez sélectionner un rôle!",
		"deleteRole": "Confirmer la suppression de ce rôle?",
		"queryResultNull": "Le résultat de la requête est nul!",
		"unableDel_user": "Le rôle a des sous-utilisateurs qui ne peuvent pas être supprimés!",
		"operaConfig": " Configuration d’opération",
		"roleNameExist": "Le nom du rôle existe déjà!"
	},
	"meterConfigurationList": {
		"systemFunction": "Fonction de système",
		"title": "Configuration d’opération",
		"id": "Id",
		"isSelect": "Sélectionné",
		"operationname": "Élément d’opération",
		"description": "Description"
	},
	"organizationList": {
		"title": "Liste des organisations",
		"addOrganization": "Ajouter une organisation",
		"delete": "Supprimer",
		"name": "Nom",
		"id": "Id",
		"userCount": "Compte d’utilisateur",
		"meterCount": "Quantité de compteur",
		"mobile": "No. de téléphone",
		"address": "Adresse",
		"description": "Description",
		"contactMan": "Contact"
	},
	"sysOrganizationList": {
		"id": "Id",
		"parentOrg": "Organisation de parenté",
		"name": "Nom",
		"mobile": "Numéro de téléphone",
		"address": "Adresse",
		"description": "Description",
		"unableDel_org": "L’organisation a des sous-organisations qui ne peuvent pas être supprimées!",
		"unableDel_comm": "L’organisation a des sous-communicateurs qui ne peuvent pas être supprimés!",
		"unableDel_meter": "L’organisation a des compteurs secondaires qui ne peuvent pas être supprimés!",
		"unableDel_user": "L’organisation a des sous-utilisateurs qui ne peuvent pas être supprimés!",
		"moveOrg": "Déplacer l’organisation",
		"moveOrgError": "Impossible de sélectionner l’organisation elle-même!",
		"selectOrg": "Veuillez sélectionner une organisation !",
		"organization": "Organisation",
		"addOrganization": "Ajouter une organisation",
		"moveSuccess": "Déplacer avec succès",
		"admitFourLevelOrg": "4 niveaux d’organisations peuvent être créés au maximum!",
		"deleteOrg": "Confirmer la suppression de cette organisation?",
		"noMoveToChildOrg": "L’organisation n’est pas autorisée à se déplacer vers son organisation subordonnée!"
	},
	"device_read_result_list": {
		"id": "Id",
		"sn": "Numéro de série",
		"profileId": "ID de profil",
		"profileName": "Profil",
		"communication": "Communication",
		"requestTime": "Heure de requête",
		"dataChannel": "Canal des données",
		"value": "Valeur",
		"reponseTime": " Heure des données",
		"startTime": " Heure de début",
		"endTime": " Heure de fin",
		"status": "État",
		"statusView": "État",
		"export": "Exporter",
		"print": "Imprimer"
	},
	"device_read_schedule_list": {
		"id": "Id",
		"sn": "Numéro de série",
		"model": "Modèle",
		"communication": "Communication",
		"profileId": "Profilé",
		"profile": "Profil",
		"startTime": "Heure de début",
		"endTime": "Heure de fin"
	},
	"device_read_channel_list": {
		"title": "Résultats",
		"checkReadTitle": "Cliquez sur le bouton de lecture pour lire",
		"id": "Id",
		"sn": "Compteur SN",
		"communicator": "Concentrateur SN",
		"commmunication": "Commmunication",
		"dataChannel": "Canal des données",
		"value": "Valeur",
		"status": "État",
		"statusView": "État",
		"requestTime": " Heure de requête",
		"responseTime": " Heure de réponse ",
		"export": "Exporter",
		"print": "Imprimer",
		"total": "Total",
		"completed": "Terminé",
		"success": "Succès",
		"failed": "Échoué",
		"processing": "Traitement en cours",
		"read": "Lire",
		"cancel": "Annuler",
		"cancelFailure": "Annuler l’échec"
	},
	"deploymentAndClusterManagement": {
		"title": "Déploiement et gestion de cluster",
		"serviceConfig": "Configuration du serveur et du service",
		"channelService": "Détails de configuration",
		"name": "Nom",
		"ip": "IP",
		"hostId": "ID d’hôte",
		"server": "Serveur",
		"type": "Type",
		"ipOrHostId": "IP/ID d’hôte",
		"status": "État",
		"properties": "Propriétés",
		"value": "Valeur",
		"description": "Description",
		"addServer": "Ajouter un serveur",
		"addService": "Ajouter un service",
		"deleteServer": "Supprimer le serveur",
		"deleteService": "Supprimer le service",
		"action": "Action",
		"save": "Sauvegarder",
		"editChannel": "Modifier le canal",
		"unableDel_server": "Le serveur a des sous-services qui ne peuvent pas être supprimés!",
		"unableDel_service": "Le service a des sous-propriétés qui ne peuvent pas être supprimées!",
		"serviceExistSystem": "Ce service existe déjà dans le système!",
		"serviceExistServer": "Ce service existe déjà sur le serveur!",
		"ipExistSystem": "Cette IP existe déjà dans le système!",
		"hostIdExistServer": "Cet ID d’hôte existe déjà sur le serveur!",
		"pleaseSelectService": "Veuillez sélectionner un service!",
		"pleaseAddChannel": "Veuillez ajouter une liste de services de canal!",
		"nameExist": "Ce nom existe déjà dans le système!",
		"stop": "Stop",
		"running": "En cours d’exécution",
		"channel": "Canal",
		"messageBus": " Bus de message",
		"schedule": "Calendrier",
		"UCI": "UCI",
		"calculation": "Calcul",
		"application": "Application",
		"onlineTime": "Heure en ligne"
	},
	"serviceConfig": {
		"title": "Configuration de service",
		"id": "Id",
		"parent": "ID de parent",
		"serviceTypeTemp": "Type de service ",
		"introduction": "Nom",
		"serviceType": "Type",
		"ip": "IP/ID d’hôte ",
		"isOnline": "État",
		"delete": "Supprimer"
	},
	"meterDataEventExport": {
		"title": "Exportation des données et des événements du compteur",
		"dataChannel": "Canal des données",
		"exportProgress": "Progression de l’exportation",
		"exportTime": "Heure d’exportation",
		"result": "Résultat",
		"unableDel_server": "Le serveur a des sous-services qui ne peuvent pas être supprimés!",
		"unableDel_service": "Le service a des sous-propriétés qui ne peuvent pas être supprimées!",
		"serviceExistSystem": "Ce service existe déjà dans le système!",
		"serviceExistServer": "Ce service existe déjà sur le serveur!",
		"ipExistSystem": "Cette IP existe déjà dans le système!",
		"hostIdExistServer": "Ce ID d’hôte existe déjà sur le serveur!",
		"pleaseSelectService": "Veuillez sélectionner un service!",
		"pleaseAddChannel": "Veuillez ajouter une liste de services des canals!",
		"nameExist": "Ce nom existe déjà dans le système!",
		"dstDataitemId": "ID de canal MDM",
		"timeType": "Type de temps"
	},
	"stepTariffList": {
		"title": "Liste des groupes de tarif d’échelle",
		"titleDetails": "Détails des groupes de tarif d’échelle",
		"id": "Numéro",
		"stepTariff": "Tarif d’échelle",
		"description": "Description",
		"groupName": "Nom du groupe",
		"stepName": "Nom d’échelle",
		"meterStandard": "Norme de compteur ",
		"startQuantity": "Quantité d’énergie de début (kWh)",
		"meterNumber": "Numéro de compteur",
		"endQuantity": "Quantité d’énergie de fin (kWh)",
		"activateTime": "Activer le temps",
		"price": "Prix($)",
		"referenceGroup": "Groupe de référence",
		"addGroup": "Ajouter un groupe de tarif d’échelle",
		"addStep": "Ajouter une échelle",
		"pleaseSelectData": "Veuillez sélectionner un tarif d’échelle dans cette liste!",
		"nameExist": "!",
		"stepNameExist": "Le nom du groupe de tarif d’échelle existe déjà!",
		"selectStepTariff": "Veuillez sélectionner un groupe de tarif d’échelle!",
		"deleteStepTariff": "Confirmer la suppression de ce groupe de tarif d’échelle?",
		"selectStep": "Veuillez sélectionner une échelle!",
		"delStep": "Confirmer la suppression de cette échelle?",
		"endGreaterThanStart": "La quantité d’énergie de fin doit être supérieure à la quantité d’énergie de début!",
		"pleaseAddStep": "Veuillez ajouter une liste d’échelle!",
		"pleaseCorrectFormat": "Veuillez saisir les données de format correctes!",
		"reselectStepName": "Veuillez sélectionner à nouveau le nom de l’échelle!",
		"startQuantityError": "Il doit être cohérent avec la quantité d’énergie de fin de l’étape précédente !",
		"delete": "Supprimer",
		"save": "Sauvegarder",
		"stepTariffGetPtogress": "Progression de tarif d’échelle",
		"friendly": "Amical",
		"prepay": "Prépaiement",
		"touTariff": "Tarif TOU",
		"friendlyPeriod": "Période amicale",
		"friendlyWeekDay": "Journée de la semaine amicale",
		"friendlySpecialDay": "Journée spéciale amicale",
		"friendlyTou": " TOU amical",
		"periodId": " ID de période",
		"start": "Début",
		"end": "Fin",
		"weekDayId": " ID de jour de semaine",
		"weekDay": "Jour de travail",
		"specialDayId": "ID du jour spécial",
		"enable": "Activer",
		"touId": " ID de TOU",
		"prepayDetail": "Détail de prépaiement",
		"touAtMost": "4 Tarifs TOU peuvent être créés au maximum",
		"weekAtMost": "7 jours peuvent être créés au maximum",
		"periodAtMost": "8 périodes peuvent être créées au maximum",
		"startLaterEnd": "L’heure de début doit être postérieure à l’heure de fin!",
		"touExist": "Déjà existe",
		"periodRepeat": "Les périodes amicales ne doivent pas se chevaucher",
		"stepExist": "Le prix de l’échelle existant ne peut pas ajouter le prix TOU",
		"cannotOp": "Ne peut pas fonctionner plus que le temps d’activation"
	},
	"meterConfiguration": {
		"title": "Configuration du compteur",
		"canNotBeDelete": "La tâche à l’état de traitement ne peut pas être supprimée!",
		"pleaseSelectData": "Veuillez sélectionner une tâche dans cette liste!",
		"confirmDeleteTask": " Êtes-vous sûr de supprimer cette tâche?",
		"stepNameExist": "Ce nom d’échelle a été ajouté, veuillez en sélectionner un autre!"
	},
	"commGroupUpgrade": {
		"paramTitle": "Option de paramètre de périphérique",
		"paramFile": "Fichier de paramètres",
		"set": "Configuration",
		"dcuNS": "DCU SN",
		"setFirst": "Veuillez d’abord configurer le paramètre de DCU",
		"setParam": "Configurer le paramètre DCU",
		"viewParam": "Afficher le paramètre XML de DCU"
	},

	"meterGroupUpgrade": {
		"plan": "Projet",
		"planReport": "Rapport de projet",
		"jobReport": "Rapport de travail",
		"deviceSearch": "Recherche de périphérique",
		"groupType": " Type de groupe",
		"group": "Groupe",
		"measurement": "Mesure",
		"TOU": "TOU",
		"limiter": "Limiteur",
		"stepTariff": "Tarif d’échelle",
		"friendly": "Amical",
		"search": "Chercher",
		"deviceSN": "Compteur SN",
		"pleSelGroupType": "Veuillez sélectionner le type de groupe!",
		"pleSelGroup": "Veuillez sélectionner le groupe!",
		"pleEntPlanDesc": "Veuillez saisir la description de projet!",
		"noDeviceFound": "Veuillez vérifier si le fichier de compteur que vous avez sélectionné est associé au projet ou vérifiez si le projet sélectionné correspond aux informations du fichier d’actif!",
		"startEqualExpiry": "L’heure d’expiration du projet doit être postérieureà l ’heure de début du projet!",
		"taskStartEqualEnd": " L’heure de début de la tâche doit être égale ou postérieure à l ’heure de fin de la tâche!",
		"ifExpried": "Si expiré",
		"valid": "Valide",
		"expired": "Expiré",
		"startTime": "Heure de début",
		"endTime": " Heure de fin",
		"noFoundPlan": "Aucun projet trouvé!",
		"pleaseSelectAplan": "Veuillez sélectionner un projet!",
		"pleaseSelectState": "Veuillez sélectionner un projet expiré et un travail en attente ou expiré!",
		"createPlanSucc": "Créé avec succès, le projet nouvellement créé peut être consulté dans l’onglet de rapport de projet.",
		"exceptionMsg": "Le service de travail appelé est anormal, veuillez vérifier et créer à nouveau le projet!",
		"jobUpgradeSucc": "Tous les travaux de ce projet ont été mis à jour avec succès!",
		"all": "Tous",
		"pleaseSelModel": "Veuillez sélectionner le modèle!",
		"hasMeter": "Ce groupe ayant des compteurs ne peut pas être supprimé"
	},
	"divMGU_PlanMeterList": {
		"sn": "Compteur SN",
		"manufacturer": "Fabricant",
		"model": "Modèle",
		"groupType": "Type de groupe ",
		"group": "Groupe",
		"startTime": "Date de début de projet",
		"expiryTime": "Date d’expiration de projet"
	},
	"MGU_planReportList": {
		"opt": "Action",
		"introduction": "Description de projet",
		"groupType": "Type de groupe",
		"groupName": "Nom du groupe",
		"model": "Modèle",
		"state": "État",
		"taskCycle": "Cycle de tâche(Heure)",
		"done": "Fini",
		"expired": "Expiré",
		"running": "En cours d’exécution",
		"waiting": "En attente",
		"startTime": "Date de début de projet",
		"expiryTime": "Date d’expiration de projet",
		"taskStartTime": "Heure de début de la tâche",
		"taskEndTime": "Heure d’expiration de tâche"
	},
	"MGU_planReportJobList": {
		"title": "Emplois",
		"id": "ID",
		"meterId": "ID du compteur",
		"sn": "Compteur SN",
		"manufacturer": "Fabricant",
		"model": "Modèle",
		"status": "État",
		"lastExecTime": "Heure de la dernière exécution",
		"reason": "Raison",
		"running": "En cours d’exécution",
		"done": "Fini",
		"cancel": "Annulé",
		"waiting": "En attente",
		"expired": "Expiré",
		"export": "Exporter",
		"print": "Imprimer",
		"failedReason": "Raison",
		"addPlan": "Ajouter un projet"
	},
	"connOrDisconnList": {
		"title": "Résultats",
		"mainTitle": "Connecter / Déconnecter",
		"sn": "Compteur SN",
		"commSN": "Concentrateur SN",
		"communication": "Communication",
		"command": "Commande",
		"status": "État",
		"requestTime": "Heure de requête",
		"responseTime": "Heure de réponse ",
		"connect": "Connecter",
		"disconnect": "Déconnecter",
		"pleSelMeter": "Veuillez sélectionner un compteur!",
		"export": "Exporter",
		"print": "Imprimer",
		"readTimeOut": "Délai de lecture expiré",
		"failReason": "Raison",
		"relayOptional": "Relais en option",
		"internal": "Relais principal",
		"external": "Relais d’extension 1"

	},
	"billingReportList": {
		"reports": "Rapports",
		"reportList": "Liste de rapports",
		"startTime": "Heure de début",
		"endTime": "Heure de fin",
		"organization": "Organisation",
		"lineLossObjectType": "Type",
		"timeType": "Type de temps",
		"lineLossObject": "Objet de perte de ligne",
		"import": "Import(KWh)",
		"export": "Export(KWh)",
		"loss": "Perte(KWh)",
		"rate": "Taux(%25)",
		"date": "Date",
		"lineLossStartTimeTip": "Veuillez sélectionner l’heure de début!",
		"lineLossEndTimeTip": "Veuillez sélectionner l’heure de fin!",
		"timeIssueAlert": "L’heure de fin doit être postérieure à l’heure de début!",
		"transformer": "Transformateur",
		"line": "Ligne",
		"meter": "Compteur",
		"communicator": "Concentrateur",
		"daily": "Journalier",
		"monthly": "Mensuel",
		"title": "Rapport de facturation de client",
		"sn": "Compteur SN",
		"time": "",
		"energy": "Consommation d’énergie(KWh)",
		"consumption": "Montant d’énergie(USD)",
		"rateOther": "Taux(%)",
		"serach": "Rechercher",
		"reportTime": "Heure du rapport",
		"assetSelectTip": "Veuillez sélectionner l’actif d’abord!"
	},
	"lineLossReportList": {
		"reportList": "Liste de Rapport ",
		"startTime": "Heure de début",
		"endTime": "Heure de fin",
		"organization": "Organisation",
		"lineLossObjectType": "Type",
		"timeType": "Type de temps",
		"lineLossObject": "Objet de perte de ligne",
		"import": "Import(KWh)",
		"export": "Export(KWh)",
		"loss": "Perte (KWh)",
		"rate": "Taux(%25)",
		"date": "Date",
		"lineLossStartTimeTip": "Veuillez sélectionner l’heure de début!",
		"lineLossEndTimeTip": "Veuillez sélectionner l’heure de fin!",
		"timeIssueAlert": "L’heure de fin doit être postérieure à l’heure de début!",
		"transformer": "Transformateur",
		"line": "Ligne",
		"meter": "Compteur ",
		"communicator": "Concentrateur",
		"daily": "Journalier",
		"monthly": "Mensuel",
		"title": "Rapport de perte de linge",
		"rateOther": "Taux(%)",
		"type": "Type",
		"meterName": "Nom du compteur",
		"tv": "",
		"name": "Canal des données",
		"dataValue": "Valeur de données",
		"objectName": "Objet de perte de ligne",
		"entity": "Entité",
		"entityName": "Nom de l’entité",
		"objectComparison": "Comparaison d’objets",
		"yoyComparison": "YoY comparaison",
		"qoqComparison": "QoQ comparaison",
		"momComparison": "MoM comparaison",
		"cancelComparison": "Annuler la comparaison",
		"yoy": "YoY",
		"qoq": "QoQ",
		"mom": "MoM"
	},
	"importAndExportReport": {
		"organization": "Organisation",
		"timeType": "Type de temps",
		"dataType": "Type de données",
		"startTime": "Heure de début",
		"endTime": "Heure de fin",
		"result": "Résultat",
		"time": "",
		"value": "Valeur(kWh)",
		"meterSn": "Compteur SN",
		"import": "Import",
		"export": "Export",
		"detail": "Détail",
		"noRecords": "Aucun enregistrement",
		"timeNotExist": "L’ horodatage n’existe pas!",
		"title": "Rapport de statistiques d’approvisionnement",
		"curveType": "Type de courbe"
	},
	"lineManagementList": {
		"title": "Ligne",
		"lineSn": "Ligne SN",
		"lineName": "Nom de ligne",
		"lineList": "Liste de ligne",
		"name": "Nom",
		"organization": "Organisation",
		"type": "Type",
		"voltageLevel": "Niveau de tension",
		"properties": "Propriétés",
		"basicInformation": "Informations de base",
		"transformers": "Transformateurs",
		"calculationObject": "Object de calcul ",
		"sn": "SN",
		"transformerList": "Liste de transformateur",
		"transformerSn": "Transformateur SN",
		"calObjList": "Liste d’objet de calcul",
		"calculationObjectName": "Nom d’objet de calcul ",
		"cycle": "Cycle",
		"calculationObjectProperties": "Propriétés d’objet de calcul",
		"dataChannelList": "Liste de canal de données ",
		"meterSn": "Compteur SN",
		"meterName": "Nom du compteur",
		"dataChannel": "Canal des données",
		"addDataChannel": "Ajouter un canal de données",
		"addDataChannelFromTransformers": "Ajouter un canal des données à partir des transformateurs",
		"addLine": "Ajouter une ligne",
		"opt": "Action",
		"busbar": "Barre omnibus",
		"feeder": "Branchement",
		"unit": "kV",
		"meterNotExist": "Aucun compteur trouvé!",
		"noCalObjFound": "Aucun objet de calcul trouvé!",
		"nolineFound": "Aucune ligne trouvée!"
	},
	"transformerManagementList": {
		"title": "Transformateur",
		"transformerSn": "Transformateur SN",
		"transformerName": " Nom du transformateur",
		"transformerList": " Liste des transformateurs",
		"name": "Nom",
		"organization": "Organisation",
		"ratedCapacity": "Capacité nominale",
		"properties": "Propriétés",
		"basicInformation": "Informations de base ",
		"calculationObject": "Objet de calcul",
		"sn": "SN",
		"address": "Adresse",
		"calculationObjectList": "Liste des objets de calcul",
		"calculationObjectName": "Nom de l’objet de calcul",
		"type": "Type",
		"cycle": "Cycle",
		"calculationObjectProperties": "Propriétés d’objet de calcul",
		"dataChannelList": "Liste de canal des données ",
		"meterSn": "Compteur SN",
		"meterName": "Nom du compteur",
		"dataChannel": "Canal des données",
		"addDataChannel": "Ajouter un canal de données",
		"noTrFound": "Aucun transfomateur trouvé!",
		"coolingType": "Type de refroidissement",
		"measurementPoint": "Point de meure"
	},
	"calculationObjectList": {
		"define": "Définir l’objet de calcul",
		"title": "Objet de calcul",
		"name": "Nom",
		"type": "Type d’objet",
		"cycle": "Cycle",
		"entityType": "Type d’entité",
		"entityName": "Nom de l’entité",
		"calculationObject": "Objet de calcul",
		"properties": "Propriétés",
		"calculationObjectList": "Liste des objets de calcul",
		"meterSn": "Compteur SN",
		"dataChannel": "Canal des données",
		"calcFormula": "Formule de calcul",
		"addDataChannel": "Ajouter un canal de données",
		"lineLoss": "Perte de ligne",
		"import": "Résumé",
		"export": "Ventes",
		"daily": "Journalier",
		"monthly": "Mensuel",
		"line": "Ligne",
		"transformer": "Transformateur",
		"organization": "Organisation",
		"pleaseChoose": "---Veuillez sélectionner---",
		"all": "Tous",
		"addBySearch": "Ajouter par recherche",
		"addFromRelationship": "Ajouter à partir de la relation",
		"addMeteringPoint": "Ajouter un point de mesure",
		"defaultType": "Type par défaut",
		"defaultDataChannel": "Canal de données par défaut",
		"gridLossReport": "Rapport de perte de réseau",
		"rangeOfLossRate": "Gamme de taux de perte (%)",
		"reference": "Référence",
		"calc": "Activer le calcul",
		"calcScheme": "Type de projet",
		"sdp": "SDP",
		"object": "Objet",
		"linkSdp": "Lien SDP",
		"linkObject": "Object de lien",
		"linkType": "Type de lien",
		"byAsset": "Actif associé",
		"assetType": "Type d’actif",
		"industry": "Industrie"
	},

	"dcuConfiguration": {
		"concentratorIpAddress" : "Adresse IP de concentrateur",
		"activeCollect": "Peut lire le compteur",
		"passiveCollect": "Peut lire le compteur directement",
		"startedTime": "Heure de début",
		"delayTime": "Temps retardé",
		"switch": "Basculer",
		"searchMeterSwitch": "Rechercher le commutateur de compteur",
		"title": "Configuration DCU",
		"meterListUpload": "Paramètre de liste des compteurs",
		"otherParameter": "Autre paramètre",
		"communicatorSn": "Concentrateur SN",
		"set": "Écrire",
		"meterTitle": "Liste des compteurs",
		"meterTitleHES": "Liste des compteurs (HES)",
		"meterTitleDCU": "Liste des compteurs (Lecture depuis DCU)",
		"meterSn": "Compteur SN",
		"pointNum": "Numéro de point",
		"logicalName": "Nom de logiciel",
		"baudRate": "Débit en bauds",
		"comNum": "Numéro de COM",
		"isVip": "Utilisateur clé",
		"protocol": "Protocol",
		"communicatorAddress": "Adresse de concentrateur",
		"result": "Résultat",
		"communication": "Communication",
		"status": "État",
		"requestTime": "Heure de requête",
		"responseTime": "Heure de réponse",
		"reason": "Raison",
		"get": "Lire",
		"hesCommunicationParameter": "Paramètre de communication HES",
		"gprsNetworkParameter": " Paramètre de réseau GPRS",
		"clock": "Horloge",
		"Synchronization Current Time":"Synchronisation du temps actuel",
		"concentratorVersion": "Informations sur la version du concentrateur",
		"encryptionMode": "Mode de cryptage",
		"encryptionType": "Type de cyptage",
		"encryptionKey": "Clé de cyptage",
		"globalEncryption": "Cryptage global",
		"dedicatedEncryption": "Cryptage dédié",
		"unencryted": "Non crypté",
		"ipAddress": "Adresse IP",
		"port": "Port",
		"userName": "Nom d’utilisateur ",
		"password": "Mot de passe",
		"dialedNumber": "Numéro composé",
		"ipAddressBackup": "IP adresse - Backup",
		"portBackup": "Port - Backup",
		"apn": "APN",
		"resetCommand": "Commande de réinitialisation",
		"resetType": "Type de réinitialisation",
		"pleaseChooseCommSn": "Veuillez sélectionner comm SN!",
		"pleaseFillIn": "Veuillez remplir les informations!",
		"pleaseFillInCorrect": "Veuillez remplir les informations correctes!",
		"pleaseChoose": "---Veuillez sélectionner---",
		"hardwareReset": "Réinitialisation de hardware",
		"dataBaseInit": "Initialisation de base des données",
		"parameterResetExceptComm": "Réinitialisation de paramètre(sauf communication)",
		"dataReset": "Réinitialisation des données",
		"parameterAndDataReset": "Réinitialisation de paramètre",
		"parameterExceptHesReset": "Réinitialiser les paramètres (sauf paramètres de communication HES) ",
		"importantUserSettingList": "Paramètre d’utilisateur important",
		"meterModel": "Modèle de compteur",
		"importantFlag": "Flag de compteur clé",
		"concentratorimportantFlag": "Flag de compteur clé - Concentrateur",
		"command": "Commande",
		"resetTypeCannotBeRead": "Le type de réinitialisation ne peut pas être lu",
		"itemCannotBeRead": "L’élément ne peut pas être lu",
		"selectCommunicators": "Veuillez sélectionner les concentrateurs.",
		"selectImportantUser": "Veuillez sélectionner le compteur que vous souhaitez définir comme utilisateur important.",
		"selectResetType": "Veuillez sélectionner le type de réinitialisation.",
		"wiringMethod": "Méthode de câblage",
		"comType": "Type de COM ",
		"versionInfo": "Information sur la version",
		"equipmentNumber": "Numéro d’équipement",
		"softwareVersion": "Version de software ",
		"softwareDate": "Date de software ",
		"SoftwareItem": "Élément de software",
		"communicationProtocol": "Protocole de communication ",
		"hardwareVersion": "Version de hardware ",
		"hardwareDate": "Date de hardware ",
		"versionInformationWrite": "Les informations de version du concentrateur n’écrivent pas.",
		"importUserPleaseSelect": "Veuillez sélectionner le compteur que vous souhaitez définir comme utilisateur important .",
		"blackListPleaseSelect": "Veuillez sélectionner le compteur que vous souhaitez définir comme compteur noir .",
		"indexDcu": "Index DCU",
		"heartbeatCycle": "Cycle de palpitation",
		"heartbeatCycleUnit": "Cycle de palpitation(Unité: Sec)",
		"energyProfileReadingCycle": "Cycle de lecture du profil énergétique",
		"energyProfileReadingCycleUnit": "Cycle de lecture du profil énergétique(Unité: Sec)",
		"plcChannelSendTimes": "Temps d’envoi du canal CPL",
		"plcChannelSendTimesUnit": " Temps d’envoi du canal CPL(Unité: Sec)",
		"plcChannelTimeout": "Délai d’expiration du canal CPL",
		"plcChannelTimeoutUnit": "Délai d’expiration du canal CPL (Unité: Sec)",
		"concentratorAddress": " Adresse de concentrateur",
		"managementLogicalDeviceName": "Nom d’équipement logique de gestion",
		"dormancyTime": "Durée de dormance",
		"dormancyTimeUnit": " Durée de dormance(Unité: Sec)",
		"eventConfiguration": "Configuration d’événement ",
		"broadcastTimeOrderEnable": "Activer l’ordre de temps de diffusion",
		"broadcastTimeParameter": "Paramètre de temps de diffusion",
		"broadcastTimeParameterNew": "Paramètre de temps de diffusion(Nouveau)",
		"gprsSignalStrength": "Force du signal GPRS ",
		"gprsImeiSerialNumber": "Numéro de série de GPRS IMEI ",
		"gprsNetworkStandard": "Norme de réseau GPRS",
		"wanDhcpEnable": "Activer WAN DHCP ",
		"csMode": "Mode C/S ",
		"serverPort": "Port de serveur",
		"gprsModemVersion": "Version de module GPRS",
		"plcModemVersion": "Version de module CPL ",
		"rfModemVersion": "Version de module RF ",
		"blacklistOfMeter": "Liste noire du compteur",
		"meterModuleSoftwareVersion": "Version de software du module de compteur",
		"dcuAsClient": "Dcu en tant que client",
		"dcuAsServer": "DCU en tant que serveur",
		"terminalIp": "Ip du terminal",
		"subnetMask": "Masque de sous-réseau",
		"gatewayMask": "Masque de passerelle",
		"ItemDontWrite": "L’élément n’écrit pas.",
		"blackFlag": "Black Flag",
		"pleaseCsMode": "Veuillez sélectionner le mode C/S!",
		"broadcastKey": "Clé de diffusion",
		"firmwareVersionLaterThan": "La version du firmware est postérieure à 1.39",
		"vpnDial": "Cadran d’appel de VPN ",
		"vpnNet": "Réseau de VPN",
		"connectedState": "État connecté",
		"topoInfo": "Informations sur la topologie",
		"shortAddress": "Adresse courte",
		"signalIntensity": "Intensité du signal",
		"netStatus": "État de réseau",
		"phaseInfo": "Informations sur la phase",
		"networkAccess": "Accès au réseau",
		"networkIng": "Entrer dans le réseau",
		"beaconForwarding": "Transmission par signal",
		"waitingConf": "Configuration en attente",
		"searchNode": "Rechercher un noeud",
		"delMeter": "Supprimer le compteur",
		"selectLogicName": "Veuillez sélectionner un nom logique!",
		"rs485LogicalAddress": "Adresse logique de RS485 ",
		"relayControlMode": "Mode de contrôle du relais",
		"searchLevel": "Niveau de topologie",
		"lossedInfo": "Informations de topologie perdues",
		"total": "Total",
		"lossed": "Perdu",
		"level": "Niveau",
		"dayCurves": "Nombre de profils journaliers de compteur de lecture (profils d’événement)",
		"monthCurves": "Nombre de profils mensuels de compteur de lecture",
		"loadCurves": "Nombre de profils de charge de compteur de lecture",
		"eventLogCurves": "Lecture des profils d’événements",
		"writeEvent": " Écrire:",
		"readEvent": "Lire :",
		"eventLogName": "Nom du profil d’événement",
		"obisCode": "Code Obis ",
		"selectEventToWrite": "Veuillez sélectionner les profils d’événement à écrire",
		"enterValueTimes": "Saisissez une valeur comprise entre 30 et 120 secondes",
		"timeZone": "Fuseau horaire",
		"connectionModel":"Modèle de connexion"

	},
	"importGprsMeter": {
		"importFromShipmentFile": "Importer le fichier d’expédition du compteur",
		"importGprsMeter": "Importer le fichier d’expédition",
		"selectFile": "Sélectionner un fichier",
		"num1": "1",
		"num2": "2",
		"num3": "3",
		"num4": "4",
		"step1": "Première étape",
		"step2": "Deuxième étape",
		"step3": "Troisième étape",
		"step4": "Quatrième étape",
		"assetType": "Type d’actif",
		"GPRSMeter": "Compteur GPRS ",
		"assetFile": "Fichier d’actif",
		"manufacturer": "Fabricant",
		"model": "Modèle",
		"firmwareVersion": "Version de firmware",
		"measurementGroup": "Groupe de mesure",
		"collectionSchemeGroup": "Groupe de schémas de collecte",
		"meterExist1": "Le compteur avec numéro SN",
		"meterExist2": "Existe déjà",
		"checkProgress": "Vérifier la progression",
		"importProgress": "Progression de l’importation",
		"selectFile": "Sélectionner un fichier",
		"checkStatus": "Vérification des fichiers Excel, ne changez pas!",
		"pleaseSelectFile": "Veuillez sélectionner un fichier Excel!",
		"noDataInExcel": "Il n’y a pas de données dans le fichier Excel!",
		"verificationFailed": "Échec de la vérification, veuillez vérifier et réimporter!",
		"pleaseSelMeaGroup": "Veuillez sélectionner le groupe de mesure!",
		"pleaseSelColSchGroup": "Veuillez sélectionner le groupe de schémas de collecte!",
		"checkCompleted": "Contrôle terminé!",
		"importStatus": "Importez des fichiers Excel, ne changez pas!",
		"deviceTypeIsEmpty": "Le type de périphérique est vide!",
		"pleaseSelCommType": "Veuillez sélectionner le type de communication!",
		"shipmentFileType": "Type de fichier d’expédition",
		"meterSipmentFile": "Fichier d’expédition du compteur",
		"shipmentFile": "Fichier d’expédition",
		"verifySuccess": " Vérification avec succès",
		"verifyFail": "Vérification échouée"
	},
	"customerList": {
		"customer": "Client",
		"customerList": "Liste de client",
		"customerSn": "Client SN",
		"customerType": "Type de client",
		"customerName": "Nom de client",
		"industryType": "Type d’industrie",
		"onlyOneError": "Vous avez sélectionné ce compteur ou le numéro de série du client saisi est référencé par un autre client. ",
		"ctNotNull": "CT est nul",
		"ptNotNull": "PT est nul",
		"customer1": "Point de prestation de service",
		"importSuccess": "Opération réussie",
		"importError": "L＇opération a échoué"
	},
	"dataComminicationStatus": {
		"title": "Rapport d’état de communication",
		"statusUpdateTime": "Heure de mise à jour de l’état",
		"networkAddress": "Adresse du réseau",
		"commComType": "Type de concentrateur COM ",
		"meterComType": "Type COM du compteur",
		"offlineTimeDays": "Temps hors ligne (jours)",
		"offline": "Hors ligne",
		"online": "En ligne",
		"unknown":"Inconnu",
		"status":"Tous",
		"installed":"Installé"
	},
	"saleStatReport": {
		"missData": "Données manquantes"
	},
	"veeList": {
		"validatingReport": "Validation du rapport",
		"exception tatal": "Total anormal",
		"class1": "Classe1",
		"class2": "Classe2",
		"class3": "Classe3",
		"class4": "Classe4",
		"class5": "Classe5",
		"veeGroup": "Groupe VEE ",
		"veeGroupList": "Liste de groupe VEE ",
		"groupName": "Nom de groupe",
		"meterCount": "Quantité de compteur ",
		"sdpCount": "Nombre de SDP",
		"groupType": "Type de groupe ",
		"objectType": "Type d’objet",
		"descr": "Description",
		"veeRuleList": "Liste des règles VEE",
		"ruleName": "Nom de règle",
		"ruleType": "Type de règle",
		"class": "Classe",
		"dataChannel": "Canal des données",
		"event": "Événement",
		"method": "Méthode",
		"estimationReportProgressDelay": "Rapport d’estimation - Retard de progression",
		"estimationReportMissData": "Détail d’exception(SDP)",
		"meterSn": "Compteur SN",
		"profile": "Profil",
		"communicatorSn": "Concentrateur SN",
		"communicationType": "Type de communication",
		"progress": "Progression",
		"progressDelay": "Retard de progression",
		"time": "",
		"updateTime": "Temps de mise à jour",
		"missDataListDetail": "Détail de la liste des données manquantes",
		"missDataList": "Liste des données manquantes",
		"progressDelayList": "Liste des retards de progression",
		"progressDelayListDetail": "Détail de la liste des retards de progression",
		"serizlNo": "Périphérique SN",
		"missData": "Données manquantes",
		"deadline": "Délai",
		"delayExported": "Seules les données de retard peuvent être exportées",
		"missExported": "Seules les données manquantes peuvent être exportées",
		"rowExport": "Sélectionnez la ligne à exporter",
		"taskTv": "Temps de tâche",
		"ruleStatus": "État de règle",
		"ruleClass": "Classe de règle",
		"editVeeRule": "Modifier la règle VEE",
		"addVeeRule": "Ajouter la règle VEE",
		"pleSeleVeeGroup": "Veuillez sélectionner le groupe VEE!",
		"pleFillName": "Le nom ne peut pas être vide !",
		"pleCycleCount": "Le nombre de cycles doit être des chiffres !",
		"pleStartCycle": "Le cycle de démarrage doit être des chiffres !",
		"pleDefaultValue": "La valeur du paramètre doit être des chiffres ! Veuillez le vérifier;",
		"eventRuleDesc": "Description de la règle d’événement",
		"dataItemName": "Nom de canal des données ",
		"dataItemKey": "Clé de canal des données ",
		"cycleCount": "Nombre de cycles",
		"startCycle": "Cycle de démarrage",
		"keyFlag": "Symbôle de clé",
		"cycleType": "Type de cycle",
		"paramKey": "Clé de paramètre",
		"defaultValue": "Valeur de paramètre",
		"dataItemList": "Liste de canal des données ",
		"veeEventParamList": "Liste des paramètres",
		"exceptionType": "Type d’exception",
		"eventType": "Type d’événement",
		"eventCountList": "Liste de comptage d’événements",
		"eventCount": "Nombre d’événements",
		"timeExceed": "L’intervalle de temps ne peut pas dépasser 7 jours",
		"minTimeExceed": "Données en minute, l’intervalle de temps ne peut pas dépasser 7 jours",
		"dayTimeExceed": "Données journalières, l’intervalle de temps ne peut pas dépasser 30 jours",
		"monthTimeExceed": "Données mensuelles, l’intervalle de temps ne peut pas dépasser 1 an",
		"selectedType": "Veuillez sélectionner le type de compteur, de communicateur, de ligne, de client, de transformateur et de SDP",
		"selectedTypeSDP": "Veuillez sélectionner le type SDP",
		"selectedTypeCalculation": "Veuillez sélectionner le type de calcul",
		"eventINSull": "L’événement est vide, veuillez le sélectionner à nouveau",
		"eventList": "Liste des événements",
		"eventDetailList": "Liste des détails de l’événement",
		"customerSn": "Client SN",
		"sdpName": "Nom SDP ",
		"eventName": "Nom de l’événement",
		"eventTime": "Heure de l’évènement",
		"eventStartTime": "Heure de début de l’événement",
		"eventEndTime": " Heure de fin de l’événement",
		"eventClass": "Classe de l’événement",
		"eventSource": "Source de l’événement",
		"dataDetail": "Liste des données de l’événement",
		"reviseData": "Réviser les données",
		"reviseRemind": "Opération non prise en charge pour cet événement",
		"operateSelect": "Veuillez sélectionner un événement à opérer",
		"linearRepair": "Réparer",
		"linearRepairCommit": "Déposer la réparation",
		"historicalRetrospection": "Version des données",
		"dataType": "Type des données",
		"noModifyData": "Il n’y a pas de données modifiées soumises. Veuillez les modifier",
		"dataItem": "Élément de données",
		"cannotBeEdited": "Le type d’exception ne peut pas être modifié",
		"dataName": "Nom des données",
		"dataGroup": "Groupe de données",
		"schemeType": "Type de schéma",
		"editType": "Modifier le type",
		"selectSdp": "Sélectionnez SDP à comparer",
		"selectRule": "Sélectionnez la règle",
		"pleaseSelectSdp": "Veuillez sélectionner SDP",
		"pleaseSelectVersionSdp": "Veuillez sélectionner la version des données pour SDP",
		"pleSelDataColumn": "Veuillez sélectionner une colonne de données",
		"onlyPleSelOneColumn": "Une seule colonne de données peut être sélectionnée pour la comparaison",
		"compareGraphVisualiz": "Courbe de comparaison des données",
		"donotSupportModify": "Cet événement ne prend pas en charge la modification",
		"fillReason": "Remplir la raison de l’opération.",
		"pleaseFillReason": "Veuillez remplir la raison de l’opération.",
		"pleaseRuleId": "La règle est nulle, veuillez la remplir pour sélectionner.",
		"schemeName": "Nom du schéma",
		"dataVersion": "Version des données",
		"operatorName": "Nom de l’opérateur",
		"operatorIp": "IP de l’opérateur",
		"operatorReason": "Raison de l’opérateur",
		"uruleDesigner": "Conception de règles",
		"dataSourceList": "Liste des sources de données",
		"dataCode": "Code de données",
		"exception": "Nom anormal",
		"timeType": "Type de temps",
		"missDataObject": "Détail de l’exception(Objet)",
		"estimationStatus": "État d’estimation ",
		"orderSn": "Ordre SN",
		"importTemplateFile": "Importer un fichier modèle",
		"regDataTemplateFile": "Enregistrer le fichier de modèle de données",
		"intervalDataTemplateFile": "Fichier de modèle de données d’intervalle",
		"regDataFile": "Enregistrer le fichier de données",
		"intervalDataFile": "Fichier de modèle de données d’intervalle",
		"importEnergyData": "Importer des données d’énergie",
		"importFileType": "Importer le type de fichier",
		"file": "Fichier",
		"verifySuccess": "Vérification avec succès",
		"importSuccess": "Importation avec success",
		"importFailed": " Importation échouée",
		"pleaseUsedCsv": "Veuillez appliquer le format CSV, SVP",
		"reviseFail": "Révision échouée",
		"keyFlagAuth": "La source de données doit être un symbôle clé",
		"eventSchemeExist": "Le type de schéma de l’événement actuel existe déjà",
		"eventAlreadyExist": "L’événement actuel existe déjà",
		"sourceEventType": "Type d’événement source",
		"timeSpan": "Laps de temps"
	},

	"dict": {
		"Reports": "Rapports",
		"Cusomer Billing Report": "Rapport de facturation de client",
		"Line Loss Report": "Rapport de perte de linge",
		"Supply Statistics Report": "Rapport de statistiques d’approvisionnement",
		"Sales Statistics Report": "Rapport de statistiques de ventes",
		"Non-Residential": "Non-résidentiel",
		"Residential": "Résidentiel",
		"Production": "Production",
		"Farming": "Agriculture",
		"Animal Husbandry": "Élevage d’animaux",
		"Fishery": "Pêche",
		"Mining": "Mine",
		"Lodging & Catering": "Hébergement & Restauration",
		"Minutely": "Minute",
		"Hourly": "Horaire",
		"Daily": "Journalier",
		"Monthly": "Mensuel",
		"Yes": "Oui",
		"No": "Non",
		"Data Profile": "Profil de données",
		"Event Profile": "Profil de l’événement",
		"Minute": "Minute",
		"Day": "Jour",
		"Month": "Mois"

	},

	"dcuConfiguration300": {
		"title": "娴嬮噺鐐瑰弬鏁扮鐞�",
		"communicatorSn": "闆嗕腑鍣ㄨ祫浜х紪鍙�",
		"measurementPointRange": "娴嬮噺鐐硅寖鍥�",
		"meterTitleHES": "璁￠噺鐐瑰垪琛�",
		"communication": "communication",
		"meterSn": "璧勪骇缂栧彿",
		"pointNum": "娴嬮噺鐐圭紪鍙�",
		"status": "鐘舵��",
		"meterProperties": "琛ㄨ鎬ц川",
		"mac": "娴嬮噺鐐瑰湴鍧�",
		"meterType": "鐢佃兘琛ㄧ被鍨�",
		"totalDivType": "鎬诲垎绫诲瀷",
		"isVip": "閲嶇偣鐢ㄦ埛灞炴��",
		"feeRateCount": "鏈�澶ц垂鐜囨暟",
		"collectorAddress": "閲囬泦鍣ㄥ湴鍧�",
		"comNum": "绔彛鍙�",
		"taChange": "TA鍙樺寲",
		"tvChange": "TV鍙樺寲",
		"command": "鍛戒护",
		"reason": "鍘熷洜",
		"requestTime": "璇锋眰鏃堕棿",
		"responseTime": "鍝嶅簲鏃堕棿",
		"protocol": "瑙勭害",
		"meterTitleDCU": "琛ㄨ鍒楄〃 (浠嶥CU璇诲彇)",
		"seleCommunicator": "璇烽�夋嫨闆嗕腑鍣紒",
		"seleLeastOne": "鑷冲皯閫夋嫨琛ㄤ腑鐨勪竴鏉℃暟鎹紒",
		"pleaseEnterPositiveInt": "璇疯緭鍏ユ鏁存暟",
		"beginGreaterThanEnd": "璧峰娴嬮噺鐐瑰繀椤诲ぇ浜庣粓姝㈡祴閲忕偣",
		"baudRate": "娉㈢壒鐜�",
		"stopFlag": "鍋滄浣�"

	},

	"courtsTopology": {
		"title": "Topologie de la station"
	},

	"reportDesigner": {
		"reportDesigner": "Concepteur de rapports",
		"reportManagement": "Gestion des rapports",
		"reportExploer": "Exploration de rapport",
		"reportManagementList": "Liste de gestion des rapports",
		"reportType": "Type de rapport",
		"reportName": "Nom du rapport",
		"orgName": "Nom de l’organisation",
		"folderName": "Nom du classeur",
		"isEnable": "est activé",
		"templateFile": "Fichier modèle",
		"plsSelectFolder": "Veuillez sélectionner le classeur",
		"plsSelectReportMark": "Veuillez remplir la remarque",
		"plsSelectRole": "Veuillez sélectionner le rôle",
		"deleteFolder": "Supprimer le classeur",
		"addFolder": "Ajouter un classeur",
		"parentFolderName": "Parent-classeur",
		"canotDeleted": "Ne peut pas être supprimé",
		"maxThreeLevel": "Seuls trois niveaux peuvent être ajoutés au maximum.",
		"pleaseFillfolderName": "Veuillez remplir le nom du classeur.",
		"plseCheckDeleteFolder": "Veuillez vérifier si le rapport est inclus dans le répertoire, puis confirmez s’il faut le supprimer!"

	},

	"servicePoint": {
		"name": "Nom SDP ",
		"sn": "SDP  SN",
		"list": "Liste SDP ",
		"voltageType": "Type de tension",
		"connectionMode": "Mode de connexion",
		"createTime": " Temps de création",
		"createPointTips": "Ajoutez automatiquement les informations du « Point de livraison de services » lorsque vous cliquez sur le bouton Enregistrer!",
		"connModel1": "Triphasé à trois fils",
		"connModel2": "Triphasé à quatre fils",
		"title": "Point de livraison de service",
		"linkedSpd": "SDP lié",
		"linkedSpdList": "Liste SDP liée",
		"base": "Propriétés de base",
		"advance": "Propriétés avancées",
		"billing": "Propriétés de facturation ",
		"type": "Type SDP ",
		"subType": "Sous-type SDP ",
		"isVirtual": "Est Virtuel",
		"status": " État de fonctionnement",
		"updateTime": "Temps de mise à jour",
		"capacity": "Capacité de puissance (kVa)",
		"industryType": "Type d’industrie",
		"validationGroup": "Groupe de validation",
		"estimationGroup": "Groupe d’estimation",
		"calculationGroup": "Groupe de calcul",
		"schemeType": "Type de schéma",
		"register": "Enregistrer la plage des données (Min)",
		"interval": "Plage des données d’intervalle (Min)",
		"inst": "Plage des données instantanée (Min)",
		"operTv": "Temps d’opération",
		"operType": " Type de d’opération",
		"operator": "Opérateur",
		"lastDataTv": "Heure de la dernière données",
		"linkedSdp": "SDP lié"
	},

	"mdmDataEnergy": {
		"AP_MAX": "Puissance maximale (KW)",
		"AP_MAX_DT": "Heure d’occurrence (Puissance maximale (KW))",
		"AP_MIN": "Puissance minimale (KW)",
		"AP_MIN_DT": "Heure d’occurrence (Puissance minimale (KW))",
		"AP_AVG": "Puissance active moyenne (KW)",
		"RP_MAX": "Puissance réactive maximale (KW)",
		"RP_MAX_DT": "Heure d’occurrence (Puissance réactive maximale (KW))",
		"RP_MIN": "Puissance réactive minimale (KW)",
		"RP_MIN_DT": "Heure d’occurrence (Puissance réactive minimale (KW))",
		"RP_AVG": "Puissance réactive moyenne (KW)",
		"PF_MAX": "Facteur de puissance maximale",
		"PF_MAX_DT": "Heure d’occurrence (Facteur de puissance maximale)",
		"PF_MIN": "Facteur de puissance minimale",
		"PF_MIN_DT": "Heure d’occurrence (Facteur de puissance minimale)",
		"PF_AVG": "Facteur de puissance moyenne",
		"R0P1": "Énergie active import",
		"R0P1_DT": "Heure d’occurrence (Énergie active import maximale)",
		"R0P2": "Énergie active export",
		"R0P2_DT": "Temps d’occurrence (Énergie active export maximale)",
		"R0P1A1": "Énergie active cumulée |+A|+|-A|",
		"R0P1A2": "Énergie active cumulée |+A|-|-A|",
		"R0P1A3": "Énergie réactive cumulée |+R|+|-R|",
		"R0P1A4": "Énergie réactive cumulée |+R|-|-R|",
		"V_A_MAX": "Tension maximale de phase A",
		"V_A_MAX_DT": "Heure d’occurrence (Tension maximale de phase A)",
		"V_A_MIN": "Tension minimale de phase A",
		"V_A_MIN_DT": "Heure d’occurrence (Tension minimale de phase A)",
		"V_A_AVG": "Tension moyen de phase A",
		"V_B_MAX": "Tension maximale de phase A",
		"V_B_MAX_DT": "Heure d’occurrence (Tension maximale de phase B)",
		"V_B_MIN": "Tension minimale de phase B",
		"V_B_MIN_DT": "Heure d’occurrence (Tension minimale de phase B)",
		"V_B_AVG": "Tension moyen de phase B",
		"V_C_MAX": "Tension maximale de phase C",
		"V_C_MAX_DT": "Heure d’occurrence (Tension maximale de phase C)",
		"V_C_MIN": "Tension minimale de phase C",
		"V_C_MIN_DT": "Heure d’occurrence (Tension minimale de phase C)",
		"V_C_AVG": "Tension moyen de phase C",
		"C_A_MAX": "Courant maximal de phase A",
		"C_A_MAX_DT": "Heure d’occurrence (Courant maximal de phase A)",
		"C_A_MIN": "Courant minimal de phase A",
		"C_A_MIN_DT": "Heure d’occurrence (Courant minimal de phase A)",
		"C_A_AVG": "Courant moyen de phase A",
		"C_B_MAX": "Courant maximale de phase B",
		"C_B_MAX_DT": "Heure d’occurrence (Courant maximale de phase B)",
		"C_B_MIN": "Tension minimale de phase B",
		"C_B_MIN_DT": "Heure d’occurrence (Courant minimal de phase B)",
		"C_B_AVG": "Courant moyen de phase B",
		"C_C_MAX": "Courant maximal de phase C",
		"C_C_MAX_DT": "Heure d’occurrence (Courant maximal de phase C)",
		"C_C_MIN": "Tension minimale de phase C",
		"C_C_MIN_DT": "Heure d’occurrence (Courant minimal de phase C)",
		"C_C_AVG": "Courant moyen de phase C",
		"R0P3": "Énergie réactive import",
		"R0P4": "Énergie réactive export",
		"R0P5": "Énergie réactive QI",
		"R0P6": "Énergie réactive QII",
		"R0P7": "Énergie réactive QIII",
		"R0P8": "Énergie réactive QIV",
		"R1P1": "Taux d’énergie active import 1",
		"R1P1_DT": "Heure d’occurrence (Taux d’énergie active import maximal 1)",
		"R1P2": "Taux d’énergie active export 1",
		"R1P2_DT": "Heure d’occurrence (Taux d’énergie active export maximal 1)",
		"R1P3": "Taux d’énergie réactive import 1",
		"R1P4": "Taux d’énergie réactive export  1",
		"R2P1": "Taux d’énergie active import 2",
		"R2P1_DT": "Heure d’occurrence (Taux d’énergie active import maximal 2)",
		"R2P2": "Taux d’énergie active export 2",
		"R2P2_DT": "Heure d’occurrence (Taux d’énergie active export maximal 2)",
		"R2P3": "Taux d’énergie réactive import 2",
		"R2P4": "Taux d’énergie réactive import 2",
		"R3P1": "Taux d’énergie active import 3",
		"R3P1_DT": "Heure d’occurrence (Taux d’énergie active import maximal 3)",
		"R3P2": "Taux d’énergie active export 3",
		"R3P2_DT": "Heure d’occurrence (Taux d’énergie active export maximal 3)",
		"R3P3": "Taux d’énergie réactive import 3",
		"R3P4": "Taux d’énergie réactive export 3",
		"R4P1": "Taux d’énergie active import 4",
		"R4P1_DT": "Heure d’occurrence (Taux d’énergie active import maximal 4)",
		"R4P2": "Taux d’énergie active export 4",
		"R4P2_DT": "Heure d’occurrence (Taux d’énergie active export maximal 4)",
		"R4P3": "Taux d’énergie réactive import 4",
		"R4P4": "Taux d’énergie réactive export 4",
		"U_A": "Tension A",
		"U_B": "Tension B",
		"U_C": "Tension C",
		"I_A": "Courant A",
		"I_B": "Courant B",
		"I_C": "Courant C",
		"I_0": "Courant 0",
		"AP": "Puissance active",
		"AP_A": "Puissance active A",
		"AP_B": "Puissance active B",
		"AP_C": "Puissance active C",
		"RP": "Puissance réactive",
		"RP_A": "Puissance réactive A",
		"RP_B": "Puissance réactive B",
		"RP_C": "Puissance réactive C",
		"COS": "Facteur de puissance ",
		"COS_A": "Facteur de puissance A",
		"COS_B": "Facteur de puissance B",
		"COS_C": "Facteur de puissance C",
		"SP": "Puissance apparente",
		"SP_A": "Puissance apparente A",
		"SP_B": "Puissance apparente B",
		"SP_C": "Puissance apparente C",
		"dataSource": "Source des données",
		"sdpHeadTitle": "Gestion des données SDP",
		"headTitle": "Gestion des données de facturation ",
		"MDM_DATA_ENERGY_GROUP": "Données d’énergie",
		"MDM_DATA_INTERVAL_GROUP": "Données d’intervalle",
		"MDM_DATA_REG_GROUP": "Enregistrer les données",
		"billingDataManagement": "Gestion des données de facturation"

	},

	"mdmDataCalcObjEnergy": {
		"title": "Analyse sommaire de l’énergie",
		"R0P1": "Énergie active (kWh)",
		"R0P2": "Énergie réactive (kWh)",
		"R1P1": "Taux d’énergie active 1 (kWh)",
		"R1P2": "Taux d’énergie réactive 1 (kVarh)",
		"R2P1": "Taux d’énergie active 2 (kWh)",
		"R2P2": "Taux d’énergie réactive 2 (kVarh)",
		"R3P1": "Taux d’énergie active 3(kWh)",
		"R3P2": "Taux d’énergie réactive 3(kVarh)",
		"R4P1": "Taux d’énergie active 4(kWh)",
		"R4P2": "Taux d’énergie réactive 4(kVarh)",
		"summaryObj": "Objet récapitulatif"
	},

	"mdmAssetCalcScheme": {
		"list": "Liste de calcul",
		"title": "Schéma de calcul",
		"id": " Id de Schéma",
		"name": "Nom de Schéma",
		"type": "Type de Schéma",
		"baseTime": "Heure de base",
		"isUse": "Activer",
		"cycleNum": "Numéro de cycle",
		"isDefault": "Par défaut"
	},

	"mdmDataCalcObjLoad": {
		"title": "Chargement d’objet de calcul",
		"sdpLoadTitle": "Détail extrême de la charge SDP",
		"sdpLoadList": "Liste de chargement SDP"
	},

	"mdmDataCalcObjLoadExtre": {
		"title": "Analyse de charge",
		"list": "Liste d’analyse de charge",
		"calcObjName": "Nom de l’objet",
		"tv": "Heure des données",
		"dataType": "Type de données",
		"timeType": "Type d’heure ",
		"apMaxDt": "Temps d’alimentation maximal",
		"apMinDt": "Temps d’alimentation minimal",
		"apMax": "Puissance maximale (KW)",
		"apMin": "Puissance minimale (KW)",
		"apAvg": "Puissance moyenne (KW)",
		"apMaxTitle": "Puissance active maximale (KW)",
		"apMinTitle": "Puissance active minimale (KW)",
		"apAvgTitle": "Puissance active moyenne (KW)",
		"apMaxDtTitle": " Heure de puissance active maximal",
		"apMinDtTitle": " Heure de puissance active minimal",
		"rpMaxTitle": "Puissance réactive maximale (KW)",
		"rpMinTitle": "Puissance réactive minimale (KW)",
		"rpAvgTitle": "Puissance réactive moyenne (KW)",
		"rpMaxDtTitle": "Heure puissance réactive maximale",
		"rpMinDtTitle": "Heure de puissance réactive minimale",
		"apMaxYoy": "Puissance maximale (YOY)",
		"apMaxQoq": "Puissance maximale (QOQ)",
		"totalRecord": "Nombre total SDP ",
		"missRecord": "Numéro invalide SDP",
		"integrityRate": "Taux d’intégrité (%)",
		"objNameNotNull": "Veuillez sélectionner d’abord un objet, SVP!",
		"sdpTotal": "Nombre total SDP",
		"sdpInvalid": "Numéro invalide SDP"
	},

	"mdmDataInstLoadrateSummary": {
		"overNumber": "Nombre de surcharge et de charge lourde ",
		"maxLoadRate": "Taux de charge maximal (%)",
		"maxLoadDt": "Heure de taux de charge maximal",
		"minLoadRate": "Taux de charge minimal(%)",
		"avgLoadRate": "Taux de charge moyen(%)",
		"minLoadDt": "Heure de taux de charge minimal",
		"record": "Numéro d’enregistrement",
		"missRecord": "Numéro d’enregistrement manqué",
		"integrityRate": "Taux d’intégrité( (%)",
		"dataDetail": "Données détaillés",
		"eventDetail": "Données de l’événement",
		"evaluation": "Évaluation",
		"tranType": " Type de transformateur",
		"ruleDetail": "Détail de règle ",
		"times": "Nombre",
		"privateTran": "Transformateur privé",
		"publicTran": "Transformateur public",
		"tranCapacity": "Capacité de transformateur",
		"type": "Type de taux de charge",
		"title": "Analyse du taux de charge",
		"list": "Liste des taux de charge"
	},

	"mdmDataVoltImbalSummary": {
		"title": "Analyse de déséquilibre triphasé",
		"listName": "Liste récapitulative des déséquilibres de tension",
		"listDetailName": "Liste de déséquilibre de tension",
		"objectName": "Nom d’objet",
		"imbalanceType": "Type de déséquilibre",
		"sdpType": "Type SDP ",
		"timeType": "Type de temps",
		"startTime": "Heure de début",
		"endTime": "Heure de fin",
		"ruleDetail": "Détail de règle",
		"orgCode": "Code d’organisation ",
		"orgName": "Nom d’organisation",
		"dataTime": "Heure des données",
		"totalOfSdp": "Total de SDP",
		"balanceNumber": "Numéro d’équilibre",
		"imbalanceNumber": "Numéro de déséquilibre",
		"imbalanceRate": "Taux de déséquilibre (%)",
		"missDataNumber": "Numéro de données manquées",
		"integrityRate": "Taux d’intégrité (%)",
		"sdpName": "Nom SDP",
		"meterSn": "Compteur SN",
		"monitorTimeMin": "Temps de surveillance (Min)",
		"beyondTimeMin": "Au-delà du temps (Min)",
		"passRate": "Taux de réussite (%)",
		"beyondRate": "Au-delà du taux(%)",
		"maxImbalanceRate": " Taux de déséquilibre maximal (%)",
		"maxImbalanceHappenTime": "Temps de déséquilibre maximal",
		"minImbalanceRate": "Taux de déséquilibre minimal (%)",
		"minImbalanceHappenTime": "Heure d’occurrence du déséquilibre minimal",
		"detailData": "Données détaillées",
		"onlyOrgQuery": "Veuillez sélectionner l’organisation à interroger."
	},

	"mdmDataInstPFSummary": {
		"title": "Analyse PF",
		"listName": "Liste de facteur de puissance ",
		"listDetailName": "Liste des facteurs de puissance moyens",
		"objectName": "Nom d’objet",
		"timeType": "Type de temps",
		"industryType": "Type d’industrie",
		"startTime": "Heure de début",
		"endTime": "Heure de fin",
		"powerFactorList": "Liste de facteur de puissance",
		"orgCode": "Code d’organisation",
		"orgName": "Nom d’organisation",
		"industryName": "Nom d’industrie",
		"totalOfSdp": "Total du SDP",
		"numberPf08": "Numéro (PF < 0.8)",
		"ratePf08": "Taux (PF < 0.8)",
		"numberPf08085": "Numéro (0.8 < PF <0.85)",
		"ratePf08085": "Taux (0.8 < PF <0.85)",
		"numberPf08509": "Numéro (0.85 < PF <0.9)",
		"ratePf08509": "Taux (0.85 < PF <0.9)",
		"numberPf09095": "Numéro (0.9 < PF <0.95)",
		"ratePf09095": "Taux (0.9 < PF <0.95)",
		"numberPf09510": "Numéro (0.95 < PF <1.0)",
		"ratePf09510": "Taux (0.95 < PF <1.0)",
		"missRecordNumber": "Numéro d’enregistrement manqué",
		"integrityRate": "Taux d’intégrité (%)",
		"sdpName": "Nom SDP",
		"meterSn": "Compteur SN",
		"dataTime": "Heure des données",
		"averagePowerFactor": "Facteur de puissance moyen",
		"activeEnergy": "Énergie active (kWh)",
		"reactiveEnergy": "Énergie réactive (kVarh)",
		"detailData": "Données détaillées"
	},

	"sdpLoadDetail": {
		"title": "Détail de charge SDP ",
		"dataType": "Type de données",
		"objectName": "Nom d’objet",
		"power": "Puissance",
		"current": "Courant",
		"voltage": "Tension",
		"powerFactor": "Facteur de puissance"
	},

	"revenue": {
		"anlysis": "Analyse de la protection des revenus",
		"list": "Liste de protection des revenus",
		"capacity": "Capacité",
		"sdpType": "Type de SDP ",
		"proccessStatus": "État de processus",
		"proccessResult": " Résultat de processus",
		"proccessUser": "Utilisateur de processus",
		"suspectedRate": "Taux suspecté (%)",
		"comments": "Commentaires sur le processus",
		"proccessDate": "Date de processus",
		"evalutin": "Évaluation",
		"protection": "Évaluation de la protection des revenus",
		"private": "Transformateur privé SDP",
		"public": "Transformateur public SDP",
		"commentsNotNull": "Commentaires de processus non nuls",
		"suspectedRateNotNull": "Taux suspecté non nul",
		"resultNotNull": "Résultat du processus non nul",
		"energy": "Énergie",
		"load": "Charge",
		"event": "Événement"
	},

	"dcuModuleConfiguration": {
		"title": " Configuration de module",
		"moduleParameter": "Paramètre du module",
		"pleaseChooseMeterSn": "Veuillez sélectionner le compteur SN!",
		"meterSn": "Compteur SN",
		"APN": "APN",
		"userAndPassword": "Utilisateur et Mot de passe",
		"pinCode": "Code PIN",
		"hesIpAndPort": "HES ip et port",
		"ftpIpAndPort": "FTP ip et port",
		"autoConnectMode": "Mode de connexion automatique",
		"autoAnswerMode": "Mode de réponse automatique",
		"inactivityTimeOut": "Délai d’inactivité",
		"noNetworkCommunicationTimeoutReset": "Pas de réinitialisation du délai de communication du réseau",
		"user": " Nom d’utilisateur",
		"password": "Mot de passe",
		"inactiveConnection": "Connexion inactive",
		"activeConnection": "Connexion active",
		"activeConnectionNotInWindowTime": "Connexion active (Impossible de réveiller dans le temps de Windows)",
		"activeConnectionInWindowTime": "Connexion inactive (Peut réveiller dans le temps de Windows)",
		"inactiveConnectionRouseHesConnect": "Connexion inactive (peut réveiller la connexion HES)",
		"alwaysConnectedGprs": "GPRS toujours connecté",
		"notNeedToAnswer": "Connexion (il n’est pas nécessaire de répondre à la requête)",
		"needToAnswer": "Connexion (il est nécessaire de répondre à la requête)",
		"closedGprs": "GRPS fermé (Ne peut pas réveiller)",
		"selectConnectType": "Veuillez sélectionner le type de connexion.",
		"selectModeType": "Veuillez sélectionner le type de mode."
	},

	"dictManagement": {
		"title": "Gestion du dictionnaire",
		"dictId": " ID Dict",
		"dictName": " Nom de Dict",
		"dictDesp": "Description de Dict ",
		"dictGroupList": "Liste des groupes de dictionnaire",
		"dictDetailList": "Liste de détails de Dict",
		"dictDetailId": "ID de détail de Dict",
		"dictGroupId": "ID du groupe de Dict",
		"dictDetailName": "Nom du détail de Dict",
		"dictDetailDesp": "Description détaillée de Dict ",
		"isDefault": "Par défaut",
		"displayOrder": "Ordre d’affichage",
		"dictIdOneTips": "L’ID de dict existe déjà!",
		"dictDetailIdOneTips": "L’ID de détail de dict existe déjà!",
		"dictGroupFirst": "Veuillez d’abord sélectionner le groupe de dictionnaire!",
		"dictEventFirst": "Veuillez d’abord sélectionner le type d’événement!"
	},

	"dictDataitem": {
		"name": "Nom du canal",
		"title": "Gestion des canaux",
		"list": "Liste des canaux",
		"id": "ID canal",
		"protocolCode": "Code OBIS",
		"protocolName": "Nom du protocole",
		"unit": "Unité",
		"dataitemType": "Type de canal",
		"showUnit": "Afficher l’unité",
		"opType": " Type d’opération",
		"idExist": "L’ID de canal existe déjà!",
		"nameExist": "Le nom de canal existe déjà!",
		"add": "Ajouter un canal",
		"import": "Importer un fichier de canal",
		"parseType": "Type d’analyse",
		"parseLen": "Longueur d’analyse",
		"scale": "Calibre",
		"Load Profile Minutely":"Charger le profil toutes les minutes",
		"Active energy import (+A) [Unit: kWh]": "Énergie active import (+A) [Unité: kWh]",
		"Active energy import (+A) rate 1 [Unit: kWh]": "Énergie active import (+A) taux 1 [Unité: kWh]",
		"Active energy import (+A) rate 2 [Unit: kWh]": "Énergie active import (+A) taux 2 [Unité: kWh]",
		"Active energy import (+A) rate 3 [Unit: kWh]": "Énergie active import (+A) taux 3 [Unité: kWh]",
		"Active energy import (+A) rate 4 [Unit: kWh]": "Énergie active import (+A) taux 4 [Unité: kWh]",
		"Active energy export (-A) [Unit: kWh]": "Énergie active export (-A) [Unité: kWh]",
		"Active energy export (-A) rate 1 [Unit: kWh]": "Énergie active export (-A) taux 1 [Unité: kWh]",
		"Active energy export (-A) rate 2 [Unit: kWh]": "Énergie active export (-A) taux 2 [Unité: kWh]",
		"Active energy export (-A) rate 3 [Unit: kWh]": "Énergie active export (-A) taux 3 [Unité: kWh]",
		"Active energy export (-A) rate 4 [Unit: kWh]": "Énergie active export (-A) taux 4 [Unité: kWh]",
		"Reactive energy import (+R) (QI+QII) [Unit: kVarh]": "Énergie réactive import (+R) (QI+QII) [Unité: kVarh]",
		"Reactive energy import (+R) (QI+QII) rate 1 [Unit: kVarh]": "Énergie réactive import (+R) (QI+QII) taux 1 [Unité: kVarh]",
		"Reactive energy import (+R) (QI+QII) rate 2 [Unit: kVarh]": "Énergie réactive import (+R) (QI+QII) taux 2 [Unité: kVarh]",
		"Reactive energy import (+R) (QI+QII) rate 3 [Unit: kVarh]": "Énergie réactive import (+R) (QI+QII) taux 3 [Unité: kVarh]",
		"Reactive energy import (+R) (QI+QII) rate 4 [Unit: kVarh]": "Énergie réactive import (+R) (QI+QII) taux 4 [Unité: kVarh]",
		"Reactive energy export (-R) (QIII+QIV) [Unit: kVarh]": "Énergie réactive export (-R) (QIII+QIV) [Unité: kVarh]",
		"Reactive energy export (-R) (QIII+QIV) rate 1 [Unit: kVarh]": "Énergie réactive export (-R) (QIII+QIV) taux 1 [Unité: kVarh]",
		"Reactive energy export (-R) (QIII+QIV) rate 2 [Unit: kVarh]": "Énergie réactive export (-R) (QIII+QIV) taux 2 [Unité: kVarh]",
		"Reactive energy export (-R) (QIII+QIV) rate 3 [Unit: kVarh]": "Énergie réactive export (-R) (QIII+QIV) taux 3 [Unité: kVarh]",
		"Reactive energy export (-R) (QIII+QIV) rate 4 [Unit: kVarh]": "Énergie réactive export (-R) (QIII+QIV) taux 4 [Unité: kVarh]",
		"Reactive energy QI (+Rl) [Unit: kVarh]": "Énergie réactive QI (+Rl) [Unité: kVarh]",
		"Reactive energy QI (+Rl) rate 1 [Unit: kVarh]": "Énergie réactive QI (+Rl) taux 1 [Unité: kVarh]",
		"Reactive energy QI (+Rl) rate 2 [Unit: kVarh]": "Énergie réactive QI (+Rl) taux 2 [Unité: kVarh]",
		"Reactive energy QI (+Rl) rate 3 [Unit: kVarh]": "Énergie réactive QI (+Rl) taux 3 [Unité: kVarh]",
		"Reactive energy QI (+Rl) rate 4 [Unit: kVarh]": "Énergie réactive QI (+Rl) taux 4 [Unité: kVarh]",
		"Reactive energy QII (+Rc) [Unit: kVarh]": "Énergie réactive QII (+Rc) [Unit: kVarh]",
		"Reactive energy QII (+Rc) rate 1 [Unit: kVarh]": "Énergie réactive QII (+Rc) taux 1 [Unité: kVarh]",
		"Reactive energy QII (+Rc) rate 2 [Unit: kVarh]": "Énergie réactive QII (+Rc) taux 2 [Unité: kVarh]",
		"Reactive energy QII (+Rc) rate 3 [Unit: kVarh]": "Énergie réactive QII (+Rc) taux 3 [Unité: kVarh]",
		"Reactive energy QII (+Rc) rate 4 [Unit: kVarh]": "Énergie réactive QII (+Rc) taux 4 [Unité: kVarh]",
		"Reactive energy QIII (+Rl) [Unit: kVarh]": "Énergie réactive QIII (+Rl) [Unité: kVarh]",
		"Reactive energy QIII (-Rl) rate 1 [Unit: kVarh]": "Énergie réactive QIII (-Rl) taux 1 [Unité: kVarh]",
		"Reactive energy QIII (-Rl) rate 2 [Unit: kVarh]": "Énergie réactive QIII (-Rl) taux 2 [Unité: kVarh]",
		"Reactive energy QIII (-Rl) rate 3 [Unit: kVarh]": "Énergie réactive QIII (-Rl) taux 3 [Unité: kVarh]",
		"Reactive energy QIII (-Rl) rate 4 [Unit: kVarh]": "Énergie réactive QIII (-Rl) taux 4 [Unité: kVarh]",
		"Reactive energy QIII (+Rl) rate 1 [Unit: kVarh]": "Énergie réactive QIII (+Rl) taux 1 [Unité: kVarh]",
		"Reactive energy QIII (+Rl) rate 2 [Unit: kVarh]": "Énergie réactive QIII (+Rl) taux 2 [Unité: kVarh]",
		"Reactive energy QIII (+Rl) rate 3 [Unit: kVarh]": "Énergie réactive QIII (+Rl) taux 3 [Unité: kVarh]",
		"Reactive energy QIII (+Rl) rate 4 [Unit: kVarh]": "Énergie réactive QIII (+Rl) taux 4 [Unité: kVarh]",
		"Reactive energy QIV (-Rc) [Unit: kVarh]": "Énergie réactive QIV (-Rc) [Unité: kVarh]",
		"Reactive energy QIV (-Rc) rate 1 [Unit: kVarh]": "Énergie réactive QIV (-Rc) taux 1 [Unité: kVarh]",
		"Reactive energy QIV (-Rc) rate 2 [Unit: kVarh]": "Énergie réactive QIV (-Rc) taux 2 [Unité: kVarh]",
		"Reactive energy QIV (-Rc) rate 3 [Unit: kVarh]": "Énergie réactive QIV (-Rc) taux 3 [Unité: kVarh]",
		"Reactive energy QIV (-Rc) rate 4 [Unit: kVarh]": "Énergie réactive QIV (-Rc) taux 4 [Unité: kVarh]",
		"Apparent energy import (+E) [Unit: kVarh]": "Énergie apparente import (+E) [Unité: kVarh]",
		"Apparent energy export (-E) [Unit: kVarh]": "Énergie apparente export (-E) [Unité: kVarh]",
		"Import active demand [Unit:kW]": "Demande active import [Unité: kW]",
		"Export active demand [Unit:kW]": " Demande active export [Unité: kW]",
		"Import reactive demand [Unit:kVar]": "Demande réactive import [Unité: kVar]",
		"Export reactive demand [Unit:kVar]": "Demande réactive export [Unité: kVar]",		
		"Instantaneous voltage L1 [Unit: V] ": "Tension instantanée L1 [Unité: V]",
		"Instantaneous voltage L2 [Unit: V]": "Tension instantanée L2 [Unité: V]",
		"Instantaneous voltage L3 [Unit: V]": "Tension instantanée L3 [Unité: V]",
		"Instantaneous current L1 [Unit: A]": "Courant instantané L1 [Unité: A]",
		"Instantaneous current L2 [Unit: A]": "Courant instantané L2 [Unité: A]",
		"Instantaneous current L3 [Unit: A]": "Courant instantané L3 [Unité: A]",
		"Neutral current [Unit: A]": "Courant de neutre [Unit: A]",
		"Instantaneous active import power (+A) [Unit: kW]": " Puissance active import instantanée (+A) [Unité: kW]",
		"Instantaneous active export power (-A) [Unit: kW]": "Puissance active export instantanée (-A) [Unité: kW]",
		"Instantaneous reactive import power (+R) [Unit: kVar]": "Puissance réactive import instantanée (+R) [Unité: kVar]",
		"Instantaneous reactive export power (-R) [Unit: kVar]": "Puissance réactive export instantanée (-R) [Unité: kVar]",
		"Instantaneous apparent export power [Unit: kVa]": "Puissance apparente export instantanée [Unité: kVa]",
		"Instantaneous Power factor (PF) L1": "Facteur de puissance instantanée (FP) L1",
		"Instantaneous Power factor (PF) L2": "Facteur de puissance instantanée (FP) L2",
		"Instantaneous Power factor (PF) L3": "Facteur de puissance instantanée (FP) L3",
		"Frequency [Unit: Hz]": "Fréquence [Unité: Hz]",
		"Meter Serial Number": "Numéro de série du compteur",
		"Manufactory Identifier": "Identifiant de fabricant",
		"Logic Device Name": " Nom du périphérique logique",
		"Firmware Version": "Version de Firmware ",
		"Clock": "Horloge",
		"Relay Control State": "État de contrôle de relais",
		"Relay Control Mode": "Mode de contrôle de relais",
		"Currently active step": "Échelle activée en cours ",
		"Currently active price": "Prix activé en cours ",
		"Tariff index": "Index de tarif",
		"Max credit limit": "Limite de crédit maximale",
		"Key expiry number": "Numéro d＇expiration de clé’",
		"Max vend limit": "Limite de vente d’énergie maximale",
		"Supply group code": " SGC ",
		"Key type": "Type de clé",
		"Key Revision number": "Numéro de révision de clé",
		"STS software versopm": "Version de software STS",
		"Current time purchase credit": "Crédit d＇achat actuel",
		"Cumulative purchase credit": "Crédit d＇achat cumulé",
		"Residual credit": "Crédit restant",
		"Cumulative consume credit": "Crédit de consommation cumulé",
		"Current month consume credit": "Crédit de consommation du mois actuel",
		"Current day consume credit": "Crédit de consommation du jour actuel",
		"Date of passive step active": " Date d’activation d’échelle de réserve ",
		"Watchdog Error": " Erreur de chien de garde ",
		"Normal Voltage L1": "Tension normale L1",
		"Under Limit Threshold of miss Voltage [Unit: V]": "Seuil limit de la perte de tension [Unité: V]",
		"Hardware Reset": "Réinitialisation de hardware",
		"Data Base Initialization": "Initialisation de base des données",
		"Parameter Reset(except communication)": "Réinitialisation de paramètre （sauf communication）",
		"Parameter Reset":"Réinitialisation de paramètre",
		"Dcu as a Client": "Dcu en tant que client",
		"Dcu as a Server": "Dcu en tant que serveur"
	},

	"dictDataitemGroup": {
		"title": "Afficher la gestion de groupe",
		"list": "Afficher la liste des groupes",
		"name": "Nom de groupe",
		"id": "Groupe ID",
		"appType": "Type de groupe",
		"protocolName": "Nom du protocole",
		"idExist": "L’ID de groupe existe déjà!",
		"nameExist": "Le nom de groupe existe déjà!",
		"selectGroup": "Veuillez d’abord sélectionner le groupe!",
		"sortId": "ID de tri",
		"deleteItem": "Veuillez d’abord supprimer les canaux liés",
		"Standard Event Log": "Journal d＇événement standard",
        "Three phase meter event": "Événement de compteur triphasé",
        "Relay Control Log": "Journal d＇événement de contrôle de relais",
"Single phase meter event": "Événement de compteur monophasé",
"Power Quality Log": "Événement de qualité de réseau d’alimentation",
"Token Transfer Log": "Événement de transfert de Token ",
"Communication Log": "Événement de communication  ",
"Fraud Event Log": "Journal d＇événement de fraude ",
"Self define Log": "Événement d’autodéfinition ",
"Over voltage Log": "Événement de surtension ",
"Under voltage Log": "Événement de sous tension ",
"Normal over load Log": "Événement de surcharge normal",
"Purchase Log": "Événement d’achat ",
"Emergency over load Log": "Événement de surcharge urgent",
"Power Failure Log": "Événement de mise hors tension",
"Meter Register Log": "Événement de registre des compteurs",
"Top cover open Log": "Événement d’ouverture de couvercle de compteur",
"Terminal cover open Log": "Événement d’ouverture de cache-borne de compteur",
"Module cover open Log": "Événement d’ouverture de couvercle de module ",
"Battery voltage low Log": "Événement de basse tension de batterie ",
"Switch off": " Éteindre",
"Clear meter": "Supprimer le compteur ",
"Clear event ": "Éffacer les événements ",
"Clear demand ": "Éffacer les données de demande ",
"Program ": "Programme ",
"Power down": "Mise hors tension ",
"Cover open": "Ouverture de couvercle ",
"terminal open": "Ouverture de cache-borne",
"calibration time": "Temps d＇étalonnage",
"over voltage": "Surtension",
"under voltage": "Sous tension",
"Power reverse": "Puissance inversée",
"event summary": "Résumé de l＇événement",
"Power failure event log_hairui": "Journal d’événement de mise hors tension_hairui",
"Other fraud related event log_hairui": "Autre journal d’événement relatif à la fraude_hairui",
"Non-rollover(Cover open) related event log_hairui": "Journal d’événement relatif à non-basculement_hairui",
"Relay control related event log_hairui": "Journal d’événement relatif au contrôle de relais_hairui",
"Standard event log_hairui": "Journal d’événement standard_hairui",
"Firmware upgraded event log_hairui": "Journal d’événement de mise à jour de firmware_hairui",
"Payment related event log_hairui": "Journal d’événement relatif au paiement_hairui",
"Switch on": " Allumer"

	},

	"dictProfile": {
		"id": "ID de profil",
		"name": "Nom de profil",
		"type": "Type de profil ",
		"title": "Gestion des Modèle de profil",
		"list": "Liste des Modèle de profil",
		"idExist": "L’ID de profil existe déjà!",
		"nameExist": "Le nom de profil existe déjà!",
		"itemList": "Liste des canaux liés",
		"linkChannel": "Canal de liaison",
		"sortChannel": "Canal de tri",
		"selectProfile": "Veuillez d’abord sélectionner le profil!"
	},

	"storeTable": {
		"title": "Gestion de stockage",
		"list": "Liste des tables de stockage",
		"columnCount": "Nombre de colonne",
		"id": " ID de Table",
		"name": "Nom de la table",
		"columnList": "Liste des détails de la colonne",
		"columnId": " ID de colonne",
		"selectTable": "Veuillez d’abord sélectionner la table de stockage!",
		"idExist": "L’ID de table existe déjà!",
		"nameExist": "Le nom de la table existe déjà!",
		"delHasData": "Le tableau contenant des données ne peut pas être supprimé",
		"setProfile": "Le nom du profil est vide,impossible de lier le canal!"
	},

	"mdmActionOrder": {
		"title": "Détail de l’ordre de travail",
		"list": "Liste de l’ordre de travail",
		"id": "ID Ordre ",
		"sn": "Ordre SN",
		"orderType": "Type de l’ordre",
		"orderSource": "Source de données",
		"createUserId": "Créer par utilisateur",
		"priorityType": "Type de priorité",
		"orderDesc": " Description d’ordre",
		"processDesc": "Description du processus",
		"createTime": "Heure de création",
		"dueTime": "Heure d’échéance",
		"actualFinishTime": "Heure terminée",
		"currentStatus": "État",
		"sentCis": "Envoyé à CIS",
		"currentUserId": " Utilisateur actuel",
		"needSendCis": "Besoin d’envoyer CIS",
		"nextStatus": " État suivant",
		"assignUser": "Attribuer à l’utilisateur",
		"processDetail": "Détails du processus",
		"exceptionDetail": "Détails de l’exception",
		"createOrder": "Créer une commande",
		"processOrder": "Transaction de la commande",
		"searchType": "Type de recherche",
		"createdByMe": "Créé par moi",
		"dealedByMe": "Traité par moi",
		"assignedByMe": "Disttribué par moi",
		"deal": "Accord",
		"dealSuccess": "Réussite de l’ordre de travail!",
		"objectType": "Type d’objet",
		"objectName": "Nom d’objet",
		"noEvent": " Veuillez d’abord sélectionner l’événement!",
		"hasOrder": "Les événements sélectionnés ont été créés!"
	},

	"mdmActionOrderProcess": {
		"id": "ID de processus de commande",
		"dealUserId": " Utilisateur",
		"dealTime": "Heure de transaction",
		"dealDesc": " Description de transaction",
		"orderStatus": "État de la commande"
	},

	"mdmAlarmConfig": {
		"alarmMode": "Mode d’alarme",
		"deviceEvents": "Événements de périphérique",
		"veeEvents": "Événements de VEE",
		"orders": "Ordre",
		"eventType": "Type d’événement ",
		"eventName": "Nom d’événement",
		"exceptionType": "Type d’exception",
		"alarmSetting": "Configuration d’alarme",
		"index": "Index",
		"orderType": "Type de l’ordre",
		"eventId": "ID d’événement",
		"onlySubscribe": "S’abonner seulement"
	},
	"dictVeeEvent": {
		"id": "ID d’événement",
		"name": "Nom d’événement",
		"method": "Méthode VEE",
		"veeEvent": "Événements de Vee ",
		"descr": "Description",
		"sortId": "ID de tri",
		"exceptionName": "Type d’exception",
		"ruleType": "Type de règle",
		"eventNameNotNull": "Le nom de l’événement ne doit pas être nul",
		"estimationFlag": "Flag d’estimation",
		"isMutlDatasource": "Multi-sources des données",
		"datasourceType": "Type de sources des données "

	},
	"dictVeeEventDatasource": {
		"dataType": "Type des données",
		"dataCode": "Code des données",
		"schemeName": "Nom du schéma",
		"cycleType": "Type de cycle",
		"cycleCount": "Nombre de cycles",
		"keyFlag": "Flag clé",
		"startCycle": "Cycle de début",
		"sourceEventType": "Type d’événement source"
	},
	"dictVeeEventDataitem": {
		"dataitemKey": "Clé de paramètre",
		"dataitemId": "Valeur du paramètre",
		"descr": "Description",
		"cycleCount": "Nombre de cycle",
		"cycleType": "Type de cycle",
		"desc": "Description"
	},


	"sysUtility": {
		"licenseInfo": "Informations sur la licence",
		"newLicense": "Nouvelle licence",
		"companyName": "Nom de la compagnie",
		"licenseStartDate": "Date de début de licence actuelle",
		"licenseEndDate": "Date de fin de licence actuelle",
		"meterNum": "Numéro de compteur d’assistance actuel"
	},

	"afterAdd": {
		"addTask": "Ajouter une tâche",
		"execute": "Exécuter ",
		"content": "Contenu"
	},

	"mdmDataUpdateLog": {
		"dataTimeStart": "Heure de début des données",
		"dataTimeEnd": "Heure de fin des données ",
		"dataType": "Type des données",
		"schemeType": "Type de schéma",
		"type": "Type",
		"lastUpdateTime": "Importer l’heure de mise à jour",
		"lastDataTime": "Importer l’heure des données",
		"exportUpdateTime": "Exporter l’heure de mise à jour ",
		"exportDataTime": "Exporter l’heure des données",
		"title": "Journal de transfert de données",
		"list": "Liste de transfert de données",
		
  		"title1":"---------------------Data Import Log",
     	"title2":"---------------------Data Export Log",
     	"title3":"---------------------Data Export Management",
     	"list1":"---------------------Data Import Log",
     	"list2":"---------------------Data Export Log",
     	"list3":"---------------------Data Export Management",
     	"mdmDataitemId":"---------------------Data Channel",
     	"dstDataitemId":"---------------------Destination Data Channel"		
	},
	"mdmSysIntegrationDetailLog": {
		"reqId": "ID de la requête",
		"customerName": "Nom du client",
		"sn": "SDP SN",
		"meterSn": "Compteur SN",
		"reqResult": " Résultat de requête "
	},

	"replaceMeter": {
		"dataSource": "Source des données",
		"processingType": "Type de traitement",
		"new": "Nouveau",
		"old": "Ancien",
		"detail": "Détail",
		"tv": "Heure des données",
		"operationType": "Type d’opération ",
		"replaceTitle": "Remplacer les informations de données "

	},
	"unknowDeviceManageReport": {
		"unknowDeviceManageStartTimeTip": "Veuillez sélectionner l’heure de début du périphérique inconnu!",
		"unknowDeviceManageEndTimeTip": "Veuillez sélectionner l’heure de fin du périphérique inconnu!",
		"timeIssueAlert": "L’heure de fin du projet doit être postérieure à l’heure de début de la perte de ligne!",
		"title": "Rapport du gestionnaire d’équipement inconnu"
	},

	"mdmDataOutageStatistics": {
		"model": "Mode Statistiques",
		"tv": "Heure des données",
		"startTv": "Heure de début",
		"endTv": "Heure de fin",
		"totalTitle": "Analyse de mise hors tension",
		"manageTitle": "Gestion de mise hors tension",
		"totalList": "Liste d’analyse de mise hors tension",
		"manageList": "Liste de gestion de mise hors tension",
		"analysis": "Analyse de mise hors tension",
		"sdpTotalNum": "Nombre total (SDP)",
		"totalTime": "Durée totale de mise hors tension (Mins)",
		"totalNum": "Nombre total de mise hors tension",
		"avgTime": "Durée moyenne de mise hors tension (Mins)",
		"avgNum": "Nombre moyen de mise hors tension",
		"durationTime": "Durée de mise hors tension(Mins)",
		"greaterThanDurationTime": ">Durée(Mins)",
		"customerSn": "Client SN",
		"sdpName": "Nom SDP",
		"meterSn": "Compteur SN",
		"orgName": "Organisation",
		"lineName": "Nom de la ligne",
		"tranName": "Nom du transformateur ",
		"minutes": "Minutes",
		"objectName": "Nom de l’objet",
		"days": "Jours",
		"hours": "Heures",
		"all": "Tous",
		"byLine": "Par ligne"
	},
	"menu": {
		"Data Collection": "Collection des données",
		"Tools": "Outils",
		"System": "Système",
		"Provisioning": "Approvisionnement",
		"Meter Data Report": "Rapport des données de compteur",
		"Meter Event Report": "Rapport des événements de compteur",
		"Schedule Reads Report": "Rapport des lectures planifiées",
		"Miss Data Tracing": "Poursuite des données manquantes",
		"Collection Scheme Management": "Gestion des programmes de collecte",
		"Meter Group Management": "Gestion des groupes de compteurs",
		"Meter Configuration": "Configuration du compteur",
		"Firmware Upgrade": "Mise à jour du firmware",
		"On Demand Reads": "Lectures à la demande",
		"Connect / Disconnect": "Conecter /Déconnecter",
		"Asset Management": "Gestion d’actifs",
		"Deployment Management": "Gestion du déploiement",
		"Log Explorer": "Navigateur des journaux",
		"Permission": "Autorisation",
		"Data Export Management": "Gestion des exportations de données",
		"Meter Group Upgrade": "Mise à niveau du groupe de compteurs",
		"Data Management": "Gestion de données",
		"DCU Configuration - SG376": "Configuration DCU",
		"DCU Configuration": "Configuration du DCU",
		"On Demand Reads - CSG": "Lectures à la demande - CSG",
		"Calculation Object Define": "Définition d’objet de calcul",
		"Reports": "Rapport",
		"Communication Status Report": "Rapport d’état des communication",
		"DCU Group Upgrade":"Mise à jour de groupe de DCU",		
		"Data Transfer Management": "Data Transfer Management",
		"Engineer Tools": "Engineer Tools",
		"Data Analysis": "Data Analysis",
		"Object Management": "Object Management",
		"Billing Determinants": "Billing Determinants",
		"Calculation Scheme Management": "Calculation Scheme Management",
		"Billing Data Management": "Billing Data Management",
		"Loss Management": "Loss Management",
		"Energy Analysis": "Energy Analysis",
		"Summary Energy Analysis": "Summary Energy Analysis",
		"Load Analysis": "Load Analysis",
		"Load Rate Analysis": "Load Rate Analysis",
		"Imbalance Analysis": "Imbalance Analysis",
		"PF Analysis": "PF Analysis",
		"SDP Load Detail": "SDP Load Detail",
		"Revenue Protection": "Revenue Protection",
		"Meter Data Report Organization": "Organisation de rapport des données de compteur"
	},
	"profile":{
	    "Standard Event Log": "Journal d＇événement standard",
"Three phase meter event": "Événement de compteur triphasé",
"Relay Control Log": "Journal d＇événement de contrôle de relais",
"Single phase meter event": "Événement de compteur monophasé",
"Power Quality Log": "Événement de qualité de réseau d’alimentation",
"Token Transfer Log": "Événement de transfert de Token ",
"Communication Log": "Événement de communication  ",
"Fraud Event Log": "Journal d＇événement de fraude ",
"Self define Log": "Événement d’autodéfinition ",
"Over voltage Log": "Événement de surtension ",
"Under voltage Log": "Événement de sous tension ",
"Normal over load Log": "Événement de surcharge normal",
"Purchase Log": "Événement d’achat ",
"Emergency over load Log": "Événement de surcharge urgent",
"Power Failure Log": "Événement de mise hors tension",
"Meter Register Log": "Événement de registre des compteurs",
"Top cover open Log": "Événement d’ouverture de couvercle de compteur",
"Terminal cover open Log": "Événement d’ouverture de cache-borne de compteur",
"Module cover open Log": "Événement d’ouverture de couvercle de module ",
"Battery voltage low Log": "Événement de basse tension de batterie ",
"Switch off": " Éteindre",
"Clear meter": "Supprimer le compteur ",
"Clear event ": "Éffacer les événements ",
"Clear demand ": "Éffacer les données de demande ",
"Program ": "Programme ",
"Power down": "Mise hors tension ",
"Cover open": "Ouverture de couvercle ",
"terminal open": "Ouverture de cache-borne",
"calibration time": "Temps d＇étalonnage",
"over voltage": "Surtension",
"under voltage": "Sous tension",
"Power reverse": "Puissance inversée",
"event summary": "Résumé de l＇événement",
"Power failure event log_hairui": "Journal d’événement de mise hors tension_hairui",
"Other fraud related event log_hairui": "Autre journal d’événement relatif à la fraude_hairui",
"Non-rollover(Cover open) related event log_hairui": "Journal d’événement relatif à non-basculement_hairui",
"Relay control related event log_hairui": "Journal d’événement relatif au contrôle de relais_hairui",
"Standard event log_hairui": "Journal d’événement standard_hairui",
"Firmware upgraded event log_hairui": "Journal d’événement de mise à jour de firmware_hairui",
"Payment related event log_hairui": "Journal d’événement relatif au paiement_hairui",
"Switch on": " Allumer"
	}
	

}