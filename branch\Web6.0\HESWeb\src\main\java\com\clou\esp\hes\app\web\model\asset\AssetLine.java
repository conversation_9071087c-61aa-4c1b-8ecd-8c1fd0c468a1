package com.clou.esp.hes.app.web.model.asset;

import java.math.BigDecimal;

import com.clou.esp.hes.app.web.model.common.BaseEntity;


/**
 * @ClassName: AssetLine
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年9月3日 下午1:50:47
 *
 */
public class AssetLine extends BaseEntity{
	
	private static final long serialVersionUID = 1L;
	private String sn;
	private String name;
	private String orgId;
	private String orgName;
	private int lineType;
	private String lineTypeName;
	private BigDecimal voltageLevel;
	
	/**是否收藏*/
	private Boolean isCollect;


	public Boolean getIsCollect() {
		return isCollect;
	}

	public void setIsCollect(Boolean isCollect) {
		this.isCollect = isCollect;
	}
	public String getSn() {
		return sn;
	}
	public void setSn(String sn) {
		this.sn = sn;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getOrgId() {
		return orgId;
	}
	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public int getLineType() {
		return lineType;
	}
	public void setLineType(int lineType) {
		this.lineType = lineType;
	}
	public String getLineTypeName() {
		return lineTypeName;
	}
	public void setLineTypeName(String lineTypeName) {
		this.lineTypeName = lineTypeName;
	}
	public BigDecimal getVoltageLevel() {
		return voltageLevel;
	}
	public void setVoltageLevel(BigDecimal voltageLevel) {
		this.voltageLevel = voltageLevel;
	}
}
