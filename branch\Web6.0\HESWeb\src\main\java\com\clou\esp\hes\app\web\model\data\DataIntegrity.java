/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataIntegrity{ } 
 * 
 * 摘    要： dataIntegrity
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-12-01 06:03:45
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.data;

import java.math.BigDecimal;
import java.util.Date;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.excel.Excel;

public class DataIntegrity extends BaseEntity {

	/**
	 * 意义，目的和功能，以及被用到的地方<br>
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DataIntegrity() {
	}

	private String idType;

	private String profileId;

	/*** 设备SN */
	@Excel(name = "Serial Number", width = 15, groups = ValidGroup1.class)
	private String serizlNo;
	@Excel(name = "Date", width = 12, groups = ValidGroup1.class)
	private Date tv;

	@Excel(name = "Profile Name", width = 15, groups = ValidGroup1.class)
	private String profileName;

	private Date tvData;

	@Excel(name = "Manufacturer", width = 12, groups = ValidGroup1.class)
	private String mfrId;
	@Excel(name = "Model", width = 12, groups = ValidGroup1.class)
	private String modelId;
	@Excel(name = "Communication", width = 12, groups = ValidGroup1.class)
	private String commId;
	@Excel(name = "Communicator SN", width = 12, groups = ValidGroup1.class)
	private String communicatorSn;
	@Excel(name = "Progress", width = 18, groups = ValidGroup1.class)
	private Date progress;
	@Excel(name = "Last Task", width = 18, groups = ValidGroup1.class)
	private Date lastTask;
	@Excel(name = "Task Result", width = 14, groups = ValidGroup1.class)
	private String taskResult;

	@Excel(name = "Failure Cause", width = 10, groups = ValidGroup1.class)
	private String failureCause;
	
	@Excel(name = "Status Update Time", width = 12, groups = ValidGroup1.class)
	private Date updateTv;
	
	@Excel(name = "Com Status", width = 12, groups = ValidGroup1.class)
	private String comStatus;
	/** integrity */
	@Excel(name = "Ratio(%)", width = 10, groups = ValidGroup1.class)
	private java.math.BigDecimal integrity;
	@Excel(name = "Reference Analysis", width = 32, groups = ValidGroup1.class)
	private String analysis;
	private int countTotal;
	private int countActual;
	private String stv;
	private BigDecimal startIntegrity;

	private BigDecimal endIntegrity;
	private int tvType;
	private String delayDay;
	/** orgId */
	private java.lang.String orgId;
	private String commTypeId;


	public String getCommTypeId() {
		return commTypeId;
	}

	public void setCommTypeId(String commTypeId) {
		this.commTypeId = commTypeId;
	}

	public java.lang.String getOrgId() {
		return orgId;
	}

	public void setOrgId(java.lang.String orgId) {
		this.orgId = orgId;
	}
	
	

	public Date getTvData() {
		return tvData;
	}

	public void setTvData(Date tvData) {
		this.tvData = tvData;
	}

	public String getDelayDay() {
		return delayDay;
	}

	public void setDelayDay(String delayDay) {
		this.delayDay = delayDay;
	}

	/**
	 * integrity
	 * 
	 * @return the value of DATA_INTEGRITY.INTEGRITY
	 * @mbggenerated 2017-12-01 06:03:45
	 */
	public java.math.BigDecimal getIntegrity() {
		return integrity;
	}

	/**
	 * integrity
	 * 
	 * @param integrity
	 *            the value for DATA_INTEGRITY.INTEGRITY
	 * @mbggenerated 2017-12-01 06:03:45
	 */
	public void setIntegrity(java.math.BigDecimal integrity) {
		this.integrity = integrity;
	}

	public DataIntegrity(java.math.BigDecimal integrity) {
		super();
		this.integrity = integrity;
	}

	/**
	 * 1: meter
2:  manufacturer
3:: model
4: communication type
5、总体完整率
6、日1负荷曲线100抄读率
7、日1负荷曲线0-100抄读率
8、日1负荷曲线0抄读率
9、日1负荷曲线100抄读数
10、日1负荷曲线0-100抄读数
11、日1负荷曲线0抄读数
	 * @return
	 */
	public String getIdType() {
		return idType;
	}

	/**
	 * 1: meter
2:  manufacturer
3:: model
4: communication type
5、总体完整率
6、日1负荷曲线100抄读率
7、日1负荷曲线0-100抄读率
8、日1负荷曲线0抄读率
9、日1负荷曲线100抄读数
10、日1负荷曲线0-100抄读数
11、日1负荷曲线0抄读数
	 * @param idType
	 */
	public void setIdType(String idType) {
		this.idType = idType;
	}

	public String getProfileId() {
		return profileId;
	}

	public void setProfileId(String profileId) {
		this.profileId = profileId;
	}

	public Date getTv() {
		return tv;
	}

	public void setTv(Date tv) {
		this.tv = tv;
	}

	public String getSerizlNo() {
		return serizlNo;
	}

	public void setSerizlNo(String serizlNo) {
		this.serizlNo = serizlNo;
	}

	public String getMfrId() {
		return mfrId;
	}

	public void setMfrId(String mfrId) {
		this.mfrId = mfrId;
	}

	public String getModelId() {
		return modelId;
	}

	public void setModelId(String modelId) {
		this.modelId = modelId;
	}

	public String getCommId() {
		return commId;
	}

	public void setCommId(String commId) {
		this.commId = commId;
	}

	public Date getProgress() {
		return progress;
	}

	public void setProgress(Date progress) {
		this.progress = progress;
	}

	public Date getLastTask() {
		return lastTask;
	}

	public void setLastTask(Date lastTask) {
		this.lastTask = lastTask;
	}

	public String getTaskResult() {
		return taskResult;
	}

	public void setTaskResult(String taskResult) {
		this.taskResult = taskResult;
	}

	public String getFailureCause() {
		return failureCause;
	}

	public void setFailureCause(String failureCause) {
		this.failureCause = failureCause;
	}

	public String getAnalysis() {
		return analysis;
	}

	public void setAnalysis(String analysis) {
		this.analysis = analysis;
	}

	public int getCountTotal() {
		return countTotal;
	}

	public void setCountTotal(int countTotal) {
		this.countTotal = countTotal;
	}

	public int getCountActual() {
		return countActual;
	}

	public void setCountActual(int countActual) {
		this.countActual = countActual;
	}

	public String getStv() {
		return stv;
	}

	public void setStv(String stv) {
		this.stv = stv;
	}

	public BigDecimal getStartIntegrity() {
		return startIntegrity;
	}

	public void setStartIntegrity(BigDecimal startIntegrity) {
		this.startIntegrity = startIntegrity;
	}

	public BigDecimal getEndIntegrity() {
		return endIntegrity;
	}

	public void setEndIntegrity(BigDecimal endIntegrity) {
		this.endIntegrity = endIntegrity;
	}

	public int getTvType() {
		return tvType;
	}

	public void setTvType(int tvType) {
		this.tvType = tvType;
	}

	public String getCommunicatorSn() {
		return communicatorSn;
	}

	public void setCommunicatorSn(String communicatorSn) {
		this.communicatorSn = communicatorSn;
	}

	public Date getUpdateTv() {
		return updateTv;
	}

	public void setUpdateTv(Date updateTv) {
		this.updateTv = updateTv;
	}

	public String getComStatus() {
		return comStatus;
	}

	public void setComStatus(String comStatus) {
		this.comStatus = comStatus;
	}

	public String getProfileName() {
		return profileName;
	}

	public void setProfileName(String profileName) {
		this.profileName = profileName;
	}

}