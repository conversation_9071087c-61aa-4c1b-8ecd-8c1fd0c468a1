/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeterGroupMap{ } 
 * 
 * 摘    要： assetMeterGroupMap
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-01-25 07:22:09
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.asset;

import java.util.Date;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.clou.esp.hes.app.web.validation.ValidGroup2;
import com.clou.esp.hes.app.web.validation.ValidGroup3;
import com.power7000g.core.excel.Excel;

public class AssetMeterGroupMap  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public AssetMeterGroupMap() {
	}
    
	private java.lang.String meterId;
	
	/**scheduleSchemeId*/
	private java.lang.String scheduleSchemeId;
	/**minutely, hourly, daily, monthly*/
	private java.lang.String measurementGroupId;
	/**touGroupId*/
	private java.lang.String touGroupId;
	/**limiterGroupId*/
	private java.lang.String limiterGroupId;
	
	private java.lang.String type;
	private java.lang.String groupId;
	
	@Excel(name = "Device SN", width = 30, groups = {ValidGroup1.class,ValidGroup2.class,ValidGroup3.class})
	private java.lang.String sn;
	@Excel(name = "Group Type", width = 30, groups = {ValidGroup1.class,ValidGroup2.class})
	private java.lang.String groupType;
	@Excel(name = "Group", width = 30, groups = {ValidGroup1.class,ValidGroup2.class})
	private java.lang.String groupName;
	@Excel(name = "Model", width = 30, groups = {ValidGroup1.class,ValidGroup2.class,ValidGroup3.class})
	private java.lang.String model;
	@Excel(name = "Manufacturer", width = 30, groups = {ValidGroup1.class,ValidGroup2.class,ValidGroup3.class})
	private java.lang.String manufacturer;
	
	private Date startTime;
	private Date expiryTime;
	@Excel(name = "Plan Start Time", width = 30, groups = ValidGroup2.class)
	private String startTimeStr;
	@Excel(name = "Plan xpiry Time", width = 30, groups = ValidGroup2.class)
	private String expiryTimeStr;
	
	
	@Excel(name = "Device Type", width = 30, groups = ValidGroup3.class)
	private String deviceType;

	public String getDeviceType() {
		return deviceType;
	}

	public void setDeviceType(String deviceType) {
		this.deviceType = deviceType;
	}

	/**
	 * scheduleSchemeId
	 * @return the value of ASSET_METER_GROUP_MAP.SCHEDULE_SCHEME_ID
	 * @mbggenerated 2018-01-25 07:22:09
	 */
	public java.lang.String getScheduleSchemeId() {
		return scheduleSchemeId;
	}

	/**
	 * scheduleSchemeId
	 * @param scheduleSchemeId the value for ASSET_METER_GROUP_MAP.SCHEDULE_SCHEME_ID
	 * @mbggenerated 2018-01-25 07:22:09
	 */
    	public void setScheduleSchemeId(java.lang.String scheduleSchemeId) {
		this.scheduleSchemeId = scheduleSchemeId;
	}
	/**
	 * minutely, hourly, daily, monthly
	 * @return the value of ASSET_METER_GROUP_MAP.MEASUREMENT_GROUP_ID
	 * @mbggenerated 2018-01-25 07:22:09
	 */
	public java.lang.String getMeasurementGroupId() {
		return measurementGroupId;
	}

	/**
	 * minutely, hourly, daily, monthly
	 * @param measurementGroupId the value for ASSET_METER_GROUP_MAP.MEASUREMENT_GROUP_ID
	 * @mbggenerated 2018-01-25 07:22:09
	 */
    	public void setMeasurementGroupId(java.lang.String measurementGroupId) {
		this.measurementGroupId = measurementGroupId;
	}
	/**
	 * touGroupId
	 * @return the value of ASSET_METER_GROUP_MAP.TOU_GROUP_ID
	 * @mbggenerated 2018-01-25 07:22:09
	 */
	public java.lang.String getTouGroupId() {
		return touGroupId;
	}

	/**
	 * touGroupId
	 * @param touGroupId the value for ASSET_METER_GROUP_MAP.TOU_GROUP_ID
	 * @mbggenerated 2018-01-25 07:22:09
	 */
    	public void setTouGroupId(java.lang.String touGroupId) {
		this.touGroupId = touGroupId;
	}
	/**
	 * limiterGroupId
	 * @return the value of ASSET_METER_GROUP_MAP.LIMITER_GROUP_ID
	 * @mbggenerated 2018-01-25 07:22:09
	 */
	public java.lang.String getLimiterGroupId() {
		return limiterGroupId;
	}

	/**
	 * limiterGroupId
	 * @param limiterGroupId the value for ASSET_METER_GROUP_MAP.LIMITER_GROUP_ID
	 * @mbggenerated 2018-01-25 07:22:09
	 */
    	public void setLimiterGroupId(java.lang.String limiterGroupId) {
		this.limiterGroupId = limiterGroupId;
	}

	public java.lang.String getMeterId() {
		return meterId;
	}

	public void setMeterId(java.lang.String meterId) {
		this.meterId = meterId;
	}

	public AssetMeterGroupMap(String meterId, String scheduleSchemeId,
			String measurementGroupId, String touGroupId, String limiterGroupId) {
		super();
		this.meterId = meterId;
		this.scheduleSchemeId = scheduleSchemeId;
		this.measurementGroupId = measurementGroupId;
		this.touGroupId = touGroupId;
		this.limiterGroupId = limiterGroupId;
	}

	/**
	 * 1:Measurement,2:TOU,3:Limiter,4:stepTariffId
	 * @return
	 */
	public java.lang.String getType() {
		return type;
	}

	/**
	 * 1:Measurement,2:TOU,3:Limiter,4:stepTariffId
	 * @param type
	 */
	public void setType(java.lang.String type) {
		this.type = type;
	}

	public java.lang.String getGroupId() {
		return groupId;
	}

	public void setGroupId(java.lang.String groupId) {
		this.groupId = groupId;
	}

	public java.lang.String getSn() {
		return sn;
	}

	public void setSn(java.lang.String sn) {
		this.sn = sn;
	}

	public java.lang.String getGroupType() {
		return groupType;
	}

	public void setGroupType(java.lang.String groupType) {
		this.groupType = groupType;
	}

	public java.lang.String getGroupName() {
		return groupName;
	}

	public void setGroupName(java.lang.String groupName) {
		this.groupName = groupName;
	}

	public java.lang.String getModel() {
		return model;
	}

	public void setModel(java.lang.String model) {
		this.model = model;
	}

	public java.lang.String getManufacturer() {
		return manufacturer;
	}

	public void setManufacturer(java.lang.String manufacturer) {
		this.manufacturer = manufacturer;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getExpiryTime() {
		return expiryTime;
	}

	public void setExpiryTime(Date expiryTime) {
		this.expiryTime = expiryTime;
	}

	public String getStartTimeStr() {
		return startTimeStr;
	}

	public void setStartTimeStr(String startTimeStr) {
		this.startTimeStr = startTimeStr;
	}

	public String getExpiryTimeStr() {
		return expiryTimeStr;
	}

	public void setExpiryTimeStr(String expiryTimeStr) {
		this.expiryTimeStr = expiryTimeStr;
	}

}