update dict_dataitem_group set name = 'Prepaid' where id = 1004003;
delete from DICT_DATAITEM_GROUP_MAP where GROUP_ID = '1004003';

alter table DICT_DATAITEM add (DATAITEM_TYPE VARCHAR2(16));
COMMENT ON COLUMN DICT_DATAITEM.DATAITEM_TYPE IS '1000-电量,1001-正向有功,1002-反向有功,1003-正向无功,1004-反向无功,1011-rate 1,1012-rate 2,1013-rate 3,1014-rate 4,2000-瞬时量,2001-功率,2002-电压,2003-电流,2004-功率因数,3000-事件';
alter table DICT_DATAITEM add (IS_SHOW NUMBER(2,0));
COMMENT ON COLUMN DICT_DATAITEM.IS_SHOW IS '0: do not show at webpage; 1: can show at webpage';
update DICT_DATAITEM set IS_SHOW=1;

insert into dict_dataitem values('40.0.0.0.0', 100, 'Step tariff', null, null, 'RW', 1, null, null);
insert into dict_dataitem values('*********', 100, 'Friendly period', '1#0.0.96.54.2.255#2', null, 'RW', 1, null, null);
insert into dict_dataitem values('*********', 100, 'Friendly week', '1#0.0.96.54.3.255#2', null, 'RW', 1, null, null);
insert into dict_dataitem values('*********', 100, 'Friendly special day', '11#********.0.255#2', null, 'RW', 1, null, null);
insert into DICT_DATAITEM_GROUP_MAP values('1004003', '40.0.0.0.0', 1);
insert into DICT_DATAITEM_GROUP_MAP values('1004003', '*********', 2);
insert into DICT_DATAITEM_GROUP_MAP values('1004003', '*********', 3);
insert into DICT_DATAITEM_GROUP_MAP values('1004003', '*********', 4);

UPDATE DICT_OPERATION SET OPERATIONNAME = 'Prepaid Tab Page' WHERE ID = '1006004';
UPDATE DICT_OPERATION SET DESCRIPTION = 'View Prepaid Management Page' WHERE ID = '1006004';
UPDATE DICT_OPERATION SET OPERATIONNAME = 'Add Prepaid group' WHERE ID = '1006012';
UPDATE DICT_OPERATION SET DESCRIPTION = 'Add Prepaid group' WHERE ID = '1006012';
UPDATE DICT_OPERATION SET OPERATIONNAME = 'Edit and Save Prepaid group' WHERE ID = '1006016';
UPDATE DICT_OPERATION SET DESCRIPTION = 'Edit and Save Prepaid group' WHERE ID = '1006016';

INSERT INTO DICT_OPERATION VALUES ('1006017','Step Tariff Tab Page in Prepaid Group','1006','StepTariffTabPageinPrepaidGroup',17,'Step Tariff Tab Page in Prepaid Group');
INSERT INTO DICT_OPERATION VALUES ('1006018','Friendly Tab Page in Prepaid Group','1006','FriendlyTabPageinPrepaidGroup',18,'Friendly Tab Page in Prepaid Group');
INSERT INTO DICT_OPERATION VALUES ('1006019','TOU Tariff Tab Page in Prepaid Group','1006','TOUTariffTabPageinPrepaidGroup',19,'TOU Tariff Tab Page in Prepaid Group');
