/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataUserEventProgress{ } 
 * 
 * 摘    要： dataUserEventProgress
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-11-16 02:57:56
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.dataUserEvent;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DataUserEventProgress  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DataUserEventProgress() {
	}

	/**progress*/
	private java.util.Date progress;
//	/**updateTv*/
//	private java.util.Date updateTv;
	protected String userId;
	/**
	 * progress
	 * @return the value of DATA_USER_EVENT_PROGRESS.PROGRESS
	 * @mbggenerated 2018-11-16 02:57:56
	 */
	
	
	
	public java.util.Date getProgress() {
		return progress;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	/**
	 * progress
	 * @param progress the value for DATA_USER_EVENT_PROGRESS.PROGRESS
	 * @mbggenerated 2018-11-16 02:57:56
	 */
    	public void setProgress(java.util.Date progress) {
		this.progress = progress;
	}
	/**
	 * updateTv
	 * @return the value of DATA_USER_EVENT_PROGRESS.UPDATE_TV
	 * @mbggenerated 2018-11-16 02:57:56
	 */
//	public java.util.Date getUpdateTv() {
//		return updateTv;
//	}
//
//	/**
//	 * updateTv
//	 * @param updateTv the value for DATA_USER_EVENT_PROGRESS.UPDATE_TV
//	 * @mbggenerated 2018-11-16 02:57:56
//	 */
//    	public void setUpdateTv(java.util.Date updateTv) {
//		this.updateTv = updateTv;
//	}

	public DataUserEventProgress(java.util.Date progress 
	 ) {
		super();
		this.progress = progress;
		
	}

}