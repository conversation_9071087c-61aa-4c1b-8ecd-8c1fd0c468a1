/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictMeterDataStorageTable{ } 
 * 
 * 摘    要： dictMeterDataStorageTable
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.dict;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DictMeterDataStorageTable  extends BaseEntity {

	private static final long serialVersionUID = 8530687860895320554L;

	public DictMeterDataStorageTable() {}

	private String 		name;
	private String      profileId;
	private String      profileName;
	private int     	columnCount;
	
	private String      oldId;
	
	
	public String getOldId() {
		return oldId;
	}

	public void setOldId(String oldId) {
		this.oldId = oldId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	public int getColumnCount() {
		return columnCount;
	}

	public void setColumnCount(int columnCount) {
		this.columnCount = columnCount;
	}

	public String getProfileId() {
		return profileId;
	}

	public void setProfileId(String profileId) {
		this.profileId = profileId;
	}

	public String getProfileName() {
		return profileName;
	}

	public void setProfileName(String profileName) {
		this.profileName = profileName;
	}
	
}