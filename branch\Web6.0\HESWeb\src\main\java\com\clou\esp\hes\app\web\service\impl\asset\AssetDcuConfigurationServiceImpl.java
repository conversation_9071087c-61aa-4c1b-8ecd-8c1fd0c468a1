/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeter{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-06 08:04:55
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.asset;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.asset.AssetDcuConfigurationDao;
import com.clou.esp.hes.app.web.model.asset.DcuConfiguration;
import com.clou.esp.hes.app.web.model.asset.DcuConfiguration300;
import com.clou.esp.hes.app.web.service.asset.AssetDcuConfigurationService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@Component
@Service("assetDcuConfigurationService")
public class AssetDcuConfigurationServiceImpl extends CommonServiceImpl<DcuConfiguration>
		implements AssetDcuConfigurationService {

	@Resource
	private AssetDcuConfigurationDao dcuConfigurationDao;

	@Autowired
	public void setCommonService() {
		super.setCommonService(dcuConfigurationDao);
	}

	public AssetDcuConfigurationServiceImpl() {
	}

	@Override
	public JqGridResponseTo findMetersForJqGrid(JqGridSearchTo jqGridSearchTo) {
		// TODO Auto-generated method stub
		PageHelper
		.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		PageInfo<DcuConfiguration> pageInfo = new PageInfo<DcuConfiguration>(dcuConfigurationDao.findMetersForJqGrid(jqGridSearchTo));
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}
	
	@Override
	public JqGridResponseTo findMetersForJqGrid300(JqGridSearchTo jqGridSearchTo) {
		// TODO Auto-generated method stub
		PageHelper
		.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		PageInfo<DcuConfiguration300> pageInfo = new PageInfo<DcuConfiguration300>(dcuConfigurationDao.findMetersForJqGrid300(jqGridSearchTo));
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}

	@Override
	public DcuConfiguration queryComType(String comType) {
		// TODO Auto-generated method stub
		return dcuConfigurationDao.queryComType(comType);
	}
}