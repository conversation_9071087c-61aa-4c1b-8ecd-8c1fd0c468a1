Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('12',1,'DLMS/COSEM','DLMS/COSEM');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('14',1,'State grid company','国网公司');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('14',2,'Provincial company','省公司');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('14',3,'City company','地市公司');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('14',4,'Dostroct and county company','区县公司');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('14',5,'Subsidairy','分公司');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('14',6,'Power supply station','供电所');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('14',7,'Headquarter','总部');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('14',8,'Branches','分部');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('14',9,'System Software Provider','系统软件提供单位');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('21',1,'Enable','启用');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('21',2,'Disable','禁用');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('21',3,'Account Locked','账户锁定');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('21',4,'Dimission','离职');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('27',1,'Common Step Tariff','普通的阶梯费率');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('27',2,'Special Step Tariff','特殊的阶梯费率');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('28',1,'Enable','启用');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('28',2,'Invalid','失效');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('28',3,'Cancel','作废');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('29',1,'Single Tariff','单费率');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('29',2,'Step Tariff','阶梯费率');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('29',3,'TOU Tariff','分时电价');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('3',1,'Yes','是');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('3',2,'No','否');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('30',1,'1.0','1.0');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('30',2,'2.0','2.0');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('30',3,'0.5','0.5');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('30',4,'0.2','0.2');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('30',5,'0.2S','0.2S');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('4',1,'3P3W','三相三线');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('4',2,'3P4W','三相四线');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('4',3,'Single Phase','单相');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('47',1,'Bussiness Hall','营业大厅');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('47',2,'Vending Station','直属售电点');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('47',3,'Vending Agency','代售点');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('48',1,'Allow vending','是(允许售电)');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('48',2,'Stop vending','锁定(不允许售电)');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('5',1,'To be installed','待装');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('5',2,'To be run','待投');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('5',3,'Running','运行');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('5',4,'Stopped','停运');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('5',5,'Error','故障');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('5',6,'Installed '||'&'||' Unverified','新装未核对');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('5',7,'Changed '||'&'||' Unverified','变更未核对');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('5',8,'Change meter','换表');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('5',9,'Remove','拆除');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('53',1,'GPRS','GPRS');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('53',2,'PLC','PLC');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('53',3,'RF','RF');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('53',4,'Offline','OFF-Line');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('54',0,'Load Profile Setting Token','设置最大负荷Token');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('54',1,'Electricity Clear Token','清除电量Token');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('54',3,'Recharge Token','充值Token');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('54',4,'Key Change Token','变更密钥Token');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('54',5,'Electricy Trip Clear Token','清窃电Token');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('54',15,'设置卸载电量','设置卸载电量');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('56',1,'Charge by month','按月收取');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('56',2,'Chagre by time','按次收取');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('6',1,'Yes','是(参与计算) ');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('6',2,'No','否(不参与计算) ');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('60',1,'Street lighting taxes','街道照明税费');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('60',2,'Government taxes and fees','政府税费');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('60',3,'Government subsidies','政府津贴');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('60',4,'Subsidy','补贴');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('60',5,'VAT','增值税');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('60',6,'NHIS','国家保险计划征税');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('60',7,'Subsidy for VAT','增值税补贴');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('60',8,'Service Charge','服务费');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('60',9,'Subsidy One','补助1');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('60',10,'Subsidy Two','补助2');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('60',11,'Subsidy Three','补助3');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('60',12,'Utility Relief','税费%*用电量');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('60',13,'Power','用电量');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('60',14,'Initial Credit','初始债务');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('60',15,'Meter Replacement Debt','换表债务');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('60',16,'Other Debt','其他债务');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('64',1,'Money Recharge','电费充值');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('64',2,'Electricity Recharge','电量充值');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('65',1,'Normal','正常');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('65',2,'Locked (cant buy electricity)','锁定(不允许售电)');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('65',3,'Disabled','销户');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',68,'Response invalid data','响应无效数据');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',69,'DCU denies the request','DCU拒绝请求');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('92',-1,'N/A (Not applicable)','Token不适用');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('92',0,'(0)Token format result OK','(0)Token格式正确');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('92',1,'(1)Authentication result OK','(1)身份认证成功');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('92',2,'(2)Validation result OK','(2)验证成功');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('92',3,'(3)Token execution result OK','(3)Token执行成功');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('92',4,'(4)Token format failure','(4)Token格式失败');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('92',5,'(5)Authentication failure','(5)身份认证失败');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('92',6,'(6)Validation result failure','(6)验证结果失败');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('92',7,'(7)Token execution result failure','(7)Token 执行结果失败');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('92',8,'(8)Token received and not yet processed','(8)Token已收到，尚未处理');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('93',0,'Invalid','无效');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('93',1,'Valid','有效');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('93',2,'To be confirmed','待确认');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('82',0,'Customer Recharge','用户充值');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('82',1,'Vending Correction','撤销充值');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('83',1,'Pending','待切换');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('81',1,'Residential','居民');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('81',2,'Non-Residential','非居民');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('83',4,'Successed','切换成功');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('84',0,'Dsiabled','未启用 ');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('84',1,'Enabled','启用 ');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('84',2,'Stopped Enabled','作废');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('66',1,'Normal','正常');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('66',2,'Stop','停用');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('66',3,'Expired','过期');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('67',1,'Common Log','普通日志');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('67',2,'Alarm Log','告警日志');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('67',3,'Error Log','报错日志');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('68',1,'Add','新增');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('68',2,'Delete','删除');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('68',3,'Edit','更新');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('68',4,'Query','查询');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('68',5,'Upload','上传');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('68',6,'Login','登录');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('68',7,'Logout','退出系统');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('68',8,'Other','其他');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('69',1,'Login','登录');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('69',2,'Log out','退出系统');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('70',1,'Normal','正常(不删除)');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('70',2,'Delete','删除');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('71',1,'Super User','超级用户(科陆)');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('71',2,'System User','系统用户(租户约束,客户超级管理员)');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('71',3,'Ordinary User','普通用户(客户的,带权限的)');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('72',1,'Station Recharge','售电站充值');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('72',2,'Customer Recharge','用户充值');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('72',3,'Free Token','免费token');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('72',4,'Cancelled','用户充值撤销');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('73',1,'Cash Sale','现金');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('73',2,'Cheque','支票');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('73',3,'Credit Card Via Terminal','信用卡');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('73',4,'Debit Care Via Terminal','借记卡');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('73',5,'Free','免费');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('74',1,'Initial Debt','初始债务');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('74',2,'Change Meter Debt','换表债务');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('74',3,'Other Debt','其它债务');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('75',1,'Normal','正常');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('75',2,'Obsoleted','作废');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('83',2,'Switching','已下发');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('83',3,'Failed','切换失败');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('85',1,'Successful','成功');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('85',2,'Failure','失败');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('86',1,'Prepaid','预付费电表');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('86',2,'Postpaid','后付费电表');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('87',1,'paid','已缴');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('87',2,'unpaid','未缴');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('88',1,'Current month','当月');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('88',2,'Next month','下月');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('89',0,'Customer Recharge','用户充值');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('89',1,'Vending Correction','撤销充值');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('90',1,'Handled successfully','已成功处理');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('90',2,'Undisposed','未处理');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('90',3,'Handled failure','处理失败');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('90',4,'Invalid','作废');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',1,'Success / No errors','OK');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',2,'Partial result (additional results conveyed in separate messages)','部分结果(在单独消息中传递的其他结果)');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',3,'Partial result (no further results to follow)','部分结果(没有后续结果)');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',4,'IEC61968-9 ACK OK','IEC61968-9 ACK OK');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',5,'Mandatory Header elements missing','缺少强制头元素');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',6,'Mandatory Request elements missing','缺少强制请求元素');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',7,'Mandatory Payload elements missing','缺少强制有效载荷元素');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',8,'Format of request does not validate against schema','请求的格式不能根据模式进行验证');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',9,'Unsupported message revision in Header','标题中不支持的消息修订');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',10,'Invalid Meter(s)(This meter does not exist in the HES system)','无效的电表(HES系统中不存在此电表)');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',11,'Invalid Noun','无效的名词');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',12,'Invalid ReadingType(s)','无效的读类型');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',13,'Invalid Verb','无效的动词');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',14,'Unsupported ReadingType(s)','不支持的读类型');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',15,'Invalid UsagePoint(s)','无效的使用点');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',16,'Meter / UsagePoint mismatch','表/使用点不匹配');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',17,'Invalid Source','无效的来源');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',18,'Invalid Request ID(s)','无效的请求ID');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',19,'Invalid ServiceLocation(s)','无效的服务点位置');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',20,'Meter / ServiceLocation mismatch*','表/服务点位置不匹配*');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',21,'ComModule / Meter mismatch*','通讯模块/表不匹配*');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',22,'Invalid CustomerAccount(s)','无效的客户账户');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',23,'Invalid ServiceSupplier(s)','无效的服务供应商');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',24,'CustomerAccount / ServiceSupplier mismatch','客户帐户/服务供应商不匹配');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',25,'Invalid Customer(s)','无效的客户');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',26,'Customer / CustomerAccount mismatch','客户/客户帐户不匹配');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',27,'Invalid CustomerAgreement(s)','无效的客户协议');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',28,'CustomerAccount / CustomerAgreement mismatch','客户帐户/客户协议不匹配');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',29,'CustomerAgreement / UsagePoint mismatch','客户协议/使用点不匹配');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',30,'CustomerAccount / UsagePoint mismatch','客户帐户/使用点不匹配');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',31,'ServiceSupplier / UsagePoint mismatch','服务供应商/使用点不匹配');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',32,'Object relationship mismatch','对象关系不匹配');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',33,'Invalid ComModule(s)','无效的通讯模块');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',34,'Invalid ServiceCategory(ies)','无效的服务类别(ies)');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',35,'Invalid UsagePointLocation(s)','无效使用点位置');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',36,'Invalid PricingStructure(s)','无效的定价结构');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',37,'Too many items in request','请求的项目太多');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',38,'Too many pending requests','等待的请求太多');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',39,'Request timed out','请求超时');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',40,'Unable to process the request - high system activity level','无法处理请求高的系统活动级别');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',41,'Unable to process request -transaction not attempted','无法处理请求-未尝试事务');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',42,'Unable to process the request - transaction attempted and failed ','无法处理请求事务——尝试并失败');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',43,'Unable to process the request - multiple error types encountered','无法处理遇到的请求-多个错误类型');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',44,'Some or all of the requested ReadingTypes are unavailable in MDMS','某些或所有请求的读取类型在MDMS中不可用');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',45,'Some or all of the requested ReadingTypes are unavailable in AMI','在AMI中，一些或所有请求的读取类型都不可用');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',46,'Some or all of the requested data is unavailable','请求的部分或全部数据不可用');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',47,'Unable to process the request – mandatory field(s) missing','无法处理缺少的请求强制字段');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',48,'Transaction aborted to maintain transactional integrity','事务中止以维护事务完整性');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',49,'Request canceled per business rule','根据业务规则取消请求');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',50,'Request placed on hold per business rule','根据业务规则搁置的请求');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',51,'Request released from business rule hold','从业务规则保持中释放的请求');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',52,'Request rescheduled per business rule','请求按业务规则重新调度');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',53,'Request canceled by user','用户取消的请求');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',54,'Temporary authentication failure','临时身份验证失败');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',55,'Authentication required','需要认证');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',56,'Authentication mechanism insufficient','认证机制不足');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',57,'Authentication failure','认证失败');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',58,'Action not authorized for user','未经用户授权的操作');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',59,'Authentication mechanism requires encryption','认证机制需要加密');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',60,'Policy violation','违反安全策略');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',61,'Request time out','请求超时');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',62,'Local error in processing','程序本地处理错误');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',63,'Meter unidentified responds','表计回复报文无法识别');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',64,'Channel is busy','通道繁忙');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',65,'(Meter) Device offline','(电表)设备离线');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',66,'Cosem associate failed','cosem层建立连接失败');
Insert into PPM_DICT_DETAIL (DICT_ID,INNER_VALUE,GUI_DISPLAY_NAME,README) values ('91',67,'Packet decrypt failed','包解密失败');

Insert into PPM_DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('2000','Home','systemController/main.do','Home');
Insert into PPM_DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('2001','Customer Recharge','customerRechargeController/toCustomerRechargePage.do','Recharge');
Insert into PPM_DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('2002','Engineering Tokens','vendEngineeringTokensController/list.do','Recharge');
Insert into PPM_DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('2003','Vending Station Recharge','vendStationRechargeController/list.do','Recharge');
Insert into PPM_DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('2004','Vending Cancellation','vendUnloadElectricityManageController/list.do','Recharge');
Insert into PPM_DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('2006','Permission','sysUserController/sysUserAndRoleList.do','System');
Insert into PPM_DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('2007','Vending Station','assetVendStationController/list.do','System');
Insert into PPM_DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('2008','Security Module','assetSecurityModuleController/list.do','System');
Insert into PPM_DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('2009','Customer','assetCustomerController/list.do','Asset Management');
Insert into PPM_DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('2010','Debt','vendMeterDebtController/list.do','Asset Management');
Insert into PPM_DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('2011','Tariff Group','assetTariffSolutionController/list.do','Tariff Management');
Insert into PPM_DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('2012','Log Explorer','ppmDataUserLogController/list.do','System');
Insert into PPM_DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('2013','Meter Data Report','meterDataReportController/list.do','Report');
Insert into PPM_DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('2014','Customize Report',null,'Report');

Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2009001','Customer Manage Add','2009','/assetCustomerController/assetCustomer.do?add',1,'Customer Manage Add');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2009002','Customer Manage Edit','2009','/assetCustomerController/assetCustomer.do?edit',1,'Customer Manage Edit');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2009003','Customer Manage Delete','2009','/assetCustomerController/del.do',1,'Customer Manage Delete');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2008001','Security Recharge','2008','/assetSecurityModuleController/updateSecurityModuleCreditBalance.do',1,'Security Recharge');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2008002','Read Balance','2008','/assetSecurityModuleController/getSecurityModuleCreditBalance.do',2,'Read Balance');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2008003','Upload Key File','2008','/assetSecurityModuleController/loadKeyFileToSecurityModule.do',3,'Upload Key File');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017005','Add/Edit user','1017','/sysUserController/toAddSysUser.do',2,'Add user; Edit user; Reset password');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017007','Add/Edit role','1017','/sysRoleController/toAddSysRole.do',5,'Add/Edit role');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017008','Delete role','1017','/sysRoleController/deleteRole.do',6,'Delete role');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017006','Delete user','1017','/sysUserController/del.do',3,'Delete user');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017009','Add/Edit/Remove organization','1017','/sysOrgController/toAddSysOrg.do',8,'Add, edit or remove organization');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017010','Delete organization','1017','/sysOrgController/del.do',9,'Delete organization');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017001','View User tab page','1017','ViewUserTabPage',1,'View User tab page');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017002','View Role tab page','1017','ViewRoleTabPage',4,'View Role tab page');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017003','View Organization tab page','1017','ViewOrganizationTabPage',7,'View Organization tab page');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2001003','Send Token','2001','sendRechargeToken',3,'Send Recharge Token Button');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2001004','Send Token '||'&'||' Print','2001','issuedTokenAndPrint',4,'Print Ticket Button');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2001005','Print Ticket','2001','printRechargeTicket',5,'Print Recharge Ticket');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2007001','Vend Station Manage Add','2007','/assetVendStationController/assetVendStation.do?add',1,'Vend Station Manage Add');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2007002','Vend Station Manage Edit','2007','/assetVendStationController/assetVendStation.do?edit',1,'Vend Station Manage Edit');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2007003','Vend Station Manage Delete','2007','/assetVendStationController/del.do',1,'Vend Station Manage Delete');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2003001','Vend Station Recharge','2003','/vendStationRechargeController/save.do',1,'Vend Station Recharge');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2004001','Send Token','2004','/vendUnloadElectricityManageController/pushUnloadElectricityToken.do',1,'Send Token');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2004002','Print','2004','/vendUnloadElectricityManageController/getUnloadElectricityTokenTicket.do',2,'Print');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2002005','Send Token','2002','/vendEngineeringTokensController/publishToken.do',6,'Send Token');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2002001','Clear Tamper','2002','clearTamper',1,'clear Tamper');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2002002','Clear Credit','2002','clearCredit',2,'clear Credit');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2002003','Change Secret Key','2002','keyChange',3,'Change Secret Key');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2002004','Set Max Power','2002','setMaxPower',4,'Set Max Power');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2002006','Print','2002','/vendEngineeringTokensController/jumpToTicketPrintPage.do',5,'Print ');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2006007','Add/Edit role','2006','/sysRoleController/toAddSysRole.do',5,'Add/Edit role');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2006008','Delete role','2006','/sysRoleController/deleteRole.do',6,'Delete role');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2006006','Delete user','2006','/sysUserController/del.do',3,'Delete user');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2006009','Add/Edit/Remove organization','2006','/sysOrgController/toAddSysOrg.do',8,'Add, edit or remove organization');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2006010','Delete organization','2006','/sysOrgController/del.do',9,'Delete organization');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2006001','View User tab page','2006','ViewUserTabPage',1,'View User tab page');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2006002','View Role tab page','2006','ViewRoleTabPage',4,'View Role tab page');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2006003','View Organization tab page','2006','ViewOrganizationTabPage',7,'View Organization tab page');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2001001','Recharge','2001','/customerRechargeController/saveCustomerRechargeInfo.do',1,'Customer Recharge');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2001002','Print Again','2001','/customerRechargeController/customerRechargePrintAgain.do',2,'Customer Recharge');

Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Vending Recharge','Recharge',1);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('vending Cancellation','Cancellation',2);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Engineering Token','Clear Tamper',1);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Engineering Token','Clear Credit',2);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Engineering Token','Change Secret Key',3);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Engineering Token','Change Max Power',4);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Customer Management','Add Customer',1);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Customer Management','Modify Customer',2);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Customer Management','Delete Customer',3);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Debt Management','Add Debt',1);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Debt Management','Delete Debt',2);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Vending Station Recharge','Recharge',1);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Vending Station Recharge','Cancellation',2);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Tariff Group Management','Add Tariff Group',1);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Tariff Group Management','Modify Tariff Group',2);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Tariff Group Management','Delete Tariff Group',3);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Tariff Group Management','Set Passvie Tariff Group',4);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Tariff Group Management','Activate Tariff Group',5);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Vending Station Management','Add Vending Station',1);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Vending Station Management','Delete Vending Station',2);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Vending Station Management','Modify Vending Station',3);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission Management','Add User',1);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission Management','Modify User',2);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission Management','Delete User',3);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission Management','Set Password',4);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('login/logout','Login',1);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('login/logout','Logout',2);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission Management','Add Role',1);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission Management','Modify Role',2);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission Management','Delete Role',3);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission Management','Add Organization',4);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission Management','Modify Organization',5);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission Management','Delete Organization',6);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Security Module Management','Recharge',1);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Security Module Management','Upload Key File',2);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Security Module Management','Set SGC Default',3);

Insert into PPM_SYS_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1e458f11beda11e79bb968f728c50006','1',2,'Cash Recipts By Transaction Type In',4,'uReportController/toCashReciptsByTransaction.do','1e858f11beda11e79bb968f728c50006',null,null,null);
Insert into PPM_SYS_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1e458f11beda11e79bb968f728c50007','1',2,'Cashier Summary Report',5,'uReportController/toCashierSummary.do','1e858f11beda11e79bb968f728c50006',null,null,null);
Insert into PPM_SYS_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1e858f11beda11e79bb968f728c52001','1',2,'Customer Recharge',1,'customerRechargeController/toCustomerRechargePage.do','1e858f11beda11e79bb968f728c50002',null,'2001',null);
Insert into PPM_SYS_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1e858f11beda11e79bb968f728c52002','1',2,'Engineering Tokens',2,'vendEngineeringTokensController/list.do','1e858f11beda11e79bb968f728c50002',null,'2002',null);
Insert into PPM_SYS_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1e858f11beda11e79bb968f728c52003','1',2,'Vending Station Recharge',3,'vendStationRechargeController/list.do','1e858f11beda11e79bb968f728c50002',null,'2003',null);
Insert into PPM_SYS_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1e858f11beda11e79bb968f728c52004','1',2,'Vending Cancellation',4,'vendUnloadElectricityManageController/list.do','1e858f11beda11e79bb968f728c50002',null,'2004',null);
Insert into PPM_SYS_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1e858f11beda11e79bb968f728c52006','1',2,'Permission',1,'sysUserController/sysUserAndRoleList.do','1e858f11beda11e79bb968f728c50005',null,'2006',null);
Insert into PPM_SYS_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1e858f11beda11e79bb968f728c52007','1',2,'Vending Station',2,'assetVendStationController/list.do','1e858f11beda11e79bb968f728c50005',null,'2007',null);
Insert into PPM_SYS_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1e858f11beda11e79bb968f728c52008','1',2,'Security Module',3,'assetSecurityModuleController/list.do','1e858f11beda11e79bb968f728c50005',null,'2008',null);
Insert into PPM_SYS_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1e858f11beda11e79bb968f728c52009','1',2,'Customer',1,'assetCustomerController/list.do','1e858f11beda11e79bb968f728c50003',null,'2009',null);
Insert into PPM_SYS_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1e858f11beda11e79bb968f728c52010','1',2,'Debt',2,'vendMeterDebtController/list.do','1e858f11beda11e79bb968f728c50003',null,'2010',null);
Insert into PPM_SYS_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1e858f11beda11e79bb968f728c52011','1',2,'Tariff Group',1,'assetTariffSolutionController/list.do','1e858f11beda11e79bb968f728c50004',null,'2011',null);
Insert into PPM_SYS_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1e858f11beda11e79bb968f728c52012','1',2,'Log Explorer',4,'ppmDataUserLogController/list.do','1e858f11beda11e79bb968f728c50005',null,'2012',null);
Insert into PPM_SYS_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('21e858f11beda11e79bb968f728c5013','1',2,'Meter Data Report',1,'meterDataReportController/list.do','1e858f11beda11e79bb968f728c50006',null,'2013',null);
Insert into PPM_SYS_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1e858f11beda11e79bb968f728c52014','1',2,'Customize Report',2,null,'1e858f11beda11e79bb968f728c50006',null,'2014',null);
Insert into PPM_SYS_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1e858f11beda11e79bb968f728c50001','1',1,'Home',1,'systemController/main.do','0',null,'2000',null);
Insert into PPM_SYS_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1e858f11beda11e79bb968f728c50002','1',1,'Recharge',2,null,'0',null,null,null);
Insert into PPM_SYS_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1e858f11beda11e79bb968f728c50003','1',1,'Asset Management',3,null,'0',null,null,null);
Insert into PPM_SYS_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1e858f11beda11e79bb968f728c50004','1',1,'Tariff Management',4,null,'0',null,null,null);
Insert into PPM_SYS_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1e858f11beda11e79bb968f728c50005','1',1,'System',6,null,'0',null,null,null);
Insert into PPM_SYS_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1e858f11beda11e79bb968f728c50006','1',1,'Report',5,null,'0',null,null,null);
Insert into PPM_SYS_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1e858f11beda11e79bb968f728c50300','1',2,'Cashier Residential Report',3,'uReportController/toCashierResidentialReport.do','1e858f11beda11e79bb968f728c50006',null,'2015',null);

Insert into PPM_SYS_ROLE (ID,UTILITY_ID,NAME,DESCRIPTION) values ('ROLE0000000000000000000000000002','1','Cashier','Cashier');
Insert into PPM_SYS_ROLE (ID,UTILITY_ID,NAME,DESCRIPTION) values ('ROLE0000000000000000000000000001','1','Administrator','Administrator');

Insert into PPM_SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ROLE0000000000000000000000000001','1e858f11beda11e79bb968f728c50001',null);
Insert into PPM_SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ROLE0000000000000000000000000001','1e858f11beda11e79bb968f728c50002',null);
Insert into PPM_SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ROLE0000000000000000000000000001','1e858f11beda11e79bb968f728c52001','2001001,2001002,2001003,2001004,2001005');
Insert into PPM_SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ROLE0000000000000000000000000001','1e858f11beda11e79bb968f728c52002','2002001,2002002,2002003,2002004,2002006,2002005');
Insert into PPM_SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ROLE0000000000000000000000000001','1e858f11beda11e79bb968f728c52003','2003001');
Insert into PPM_SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ROLE0000000000000000000000000001','1e858f11beda11e79bb968f728c52004',null);
Insert into PPM_SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ROLE0000000000000000000000000001','1e858f11beda11e79bb968f728c50003',null);
Insert into PPM_SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ROLE0000000000000000000000000001','1e858f11beda11e79bb968f728c52009',null);
Insert into PPM_SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ROLE0000000000000000000000000001','1e858f11beda11e79bb968f728c52010',null);
Insert into PPM_SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ROLE0000000000000000000000000001','1e858f11beda11e79bb968f728c50004',null);
Insert into PPM_SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ROLE0000000000000000000000000001','1e858f11beda11e79bb968f728c52011',null);
Insert into PPM_SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ROLE0000000000000000000000000001','1e858f11beda11e79bb968f728c50006',null);
Insert into PPM_SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ROLE0000000000000000000000000001','21e858f11beda11e79bb968f728c5013',null);
Insert into PPM_SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ROLE0000000000000000000000000001','1e858f11beda11e79bb968f728c52014',null);
Insert into PPM_SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ROLE0000000000000000000000000001','1e858f11beda11e79bb968f728c50005',null);
Insert into PPM_SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ROLE0000000000000000000000000001','1e858f11beda11e79bb968f728c52006','2006001,2006005,2006006,2006002,2006007,2006008,2006003,2006009,2006010,2008001,2008002,2008003');
Insert into PPM_SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ROLE0000000000000000000000000001','1e858f11beda11e79bb968f728c52007',null);
Insert into PPM_SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ROLE0000000000000000000000000001','1e858f11beda11e79bb968f728c52008',null);
Insert into PPM_SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ROLE0000000000000000000000000001','1e858f11beda11e79bb968f728c52012',null);
Insert into PPM_SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ROLE0000000000000000000000000002','1e858f11beda11e79bb968f728c50001',null);
Insert into PPM_SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ROLE0000000000000000000000000002','1e858f11beda11e79bb968f728c50002',null);
Insert into PPM_SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ROLE0000000000000000000000000002','1e858f11beda11e79bb968f728c52001','2001001,2001002,2001003,2001004,2001005');
Insert into PPM_SYS_ROLE_MENU (ROLE_ID,MENU_ID,OPERATION) values ('ROLE0000000000000000000000000002','1e858f11beda11e79bb968f728c52002',null);

Insert into PPM_SYS_USER (ID,UTILITY_ID,ORG_ID,ROLE_ID,NAME,USERNAME,PASSWORD,EMAIL,MOBILE_PHONE,PROFILE_FILE,USER_TYPE,SIGNATURE,USERKEY,DELETE_FLAG,USER_STATE,LAST_LOGIN_TIME,STATION_ID) values ('1','1','1','ROLE0000000000000000000000000001','Admin','admin','9e5f9180802d96a05291d731fb8b3b72','<EMAIL>','222',null,1,HexToRaw('1D86F16D242E6663'),null,0,1,null,'VEND0000000000000000000000000001');
Insert into PPM_VENDING_STATION (ID,ORG_ID,STATION_CODE,STATION_NAME,STATION_TYPE,STATION_ADDRESS,LONGITUGE,LATITUDE,TELEPHONE_NO,STATION_STATUS,COMMISSION,TAX,MAX_LIMT_AMOUNT,AVAILABLE_ELECTRICITY,VERSION_OPTIMISTIC_LOCKING) values ('VEND0000000000000000000000000001','1','000000','SystemVend-001',1,null,'1','1','073029299999',1,0.8,0.6,1111,1,'754854fcaeb447f0a0ccb5f64d43b4b9');