package com.clou.esp.hes.app.web.model.data;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.commons.lang.StringUtils;

import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.model.common.BaseEntity;

/**
 * 
 * <AUTHOR>
 *
 */
public class DataCalcObj extends BaseEntity {

	private static final long serialVersionUID = -6287550892299483720L;

	public DataCalcObj(){}
	
	private Date tv;
	private String calcObjId;
	private String calcName;
	private String outTotal;
	private String inTotal;
	private String calcValue;
	private String missData;
	
	
	
	public String getTvStr(String pattern) {
		if(tv!=null) {
			if(StringUtils.isEmpty(pattern)) {
				pattern=ResourceUtil.getSessionattachmenttitle("local.date.formatter");
			}
			SimpleDateFormat sdf = new SimpleDateFormat(pattern);
			return sdf.format(tv);
		}
		return "";
	};
	


	public Date getTv() {
		return tv;
	}
	public void setTv(Date tv) {
		this.tv = tv;
	}
	public String getCalcObjId() {
		return calcObjId;
	}
	public void setCalcObjId(String calcObjId) {
		this.calcObjId = calcObjId;
	}
	public String getCalcName() {
		return calcName;
	}
	public void setCalcName(String calcName) {
		this.calcName = calcName;
	}
	public String getOutTotal() {
		return outTotal;
	}
	public void setOutTotal(String outTotal) {
		this.outTotal = outTotal;
	}
	public String getInTotal() {
		return inTotal;
	}
	public void setInTotal(String inTotal) {
		this.inTotal = inTotal;
	}
	public String getCalcValue() {
		return calcValue;
	}
	public void setCalcValue(String calcValue) {
		this.calcValue = calcValue;
	}
	public String getMissData() {
		return missData;
	}
	public void setMissData(String missData) {
		this.missData = missData;
	}
	
	
	
}
