/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class MdmReportFolder{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2020-06-23 04:02:24
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.report;

import com.clou.esp.hes.app.web.dao.report.MdmReportFolderDao;
import com.clou.esp.hes.app.web.model.report.MdmReportFolder;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.clou.esp.hes.app.web.service.report.MdmReportFolderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Component
@Service("mdmReportFolderService")
public class MdmReportFolderServiceImpl  extends CommonServiceImpl<MdmReportFolder> implements MdmReportFolderService {

	@Resource
	private MdmReportFolderDao mdmReportFolderDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(mdmReportFolderDao);
    }
	@SuppressWarnings("rawtypes")
	public MdmReportFolderServiceImpl() {}
	
	
}