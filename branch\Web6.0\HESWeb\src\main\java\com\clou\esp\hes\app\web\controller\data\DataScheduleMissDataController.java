/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataScheduleMissData{ } 
 * 
 * 摘    要： dataScheduleMissData
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-12-01 06:03:45
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.data;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.CreatePdf;
import com.clou.esp.hes.app.web.core.util.DateTimeFormatterUtil;
import com.clou.esp.hes.app.web.core.util.ExcelUtils;
import com.clou.esp.hes.app.web.core.util.OrganizationUtils;
import com.clou.esp.hes.app.web.core.util.ProgressMissDataUtil;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.excel.Excel;
import com.power7000g.core.excel.ExcelDataFormatter;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.report.MeterDataReport;
import com.clou.esp.hes.app.web.model.report.MissDataReport;
import com.clou.esp.hes.app.web.model.report.MissDataReport1;
import com.clou.esp.hes.app.web.model.report.ProgressDelayDataReport;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.model.asset.AssetCommunicator;
import com.clou.esp.hes.app.web.model.asset.AssetMeasurementProfile;
import com.clou.esp.hes.app.web.model.asset.AssetMeasurementProfileDi;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroupMap;
import com.clou.esp.hes.app.web.model.data.DataScheduleMissData;
import com.clou.esp.hes.app.web.model.data.DataScheduleProgress;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;
import com.clou.esp.hes.app.web.model.dict.DictMeterDataStorageInfo;
import com.clou.esp.hes.app.web.model.dict.DictMeterDataStorageTable;
import com.clou.esp.hes.app.web.model.dict.DictProfile;
import com.clou.esp.hes.app.web.service.asset.AssetCommunicatorService;
import com.clou.esp.hes.app.web.service.asset.AssetMeasurementProfileDiService;
import com.clou.esp.hes.app.web.service.asset.AssetMeasurementProfileService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterGroupMapService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.data.DataScheduleMissDataService;
import com.clou.esp.hes.app.web.service.data.DataScheduleProgressService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemService;
import com.clou.esp.hes.app.web.service.dict.DictMeterDataStorageInfoService;
import com.clou.esp.hes.app.web.service.dict.DictMeterDataStorageTableService;
import com.clou.esp.hes.app.web.service.dict.DictProfileService;
import com.clou.esp.hes.app.web.service.system.SysOrgService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

/**
 * <AUTHOR>
 * @时间：2017-12-01 06:03:45
 * @描述：dataScheduleMissData类
 */
@Controller
@RequestMapping("/dataScheduleMissDataController")
public class DataScheduleMissDataController extends BaseController{

 	@Resource
    private DataScheduleMissDataService dataScheduleMissDataService;
 	
 	@Resource
    private DataScheduleProgressService dataScheduleProgressService;
	@Resource
    private AssetCommunicatorService assetCommunicatorService;
	@Resource
    private AssetMeterService assetMeterService;
	@Resource
    private SysOrgService sysOrgService;
	@Resource
    private DictProfileService dictProfileService;
	@Resource
    private AssetMeterGroupMapService assetMeterGroupMapService;
	@Resource
    private AssetMeasurementProfileService assetMeasurementProfileService;
	@Resource
    private AssetMeasurementProfileDiService assetMeasurementProfileDiService;
	@Resource
    private DictDataitemService dictDataitemService;
	@Resource
    private DictMeterDataStorageInfoService dictMeterDataStorageInfoService;
	@Resource
    private DictMeterDataStorageTableService dictMeterDataStorageTableService;
	
	
	/**
	 * 跳转到dataScheduleMissData列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/data/dataScheduleMissDataList");
    }

	/**
	 * 跳转到dataScheduleMissData新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dataScheduleMissData")
	public ModelAndView dataScheduleMissData(DataScheduleMissData dataScheduleMissData,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dataScheduleMissData.getId())){
			try {
                dataScheduleMissData=dataScheduleMissDataService.getEntity(dataScheduleMissData.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dataScheduleMissData", dataScheduleMissData);
		}
		return new ModelAndView("/data/dataScheduleMissData");
	}


	/**
	 * dataScheduleProgress查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
        	 jqGridSearchTo.getMap().put("tv_start", DateUtils.date2Str(
						DateUtils.parseDate(jqGridSearchTo.getMap().get("tv_start").toString(), ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
						new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")));
        	 jqGridSearchTo.getMap().put("tv_end", DateUtils.date2Str(
						DateUtils.parseDate(jqGridSearchTo.getMap().get("tv_end").toString(), ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
						new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")));
        	 
        	 if(StringUtils.isEmpty(jqGridSearchTo.getMap().get("missSerizlNo"))){
        		 jqGridSearchTo.getMap().put("missSearchType", "");
        	 }
        	 
        	 List<AssetMeter> assetMeterLists = null;
        	 List<String> meterSns = null;
        	 if(!StringUtils.isEmpty(jqGridSearchTo.getMap().get("missSearchType"))
        			 && "Meter".equals(jqGridSearchTo.getMap().get("missSearchType"))
        			 && !StringUtils.isEmpty(jqGridSearchTo.getMap().get("missSerizlNo"))){
        		
        		 
        			AssetMeter entity = new AssetMeter();
     				entity.setSn(jqGridSearchTo.getMap().get("missSerizlNo").toString());
     				AssetMeter entity1 = assetMeterService.get(entity);
        		 if(entity1 != null){
        			 meterSns = new ArrayList();
        			 meterSns.add(entity1.getId());
        		 }
        		 jqGridSearchTo.getMap().put("snLists", meterSns);
        	 }else if(!StringUtils.isEmpty(jqGridSearchTo.getMap().get("missSearchType"))
        			 && "Commnuicator".equals(jqGridSearchTo.getMap().get("missSearchType"))
        			 && !StringUtils.isEmpty(jqGridSearchTo.getMap().get("missSerizlNo"))){
        		 
        	
     			AssetCommunicator commTemp = new AssetCommunicator();
     			commTemp.setSn(jqGridSearchTo.getMap().get("missSerizlNo").toString());
     			AssetCommunicator newComm = assetCommunicatorService.get(commTemp);
     			if(StringUtil.isNotEmpty(newComm)){
     				AssetMeter entity = new AssetMeter();
     				entity.setCommunicatorId(newComm.getId());
     				assetMeterLists = assetMeterService.getList(entity);
     			}
     		
     			if(assetMeterLists != null && assetMeterLists.size() > 0 ){
     				for(AssetMeter assetMeter : assetMeterLists){
     					 meterSns = new ArrayList();
     					meterSns.add(assetMeter.getId());
     				}
     				jqGridSearchTo.getMap().put("snLists", meterSns);
     			}else{
     				
     				jqGridSearchTo.getMap().put("snLists", meterSns);
     			}
        	 }
        	 
        	SysUser su = TokenManager.getToken();
 			List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
 			jqGridSearchTo.getMap().put("orgIdList", orgIdList);
        	 
            j=dataScheduleMissDataService.getForJqGrid(jqGridSearchTo);
            
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
	
	/**
	 * dataScheduleProgress查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid1")
    @ResponseBody
    public JqGridResponseTo datagrid1(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
        	 jqGridSearchTo.getMap().put("tv_start", DateUtils.date2Str(
						DateUtils.parseDate(jqGridSearchTo.getMap().get("tv_start").toString(), ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
						new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")));
        	 jqGridSearchTo.getMap().put("tv_end", DateUtils.date2Str(
						DateUtils.parseDate(jqGridSearchTo.getMap().get("tv_end").toString(), ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
						new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")));
        	 
        	 if(StringUtils.isEmpty(jqGridSearchTo.getMap().get("missSerizlNo"))){
        		 jqGridSearchTo.getMap().put("missSearchType", "");
        	 }
        	 
        	 List<AssetMeter> assetMeterLists = null;
        	 List<String> meterSns = null;
        	 if(!StringUtils.isEmpty(jqGridSearchTo.getMap().get("missSearchType"))
        			 && "Meter".equals(jqGridSearchTo.getMap().get("missSearchType"))
        			 && !StringUtils.isEmpty(jqGridSearchTo.getMap().get("missSerizlNo"))){
        		
        		 
        			AssetMeter entity = new AssetMeter();
     				entity.setSn(jqGridSearchTo.getMap().get("missSerizlNo").toString());
     				AssetMeter entity1 = assetMeterService.get(entity);
        		 if(entity1 != null){
        			 meterSns = new ArrayList();
        			 meterSns.add(entity1.getId());
        		 }
        		 jqGridSearchTo.getMap().put("snLists", meterSns);
        	 }else if(!StringUtils.isEmpty(jqGridSearchTo.getMap().get("missSearchType"))
        			 && "Commnuicator".equals(jqGridSearchTo.getMap().get("missSearchType"))
        			 && !StringUtils.isEmpty(jqGridSearchTo.getMap().get("missSerizlNo"))){
        		 
        	
     			AssetCommunicator commTemp = new AssetCommunicator();
     			commTemp.setSn(jqGridSearchTo.getMap().get("missSerizlNo").toString());
     			AssetCommunicator newComm = assetCommunicatorService.get(commTemp);
     			if(StringUtil.isNotEmpty(newComm)){
     				AssetMeter entity = new AssetMeter();
     				entity.setCommunicatorId(newComm.getId());
     				assetMeterLists = assetMeterService.getList(entity);
     			}
     		
     			if(assetMeterLists != null && assetMeterLists.size() > 0 ){
     				for(AssetMeter assetMeter : assetMeterLists){
     					 meterSns = new ArrayList();
     					meterSns.add(assetMeter.getId());
     				}
     				jqGridSearchTo.getMap().put("snLists", meterSns);
     			}else{
     				
     				jqGridSearchTo.getMap().put("snLists", meterSns);
     			}
        	 }
        	 
        	SysUser su = TokenManager.getToken();
 			List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
 			jqGridSearchTo.getMap().put("orgIdList", orgIdList);
        	 
            j=dataScheduleMissDataService.getForJqGrid(jqGridSearchTo);
            
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
       
    /**
	 * 导出
	 * 
	 * @param delay data
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "getExcelMissData")
	@ResponseBody
	public void getExcelMissData(String startTime,String endTime, String profileId,
			String searchType,String serizlNo,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			Map<String, Object> extData = new HashMap<String, Object>();
			extData.put("startTime",  DateUtils.date2Str(
					DateUtils.parseDate(startTime, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
					new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")));
			extData.put("endTime",  DateUtils.date2Str(
					DateUtils.parseDate(endTime, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
					new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")));
			extData.put("profileId" , profileId);
			extData.put("searchType" , searchType);
			extData.put("serizlNo" , serizlNo);
            // deviceIds 获取
			 List<AssetMeter> assetMeterLists = null;
        	 List<String> meterSns = null;
        	 if(!StringUtils.isEmpty(searchType)
        			 && "Meter".equals(searchType)
        			 && !StringUtils.isEmpty(serizlNo)){
        		
        		 
        			AssetMeter entity = new AssetMeter();
     				entity.setSn(serizlNo);
     				AssetMeter entity1 = assetMeterService.get(entity);
        		 if(entity1 != null){
        			 meterSns = new ArrayList();
        			 meterSns.add(entity1.getId());
        		 }
        		 extData.put("snLists", meterSns);
        	 }else if(!StringUtils.isEmpty(searchType)
        			 && "Commnuicator".equals(searchType)
        			 && !StringUtils.isEmpty(serizlNo)){
        		 
        	
     			AssetCommunicator commTemp = new AssetCommunicator();
     			commTemp.setSn(serizlNo);
     			AssetCommunicator newComm = assetCommunicatorService.get(commTemp);
     			if(StringUtil.isNotEmpty(newComm)){
     				AssetMeter entity = new AssetMeter();
     				entity.setCommunicatorId(newComm.getId());
     				assetMeterLists = assetMeterService.getList(entity);
     			}
     		
     			if(assetMeterLists != null && assetMeterLists.size() > 0 ){
     				for(AssetMeter assetMeter : assetMeterLists){
     					 meterSns = new ArrayList();
     					meterSns.add(assetMeter.getId());
     				}
     				 extData.put("snLists", meterSns);
     			}else{
     				
     				 extData.put("snLists", meterSns);
     			}
        	 }
        	 
        	SysUser su = TokenManager.getToken();
 			List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
 			extData.put("orgIdList", orgIdList);
 			DataScheduleMissData dataScheduleMissData = new DataScheduleMissData();
 			dataScheduleMissData.setExtData(extData);
 			List<DataScheduleMissData> list = dataScheduleMissDataService.getList(dataScheduleMissData);
			
 		
			if (list.size() <= 0) {
				DataScheduleMissData d = new DataScheduleMissData();
				list.add(d);
			}
			ExcelDataFormatter edf = new ExcelDataFormatter();
			
			Map<String, String> progress = new HashMap<String, String>();
			progress.put("progress", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
			edf.set("progress", progress);
			
			DictProfile dictProfile = new DictProfile();
			dictProfile.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
			List<DictProfile> dmfList = dictProfileService.getList(dictProfile);

			Map<String, String> dmf = new HashMap<String, String>();
			for (DictProfile d : dmfList) {
				dmf.put(d.getId(), d.getName());
			}
			edf.set("missProfileId", dmf);
		
			ExcelUtils.writeToFile(list, edf, "missData.xlsx", response,
					ValidGroup1.class);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	/**
	 * 打印
	 * 
	 * @param testUser
	 * @param response
	 */
	@RequestMapping(value = "printMissData")
	@ResponseBody
	public void printMissData(String startTime,String endTime, String profileId,
			String searchType,String serizlNo,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			Map<String, Object> extData = new HashMap<String, Object>();
			extData.put("startTime",  DateUtils.date2Str(
					DateUtils.parseDate(startTime, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
					new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")));
			extData.put("endTime",  DateUtils.date2Str(
					DateUtils.parseDate(endTime, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
					new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")));
			extData.put("profileId" , profileId);
			extData.put("searchType" , searchType);
			extData.put("serizlNo" , serizlNo);
            // deviceIds 获取
			 List<AssetMeter> assetMeterLists = null;
        	 List<String> meterSns = null;
        	 if(!StringUtils.isEmpty(searchType)
        			 && "Meter".equals(searchType)
        			 && !StringUtils.isEmpty(serizlNo)){
        		
        		 
        			AssetMeter entity = new AssetMeter();
     				entity.setSn(serizlNo);
     				AssetMeter entity1 = assetMeterService.get(entity);
        		 if(entity1 != null){
        			 meterSns = new ArrayList();
        			 meterSns.add(entity1.getId());
        		 }
        		 extData.put("snLists", meterSns);
        	 }else if(!StringUtils.isEmpty(searchType)
        			 && "Commnuicator".equals(searchType)
        			 && !StringUtils.isEmpty(serizlNo)){
        		 
        	
     			AssetCommunicator commTemp = new AssetCommunicator();
     			commTemp.setSn(serizlNo);
     			AssetCommunicator newComm = assetCommunicatorService.get(commTemp);
     			if(StringUtil.isNotEmpty(newComm)){
     				AssetMeter entity = new AssetMeter();
     				entity.setCommunicatorId(newComm.getId());
     				assetMeterLists = assetMeterService.getList(entity);
     			}
     		
     			if(assetMeterLists != null && assetMeterLists.size() > 0 ){
     				for(AssetMeter assetMeter : assetMeterLists){
     					meterSns = new ArrayList();
     					meterSns.add(assetMeter.getId());
     				}
     				 extData.put("snLists", meterSns);
     			}else{	
     				 extData.put("snLists", meterSns);
     			}
        	 }
        	 
        	SysUser su = TokenManager.getToken();
 			List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
 			extData.put("orgIdList", orgIdList);
 			DataScheduleMissData dataScheduleMissData = new DataScheduleMissData();
 			dataScheduleMissData.setExtData(extData);
 			List<DataScheduleMissData> list = dataScheduleMissDataService.getList(dataScheduleMissData);

			if (list.size() <= 0) {
				DataScheduleMissData d = new DataScheduleMissData();
				list.add(d);
			}
			ExcelDataFormatter edf = new ExcelDataFormatter();
			
			Map<String, String> progress = new HashMap<String, String>();
			progress.put("progress", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
			edf.set("progress", progress);
			
			DictProfile dictProfile = new DictProfile();
			dictProfile.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
			List<DictProfile> dmfList = dictProfileService.getList(dictProfile);

			Map<String, String> dmf = new HashMap<String, String>();
			for (DictProfile d : dmfList) {
				dmf.put(d.getId(), d.getName());
			}
			edf.set("profileId", dmf);
			// list生成pdf打印；
			CreatePdf.printPdf(list, edf, ValidGroup1.class, request, response);

		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	
	
	  /**
		 * 导出
		 * 
		 * @param miss data
		 * @param request
		 * @param response
		 */
		@RequestMapping(value = "getExcelMissDataDetail")
		@ResponseBody
		public void getExcelMissDataDetail(String meterSn , String profileId 
				, String startTime, String endTime ,HttpServletRequest request, HttpServletResponse response) {
			try {

				//Meter  //Commnuicator
				AssetMeter assetMeter = new AssetMeter();
	         	assetMeter.setSn(meterSn);
	         	assetMeter = assetMeterService.get(assetMeter);
	         	String meterId=assetMeter.getId();
				
	        	AssetMeterGroupMap group = new AssetMeterGroupMap();
	    		group.setId(meterId);
	    		group.setType("1");
	    		group=	assetMeterGroupMapService.get(group);
	
	    		AssetMeasurementProfileDi assetMeasurementProfileDi = new AssetMeasurementProfileDi();
				assetMeasurementProfileDi.setMgId(group.getGroupId());
				assetMeasurementProfileDi.setProfileId(profileId);
				List<AssetMeasurementProfileDi> dis = assetMeasurementProfileDiService
						.getList(assetMeasurementProfileDi);

				List<String> ids = new ArrayList();

				for (AssetMeasurementProfileDi assetMeasurementProfileDi1 : dis) {
					ids.add(assetMeasurementProfileDi1.getDataitemId());
				}

				List<DictDataitem> dictDataItems = new ArrayList();
				if (ids != null && ids.size() > 0) {
					dictDataItems = dictDataitemService.getListByIds(ids);
				}
	
				Map<String,Excel> excels=new HashMap<String, Excel>();
		        Excel times=ExcelUtils.createExcel("Time", 22);
		        excels.put("times", times);
		        Excel serizlName=ExcelUtils.createExcel("Serial Number", 15);
		        excels.put("serizlName", serizlName);
		        for(int i=0;i<dictDataItems.size();i++){
			        	Excel value = ExcelUtils.createExcel(dictDataItems.get(i).getName(), dictDataItems.get(i).getName().length());
			        	excels.put("miss_value"+(i+1), value);    
		        }
		    
			   	String starTimeStr =  DateUtils.date2Str(
							DateUtils.parseDate(startTime, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
							new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
				String endTimeStr =  DateUtils.date2Str(
							DateUtils.parseDate(endTime, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
							new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
	     	 
				
	         	String tableName="";
	         	Map<String, Object> extData = new HashMap<String, Object>();
	         	
	            List<String> tIds= new ArrayList();
	            for(AssetMeasurementProfileDi di : dis){
	            	tIds.add(di.getDataitemId());
	            }
	            if(tIds.size()>0){
	            	for(String tId : tIds){
	            		DictMeterDataStorageInfo dictMeterDataStorageInfo =dictMeterDataStorageInfoService.getEntity(tId);
	                	if(dictMeterDataStorageInfo==null){
	                		continue;
	                	}
	                	DictMeterDataStorageTable dictMeterDataStorageTable=dictMeterDataStorageTableService.getEntity(dictMeterDataStorageInfo.getTableId());
	                	if(dictMeterDataStorageTable==null){
	                		continue;
	                	}
	                	tableName=dictMeterDataStorageTable.getName();
	                	break;
	            	}
	            	
	            }
	           
	            Map<String,Object> dsm=new HashMap<String, Object>();
	            dsm.put("ids", tIds);
	            DictMeterDataStorageInfo dmd=new DictMeterDataStorageInfo();
	            dmd.setExtData(dsm);
	            List<DictMeterDataStorageInfo> dmdList= dictMeterDataStorageInfoService.getList(dmd);
	            String viewField="";
	            String viewFieldMysql="";
	            for(int i=0;i<dmdList.size();i++){
	            	if(dmdList.get(i).getShowUnit().doubleValue()==1){
	            		viewField+=",rtrim(to_char(a.VALUE"+dmdList.get(i).getFieldIndex()+",'FM999999990.9999'),'.') as miss_value"+(i+1);
	            		viewFieldMysql+=",cast(a.VALUE"+dmdList.get(i).getFieldIndex()+" as decimal(10,3)) as miss_value"+(i+1);
	            	}else{
	            		viewField+=",rtrim(to_char(a.VALUE"+dmdList.get(i).getFieldIndex()+"*"+1d+",'FM999999990.9999'),'.') as miss_value"+(i+1);
	            		viewFieldMysql+=",cast(a.VALUE"+dmdList.get(i).getFieldIndex()+"*"+1d+" as decimal(10,3)) as miss_value"+(i+1);
	            	}
	            }
            	extData.put("deviceId", meterId);
	            extData.put("tableName", tableName.toUpperCase()); 
	            extData.put("viewField", viewField);
	            extData.put("viewFieldMysql", viewFieldMysql);
           
            	extData.put("sidx", "times");
            	extData.put("sort", "asc");

    			extData.put("serizlName", meterSn);

       
                List<Date> timeList = Lists.newArrayList();
        
                //获取缺数数据
                 
                DataScheduleMissData dataScheduleMissData  = new DataScheduleMissData();
                Map<String, Object> extData1 = new HashMap<String, Object>();
                extData1.put("profileId", profileId);
                extData1.put("deviceId", meterId);
                extData1.put("startTime", starTimeStr);
                extData1.put("endTime", endTimeStr);
                dataScheduleMissData.setExtData(extData1);
                
                //缺点的数量确立
                List<DataScheduleMissData> dataScheduleMissDataList = 
                		dataScheduleMissDataService.getListMissDataTv(dataScheduleMissData);
              
                
                for(DataScheduleMissData md : dataScheduleMissDataList){
                	timeList.add(md.getMissDataTv());
                }
                
                MissDataReport missDataReportCalc  = new MissDataReport();
                extData.put("timeList", timeList);
                missDataReportCalc.setExtData(extData);
              
            	List<MissDataReport> tmpList1 = dictMeterDataStorageTableService.getMissDataList(missDataReportCalc);

        		Map<Date,MissDataReport> map = Maps.newHashMap();
        
        		if(tmpList1!=null) {
        			for(MissDataReport report:tmpList1) {
        				map.put(report.getTimes(), report);
        			}
        		}
  
        		List<MissDataReport>	tmplist=Lists.newArrayList();
        		for(Date date:timeList) {
        			MissDataReport report =map.get(date);	
        			
        			if(report==null) {    
        				
        				report = new MissDataReport(meterSn,date);
        			}
        			tmplist.add(report);
        		}

			
	     		ExcelDataFormatter edf = new ExcelDataFormatter();
	  	        Map<String,String> tvs=new HashMap<String, String>();
	  	        tvs.put("times",  ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
	  	        edf.set("times", tvs);
	  	        
	  	     	if(tmplist.size()<=0){
	  	    	  MissDataReport mdr=new MissDataReport();
	  	    	  tmplist.add(mdr);
		        }
	  	        
	  	        ExcelUtils.writeToFile(tmplist, edf,"excelMissDataDetailList.xlsx",response, excels);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		 /**
		 * 导出
		 * 
		 * @param miss data
		 * @param request
		 * @param response
		 */
		@RequestMapping(value = "getExcelMissDataDetail1")
		@ResponseBody
		public void getExcelMissDataDetail1(String meterSn , String profileId 
				, String startTime, String endTime ,HttpServletRequest request, HttpServletResponse response) {
			try {
				//Meter  //Commnuicator
				AssetMeter assetMeter = new AssetMeter();
				assetMeter.setSn(meterSn);
				assetMeter = assetMeterService.get(assetMeter);
				String meterId=assetMeter.getId();

				AssetMeterGroupMap group = new AssetMeterGroupMap();
				group.setId(meterId);
				group.setType("1");
				group=	assetMeterGroupMapService.get(group);

				AssetMeasurementProfileDi assetMeasurementProfileDi = new AssetMeasurementProfileDi();
				assetMeasurementProfileDi.setMgId(group.getGroupId());
				assetMeasurementProfileDi.setProfileId(profileId);
				List<AssetMeasurementProfileDi> dis = assetMeasurementProfileDiService
						.getList(assetMeasurementProfileDi);

				List<String> ids = new ArrayList();

				for (AssetMeasurementProfileDi assetMeasurementProfileDi1 : dis) {
					ids.add(assetMeasurementProfileDi1.getDataitemId());
				}

				List<DictDataitem> dictDataItems = new ArrayList();
				if (ids != null && ids.size() > 0) {
					dictDataItems = dictDataitemService.getListByIds(ids);
				}

				Map<String,Excel> excels=new HashMap<String, Excel>();
				Excel times=ExcelUtils.createExcel("Time", 22);
				excels.put("times", times);
				Excel taskTv=ExcelUtils.createExcel("task Time", 22);
				excels.put("taskTv", taskTv);
				Excel taskState=ExcelUtils.createExcel("task Result", 22);
				excels.put("taskState", taskState);
				Excel failedInfo=ExcelUtils.createExcel("failure Cause", 22);
				excels.put("failedInfo", failedInfo);
				Excel comStatusOther=ExcelUtils.createExcel("comStatus", 22);
				excels.put("comStatusOther", comStatusOther);

				Excel serizlName=ExcelUtils.createExcel("Serial Number", 15);
				excels.put("serizlName", serizlName);
				Excel updateTvStr=ExcelUtils.createExcel("status update Time", 22);
				excels.put("updateTvStr", updateTvStr);

				for(int i=0;i<dictDataItems.size();i++){
					Excel value = ExcelUtils.createExcel(dictDataItems.get(i).getName(), dictDataItems.get(i).getName().length());
					excels.put("miss_value"+(i+1), value);
				}

				String starTimeStr =  DateUtils.date2Str(
						DateUtils.parseDate(startTime, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
						new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
				String endTimeStr =  DateUtils.date2Str(
						DateUtils.parseDate(endTime, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
						new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));


				String tableName="";
				Map<String, Object> extData = new HashMap<String, Object>();

				List<String> tIds= new ArrayList();
				for(AssetMeasurementProfileDi di : dis){
					tIds.add(di.getDataitemId());
				}
				if(tIds.size()>0){
					for(String tId : tIds){
						DictMeterDataStorageInfo dictMeterDataStorageInfo =dictMeterDataStorageInfoService.getEntity(tId);
						if(dictMeterDataStorageInfo==null){
							continue;
						}
						DictMeterDataStorageTable dictMeterDataStorageTable=dictMeterDataStorageTableService.getEntity(dictMeterDataStorageInfo.getTableId());
						if(dictMeterDataStorageTable==null){
							continue;
						}
						tableName=dictMeterDataStorageTable.getName();
						break;
					}

				}

				Map<String,Object> dsm=new HashMap<String, Object>();
				dsm.put("ids", tIds);
				DictMeterDataStorageInfo dmd=new DictMeterDataStorageInfo();
				dmd.setExtData(dsm);
				List<DictMeterDataStorageInfo> dmdList= dictMeterDataStorageInfoService.getList(dmd);
				String viewField="";
				String viewFieldMysql="";
				for(int i=0;i<dmdList.size();i++){
					if(dmdList.get(i).getShowUnit().doubleValue()==1){
						viewField+=",rtrim(to_char(a.VALUE"+dmdList.get(i).getFieldIndex()+",'FM999999990.9999'),'.') as miss_value"+(i+1);
						viewFieldMysql+=",cast(a.VALUE"+dmdList.get(i).getFieldIndex()+" as decimal(10,3)) as miss_value"+(i+1);
					}else{
						viewField+=",rtrim(to_char(a.VALUE"+dmdList.get(i).getFieldIndex()+"*"+1d+",'FM999999990.9999'),'.') as miss_value"+(i+1);
						viewFieldMysql+=",cast(a.VALUE"+dmdList.get(i).getFieldIndex()+"*"+1d+" as decimal(10,3)) as miss_value"+(i+1);
					}
				}
				extData.put("deviceId", meterId);
				extData.put("tableName", tableName.toUpperCase());
				extData.put("viewField", viewField);
				extData.put("viewFieldMysql", viewFieldMysql);

				extData.put("sidx", "times");
				extData.put("sort", "asc");

				extData.put("serizlName", meterSn);

				List<Date> timeList = Lists.newArrayList();

				//获取缺数数据

				DataScheduleMissData dataScheduleMissData  = new DataScheduleMissData();
				Map<String, Object> extData1 = new HashMap<String, Object>();
				extData1.put("profileId", profileId);
				extData1.put("deviceId", meterId);
				extData1.put("startTime", starTimeStr);
				extData1.put("endTime", endTimeStr);
				dataScheduleMissData.setExtData(extData1);

				//缺点的数量确立
				List<DataScheduleMissData> dataScheduleMissDataList =
						dataScheduleMissDataService.getListMissDataTv(dataScheduleMissData);

				for(DataScheduleMissData md : dataScheduleMissDataList){
					timeList.add(md.getMissDataTv());
				}

				MissDataReport missDataReportCalc  = new MissDataReport();
				extData.put("timeList", timeList);
				missDataReportCalc.setExtData(extData);

				List<MissDataReport1> tmpList1 = dictMeterDataStorageTableService.getMissDataList1(missDataReportCalc);
				DataScheduleMissData dataScheduleMissDataEntity = new DataScheduleMissData();
				dataScheduleMissDataEntity.setExtData(extData);
				List<DataScheduleMissData> lackDataList = dataScheduleMissDataService.getMissDataList(dataScheduleMissDataEntity);

				Map<Date,MissDataReport1> map = Maps.newHashMap();
				Map<Date,DataScheduleMissData> missDataMap = Maps.newHashMap();
				if(tmpList1 != null) {
					for(MissDataReport1 report : tmpList1) {
						map.put(report.getTimes(), report);
					}
				}
				if(lackDataList!=null) {
					for(DataScheduleMissData missData:lackDataList) {
						missDataMap.put(missData.getTv(), missData);
					}
				}

				List<MissDataReport1> tmplist=Lists.newArrayList();
				for(Date date:timeList) {
					MissDataReport1 report =map.get(date);
					DataScheduleMissData missData = missDataMap.get(date);
					if(report==null) {
						if(missData == null){
							report = new MissDataReport1(meterId,meterSn,date);
						}else{
							BigDecimal b2 = BigDecimal.ONE;
							String st = missData.getComStatus().compareTo(b2)==0?"online":"offline";
							String task = missData.getTaskState().equals("1")?"Success":"Failed";
							report = new MissDataReport1(meterId,meterSn,date,missData.getTaskTv(),task,missData.getFailedInfo(),missData.getUpdateTv(),st);
						}

					}else{
						if(missData != null){
							report.setMeterId(meterId);
							report.setTaskTv(missData.getTaskTv());

							if(missData.getTaskTv() != null){
								report.setTaskTvStr(DateUtils.date2Str(
										missData.getTaskTv(),
										new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"))));
							}
							report.setTimeStr(DateUtils.date2Str(missData.getTv(),new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"))));
							report.setTaskState(missData.getTaskState());
							report.setFailedInfo(missData.getFailedInfo());
							report.setUpdateTv(missData.getUpdateTv());
							report.setComStatus(missData.getComStatus());
						}

					}

					tmplist.add(report);
				}
				//这


				ExcelDataFormatter edf = new ExcelDataFormatter();
				Map<String,String> tvs=new HashMap<String, String>();
				tvs.put("times",  ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
				edf.set("times", tvs);

				if(tmplist.size()<=0){
					MissDataReport1 mdr=new MissDataReport1();
					tmplist.add(mdr);
				}

	  	        ExcelUtils.writeToFile(tmplist, edf,"excelMissDataDetailList.xlsx",response, excels);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		/**
		 * 打印
		 * 
		 * @param testUser
		 * @param response
		 */
		@RequestMapping(value = "printMissDataDetail")
		@ResponseBody
		public void printMissDataDetail(String meterSn , String profileId 
				, String startTime, String endTime ,HttpServletRequest request, HttpServletResponse response) {
			try {
				//Meter  //Commnuicator
				AssetMeter assetMeter = new AssetMeter();
	         	assetMeter.setSn(meterSn);
	         	assetMeter = assetMeterService.get(assetMeter);
	         	String meterId=assetMeter.getId();
				
	        	AssetMeterGroupMap group = new AssetMeterGroupMap();
	    		group.setId(meterId);
	    		group.setType("1");
	    		group=	assetMeterGroupMapService.get(group);
	
	    		AssetMeasurementProfileDi assetMeasurementProfileDi = new AssetMeasurementProfileDi();
				assetMeasurementProfileDi.setMgId(group.getGroupId());
				assetMeasurementProfileDi.setProfileId(profileId);
				List<AssetMeasurementProfileDi> dis = assetMeasurementProfileDiService
						.getList(assetMeasurementProfileDi);

				List<String> ids = new ArrayList();

				for (AssetMeasurementProfileDi assetMeasurementProfileDi1 : dis) {
					ids.add(assetMeasurementProfileDi1.getDataitemId());
				}

				List<DictDataitem> dictDataItems = new ArrayList();
				if (ids != null && ids.size() > 0) {
					dictDataItems = dictDataitemService.getListByIds(ids);
				}
	
				Map<String,Excel> excels=new HashMap<String, Excel>();
		        Excel times=ExcelUtils.createExcel("Time", 22);
		        excels.put("times", times);
		        Excel serizlName=ExcelUtils.createExcel("Serial Number", 15);
		        excels.put("serizlName", serizlName);
		        for(int i=0;i<dictDataItems.size();i++){
			        	Excel value = ExcelUtils.createExcel(dictDataItems.get(i).getName(), dictDataItems.get(i).getName().length());
			        	excels.put("miss_value"+(i+1), value);    
		        }
		    
			   	String starTimeStr =  DateUtils.date2Str(
							DateUtils.parseDate(startTime, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
							new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
				String endTimeStr =  DateUtils.date2Str(
							DateUtils.parseDate(endTime, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
							new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
	     	 
				
	         	String tableName="";
	         	Map<String, Object> extData = new HashMap<String, Object>();
	         	
	            List<String> tIds= new ArrayList();
	            for(AssetMeasurementProfileDi di : dis){
	            	tIds.add(di.getDataitemId());
	            }
	            if(tIds.size()>0){
	            	for(String tId : tIds){
	            		DictMeterDataStorageInfo dictMeterDataStorageInfo =dictMeterDataStorageInfoService.getEntity(tId);
	                	if(dictMeterDataStorageInfo==null){
	                		continue;
	                	}
	                	DictMeterDataStorageTable dictMeterDataStorageTable=dictMeterDataStorageTableService.getEntity(dictMeterDataStorageInfo.getTableId());
	                	if(dictMeterDataStorageTable==null){
	                		continue;
	                	}
	                	tableName=dictMeterDataStorageTable.getName();
	                	break;
	            	}
	            	
	            }
	           
	            Map<String,Object> dsm=new HashMap<String, Object>();
	            dsm.put("ids", tIds);
	            DictMeterDataStorageInfo dmd=new DictMeterDataStorageInfo();
	            dmd.setExtData(dsm);
	            List<DictMeterDataStorageInfo> dmdList= dictMeterDataStorageInfoService.getList(dmd);
	            String viewField="";
	            String viewFieldMysql="";
	            for(int i=0;i<dmdList.size();i++){
	            	if(dmdList.get(i).getShowUnit().doubleValue()==1){
	            		viewField+=",rtrim(to_char(a.VALUE"+dmdList.get(i).getFieldIndex()+",'FM999999990.9999'),'.') as miss_value"+(i+1);
	            		viewFieldMysql+=",cast(a.VALUE"+dmdList.get(i).getFieldIndex()+" as decimal(10,3)) as miss_value"+(i+1);
	            	}else{
	            		viewField+=",rtrim(to_char(a.VALUE"+dmdList.get(i).getFieldIndex()+"*"+1d+",'FM999999990.9999'),'.') as miss_value"+(i+1);
	            		viewFieldMysql+=",cast(a.VALUE"+dmdList.get(i).getFieldIndex()+"*"+1d+" as decimal(10,3)) as miss_value"+(i+1);
	            	}
	            }
            	extData.put("deviceId", meterId);
	            extData.put("tableName", tableName.toUpperCase()); 
	            extData.put("viewField", viewField);
	            extData.put("viewFieldMysql", viewFieldMysql);
           
            	extData.put("sidx", "times");
            	extData.put("sort", "asc");

    			extData.put("serizlName", meterSn);

       
                List<Date> timeList = Lists.newArrayList();
        
                //获取缺数数据
                 
                DataScheduleMissData dataScheduleMissData  = new DataScheduleMissData();
                Map<String, Object> extData1 = new HashMap<String, Object>();
                extData1.put("profileId", profileId);
                extData1.put("deviceId", meterId);
                extData1.put("startTime", starTimeStr);
                extData1.put("endTime", endTimeStr);
                dataScheduleMissData.setExtData(extData1);
                
                //缺点的数量确立
                List<DataScheduleMissData> dataScheduleMissDataList = 
                		dataScheduleMissDataService.getListMissDataTv(dataScheduleMissData);
              
                
                for(DataScheduleMissData md : dataScheduleMissDataList){
                	timeList.add(md.getMissDataTv());
                }
                
                MissDataReport missDataReportCalc  = new MissDataReport();
                extData.put("timeList", timeList);
                missDataReportCalc.setExtData(extData);
              
            	List<MissDataReport> tmpList1 = dictMeterDataStorageTableService.getMissDataList(missDataReportCalc);

        		Map<Date,MissDataReport> map = Maps.newHashMap();
        
        		if(tmpList1!=null) {
        			for(MissDataReport report:tmpList1) {
        				map.put(report.getTimes(), report);
        			}
        		}
  
        		List<MissDataReport>	tmplist=Lists.newArrayList();
        		for(Date date:timeList) {
        			MissDataReport report =map.get(date);	
        			
        			if(report==null) {    
        				
        				report = new MissDataReport(meterSn,date);
        			}
        			tmplist.add(report);
        		}

			
	     		ExcelDataFormatter edf = new ExcelDataFormatter();
	  	        Map<String,String> tvs=new HashMap<String, String>();
	  	        tvs.put("times",  ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
	  	        edf.set("times", tvs);
	  	        
	  	     	if(tmplist.size()<=0){
	  	    	  MissDataReport mdr=new MissDataReport();
	  	    	  tmplist.add(mdr);
		        }
	  	        
				// list生成pdf打印；
	  	      	CreatePdf.printPdf(tmplist, edf,excels, request, response);

			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	
		
		/**
		 * 打印
		 * 
		 * @param testUser
		 * @param response
		 */
		@RequestMapping(value = "printMissDataDetail1")
		@ResponseBody
		public void printMissDataDetail1(String meterSn , String profileId 
				, String startTime, String endTime ,HttpServletRequest request, HttpServletResponse response) {
			try {
				//Meter  //Commnuicator
				AssetMeter assetMeter = new AssetMeter();
	         	assetMeter.setSn(meterSn);
	         	assetMeter = assetMeterService.get(assetMeter);
	         	String meterId=assetMeter.getId();
				
	        	AssetMeterGroupMap group = new AssetMeterGroupMap();
	    		group.setId(meterId);
	    		group.setType("1");
	    		group=	assetMeterGroupMapService.get(group);
	
	    		AssetMeasurementProfileDi assetMeasurementProfileDi = new AssetMeasurementProfileDi();
				assetMeasurementProfileDi.setMgId(group.getGroupId());
				assetMeasurementProfileDi.setProfileId(profileId);
				List<AssetMeasurementProfileDi> dis = assetMeasurementProfileDiService
						.getList(assetMeasurementProfileDi);

				List<String> ids = new ArrayList();

				for (AssetMeasurementProfileDi assetMeasurementProfileDi1 : dis) {
					ids.add(assetMeasurementProfileDi1.getDataitemId());
				}

				List<DictDataitem> dictDataItems = new ArrayList();
				if (ids != null && ids.size() > 0) {
					dictDataItems = dictDataitemService.getListByIds(ids);
				}
	
				Map<String,Excel> excels=new HashMap<String, Excel>();
		        Excel times=ExcelUtils.createExcel("Time", 22);
		        excels.put("times", times);
		        Excel taskTv=ExcelUtils.createExcel("task Time", 22);
		        excels.put("taskTv", taskTv);
		        Excel taskState=ExcelUtils.createExcel("task Result", 22);
		        excels.put("taskState", taskState);
		         Excel failedInfo=ExcelUtils.createExcel("failure Cause", 22);
		        excels.put("failedInfo", failedInfo);
				Excel comStatusOther=ExcelUtils.createExcel("comStatus", 22);
				excels.put("comStatusOther", comStatusOther);

				Excel serizlName=ExcelUtils.createExcel("Serial Number", 15);
				excels.put("serizlName", serizlName);
		        Excel updateTvStr=ExcelUtils.createExcel("status update Time", 22);
		        excels.put("updateTvStr", updateTvStr);


		        for(int i=0;i<dictDataItems.size();i++){
			        	Excel value = ExcelUtils.createExcel(dictDataItems.get(i).getName(), dictDataItems.get(i).getName().length());
			        	excels.put("miss_value"+(i+1), value);
		        }
		    
			   	String starTimeStr =  DateUtils.date2Str(
							DateUtils.parseDate(startTime, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
							new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
				String endTimeStr =  DateUtils.date2Str(
							DateUtils.parseDate(endTime, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
							new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
	     	 
				
	         	String tableName="";
	         	Map<String, Object> extData = new HashMap<String, Object>();
	         	
	            List<String> tIds= new ArrayList();
	            for(AssetMeasurementProfileDi di : dis){
	            	tIds.add(di.getDataitemId());
	            }
	            if(tIds.size()>0){
	            	for(String tId : tIds){
	            		DictMeterDataStorageInfo dictMeterDataStorageInfo =dictMeterDataStorageInfoService.getEntity(tId);
	                	if(dictMeterDataStorageInfo==null){
	                		continue;
	                	}
	                	DictMeterDataStorageTable dictMeterDataStorageTable=dictMeterDataStorageTableService.getEntity(dictMeterDataStorageInfo.getTableId());
	                	if(dictMeterDataStorageTable==null){
	                		continue;
	                	}
	                	tableName=dictMeterDataStorageTable.getName();
	                	break;
	            	}
	            	
	            }
	           
	            Map<String,Object> dsm=new HashMap<String, Object>();
	            dsm.put("ids", tIds);
	            DictMeterDataStorageInfo dmd=new DictMeterDataStorageInfo();
	            dmd.setExtData(dsm);
	            List<DictMeterDataStorageInfo> dmdList= dictMeterDataStorageInfoService.getList(dmd);
	            String viewField="";
	            String viewFieldMysql="";
	            for(int i=0;i<dmdList.size();i++){
	            	if(dmdList.get(i).getShowUnit().doubleValue()==1){
	            		viewField+=",rtrim(to_char(a.VALUE"+dmdList.get(i).getFieldIndex()+",'FM999999990.9999'),'.') as miss_value"+(i+1);
	            		viewFieldMysql+=",cast(a.VALUE"+dmdList.get(i).getFieldIndex()+" as decimal(10,3)) as miss_value"+(i+1);
	            	}else{
	            		viewField+=",rtrim(to_char(a.VALUE"+dmdList.get(i).getFieldIndex()+"*"+1d+",'FM999999990.9999'),'.') as miss_value"+(i+1);
	            		viewFieldMysql+=",cast(a.VALUE"+dmdList.get(i).getFieldIndex()+"*"+1d+" as decimal(10,3)) as miss_value"+(i+1);
	            	}
	            }
            	extData.put("deviceId", meterId);
	            extData.put("tableName", tableName.toUpperCase()); 
	            extData.put("viewField", viewField);
	            extData.put("viewFieldMysql", viewFieldMysql);
           
            	extData.put("sidx", "times");
            	extData.put("sort", "asc");

    			extData.put("serizlName", meterSn);

                List<Date> timeList = Lists.newArrayList();
        
                //获取缺数数据
                 
                DataScheduleMissData dataScheduleMissData  = new DataScheduleMissData();
                Map<String, Object> extData1 = new HashMap<String, Object>();
                extData1.put("profileId", profileId);
                extData1.put("deviceId", meterId);
                extData1.put("startTime", starTimeStr);
                extData1.put("endTime", endTimeStr);
                dataScheduleMissData.setExtData(extData1);
                
                //缺点的数量确立
                List<DataScheduleMissData> dataScheduleMissDataList = 
                		dataScheduleMissDataService.getListMissDataTv(dataScheduleMissData);
  
                for(DataScheduleMissData md : dataScheduleMissDataList){
                	timeList.add(md.getMissDataTv());
                }
                
                MissDataReport missDataReportCalc  = new MissDataReport();
                extData.put("timeList", timeList);
                missDataReportCalc.setExtData(extData);
              
            	List<MissDataReport1> tmpList1 = dictMeterDataStorageTableService.getMissDataList1(missDataReportCalc);
				DataScheduleMissData dataScheduleMissDataEntity = new DataScheduleMissData();
				dataScheduleMissDataEntity.setExtData(extData);
				List<DataScheduleMissData> lackDataList = dataScheduleMissDataService.getMissDataList(dataScheduleMissDataEntity);

        		Map<Date,MissDataReport1> map = Maps.newHashMap();
				Map<Date,DataScheduleMissData> missDataMap = Maps.newHashMap();
        		if(tmpList1 != null) {
        			for(MissDataReport1 report : tmpList1) {
        				map.put(report.getTimes(), report);
        			}
        		}
				if(lackDataList!=null) {
					for(DataScheduleMissData missData:lackDataList) {
						missDataMap.put(missData.getTv(), missData);
					}
				}
  
        		List<MissDataReport1> tmplist=Lists.newArrayList();
				for(Date date:timeList) {
					MissDataReport1 report =map.get(date);
					DataScheduleMissData missData = missDataMap.get(date);
					if(report==null) {
						if(missData == null){
							report = new MissDataReport1(meterId,meterSn,date);
						}else{
							BigDecimal b2 = BigDecimal.ONE;
							String st = missData.getComStatus().compareTo(b2)==0?"online":"offline";
							String task = missData.getTaskState().equals("1")?"Success":"Failed";
							report = new MissDataReport1(meterId,meterSn,date,missData.getTaskTv(),task,missData.getFailedInfo(),missData.getUpdateTv(),st);
						}

					}else{
						if(missData != null){
							report.setMeterId(meterId);
							report.setTaskTv(missData.getTaskTv());

							if(missData.getTaskTv() != null){
								report.setTaskTvStr(DateUtils.date2Str(
										missData.getTaskTv(),
										new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"))));
							}
							report.setTimeStr(DateUtils.date2Str(missData.getTv(),new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"))));
							report.setTaskState(missData.getTaskState());
							report.setFailedInfo(missData.getFailedInfo());
							report.setUpdateTv(missData.getUpdateTv());
							report.setComStatus(missData.getComStatus());
						}

					}

					tmplist.add(report);
				}
				//这


				ExcelDataFormatter edf = new ExcelDataFormatter();
	  	        Map<String,String> tvs=new HashMap<String, String>();
	  	        tvs.put("times",  ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
	  	        edf.set("times", tvs);
	  	        
	  	     	if(tmplist.size()<=0){
					MissDataReport1 mdr=new MissDataReport1();
	  	    	  tmplist.add(mdr);
		        }

				// list生成pdf打印；
	  	      	CreatePdf.printPdf(tmplist, edf,excels, request, response);

			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	
	
	/**
	 * dataItem Title
	 * @param sn
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getDataItemIdTitle")
	@ResponseBody
	public AjaxJson getDataItemIdTitle(String meterSn , String profileId , HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			//Meter  //Commnuicator
			Map<String,Object> m=new HashMap();
			MeterDataReport meterDataReport = new MeterDataReport();

			AssetMeter assetMeter = new AssetMeter();
         	assetMeter.setSn(meterSn);
         	assetMeter = assetMeterService.get(assetMeter);
         	String meterId=assetMeter.getId();
         	m.put("deviceId", meterId);
			
        	AssetMeterGroupMap group = new AssetMeterGroupMap();
    		group.setId(meterId);
    		group.setType("1");
    		group=	assetMeterGroupMapService.get(group);
    		
    		AssetMeasurementProfileDi assetMeasurementProfileDi = new AssetMeasurementProfileDi();
    		assetMeasurementProfileDi.setMgId(group.getGroupId());
    		assetMeasurementProfileDi.setProfileId(profileId);
    		List<AssetMeasurementProfileDi> dis = assetMeasurementProfileDiService.getList(assetMeasurementProfileDi);
    
    		List<String> ids = new ArrayList();
    		
    		for(AssetMeasurementProfileDi assetMeasurementProfileDi1 : dis){
    			ids.add(assetMeasurementProfileDi1.getDataitemId());
    		}
     		
    		List<DictDataitem> dictDataItems = new ArrayList();
    		if(ids != null && ids.size() > 0){
    			dictDataItems = dictDataitemService.getListByIds(ids);
    		}

			j.put("dictDataItemsList", dictDataItems);
			
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg("Abnormal operation!");
		}
		return j;
	}
	
	/**
	 * dataItem Title
	 * @param sn
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getMissDataDetail")
	@ResponseBody
	public AjaxJson getMissDataDetail(String meterSn , String profileId , String startTime, String endTime ,HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			//Meter  //Commnuicator

		   	String starTimeStr =  DateUtils.date2Str(
						DateUtils.parseDate(startTime, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
						new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
			String endTimeStr =  DateUtils.date2Str(
						DateUtils.parseDate(endTime, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
						new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
     	 
			AssetMeter assetMeter = new AssetMeter();
         	assetMeter.setSn(meterSn);
         	assetMeter = assetMeterService.get(assetMeter);
         	String meterId=assetMeter.getId();
         	
         	AssetMeterGroupMap group = new AssetMeterGroupMap();
    		group.setId(meterId);
    		group.setType("1");
    		group=	assetMeterGroupMapService.get(group);

         	AssetMeasurementProfileDi assetMeasurementProfileDi = new AssetMeasurementProfileDi();
			assetMeasurementProfileDi.setMgId(group.getGroupId());
			assetMeasurementProfileDi.setProfileId(profileId);
			List<AssetMeasurementProfileDi> dis = assetMeasurementProfileDiService
					.getList(assetMeasurementProfileDi);
			if(dis==null) {
         		System.out.println("查不到曲线：MgId["+meterId+"],profileId["+profileId+"]");
         		return j;
         	}
         	String tableName="";
         	Map<String, Object> extData = new HashMap<String, Object>();
         	
            List<String> tIds= new ArrayList();
            for(AssetMeasurementProfileDi di : dis){
            	tIds.add(di.getDataitemId());
            }
            if(tIds.size()>0){
            	for(String tId : tIds){
            		DictMeterDataStorageInfo dictMeterDataStorageInfo =dictMeterDataStorageInfoService.getEntity(tId);
                	if(dictMeterDataStorageInfo==null){
                		continue;
                	}
                	DictMeterDataStorageTable dictMeterDataStorageTable=dictMeterDataStorageTableService.getEntity(dictMeterDataStorageInfo.getTableId());
                	if(dictMeterDataStorageTable==null){
                		continue;
                	}
                	tableName=dictMeterDataStorageTable.getName();
                	break;
            	}
            	
            }
            
            if(StringUtils.isEmpty(tableName)){
            	return j;
            }
            Map<String,Object> dsm=new HashMap<String, Object>();
            dsm.put("ids", tIds);
            DictMeterDataStorageInfo dmd=new DictMeterDataStorageInfo();
            dmd.setExtData(dsm);
            List<DictMeterDataStorageInfo> dmdList= dictMeterDataStorageInfoService.getList(dmd);
            String viewField="";
            String viewFieldMysql="";
            for(int i=0;i<dmdList.size();i++){
            	if(dmdList.get(i).getShowUnit().doubleValue()==1){
            		viewField+=",rtrim(to_char(a.VALUE"+dmdList.get(i).getFieldIndex()+",'FM999999990.9999'),'.') as miss_value"+(i+1);
            		viewFieldMysql+=",cast(a.VALUE"+dmdList.get(i).getFieldIndex()+" as decimal(10,3)) as miss_value"+(i+1);
            	}else{
            		viewField+=",rtrim(to_char(a.VALUE"+dmdList.get(i).getFieldIndex()+"*"+1d+",'FM999999990.9999'),'.') as miss_value"+(i+1);
            		viewFieldMysql+=",cast(a.VALUE"+dmdList.get(i).getFieldIndex()+"*"+1d+" as decimal(10,3)) as miss_value"+(i+1);
            	}
            }
            	extData.put("deviceId", meterId);
	            extData.put("tableName", tableName.toUpperCase()); 
	            extData.put("viewField", viewField);
	            extData.put("viewFieldMysql", viewFieldMysql);
           
            	extData.put("sidx", "times");
            	extData.put("sort", "asc");
            	 
//        		SysUser su = TokenManager.getToken();
//    			List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
//    			extData.put("orgIdList", orgIdList);
    			extData.put("serizlName", meterSn);

           // 	String dataitemId=tIds.get(0);
            	 //找出采集频率，算出有多少点
             
//             	AssetMeasurementProfile prile= getMearurementProfile(meterId,dataitemId) ;
//             	if(prile==null) {
//  
//             		System.out.println("查不到曲线：meterId["+meterId+"],dataitemId["+dataitemId+"]");
//             		return j;
//             	}
                List<Date> timeList = Lists.newArrayList();
        
                //获取缺数数据
                 
                DataScheduleMissData dataScheduleMissData  = new DataScheduleMissData();
                Map<String, Object> extData1 = new HashMap<String, Object>();
                extData1.put("profileId", profileId);
                extData1.put("deviceId", meterId);
                extData1.put("startTime", starTimeStr);
                extData1.put("endTime", endTimeStr);
                dataScheduleMissData.setExtData(extData1);
                
                //缺点的数量确立
                List<DataScheduleMissData> dataScheduleMissDataList = 
                		dataScheduleMissDataService.getListMissDataTv(dataScheduleMissData);
                if(dataScheduleMissDataList==null) {
                	  
             		System.out.println("查不到缺数数据");
             		return j;
             	}
                
                for(DataScheduleMissData md : dataScheduleMissDataList){
                	timeList.add(md.getMissDataTv());
                }
                
                MissDataReport missDataReportCalc  = new MissDataReport();
                extData.put("timeList", timeList);
                missDataReportCalc.setExtData(extData);
                if(timeList.size() == 0){
                	return j;
                }
            	List<MissDataReport> tmpList1 = dictMeterDataStorageTableService.getMissDataList(missDataReportCalc);
//            	for(MissDataReport mdr : tmpList){
//            		mdr.setTimeStr(DateUtils.date2Str(
//            				mdr.getTimes(),
//            				new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"))));
//            	}
            	
            	

        		Map<Date,MissDataReport> map = Maps.newHashMap();
        
        		if(tmpList1!=null) {
        			for(MissDataReport report:tmpList1) {
        				map.put(report.getTimes(), report);
        			}
        		}
        	//	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        		List<MissDataReport>	tmplist=Lists.newArrayList();
        		for(Date date:timeList) {
        			MissDataReport report =map.get(date);	
        			
        			if(report==null) {    
        				report = new MissDataReport(meterSn,date);
        			}
        			tmplist.add(report);
        		}

            	j.put("missDataList", tmplist);
			
				} catch (Exception e) {
					e.printStackTrace();
					j.setSuccess(false);
					j.setMsg("Abnormal operation!");
				}
			return j;
	}
	
	
	/**
	 * dataItem Title
	 * @param sn
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getMissDataDetail1")
	@ResponseBody
	public AjaxJson getMissDataDetail1(String meterSn , String profileId , String startTime, String endTime ,HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			//Meter  //Commnuicator

		   	String starTimeStr =  DateUtils.date2Str(
						DateUtils.parseDate(startTime, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
						new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
			String endTimeStr =  DateUtils.date2Str(
						DateUtils.parseDate(endTime, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
						new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
     	 
			AssetMeter assetMeter = new AssetMeter();
         	assetMeter.setSn(meterSn);
         	assetMeter = assetMeterService.get(assetMeter);
         	String meterId=assetMeter.getId();
         	
         	AssetMeterGroupMap group = new AssetMeterGroupMap();
    		group.setId(meterId);
    		group.setType("1");
    		group=	assetMeterGroupMapService.get(group);

         	AssetMeasurementProfileDi assetMeasurementProfileDi = new AssetMeasurementProfileDi();
			assetMeasurementProfileDi.setMgId(group.getGroupId());
			assetMeasurementProfileDi.setProfileId(profileId);
			List<AssetMeasurementProfileDi> dis = assetMeasurementProfileDiService
					.getList(assetMeasurementProfileDi);
			if(dis==null) {
         		System.out.println("查不到曲线：MgId["+meterId+"],profileId["+profileId+"]");
         		return j;
         	}
         	String tableName="";
         	Map<String, Object> extData = new HashMap<String, Object>();
         	
            List<String> tIds= new ArrayList();
            for(AssetMeasurementProfileDi di : dis){
            	tIds.add(di.getDataitemId());
            }
            if(tIds.size()>0){
            	for(String tId : tIds){
            		DictMeterDataStorageInfo dictMeterDataStorageInfo =dictMeterDataStorageInfoService.getEntity(tId);
                	if(dictMeterDataStorageInfo==null){
                		continue;
                	}
                	DictMeterDataStorageTable dictMeterDataStorageTable=dictMeterDataStorageTableService.getEntity(dictMeterDataStorageInfo.getTableId());
                	if(dictMeterDataStorageTable==null){
                		continue;
                	}
                	tableName=dictMeterDataStorageTable.getName();
                	break;
            	}
            	
            }
            
            if(StringUtils.isEmpty(tableName)){
            	return j;
            }
            Map<String,Object> dsm=new HashMap<String, Object>();
            dsm.put("ids", tIds);
            DictMeterDataStorageInfo dmd=new DictMeterDataStorageInfo();
            dmd.setExtData(dsm);
            List<DictMeterDataStorageInfo> dmdList= dictMeterDataStorageInfoService.getList(dmd);
            String viewField="";
            String viewFieldMysql="";
            for(int i=0;i<dmdList.size();i++){
            	if(dmdList.get(i).getShowUnit().doubleValue()==1){
            		viewField+=",rtrim(to_char(a.VALUE"+dmdList.get(i).getFieldIndex()+",'FM999999990.9999'),'.') as miss_value"+(i+1);
            		viewFieldMysql+=",cast(a.VALUE"+dmdList.get(i).getFieldIndex()+" as decimal(10,3)) as miss_value"+(i+1);
            	}else{
            		viewField+=",rtrim(to_char(a.VALUE"+dmdList.get(i).getFieldIndex()+"*"+1d+",'FM999999990.9999'),'.') as miss_value"+(i+1);
            		viewFieldMysql+=",cast(a.VALUE"+dmdList.get(i).getFieldIndex()+"*"+1d+" as decimal(10,3)) as miss_value"+(i+1);
            	}
            }
            	extData.put("deviceId", meterId);
	            extData.put("tableName", tableName.toUpperCase()); 
	            extData.put("viewField", viewField);
	            extData.put("viewFieldMysql", viewFieldMysql);
           
            	extData.put("sidx", "times");
            	extData.put("sort", "asc");
            	 
//        		SysUser su = TokenManager.getToken();
//    			List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
//    			extData.put("orgIdList", orgIdList);
    			extData.put("serizlName", meterSn);

           // 	String dataitemId=tIds.get(0);
            	 //找出采集频率，算出有多少点
             
//             	AssetMeasurementProfile prile= getMearurementProfile(meterId,dataitemId) ;
//             	if(prile==null) {
//  
//             		System.out.println("查不到曲线：meterId["+meterId+"],dataitemId["+dataitemId+"]");
//             		return j;
//             	}
                List<Date> timeList = Lists.newArrayList();
        
                //获取缺数数据
                 
                DataScheduleMissData dataScheduleMissData  = new DataScheduleMissData();
                Map<String, Object> extData1 = new HashMap<String, Object>();
                extData1.put("profileId", profileId);
                extData1.put("deviceId", meterId);
                extData1.put("startTime", starTimeStr);
                extData1.put("endTime", endTimeStr);
                dataScheduleMissData.setExtData(extData1);
                
                //缺点的数量确立
                List<DataScheduleMissData> dataScheduleMissDataList = 
                		dataScheduleMissDataService.getListMissDataTv(dataScheduleMissData);
                if(dataScheduleMissDataList==null) {
                	  
             		System.out.println("查不到缺数数据");
             		return j;
             	}
                
                for(DataScheduleMissData md : dataScheduleMissDataList){
                	timeList.add(md.getMissDataTv());
                }
                
                MissDataReport missDataReportCalc  = new MissDataReport();
                extData.put("timeList", timeList);
                missDataReportCalc.setExtData(extData);
                if(timeList.size() == 0){
                	return j;
                }
            	List<MissDataReport1> tmpList1 = dictMeterDataStorageTableService.getMissDataList1(missDataReportCalc);

            	DataScheduleMissData dataScheduleMissDataEntity = new DataScheduleMissData();
            	dataScheduleMissDataEntity.setExtData(extData);
            	List<DataScheduleMissData> lackDataList = dataScheduleMissDataService.getMissDataList(dataScheduleMissDataEntity);
            	
        		Map<Date,MissDataReport1> map = Maps.newHashMap();
        		
        		Map<Date,DataScheduleMissData> missDataMap = Maps.newHashMap();
        
        		if(tmpList1!=null) {
        			for(MissDataReport1 report:tmpList1) {
        				map.put(report.getTimes(), report);
        			}
        		}
        		if(lackDataList!=null) {
        			for(DataScheduleMissData missData:lackDataList) {
        				missDataMap.put(missData.getTv(), missData);
        			}
        		}
        		
        		
        	//	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        		List<MissDataReport1>	tmplist=Lists.newArrayList();
        		for(Date date:timeList) {
        			MissDataReport1 report =map.get(date);	
        			DataScheduleMissData missData = missDataMap.get(date);
        			if(report==null) {    
        				if(missData == null){
        					report = new MissDataReport1(meterId,meterSn,date);
        				}else{
        				
        					report = new MissDataReport1(meterId,meterSn,date,missData.getTaskTv(),missData.getTaskState(),missData.getFailedInfo(),missData.getUpdateTv(),missData.getComStatus());
        				}
        				
        			}else{
        				if(missData != null){
        					report.setMeterId(meterId);
        					report.setTaskTv(missData.getTaskTv());
        					
        					if(missData.getTaskTv() != null){
        						report.setTaskTvStr(DateUtils.date2Str(
                						missData.getTaskTv(),
                						new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"))));
        					}
            				
            				report.setTaskState(missData.getTaskState());
            				report.setFailedInfo(missData.getFailedInfo());
            				report.setUpdateTv(missData.getUpdateTv());
            				report.setComStatus(missData.getComStatus());
        				}
        			
        			}
        			
        			tmplist.add(report);
        		}

            	j.put("missDataList", tmplist);
			
				} catch (Exception e) {
					e.printStackTrace();
					j.setSuccess(false);
					j.setMsg("Abnormal operation!");
				}
			return j;
	}
    
    
    
    /**
     * 保存dataScheduleMissData信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DataScheduleMissData dataScheduleMissData,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DataScheduleMissData t=new  DataScheduleMissData();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dataScheduleMissData.getId())){
        	t=dataScheduleMissDataService.getEntity(dataScheduleMissData.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dataScheduleMissData, t);
				dataScheduleMissDataService.update(t);
				j.setMsg("修改成功");
				
			}else{
	            dataScheduleMissDataService.save(dataScheduleMissData);
	            j.setMsg("创建成功");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
    
    
    /**
     * 删除dataScheduleMissData信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DataScheduleMissData dataScheduleMissData,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dataScheduleMissDataService.deleteById(dataScheduleMissData.getId())>0){
                j.setMsg("删除成功");
            }else{
                j.setSuccess(false);
                j.setMsg("删除失败");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
	
    
    public AssetMeasurementProfile getMearurementProfile(String meterId,String dataitemId) {
		//找出采集频率   ASSET_METER_GROUP_MAP  ASSET_MEASUREMENT_PROFILE
		AssetMeterGroupMap group = new AssetMeterGroupMap();
		group.setId(meterId);
		group.setType("1");
		group=	assetMeterGroupMapService.get(group);
		String groupId = group.getGroupId();
		String protocolId=ResourceUtil.getSessionattachmenttitle("protocol.id");
		if(!"100".equals(protocolId)) {
			dataitemId=this.dictDataitemService.getDataitemBySubId(dataitemId, protocolId);
			//查出对应数据项id 
		}
		//找出曲线采集频率
//		AssetMeasurementProfileDi profileDiTmp = new AssetMeasurementProfileDi();
//		profileDiTmp.setMgId(groupId);
//		profileDiTmp.setDataitemId(dataitemId);
//		profileDiTmp= assetMeasurementProfileDiService.get(profileDiTmp);
//		
//     	String profileId = profileDiTmp.getProfileId();
//     	AssetMeasurementProfile profileTmp = new AssetMeasurementProfile();
//		profileTmp.setMgId(groupId);
//		profileTmp.setProfileId(profileId);
		return assetMeasurementProfileService.getProfileByMgAndDataItemAndType(groupId, dataitemId, "1");
	}
	
	
}