/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataScheduleMissData{ } 
 * 
 * 摘    要： dataScheduleMissData
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-12-01 06:03:45
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.data;

import java.math.BigDecimal;
import java.util.Date;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.excel.Excel;

public class DataScheduleMissData extends BaseEntity {

	/**
	 * 意义，目的和功能，以及被用到的地方<br>
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DataScheduleMissData() {
	}

	

	/*** Meter Sn */
	@Excel(name = "Meter Sn", width = 15, groups = ValidGroup1.class)
	private String meterSn;
	
	
	private String profileId;
	/*** Profile */
	@Excel(name = "Profile", width = 15, groups = ValidGroup1.class)
	private String missProfileId;
	
	private String deviceId;
	private String profile;
	
	private String mfrId;
	private String modelId;
	
	
	/*** Communicator Sn */
	@Excel(name = "Communicator Sn", width = 15, groups = ValidGroup1.class)
	private String communicatorSn;
	
	/*** communicator Type */
	@Excel(name = "Communicator Type", width = 15, groups = ValidGroup1.class)
	private String communicationType;
	
	/*** Progress */
	@Excel(name = "Progress", width = 15, groups = ValidGroup1.class)
	private Date progress;
	
	/*** Miss Data */
	@Excel(name = "Miss Data", width = 15, groups = ValidGroup1.class)
	private String missData;

	private Date tv;
	
	private Date missDataTv;
	
	private Date taskTv;
	private Date updateTv;
	private BigDecimal comStatus;
	private String taskState;
	private String failedInfo;
	
	private String startTime;
	private String endTime;
	

	public Date getUpdateTv() {
		return updateTv;
	}

	public void setUpdateTv(Date updateTv) {
		this.updateTv = updateTv;
	}

	public BigDecimal getComStatus() {
		return comStatus;
	}

	public void setComStatus(BigDecimal comStatus) {
		this.comStatus = comStatus;
	}

	public Date getTaskTv() {
		return taskTv;
	}

	public void setTaskTv(Date taskTv) {
		this.taskTv = taskTv;
	}

	public String getTaskState() {
		return taskState;
	}

	public void setTaskState(String taskState) {
		this.taskState = taskState;
	}

	public String getFailedInfo() {
		return failedInfo;
	}

	public void setFailedInfo(String failedInfo) {
		this.failedInfo = failedInfo;
	}

	public String getMfrId() {
		return mfrId;
	}

	public void setMfrId(String mfrId) {
		this.mfrId = mfrId;
	}

	public String getModelId() {
		return modelId;
	}

	public void setModelId(String modelId) {
		this.modelId = modelId;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public Date getMissDataTv() {
		return missDataTv;
	}

	public void setMissDataTv(Date missDataTv) {
		this.missDataTv = missDataTv;
	}

	public String getMissProfileId() {
		return missProfileId;
	}

	public void setMissProfileId(String missProfileId) {
		this.missProfileId = missProfileId;
	}

	public String getMissData() {
		return missData;
	}

	public void setMissData(String missData) {
		this.missData = missData;
	}

	public String getMeterSn() {
		return meterSn;
	}

	public void setMeterSn(String meterSn) {
		this.meterSn = meterSn;
	}

	public String getProfile() {
		return profile;
	}

	public void setProfile(String profile) {
		this.profile = profile;
	}

	public String getCommunicatorSn() {
		return communicatorSn;
	}

	public void setCommunicatorSn(String communicatorSn) {
		this.communicatorSn = communicatorSn;
	}

	public String getCommunicationType() {
		return communicationType;
	}

	public void setCommunicationType(String communicationType) {
		this.communicationType = communicationType;
	}

	public Date getProgress() {
		return progress;
	}

	public void setProgress(Date progress) {
		this.progress = progress;
	}

	public String getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}

	public String getProfileId() {
		return profileId;
	}

	public void setProfileId(String profileId) {
		this.profileId = profileId;
	}

	public Date getTv() {
		return tv;
	}

	public void setTv(Date tv) {
		this.tv = tv;
	}

}