/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataMeterDataEnergyMinutely{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-24 03:20:08
 * 最后修改时间：
 * 
*********************************************************************************************************/

package com.clou.esp.hes.app.web.service.data;

import com.clou.esp.hes.app.web.model.data.DataMdInsMinutely;
import com.clou.esp.hes.app.web.service.common.CommonService;

public interface DataMdInsMinutelyService extends CommonService<DataMdInsMinutely>{
	
	public DataMdInsMinutely  getLastInfoByDeviceId(String deviceId);
	
	//获取正向有功总电能 (日) [单位: kWh] 
	public String getLastProfileDaily(String deviceId);
	
}