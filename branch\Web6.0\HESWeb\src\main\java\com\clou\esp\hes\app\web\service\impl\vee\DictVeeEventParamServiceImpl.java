/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictVeeEventParam{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2019-03-06 02:51:20
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.vee;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.vee.DictVeeEventParamDao;
import com.clou.esp.hes.app.web.model.vee.DictVeeEventDataitem;
import com.clou.esp.hes.app.web.model.vee.DictVeeEventParam;
import com.clou.esp.hes.app.web.service.vee.DictVeeEventParamService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@Component
@Service("dictVeeEventParamService")
public class DictVeeEventParamServiceImpl  extends CommonServiceImpl<DictVeeEventParam>  implements DictVeeEventParamService {

	@Resource
	private DictVeeEventParamDao dictVeeEventParamDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dictVeeEventParamDao);
    }
	@SuppressWarnings("rawtypes")
	public DictVeeEventParamServiceImpl() {}
	
	@Override
	public JqGridResponseTo getForJqGrid1(JqGridSearchTo jqGridSearchTo) {
		// TODO Auto-generated method stub
		PageHelper
		.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		
		PageInfo<DictVeeEventParam> pageInfo = new PageInfo<DictVeeEventParam>(dictVeeEventParamDao.getForJqGrid1(jqGridSearchTo));
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}
	
}