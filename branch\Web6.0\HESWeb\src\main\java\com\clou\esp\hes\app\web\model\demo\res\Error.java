package com.clou.esp.hes.app.web.model.demo.res;

import java.io.Serializable;

import javax.jws.soap.SOAPBinding;
import javax.jws.soap.SOAPBinding.Style;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * 孟加拉接口项目响应数据对象
 * 
 * <AUTHOR>
 * 
 */
@SOAPBinding(style = Style.RPC)
@XmlRootElement(name = "Error")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = { "code" })
public class Error implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public Error() {
		super();
	}

	public Error(Double code) {
		super();
		this.code = code;
	}

	public Double code;

	public Double getCode() {
		return code;
	}

	public void setCode(Double code) {
		this.code = code;
	}

	@Override
	public String toString() {
		return "Error [code=" + code + "]";
	}

}
