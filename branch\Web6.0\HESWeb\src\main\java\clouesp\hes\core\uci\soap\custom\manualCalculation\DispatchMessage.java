package clouesp.hes.core.uci.soap.custom.manualCalculation;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>DispatchMessage complex type�� Java �ࡣ
 * 
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 * 
 * <pre>
 * &lt;complexType name="DispatchMessage"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="ids" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="type" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="opType" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="opStartDate" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
 *         &lt;element name="opEndDate" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
 *         &lt;element name="time" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DispatchMessage", propOrder = {
    "ids",
    "type",
    "opType",
    "opStartDate",
    "opEndDate",
    "time"
})
public class DispatchMessage {

    protected List<String> ids;
    protected String type;
    protected String opType;
    protected long opStartDate;
    protected long opEndDate;
    protected long time;

    /**
     * Gets the value of the ids property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the ids property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getIds().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     * 
     * 
     */

    /**
     * ��ȡtype���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getType() {
        return type;
    }

    public List<String> getIds() {
		return ids;
	}

	public void setIds(List<String> ids) {
		this.ids = ids;
	}

	/**
     * ����type���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setType(String value) {
        this.type = value;
    }

    /**
     * ��ȡopType���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOpType() {
        return opType;
    }

    /**
     * ����opType���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOpType(String value) {
        this.opType = value;
    }

    /**
     * ��ȡopStartDate���Ե�ֵ��
     * 
     */
    public long getOpStartDate() {
        return opStartDate;
    }

    /**
     * ����opStartDate���Ե�ֵ��
     * 
     */
    public void setOpStartDate(long value) {
        this.opStartDate = value;
    }

    /**
     * ��ȡopEndDate���Ե�ֵ��
     * 
     */
    public long getOpEndDate() {
        return opEndDate;
    }

    /**
     * ����opEndDate���Ե�ֵ��
     * 
     */
    public void setOpEndDate(long value) {
        this.opEndDate = value;
    }

    /**
     * ��ȡtime���Ե�ֵ��
     * 
     */
    public long getTime() {
        return time;
    }

    /**
     * ����time���Ե�ֵ��
     * 
     */
    public void setTime(long value) {
        this.time = value;
    }

}
