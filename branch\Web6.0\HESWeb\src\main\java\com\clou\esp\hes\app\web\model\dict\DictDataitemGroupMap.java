/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictDataitemGroupMap{ } 
 * 
 * 摘    要： 数据项分组映射
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.dict;

import java.math.BigDecimal;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DictDataitemGroupMap  extends BaseEntity {

	private static final long serialVersionUID = 5510945305483166865L;


	public DictDataitemGroupMap() {}
	
	private String 		groupId;
	private String 		dataitemId;
	private Integer     sortId;
	private Integer 	sortIdType;
	//item表的属性开始
	private String 		name;
	private String 		protocolId;
	private String 		opType;
	private String 		unit;
	private BigDecimal 	showUnit;
	private String 		protocolCode;
	private String 		dataitemType;
	//item表的属性结束
	
	private String 		paramterTypeName;


	public Integer getSortIdType() {
		return sortIdType;
	}

	public void setSortIdType(Integer sortIdType) {
		this.sortIdType = sortIdType;
	}

	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getProtocolId() {
		return protocolId;
	}
	public void setProtocolId(String protocolId) {
		this.protocolId = protocolId;
	}
	public String getOpType() {
		return opType;
	}
	public void setOpType(String opType) {
		this.opType = opType;
	}
	public String getUnit() {
		return unit;
	}
	public void setUnit(String unit) {
		this.unit = unit;
	}
	public BigDecimal getShowUnit() {
		return showUnit;
	}
	public void setShowUnit(BigDecimal showUnit) {
		this.showUnit = showUnit;
	}
	public String getProtocolCode() {
		return protocolCode;
	}
	public void setProtocolCode(String protocolCode) {
		this.protocolCode = protocolCode;
	}
	public String getDataitemType() {
		return dataitemType;
	}
	public void setDataitemType(String dataitemType) {
		this.dataitemType = dataitemType;
	}
	
	public String getParamterTypeName() {
		return paramterTypeName;
	}
	public void setParamterTypeName(String paramterTypeName) {
		this.paramterTypeName = paramterTypeName;
	}
	public String getGroupId() {
		return groupId;
	}
	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}
	public String getDataitemId() {
		return dataitemId;
	}
	public void setDataitemId(String dataitemId) {
		this.dataitemId = dataitemId;
	}
	public Integer getSortId() {
		return sortId;
	}
	public void setSortId(Integer sortId) {
		this.sortId = sortId;
	}
	
}