CREATE TABLE ASSET_CALC_OBJ 
(
	ID varchar(32) not null, 
	TYPE DECIMAL(10,0), 
	<PERSON><PERSON><PERSON> varchar(64), 
	ORG_ID varchar(32), 
	ENTITY_TYPE DECIMAL(10,0), 
	ENTITY_ID varchar(32), 
	TV_TYPE DECIMAL(10,0),
	primary key (id)
);
CREATE INDEX ASSET_LINELOSS_OBJ_INDEX_TYPE ON ASSET_CALC_OBJ (ENTITY_ID);
CREATE INDEX ASSET_LINELOSS_OBJ_INDEX_ORG ON ASSET_CALC_OBJ (ORG_ID);
 
CREATE TABLE ASSET_CALC_OBJ_MAP 
(	
	ID varchar(32) not null, 
	TYPE DECIMAL(10,0) not null, 
	METERING_ID varchar(32) not null, 
	METERING_TYPE DECIMAL(10,0), 
	DATAITEM_ID varchar(64) not null,
	primary key (id,TYPE,METERING_ID,DATAITEM_ID)
) ;

CREATE TABLE DATA_CALC_OBJ 
(	
	CA<PERSON>_OBJ_ID varchar(32) not null,  
	TV datetime(6) not null,  
	OUT_TOTAL DECIMAL(10,0), 
	IN_TOTAL DECIMAL(10,0), 
	CALC_VALUE DECIMAL(10,2), 
	MISS_DATA DECIMAL(10,0), 
	UPDATE_TV datetime(6),
	primary key (CALC_OBJ_ID,TV)
) ;
CREATE INDEX DATA_LINELOSS_DAILY_INDEX1 ON DATA_CALC_OBJ (UPDATE_TV) ;

CREATE TABLE DATA_MD_INTERVAL_DAILY
(	
	DEVICE_ID varchar(32) not null, 
	TV datetime(6) not null, 
	VALUE1 varchar(20), 
	VALUE2 varchar(20), 
	VALUE3 varchar(20), 
	VALUE4 varchar(20), 
	VALUE5 varchar(20), 
	VALUE6 varchar(20), 
	VALUE7 varchar(20), 
	VALUE8 varchar(20), 
	VALUE9 varchar(20), 
	VALUE10 varchar(20), 
	VALUE11 varchar(20), 
	VALUE12 varchar(20), 
	VALUE13 varchar(20), 
	VALUE14 varchar(20), 
	VALUE15 varchar(20), 
	VALUE16 varchar(20), 
	VALUE17 varchar(20), 
	VALUE18 varchar(20), 
	VALUE19 varchar(20), 
	VALUE20 varchar(20), 
	UPDATE_TV datetime(6),
	primary key (DEVICE_ID,TV)
);
CREATE INDEX DATA_MD_INTERVAL_DATA_INDEX2 ON DATA_MD_INTERVAL_DAILY (UPDATE_TV);

CREATE TABLE DATA_MD_INTERVAL_MONTHLY
(	
	DEVICE_ID varchar(32) not null, 
	TV datetime(6) not null, 
	VALUE1 varchar(20), 
	VALUE2 varchar(20), 
	VALUE3 varchar(20), 
	VALUE4 varchar(20), 
	VALUE5 varchar(20), 
	VALUE6 varchar(20), 
	VALUE7 varchar(20), 
	VALUE8 varchar(20), 
	VALUE9 varchar(20), 
	VALUE10 varchar(20), 
	VALUE11 varchar(20), 
	VALUE12 varchar(20), 
	VALUE13 varchar(20), 
	VALUE14 varchar(20), 
	VALUE15 varchar(20), 
	VALUE16 varchar(20), 
	VALUE17 varchar(20), 
	VALUE18 varchar(20), 
	VALUE19 varchar(20), 
	VALUE20 varchar(20), 
	UPDATE_TV datetime(6),
	primary key (DEVICE_ID,TV)
);

CREATE TABLE DICT_REPORT 
(	
	ID varchar(32) not null,  
	REPORTNAME varchar(64) not null,  
	SORT_ID DECIMAL(11,0), 
	FUNCTIONURL varchar(64), 
	PARENT_ID varchar(20),
	primary key (ID)
) ;

Insert into DICT_REPORT (ID,REPORTNAME,SORT_ID,FUNCTIONURL,PARENT_ID) values ('100001','Billing Reports',1,null,'100');
Insert into DICT_REPORT (ID,REPORTNAME,SORT_ID,FUNCTIONURL,PARENT_ID) values ('200001','Line Loss - Organization',1,'dictReportController/lineLossOrgList.do','200');
Insert into DICT_REPORT (ID,REPORTNAME,SORT_ID,FUNCTIONURL,PARENT_ID) values ('200002','Line Loss - Single Object',2,'dictReportController/lineLossSingleObjectList.do','200');
Insert into DICT_REPORT (ID,REPORTNAME,SORT_ID,FUNCTIONURL,PARENT_ID) values ('100','Billing Reports',1,null,'0');
Insert into DICT_REPORT (ID,REPORTNAME,SORT_ID,FUNCTIONURL,PARENT_ID) values ('200','Line Loss Reports',2,null,'0');
