/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataMeterDataEnergyDaily{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-24 03:20:08
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.data;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.data.DataMdEnergyDailyDao;
import com.clou.esp.hes.app.web.model.data.DataMdEnergyDaily;
import com.clou.esp.hes.app.web.service.data.DataMdEnergyDailyService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dataMeterDataEnergyDailyService")
public class DataMdEnergyDailyServiceImpl  extends CommonServiceImpl<DataMdEnergyDaily>  implements DataMdEnergyDailyService {

	@Resource
	private DataMdEnergyDailyDao dataMeterDataEnergyDailyDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dataMeterDataEnergyDailyDao);
    }
	@SuppressWarnings("rawtypes")
	public DataMdEnergyDailyServiceImpl() {}
	
	
}