delete from sys_utility where id='29018328bd4011e79bb968f728c516f9';
INSERT INTO sys_utility VALUES ('29018328bd4011e79bb968f728c516f9', '1', 'ClouESP PPM Default Utility', 'ClouESP PPM Default Utility', 'szc<PERSON>@szclou.com', '18675569695', '2019-01-01 00:00:00', '2069-01-01 00:00:00', '3000', '10', 'GHC', null, null, null, null, 'clou.png', 'Shenzhen Clou Electronics Co., Ltd', 'Clou POWER', null, null, 'dd-MM-yyyy', 'HH:mm:ss', 'dd-MM-yyyy HH:mm:ss', '0', '2018-05-22 15:18:57', '2018-05-22 15:18:57', '系统管理使用租户');

update PPM_SYS_ROLE set UTILITY_ID = '29018328bd4011e79bb968f728c516f9';
update PPM_SYS_USER set UTILITY_ID = '29018328bd4011e79bb968f728c516f9';
update SYS_ORG set UTILITY_ID = '29018328bd4011e79bb968f728c516f9';
update SYS_ROLE set UTILITY_ID = '29018328bd4011e79bb968f728c516f9';
update SYS_USER set UTILITY_ID = '29018328bd4011e79bb968f728c516f9';

drop table if exists ppm_data_user_log;
  CREATE TABLE PPM_DATA_USER_LOG 
   (	
    TV DATE NOT NULL, 
	USER_ID VARCHAR2(32) NOT NULL, 
	LOG_TYPE VARCHAR2(64) NOT NULL, 
	LOG_SUB_TYPE VARCHAR2(64) NOT NULL, 
	DETAIL VARCHAR2(256),
	PRIMARY KEY (TV, USER_ID, LOG_TYPE, LOG_SUB_TYPE)
   );
   
DELETE FROM DICT_DATAITEM_GROUP WHERE ID='1003005';

update dict_dataitem_parse_dlms set parse_type = 'double-long', parse_len = 4 where code = '112#0.0.19.10.0.255#5';

Insert into PPM_SYS_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1a458f11beda11e79bb968f728c50008','29018328bd4011e79bb968f728c516f9',2,'Debt Management Report',6,'uReportController/debtManageReport.do','1e858f11beda11e79bb968f728c50006',null,null,null);
Insert into PPM_SYS_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1c458f11beda11e79bb968f728c50009','29018328bd4011e79bb968f728c516f9',2,'Non Purchased Customer Report',7,'uReportController/nonPurchasedCustomerReport.do','1e858f11beda11e79bb968f728c50006',null,null,null);

delete from ppm_dict_operation  where ID in ('1017001','1017002','1017003','1017005','1017006','1017007','1017008','1017009','1017010');
Insert into PPM_DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2006005','Add/Edit user','2006','/sysUserController/toAddSysUser.do',2,'Add user; Edit user; Reset password');

update ppm_dict_detail set gui_display_name = 'GETFL & NHIL' where dict_id ='60' and inner_value = 6;

alter table ppm_asset_tariff_step_detail drop PRIMARY KEY;
alter table ppm_asset_tariff_step_detail modify tariff_name varchar2(120);
alter table ppm_asset_tariff_step_detail add PRIMARY KEY (GROUP_ID,STEP_INDEX,tariff_name);

delete from ppm_sys_role_menu where menu_id='1e858f11beda11e79bb968f728c52014';
delete from ppm_sys_menu where id='1e858f11beda11e79bb968f728c52014';