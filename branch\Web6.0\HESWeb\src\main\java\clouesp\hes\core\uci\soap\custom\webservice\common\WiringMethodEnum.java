package clouesp.hes.core.uci.soap.custom.webservice.common;


/**
 * @ClassName: BaudType
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年8月7日 上午10:39:41
 *
 */
public enum WiringMethodEnum {

	PHASE_0(0,"Single Phase"),
	PHASE_1(1,"Three-phase Three-wire"),
	PHASE_2(2,"Three-phase Four-wire");
	
	private WiringMethodEnum(int index,String phase) {
		this.index = index;
		this.phase = phase;
	}
	
	public static WiringMethodEnum parseWiringMethod(String phase) {
		for (WiringMethodEnum type : values()) {
			if(type.phase.equals(phase))
				return type;
		}
		return null;
	}
	
	private int index;
	private String phase;
	
	public int getIndex() {
		return index;
	}
	public String getPhase() {
		return phase;
	}
	
	
}
