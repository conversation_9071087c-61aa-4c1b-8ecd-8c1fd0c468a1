/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataDiExportProgress{ } 
 * 
 * 摘    要： dataDiExportProgress
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-04-04 06:12:00
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.data;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DataDiExportProgress  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DataDiExportProgress() {
	}

	/**tv*/
	private java.util.Date tv;
	/**exportTv*/
	private java.util.Date exportTv;
	/**exportResult*/
	private java.lang.String exportResult;
	/**exportReason*/
	private java.lang.String exportReason;

	/**
	 * tv
	 * @return the value of DATA_DI_EXPORT_PROGRESS.TV
	 * @mbggenerated 2018-04-04 06:12:00
	 */
	public java.util.Date getTv() {
		return tv;
	}

	/**
	 * tv
	 * @param tv the value for DATA_DI_EXPORT_PROGRESS.TV
	 * @mbggenerated 2018-04-04 06:12:00
	 */
    	public void setTv(java.util.Date tv) {
		this.tv = tv;
	}
	/**
	 * exportTv
	 * @return the value of DATA_DI_EXPORT_PROGRESS.EXPORT_TV
	 * @mbggenerated 2018-04-04 06:12:00
	 */
	public java.util.Date getExportTv() {
		return exportTv;
	}

	/**
	 * exportTv
	 * @param exportTv the value for DATA_DI_EXPORT_PROGRESS.EXPORT_TV
	 * @mbggenerated 2018-04-04 06:12:00
	 */
    	public void setExportTv(java.util.Date exportTv) {
		this.exportTv = exportTv;
	}
	/**
	 * exportResult
	 * @return the value of DATA_DI_EXPORT_PROGRESS.EXPORT_RESULT
	 * @mbggenerated 2018-04-04 06:12:00
	 */
	public java.lang.String getExportResult() {
		return exportResult;
	}

	/**
	 * exportResult
	 * @param exportResult the value for DATA_DI_EXPORT_PROGRESS.EXPORT_RESULT
	 * @mbggenerated 2018-04-04 06:12:00
	 */
    	public void setExportResult(java.lang.String exportResult) {
		this.exportResult = exportResult;
	}
	/**
	 * exportReason
	 * @return the value of DATA_DI_EXPORT_PROGRESS.EXPORT_REASON
	 * @mbggenerated 2018-04-04 06:12:00
	 */
	public java.lang.String getExportReason() {
		return exportReason;
	}

	/**
	 * exportReason
	 * @param exportReason the value for DATA_DI_EXPORT_PROGRESS.EXPORT_REASON
	 * @mbggenerated 2018-04-04 06:12:00
	 */
    	public void setExportReason(java.lang.String exportReason) {
		this.exportReason = exportReason;
	}

	public DataDiExportProgress(java.util.Date tv 
	,java.util.Date exportTv 
	,java.lang.String exportResult 
	,java.lang.String exportReason ) {
		super();
		this.tv = tv;
		this.exportTv = exportTv;
		this.exportResult = exportResult;
		this.exportReason = exportReason;
	}

}