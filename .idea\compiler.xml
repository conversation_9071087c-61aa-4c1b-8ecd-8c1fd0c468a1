<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="calculation" />
        <module name="Schedules" />
        <module name="Task" />
        <module name="logger" />
        <module name="storage" />
        <module name="gyuci" />
        <module name="uci_s" />
        <module name="Asset" />
        <module name="Calculation" />
        <module name="Protocol" />
        <module name="DbAccess" />
        <module name="MQBus" />
        <module name="jnuci" />
        <module name="AssetTrans" />
        <module name="hsuci" />
        <module name="loggerqueryservice" />
        <module name="IntervalCalc" />
        <module name="loggerqueryser" />
        <module name="task" />
        <module name="Storage" />
        <module name="channels" />
        <module name="syuci" />
        <module name="hxuci" />
        <module name="loggerquery" />
        <module name="UCI" />
        <module name="MdmUCI" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel target="1.8">
      <module name="HESWeb" target="1.7" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="hsuci" options="-parameters" />
    </option>
  </component>
</project>