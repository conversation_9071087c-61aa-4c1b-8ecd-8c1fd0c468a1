/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataMeterEvent{ } 
 * 
 * 摘    要： dataMeterEvent
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-24 01:56:05
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.data;

import java.util.Date;
import java.util.List;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.excel.Excel;

public class DataMeterEvent extends BaseEntity {

	/**
	 * 意义，目的和功能，以及被用到的地方<br>
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DataMeterEvent() {
	}

	private String deviceId;
	private String commId;
	private String deviceType;

	private String eventId;
	@Excel(name = "Serial Number", width = 30, groups = ValidGroup1.class)
	private String sn;
	@Excel(name = "Communicator Sn", width = 30, groups = ValidGroup1.class)
	private String communicatorSn;

	@Excel(name = "Time", width = 30, groups = ValidGroup1.class)
	private Date tv;
	private String stv;
	// @Excel(name = "Region", width = 30, groups = ValidGroup1.class)
	private String orgName;
	@Excel(name = "Event Type", width = 30, groups = ValidGroup1.class)
	private String eventType;
	@Excel(name = "Event", width = 30, groups = ValidGroup1.class)
	private String event;
	@Excel(name = "Event Details", width = 30, groups = ValidGroup1.class)
	private String eventDetail;
	private String tvStart;
	private String tvEnd;
	
	private String eventSearchType;
	private List<String> meterIdList;
	private List<String> orgIdList;

	
	public List<String> getOrgIdList() {
		return orgIdList;
	}

	public void setOrgIdList(List<String> orgIdList) {
		this.orgIdList = orgIdList;
	}

	public List<String> getMeterIdList() {
		return meterIdList;
	}

	public void setMeterIdList(List<String> meterIdList) {
		this.meterIdList = meterIdList;
	}

	public String getEventSearchType() {
		return eventSearchType;
	}

	public void setEventSearchType(String eventSearchType) {
		this.eventSearchType = eventSearchType;
	}

	public String getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}

	public Date getTv() {
		return tv;
	}

	public void setTv(Date tv) {
		this.tv = tv;
	}

	public String getEventId() {
		return eventId;
	}

	public void setEventId(String eventId) {
		this.eventId = eventId;
	}


	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}

	public String getEventType() {
		return MutiLangUtil.assemblyI18nData(MutiLangUtil.DATAITEM_GROUP_I18N,eventType);
	}

	public void setEventType(String eventType) {
		this.eventType = eventType;
	}

	public String getEvent() {
		return MutiLangUtil.assemblyI18nData(MutiLangUtil.DATAITEM_I18N,event);
	}

	public void setEvent(String event) {
		this.event = event;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getStv() {
		return stv;
	}

	public void setStv(String stv) {
		this.stv = stv;
	}

	public String getTvStart() {
		return tvStart;
	}

	public void setTvStart(String tvStart) {
		this.tvStart = tvStart;
	}

	public String getTvEnd() {
		return tvEnd;
	}

	public void setTvEnd(String tvEnd) {
		this.tvEnd = tvEnd;
	}

	public String getEventDetail() {
		return eventDetail;
	}

	public void setEventDetail(String eventDetail) {
		this.eventDetail = eventDetail;
	}

	public String getCommunicatorSn() {
		return communicatorSn;
	}

	public void setCommunicatorSn(String communicatorSn) {
		this.communicatorSn = communicatorSn;
	}

	public String getDeviceType() {
		return deviceType;
	}

	public void setDeviceType(String deviceType) {
		this.deviceType = deviceType;
	}

	public String getCommId() {
		return commId;
	}

	public void setCommId(String commId) {
		this.commId = commId;
	}

}