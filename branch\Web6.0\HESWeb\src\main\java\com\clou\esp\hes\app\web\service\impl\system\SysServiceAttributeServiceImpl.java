/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysServiceAttribute{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-03-29 03:17:56
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.system;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.system.SysServiceAttributeDao;
import com.clou.esp.hes.app.web.model.system.SysServiceAttribute;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.clou.esp.hes.app.web.service.system.SysServiceAttributeService;

@Component
@Service("sysServiceAttributeService")
public class SysServiceAttributeServiceImpl  extends CommonServiceImpl<SysServiceAttribute>  implements SysServiceAttributeService {

	@Resource
	private SysServiceAttributeDao sysServiceAttributeDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(sysServiceAttributeDao);
    }
	public SysServiceAttributeServiceImpl() {}
	
	@Override
	public void batchSaveAttr(List<SysServiceAttribute> list) {
		if(list.size() > 0){
			for (int i = 0; i < list.size(); i++) {
				sysServiceAttributeDao.insertEntity(list.get(i));
			}
		}
	}
	
	
}