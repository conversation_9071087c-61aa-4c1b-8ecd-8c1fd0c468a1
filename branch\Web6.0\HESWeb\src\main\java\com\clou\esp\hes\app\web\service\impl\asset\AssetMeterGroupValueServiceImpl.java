/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeterGroupValue{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-01-16 01:58:30
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.asset;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.asset.AssetMeterGroupValueDao;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroupValue;
import com.clou.esp.hes.app.web.service.asset.AssetMeterGroupValueService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("assetMeterGroupValueService")
public class AssetMeterGroupValueServiceImpl  extends CommonServiceImpl<AssetMeterGroupValue>  implements AssetMeterGroupValueService {

	@Resource
	private AssetMeterGroupValueDao assetMeterGroupValueDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(assetMeterGroupValueDao);
    }
	public AssetMeterGroupValueServiceImpl() {}
	
	@Override
	public AssetMeterGroupValue selectOneDate(String groupId, String dataitemId) {
		return assetMeterGroupValueDao.selectOneDate(groupId, dataitemId);
	}
	
	
}