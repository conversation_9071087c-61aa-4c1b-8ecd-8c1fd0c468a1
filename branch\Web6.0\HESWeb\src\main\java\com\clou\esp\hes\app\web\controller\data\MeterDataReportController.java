/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictMeterDataStorageTable{ } 
 * 
 * 摘    要： dictMeterDataStorageTable
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.data;

import java.math.BigDecimal;
import java.text.Format;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.clou.esp.hes.app.web.model.system.SysOrg;
import com.clou.esp.hes.app.web.service.system.SysUserService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.tag.vo.ZtreeUtil;
import com.clou.esp.hes.app.web.core.util.CreatePdf;
import com.clou.esp.hes.app.web.core.util.ExcelUtils;
import com.clou.esp.hes.app.web.core.util.OrganizationUtils;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.model.asset.AssetCommunicator;
import com.clou.esp.hes.app.web.model.asset.AssetCustomer;
import com.clou.esp.hes.app.web.model.asset.AssetEntityRelationship;
import com.clou.esp.hes.app.web.model.asset.AssetLine;
import com.clou.esp.hes.app.web.model.asset.AssetMeasurementProfile;
import com.clou.esp.hes.app.web.model.asset.AssetMeasurementProfileDi;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroupMap;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;
import com.clou.esp.hes.app.web.model.dict.DictDataitemGroup;
import com.clou.esp.hes.app.web.model.dict.DictMeterDataStorageInfo;
import com.clou.esp.hes.app.web.model.dict.DictMeterDataStorageTable;
import com.clou.esp.hes.app.web.model.report.MeterDataReport;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.asset.AssetCommunicatorService;
import com.clou.esp.hes.app.web.service.asset.AssetCustomerService;
import com.clou.esp.hes.app.web.service.asset.AssetEntityRelationshipService;
import com.clou.esp.hes.app.web.service.asset.AssetLineManagementService;
import com.clou.esp.hes.app.web.service.asset.AssetMeasurementProfileDiService;
import com.clou.esp.hes.app.web.service.asset.AssetMeasurementProfileService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterGroupMapService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.asset.AssetTransformerService;
import com.clou.esp.hes.app.web.service.data.DataMeterEventService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemGroupService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemService;
import com.clou.esp.hes.app.web.service.dict.DictMeterDataStorageInfoService;
import com.clou.esp.hes.app.web.service.dict.DictMeterDataStorageTableService;
import com.clou.esp.hes.app.web.service.system.SysOrgService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.power7000g.core.excel.Excel;
import com.power7000g.core.excel.ExcelDataFormatter;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;
import com.power7000g.core.util.redis.JedisUtils;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * <AUTHOR>
 * @时间：2017-11-22 03:30:03
 * @描述：MeterDataReportController类
 */
@Controller
@RequestMapping("/meterDataReportController")
public class MeterDataReportController extends BaseController{

 	@Resource
    private DictMeterDataStorageTableService    dictMeterDataStorageTableService;
	@Resource
    private DictMeterDataStorageInfoService     dictMeterDataStorageInfoService;
	@Resource
	private DataMeterEventService 				dataMeterEventService;
	@Resource
	private DictDataitemGroupService 			dictDataitemGroupService;
	@Resource
	private DictDataitemService 				dictDataitemService;
	@Resource
    private AssetMeterService 					assetMeterService;
	@Resource	
    private AssetCommunicatorService 			assetCommunicatorService;
	@Resource
    private SysOrgService 						sysOrgService;
	@Resource
	private AssetMeterGroupMapService 			assetMeterGroupMapService;
	@Resource
	private AssetMeasurementProfileService    	assetMeasurementProfileService;
	@Resource
	private AssetMeasurementProfileDiService    assetMeasurementProfileDiService;
	@Resource
	private AssetEntityRelationshipService 		assetEntityRelationshipService;
	@Resource
	private AssetLineManagementService 			assetLineService;
	@Resource
	private AssetTransformerService 			assetTranService;
	@Resource
	private AssetCustomerService 			    assetCustomerService;
	/**
	 * 跳转到dictMeterDataStorageTable列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
		List<DictMeterDataStorageTable> tableList=dictMeterDataStorageTableService.getAllList();
//		model.addAttribute("tableReplace", RoletoJson.listToReplaceStr(tableList, "id", "desc"));
		String channelIds="";
		String channelNames="";
		DictDataitemGroup entity=new DictDataitemGroup();
		entity.setAppType("1");
		entity.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
		List<DictDataitemGroup> ddgList = dictDataitemGroupService.getList(entity);
		if(ddgList.size()>0){
			DictDataitem dd=new DictDataitem();
			Map<String, Object> qm = new HashMap<String, Object>();
			qm.put("groupId", ddgList.get(0).getId());
			model.addAttribute("groupId", ddgList.get(0).getId());
			qm.put("appType", "1");
			dd.setExtData(qm);
		//	dd.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
			channelIds+=ddgList.get(0).getId();
			List<DictDataitem> ddList=dictDataitemService.getList(dd);
			for(DictDataitem d:ddList){
				channelIds+=","+d.getId();
				if(StringUtil.isNotEmpty(channelNames)){
					channelNames+="&"+d.getName();
				}else{
					channelNames+=d.getName();
				}
			}
		}
		String groupReplace = RoletoJson.listToReplaceStr(ddgList, "id", "name");
		
		//设置开始结束时间
		SimpleDateFormat sdf = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.formatter"));
	    SimpleDateFormat sdf1 = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));

	    String date =sdf.format(new Date());
	    model.addAttribute("startDate", date+" 00:00:00");
	    String endDate=sdf1.format(new Date());
	    model.addAttribute("endDate", endDate);
	    
		model.addAttribute("groupReplace", groupReplace);
		model.addAttribute("channelIds", channelIds);
		model.addAttribute("channelNames", channelNames);
		model.addAttribute("meterType", "Meter");
        return new ModelAndView("/data/meterDataReport");
    }

    public boolean hasProfile(String profileId,String[] profileList) {
		for(int i = 0;i < profileList.length;i++) {
			if(profileId.equals(profileList[i])) {
				return true;
			}
		}
		return false;
	}
	@Resource
	private SysUserService sysUserService;

	/**
	 * 跳转到dictMeterDataStorageTable列表页面
	 *
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "listByOrg")
	public ModelAndView listByOrg(HttpServletRequest request, Model model) {
		List<DictMeterDataStorageTable> tableList=dictMeterDataStorageTableService.getAllList();
		//model.addAttribute("tableReplace", RoletoJson.listToReplaceStr(tableList, "id", "desc"));
		String channelIds="";
		String channelNames="";
		DictDataitemGroup entity=new DictDataitemGroup();
		entity.setAppType("1");
		entity.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
		String meterdatareportpriflelist = ResourceUtil.getSessionattachmenttitle("meterdatareport.profile");
		String[] profileList = meterdatareportpriflelist.split(",");
		List<DictDataitemGroup> ddgList = dictDataitemGroupService.getList(entity);


		Iterator<DictDataitemGroup> iterator = ddgList.iterator();
		while (iterator.hasNext()) {
			DictDataitemGroup next = iterator.next();
			String profileId = next.getId();
			if (!hasProfile(profileId, profileList)) {
				iterator.remove();
			}
		}
		if(ddgList.size()>0){
			DictDataitem dd=new DictDataitem();
			Map<String, Object> qm = new HashMap<String, Object>();
			qm.put("groupId", ddgList.get(0).getId());
			model.addAttribute("groupId", ddgList.get(0).getId());
			qm.put("appType", "1");
			dd.setExtData(qm);
			//	dd.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
			channelIds+=ddgList.get(0).getId();
			List<DictDataitem> ddList=dictDataitemService.getList(dd);
			for(DictDataitem d:ddList){
				channelIds+=","+d.getId();
				if(StringUtil.isNotEmpty(channelNames)){
					channelNames+="&"+d.getName();
				}else{
					channelNames+=d.getName();
				}
			}
		}
		String groupReplace = RoletoJson.listToReplaceStr(ddgList, "id", "name");

		//设置开始结束时间
		SimpleDateFormat sdf = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.formatter"));
		SimpleDateFormat sdf1 = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));

		String date =sdf.format(new Date());
		model.addAttribute("startDate", date);
		String endDate=sdf1.format(new Date());
		model.addAttribute("endDate", date);

		SysUser su = TokenManager.getToken();
		SysUser sysUser = sysUserService.getEntity(su.getId());
		SysOrg sysOrg = sysOrgService.getEntity(sysUser.getOrgId());
		model.addAttribute("sysOrg", sysOrg);
		model.addAttribute("groupReplace", groupReplace);
		model.addAttribute("channelIds", channelIds);
		model.addAttribute("channelNames", channelNames);
		return new ModelAndView("/data/meterDataReportOrg");
	}

	/**
	 * 跳转到dictMeterDataStorageTable列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "listReport")
	public ModelAndView listReport(HttpServletRequest request, Model model) {
		List<DictMeterDataStorageTable> tableList=dictMeterDataStorageTableService.getAllList();
//		model.addAttribute("tableReplace", RoletoJson.listToReplaceStr(tableList, "id", "desc"));
		String channelIds="";
		String channelNames="";
		DictDataitemGroup entity=new DictDataitemGroup();
		entity.setAppType("1");
		entity.setId("3001002");
		//entity.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
		List<DictDataitemGroup> ddgList = dictDataitemGroupService.getList(entity);
		if(ddgList.size()>0){
			DictDataitem dd=new DictDataitem();
			Map<String, Object> qm = new HashMap<String, Object>();
			qm.put("groupId", ddgList.get(0).getId());
			model.addAttribute("groupId", ddgList.get(0).getId());
			qm.put("appType", "1");
			dd.setExtData(qm);
			//	dd.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
			channelIds+=ddgList.get(0).getId();
			List<DictDataitem> ddList=dictDataitemService.getList(dd);
			for(DictDataitem d:ddList){
				channelIds+=","+d.getId();
				if(StringUtil.isNotEmpty(channelNames)){
					channelNames+="&"+d.getName();
				}else{
					channelNames+=d.getName();
				}
			}
		}
		String groupReplace = RoletoJson.listToReplaceStr(ddgList, "id", "name");
		
		//设置开始结束时间
		SimpleDateFormat sdf = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.formatter"));
		SimpleDateFormat sdf1 = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
		
		String date =sdf.format(new Date());
		model.addAttribute("startDate", date+" 00:00:00");
		String endDate=sdf1.format(new Date());
		model.addAttribute("endDate", endDate);
		
		model.addAttribute("groupReplace", groupReplace);
		model.addAttribute("channelIds", channelIds);
		model.addAttribute("channelNames", channelNames);
		return new ModelAndView("/data/intervalDataReport");
	}
	/**
	 * 获取选择显示列表树形框数据
	 * @param searchName
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getChannelZtree")
	@ResponseBody
	public AjaxJson getChannelZtree(String appType,String searchName,HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		List<Map<String, Object>> list = new ArrayList<>();
		DictDataitemGroup entity=new DictDataitemGroup();
		entity.setAppType(appType);
		entity.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
		List<DictDataitemGroup> ddgList = dictDataitemGroupService.getList(entity);
		for(DictDataitemGroup g:ddgList){
			Map<String, Object> m = new HashMap<String, Object>();
			m.put("id", g.getId());
			m.put("name", g.getName());
			m.put("parent", "");
			m.put("expanded", true);
			DictDataitem dd=new DictDataitem();
			dd.put("groupId", g.getId());
			dd.put("appType", appType);
	//		dd.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
			if(StringUtil.isNotEmpty(searchName)&&g.getName().indexOf(searchName)<0){
				dd.setName(searchName);
			}
			List<DictDataitem> ddList=dictDataitemService.getList(dd);
			List<Map<String, Object>> listk = new ArrayList<>();
			for(DictDataitem d:ddList){
				Map<String, Object> dm = new HashMap<String, Object>();
				dm.put("id", d.getId());
				dm.put("name", d.getName());
				dm.put("parent", g.getId());
				dm.put("expanded", false);
				listk.add(dm);
			}
			if(StringUtil.isNotEmpty(searchName)){
				if(listk.size()>0){
					m.put("list", listk);
					m.put("expanded", true);
					list.add(m);
				}
			}else{
				if(listk.size()>0){
				m.put("list", listk);
				list.add(m);
				}
			}
		}
		ZtreeUtil jtu = new ZtreeUtil();
		jtu.setIdFname("id");
		jtu.setNameFname("name");
		jtu.setpIdFname("parent");
		jtu.setSubsetFieldName("list");
		jtu.setOpenFname("expanded");
		j.setObj(jtu.getZtreeNodeData(list));
		return j;
	}
	
	/**
	 * 获取选择显示列表树形框数据
	 * @param searchName
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getChannelZtreeHY")
	@ResponseBody
	public AjaxJson getChannelZtreeHY(String appType,String searchName,HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		ZtreeUtil jtu = new ZtreeUtil();
		jtu.setIdFname("id");
		jtu.setNameFname("name");
		jtu.setpIdFname("parent");
		jtu.setSubsetFieldName("list");
		jtu.setOpenFname("expanded");
		j.setObj(jtu.getZtreeNodeData(buildChannelZtreeHY()));
		return j;
	}
	
	@RequestMapping(value = "getChanneldataGridList")
	@ResponseBody
	public AjaxJson getChanneldataGridList() {
		AjaxJson j = new AjaxJson();
		
		List<Map<String, Object>> channelRecords = new ArrayList<Map<String, Object>>();
		
		channelRecords.add(buildChanneldataMap("1#1.0.0.2.0.255#2","Active firmware identity",1));
		channelRecords.add(buildChanneldataMap("1#0.0.96.55.30.255#2","Hardware version",2));
		// channelRecords.add(buildChanneldataMap("1#0.0.96.1.0.255#2","Device ID",3));
		channelRecords.add(buildChanneldataMap("1#0.0.96.1.9.255#2","Utility",4));
		channelRecords.add(buildChanneldataMap("1#0.0.96.1.0.255#2","Meter serial number",5));
		channelRecords.add(buildChanneldataMap("1#0.0.96.5.1.255#2","By code",6));
		channelRecords.add(buildChanneldataMap("1#0.0.96.51.4.255#2","All segments",7));
		channelRecords.add(buildChanneldataMap("1#1.0.0.9.1.255#2","Local time",8));
		channelRecords.add(buildChanneldataMap("1#1.0.0.9.2.255#2","Local date",9));
		channelRecords.add(buildChanneldataMap("1#0.0.17.0.0.255#2","Logical name",10));
		channelRecords.add(buildChanneldataMap("3#1.0.1.8.0.255#2","Active energy import (+A)",11));
		channelRecords.add(buildChanneldataMap("3#1.0.1.8.1.255#2","Active energy import (+A)- rate 1",12));
		channelRecords.add(buildChanneldataMap("3#1.0.1.8.2.255#2","Active energy import (+A)- rate 2",13));
		channelRecords.add(buildChanneldataMap("3#1.0.1.8.3.255#2","Active energy import (+A)- rate 3",14));
		channelRecords.add(buildChanneldataMap("3#1.0.1.8.4.255#2","Active energy import (+A)- rate 4",15));
		
		channelRecords.add(buildChanneldataMap("3#1.0.128.8.0.255#2","Reactive energy combined total (|+R|+|-R|)",16));
		
		channelRecords.add(buildChanneldataMap("3#1.0.2.8.0.255#2","Active energy export (-A)",17));
		channelRecords.add(buildChanneldataMap("3#1.0.2.8.1.255#2","Active energy export (-A)- rate 1",18));
		channelRecords.add(buildChanneldataMap("3#1.0.2.8.2.255#2","Active energy export (-A)- rate 2",19));
		channelRecords.add(buildChanneldataMap("3#1.0.2.8.3.255#2","Active energy export (-A)- rate 3",20));
		channelRecords.add(buildChanneldataMap("3#1.0.2.8.4.255#2","Active energy export (-A)- rate 4",21));
		channelRecords.add(buildChanneldataMap("3#1.0.3.8.0.255#2","Reactive energy import (+R)",22));
		channelRecords.add(buildChanneldataMap("3#1.0.3.8.1.255#2","Reactive energy import (+R)- rate 1",23));
		channelRecords.add(buildChanneldataMap("3#1.0.3.8.2.255#2","Reactive energy import (+R)- rate 2",24));
		channelRecords.add(buildChanneldataMap("3#1.0.3.8.3.255#2","Reactive energy import (+R)- rate 3",25));
		channelRecords.add(buildChanneldataMap("3#1.0.3.8.4.255#2","Reactive energy import (+R)- rate 4",26));
		channelRecords.add(buildChanneldataMap("3#1.0.4.8.0.255#2","Reactive energy export (-R)",27));
		channelRecords.add(buildChanneldataMap("3#1.0.4.8.1.255#2","Reactive energy export (-R)- rate 1",28));
		channelRecords.add(buildChanneldataMap("3#1.0.4.8.2.255#2","Reactive energy export (-R)- rate 2",29));
		channelRecords.add(buildChanneldataMap("3#1.0.4.8.3.255#2","Reactive energy export (-R)- rate 3",30));
		channelRecords.add(buildChanneldataMap("3#1.0.4.8.4.255#2","Reactive energy export (-R)- rate 4",31));
		
		channelRecords.add(buildChanneldataMap("4#1.0.1.6.0.255#2","Active maximum demand import (+A)",32));
		channelRecords.add(buildChanneldataMap("4#1.0.1.6.0.255#5","Capture time - Active maximum demand import (+A)",33));
		channelRecords.add(buildChanneldataMap("4#1.0.1.6.1.255#2","Active maximum demand import (+A) - rate1",34));
		channelRecords.add(buildChanneldataMap("4#1.0.1.6.1.255#5","Capture time - Active maximum demand import (+A) - rate1",35));
		channelRecords.add(buildChanneldataMap("4#1.0.1.6.2.255#2","Active maximum demand import (+A) - rate2",36));
		channelRecords.add(buildChanneldataMap("4#1.0.1.6.2.255#5","Capture time - Active maximum demand import (+A) - rate2",37));
		channelRecords.add(buildChanneldataMap("4#1.0.1.6.3.255#2","Active maximum demand import (+A) - rate3",38));
		channelRecords.add(buildChanneldataMap("4#1.0.1.6.3.255#5","Capture time - Active maximum demand import (+A) - rate3",39));
		channelRecords.add(buildChanneldataMap("4#1.0.1.6.4.255#2","Active maximum demand import (+A) - rate4",40));
		channelRecords.add(buildChanneldataMap("4#1.0.1.6.4.255#5","Capture time - Active maximum demand import (+A) - rate4",41));
		
		channelRecords.add(buildChanneldataMap("4#1.0.2.6.0.255#2","Active  maximum demand export (-A)",42));
		channelRecords.add(buildChanneldataMap("4#1.0.2.6.0.255#5","Capture time - Active maximum demand export (-A)",43));
		channelRecords.add(buildChanneldataMap("4#1.0.2.6.1.255#2","Active  maximum demand export (-A) - rate1",44));
		channelRecords.add(buildChanneldataMap("4#1.0.2.6.1.255#5","Capture time - Active maximum demand export (-A) - rate1",45));
		channelRecords.add(buildChanneldataMap("4#1.0.2.6.2.255#2","Active  maximum demand export (-A) - rate2",46));
		channelRecords.add(buildChanneldataMap("4#1.0.2.6.2.255#5","Capture time - Active maximum demand export (-A) - rate2",47));
		channelRecords.add(buildChanneldataMap("4#1.0.2.6.3.255#2","Active  maximum demand export (-A) - rate3",48));
		channelRecords.add(buildChanneldataMap("4#1.0.2.6.3.255#5","Capture time - Active maximum demand export (-A) - rate3",49));
		channelRecords.add(buildChanneldataMap("4#1.0.2.6.4.255#2","Active  maximum demand export (-A) - rate4",50));
		channelRecords.add(buildChanneldataMap("4#1.0.2.6.4.255#5","Capture time - Active maximum demand export (-A) - rate4",51));
		
		channelRecords.add(buildChanneldataMap("4#1.0.3.6.0.255#2","Reactive maximum demand import (+R)",52));
		channelRecords.add(buildChanneldataMap("4#1.0.3.6.0.255#5","Capture time - Reactive maximum demand import (+R)",53));
		channelRecords.add(buildChanneldataMap("4#1.0.3.6.1.255#2","Reactive maximum demand import (+R) - rate1",54));
		channelRecords.add(buildChanneldataMap("4#1.0.3.6.1.255#5","Capture time - Reactive maximum demand import (+R) - rate1",55));
		channelRecords.add(buildChanneldataMap("4#1.0.3.6.2.255#2","Reactive maximum demand import (+R) - rate2",56));
		channelRecords.add(buildChanneldataMap("4#1.0.3.6.2.255#5","Capture time - Reactive maximum demand import (+R) - rate2",57));
		channelRecords.add(buildChanneldataMap("4#1.0.3.6.3.255#2","Reactive maximum demand import (+R) - rate3",58));
		channelRecords.add(buildChanneldataMap("4#1.0.3.6.3.255#5","Capture time - Reactive maximum demand import (+R) - rate3",59));
		channelRecords.add(buildChanneldataMap("4#1.0.3.6.4.255#2","Reactive maximum demand import (+R) - rate4",60));
		channelRecords.add(buildChanneldataMap("4#1.0.3.6.4.255#5","Capture time - Reactive maximum demand import (+R) - rate4",61));
		
		channelRecords.add(buildChanneldataMap("4#1.0.4.6.0.255#2","Reactive maximum demand export (-R)",62));
		channelRecords.add(buildChanneldataMap("4#1.0.4.6.0.255#5","Capture time - Reactive maximum demand export (-R)",63));
		channelRecords.add(buildChanneldataMap("4#1.0.4.6.1.255#2","Reactive maximum demand export (-R) - rate1",64));
		channelRecords.add(buildChanneldataMap("4#1.0.4.6.1.255#5","Capture time - Reactive maximum demand export (-R) - rate1",65));
		channelRecords.add(buildChanneldataMap("4#1.0.4.6.2.255#2","Reactive maximum demand export (-R) - rate2",66));
		channelRecords.add(buildChanneldataMap("4#1.0.4.6.2.255#5","Capture time - Reactive maximum demand export (-R) - rate2",67));
		channelRecords.add(buildChanneldataMap("4#1.0.4.6.3.255#2","Reactive maximum demand export (-R) - rate3",68));
		channelRecords.add(buildChanneldataMap("4#1.0.4.6.3.255#5","Capture time - Reactive maximum demand export (-R) - rate3",69));
		channelRecords.add(buildChanneldataMap("4#1.0.4.6.4.255#2","Reactive maximum demand export (-R) - rate4",70));
		channelRecords.add(buildChanneldataMap("4#1.0.4.6.4.255#5","Capture time - Reactive maximum demand export (-R) - rate4",71));
		
		channelRecords.add(buildChanneldataMap("3#1.0.32.7.0.255#2","Instantaneous voltage L1",72));
		channelRecords.add(buildChanneldataMap("3#1.0.52.7.0.255#2","Instantaneous voltage L2",73));
		channelRecords.add(buildChanneldataMap("3#1.0.72.7.0.255#2","Instantaneous voltage L3",74));
		
		channelRecords.add(buildChanneldataMap("3#1.0.31.7.0.255#2","Instantaneous current L1",75));
		channelRecords.add(buildChanneldataMap("3#1.0.51.7.0.255#2","Instantaneous current L2",76));
		channelRecords.add(buildChanneldataMap("3#1.0.71.7.0.255#2","Instantaneous current L3",77));
		
		channelRecords.add(buildChanneldataMap("3#1.0.15.8.0.255#2","Active energy combined total (|+A|+|-A|)",78));
		channelRecords.add(buildChanneldataMap("3#1.0.15.7.0.255#2","Instantaneous active power (|+A|+|-A|)",79));
		channelRecords.add(buildChanneldataMap("3#1.0.35.7.0.255#2","Instantaneous active power (|+A|+|-A|) L1",80));
		channelRecords.add(buildChanneldataMap("3#1.0.55.7.0.255#2","Instantaneous active power (|+A|+|-A|) L2",81));
		channelRecords.add(buildChanneldataMap("3#1.0.75.7.0.255#2","Instantaneous active power (|+A|+|-A|) L3",82));
		
		channelRecords.add(buildChanneldataMap("3#1.0.32.24.0.255#2","Average voltage L1",83));
		channelRecords.add(buildChanneldataMap("3#1.0.52.24.0.255#2","Average voltage L2",84));
		channelRecords.add(buildChanneldataMap("3#1.0.72.24.0.255#2","Average voltage L3",85));
		
		channelRecords.add(buildChanneldataMap("3#1.0.31.24.0.255#2","Average current L1",86));
		channelRecords.add(buildChanneldataMap("3#1.0.51.24.0.255#2","Average current L2",87));
		channelRecords.add(buildChanneldataMap("3#1.0.71.24.0.255#2","Average current L3",88));
		
		channelRecords.add(buildChanneldataMap("3#1.0.1.7.0.255#2","Instantaneous active power import(+A)",89));
		channelRecords.add(buildChanneldataMap("3#1.0.21.7.0.255#2","Instantaneous active power import(+A) L1",90));
		channelRecords.add(buildChanneldataMap("3#1.0.41.7.0.255#2","Instantaneous active power import(+A) L2",91));
		channelRecords.add(buildChanneldataMap("3#1.0.61.7.0.255#2","Instantaneous active power import(+A) L3",92));
		
		channelRecords.add(buildChanneldataMap("3#1.0.2.7.0.255#2","Instantaneous active power export(-A)",93));
		channelRecords.add(buildChanneldataMap("3#1.0.22.7.0.255#2","Instantaneous active power export(-A) L1",94));
		channelRecords.add(buildChanneldataMap("3#1.0.42.7.0.255#2","Instantaneous active power export(-A) L2",95));
		channelRecords.add(buildChanneldataMap("3#1.0.62.7.0.255#2","Instantaneous active power export(-A) L3",96));
		
		channelRecords.add(buildChanneldataMap("3#1.0.3.7.0.255#2","Instantaneous reactive power import(+R)",97));
		channelRecords.add(buildChanneldataMap("3#1.0.23.7.0.255#2","Instantaneous reactive power import(+R) L1",98));
		channelRecords.add(buildChanneldataMap("3#1.0.43.7.0.255#2","Instantaneous reactive power import(+R) L2",99));
		channelRecords.add(buildChanneldataMap("3#1.0.63.7.0.255#2","Instantaneous reactive power import(+R) L3",100));
		
		channelRecords.add(buildChanneldataMap("3#1.0.4.7.0.255#2","Instantaneous reactive power export(-R)",101));
		channelRecords.add(buildChanneldataMap("3#1.0.24.7.0.255#2","Instantaneous reactive power export(-R) L1",102));
		channelRecords.add(buildChanneldataMap("3#1.0.44.7.0.255#2","Instantaneous reactive power export(-R) L2",103));
		channelRecords.add(buildChanneldataMap("3#1.0.64.7.0.255#2","Instantaneous reactive power export(-R) L3",104));
		
		channelRecords.add(buildChanneldataMap("3#1.0.9.7.0.255#2","Instantaneous apparent power import(+VA)",105));
		channelRecords.add(buildChanneldataMap("3#1.0.29.7.0.255#2","Instantaneous apparent power import(+VA) L1",106));
		channelRecords.add(buildChanneldataMap("3#1.0.49.7.0.255#2","Instantaneous apparent power import(+VA) L2",107));
		channelRecords.add(buildChanneldataMap("3#1.0.69.7.0.255#2","Instantaneous apparent power import(+VA) L3",108));
		
		channelRecords.add(buildChanneldataMap("3#1.0.10.7.0.255#2","Instantaneous apparent power export(-VA)",109));
		channelRecords.add(buildChanneldataMap("3#1.0.30.7.0.255#2","Instantaneous apparent power export(-VA) L1",110));
		channelRecords.add(buildChanneldataMap("3#1.0.50.7.0.255#2","Instantaneous apparent power export(-VA) L2",111));
		channelRecords.add(buildChanneldataMap("3#1.0.70.7.0.255#2","Instantaneous apparent power export(-VA) L3",112));
		
		channelRecords.add(buildChanneldataMap("3#1.0.13.7.0.255#2","Instantaneous power factor(+A/+VA)",113));
		channelRecords.add(buildChanneldataMap("3#1.0.33.7.0.255#2","Instantaneous power factor L1",114));
		channelRecords.add(buildChanneldataMap("3#1.0.53.7.0.255#2","Instantaneous power factor L2",115));
		channelRecords.add(buildChanneldataMap("3#1.0.73.7.0.255#2","Instantaneous power factor L3",116));

		channelRecords.add(buildChanneldataMap("3#1.0.14.7.0.255#2","Instantaneous net frequency; any phase",117));
		
		
		j.setObj(channelRecords);
		
		return j;
	}
	
	private Map<String, Object> buildChanneldataMap (String channelId,String channelName,int order) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("channelId", channelId);
		map.put("channelName", channelName);
		map.put("order", order);
		return map;
	}
	
	/**
     * 从左表选中dataitem添加到右表中
     * 过滤条件：
     * 			不能添加重复数据；
     * 			以profileId为主键，添加数据保存到Redis中，最后统一存入数据库
     * @param id
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "addChannelList")
    public AjaxJson addChannelList(String channelId, String selectedDataChannelList, HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        int judge = 0;
        try {
            JSONArray jsonArray = JSONArray.fromObject(selectedDataChannelList);
            SysUser su = TokenManager.getToken();
            if(jsonArray.size() > 0){
            	/*
            	 * 判断是否存在重复数据
            	 */
            	for (int i = 0; i < jsonArray.size(); i++) {
            		JSONObject o = jsonArray.getJSONObject(i);
            		if(o.get("channelId").equals(channelId)){
            			judge += 1;
            		}
            		
            		Map<String, Object> di = new HashMap<>();
            		di.put("channelId", o.get("channelId").toString());
            		di.put("channelName", o.get("channelName").toString());
            		di.put("order", o.get("order").toString());
            		
            		list.add(di);
            	}
            	if(judge > 1){
            		j.setSuccess(false);
                    j.setMsg("Repeat the data, please rechoose!");
                    return j;
            	}
            	//保存增加后的data channel list 到Redis
            	Map<String, Object> value = new HashMap<>();
				value.put("listDi", list);
				JedisUtils.setObjectMap(su.getId() + "autoLCDList", value, 0);
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 删除Profile Dataitem List，保存到Redis，最后统一存入数据库
     * 过滤条件：
     * 			以profileId为主键，添加数据保存到Redis中
     * @param id
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "deleteChannelList")
    public AjaxJson deleteChannelList(String channelId, String selectedDataChannelList, HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        try {
            JSONArray jsonArray = JSONArray.fromObject(selectedDataChannelList);
            SysUser su = TokenManager.getToken();
            
            if(jsonArray.size() > 0){
            	/*
            	 * 判断
            	 */
            	for (int i = 0; i < jsonArray.size(); i++) {
            		JSONObject o = jsonArray.getJSONObject(i);
            		//不保存需要删除的数据
            		if(channelId.equals(o.get("channelId").toString())){
            			continue;
            		}
            		Map<String, Object> di = new HashMap<>();
            		di.put("channelId", o.get("channelId").toString());
            		di.put("channelName", o.get("channelName").toString());
            		di.put("order", o.get("order").toString());
            		
            		list.add(di);
            	}
            }
            //保存增加后的data channel list 到Redis
            Map<String, Object> value = new HashMap<>();
			value.put("listDi", list);
			JedisUtils.setObjectMap(su.getId() + "autoLCDList", value, 0);
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 上移或者下移Channels 数据
     * @param id
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "upOrDownChannels")
    public AjaxJson upOrDownChannels(String channelId, 
    		String type, String rowNum, String selectedDataChannelList, HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        int rowNumber = Integer.parseInt(rowNum);
        try {
            JSONArray jsonArray = JSONArray.fromObject(selectedDataChannelList);
            SysUser su = TokenManager.getToken();
            
            if(jsonArray.size() > 0){
            	for (int i = 0; i < jsonArray.size(); i++) {
            		JSONObject o = jsonArray.getJSONObject(i);
            		
            		Map<String, Object> di = new HashMap<>();
            		di.put("channelId", o.get("channelId").toString());
            		di.put("channelName", o.get("channelName").toString());
            		di.put("order",String.valueOf(i+1));
            		list.add(di);
            	}
            	if("up".equals(type)){
            		int upRowNumber = rowNumber - 1;
            		Collections.swap(list, rowNumber, upRowNumber);		//数据调换
            		list.get(upRowNumber).put("order",String.valueOf(rowNumber));		//更改sortId
            		list.get(rowNumber).put("order",String.valueOf(rowNumber + 1));		//更改sortId
            		j.setObj(list);
            	}else{
            		int downRowNumber = rowNumber + 1;
            		Collections.swap(list, rowNumber, downRowNumber);	//数据调换
            		list.get(rowNumber).put("order",String.valueOf(rowNumber + 1));		//更改sortId
            		list.get(downRowNumber).put("order",String.valueOf(downRowNumber + 1));		//更改sortId
            		j.setObj(list);
            	}
            	
            	//保存增加后的data channel list 到Redis
            	Map<String, Object> value = new HashMap<>();
    			value.put("listDi", list);
    			JedisUtils.setObjectMap(su.getId() + "autoLCDList", value, 0);
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg("Abnormal operation");
        }
        return j;
    }
	
	private List<Map<String, Object>> buildChannelZtreeHY() {
		
		List<Map<String, Object>> list = new ArrayList<>();
		
		// IDs & control information
		Map<String, Object> m1 = new HashMap<String, Object>();
		m1.put("id", "1");
		m1.put("name", "IDs & control information");
		m1.put("parent", "");
		m1.put("expanded", true);
		List<Map<String, Object>> list1 = new ArrayList<>();
		addItemToList(list1,"1#1.0.0.2.0.255#2","Active firmware identity","1");
		addItemToList(list1,"1#0.0.96.55.30.255#2","Hardware version","1");
		addItemToList(list1,"1#0.0.96.1.0.255#2","Device ID","1");
		addItemToList(list1,"1#0.0.96.51.4.255#2","All segments","1");
		m1.put("list", list1);
		list.add(m1);
		
		// Time of Use
		Map<String, Object> m2 = new HashMap<String, Object>();
		m2.put("id", "2");
		m2.put("name", "Time of Use");
		m2.put("parent", "");
		m2.put("expanded", true);
		/*
		 	Local time 	Class Id 1	Logical name 1-0:0.9.1.255
			Local date	Class Id 1	Logical name 1-0:0.9.2.255
		 * */
		List<Map<String, Object>> list2 = new ArrayList<>();
		addItemToList(list2,"1#1.0.0.9.1.255#2","Local time","2");
		addItemToList(list2,"1#1.0.0.9.2.255#2","Local date","2");
		m2.put("list", list2);
		list.add(m2);
		
		
		// Limiter Setup
		Map<String, Object> m3 = new HashMap<String, Object>();
		m3.put("id", "3");
		m3.put("name", "Limiter Setup");
		m3.put("parent", "");
		m3.put("expanded", true);
		/*
		 	Limiter 	Class Id 71	Logical name 0-0:17.0.0.255
		 */
		List<Map<String, Object>> list3 = new ArrayList<>();
		addItemToList(list3,"71#0.0.17.0.0.255#2","Logical name","3");
		m3.put("list", list3);
		list.add(m3);
		
		// Energy register
		Map<String, Object> m4 = new HashMap<String, Object>();
		m4.put("id", "4");
		m4.put("name", "Energy register");
		m4.put("parent", "");
		m4.put("expanded", true);
		/*
		 	Active energy import (+A)			Class Id 3	Logical name 1-0:1.8.0.255
			Active energy import (+A)- rate 1	Class Id 3	Logical name 1-0:1.8.1.255
			Active energy import (+A)- rate 2	Class Id 3	Logical name 1-0:1.8.2.255
			Active energy import (+A)- rate 3	Class Id 3	Logical name 1-0:1.8.3.255
			Active energy import (+A)- rate 4	Class Id 3	Logical name 1-0:1.8.4.255
			Active energy export (-A)			Class Id 3	Logical name 1-0:2.8.0.255
			Active energy export (-A)- rate 1	Class Id 3	Logical name 1-0:2.8.1.255
			Active energy export (-A)- rate 2	Class Id 3	Logical name 1-0:2.8.2.255
			Active energy export (-A)- rate 3	Class Id 3	Logical name 1-0:2.8.3.255
			Active energy export (-A)- rate 4	Class Id 3	Logical name 1-0:2.8.4.255
			Reactive energy import (+R)			Class Id 3	Logical name 1-0:3.8.0.255
			Reactive energy import (+R)- rate 1	Class Id 3	Logical name 1-0:3.8.1.255
			Reactive energy import (+R)- rate 2	Class Id 3	Logical name 1-0:3.8.2.255
			Reactive energy import (+R)- rate 3	Class Id 3	Logical name 1-0:3.8.3.255
			Reactive energy import (+R)- rate 4	Class Id 3	Logical name 1-0:3.8.4.255
			Reactive energy export (-R)			Class Id 3	Logical name 1-0:4.8.0.255
			Reactive energy export (-R)- rate 1	Class Id 3	Logical name 1-0:4.8.1.255
			Reactive energy export (-R)- rate 2	Class Id 3	Logical name 1-0:4.8.2.255
			Reactive energy export (-R)- rate 3	Class Id 3	Logical name 1-0:4.8.3.255
			Reactive energy export (-R)- rate 4	Class Id 3	Logical name 1-0:4.8.4.255
			Available credit					Class Id 3	Logical name 0-0:96.80.6.255
		 */
		List<Map<String, Object>> list4 = new ArrayList<>();
		addItemToList(list4,"3#1.0.1.8.0.255#2","Active energy import (+A)","4");
		addItemToList(list4,"3#1.0.1.8.1.255#2","Active energy import (+A)- rate 1","4");
		addItemToList(list4,"3#1.0.1.8.2.255#2","Active energy import (+A)- rate 2","4");
		addItemToList(list4,"3#1.0.1.8.3.255#2","Active energy import (+A)- rate 3","4");
		addItemToList(list4,"3#1.0.1.8.4.255#2","Active energy import (+A)- rate 4","4");
		addItemToList(list4,"3#1.0.2.8.0.255#2","Active energy export (-A)","4");
		addItemToList(list4,"3#1.0.2.8.1.255#2","Active energy export (-A)- rate 1","4");
		addItemToList(list4,"3#1.0.2.8.2.255#2","Active energy export (-A)- rate 2","4");
		addItemToList(list4,"3#1.0.2.8.3.255#2","Active energy export (-A)- rate 3","4");
		addItemToList(list4,"3#1.0.2.8.4.255#2","Active energy export (-A)- rate 4","4");
		addItemToList(list4,"3#1.0.3.8.0.255#2","Reactive energy import (+R)","4");
		addItemToList(list4,"3#1.0.3.8.1.255#2","Reactive energy import (+R)- rate 1","4");
		addItemToList(list4,"3#1.0.3.8.2.255#2","Reactive energy import (+R)- rate 2","4");
		addItemToList(list4,"3#1.0.3.8.3.255#2","Reactive energy import (+R)- rate 3","4");
		addItemToList(list4,"3#1.0.3.8.4.255#2","Reactive energy import (+R)- rate 4","4");
		addItemToList(list4,"3#1.0.4.8.0.255#2","Reactive energy export (-R)","4");
		addItemToList(list4,"3#1.0.4.8.1.255#2","Reactive energy export (-R)- rate 1","4");
		addItemToList(list4,"3#1.0.4.8.2.255#2","Reactive energy export (-R)- rate 2","4");
		addItemToList(list4,"3#1.0.4.8.3.255#2","Reactive energy export (-R)- rate 3","4");
		addItemToList(list4,"3#1.0.4.8.4.255#2","Reactive energy export (-R)- rate 4","4");
		m4.put("list", list4);
		list.add(m4);
		
		// Maximum Demand Register
		Map<String, Object> m5 = new HashMap<String, Object>();
		m5.put("id", "5");
		m5.put("name", "Maximum Demand Register");
		m5.put("parent", "");
		m5.put("expanded", true);
		/*
		 	Active maximum demand import (+A)							Class Id 4	2	Logical name 1-0:1.6.0.255
			Capture time - Active maximum demand import (+A)			Class Id 4	5	Logical name 1-0:1.6.0.255
			Active maximum demand import (+A) - rate1					Class Id 4	2	Logical name 1-0:1.6.1.255
			Capture time - Active maximum demand import (+A) - rate1	Class Id 4	5	Logical name 1-0:1.6.1.255
			Active maximum demand import (+A) - rate2					Class Id 4	2	Logical name 1-0:1.6.2.255
			Capture time - Active maximum demand import (+A) - rate2	Class Id 4	5	Logical name 1-0:1.6.2.255
			Active maximum demand import (+A) - rate3					Class Id 4	2	Logical name 1-0:1.6.3.255
			Capture time - Active maximum demand import (+A) - rate3	Class Id 4	5	Logical name 1-0:1.6.3.255
			Active maximum demand import (+A) - rate4					Class Id 4	2	Logical name 1-0:1.6.4.255
			Capture time - Active maximum demand import (+A) - rate4	Class Id 4	5	Logical name 1-0:1.6.4.255
			Active  maximum demand export (-A)							Class Id 4	2	Logical name 1-0:2.6.0.255
			Capture time - Active maximum demand export (-A)			Class Id 4	5	Logical name 1-0:2.6.0.255
			Active  maximum demand export (-A) - rate1					Class Id 4	2	Logical name 1-0:2.6.1.255
			Capture time - Active maximum demand export (-A) - rate1	Class Id 4	5	Logical name 1-0:2.6.1.255
			Active  maximum demand export (-A) - rate2					Class Id 4	2	Logical name 1-0:2.6.2.255
			Capture time - Active maximum demand export (-A) - rate2	Class Id 4	5	Logical name 1-0:2.6.2.255
			Active  maximum demand export (-A) - rate3					Class Id 4	2	Logical name 1-0:2.6.3.255
			Capture time - Active maximum demand export (-A) - rate3	Class Id 4	5	Logical name 1-0:2.6.3.255
			Active  maximum demand export (-A) - rate4					Class Id 4	2	Logical name 1-0:2.6.4.255
			Capture time - Active maximum demand export (-A) - rate4	Class Id 4	5	Logical name 1-0:2.6.4.255
			Reactive maximum demand import (+R)							Class Id 4	2	Logical name 1-0:3.6.0.255
			Capture time - Reactive maximum demand import (+R)			Class Id 4	5	Logical name 1-0:3.6.0.255
			Reactive maximum demand import (+R) - rate1					Class Id 4	2	Logical name 1-0:3.6.1.255
			Capture time - Reactive maximum demand import (+R) - rate1	Class Id 4	5	Logical name 1-0:3.6.1.255
			Reactive maximum demand import (+R) - rate2					Class Id 4	2	Logical name 1-0:3.6.2.255
			Capture time - Reactive maximum demand import (+R) - rate2	Class Id 4	5	Logical name 1-0:3.6.2.255
			Reactive maximum demand import (+R) - rate3					Class Id 4	2	Logical name 1-0:3.6.3.255
			Capture time - Reactive maximum demand import (+R) - rate3	Class Id 4	5	Logical name 1-0:3.6.3.255
			Reactive maximum demand import (+R) - rate4					Class Id 4	2	Logical name 1-0:3.6.4.255
			Capture time - Reactive maximum demand import (+R) - rate4	Class Id 4	5	Logical name 1-0:3.6.4.255
			Reactive maximum demand export (-R)							Class Id 4	2	Logical name 1-0:4.6.0.255
			Capture time - Reactive maximum demand export (-R)			Class Id 4	5	Logical name 1-0:4.6.0.255
			Reactive maximum demand export (-R) - rate1					Class Id 4	2	Logical name 1-0:4.6.1.255
			Capture time - Reactive maximum demand export (-R) - rate1	Class Id 4	5	Logical name 1-0:4.6.1.255
			Reactive maximum demand export (-R) - rate2					Class Id 4	2	Logical name 1-0:4.6.2.255
			Capture time - Reactive maximum demand export (-R) - rate2	Class Id 4	5	Logical name 1-0:4.6.2.255
			Reactive maximum demand export (-R) - rate3					Class Id 4	2	Logical name 1-0:4.6.3.255
			Capture time - Reactive maximum demand export (-R) - rate3	Class Id 4	5	Logical name 1-0:4.6.3.255
			Reactive maximum demand export (-R) - rate4					Class Id 4	2	Logical name 1-0:4.6.4.255
			Capture time - Reactive maximum demand export (-R) - rate4	Class Id 4	5	Logical name 1-0:4.6.4.255
		 */
		List<Map<String, Object>> list5 = new ArrayList<>();
		addItemToList(list5,"4#1.0.1.6.0.255#2","Active maximum demand import (+A)","5");
		addItemToList(list5,"4#1.0.1.6.0.255#5","Capture time - Active maximum demand import (+A)","5");
		addItemToList(list5,"4#1.0.1.6.1.255#2","Active maximum demand import (+A) - rate1","5");
		addItemToList(list5,"4#1.0.1.6.1.255#5","Capture time - Active maximum demand import (+A) - rate1","5");
		addItemToList(list5,"4#1.0.1.6.2.255#2","Active maximum demand import (+A) - rate2","5");
		addItemToList(list5,"4#1.0.1.6.2.255#5","Capture time - Active maximum demand import (+A) - rate2","5");
		addItemToList(list5,"4#1.0.1.6.3.255#2","Active maximum demand import (+A) - rate3","5");
		addItemToList(list5,"4#1.0.1.6.3.255#5","Capture time - Active maximum demand import (+A) - rate3","5");
		addItemToList(list5,"4#1.0.1.6.4.255#2","Active maximum demand import (+A) - rate4","5");
		addItemToList(list5,"4#1.0.1.6.4.255#5","Capture time - Active maximum demand import (+A) - rate4","5");
		
		addItemToList(list5,"4#1.0.2.6.0.255#2","Active  maximum demand export (-A)","5");
		addItemToList(list5,"4#1.0.2.6.0.255#5","Capture time - Active maximum demand export (-A)","5");
		addItemToList(list5,"4#1.0.2.6.1.255#2","Active  maximum demand export (-A) - rate1","5");
		addItemToList(list5,"4#1.0.2.6.1.255#5","Capture time - Active maximum demand export (-A) - rate1","5");
		addItemToList(list5,"4#1.0.2.6.2.255#2","Active  maximum demand export (-A) - rate2","5");
		addItemToList(list5,"4#1.0.2.6.2.255#5","Capture time - Active maximum demand export (-A) - rate2","5");
		addItemToList(list5,"4#1.0.2.6.3.255#2","Active  maximum demand export (-A) - rate3","5");
		addItemToList(list5,"4#1.0.2.6.3.255#5","Capture time - Active maximum demand export (-A) - rate3","5");
		addItemToList(list5,"4#1.0.2.6.4.255#2","Active  maximum demand export (-A) - rate4","5");
		addItemToList(list5,"4#1.0.2.6.4.255#5","Capture time - Active maximum demand export (-A) - rate4","5");
		
		addItemToList(list5,"4#1.0.3.6.0.255#2","Reactive maximum demand import (+R)","5");
		addItemToList(list5,"4#1.0.3.6.0.255#5","Capture time - Reactive maximum demand import (+R)","5");
		addItemToList(list5,"4#1.0.3.6.1.255#2","Reactive maximum demand import (+R) - rate1","5");
		addItemToList(list5,"4#1.0.3.6.1.255#5","Capture time - Reactive maximum demand import (+R) - rate1","5");
		addItemToList(list5,"4#1.0.3.6.2.255#2","Reactive maximum demand import (+R) - rate2","5");
		addItemToList(list5,"4#1.0.3.6.2.255#5","Capture time - Reactive maximum demand import (+R) - rate2","5");
		addItemToList(list5,"4#1.0.3.6.3.255#2","Reactive maximum demand import (+R) - rate3","5");
		addItemToList(list5,"4#1.0.3.6.3.255#5","Capture time - Reactive maximum demand import (+R) - rate3","5");
		addItemToList(list5,"4#1.0.3.6.4.255#2","Reactive maximum demand import (+R) - rate4","5");
		addItemToList(list5,"4#1.0.3.6.4.255#5","Capture time - Reactive maximum demand import (+R) - rate4","5");
		
		addItemToList(list5,"4#1.0.4.6.0.255#2","Reactive maximum demand export (-R)","5");
		addItemToList(list5,"4#1.0.4.6.0.255#5","Capture time - Reactive maximum demand export (-R)","5");
		addItemToList(list5,"4#1.0.4.6.1.255#2","Reactive maximum demand export (-R) - rate1","5");
		addItemToList(list5,"4#1.0.4.6.1.255#5","Capture time - Reactive maximum demand export (-R) - rate1","5");
		addItemToList(list5,"4#1.0.4.6.2.255#2","Reactive maximum demand export (-R) - rate2","5");
		addItemToList(list5,"4#1.0.4.6.2.255#5","Capture time - Reactive maximum demand export (-R) - rate2","5");
		addItemToList(list5,"4#1.0.4.6.3.255#2","Reactive maximum demand export (-R) - rate3","5");
		addItemToList(list5,"4#1.0.4.6.3.255#5","Capture time - Reactive maximum demand export (-R) - rate3","5");
		addItemToList(list5,"4#1.0.4.6.4.255#2","Reactive maximum demand export (-R) - rate4","5");
		addItemToList(list5,"4#1.0.4.6.4.255#5","Capture time - Reactive maximum demand export (-R) - rate4","5");
		
		m5.put("list", list5);
		list.add(m5);
		
		// Instantaneous
		Map<String, Object> m6 = new HashMap<String, Object>();
		m6.put("id", "6");
		m6.put("name", "Instantaneous");
		m6.put("parent", "");
		m6.put("expanded", true);
		/*
		 	Instantaneous voltage L1					Class Id 3	Logical name: 1-0:32.7.0.255
			Instantaneous current L1					Class Id 3	Logical name: 1-0:31.7.0.255
			Instantaneous current L2					Class Id 3	Logical name: 1-0:51.7.0.255
			Average voltage L1							Class Id 3	Logical name: 1-0:32.24.0.255
			Average current L1							Class Id 3	Logical name: 1-0:31.24.0.255
			Average current L2							Class Id 3	Logical name: 1-0:51.24.0.255
			Instantaneous active power import(+A)		Class Id 3	Logical name: 1-0:1.7.0.255
			Instantaneous active power import(+A) L1	Class Id 3	Logical name: 1-0:21.7.0.255
			Instantaneous active power import(+A) L2	Class Id 3	Logical name: 1-0:41.7.0.255
			Instantaneous active power export(-A)		Class Id 3	Logical name: 1-0:2.7.0.255
			Instantaneous active power export(-A) L1	Class Id 3	Logical name: 1-0:22.7.0.255
			Instantaneous active power export(-A) L2	Class Id 3	Logical name: 1-0:42.7.0.255
			Instantaneous reactive power import(+R)		Class Id 3	Logical name: 1-0:3.7.0.255
			Instantaneous reactive power import(+R) L1	Class Id 3	Logical name: 1-0:23.7.0.255
			Instantaneous reactive power import(+R) L2	Class Id 3	Logical name: 1-0:43.7.0.255
			Instantaneous reactive power export(-R)		Class Id 3	Logical name: 1-0:4.7.0.255
			Instantaneous reactive power export(-R) L1	Class Id 3	Logical name: 1-0:24.7.0.255
			Instantaneous reactive power export(-R) L2	Class Id 3	Logical name: 1-0:44.7.0.255
			Instantaneous apparent power import(+VA)	Class Id 3	Logical name: 1-0:9.7.0.255
			Instantaneous apparent power import(+VA) L1	Class Id 3	Logical name: 1-0:29.7.0.255
			Instantaneous apparent power import(+VA) L2	Class Id 3	Logical name: 1-0:49.7.0.255
			Instantaneous apparent power export(-VA)	Class Id 3	Logical name: 1-0:10.7.0.255
			Instantaneous apparent power export(-VA) L1	Class Id 3	Logical name: 1-0:30.7.0.255
			Instantaneous apparent power export(-VA) L2	Class Id 3	Logical name: 1-0:50.7.0.255
			Instantaneous power factor(+A/+VA)			Class Id 3	Logical name: 1-0:13.7.0.255
			Instantaneous net frequency; any phase		Class Id 3	Logical name: 1-0:14.7.0.255
		 */
		List<Map<String, Object>> list6 = new ArrayList<>();
		addItemToList(list6,"3#1.0.32.7.0.255#2","Instantaneous voltage L1","6");
		addItemToList(list6,"3#1.0.31.7.0.255#2","Instantaneous current L1","6");
		addItemToList(list6,"3#1.0.51.7.0.255#2","Instantaneous current L2","6");
		addItemToList(list6,"3#1.0.32.24.0.255#2","Average voltage L1","6");
		addItemToList(list6,"3#1.0.31.24.0.255#2","Average current L1","6");
		addItemToList(list6,"3#1.0.51.24.0.255#2","Average current L2","6");
		addItemToList(list6,"3#1.0.1.7.0.255#2","Instantaneous active power import(+A)","6");
		addItemToList(list6,"3#1.0.21.7.0.255#2","Instantaneous active power import(+A) L1","6");
		addItemToList(list6,"3#1.0.41.7.0.255#2","Instantaneous active power import(+A) L2","6");
		addItemToList(list6,"3#1.0.2.7.0.255#2","Instantaneous active power export(-A)","6");
		addItemToList(list6,"3#1.0.22.7.0.255#2","Instantaneous active power export(-A) L1","6");
		addItemToList(list6,"3#1.0.42.7.0.255#2","Instantaneous active power export(-A) L2","6");
		addItemToList(list6,"3#1.0.3.7.0.255#2","Instantaneous reactive power import(+R)","6");
		addItemToList(list6,"3#1.0.23.7.0.255#2","Instantaneous reactive power import(+R) L1","6");
		addItemToList(list6,"3#1.0.43.7.0.255#2","Instantaneous reactive power import(+R) L2","6");
		addItemToList(list6,"3#1.0.4.7.0.255#2","Instantaneous reactive power export(-R)","6");
		addItemToList(list6,"3#1.0.24.7.0.255#2","Instantaneous reactive power export(-R) L1","6");
		addItemToList(list6,"3#1.0.44.7.0.255#2","Instantaneous reactive power export(-R) L2","6");
		addItemToList(list6,"3#1.0.9.7.0.255#2","Instantaneous apparent power import(+VA)","6");
		addItemToList(list6,"3#1.0.29.7.0.255#2","Instantaneous apparent power import(+VA) L1","6");
		addItemToList(list6,"3#1.0.49.7.0.255#2","Instantaneous apparent power import(+VA) L2","6");
		addItemToList(list6,"3#1.0.10.7.0.255#2","Instantaneous apparent power export(-VA)","6");
		addItemToList(list6,"3#1.0.30.7.0.255#2","Instantaneous apparent power export(-VA) L1","6");
		addItemToList(list6,"3#1.0.50.7.0.255#2","Instantaneous apparent power export(-VA) L2","6");
		addItemToList(list6,"3#1.0.13.7.0.255#2","Instantaneous power factor(+A/+VA)","6");
		addItemToList(list6,"3#1.0.14.7.0.255#2","Instantaneous net frequency; any phase","6");
		m6.put("list", list6);
		list.add(m6);
		
		
		return list;
	}
	
	private List<Map<String, Object>> buildLimitMonitorValueHY() {
		List<Map<String, Object>> list = new ArrayList<>();
		
		Map<String, Object> m1 = new HashMap<String, Object>();
		m1.put("id", "1");
		m1.put("name", "Limiter Setup Capture");
		m1.put("parent", "");
		m1.put("expanded", true);
		List<Map<String, Object>> list1 = new ArrayList<>();
		addItemToList(list1,"3#1.0.12.7.0.255#2","Instantaneous voltage(any phase)","1");
		addItemToList(list1,"3#1.0.32.7.0.255#2","Instantaneous voltage L1","1");
		addItemToList(list1,"3#1.0.52.7.0.255#2","Instantaneous voltage L2","1");
		addItemToList(list1,"3#1.0.72.7.0.255#2","Instantaneous voltage L3","1");
		addItemToList(list1,"3#1.0.90.7.0.255#2","Instantaneous current(sum over all phases)","1");
		addItemToList(list1,"3#1.0.11.7.0.255#2","Instantaneous current (any phase)","1");
		addItemToList(list1,"3#1.0.31.7.0.255#2","Instantaneous current L1","1");
		addItemToList(list1,"3#1.0.51.7.0.255#2","Instantaneous current L2","1");
		addItemToList(list1,"3#1.0.71.7.0.255#2","Instantaneous current L3","1");
		addItemToList(list1,"3#1.0.132.7.0.255#2","Instantaneous active power (any phase)","1");
		addItemToList(list1,"3#1.0.15.7.0.255#2","Instantaneous active power (|+A|+|-A|)","1");
		addItemToList(list1,"3#1.0.1.7.0.255#2","Instantaneous active import power (+A)","1");
		addItemToList(list1,"3#1.0.21.7.0.255#2","Instantaneous active import power (+A) L1","1");
		addItemToList(list1,"3#1.0.41.7.0.255#2","Instantaneous active import power (+A) L2","1");
		addItemToList(list1,"3#1.0.61.7.0.255#2","Instantaneous active import power (+A) L3","1");
		addItemToList(list1,"3#1.0.2.7.0.255#2","Instantaneous active export power (-A)","1");
		addItemToList(list1,"3#1.0.22.7.0.255#2","Instantaneous active export power (-A) L1","1");
		addItemToList(list1,"3#1.0.42.7.0.255#2","Instantaneous active export power (-A) L2","1");
		addItemToList(list1,"3#1.0.62.7.0.255#2","Instantaneous active export power (-A) L3","1");
		addItemToList(list1,"3#1.0.133.7.0.255#2","Instantaneous reactive power (any phase)","1");
		addItemToList(list1,"3#1.0.3.7.0.255#2","Instantaneous reactive import power (+R)","1");
		addItemToList(list1,"3#1.0.23.7.0.255#2","Instantaneous reactive import power (+R) L1","1");
		addItemToList(list1,"3#1.0.43.7.0.255#2","Instantaneous reactive import power (+R) L2","1");
		addItemToList(list1,"3#1.0.63.7.0.255#2","Instantaneous reactive import power (+R) L3","1");
		addItemToList(list1,"3#1.0.4.7.0.255#2","Instantaneous reactive export power (-R)","1");
		addItemToList(list1,"3#1.0.24.7.0.255#2","Instantaneous reactive export power (-R) L1","1");
		addItemToList(list1,"3#1.0.44.7.0.255#2","Instantaneous reactive export power (-R) L2","1");
		addItemToList(list1,"3#1.0.64.7.0.255#2","Instantaneous reactive export power (-R) L3","1");
		addItemToList(list1,"3#1.0.134.7.0.255#2","Instantaneous apparent power (any phase)","1");
		addItemToList(list1,"5#1.0.1.24.0.255#2","Average import power (+A)","1");
		addItemToList(list1,"5#1.0.15.24.0.255#2","Average total power (|+A|+|-A|)","1");
		addItemToList(list1,"3#0.0.96.9.0.255#2","Ambient temperature","1");
		addItemToList(list1,"3#FFFFFFFFFFFF#2","No limit","1");
		m1.put("list", list1);
		list.add(m1);
		
		return list;
	}
	
	/**
	 * 获取选择显示列表树形框数据
	 * @param searchName
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getLimitMonitorValueHY")
	@ResponseBody
	public AjaxJson getLimitMonitorValueHY(String appType,String searchName,HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		ZtreeUtil jtu = new ZtreeUtil();
		jtu.setIdFname("id");
		jtu.setNameFname("name");
		jtu.setpIdFname("parent");
		jtu.setSubsetFieldName("list");
		jtu.setOpenFname("expanded");
		j.setObj(jtu.getZtreeNodeData(buildLimitMonitorValueHY()));
		return j;
	}
	
	private void addItemToList(List<Map<String, Object>> listk,String id,String name,String parent) {
		Map<String, Object> dm = new HashMap<String, Object>();
		dm.put("id",  id);
		dm.put("name", name);
		dm.put("parent", parent);
		dm.put("expanded", false);
		listk.add(dm);
	}
	
	/**
	 * 获取选择显示列表树形框数据
	 * @param searchName
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getChannelZtree376")
	@ResponseBody
	public AjaxJson getChannelZtree376(String appType,String searchName,HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		List<Map<String, Object>> list = new ArrayList<>();
		DictDataitemGroup entity=new DictDataitemGroup();
		entity.setAppType(appType);
		entity.setProtocolId("200");
		List<DictDataitemGroup> ddgList = dictDataitemGroupService.getList(entity);
		for(DictDataitemGroup g:ddgList){
			Map<String, Object> m = new HashMap<String, Object>();
			m.put("id", g.getId());
			m.put("name", g.getName());
			m.put("parent", "");
			m.put("expanded", true);
			DictDataitem dd=new DictDataitem();
			dd.put("groupId", g.getId());
			dd.put("appType", appType);
			//分组已区分 规约 故删掉
		//	dd.setProtocolId("200");
			if(StringUtil.isNotEmpty(searchName)&&g.getName().indexOf(searchName)<0){
				dd.setName(searchName);
			}
			List<DictDataitem> ddList=dictDataitemService.getList(dd);
			List<Map<String, Object>> listk = new ArrayList<>();
			for(DictDataitem d:ddList){
				Map<String, Object> dm = new HashMap<String, Object>();
				dm.put("id", d.getId());
				dm.put("name", d.getName());
				dm.put("parent", g.getId());
				dm.put("expanded", false);
				listk.add(dm);
			}
			if(StringUtil.isNotEmpty(searchName)){
				if(listk.size()>0){
					m.put("list", listk);
					m.put("expanded", true);
					list.add(m);
				}
			}else{
				if(listk.size()>0){
				m.put("list", listk);
				list.add(m);
				}
			}
		}
		ZtreeUtil jtu = new ZtreeUtil();
		jtu.setIdFname("id");
		jtu.setNameFname("name");
		jtu.setpIdFname("parent");
		jtu.setSubsetFieldName("list");
		jtu.setOpenFname("expanded");
		j.setObj(jtu.getZtreeNodeData(list));
		return j;
	}
	
	/**
	 * 获取选择显示列表树形框数据
	 * @param searchName
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getChannelZtree300")
	@ResponseBody
	public AjaxJson getChannelZtree300(String appType,String searchName,HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		List<Map<String, Object>> list = new ArrayList<>();
		DictDataitemGroup entity=new DictDataitemGroup();
		entity.setAppType(appType);
		entity.setProtocolId("300");
		List<DictDataitemGroup> ddgList = dictDataitemGroupService.getList(entity);
		for(DictDataitemGroup g:ddgList){
			Map<String, Object> m = new HashMap<String, Object>();
			m.put("id", g.getId());
			m.put("name", g.getName());
			m.put("parent", "");
			m.put("expanded", true);
			DictDataitem dd=new DictDataitem();
			dd.put("groupId", g.getId());
			dd.put("appType", appType);
			//分组已区分 规约 故删掉
		//	dd.setProtocolId("200");
			if(StringUtil.isNotEmpty(searchName)&&g.getName().indexOf(searchName)<0){
				dd.setName(searchName);
			}
			List<DictDataitem> ddList=dictDataitemService.getList(dd);
			List<Map<String, Object>> listk = new ArrayList<>();
			for(DictDataitem d:ddList){
				Map<String, Object> dm = new HashMap<String, Object>();
				dm.put("id", d.getId());
				dm.put("name", d.getName());
				dm.put("parent", g.getId());
				dm.put("expanded", false);
				listk.add(dm);
			}
			if(StringUtil.isNotEmpty(searchName)){
				if(listk.size()>0){
					m.put("list", listk);
					m.put("expanded", true);
					list.add(m);
				}
			}else{
				if(listk.size()>0){
				m.put("list", listk);
				list.add(m);
				}
			}
		}
		ZtreeUtil jtu = new ZtreeUtil();
		jtu.setIdFname("id");
		jtu.setNameFname("name");
		jtu.setpIdFname("parent");
		jtu.setSubsetFieldName("list");
		jtu.setOpenFname("expanded");
		j.setObj(jtu.getZtreeNodeData(list));
		return j;
	}
	public static String flipTime(String times){
		String year = StringUtils.substring(times,6,10);
		String mohth = StringUtils.substring(times,3,5);
		String day = StringUtils.substring(times,0,2);

		return year+"-"+mohth+"-"+day;
	}
	/**
	 * dictMeterDataStorageTable查询分页
	 * @param reques
	 * @param model
	 * @return
	 */
	private JqGridSearchTo jgrt;
	private int tType = 0;
	private Map<String,String> meterMap;
	private Map<String,Object> params;
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request,String deviceId) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        Map<String,Object> m=jqGridSearchTo.getMap();
        String channelStrs=(String) m.get("channel");
        String times_start=(String) m.get("times_start");
        String times_end=(String) m.get("times_end");
		String start_time_t =  flipTime(times_start);
		String end_time_t =  flipTime(times_end);

		m.put("start_time_t",start_time_t);
		m.put("end_time_t",end_time_t);
        String serizlName=(String)m.get("serizlName");
        String meterType=(String)m.get("meterType");
        m.put("serizlName", serizlName);
        m.put("meterType", meterType);
        m.put("deviceId", deviceId);

    	try {
    		if(StringUtil.isNotEmpty(times_start)){
    			m.put("times_start", DateUtils.parseDate(times_start, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
    		}
			
			if(StringUtil.isNotEmpty(times_end)){
				m.put("times_end", DateUtils.parseDate(times_end, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
        	}
		} catch (ParseException e) {
			e.printStackTrace();
		}
    	 String tableName="";
    	 String selectTimeType = "";
         JqGridResponseTo j=new JqGridResponseTo();
         if(!StringUtil.isNotEmpty(channelStrs)){
         	 return j;
         }
        String[] tIds=channelStrs.split(",");
        if(tIds.length>0){
        	DictMeterDataStorageInfo dictMeterDataStorageInfo =dictMeterDataStorageInfoService.getEntity(tIds[0]);
        	if(dictMeterDataStorageInfo==null){
        		return j;
        	}
        	DictMeterDataStorageTable dictMeterDataStorageTable=dictMeterDataStorageTableService.getEntity(dictMeterDataStorageInfo.getTableId());
        	if(dictMeterDataStorageTable==null){
        		return j;
        	}
        	tableName=dictMeterDataStorageTable.getName();
			AssetMeasurementProfile measurementProfile = assetMeasurementProfileService.getProfileByProfileid(dictMeterDataStorageTable.getProfileId());
			if(!measurementProfile.equals(null)){
				selectTimeType = measurementProfile.getProfileCycleType();
			}

        }
        int t = 0;
		this.tType = 0;
		String mode = ResourceUtil.getSessionattachmenttitle("measurement.profile.time");
		if(selectTimeType.equals("Minutely") && "15".equals(mode.trim())){
			t = 1;
			this.tType = 1;
		}else if(selectTimeType.equals("Monthly")){
			t = 2;
			this.tType = 2;
		}
        Map<String,Object> dsm=new HashMap<String, Object>();
        dsm.put("ids", tIds);
        DictMeterDataStorageInfo dmd=new DictMeterDataStorageInfo();
        dmd.setExtData(dsm);
        List<DictMeterDataStorageInfo> dmdList= dictMeterDataStorageInfoService.getList(dmd);
        String viewField="";
        
        //根据页面显示排序start
        Map<String,DictMeterDataStorageInfo> dmdMap = Maps.newHashMap();
        for(int i=0;i<dmdList.size();i++){
        	dmdMap.put(dmdList.get(i).getId(), dmdList.get(i));
        }
        for(int i=0;i<tIds.length;i++) {
        	viewField+=",a.VALUE"+dmdMap.get(tIds[i]).getFieldIndex()+" as value"+(i+1);
        }
        //根据页面显示排序end
        
        jqGridSearchTo.put("tableName", tableName.toUpperCase()); 
        jqGridSearchTo.put("viewField", viewField);
        jqGridSearchTo.put("viewFieldMysql", viewField);
        try {
        	 if(StringUtils.isEmpty(jqGridSearchTo.getSidx()) || "null".equals(jqGridSearchTo.getSidx())){
        		 jqGridSearchTo.setSidx("times");
        	 }
        	 
    		SysUser su = TokenManager.getToken();
			List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
			jqGridSearchTo.put("orgIdList", orgIdList);

             if("Meter".equalsIgnoreCase(meterType)){
        		AssetMeter assetMeter = new AssetMeter();
             	assetMeter.setSn(serizlName);
             	assetMeter = assetMeterService.get(assetMeter);
             	String meterId=assetMeter.getId();
             	m.put("deviceId", meterId);
             	if(t==0){
					this.jgrt = jqGridSearchTo;
					j=dictMeterDataStorageTableService.getForJqGridMeter(jqGridSearchTo, null);
				}else if(t==1){
					String times_start_x = times_start.substring(10);
					String other_start = "";
//					String other_start = " "+times_start.substring(11,13) +":00:00";
					int start_x = Integer.parseInt(times_start_x.substring(4,6));
					int start_hour = Integer.parseInt(times_start_x.substring(1,3));
					String st= "";
					if(start_x > 0  && start_x <= 15){
						st = start_time_t+" "+start_hour+":15:00";
					}else if(start_x > 15  && start_x <= 30){
						st = start_time_t+" "+start_hour+":30:00";
					}else if(start_x > 30  && start_x <= 45){
						st = start_time_t+" "+start_hour+":45:00";
					}else if(start_x > 45  && start_x < 60){

						if(start_hour < 23){
							start_hour = start_hour+1;

							st = start_time_t +" "+start_hour+":00:00";
						}else{
							SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
							Date sDate = sdf.parse(start_time_t);
 							Format f = new SimpleDateFormat("yyyy-MM-dd");
							Calendar c = Calendar.getInstance();
							c.setTime(sDate);
							c.add(Calendar.DAY_OF_MONTH, 1);       //利用Calendar 实现 Date日期+1天
							sDate = c.getTime();
 							st = f.format(sDate)+"00:00:00";
						}

					}else if(start_x == 0){
						st = start_time_t+" "+start_hour+":00:00";
					}


					String times_end_y = times_end.substring(10);
					m.put("times_start_x",st);
					m.put("times_end_y",end_time_t+times_end_y);
					this.jgrt = jqGridSearchTo;
					j=dictMeterDataStorageTableService.getForJqGridMeterOnlyShenyu(jqGridSearchTo);
				}else if(t==2){
					String times_start_x = times_start.substring(10);
					String times_end_y = times_end.substring(10);
					m.put("times_start_x",start_time_t);
					m.put("times_end_y",end_time_t);
					this.jgrt = jqGridSearchTo;
					j=dictMeterDataStorageTableService.getForJqGridMeterMonthShenyu(jqGridSearchTo);
				}


             }else if("Commnuicator".equalsIgnoreCase(meterType)){//集中器
            	   AssetCommunicator communicator = new AssetCommunicator();
            	   communicator.setSn(serizlName);
                   communicator = assetCommunicatorService.get(communicator);
                   if(communicator!=null){
                     AssetMeter entity = new AssetMeter();
               		 entity.setCommunicatorId(communicator.getId());
               		 List<AssetMeter> meterList = assetMeterService.getList(entity);
               		 List<String> meterIds = Lists.newArrayList();
               		 Map<String,String> meterMap = Maps.newHashMap();
               		 if(meterList !=null && meterList.size() > 0){
               			 for(AssetMeter assetMeter : meterList){
               				meterIds.add(assetMeter.getId());
               				meterMap.put(assetMeter.getId(), assetMeter.getSn());
               			 }
               			m.put("deviceIds", meterIds);
						 this.jgrt = jqGridSearchTo;
						 this.meterMap = meterMap;
           				j=dictMeterDataStorageTableService.getForJqGridCommnuicator(jqGridSearchTo,meterMap);
               		 }else{
						 Map<String,Object> params = new HashMap<String,Object>();
						 params.put("serizlName",serizlName);
						 params.put("start_time_t",start_time_t);
						 params.put("end_time_t",end_time_t);
						 this.params = params;
						 List<Map<String,Object>> emptyList =  dictMeterDataStorageTableService.getMeterDateList(params);

						 j.setRows(emptyList);
					 }
                  }else{
					   Map<String,Object> params = new HashMap<String,Object>();
					   params.put("serizlName",serizlName);
					   params.put("start_time_t",start_time_t);
					   params.put("end_time_t",end_time_t);
					   this.params = params;
					   List<Map<String,Object>> emptyList =  dictMeterDataStorageTableService.getMeterDateList(params);

					   j.setRows(emptyList);
				   }
				 this.jgrt = jqGridSearchTo;
             }else if("Line".equalsIgnoreCase(meterType)){//线路
            	 AssetEntityRelationship   ship = new AssetEntityRelationship();
            	 ship.setParentId(deviceId);
            	 ship.setParentType(3);
            	 ship.setType(1);
            	 List<AssetEntityRelationship> shipList= this.assetEntityRelationshipService.getList(ship);
            	 List<String> meterIds = Lists.newArrayList();
           		 Map<String,String> meterMap = Maps.newHashMap();
           		 if(shipList!=null&&shipList.size()>0) {
           			 for(AssetEntityRelationship shipTmp:shipList) {
           				 meterIds.add(shipTmp.getId());
           			 }
           			 //获取电表sn关系
           			 List<AssetMeter> meterList= this.assetMeterService.getByIds(meterIds);
	           		 for(AssetMeter assetMeter : meterList){
	        				meterMap.put(assetMeter.getId(), assetMeter.getSn());
        			 }
           			m.put("deviceIds", meterIds);
	           		 this.meterMap = meterMap;
       				j= dictMeterDataStorageTableService.getForJqGridCommnuicator(jqGridSearchTo,meterMap);
           		 }else{
					 Map<String,Object> params = new HashMap<String,Object>();
					 params.put("serizlName",serizlName);
					 params.put("start_time_t",start_time_t);
					 params.put("end_time_t",end_time_t);
					 this.params = params;
					 List<Map<String,Object>> emptyList =  dictMeterDataStorageTableService.getMeterDateList(params);

					 j.setRows(emptyList);
				 }
				 this.jgrt = jqGridSearchTo;
             }else if("Transformer".equalsIgnoreCase(meterType)){//变压器
            	 AssetEntityRelationship   ship = new AssetEntityRelationship();
            	 ship.setParentId(deviceId);
            	 ship.setParentType(4);
            	 ship.setType(1);
            	 List<AssetEntityRelationship> shipList= this.assetEntityRelationshipService.getList(ship);
            	 List<String> meterIds = Lists.newArrayList();
           		 Map<String,String> meterMap = Maps.newHashMap();
           		 if(shipList!=null&&shipList.size()>0) {
           			 for(AssetEntityRelationship shipTmp:shipList) {
           				 meterIds.add(shipTmp.getId());
           			 }
           			 //获取电表sn关系
           			 List<AssetMeter> meterList= this.assetMeterService.getByIds(meterIds);
	           		 for(AssetMeter assetMeter : meterList){
	        				meterMap.put(assetMeter.getId(), assetMeter.getSn());
        			 }
           			m.put("deviceIds", meterIds);
					 this.meterMap = meterMap;
       				j=dictMeterDataStorageTableService.getForJqGridCommnuicator(jqGridSearchTo,meterMap);
           		 }else{
					 Map<String,Object> params = new HashMap<String,Object>();
					 params.put("serizlName",serizlName);
					 params.put("start_time_t",start_time_t);
					 params.put("end_time_t",end_time_t);
					 this.params = params;
					 List<Map<String,Object>> emptyList =  dictMeterDataStorageTableService.getMeterDateList(params);

					 j.setRows(emptyList);
				 }
				 this.jgrt = jqGridSearchTo;
             }else if("Customer".equalsIgnoreCase(meterType)){//客户
            	 AssetCustomer customer= this.assetCustomerService.getEntity(deviceId);
            	 if(StringUtils.isNotEmpty(customer.getMeterId())) {
            		 m.put("deviceId", customer.getMeterId());

     				 j=dictMeterDataStorageTableService.getForJqGridMeter(jqGridSearchTo, null);
            	 }else{
					 Map<String,Object> params = new HashMap<String,Object>();
					 params.put("serizlName",serizlName);
					 params.put("start_time_t",start_time_t);
					 params.put("end_time_t",end_time_t);
					 this.params = params;
					 List<Map<String,Object>> emptyList =  dictMeterDataStorageTableService.getMeterDateList(params);

					 j.setRows(emptyList);
				 }
				 this.jgrt = jqGridSearchTo;
             } 
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
	/**
	 * dictMeterDataStorageTable查询分页
	 * @param reques
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagridOrg")
	@ResponseBody
	public JqGridResponseTo datagridOrg(JqGridSearchTo jqGridSearchTo,HttpServletRequest request,String deviceId,String txt_org_id) {
		TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
		Map<String,Object> m=jqGridSearchTo.getMap();
		String channelStrs=(String) m.get("channel");
		String times_start=(String) m.get("times_start") + " 00:00:00";
		m.put("times_start", times_start);

		String serizlName=(String)m.get("serizlName");
		//String meterType=(String)m.get("meterType");
		String meterType= "Meter";
		//m.put("serizlName", serizlName);
		m.put("meterType", meterType);
		m.put("deviceId", deviceId);
		List<String> orgIds=OrganizationUtils.getOrgIds(sysOrgService,txt_org_id);
		m.put("orgIdList", orgIds);

		try {
			if(StringUtil.isNotEmpty(times_start)){
				//m.put("times_start", DateUtils.parseDate(times_start, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
				Date start_time = DateUtils.parseDate(times_start, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
				m.put("times_start_lone", start_time);
//				m.put("times_end", new Date(start_time.getTime() + (1000 * 60 * 60 * 24)));//自动延期一天
			}
			m.put("times_start", "");
			m.put("times_end", "");
			//if(StringUtil.isNotEmpty(times_end)){
			//	m.put("times_end", DateUtils.parseDate(times_end, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
			//}
		} catch (ParseException e) {
			e.printStackTrace();
		}
		String tableName="";
		JqGridResponseTo j=new JqGridResponseTo();
		if(!StringUtil.isNotEmpty(channelStrs)){
			return j;
		}
		String[] tIds=channelStrs.split(",");
		if(tIds.length>0){
			DictMeterDataStorageInfo dictMeterDataStorageInfo =dictMeterDataStorageInfoService.getEntity(tIds[0]);
			if(dictMeterDataStorageInfo==null){
				return j;
			}
			DictMeterDataStorageTable dictMeterDataStorageTable=dictMeterDataStorageTableService.getEntity(dictMeterDataStorageInfo.getTableId());
			if(dictMeterDataStorageTable==null){
				return j;
			}
			tableName=dictMeterDataStorageTable.getName();
		}
		Map<String,Object> dsm=new HashMap<String, Object>();
		dsm.put("ids", tIds);
		DictMeterDataStorageInfo dmd=new DictMeterDataStorageInfo();
		dmd.setExtData(dsm);
		List<DictMeterDataStorageInfo> dmdList= dictMeterDataStorageInfoService.getList(dmd);
		String viewField="";

		//根据页面显示排序start
		Map<String,DictMeterDataStorageInfo> dmdMap = Maps.newHashMap();
		for(int i=0;i<dmdList.size();i++){
			dmdMap.put(dmdList.get(i).getId(), dmdList.get(i));
		}
		for(int i=0;i<tIds.length;i++) {
			viewField+=",a.VALUE"+dmdMap.get(tIds[i]).getFieldIndex()+" as value"+(i+1);
		}
		//根据页面显示排序end

		jqGridSearchTo.put("tableName", tableName.toUpperCase());
		jqGridSearchTo.put("viewField", viewField);
		jqGridSearchTo.put("viewFieldMysql", viewField);
		try {
			if(StringUtils.isEmpty(jqGridSearchTo.getSidx()) || "null".equals(jqGridSearchTo.getSidx())){
				jqGridSearchTo.setSidx("times");
			}

//			SysUser su = TokenManager.getToken();
//			List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
//			jqGridSearchTo.put("orgIdList", orgIdList);

			if("Meter".equalsIgnoreCase(meterType)){
				AssetMeter assetMeter = new AssetMeter();
				assetMeter.setSn(serizlName);
				assetMeter = assetMeterService.get(assetMeter);
				String meterId=assetMeter.getId();
				// m.put("deviceId", meterId);
				m.remove("deviceId");
				m.remove("serizlName");//查询所有的表计，去掉特定条件
				j=dictMeterDataStorageTableService.getForJqGridMeterOrg(jqGridSearchTo, null);

			}else if("Commnuicator".equalsIgnoreCase(meterType)){//集中器
				AssetCommunicator communicator = new AssetCommunicator();
				communicator.setSn(serizlName);
				communicator = assetCommunicatorService.get(communicator);
				if(communicator!=null){
					AssetMeter entity = new AssetMeter();
					entity.setCommunicatorId(communicator.getId());
					List<AssetMeter> meterList = assetMeterService.getList(entity);
					List<String> meterIds = Lists.newArrayList();
					Map<String,String> meterMap = Maps.newHashMap();
					if(StringUtil.isNotEmpty(meterList)){
						for(AssetMeter assetMeter : meterList){
							meterIds.add(assetMeter.getId());
							meterMap.put(assetMeter.getId(), assetMeter.getSn());
						}
						m.put("deviceIds", meterIds);
						j=dictMeterDataStorageTableService.getForJqGridCommnuicator(jqGridSearchTo,meterMap);
					}
				}
			}else if("Line".equalsIgnoreCase(meterType)){//线路
				AssetEntityRelationship   ship = new AssetEntityRelationship();
				ship.setParentId(deviceId);
				ship.setParentType(3);
				ship.setType(1);
				List<AssetEntityRelationship> shipList= this.assetEntityRelationshipService.getList(ship);
				List<String> meterIds = Lists.newArrayList();
				Map<String,String> meterMap = Maps.newHashMap();
				if(shipList!=null&&shipList.size()>0) {
					for(AssetEntityRelationship shipTmp:shipList) {
						meterIds.add(shipTmp.getId());
					}
					//获取电表sn关系
					List<AssetMeter> meterList= this.assetMeterService.getByIds(meterIds);
					for(AssetMeter assetMeter : meterList){
						meterMap.put(assetMeter.getId(), assetMeter.getSn());
					}
					m.put("deviceIds", meterIds);
					j=dictMeterDataStorageTableService.getForJqGridCommnuicator(jqGridSearchTo,meterMap);
				}

			}else if("Transformer".equalsIgnoreCase(meterType)){//变压器
				AssetEntityRelationship   ship = new AssetEntityRelationship();
				ship.setParentId(deviceId);
				ship.setParentType(4);
				ship.setType(1);
				List<AssetEntityRelationship> shipList= this.assetEntityRelationshipService.getList(ship);
				List<String> meterIds = Lists.newArrayList();
				Map<String,String> meterMap = Maps.newHashMap();
				if(shipList!=null&&shipList.size()>0) {
					for(AssetEntityRelationship shipTmp:shipList) {
						meterIds.add(shipTmp.getId());
					}
					//获取电表sn关系
					List<AssetMeter> meterList= this.assetMeterService.getByIds(meterIds);
					for(AssetMeter assetMeter : meterList){
						meterMap.put(assetMeter.getId(), assetMeter.getSn());
					}
					m.put("deviceIds", meterIds);
					j=dictMeterDataStorageTableService.getForJqGridCommnuicator(jqGridSearchTo,meterMap);
				}
			}else if("Customer".equalsIgnoreCase(meterType)){//客户
				AssetCustomer customer= this.assetCustomerService.getEntity(deviceId);
				if(StringUtils.isNotEmpty(customer.getMeterId())) {
					m.put("deviceId", customer.getMeterId());
					j=dictMeterDataStorageTableService.getForJqGridMeter(jqGridSearchTo, null);
				}
			}
		}
		catch (Exception e) {
			e.printStackTrace();
		}
		return j;
	}
	/**
	 * dictMeterDataStorageTable查询分页
	 * @param reques
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagridReport")
	@ResponseBody
	public JqGridResponseTo datagridReport(JqGridSearchTo jqGridSearchTo,HttpServletRequest request,String deviceId) {
		TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
		Map<String,Object> m=jqGridSearchTo.getMap();
		String channelStrs=(String) m.get("channel");
		String times_start=(String) m.get("times_start");
		String times_end=(String) m.get("times_end");
		String serizlName=(String)m.get("serizlName");
		String meterType=(String)m.get("meterType");
		String searchTimeType=(String)m.get("searchTimeType");
		m.put("serizlName", serizlName);
		m.put("meterType", meterType);
		m.put("deviceId", deviceId);
		try {
			if(StringUtil.isNotEmpty(times_start)){
				m.put("times_start", DateUtils.parseDate(times_start, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
			}
			
			if(StringUtil.isNotEmpty(times_end)){
				m.put("times_end", DateUtils.parseDate(times_end, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
			}
		} catch (ParseException e) {
			e.printStackTrace();
		}
		String tableName="";
		JqGridResponseTo j=new JqGridResponseTo();
		if(!StringUtil.isNotEmpty(channelStrs)){
			return j;
		}
		String[] tIds=channelStrs.split(",");
		if(tIds.length>0){
			DictMeterDataStorageInfo dictMeterDataStorageInfo =dictMeterDataStorageInfoService.getEntity(tIds[0]);
			if(dictMeterDataStorageInfo==null){
				return j;
			}
			DictMeterDataStorageTable dictMeterDataStorageTable=dictMeterDataStorageTableService.getEntity(dictMeterDataStorageInfo.getTableId());
			if(dictMeterDataStorageTable==null){
				return j;
			}
			tableName=dictMeterDataStorageTable.getName();
		}
		Map<String,Object> dsm=new HashMap<String, Object>();
		dsm.put("ids", tIds);
		DictMeterDataStorageInfo dmd=new DictMeterDataStorageInfo();
		dmd.setExtData(dsm);
		List<DictMeterDataStorageInfo> dmdList= dictMeterDataStorageInfoService.getList(dmd);
		String viewField="";
//		for(int i=0;i<dmdList.size();i++){
//		      viewField+=",a.VALUE"+dmdList.get(i).getFieldIndex()+" as value"+(i+1);
//		}
		//根据页面显示排序start
        Map<String,DictMeterDataStorageInfo> dmdMap = Maps.newHashMap();
        for(int i=0;i<dmdList.size();i++){
        	dmdMap.put(dmdList.get(i).getId(), dmdList.get(i));
        }
        for(int i=0;i<tIds.length;i++) {
        	viewField+=",a.VALUE"+dmdMap.get(tIds[i]).getFieldIndex()+" as value"+(i+1);
        }
        //根据页面显示排序end
        
		jqGridSearchTo.put("tableName", tableName.toUpperCase()); 
		jqGridSearchTo.put("viewField", viewField);
		jqGridSearchTo.put("viewFieldMysql", viewField);
		try {
			if(StringUtils.isEmpty(jqGridSearchTo.getSidx()) || "null".equals(jqGridSearchTo.getSidx())){
				jqGridSearchTo.setSidx("times");
			}
			
			SysUser su = TokenManager.getToken();
			List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
			jqGridSearchTo.put("orgIdList", orgIdList);
			
			if("Meter".equalsIgnoreCase(meterType)){
				AssetMeter assetMeter = new AssetMeter();
             	assetMeter.setSn(serizlName);
             	assetMeter = assetMeterService.get(assetMeter);
             	String meterId=assetMeter.getId();
             	m.put("deviceId", meterId);
				j=dictMeterDataStorageTableService.getForJqGridMeter(jqGridSearchTo, searchTimeType);
			 }else if("Commnuicator".equalsIgnoreCase(meterType)){//集中器
				AssetCommunicator communicator = new AssetCommunicator();
				communicator.setSn(serizlName);
				communicator = assetCommunicatorService.get(communicator);
				if(communicator!=null){
					AssetMeter entity = new AssetMeter();
					entity.setCommunicatorId(communicator.getId());
					List<AssetMeter> meterList = assetMeterService.getList(entity);
					List<String> meterIds = Lists.newArrayList();
					 Map<String,String> meterMap = Maps.newHashMap();
               		 if(meterList !=null && meterList.size() > 0){
               			 for(AssetMeter assetMeter : meterList){
               				meterIds.add(assetMeter.getId());
               				meterMap.put(assetMeter.getId(), assetMeter.getSn());
               			 }
               			m.put("deviceIds", meterIds);
           				j=dictMeterDataStorageTableService.getForJqGridCommnuicator(jqGridSearchTo,meterMap);
               		 }
				}
				
			 }else if("Line".equalsIgnoreCase(meterType)){//线路
            	 AssetEntityRelationship   ship = new AssetEntityRelationship();
            	 ship.setParentId(deviceId);
            	 ship.setParentType(3);
            	 ship.setType(1);
            	 List<AssetEntityRelationship> shipList= this.assetEntityRelationshipService.getList(ship);
            	 List<String> meterIds = Lists.newArrayList();
           		 Map<String,String> meterMap = Maps.newHashMap();
           		 if(shipList!=null&&shipList.size()>0) {
               			 for(AssetEntityRelationship shipTmp:shipList) {
               				 meterIds.add(shipTmp.getId());
               			 }
               			 //获取电表sn关系
               			 List<AssetMeter> meterList= this.assetMeterService.getByIds(meterIds);
    	           		 for(AssetMeter assetMeter : meterList){
    	        				meterMap.put(assetMeter.getId(), assetMeter.getSn());
            			 }
               			m.put("deviceIds", meterIds);
           				j=dictMeterDataStorageTableService.getForJqGridCommnuicator(jqGridSearchTo,meterMap);
               		 }
			 }else if("Transformer".equalsIgnoreCase(meterType)){//变压器
            	 AssetEntityRelationship   ship = new AssetEntityRelationship();
            	 ship.setParentId(deviceId);
            	 ship.setParentType(4);
            	 ship.setType(1);
            	 List<AssetEntityRelationship> shipList= this.assetEntityRelationshipService.getList(ship);
            	 List<String> meterIds = Lists.newArrayList();
           		 Map<String,String> meterMap = Maps.newHashMap();
           		 if(shipList!=null&&shipList.size()>0) {
           			 for(AssetEntityRelationship shipTmp:shipList) {
           				 meterIds.add(shipTmp.getId());
           			 }
           		//获取电表sn关系
           			 List<AssetMeter> meterList= this.assetMeterService.getByIds(meterIds);
	           		 for(AssetMeter assetMeter : meterList){
	        				meterMap.put(assetMeter.getId(), assetMeter.getSn());
        			 }
           			m.put("deviceIds", meterIds);
       				j=dictMeterDataStorageTableService.getForJqGridCommnuicator(jqGridSearchTo,meterMap);
           		 }
             }else if("Customer".equalsIgnoreCase(meterType)){//客户
            	 AssetCustomer customer= this.assetCustomerService.getEntity(deviceId);
            	 if(StringUtils.isNotEmpty(customer.getMeterId())) {
            		 m.put("deviceId", customer.getMeterId());
     				 j=dictMeterDataStorageTableService.getForJqGridMeter(jqGridSearchTo, null);
            	 }
             } 
		}
		catch (Exception e) {
			e.printStackTrace();
		}
		return j;
	}
	
	 /**
	  * 导出
	  * @param dataIntegrity
	  * @param request
	  * @param response
	  */
	 @RequestMapping(value = "getExcelMeterData")
	 @ResponseBody
	 public void getExcelMeterData(String deviceId,String groupId,String channel,String times_start,String times_end,String meterType,MeterDataReport meterDataReport,HttpServletRequest request, HttpServletResponse response) {
		 Map<String,Object> m = new HashMap<String, Object>();
    	try {
//    		if(StringUtil.isNotEmpty(times_start)){
//    			m.put("times_start", DateUtils.parseDate(times_start, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
//    		}
//			if(StringUtil.isNotEmpty(times_end)){
//				m.put("times_end", DateUtils.parseDate(times_end, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
//        	}
			String tableName="";
	        if(!StringUtil.isNotEmpty(channel)){
	        	 return ;
	        }
	        String[] tIds=channel.split(",");
	        String dataitemId=tIds[0];
	        List<DictDataitem> ddList=new ArrayList<DictDataitem>();
	        if(tIds.length>0){
	        	DictMeterDataStorageInfo dictMeterDataStorageInfo =dictMeterDataStorageInfoService.getEntity(tIds[0]);
	        	if(dictMeterDataStorageInfo==null){
	        		List<MeterDataReport> list= new ArrayList();
	    	    	ExcelDataFormatter edf = new ExcelDataFormatter();
	    	        //要日期格式化使用以下方式
	    	        Map<String,String> tvs=new HashMap<String, String>();
	    	        tvs.put("date", "MM/dd/yyyy HH:mm:ss");
	    	        edf.set("date", tvs);
	    			ExcelUtils.writeToFile(list, edf, "excelMeterDataList.xlsx", response);
	    			return;
	        	}
	        	DictMeterDataStorageTable dictMeterDataStorageTable=dictMeterDataStorageTableService.getEntity(dictMeterDataStorageInfo.getTableId());
	        	if(dictMeterDataStorageTable==null){
	        		 List<MeterDataReport> list= new ArrayList();
	    	    	ExcelDataFormatter edf = new ExcelDataFormatter();
	    	        //要日期格式化使用以下方式
	    	        Map<String,String> tvs=new HashMap<String, String>();
	    	        tvs.put("date", "MM/dd/yyyy HH:mm:ss");
	    	        edf.set("date", tvs);
	    			ExcelUtils.writeToFile(list, edf, "excelMeterDataList.xlsx", response);
	    			return;
	        	}
	        	tableName=dictMeterDataStorageTable.getName();
	        }

	        DictDataitem dd=new DictDataitem();
	        dd.put("groupId",groupId);
	        ddList=dictDataitemService.getList(dd);
	        List<DictDataitem> ddList1=new ArrayList<DictDataitem>();
	        
	        for(DictDataitem dictDataitem : ddList){
	         if(Arrays.asList(tIds).contains(dictDataitem.getId())){
	        	 ddList1.add(dictDataitem);
	         }
	        }
	  
            Map<String,Object> dsm=new HashMap<String, Object>();
	        dsm.put("ids", tIds);
			dsm.put("serizlName", meterDataReport.getSerizlName());
			String start_time_t =  flipTime(times_start);
			String end_time_t =  flipTime(times_end);

	        DictMeterDataStorageInfo dmd=new DictMeterDataStorageInfo();
	        dmd.setExtData(dsm);
	        String viewField="";
	        List<DictMeterDataStorageInfo> dmdList= dictMeterDataStorageInfoService.getList(dmd);
	        Map<String,Excel> excels=new HashMap<String, Excel>();
	        Excel times=ExcelUtils.createExcel("Time", 22);
	        excels.put("times", times);
	        Excel serizlName=ExcelUtils.createExcel("Serial Number", 15);
	        excels.put("serizlName", serizlName);
//			Excel addr=ExcelUtils.createExcel("meter addr", 15);
//			excels.put("addr", addr);
			Excel meterName=ExcelUtils.createExcel("meter Name", 15);
			excels.put("meterName", meterName);
//	        for(int i=0;i<dmdList.size();i++){
//		        	Excel value = ExcelUtils.createExcel(ddList1.get(i).getName(), ddList1.get(i).getName().length());
//		        	excels.put("value"+(i+1), value);
//	        }
//	        
//	        
//	        for(int i=0;i<dmdList.size();i++){
//	             viewField+=",a.VALUE"+dmdList.get(i).getFieldIndex()+" as value"+(i+1);
//	        }
	      //根据页面显示排序start
	        Map<String,DictMeterDataStorageInfo> dmdMap = Maps.newHashMap();
	        for(int i=0;i<dmdList.size();i++){
	        	dmdMap.put(dmdList.get(i).getId(), dmdList.get(i));
	        }
	        for(int i=0;i<tIds.length;i++) {
	        	DictMeterDataStorageInfo  in = dmdMap.get(tIds[i]);
	        	viewField+=",a.VALUE"+in.getFieldIndex()+" as value"+(i+1);
	        	Excel value = ExcelUtils.createExcel(ddList1.get(i).getName(), ddList1.get(i).getName().length());
	        	excels.put("value"+(i+1), value);
	        }
	        //根据页面显示排序end
			if(StringUtil.isNotEmpty(times_start)){
				m.put("start_time",start_time_t+"00:00:00");
//				m.put("times_start", DateUtils.parseDate(times_start, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
			}
			if(StringUtil.isNotEmpty(times_end)){
				m.put("end_time",end_time_t+"23:59:59");
//				m.put("times_end", DateUtils.parseDate(times_end, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
			}

			m.put("tableName", tableName);
			m.put("viewField", viewField);

			m.put("start_time_t",start_time_t);
			m.put("end_time_t",end_time_t);
	        List<String> meterIds = Lists.newArrayList();
	        if("Commnuicator".equalsIgnoreCase(meterType)){
        		 AssetCommunicator communicatorEntity = new AssetCommunicator();
                 communicatorEntity.setSn(meterDataReport.getSerizlName());
                 AssetCommunicator communicator = assetCommunicatorService.get(communicatorEntity);
                 if(communicator!=null){
             		 AssetMeter entity = new AssetMeter();
               		 entity.setCommunicatorId(communicator.getId());
               		 List<AssetMeter> meterList = assetMeterService.getList(entity);
             		 if(meterList !=null && meterList.size() > 0){
             			 for(AssetMeter assetMeter : meterList){
             				meterIds.add(assetMeter.getId());
             			 }
             			m.put("deviceIds", meterIds);
             		 }
                 }
	        }else if("Line".equalsIgnoreCase(meterType)){//线路
            	 AssetEntityRelationship   ship = new AssetEntityRelationship();
            	 ship.setParentId(deviceId);
            	 ship.setParentType(3);
            	 ship.setType(1);
            	 List<AssetEntityRelationship> shipList= this.assetEntityRelationshipService.getList(ship);
           		if(shipList!=null&&shipList.size()>0) {
          			 for(AssetEntityRelationship shipTmp:shipList) {
          				 meterIds.add(shipTmp.getId());
          			 }
          		 }
		       }else if("Transformer".equalsIgnoreCase(meterType)){//变压器	
	           	 AssetEntityRelationship   ship = new AssetEntityRelationship();
	           	 ship.setParentId(deviceId);
	           	 ship.setParentType(4);
	           	 ship.setType(1);
	           	 List<AssetEntityRelationship> shipList= this.assetEntityRelationshipService.getList(ship);
	          		if(shipList!=null&&shipList.size()>0) {
	          			 for(AssetEntityRelationship shipTmp:shipList) {
	          				 meterIds.add(shipTmp.getId());
	          			 }
	       			 }
	        } 
	        meterDataReport.setExtData(m);
	        List<MeterDataReport> list= Lists.newArrayList();
	        if("Meter".equalsIgnoreCase(meterType)){
//	        	AssetMeter meter = new AssetMeter();
//	        	meter.setSn(meterDataReport.getSerizlName());
//	        	AssetMeter t = assetMeterService.get(meter);
//	        	meterDataReport.setId(t.getId());
//             	AssetMeasurementProfile prile= getMearurementProfile(t.getId(),dataitemId) ;
//             	List<Date> timeList = Lists.newArrayList();
//                Date startDate=regularTime((Date) m.get("times_start"),(Date) m.get("times_end"), prile, timeList);
//                //如果大于200条，不处理
//                if(timeList.size()>200||prile==null) {
//                	
//                }else {
//                	 meterDataReport.setTimes(startDate);
//                	 List<MeterDataReport> tmpList=dictMeterDataStorageTableService.getMeterDataList(meterDataReport);
//                	 Map<Date,MeterDataReport> map = Maps.newHashMap();
//                	 if(list!=null) {
//             			for(MeterDataReport report:tmpList) {
//             				map.put(report.getTimes(), report);
//             			}
//             		}
//             		for(Date date:timeList) {
//             			MeterDataReport report =map.get(date);
//             			if(report==null) {    
//             				report = new MeterDataReport(meterDataReport.getSerizlName(),date,"1");
//             			}
//             			list.add(report);
//             		}
//                }
	        	
	        	 AssetMeter meter = new AssetMeter();
				 meter.setSn(meterDataReport.getSerizlName());
				 AssetMeter t = assetMeterService.get(meter);
				 meterDataReport.setId(t.getId());
				 list=dictMeterDataStorageTableService.getMeterDataList(meterDataReport);
	        }else if("Customer".equalsIgnoreCase(meterType)){//客户
	        	 AssetCustomer customer= this.assetCustomerService.getEntity(deviceId);
		       	 if(StringUtils.isNotEmpty(customer.getMeterId())) {
			       	 meterDataReport.setId(customer.getMeterId());
					 list=dictMeterDataStorageTableService.getMeterDataList(meterDataReport);
		       	 }
	        }else{
	        	if(meterIds!=null&&meterIds.size()>0) {
	        		m.put("deviceIds", meterIds);
	        		list=dictMeterDataStorageTableService.getMeterDataListByCommnuicator(meterDataReport);
	        	}
	        }
	       
	        if(list.size()<=0){
	        	MeterDataReport mdr=new MeterDataReport();
	        	list.add(mdr);
	        }

	        ExcelDataFormatter edf = new ExcelDataFormatter();
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("times",  ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
	        edf.set("times", tvs);
	        ExcelUtils.writeToFile(list, edf,"excelMeterDataList.xlsx",response, excels);
		} catch (Exception e) {
			e.printStackTrace();
		}
		 
	 }
	/**
	 * 导出
	 * @param dataIntegrity
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "getExcelMeterDataOrg")
	@ResponseBody
	public void getExcelMeterDataOrg(String deviceId,String groupId,String channel,String times_start,String times_end,String meterType,MeterDataReport meterDataReport,HttpServletRequest request, HttpServletResponse response) {
		meterType = "Meter";
		Map<String,Object> m = new HashMap<String, Object>();
		try {
			if(StringUtil.isNotEmpty(times_start)){
				Date start_time = DateUtils.parseDate(times_start, ResourceUtil.getSessionattachmenttitle("local.date.formatter"));
				m.put("times_start_lone", start_time);
//				m.put("times_end", new Date(start_time.getTime() + (1000 * 60 * 60 * 24)));//自动延期一天
			}
			//if(StringUtil.isNotEmpty(times_end)){
			//	m.put("times_end", DateUtils.parseDate(times_end, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
			//}
			String tableName="";
			if(!StringUtil.isNotEmpty(channel)){
				return ;
			}
			String[] tIds=channel.split(",");
			String dataitemId=tIds[0];
			List<DictDataitem> ddList=new ArrayList<DictDataitem>();
			if(tIds.length>0){
				DictMeterDataStorageInfo dictMeterDataStorageInfo =dictMeterDataStorageInfoService.getEntity(tIds[0]);
				if(dictMeterDataStorageInfo==null){
					List<MeterDataReport> list= new ArrayList();
					ExcelDataFormatter edf = new ExcelDataFormatter();
					//要日期格式化使用以下方式
					Map<String,String> tvs=new HashMap<String, String>();
					tvs.put("date", "MM/dd/yyyy HH:mm:ss");
					edf.set("date", tvs);
					ExcelUtils.writeToFile(list, edf, "excelMeterDataList.xlsx", response);
					return;
				}
				DictMeterDataStorageTable dictMeterDataStorageTable=dictMeterDataStorageTableService.getEntity(dictMeterDataStorageInfo.getTableId());
				if(dictMeterDataStorageTable==null){
					List<MeterDataReport> list= new ArrayList();
					ExcelDataFormatter edf = new ExcelDataFormatter();
					//要日期格式化使用以下方式
					Map<String,String> tvs=new HashMap<String, String>();
					tvs.put("date", "MM/dd/yyyy HH:mm:ss");
					edf.set("date", tvs);
					ExcelUtils.writeToFile(list, edf, "excelMeterDataList.xlsx", response);
					return;
				}
				tableName=dictMeterDataStorageTable.getName();
			}

			DictDataitem dd=new DictDataitem();
			dd.put("groupId",groupId);
			ddList=dictDataitemService.getList(dd);
			List<DictDataitem> ddList1=new ArrayList<DictDataitem>();

			for(DictDataitem dictDataitem : ddList){
				if(Arrays.asList(tIds).contains(dictDataitem.getId())){
					ddList1.add(dictDataitem);
				}
			}

			Map<String,Object> dsm=new HashMap<String, Object>();
			dsm.put("ids", tIds);
			DictMeterDataStorageInfo dmd=new DictMeterDataStorageInfo();
			dmd.setExtData(dsm);
			String viewField="";
			List<DictMeterDataStorageInfo> dmdList= dictMeterDataStorageInfoService.getList(dmd);
			Map<String,Excel> excels=new HashMap<String, Excel>();
			Excel times=ExcelUtils.createExcel("Time", 22);
			excels.put("times", times);
			Excel serizlName=ExcelUtils.createExcel("Serial Number", 15);
			excels.put("serizlName", serizlName);
//	        for(int i=0;i<dmdList.size();i++){
//		        	Excel value = ExcelUtils.createExcel(ddList1.get(i).getName(), ddList1.get(i).getName().length());
//		        	excels.put("value"+(i+1), value);
//	        }
//
//
//	        for(int i=0;i<dmdList.size();i++){
//	             viewField+=",a.VALUE"+dmdList.get(i).getFieldIndex()+" as value"+(i+1);
//	        }
			//根据页面显示排序start
			Map<String,DictMeterDataStorageInfo> dmdMap = Maps.newHashMap();
			for(int i=0;i<dmdList.size();i++){
				dmdMap.put(dmdList.get(i).getId(), dmdList.get(i));
			}
			for(int i=0;i<tIds.length;i++) {
				DictMeterDataStorageInfo  in = dmdMap.get(tIds[i]);
				viewField+=",a.VALUE"+in.getFieldIndex()+" as value"+(i+1);
				Excel value = ExcelUtils.createExcel(ddList1.get(i).getName(), ddList1.get(i).getName().length());
				excels.put("value"+(i+1), value);
			}
			//根据页面显示排序end

			m.put("tableName", tableName);
			m.put("viewField", viewField);
			List<String> meterIds = Lists.newArrayList();
			if("Commnuicator".equalsIgnoreCase(meterType)){
				AssetCommunicator communicatorEntity = new AssetCommunicator();
				communicatorEntity.setSn(meterDataReport.getSerizlName());
				AssetCommunicator communicator = assetCommunicatorService.get(communicatorEntity);
				if(communicator!=null){
					AssetMeter entity = new AssetMeter();
					entity.setCommunicatorId(communicator.getId());
					List<AssetMeter> meterList = assetMeterService.getList(entity);
					if(StringUtil.isNotEmpty(meterList)){
						for(AssetMeter assetMeter : meterList){
							meterIds.add(assetMeter.getId());
						}
						m.put("deviceIds", meterIds);
					}
				}
			}else if("Line".equalsIgnoreCase(meterType)){//线路
				AssetEntityRelationship   ship = new AssetEntityRelationship();
				ship.setParentId(deviceId);
				ship.setParentType(3);
				ship.setType(1);
				List<AssetEntityRelationship> shipList= this.assetEntityRelationshipService.getList(ship);
				if(shipList!=null&&shipList.size()>0) {
					for(AssetEntityRelationship shipTmp:shipList) {
						meterIds.add(shipTmp.getId());
					}
				}
			}else if("Transformer".equalsIgnoreCase(meterType)){//变压器
				AssetEntityRelationship   ship = new AssetEntityRelationship();
				ship.setParentId(deviceId);
				ship.setParentType(4);
				ship.setType(1);
				List<AssetEntityRelationship> shipList= this.assetEntityRelationshipService.getList(ship);
				if(shipList!=null&&shipList.size()>0) {
					for(AssetEntityRelationship shipTmp:shipList) {
						meterIds.add(shipTmp.getId());
					}
				}
			}
			meterDataReport.setExtData(m);
			List<MeterDataReport> list= Lists.newArrayList();
			if("Meter".equalsIgnoreCase(meterType)){
				AssetMeter meter = new AssetMeter();
				meter.setSn(meterDataReport.getSerizlName());
				AssetMeter t = assetMeterService.get(meter);
				//meterDataReport.setId(t.getId());
				list=dictMeterDataStorageTableService.getMeterDataListOrg(meterDataReport);
			}else if("Customer".equalsIgnoreCase(meterType)){//客户
				AssetCustomer customer= this.assetCustomerService.getEntity(deviceId);
				if(StringUtils.isNotEmpty(customer.getMeterId())) {
					meterDataReport.setId(customer.getMeterId());
					list=dictMeterDataStorageTableService.getMeterDataList(meterDataReport);
				}
			}else{
				if(meterIds!=null&&meterIds.size()>0) {
					m.put("deviceIds", meterIds);
					list=dictMeterDataStorageTableService.getMeterDataListByCommnuicator(meterDataReport);
				}
			}

			if(list.size()<=0){
				MeterDataReport mdr=new MeterDataReport();
				list.add(mdr);
			}

			ExcelDataFormatter edf = new ExcelDataFormatter();
			Map<String,String> tvs=new HashMap<String, String>();
			tvs.put("times",  ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
			edf.set("times", tvs);
			ExcelUtils.writeToFile(list, edf,"excelMeterDataList.xlsx",response, excels);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	 /**
	  * 打印+导出
	  * @param dataIntegrity
	  * @param request
	  * @param response
	  */
	 @RequestMapping(value = "getPrintMeterData")
	 @ResponseBody
	 public void getPrintMeterData(String eType,String deviceId,String groupId,String channel,String times_start,String times_end,String meterType,MeterDataReport meterDataReport,HttpServletRequest request, HttpServletResponse response) {
		 Map<String,Object> m = new HashMap<String, Object>();
	 	try {
			Map<String,Object> jg=this.jgrt.getMap();
			String serizlNames=(String)jg.get("serizlName");
	        if(!StringUtil.isNotEmpty(channel)){
	        	 return ;
	        }
	        String[] tIds=channel.split(",");
	        List<DictDataitem> ddList=new ArrayList<DictDataitem>();


			DictDataitem dd=new DictDataitem();
	        dd.put("groupId",groupId);
	        ddList=dictDataitemService.getList(dd);
	        Map<String,Object> dsm=new HashMap<String, Object>();
	        dsm.put("ids", tIds);
	        dsm.put("serizlName", meterDataReport.getSerizlName());


	        String dataitemId=tIds[0];
	        DictMeterDataStorageInfo dmd=new DictMeterDataStorageInfo();
	        dmd.setExtData(dsm);
	        List<DictMeterDataStorageInfo> dmdList= dictMeterDataStorageInfoService.getList(dmd);
	        List<DictDataitem> ddList1=new ArrayList<DictDataitem>();
	        for(DictDataitem dictDataitem : ddList){
		         if(Arrays.asList(tIds).contains(dictDataitem.getId())){
		        	 ddList1.add(dictDataitem);
		         }
	        }
	        String viewField="";
	        Map<String,Excel> excels=new HashMap<String, Excel>();
	        Excel times=ExcelUtils.createExcel("Time", 22);
	        excels.put("times", times);
	        Excel serizlName=ExcelUtils.createExcel("Serial Number", 15);
	        excels.put("serizlName", serizlName);
//	        Excel addr=ExcelUtils.createExcel("meter addr", 15);
//	        excels.put("addr", addr);
	        Excel meterName=ExcelUtils.createExcel("meter Name", 15);
	        excels.put("meterName", meterName);
			//根据页面显示排序start
			Map<String,DictMeterDataStorageInfo> dmdMap = Maps.newHashMap();
			for(int i=0;i<dmdList.size();i++){
				dmdMap.put(dmdList.get(i).getId(), dmdList.get(i));
			}
			for(int i=0;i<tIds.length;i++) {
				DictMeterDataStorageInfo  in = dmdMap.get(tIds[i]);
				viewField+=",a.VALUE"+in.getFieldIndex()+" as value"+(i+1);
				Excel value = ExcelUtils.createExcel(ddList1.get(i).getName(), ddList1.get(i).getName().length());
				excels.put("value"+(i+1), value);
			}
			List<MeterDataReport> list= Lists.newArrayList();
			List<Map<String, Object>> lists= Lists.newArrayList();
        	if("Commnuicator".equalsIgnoreCase(meterType)){
				AssetCommunicator communicator = new AssetCommunicator();
				communicator.setSn(serizlNames);
				communicator = assetCommunicatorService.get(communicator);

				if(communicator!=null){
					AssetMeter entity = new AssetMeter();
					entity.setCommunicatorId(communicator.getId());
					List<AssetMeter> meterList = assetMeterService.getList(entity);
					Map<String,String> meterMap = Maps.newHashMap();

					if(meterList !=null && meterList.size() > 0){
						if(this.jgrt.equals(null) || this.meterMap.equals(null)){
							return ;
						}
						 list =dictMeterDataStorageTableService.getForJqGridCommnuicatorExcel(this.jgrt,this.meterMap);

             		 }else{
						if(this.params.equals(null)){
							return ;
						}
						 lists =  dictMeterDataStorageTableService.getMeterDateList(this.params);
						 for (int i = 0; i < lists.size(); i++){
							 MeterDataReport mdrts = new MeterDataReport();
							 mdrts.setSerizlName((String) lists.get(i).get("serizlName"));
							 mdrts.setMeterName((String) lists.get(i).get("meterName"));
							 SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
							 Date dates = formatter.parse((String) lists.get(i).get("times"));
							 mdrts.setTimes(dates);
							 list.add(mdrts);
						 }
					 }
                 }
        	}else if("Line".equalsIgnoreCase(meterType)){//线路
				AssetEntityRelationship   ship = new AssetEntityRelationship();
				ship.setParentId(deviceId);
				ship.setParentType(3);
				ship.setType(1);
				List<AssetEntityRelationship> shipList= this.assetEntityRelationshipService.getList(ship);
				Map<String,String> meterMap = Maps.newHashMap();
				if(shipList!=null&&shipList.size()>0) {
					if(this.jgrt.equals(null) || this.meterMap.equals(null)){
						return ;
					}
					list = dictMeterDataStorageTableService.getForJqGridCommnuicatorExcel(this.jgrt,this.meterMap);
				}else{
					if(this.params.equals(null)){
						return ;
					}
					list = dictMeterDataStorageTableService.getMeterDateListExcel(this.params);

				}
        	 }else if("Transformer".equalsIgnoreCase(meterType)){//变压器	
				AssetEntityRelationship   ship = new AssetEntityRelationship();
				ship.setParentId(deviceId);
				ship.setParentType(4);
				ship.setType(1);
				List<AssetEntityRelationship> shipList= this.assetEntityRelationshipService.getList(ship);
				if(shipList!=null&&shipList.size()>0) {
					if(this.meterMap.equals(null)){
						return ;
					}
					list =dictMeterDataStorageTableService.getForJqGridCommnuicatorExcel(this.jgrt,this.meterMap);
				}else{
					if(this.params.equals(null)){
						return ;
					}
					list = dictMeterDataStorageTableService.getMeterDateListExcel(this.params);

				}
	        }
   	        if("Meter".equalsIgnoreCase(meterType)){
				if(this.jgrt.equals(null)){
					return ;
				}
				if(this.tType==0){
					list=dictMeterDataStorageTableService.getForJqGridMeterExcel(this.jgrt);
				}else if(this.tType==1){
					list=dictMeterDataStorageTableService.getForJqGridMeterOnlyShenyuExcel(this.jgrt);
				}else if(this.tType==2){
					list=dictMeterDataStorageTableService.getForJqGridMeterMonthShenyuExcel(this.jgrt);
				}


   	        }else if("Customer".equalsIgnoreCase(meterType)){//客户
	        	 AssetCustomer customer= this.assetCustomerService.getEntity(deviceId);
		       	 if(StringUtils.isNotEmpty(customer.getMeterId())) {
			       	 meterDataReport.setId(customer.getMeterId());
					 list=dictMeterDataStorageTableService.getMeterDataList(meterDataReport);
		       	 }
	        }
  
	        if(list.size()<=0){
	        	MeterDataReport mdr=new MeterDataReport();
	        	list.add(mdr);
	        }
//	        else{
//	        	   for(MeterDataReport meterDataReport1 : list){
//		   	        	meterDataReport1.setValue1(StringUtils.isEmpty(meterDataReport1.getValue1()) ? "" : String.valueOf(new BigDecimal(meterDataReport1.getValue1())));
//		   	        	meterDataReport1.setValue2(StringUtils.isEmpty(meterDataReport1.getValue2()) ? "" : String.valueOf(new BigDecimal(meterDataReport1.getValue2())));
//		   	        	meterDataReport1.setValue3(StringUtils.isEmpty(meterDataReport1.getValue3()) ? "" : String.valueOf(new BigDecimal(meterDataReport1.getValue3())));
//		   	        	meterDataReport1.setValue4(StringUtils.isEmpty(meterDataReport1.getValue4()) ? "" : String.valueOf(new BigDecimal(meterDataReport1.getValue4())));
//
//		   	        }
//	        }
	        ExcelDataFormatter edf = new ExcelDataFormatter();
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("times", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
	        edf.set("times", tvs);
	        if(eType.equals("0")){
				CreatePdf.printPdf(list, edf,excels, request, response);
			}else{
				ExcelUtils.writeToFile(list, edf,"excelMeterDataList.xlsx",response, excels);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		 
	 }
	 /**
	  * 导出
	  * @param dataIntegrity
	  * @param request
	  * @param response
	  */
	 @RequestMapping(value = "getExcelMeterDataReport")
	 @ResponseBody
	 public void getExcelMeterDataReport(String deviceId,String groupId,String channel,String times_start,String times_end,String meterType,String searchTimeType,MeterDataReport meterDataReport,HttpServletRequest request, HttpServletResponse response) {
		 Map<String,Object> m = new HashMap<String, Object>();
		 try {
			 if(StringUtil.isNotEmpty(times_start)){
				 m.put("times_start", DateUtils.parseDate(times_start, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
			 }
			 if(StringUtil.isNotEmpty(times_end)){
				 m.put("times_end", DateUtils.parseDate(times_end, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
			 }
			 String tableName="";
			 if(!StringUtil.isNotEmpty(channel)){
				 return ;
			 }
			 String[] tIds=channel.split(",");
			 String dataitemId=tIds[0];
			 List<DictDataitem> ddList=new ArrayList<DictDataitem>();
			 if(tIds.length>0){
				 DictMeterDataStorageInfo dictMeterDataStorageInfo =dictMeterDataStorageInfoService.getEntity(tIds[0]);
				 if(dictMeterDataStorageInfo==null){
					 List<MeterDataReport> list= new ArrayList();
					 ExcelDataFormatter edf = new ExcelDataFormatter();
					 //要日期格式化使用以下方式
					 Map<String,String> tvs=new HashMap<String, String>();
					 tvs.put("date", "MM/dd/yyyy HH:mm:ss");
					 edf.set("date", tvs);
					 ExcelUtils.writeToFile(list, edf, "excelMeterDataList.xlsx", response);
					 return;
				 }
				 DictMeterDataStorageTable dictMeterDataStorageTable=dictMeterDataStorageTableService.getEntity(dictMeterDataStorageInfo.getTableId());
				 if(dictMeterDataStorageTable==null){
					 List<MeterDataReport> list= new ArrayList();
					 ExcelDataFormatter edf = new ExcelDataFormatter();
					 //要日期格式化使用以下方式
					 Map<String,String> tvs=new HashMap<String, String>();
					 tvs.put("date", "MM/dd/yyyy HH:mm:ss");
					 edf.set("date", tvs);
					 ExcelUtils.writeToFile(list, edf, "excelMeterDataList.xlsx", response);
					 return;
				 }
				 tableName=dictMeterDataStorageTable.getName();
			 }
			 
			 DictDataitem dd=new DictDataitem();
			 dd.put("groupId",groupId);
			 ddList=dictDataitemService.getList(dd);
			 List<DictDataitem> ddList1=new ArrayList<DictDataitem>();
			 
			 for(DictDataitem dictDataitem : ddList){
				 if(Arrays.asList(tIds).contains(dictDataitem.getId())){
					 ddList1.add(dictDataitem);
				 }
			 }
			 
			 Map<String,Object> dsm=new HashMap<String, Object>();
			 dsm.put("ids", tIds);
			 DictMeterDataStorageInfo dmd=new DictMeterDataStorageInfo();
			 dmd.setExtData(dsm);
			 String viewField="";
			 List<DictMeterDataStorageInfo> dmdList= dictMeterDataStorageInfoService.getList(dmd);
			 Map<String,Excel> excels=new HashMap<String, Excel>();
			 Excel times=ExcelUtils.createExcel("Time", 22);
			 excels.put("times", times);
			 Excel serizlName=ExcelUtils.createExcel("Serial Number", 15);
			 excels.put("serizlName", serizlName);
//			 for(int i=0;i<dmdList.size();i++){
//				 Excel value = ExcelUtils.createExcel(ddList1.get(i).getName(), ddList1.get(i).getName().length());
//				 excels.put("value"+(i+1), value);
//			 }
//			 
//			 
//			 for(int i=0;i<dmdList.size();i++){
//		        	viewField+=",a.VALUE"+dmdList.get(i).getFieldIndex()+" as value"+(i+1);
//		        }
			//根据页面显示排序start
	        Map<String,DictMeterDataStorageInfo> dmdMap = Maps.newHashMap();
	        for(int i=0;i<dmdList.size();i++){
	        	dmdMap.put(dmdList.get(i).getId(), dmdList.get(i));
	        }
	        for(int i=0;i<tIds.length;i++) {
	        	DictMeterDataStorageInfo  in = dmdMap.get(tIds[i]);
	        	viewField+=",a.VALUE"+in.getFieldIndex()+" as value"+(i+1);
	        	Excel value = ExcelUtils.createExcel(ddList1.get(i).getName(), ddList1.get(i).getName().length());
	        	excels.put("value"+(i+1), value);
	        }
	        //根据页面显示排序end
			 
			 m.put("tableName", tableName);
			 m.put("viewField", viewField);
			 List<String> meterIds = Lists.newArrayList();
			 if("Commnuicator".equalsIgnoreCase(meterType)){
				 AssetCommunicator communicatorEntity = new AssetCommunicator();
				 communicatorEntity.setSn(meterDataReport.getSerizlName());
				 AssetCommunicator communicator = assetCommunicatorService.get(communicatorEntity);
				 if(communicator!=null){
					 AssetMeter entity = new AssetMeter();
					 entity.setCommunicatorId(communicator.getId());
					 List<AssetMeter> meterList = assetMeterService.getList(entity);
					 if(meterList!=null){
						 for(AssetMeter assetMeter : meterList){
							 meterIds.add(assetMeter.getId());
						 }
					 }
				 }
			 }else if("Line".equalsIgnoreCase(meterType)){//线路
	           	 AssetEntityRelationship   ship = new AssetEntityRelationship();
	           	 ship.setParentId(deviceId);
	           	 ship.setParentType(3);
	           	 ship.setType(1);
	           	 List<AssetEntityRelationship> shipList= this.assetEntityRelationshipService.getList(ship);
	          		if(shipList!=null&&shipList.size()>0) {
	         			 for(AssetEntityRelationship shipTmp:shipList) {
	         				 meterIds.add(shipTmp.getId());
	         			 }
	         			
	         		 }
			 }else if("Transformer".equalsIgnoreCase(meterType)){//变压器	
	           	 AssetEntityRelationship   ship = new AssetEntityRelationship();
	           	 ship.setParentId(deviceId);
	           	 ship.setParentType(4);
	           	 ship.setType(1);
	           	 List<AssetEntityRelationship> shipList= this.assetEntityRelationshipService.getList(ship);
	          		if(shipList!=null&&shipList.size()>0) {
	          			 for(AssetEntityRelationship shipTmp:shipList) {
	          				 meterIds.add(shipTmp.getId());
	          			 }
	       			 }
			 	} 

			 
			 meterDataReport.setExtData(m);
			 List<MeterDataReport> list= Lists.newArrayList();
			 if("Meter".equalsIgnoreCase(meterType)){
				 AssetMeter meter = new AssetMeter();
				 meter.setSn(meterDataReport.getSerizlName());
				 AssetMeter t = assetMeterService.get(meter);
				 meterDataReport.setId(t.getId());
				 list=dictMeterDataStorageTableService.getMeterDataList(meterDataReport);
				 if(!"0".equals(searchTimeType)) {
					for (Iterator<MeterDataReport> iterator = list.iterator(); iterator.hasNext();) {
						MeterDataReport meterDataReportTmp = (MeterDataReport) iterator.next();
						//如果不符合格式就删除
						if(!meterDataReportTmp.isRight(searchTimeType)) {
							iterator.remove();
						}
					}
				}
			 }else if("Customer".equalsIgnoreCase(meterType)){//客户
	        	 AssetCustomer customer= this.assetCustomerService.getEntity(deviceId);
		       	 if(StringUtils.isNotEmpty(customer.getMeterId())) {
			       	 meterDataReport.setId(customer.getMeterId());
					 list=dictMeterDataStorageTableService.getMeterDataList(meterDataReport);
		       	 }
			 }else{
				 if(meterIds!=null&&meterIds.size()>0) {
					 m.put("deviceIds", meterIds);
					 list=dictMeterDataStorageTableService.getMeterDataListByCommnuicator(meterDataReport);
				 }
			 }
			 
			 if(list.size()<=0){
				 MeterDataReport mdr=new MeterDataReport();
				 list.add(mdr);
			 }
			 
			 ExcelDataFormatter edf = new ExcelDataFormatter();
			 Map<String,String> tvs=new HashMap<String, String>();
			 tvs.put("times",  ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
			 edf.set("times", tvs);
			 ExcelUtils.writeToFile(list, edf,"excelMeterDataList.xlsx",response, excels);
		 } catch (Exception e) {
			 e.printStackTrace();
		 }
		 
	 }
	 
	 
	 /**
	  * 打印
	  * @param dataIntegrity
	  * @param request
	  * @param response
	  */
	 @RequestMapping(value = "getPrintMeterDataReport")
	 @ResponseBody
	 public void getPrintMeterDataReport(String deviceId,String groupId,String channel,String times_start,String times_end,String meterType,String searchTimeType,MeterDataReport meterDataReport,HttpServletRequest request, HttpServletResponse response) {
		 Map<String,Object> m=new HashMap<String, Object>();
		 try {
			 if(StringUtil.isNotEmpty(times_start)){
				 m.put("times_start", DateUtils.parseDate(times_start, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
			 }
			 if(StringUtil.isNotEmpty(times_end)){
				 m.put("times_end", DateUtils.parseDate(times_end, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
			 }
			 String tableName="";
			 if(!StringUtil.isNotEmpty(channel)){
				 return ;
			 }
			 String[] tIds=channel.split(",");
			 List<DictDataitem> ddList=new ArrayList<DictDataitem>();
			 if(tIds.length>0){
				 DictMeterDataStorageInfo dictMeterDataStorageInfo =dictMeterDataStorageInfoService.getEntity(tIds[0]);
				 if(dictMeterDataStorageInfo==null){
					 return ;
				 }
				 DictMeterDataStorageTable dictMeterDataStorageTable=dictMeterDataStorageTableService.getEntity(dictMeterDataStorageInfo.getTableId());
				 if(dictMeterDataStorageTable==null){
					 return ;
				 }
				 tableName=dictMeterDataStorageTable.getName();
			 }
			 
			 DictDataitem dd=new DictDataitem();
			 dd.put("groupId",groupId);
			 ddList=dictDataitemService.getList(dd);
			 Map<String,Object> dsm=new HashMap<String, Object>();
			 dsm.put("ids", tIds);
			 String dataitemId=tIds[0];
			 DictMeterDataStorageInfo dmd=new DictMeterDataStorageInfo();
			 dmd.setExtData(dsm);
			 List<DictMeterDataStorageInfo> dmdList= dictMeterDataStorageInfoService.getList(dmd);
			 List<DictDataitem> ddList1=new ArrayList<DictDataitem>();
			 for(DictDataitem dictDataitem : ddList){
				 if(Arrays.asList(tIds).contains(dictDataitem.getId())){
					 ddList1.add(dictDataitem);
				 }
			 }
			 String viewField="";
			 Map<String,Excel> excels=new HashMap<String, Excel>();
			 Excel times=ExcelUtils.createExcel("Time", 22);
			 excels.put("times", times);
			 Excel serizlName=ExcelUtils.createExcel("Serial Number", 15);
			 excels.put("serizlName", serizlName);
			 
//			 for(int i=0;i<dmdList.size();i++){
//				 Excel value = ExcelUtils.createExcel(ddList1.get(i).getName(), ddList1.get(i).getName().length());
//				 excels.put("value"+(i+1), value);
//			 }
//			 
//			 
//			 for(int i=0;i<dmdList.size();i++){
//		        	viewField+=",a.VALUE"+dmdList.get(i).getFieldIndex()+" as value"+(i+1);
//		        }
			 
			//根据页面显示排序start
	        Map<String,DictMeterDataStorageInfo> dmdMap = Maps.newHashMap();
	        for(int i=0;i<dmdList.size();i++){
	        	dmdMap.put(dmdList.get(i).getId(), dmdList.get(i));
	        }
	        for(int i=0;i<tIds.length;i++) {
	        	DictMeterDataStorageInfo  in = dmdMap.get(tIds[i]);
	        	viewField+=",a.VALUE"+in.getFieldIndex()+" as value"+(i+1);
	        	Excel value = ExcelUtils.createExcel(ddList1.get(i).getName(), ddList1.get(i).getName().length());
	        	excels.put("value"+(i+1), value);
	        }
	        //根据页面显示排序end
	        
			 m.put("tableName", tableName);
			 m.put("viewField", viewField);
			 
			 
			 List<String> meterIds = Lists.newArrayList();
			 if("Commnuicator".equalsIgnoreCase(meterType)){
				 AssetCommunicator communicatorEntity = new AssetCommunicator();
				 communicatorEntity.setSn(meterDataReport.getSerizlName());
				 AssetCommunicator communicator = assetCommunicatorService.get(communicatorEntity);
				 if(communicator!=null){
					 AssetMeter entity = new AssetMeter();
					 entity.setCommunicatorId(communicator.getId());
					 List<AssetMeter> meterList = assetMeterService.getList(entity);
					
					 if(meterList!=null&&meterList.size()>0){
						 for(AssetMeter assetMeter : meterList){
							 meterIds.add(assetMeter.getId());
						 }
					 }
				 }
			 }else if("Line".equalsIgnoreCase(meterType)){//线路
	           	 AssetEntityRelationship   ship = new AssetEntityRelationship();
	           	 ship.setParentId(deviceId);
	           	 ship.setParentType(3);
	           	 ship.setType(1);
	           	 List<AssetEntityRelationship> shipList= this.assetEntityRelationshipService.getList(ship);
	          		if(shipList!=null&&shipList.size()>0) {
	         			 for(AssetEntityRelationship shipTmp:shipList) {
	         				 meterIds.add(shipTmp.getId());
	         			 }
	         			
	         		 }
			 }else if("Transformer".equalsIgnoreCase(meterType)){//变压器	
	           	 AssetEntityRelationship   ship = new AssetEntityRelationship();
	           	 ship.setParentId(deviceId);
	           	 ship.setParentType(4);
	           	 ship.setType(1);
	           	 List<AssetEntityRelationship> shipList= this.assetEntityRelationshipService.getList(ship);
	          		if(shipList!=null&&shipList.size()>0) {
	          			 for(AssetEntityRelationship shipTmp:shipList) {
	          				 meterIds.add(shipTmp.getId());
	          			 }
	       			 }
	        } 
			 meterDataReport.setExtData(m);
			 
			 List<MeterDataReport> list= Lists.newArrayList();
			 if("Meter".equalsIgnoreCase(meterType)){
				 AssetMeter meter = new AssetMeter();
				 meter.setSn(meterDataReport.getSerizlName());
				 AssetMeter t = assetMeterService.get(meter);
				 meterDataReport.setId(t.getId());
				 list=dictMeterDataStorageTableService.getMeterDataList(meterDataReport);
				 if(!"0".equals(searchTimeType)) {
					for (Iterator<MeterDataReport> iterator = list.iterator(); iterator.hasNext();) {
						MeterDataReport meterDataReportTmp = (MeterDataReport) iterator.next();
						//如果不符合格式就删除
						if(!meterDataReportTmp.isRight(searchTimeType)) {
							iterator.remove();
						}
					}
				}
			 }else if("Customer".equalsIgnoreCase(meterType)){//客户
	        	 AssetCustomer customer= this.assetCustomerService.getEntity(deviceId);
		       	 if(StringUtils.isNotEmpty(customer.getMeterId())) {
			       	 meterDataReport.setId(customer.getMeterId());
					 list=dictMeterDataStorageTableService.getMeterDataList(meterDataReport);
		       	 }
			 }else{
				 if(meterIds!=null&&meterIds.size()>0) {
					 m.put("deviceIds", meterIds);
					 list=dictMeterDataStorageTableService.getMeterDataListByCommnuicator(meterDataReport);
				 }
			 }
			 
			 if(list.size()<=0){
				 MeterDataReport mdr=new MeterDataReport();
				 list.add(mdr);
			 }else{
				 for(MeterDataReport meterDataReport1 : list){
					 meterDataReport1.setValue1(StringUtils.isEmpty(meterDataReport1.getValue1()) ? "" : String.valueOf(new BigDecimal(meterDataReport1.getValue1()))); 
					 meterDataReport1.setValue2(StringUtils.isEmpty(meterDataReport1.getValue2()) ? "" : String.valueOf(new BigDecimal(meterDataReport1.getValue2())));
					 meterDataReport1.setValue3(StringUtils.isEmpty(meterDataReport1.getValue3()) ? "" : String.valueOf(new BigDecimal(meterDataReport1.getValue3())));
					 meterDataReport1.setValue4(StringUtils.isEmpty(meterDataReport1.getValue4()) ? "" : String.valueOf(new BigDecimal(meterDataReport1.getValue4())));
					 
				 }
			 }
			 ExcelDataFormatter edf = new ExcelDataFormatter();
			 Map<String,String> tvs=new HashMap<String, String>();
			 tvs.put("times", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
			 edf.set("times", tvs);
			 CreatePdf.printPdf(list, edf,excels, request, response);
		 } catch (Exception e) {
			 e.printStackTrace();
		 }
		 
	 }
    
	 



private static Date regularTime(Date starTime,Date endTime,AssetMeasurementProfile measurementGroup,List<Date> timeList) {  
		int freq = Integer.parseInt(measurementGroup.getProfileCycle());
	    String freqType = measurementGroup.getProfileCycleType();
	    freqType = freqType.toLowerCase();
	   
	    Calendar cal = Calendar.getInstance();
	    cal.setTime(starTime);
	    
	    switch (freqType) {
	    case "minutely": {
	    	int minute = cal.get(Calendar.MINUTE);
	    	if (minute != 0) {
	    		minute = (minute / freq) * freq;
	    		cal.set(Calendar.MINUTE, minute);
	    		cal.set(Calendar.SECOND, 0);
	    		cal.set(Calendar.MILLISECOND, 0);
	    	}
	    	
    		Date tmpDate= cal.getTime();
    		timeList.add(tmpDate);	
			while(!tmpDate.after(endTime)) {
				tmpDate=org.apache.commons.lang.time.DateUtils.addMinutes(tmpDate, freq);
				if(!tmpDate.after(endTime)) {
					timeList.add(tmpDate);	
				}
			}
			
	    	break;
	    }
	    case "hourly": {
    		cal.set(Calendar.MINUTE, 0);
    		cal.set(Calendar.SECOND, 0);
    		cal.set(Calendar.MILLISECOND, 0);
    		Date tmpDate= cal.getTime();
    		timeList.add(tmpDate);	
			while(!tmpDate.after(endTime)) {
				tmpDate=org.apache.commons.lang.time.DateUtils.addHours(tmpDate, freq);
				if(!tmpDate.after(endTime)) {
					timeList.add(tmpDate);	
				}
			}
	    	break;
	    }
	    case "daily": {
	    	cal.set(Calendar.HOUR_OF_DAY, 0);
    		cal.set(Calendar.MINUTE, 0);
    		cal.set(Calendar.SECOND, 0);
    		cal.set(Calendar.MILLISECOND, 0);
    		Date tmpDate= cal.getTime();
    		timeList.add(tmpDate);	
    		while(!tmpDate.after(endTime)) {
				tmpDate=org.apache.commons.lang.time.DateUtils.addDays(tmpDate, freq);
				if(!tmpDate.after(endTime)) {
					timeList.add(tmpDate);	
				}
			}
	    	break;
	    }
	    case "monthly": {
	    	cal.set(Calendar.DAY_OF_MONTH, 1);
	    	cal.set(Calendar.HOUR_OF_DAY, 0);
    		cal.set(Calendar.MINUTE, 0);
    		cal.set(Calendar.SECOND, 0);
    		cal.set(Calendar.MILLISECOND, 0);
    		Date tmpDate= cal.getTime();
    		timeList.add(tmpDate);	
    		while(!tmpDate.after(endTime)) {
				tmpDate=org.apache.commons.lang.time.DateUtils.addMonths(tmpDate, freq);
				if(!tmpDate.after(endTime)) {
					timeList.add(tmpDate);	
				}
			}
	    	break;
	    }
    	default:
    		break;
	    }
		return cal.getTime();   
	}

	public AssetMeasurementProfile getMearurementProfile(String meterId,String dataitemId) {
		//找出采集频率   ASSET_METER_GROUP_MAP  ASSET_MEASUREMENT_PROFILE
		AssetMeterGroupMap group = new AssetMeterGroupMap();
		group.setId(meterId);
		group.setType("1");
		group=	assetMeterGroupMapService.get(group);
		String groupId = group.getGroupId();
		String protocolId=ResourceUtil.getSessionattachmenttitle("protocol.id");
		if(!"100".equals(protocolId)) {
			dataitemId=this.dictDataitemService.getDataitemBySubId(dataitemId, protocolId);
			//查出对应数据项id 
		}
		//找出曲线采集频率
//		AssetMeasurementProfileDi profileDiTmp = new AssetMeasurementProfileDi();
//		profileDiTmp.setMgId(groupId);
//		profileDiTmp.setDataitemId(dataitemId);
//		profileDiTmp= assetMeasurementProfileDiService.get(profileDiTmp);
//		
//     	String profileId = profileDiTmp.getProfileId();
//     	AssetMeasurementProfile profileTmp = new AssetMeasurementProfile();
//		profileTmp.setMgId(groupId);
//		profileTmp.setProfileId(profileId);
		return assetMeasurementProfileService.getProfileByMgAndDataItemAndType(groupId, dataitemId, "1");
	}
	
	/**
	 * 获取选择显示列表树形框数据
	 * @param searchName
	 * @param request
	 * @return
	 * @throws ParseException 
	 */
	@RequestMapping(value = "isOver200")
	@ResponseBody
	public AjaxJson isOver200(String channel,String times_start,String times_end,String meterId,HttpServletRequest request, HttpServletResponse response) throws ParseException {
		AjaxJson j = new AjaxJson();
		boolean flag=false;
		String[] tIds=channel.split(",");
		AssetMeasurementProfile prile= getMearurementProfile(meterId,tIds[0]) ;
		if(prile!=null) {
			 List<Date> timeList = Lists.newArrayList();
		        regularTime(DateUtils.parseDate(times_start, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),DateUtils.parseDate(times_end, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")), prile, timeList);
		        //如果大于200条，不处理
		        if(timeList.size()>200) {
		        	flag=true;
				}
		}
		j.setSuccess(flag);
		return j;
	}
}