var trflag = false;
var transformerId;

var tr_calcOrgId;
var tr_calcFlag = false;

function queryTransformerDataFun(){
	var sn = $("#other_transformerSn").val();
	var name = $("#other_transformerName").val();
	var orgId= $("#tran_txt_org_id").val();
	var data = {sn:sn,name:name,flag:trflag,orgId:orgId};
	return data;
}

function queryTransformerDataOnclick() {
	trflag = true;
	other_transformerListsearchOnEnterFn();
}

function onkeypress_Tr(){
	if(event.keyCode == 13){
		trflag = true;
		other_transformerListsearchOnEnterFn();
	}
}

function editTrRowData(rowid,iRow,iCol,e){
	if(rowid == null || rowid == ""){
		//middleErrorMsg(i18n.t("login.usernameNull"));
		alert("rowid is null");
		return;
	}
	// 为 Basic Information 赋值
	var rowData = $("#other_transformerList").jqGrid('getRowData',rowid);
	$("#tr_span").html(rowData.sn +" "+ i18n.t("lineManagementList.properties"));
	$("#calcTransformerId").val(rowData.id);
	$("#trSn").val(rowData.sn);
	$("#tranOrgId").val(rowData.orgId);
	$("#trName").val(rowData.name);
	$("#trOrganization").val(rowData.orgName);
	$("#trRatedCapacity").val(rowData.ratedCapacity);
	$("#trAddress").val(rowData.addr);
	
	if(rowData.id != transformerId) {
		// 重置 calc Object map 数据项
		$("#tr_calcObjId").val("");
		$("#tr_calcObjProSpan").html(i18n.t("lineManagementList.calculationObjectProperties"));
		
		$("#tr_txt_map_name").val("");
		$("#tr_txt_map_type").val("");
		$("#tr_txt_map_cycle").val("");
		//tr_calculationObjectMapListsearchOnEnterFn();
	}
	
	transformerId = rowData.id;
	$('#relTransformerId').val(transformerId);
	tr_calcFlag = true;
	tr_calcOrgId = rowData.orgId;
	tr_calculationObjectListsearchOnEnterFn();
	queryLinkedMeterByTransformerOnclick();
}

function tr_queryCalcDataFun(){
	var data = {id:transformerId,orgId:tr_calcOrgId,calcFlag:tr_calcFlag};
	return data;
}

function tr_editCalcObjRowData(rowid,iRow,iCol,e){
	if(rowid == null || rowid == ""){
		//middleErrorMsg(i18n.t("login.usernameNull"));
		alert("rowid is null");
		return;
	}
	var rowData = $("#tr_calculationObjectList").jqGrid('getRowData',rowid);
	$("#tr_calcObjProSpan").html(i18n.t("lineManagementList.calculationObjectProperties") +" - " +rowData.name);
	$("#tr_txt_map_name").val(rowData.name);
	$("#tr_calcObjId").val(rowData.id);
	$("#tr_txt_map_type").val(rowData.type);
	$("#tr_txt_map_cycle").val(rowData.tvType);
	
	//tr_calculationObjectMapListsearchOnEnterFn();
}

function tr_queryCalcMapDataFun(){
	var objId = $("#tr_calcObjId").val();
//	if(null == objId || "" == objId){
//		return;
//	}
	
	var data = {objId:objId};
	return data;
}

function tr_deleteCalcObj(id){
	var rowData = $("#tr_calculationObjectList").jqGrid("getRowData", id);
	
	var close_link = window.parent.layer.confirm(i18n.t("system.aydeltherdate"), 
		{
			btn: [i18n.t("system.determine"), i18n.t("system.cancel")],
			title: i18n.t("system.delete")
		}, function() {
			window.parent.layer.close(close_link);
			layer.load();
			$.ajax({
				async : true,
	            cache : false,
	            traditional: true,
				type: 'POST',
				url: getRootPathWeb()+'/assetLineManagementController/delCalcObj.do?&id=' + rowData.id,
				dataType: 'json',
				success: function(data) {
					if(data.success) {
						$("#tr_calculationObjectList").trigger('reloadGrid');	//刷新界面
						$("#tr_calculationObjectMapList").trigger('reloadGrid');	//刷新界面
						middleTipMsg(data.msg,true);
						
						var calId = $("#tr_calcObjId").val();
						
						if(rowData.id==calId) {
							$("#tr_calcObjId").val("");
							$("#tr_calcObjProSpan").html(i18n.t("lineManagementList.calculationObjectProperties"));
							//$("#viewspan").html("");
							
							$("#tr_txt_map_name").val("");
							$("#tr_txt_map_type").val("");
							$("#tr_txt_map_cycle").val("");
						}
					} else {
						window.parent.layer.msg(data.msg, {icon: 2});
					}
					layer.closeAll('loading');
				},
				error: function(msg) {
					window.parent.layer.msg(i18n.t("system.requestError"), {icon: 2});
					layer.closeAll('loading');
				}
			});
		});
}

function deleteTransformer(id){
	var rowData = $("#other_transformerList").jqGrid("getRowData", id);
	
	var close_link = window.parent.layer.confirm(i18n.t("system.aydeltherdate"), 
		{
			btn: [i18n.t("system.determine"), i18n.t("system.cancel")],
			title: i18n.t("system.delete")
		}, function() {
			window.parent.layer.close(close_link);
			layer.load();
			$.ajax({
				async : true,
	            cache : false,
	            traditional: true,
				type: 'POST',
				url: getRootPathWeb()+'/assetTransformerController/del.do?&id=' + rowData.id,
				dataType: 'json',
				success: function(data) {
					if(data.success) {
						$("#other_transformerList").trigger('reloadGrid');	//刷新界面
						$("#tr_calculationObjectList").trigger('reloadGrid');	//刷新界面
						$("#tr_calculationObjectMapList").trigger('reloadGrid');	//刷新界面
						middleTipMsg(data.msg,true);
						
						if(rowData.id==transformerId) {
							transformerId = "";
							tr_calcOrgId = "";
							$("#tr_calcObjId").val("");
							
							$("#tr_calcObjProSpan").html(i18n.t("lineManagementList.calculationObjectProperties"));
							$("#tr_span").html("");
							
							$("#tr_txt_map_name").val("");
							$("#tr_txt_map_type").val("");
							$("#tr_txt_map_cycle").val("");
						}
					} else {
						window.parent.layer.msg(data.msg, {icon: 2});
					}
					layer.closeAll('loading');
				},
				error: function(msg) {
					window.parent.layer.msg(i18n.t("system.requestError"), {icon: 2});
					layer.closeAll('loading');
				}
			});
		});
}