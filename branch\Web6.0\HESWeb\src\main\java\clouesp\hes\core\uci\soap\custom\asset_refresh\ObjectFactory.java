
package clouesp.hes.core.uci.soap.custom.asset_refresh;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the clouesp.hes.core.uci.soap.custom.asset_refresh package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _Refresh_QNAME = new QName("http://asset_refresh.custom.soap.uci.core.hes.clouesp/", "refresh");
    private final static QName _RefreshResponse_QNAME = new QName("http://asset_refresh.custom.soap.uci.core.hes.clouesp/", "refreshResponse");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: clouesp.hes.core.uci.soap.custom.asset_refresh
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link Refresh }
     * 
     */
    public Refresh createRefresh() {
        return new Refresh();
    }

    /**
     * Create an instance of {@link RefreshResponse }
     * 
     */
    public RefreshResponse createRefreshResponse() {
        return new RefreshResponse();
    }

    /**
     * Create an instance of {@link RefreshMessage }
     * 
     */
    public RefreshMessage createRefreshMessage() {
        return new RefreshMessage();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Refresh }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://asset_refresh.custom.soap.uci.core.hes.clouesp/", name = "refresh")
    public JAXBElement<Refresh> createRefresh(Refresh value) {
        return new JAXBElement<Refresh>(_Refresh_QNAME, Refresh.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RefreshResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://asset_refresh.custom.soap.uci.core.hes.clouesp/", name = "refreshResponse")
    public JAXBElement<RefreshResponse> createRefreshResponse(RefreshResponse value) {
        return new JAXBElement<RefreshResponse>(_RefreshResponse_QNAME, RefreshResponse.class, null, value);
    }

}
