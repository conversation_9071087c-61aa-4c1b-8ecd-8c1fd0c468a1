{"version": "0.2.0", "configurations": [{"type": "java", "name": "TaskPullEventDlmsHandlerElster", "request": "launch", "mainClass": "clouesp.hes.core.schedules.Third.TaskPullEventDlmsHandlerElster", "projectName": "clouesp.hes.core-Schedules"}, {"name": "Calculation - 阿里云", "type": "java", "request": "attach", "hostName": "**************", "port": 8993, "sourceRoots": ["${workspaceFolder}/Core/Calculation"]}, {"name": "Calculation - 长沙内部", "type": "java", "request": "attach", "hostName": "*************", "port": 8993, "sourceRoots": ["${workspaceFolder}/Core/Calculation"]}, {"name": "Schedules - 阿里云", "type": "java", "request": "attach", "hostName": "**************", "port": 8993, "sourceRoots": ["${workspaceFolder}/Core/Schedules"]}, {"name": "Schedules - 长沙内部", "type": "java", "request": "attach", "hostName": "*************", "port": 8993, "sourceRoots": ["${workspaceFolder}/Core/Schedules"]}, {"name": "Channels - 阿里云", "type": "java", "request": "attach", "hostName": "**************", "port": 8993, "sourceRoots": ["${workspaceFolder}/Core/Channels"]}, {"name": "Channels - 长沙内部", "type": "java", "request": "attach", "hostName": "*************", "port": 8993, "sourceRoots": ["${workspaceFolder}/Core/Channels"]}, {"name": "UCI - 阿里云", "type": "java", "request": "attach", "hostName": "**************", "port": 8993, "sourceRoots": ["${workspaceFolder}/Core/UCI"]}, {"name": "UCI - 长沙内部", "type": "java", "request": "attach", "hostName": "*************", "port": 8993, "sourceRoots": ["${workspaceFolder}/Core/UCI"]}, {"name": "syuci - 阿里云", "type": "java", "request": "attach", "hostName": "**************", "port": 8993, "sourceRoots": ["${workspaceFolder}/Core/syuci"]}, {"name": "syuci - 长沙内部", "type": "java", "request": "attach", "hostName": "*************", "port": 8993, "sourceRoots": ["${workspaceFolder}/Core/syuci"]}]}