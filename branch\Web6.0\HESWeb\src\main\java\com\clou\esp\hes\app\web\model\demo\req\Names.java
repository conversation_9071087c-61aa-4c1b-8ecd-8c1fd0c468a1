package com.clou.esp.hes.app.web.model.demo.req;

import java.io.Serializable;

import javax.jws.soap.SOAPBinding;
import javax.jws.soap.SOAPBinding.Style;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * 孟加拉接口项目请求数据头数据对象
 * 
 * <AUTHOR>
 * 
 */
@SOAPBinding(style = Style.RPC)
@XmlRootElement(name = "Names")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = { "name1", "nameType" })
public class Names implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public Names() {
		super();
	}

	public Names(String name, NameType nameType) {
		super();
		this.name1 = name;
		this.nameType = nameType;
	}

	public String name1;

	public NameType nameType;

	public String getName() {
		return name1;
	}

	public void setName(String name) {
		this.name1 = name;
	}

	public NameType getNameType() {
		return nameType;
	}

	public void setNameType(NameType nameType) {
		this.nameType = nameType;
	}

	@Override
	public String toString() {
		return "Names [name=" + name1 + ", nameType=" + nameType + "]";
	}

}
