<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:p="http://www.springframework.org/schema/p" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
		http://www.springframework.org/schema/context
		http://www.springframework.org/schema/context/spring-context-3.0.xsd
		http://www.springframework.org/schema/mvc
		http://www.springframework.org/schema/mvc/spring-mvc-3.0.xsd 
		http://www.springframework.org/schema/tx
		http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
		http://cxf.apache.org/jaxws
   		http://cxf.apache.org/schemas/jaxws.xsd">
	<!-- 自动扫描该包，使SpringMVC认为包下用了@controller注解的类是控制器 -->  
    <context:component-scan base-package="com.clou.esp.hes.app.web.controller" />  
    <!--避免IE执行AJAX时，返回JSON出现下载文件 -->  
    <bean id="mappingJacksonHttpMessageConverter"  
        class="org.springframework.http.converter.json.MappingJacksonHttpMessageConverter">  
        <property name="supportedMediaTypes">  
            <list>  
                <value>text/html;charset=UTF-8</value>  
            </list>  
        </property>  
    </bean>  
    <!-- 启动SpringMVC的注解功能，完成请求和注解POJO的映射 -->  
    <bean  
        class="org.springframework.web.servlet.mvc.annotation.AnnotationMethodHandlerAdapter">  
        <property name="messageConverters">  
            <list>  
                <ref bean="mappingJacksonHttpMessageConverter" /> <!-- JSON转换器 -->  
            </list>  
        </property>  
    </bean> 
    <!-- spring mvc验证开始 ，若不使用这种配置，需要将错误信息硬编码到代码里，不易修改 -->
	<mvc:annotation-driven validator="validator" />
	<bean
		class="org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter">
		<property name="messageConverters">
			<list>
				<ref bean="mappingJacksonHttpMessage2Converter" />
			</list>
		</property>
	</bean>

	<bean id="mappingJacksonHttpMessage2Converter"
		class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter">
		<property name="supportedMediaTypes">
			<list>
				<bean class="org.springframework.http.MediaType">
					<constructor-arg index="0" value="text" />
					<constructor-arg index="1" value="plain" />
					<constructor-arg index="2" value="UTF-8" />
				</bean>
				<bean class="org.springframework.http.MediaType">
					<constructor-arg index="0" value="*" />
					<constructor-arg index="1" value="*" />
					<constructor-arg index="2" value="UTF-8" />
				</bean>
				<bean class="org.springframework.http.MediaType">
					<constructor-arg index="0" value="text" />
					<constructor-arg index="1" value="*" />
					<constructor-arg index="2" value="UTF-8" />
				</bean>
				<bean class="org.springframework.http.MediaType">
					<constructor-arg index="0" value="application" />
					<constructor-arg index="1" value="json" />
					<constructor-arg index="2" value="UTF-8" />
				</bean>
			</list>
		</property>
	</bean>
	<bean id="validator"
		class="org.springframework.validation.beanvalidation.LocalValidatorFactoryBean">
		<property name="providerClass" value="org.hibernate.validator.HibernateValidator" />
		<!--不设置则默认为classpath下的ValidationMessages.properties -->
		<property name="validationMessageSource" ref="validatemessageSource"/>
	</bean>
	<!-- 校验错误信息配置文件 -->  
	<bean id="validatemessageSource"  
	    class="org.springframework.context.support.ReloadableResourceBundleMessageSource">  
	    <!-- 资源文件名-->  
	    <property name="basenames">     
	         <list>      
	           <value>classpath:ValidationMessages</value>   
	         </list>     
	    </property>  
	    <!-- 资源文件编码格式 -->  
	    <property name="fileEncodings" value="utf-8" />  
	    <!-- 对资源文件内容缓存时间，单位秒 -->  
	    <property name="cacheSeconds" value="120" />  
	</bean>  
	<!-- spring mvc验证结束 --> 
	
    <!-- 定义跳转的文件的前后缀 ，视图模式配置-->  
    <bean id="defaultViewResolver"
		class="org.springframework.web.servlet.view.InternalResourceViewResolver"
		p:order="3">
		<property name="viewClass"
			value="org.springframework.web.servlet.view.JstlView" />
		<property name="contentType" value="text/html" />
		<property name="prefix" value="/WEB-INF/views/modules/" />
		<property name="suffix" value=".jsp" />
	</bean>
    <!-- 配置文件上传，如果没有使用文件上传可以不用配置，当然如果不配，那么配置文件中也不必引入上传组件包 -->  
        <!-- class="org.springframework.web.multipart.commons.CommonsMultipartResolver"> -->
    <bean id="multipartResolver"    
       		class="com.clou.esp.hes.app.web.listener.upload.PJCommonsMultipartResolver">  
        <!-- 默认编码 -->  
        <property name="defaultEncoding" value="utf-8" />    
        <!-- 文件大小最大值 -->  
        <property name="maxUploadSize" value="10485760000" />    
        <!-- 内存中的最大值 -->  
        <property name="maxInMemorySize" value="40960" />    
    </bean>
    <!-- 拦截器 -->
	<mvc:interceptors>
		<mvc:interceptor>
			<mvc:mapping path="/**" />
			<bean class="com.clou.esp.hes.app.web.core.interceptors.EncodingInterceptor" />
		</mvc:interceptor>
		<mvc:interceptor>
			<mvc:mapping path="/**" />
			<bean
				class="com.clou.esp.hes.app.web.core.interceptors.AvoidDuplicateSubmissionInterceptor" />
		</mvc:interceptor>
		<!-- 数据权限拦截 -->
		<mvc:interceptor>
			<mvc:mapping path="/**" />
			<bean class="com.clou.esp.hes.app.web.core.interceptors.DataAuthInterceptor">
				<property name="includeUrls">
					<list>
						<value>systemController/index.do</value>
					</list>
				</property>
			</bean>
		</mvc:interceptor>
	</mvc:interceptors>
    <!-- ApplicationContext -->
	<bean name="springContextUtil" class="com.clou.esp.hes.app.web.core.util.SpringContextUtil"
		scope="singleton"></bean>
    <!--集成cxf webservice接口 -->
	<import resource="classpath:cxf-servlet.xml" /> 
	
	<import resource="classpath:ureport-console-context.xml"/>	
	<bean id="propertyConfigurer" parent="ureport.props">
    <property name="location">
        <value>/WEB-INF/config.properties</value>
    </property>
    </bean>
	<!-- 开启事务注解驱动 -->  
    <tx:annotation-driven transaction-manager="transactionManager"/> 
    <!-- 定义事务管理器  -->
    <bean id="transactionManager"
         class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
       <property name="dataSource" ref="dataSource" />
    </bean>
    
     <bean class="com.clou.esp.hes.app.web.listener.system.InitDataListener"></bean> 
     <bean class="com.clou.esp.hes.app.web.listener.system.FileReportProvider"></bean> 

     <bean id="eneryDataDailyReportBean" class="com.clou.esp.hes.app.web.controller.report.EneryDataDailyReportBean"></bean> 


</beans>