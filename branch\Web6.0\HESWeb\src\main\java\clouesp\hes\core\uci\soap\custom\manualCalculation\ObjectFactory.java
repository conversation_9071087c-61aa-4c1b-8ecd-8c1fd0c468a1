package clouesp.hes.core.uci.soap.custom.manualCalculation;


import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _Dispatch_QNAME = new QName("http://dispatch_task.custom.soap.uci.core.hes.clouesp/", "dispatch");
    private final static QName _DispatchResponse_QNAME = new QName("http://dispatch_task.custom.soap.uci.core.hes.clouesp/", "dispatchResponse");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link Dispatch }
     * 
     */
    public Dispatch createDispatch() {
        return new Dispatch();
    }

    /**
     * Create an instance of {@link DispatchResponse }
     * 
     */
    public DispatchResponse createDispatchResponse() {
        return new DispatchResponse();
    }

    /**
     * Create an instance of {@link DispatchMessage }
     * 
     */
    public DispatchMessage createDispatchMessage() {
        return new DispatchMessage();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Dispatch }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://dispatch_task.custom.soap.uci.core.hes.clouesp/", name = "dispatch")
    public JAXBElement<Dispatch> createDispatch(Dispatch value) {
        return new JAXBElement<Dispatch>(_Dispatch_QNAME, Dispatch.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link DispatchResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://dispatch_task.custom.soap.uci.core.hes.clouesp/", name = "dispatchResponse")
    public JAXBElement<DispatchResponse> createDispatchResponse(DispatchResponse value) {
        return new JAXBElement<DispatchResponse>(_DispatchResponse_QNAME, DispatchResponse.class, null, value);
    }

}
