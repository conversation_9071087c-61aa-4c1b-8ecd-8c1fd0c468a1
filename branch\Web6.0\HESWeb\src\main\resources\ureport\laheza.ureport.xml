<?xml version="1.0" encoding="UTF-8"?><ureport><cell expand="None" name="A1" row="1" col="1"><cell-style font-size="10" forecolor="0,0,0" font-family="宋体" bold="false" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="B1" row="1" col="2"><cell-style font-size="10" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Meter Sn]]></simple-value></cell><cell expand="None" name="C1" row="1" col="3"><cell-style font-size="10" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Event Time]]></simple-value></cell><cell expand="None" name="D1" row="1" col="4"><cell-style font-size="10" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Status]]></simple-value></cell><cell expand="Down" name="A2" row="2" col="1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><expression-value><![CDATA[row() - 1]]></expression-value></cell><cell expand="Down" name="B2" row="2" col="2"><cell-style font-size="10" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="DataMeterSwitchStatus" aggregate="group" property="METER_ID" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="C2" row="2" col="3"><cell-style font-size="10" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="DataMeterSwitchStatus" aggregate="group" property="UPDATE_TV" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="D2" row="2" col="4"><cell-style font-size="10" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="DataMeterSwitchStatus" aggregate="group" property="STATUS" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="A3" row="3" col="1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="B3" row="3" col="2"><cell-style font-size="10" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="C3" row="3" col="3"><cell-style font-size="10" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="D3" row="3" col="4"><cell-style font-size="10" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><row row-number="1" height="18"/><row row-number="2" height="18"/><row row-number="3" height="18"/><column col-number="1" width="74"/><column col-number="2" width="151"/><column col-number="3" width="123"/><column col-number="4" width="123"/><datasource name="HesDataSource" type="jdbc" username="hes" password="hes" url="************************************" driver="oracle.jdbc.OracleDriver"><dataset name="DataMeterSwitchStatus" type="sql"><sql><![CDATA[
${
    if(param("assetType") == 'Commnuicator'){
return "select t.meter_id,t.update_tv,decode(t.switch_status,1,\'Closed\',\'Connected\') as status from data_meter_switch_status t
inner join asset_meter t1 on t1.sn = t.meter_id and t1.sn =:sn
inner join sys_org t2 on t1.org_id = t2.id and t2.id in(:orgIds)
where 1=1
and t.update_tv between to_date(:time,\'MM-DD-YYYY\') AND to_date(:time,\'MM-DD-YYYY\')+1";
    }else{
                                return "select t.meter_id,t.update_tv,decode(t.switch_status,1,\'Closed\',\'Connected\') as status from data_meter_switch_status t
inner join asset_meter t1 on t1.sn = t.meter_id and t1.sn =:sn
inner join sys_org t2 on t1.org_id = t2.id and t2.id in(:orgIds)
where 1=1
and t.update_tv between to_date(:time,\'MM-DD-YYYY\') AND to_date(:time,\'MM-DD-YYYY\')+1";
         }          
 }]]></sql><field name="METER_ID"/><field name="UPDATE_TV"/><field name="STATUS"/><parameter name="sn" type="String" default-value="1"/><parameter name="orgIds" type="List" default-value="1"/><parameter name="time" type="String" default-value="05-27-2019"/></dataset></datasource><paper type="A4" left-margin="90" right-margin="90"
    top-margin="72" bottom-margin="72" paging-mode="fitpage" fixrows="0"
    width="595" height="842" orientation="portrait" html-report-align="left" bg-image="" html-interval-refresh-value="0" column-enabled="false"></paper></ureport>