/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class TestUser{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-03-29 01:14:36
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.system;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import net.sf.json.JSONArray;

import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AccountException;
import org.apache.shiro.web.util.SavedRequest;
import org.apache.shiro.web.util.WebUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.pushlet.PushletData;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.DateTimeFormatterUtil;
import com.clou.esp.hes.app.web.core.util.Globals;
import com.clou.esp.hes.app.web.core.util.IpUtil;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.OrganizationUtils;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.enums.system.EnumUserType;
import com.clou.esp.hes.app.web.model.asset.AssetCommunicator;
import com.clou.esp.hes.app.web.model.asset.AssetCommunicatorFavorite;
import com.clou.esp.hes.app.web.model.asset.AssetFavorite;
import com.clou.esp.hes.app.web.model.asset.AssetMeterFavorite;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroup;
import com.clou.esp.hes.app.web.model.asset.AssetScheduleScheme;
import com.clou.esp.hes.app.web.model.data.DataStatisticsCustomer;
import com.clou.esp.hes.app.web.model.data.DataStatisticsDevice;
import com.clou.esp.hes.app.web.model.data.DataStatisticsEvent;
import com.clou.esp.hes.app.web.model.data.DataUserLog;
import com.clou.esp.hes.app.web.model.dict.DictCommunicationType;
import com.clou.esp.hes.app.web.model.dict.DictManufacturer;
import com.clou.esp.hes.app.web.model.dict.DictMenu;
import com.clou.esp.hes.app.web.model.report.ImportExportReport;
import com.clou.esp.hes.app.web.model.system.SysOrg;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.asset.AssetCommunicatorFavoriteService;
import com.clou.esp.hes.app.web.service.asset.AssetCommunicatorService;
import com.clou.esp.hes.app.web.service.asset.AssetCustomerService;
import com.clou.esp.hes.app.web.service.asset.AssetFavoriteService;
import com.clou.esp.hes.app.web.service.asset.AssetLineManagementService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterFavoriteService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterGroupService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.asset.AssetScheduleSchemeService;
import com.clou.esp.hes.app.web.service.asset.AssetTransformerService;
import com.clou.esp.hes.app.web.service.data.DataIntegrityService;
import com.clou.esp.hes.app.web.service.data.DataStatisticsCustomerService;
import com.clou.esp.hes.app.web.service.data.DataStatisticsDeviceService;
import com.clou.esp.hes.app.web.service.data.DataStatisticsEventService;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.demo.TestUserService;
import com.clou.esp.hes.app.web.service.dict.DictCommunicationTypeService;
import com.clou.esp.hes.app.web.service.dict.DictManufacturerService;
import com.clou.esp.hes.app.web.service.impl.data.DataStatisticsCustomerServiceImpl;
import com.clou.esp.hes.app.web.service.report.ImportExportReportService;
import com.clou.esp.hes.app.web.service.system.SysMenuService;
import com.clou.esp.hes.app.web.service.system.SysOrgService;
import com.clou.esp.hes.app.web.service.system.SysUserService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.format.FormatUtil;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;

/**
 * <AUTHOR>
 * @时间：2017-03-29 01:14:36
 * @描述：系统基本Controller
 */
@Controller
@RequestMapping("/systemController")
public class SystemController extends BaseController {
	

	final static String TIME_FLAG =ResourceUtil.getSessionattachmenttitle("local.time.flag");
	final static Integer ORG_TYPE_FLAG = 11 ;
	final static Integer USER_TYPE_FLAG = 12 ;
	final static Integer INDUSTRY_TYPE_FLAG = 13 ;
	@Resource
	DataStatisticsDeviceService dataStatisticsDeviceService;
	@Resource
	DataStatisticsEventService dataStatisticsEventService;

	@Resource
	private TestUserService testUserService;
	@Resource
	private SysUserService sysUserService;
	@Resource
	private SysMenuService sysMenuService;
	@Resource
	private AssetMeterService assetMeterService;
	@Resource
	private AssetCommunicatorService assetCommunicatorService;
	@Resource
	private AssetLineManagementService assetLineManagementService;
	@Resource
	private AssetCustomerService assetCustomerService;
	@Resource
	private AssetTransformerService assetTransformerService;
	
	
	
	@Resource
	private DataIntegrityService dataIntegrityService;
	@Resource
    private AssetMeterFavoriteService assetMeterFavoriteService;
	@Resource
    private AssetCommunicatorFavoriteService assetCommunicatorFavoriteService;
	@Resource
    private SysOrgService sysOrgService;
	@Resource
	private DataUserLogService dataUserLogService;
	@Resource
	private DictManufacturerService dictManufacturerService;
	@Resource
	private DictCommunicationTypeService dictCommunicationTypeService;
	@Resource
	private AssetScheduleSchemeService assetScheduleSchemeService;
	@Resource
	private AssetMeterGroupService assetMeterGroupService;
	@Resource
	private ImportExportReportService importExportReportService;
	@Resource
	private DataStatisticsCustomerService dataStatisticsCustomerService;
	
	@Resource
	private AssetFavoriteService assetFavoriteService;
	
	
	
	/**
	 * 获取角色管理列表分页数据
	 * 
	 * <AUTHOR>
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "getDevicesJqgrid")
	@ResponseBody
	public JqGridResponseTo getDevicesJqgrid(JqGridSearchTo jqGridSearchTo,
			HttpServletRequest request) {
		SysUser su = TokenManager.getToken();
		TagUtil.setFieldValue(jqGridSearchTo, request);
		jqGridSearchTo.put("userId", su.getId());
		
		List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
		jqGridSearchTo.put("orgIdList", orgIdList);
		jqGridSearchTo.put("orgIds", orgIdList);
		
		JqGridResponseTo j = null;
		String searchType = request.getParameter("searchType");
		//Meter  //Commnuicator //Line //Transformer //Customer
		//1,2,3,4,6			
		// line ,transformer,customer 使用新的方法
		if (searchType != null && searchType.equals("Commnuicator")) {
			request.getSession(true).setAttribute(Globals.SESSION_DEVICE_TYPE,
					"2");
			jqGridSearchTo.put("noDeviceType", 201);
			j = assetCommunicatorService.getForJqGrid(jqGridSearchTo);
		}else if (searchType != null && searchType.equals("Line")) { 
			request.getSession(true).setAttribute(Globals.SESSION_DEVICE_TYPE,
					"3");
			j = assetLineManagementService.findLinesForJqGrid(jqGridSearchTo);
		}else if (searchType != null && searchType.equals("Transformer")) { 
			request.getSession(true).setAttribute(Globals.SESSION_DEVICE_TYPE,
					"4");
			j = assetTransformerService.getForJqGrid(jqGridSearchTo);
		}else if (searchType != null && searchType.equals("Customer")) { 
			request.getSession(true).setAttribute(Globals.SESSION_DEVICE_TYPE,
					"6");
			j = assetCustomerService.getForJqGrid(jqGridSearchTo);
		} else { 
			request.getSession(true).setAttribute(Globals.SESSION_DEVICE_TYPE,
					"1");
			j = assetMeterService.getForJqGrid(jqGridSearchTo);

		}

		return j;
	}

	
	/**
	 * 获取角色管理列表分页数据
	 * 
	 * <AUTHOR>
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "getDevicesJqgridAdvanced")
	@ResponseBody
	public JqGridResponseTo getDevicesJqgridAdvanced(JqGridSearchTo jqGridSearchTo,
			HttpServletRequest request) {
		SysUser su = TokenManager.getToken();
		TagUtil.setFieldValue(jqGridSearchTo, request);
		jqGridSearchTo.put("userId", su.getId());
		
		String queryTypeId = request.getParameter("queryTypeId");
		jqGridSearchTo.put("queryTypeId", queryTypeId);

		String commuicatorCountFlag = request.getParameter("commuicatorCountFlag");
		String commuicatorName ="commuicatorSnName" + commuicatorCountFlag + "[]";
		
		String[] commuicatorSnIdArr = request.getParameterValues(commuicatorName);
		
		jqGridSearchTo.put("communicatorIdList", commuicatorSnIdArr);
		
		String frimwareVersionId = request.getParameter("frimwareVersionId");
		jqGridSearchTo.put("frimwareVersionId", frimwareVersionId);

		String[] manufacturerIdArr = request.getParameterValues("manufacturerId[]");
	
		jqGridSearchTo.put("manufacturerIdList", manufacturerIdArr);
		
		String[] modelIdArr = request.getParameterValues("modelId[]");
	
		jqGridSearchTo.put("modelIdList", modelIdArr);
		
		String[] commuicationTypeIdArr = request.getParameterValues("commuicationTypeId[]");
		
		jqGridSearchTo.put("commuicationTypeIdList", commuicationTypeIdArr);
		
		String[] measurementGroupIdArr = request.getParameterValues("measurementGroupId[]");
		
		jqGridSearchTo.put("measurementGroupIdList", measurementGroupIdArr);
		
		String[] collectSchemeGroupIdArr = request.getParameterValues("collectSchemeGroupId[]");
		
		jqGridSearchTo.put("collectSchemeGroupIdList", collectSchemeGroupIdArr);
		
		String lineId = request.getParameter("lineId");
		jqGridSearchTo.put("lineId", lineId);
		
		String transformerId = request.getParameter("transformerId");
		jqGridSearchTo.put("transformerId", transformerId);
		
		String orgId = request.getParameter("orgId");
		jqGridSearchTo.put("orgId", orgId);
		
		String keywordAdvanced = request.getParameter("keywordAdvanced");
		jqGridSearchTo.put("keywordAdvanced", keywordAdvanced);
		
        if(StringUtils.isEmpty(orgId)){
			List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
			jqGridSearchTo.put("orgIdList", orgIdList);
        }
		
		JqGridResponseTo j = null;
	
		if (queryTypeId != null && queryTypeId.equals("2")) {
			request.getSession(true).setAttribute(Globals.SESSION_DEVICE_TYPE,
					"2");
			jqGridSearchTo.put("noDeviceType", 201);
			j = assetCommunicatorService.getForJqGridAdvanced(jqGridSearchTo);
		} else {
			request.getSession(true).setAttribute(Globals.SESSION_DEVICE_TYPE,
					"1");
			j = assetMeterService.getForJqGridAdvanced(jqGridSearchTo);
		}

		return j;
	}
	
	/**
	 * 默认主页
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "index")
	public ModelAndView index(HttpServletRequest request, Model model) {
		List<DictManufacturer> manufacturerList = dictManufacturerService.getAllList();
		String manufacturers = RoletoJson.listToReplaceStr(manufacturerList, "id", "name");
		model.addAttribute("manufacturerReplace", manufacturers);
		List<DictCommunicationType> dctl = dictCommunicationTypeService.getAllList();
		String comms = RoletoJson.listToReplaceStr(dctl, "id", "name");
		model.addAttribute("commTypeReplace", comms);
		
		List<AssetScheduleScheme> scheduleSchemeList = assetScheduleSchemeService.getAllList();
		String scheduleSchemes = RoletoJson.listToReplaceStr(scheduleSchemeList, "id", "name");
		model.addAttribute("scheduleSchemeReplace", scheduleSchemes);
		
		List<AssetMeterGroup> meterGroupList=assetMeterGroupService.getAllList();
		Map<String,List<AssetMeterGroup>> map = Maps.newHashMap();
		if(meterGroupList!=null) {
			for(AssetMeterGroup group:meterGroupList) {
				String type = group.getType();
				List<AssetMeterGroup> tmp = map.get(type);
				if(tmp==null) {
					tmp=Lists.newArrayList();
				}
				tmp.add(group);
				map.put(type, tmp);
			}
		}
        if(map != null && map.get("1") != null){
        	String meaGroups = RoletoJson.listToReplaceStr(map.get("1"), "id", "name");

    		model.addAttribute("meaGroupReplace", meaGroups);
        }
		
    	Map<String, Object> params = new HashMap<String, Object>();
		List<AssetCommunicator> communicators= assetCommunicatorService.getListLimitTwenty(params);
		String commReplace = RoletoJson.listToReplaceStr(communicators, "id", "sn");
		model.addAttribute("commReplace", commReplace);
		
		return new ModelAndView("/main/index");
	}

	/**
	 * 展示首页
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "main")
	public ModelAndView main(HttpServletRequest request, Model model) {
		return new ModelAndView("redirect:/dataIntegrityController/scheduleReadsReport.do", null);
	}
	
	/**
	 * 默认新主页
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "home")
	public ModelAndView home(HttpServletRequest request, Model model) {
		String defaultId = request.getParameter("defaultId");
		System.out.println(defaultId);

		String selectProfileCycleType = request.getParameter("selectProfileCycleType");

		// 获取昨日日期字符串
		Date d = new Date();
		Map<String, Object> paramsPie = new HashMap<String, Object>();
		SysUser su = TokenManager.getToken();

		List<SysOrg> sysOrgList = sysOrgService.getAllList();
		List<SysOrg> sysOrgListDisplay = new ArrayList<>();

		String profileCycleTypeListStr = ResourceUtil.getSessionattachmenttitle("profile.cycle.type.list");

		List<String> profileCycleListDispaly = null;
		if (profileCycleTypeListStr == null || "".equals(profileCycleTypeListStr)) {
			profileCycleListDispaly = new ArrayList<>();
			profileCycleListDispaly.add("Total");
			profileCycleListDispaly.add("Minutely");
			profileCycleListDispaly.add("Daily");
			profileCycleListDispaly.add("Monthly");
		} else {
			String[] splits = profileCycleTypeListStr.split(",");
			profileCycleListDispaly = new ArrayList<>(Arrays.asList(splits));
		}

		if (selectProfileCycleType == null || "".equals(selectProfileCycleType)) {
			selectProfileCycleType = profileCycleListDispaly.get(0);
		}
		request.setAttribute("profileCycleTypeDefault", selectProfileCycleType);

		String[] orgIdsN = su.getOrgId().split(",");
		boolean isAdmin = false;

		if (orgIdsN.length > 0) {
			for (String orgId : orgIdsN) {
				for (SysOrg sysOrg : sysOrgList) {
					if (sysOrg.getParentOrgTid() != "" && sysOrg.getParentOrgTid().equals("0") && sysOrg.getId().equals(orgId)) {
						isAdmin = true;
						break;
					}
				}
			}
		}


		if (!isAdmin) {
			if (orgIdsN.length > 0) {
				for (String orgId : orgIdsN) {

					SysOrg cur_org = sysOrgService.getEntity(orgId);
					sysOrgListDisplay = sysOrgService.getListByOrgCode(cur_org);
					break;
					//for(SysOrg sysOrg : sysOrgList){
					//	if(sysOrg.getId().equals(orgId)){
					//		sysOrgListDisplay.add(sysOrg);
					//		break;
					//	}
					//}
				}

			}
		} else {
			sysOrgListDisplay = sysOrgList;
		}
		request.setAttribute("sysOrgListDisplay", sysOrgListDisplay);
		request.setAttribute("profileCycleListDispaly", profileCycleListDispaly);


		if (defaultId != null) {
			request.setAttribute("orgIdDefault", defaultId);
		} else {
			defaultId = orgIdsN[0];
			request.setAttribute("orgIdDefault", orgIdsN[0]);
		}

		d = FormatUtil.addDaysToDate(d, -1);
		String tv = DateUtils.date2Str(d, DateUtils.date_sdf);
		Map<String, Object> params = new HashMap<String, Object>();
		Map<String, Object> result = new HashMap<String, Object>();


		params.put("profileId", selectProfileCycleType);
		params.put("tv", tv);
		params.put("tvType", 1);
		params.put("idTypes", "'5','6','7','8','9','10','11'");
		List<String> orgIdDataIntegrityList = new ArrayList<String>();
		orgIdDataIntegrityList.add(defaultId);
		params.put("orgIdList", orgIdDataIntegrityList);

		paramsPie.put("orgIdList", orgIdDataIntegrityList);

		List<Map<String, Object>> diList = dataIntegrityService
				.getDataIntegrityByMap(params);

		//init 新增一个profileId，在不改变原模式的情况，可以在这个位置判断是否是多个profileId用来想除

		if (diList != null && diList.size() > 0) {

			for (Map<String, Object> di : diList) {
				BigDecimal five = new BigDecimal(0);
				BigDecimal integrity_t = new BigDecimal(0);
				BigDecimal cactual = (BigDecimal) di.get("COUNT_ACTUAL");
				BigDecimal ctotal = (BigDecimal) di.get("COUNT_TOTAL");

				String idType = (String) di.get("ID_TYPE");
				BigDecimal integrity = (BigDecimal) di.get("INTEGRITY");
				//COUNT_ACTUAL/COUNT_TOTAL*100


				if (integrity == null) {
					integrity = new BigDecimal(0);
				}
				if (StringUtil.isEmpty(idType)) {
					continue;
				}
				if (idType.equals("5")) {
					result.put("dayIntegrity",
							integrity.setScale(2, BigDecimal.ROUND_DOWN).toString());

				} else if (idType.equals("6")) {

					result.put("hundredPercent",
							integrity.setScale(2, BigDecimal.ROUND_DOWN)
									.toString());

				} else if (idType.equals("7")) {
					result.put("middlePercent",
							integrity.setScale(2, BigDecimal.ROUND_DOWN)
									.toString());

				} else if (idType.equals("8")) {
					result.put("zeroPercent",
							integrity.setScale(2, BigDecimal.ROUND_DOWN)
									.toString());
				} else if (idType.equals("9")) {
					result.put("hundredTotal", integrity.intValue());
				} else if (idType.equals("10")) {
					result.put("middleTotal", integrity.intValue());
				} else if (idType.equals("11")) {
					result.put("zeroTotal", integrity.intValue());
				}
			}
		}
		if (result.get("dayIntegrity") == null) {
			result.put("dayIntegrity",
					new BigDecimal("0").setScale(2, BigDecimal.ROUND_DOWN)
							.toString());
		}
		if (result.get("hundredPercent") == null) {
			result.put("hundredPercent",
					new BigDecimal("0").setScale(2, BigDecimal.ROUND_DOWN)
							.toString());
		}
		if (result.get("middlePercent") == null) {
			result.put("middlePercent",
					new BigDecimal("0").setScale(2, BigDecimal.ROUND_DOWN)
							.toString());
		}
		if (result.get("zeroPercent") == null) {
			result.put("zeroPercent",
					new BigDecimal("0").setScale(2, BigDecimal.ROUND_DOWN)
							.toString());
		}
		if (result.get("hundredTotal") == null) {
			result.put("hundredTotal", 0);
		}
		if (result.get("middleTotal") == null) {
			result.put("middleTotal", 0);
		}
		if (result.get("zeroTotal") == null) {
			result.put("zeroTotal", 0);
		}
		request.setAttribute("dayIntegrity", result.get("dayIntegrity")); // 昨日抄读数据百分比
		request.setAttribute("hundredPercent", result.get("dayIntegrity")); // 昨日抄读数据百分比
//		request.setAttribute("hundredPercent", result.get("hundredPercent")); // 已抄读到的数据量
		request.setAttribute("middlePercent", result.get("middlePercent")); // 已抄读数据百分比
		request.setAttribute("zeroPercent", result.get("zeroPercent")); // 丢失数据量
		request.setAttribute("hundredTotal", result.get("hundredTotal")); // 丢失数据百分比
		request.setAttribute("middleTotal", result.get("middleTotal")); // 抄读失败数据量
		request.setAttribute("zeroTotal", result.get("zeroTotal")); // 抄读失败数据百分比
		// 日数据和月数据报表的默认时间
		request.setAttribute(
				"endDay",
				DateUtils.date2Str(d,
						DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
		request.setAttribute("startDay", DateUtils.date2Str(
				FormatUtil.addDaysToDate(d, -6),
				DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
		request.setAttribute("start15Days", DateUtils.date2Str(
				FormatUtil.addDaysToDate(d, -14),
				DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
		request.setAttribute("start30Days", DateUtils.date2Str(
				FormatUtil.addDaysToDate(d, -29),
				DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
		SimpleDateFormat sdf = new SimpleDateFormat(" MMMM, yyyy",
				Locale.ENGLISH);
		request.setAttribute("endMonth", DateUtils.date2Str(d, sdf));
		request.setAttribute("startMonth",
				DateUtils.date2Str(FormatUtil.addMonthsToDate(d, -11), sdf));

		request.setAttribute("start6Months",
				DateUtils.date2Str(FormatUtil.addMonthsToDate(d, -5), sdf));
		request.setAttribute("start12Months",
				DateUtils.date2Str(FormatUtil.addMonthsToDate(d, -11), sdf));


		// 以上为完整率数据 下面开始写表计数量的获取

		paramsPie.put("tvType", 1);// day

		// name=manufacturer;model;commType
		paramsPie.put("tv", tv);
		paramsPie.put("idType", "2");


		List<Map<String, Object>> dataStatisticsDeviceList = dataStatisticsDeviceService
				.getDataStatisticsDeviceByMaps(paramsPie);


		paramsPie.put("idType", "1");
		List<Map<String, Object>> dataStatisticsDeviceListTotal = dataStatisticsDeviceService
				.getDataStatisticsDeviceByMaps(paramsPie);
		paramsPie.put("idType", "11");
		List<Map<String, Object>> dataStatisticsConcentratorListTotal = dataStatisticsDeviceService
				.getDataStatisticsDeviceByMaps(paramsPie);
		paramsPie.put("idType", "21");
		List<Map<String, Object>> onlineMeterTotal = dataStatisticsDeviceService
				.getDataStatisticsDeviceByMaps(paramsPie);
		paramsPie.put("idType", "22");
		List<Map<String, Object>> onlineCommunicatorTotal = dataStatisticsDeviceService
				.getDataStatisticsDeviceByMaps(paramsPie);
		if (onlineMeterTotal != null && onlineMeterTotal.size() > 0) {
			for (Map<String, Object> di : onlineMeterTotal) {
				BigDecimal countCurrent = (BigDecimal) di.get("COUNT_CURRENT");
				BigDecimal percent = (BigDecimal) di.get("PERCENT");
				request.setAttribute("onlineMeterTotal", countCurrent);
				request.setAttribute("onlineMeterPercent", percent);
			}
		} else {
			request.setAttribute("onlineMeterTotal", "0");
			request.setAttribute("onlineMeterPercent", "0");
		}

		if (onlineCommunicatorTotal != null
				&& onlineCommunicatorTotal.size() > 0) {
			for (Map<String, Object> di : onlineCommunicatorTotal) {
				BigDecimal countCurrent = (BigDecimal) di.get("COUNT_CURRENT");
				BigDecimal percent = (BigDecimal) di.get("PERCENT");
				request.setAttribute("onlineCommunicatorTotal", countCurrent);
				request.setAttribute("onlineCommunicatorPercent", percent);
			}
		} else {
			request.setAttribute("onlineCommunicatorTotal", "0");
			request.setAttribute("onlineCommunicatorPercent", "0");
		}


		List<DataStatisticsDevice> dataStatisticsDeviceListPie = new ArrayList();
		List<DataStatisticsDevice> dataStatisticsDeviceListPieTotal = new ArrayList();
		List<DataStatisticsDevice> dataStatisticsConcentratorListPieTotal = new ArrayList();
		if (dataStatisticsDeviceList != null
				&& dataStatisticsDeviceList.size() > 0) {
			for (Map<String, Object> di : dataStatisticsDeviceList) {
				BigDecimal countCurrent = (BigDecimal) di.get("COUNT_CURRENT");
				BigDecimal percent = (BigDecimal) di.get("PERCENT");
				String name = (String) di.get("NAME");
				DataStatisticsDevice dataStatisticsDevice = new DataStatisticsDevice();
				dataStatisticsDevice.setCountCurrent(countCurrent);
				dataStatisticsDevice.setPercent(percent);
				dataStatisticsDevice.setName(name);
				dataStatisticsDeviceListPie.add(dataStatisticsDevice);
			}
		}

		if (dataStatisticsDeviceListTotal != null
				&& dataStatisticsDeviceListTotal.size() > 0) {
			for (Map<String, Object> di : dataStatisticsDeviceListTotal) {
				BigDecimal countCurrent = (BigDecimal) di.get("COUNT_CURRENT");
				DataStatisticsDevice dataStatisticsDevice = new DataStatisticsDevice();
				dataStatisticsDevice.setCountCurrent(countCurrent);
				dataStatisticsDeviceListPieTotal.add(dataStatisticsDevice);
			}
		}

		if (dataStatisticsConcentratorListTotal != null
				&& dataStatisticsConcentratorListTotal.size() > 0) {
			for (Map<String, Object> di : dataStatisticsConcentratorListTotal) {
				BigDecimal countCurrent = (BigDecimal) di.get("COUNT_CURRENT");
				DataStatisticsDevice dataStatisticsDevice = new DataStatisticsDevice();
				dataStatisticsDevice.setCountCurrent(countCurrent);
				dataStatisticsConcentratorListPieTotal
						.add(dataStatisticsDevice);
			}
		}


		// 按点击数倒序
		Collections.sort(dataStatisticsDeviceListPie,
				new Comparator<DataStatisticsDevice>() {
					public int compare(DataStatisticsDevice arg0,
									   DataStatisticsDevice arg1) {
						BigDecimal hits0 = arg0.getCountCurrent();
						BigDecimal hits1 = arg1.getCountCurrent();
						if (hits0.compareTo(hits1) == -1) {
							return 1;
						} else if (hits0.compareTo(hits1) == 0) {
							return 0;
						} else {
							return -1;
						}
					}
				});

		BigDecimal deviceTotal = new BigDecimal(0);
		BigDecimal deviceTotal1 = new BigDecimal(0);
		if (StringUtil.isNotEmpty(dataStatisticsDeviceListPieTotal)
				&& dataStatisticsDeviceListPieTotal.size() > 0) {
			deviceTotal = dataStatisticsDeviceListPieTotal.get(0)
					.getCountCurrent();
			deviceTotal1 = dataStatisticsDeviceListPieTotal.get(0)
					.getCountCurrent();
			request.setAttribute("dataStatisticsDeviceListPieTotal",
					dataStatisticsDeviceListPieTotal.get(0).getCountCurrent());
		}

		List<DataStatisticsDevice> dataStatisticsDeviceListDisplay = new ArrayList();
		int k = 0;
		BigDecimal eventDeviceContent = new BigDecimal(0);
		for (DataStatisticsDevice dataStatisticsDevice1 : dataStatisticsDeviceListPie) {
			if (k < 4) {
				eventDeviceContent = eventDeviceContent
						.add(dataStatisticsDevice1.getCountCurrent());
				dataStatisticsDeviceListDisplay.add(dataStatisticsDevice1);
			}
			k++;
		}

		if (k > 4) {
			BigDecimal deviceTotalDisplay = new BigDecimal(0);
			BigDecimal subDisplay = new BigDecimal(0);
			subDisplay = deviceTotal.subtract(eventDeviceContent);

			if (deviceTotal1.compareTo(new BigDecimal(0)) != 0) {
				deviceTotalDisplay = deviceTotal.subtract(eventDeviceContent)
						.divide(deviceTotal1, 2, BigDecimal.ROUND_HALF_UP);
			}

			DataStatisticsDevice dataStatisticsDevice2 = new DataStatisticsDevice();
			dataStatisticsDevice2.setPercent(deviceTotalDisplay);
			dataStatisticsDevice2.setCountCurrent(subDisplay);
			dataStatisticsDevice2.setName(MutiLangUtil.doMutiLang("home.other"));
			dataStatisticsDeviceListDisplay.add(dataStatisticsDevice2);
		}

		if (dataStatisticsDeviceListPie.size() == 0) {
			DataStatisticsDevice dataStatisticsDevice = new DataStatisticsDevice();
			dataStatisticsDevice.setPercent(new BigDecimal(0));
			dataStatisticsDevice.setCountCurrent(new BigDecimal(0));
			dataStatisticsDevice.setName(MutiLangUtil.doMutiLang("home.noData"));
			dataStatisticsDeviceListPie.add(dataStatisticsDevice);
		}

		if (dataStatisticsDeviceListDisplay.size() == 0) {
			DataStatisticsDevice dataStatisticsDevice = new DataStatisticsDevice();
			dataStatisticsDevice.setPercent(new BigDecimal(0));
			dataStatisticsDevice.setCountCurrent(new BigDecimal(0));
			dataStatisticsDevice.setName(MutiLangUtil.doMutiLang("home.noData"));
			dataStatisticsDeviceListDisplay.add(dataStatisticsDevice);
		}

		String deviceInfo = JSONArray.fromObject(
				dataStatisticsDeviceListDisplay).toString();
		request.setAttribute("dataStatisticsDeviceListPie",
				dataStatisticsDeviceListPie);
		request.setAttribute("deviceInfo", deviceInfo);

		if (StringUtil.isNotEmpty(dataStatisticsConcentratorListPieTotal)
				&& dataStatisticsConcentratorListPieTotal.size() > 0) {
			request.setAttribute("dataStatisticsConcentratorListPieTotal",
					dataStatisticsConcentratorListPieTotal.get(0)
							.getCountCurrent());
		}

		// 以上为表计数量 下面报警数据
		DataStatisticsEvent dataStatisticsEvent = new DataStatisticsEvent();
		dataStatisticsEvent.setTvTime(tv);
		dataStatisticsEvent.setOrgIdList(orgIdDataIntegrityList);
		List<DataStatisticsEvent> dataStatisticsEventList = dataStatisticsEventService
				.getListForOrgIds(dataStatisticsEvent);
		List<DataStatisticsEvent> dataStatisticsEventListNew = new ArrayList();
		BigDecimal bdTotal = new BigDecimal(0);
		BigDecimal bdTotal1 = new BigDecimal(0);
		for (DataStatisticsEvent dataStatisticsEvent1 : dataStatisticsEventList) {
			if (!"total".equalsIgnoreCase(dataStatisticsEvent1.getEventName())) {
				dataStatisticsEventListNew.add(dataStatisticsEvent1);
			} else {
				bdTotal = dataStatisticsEvent1.getCountCurrent();
				bdTotal1 = dataStatisticsEvent1.getCountCurrent();
			}
		}

		// 按点击数倒序
		Collections.sort(dataStatisticsEventListNew,
				new Comparator<DataStatisticsEvent>() {
					public int compare(DataStatisticsEvent arg0,
									   DataStatisticsEvent arg1) {
						BigDecimal hits0 = arg0.getCountCurrent();
						BigDecimal hits1 = arg1.getCountCurrent();
						if (hits0.compareTo(hits1) == -1) {
							return 1;
						} else if (hits0.compareTo(hits1) == 0) {
							return 0;
						} else {
							return -1;
						}
					}
				});

		List<DataStatisticsEvent> dataStatisticsEventListDisplay = new ArrayList();
		int i = 0;
		BigDecimal eventContent = new BigDecimal(0);
		for (DataStatisticsEvent dataStatisticsEvent1 : dataStatisticsEventListNew) {
//			if (i < 4) {
			eventContent = eventContent.add(dataStatisticsEvent1
					.getCountCurrent());
			dataStatisticsEventListDisplay.add(dataStatisticsEvent1);
//			}
			i++;
		}

//		if (i > 4) {
//			BigDecimal bdTotalDisplay = new BigDecimal(0);
//			BigDecimal subDisplay = new BigDecimal(0);
//			subDisplay = bdTotal.subtract(eventContent);
//
//			if (bdTotal1.compareTo(new BigDecimal(0)) != 0) {
//				bdTotalDisplay = bdTotal.subtract(eventContent).divide(
//						bdTotal1, 2, BigDecimal.ROUND_HALF_UP);
//			}
//
//			DataStatisticsEvent dataStatisticsEvent2 = new DataStatisticsEvent();
//			dataStatisticsEvent2.setCountCurrent(subDisplay);
//			dataStatisticsEvent2.setPercent(bdTotalDisplay);
//			dataStatisticsEvent2.setEventName(MutiLangUtil.doMutiLang("home.other"));
//			dataStatisticsEventListDisplay.add(dataStatisticsEvent2);
//		}
//
//		if (dataStatisticsEventListDisplay.size() == 0) {
//			DataStatisticsEvent dataStatisticsEvent1 = new DataStatisticsEvent();
//			dataStatisticsEvent1.setCountCurrent(new BigDecimal(0));
//			dataStatisticsEvent1.setPercent(new BigDecimal(0));
//			dataStatisticsEvent1.setEventName(MutiLangUtil.doMutiLang("home.noData"));
//			dataStatisticsEventListDisplay.add(dataStatisticsEvent1);
//		}
//
//		if (dataStatisticsEventListNew.size() == 0) {
//			DataStatisticsEvent dataStatisticsEvent1 = new DataStatisticsEvent();
//			dataStatisticsEvent1.setCountCurrent(new BigDecimal(0));
//			dataStatisticsEvent1.setPercent(new BigDecimal(0));
//			dataStatisticsEvent1.setEventName(MutiLangUtil.doMutiLang("home.noData"));
//			dataStatisticsEventListNew.add(dataStatisticsEvent1);
//		}

		String eventInfo = JSONArray.fromObject(dataStatisticsEventListDisplay)
				.toString();

		request.setAttribute("bdTotal", bdTotal);
		request.setAttribute("eventInfo", eventInfo);
		request.setAttribute("dataStatisticsEventList",
				dataStatisticsEventListNew);

		// second page begin 第二页

		Map<String, Object> paramsImportExport = new HashMap<String, Object>();
		paramsImportExport.put("entityType", "5");
		paramsImportExport.put("timeType", "7");
		paramsImportExport.put("timePattern", TIME_FLAG);

		paramsImportExport.put("dataType", "2");
		paramsImportExport.put("startTime", DateUtils.date2Str(
				FormatUtil.addDaysToDate(d, 0),
				DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
		paramsImportExport.put("endTime", DateUtils.date2Str(
				FormatUtil.addDaysToDate(d, 0),
				DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
		List<String> orgIds = OrganizationUtils.assembleOrgIds(su.getOrgId(),
				sysOrgService, true, 0);
		paramsImportExport.put("orgIds", orgIds);

		List<ImportExportReport> importExportReportList = importExportReportService
				.findImportExportParamList(paramsImportExport);
		BigDecimal importExportOrgSum = new BigDecimal(0);
		BigDecimal importExportOrgYesterdayTotal = new BigDecimal(0);
		List<ImportExportReport> importExportReportDisplay = new ArrayList();
		if (importExportReportList != null && importExportReportList.size() > 0) {
			for (ImportExportReport importExportReport1 : importExportReportList) {
				importExportOrgSum = importExportOrgSum.add(importExportReport1
						.getValue());
			}

			for (ImportExportReport importExportReport1 : importExportReportList) {

				if (importExportOrgSum.compareTo(new BigDecimal(0)) != 0) {
					importExportReport1.setPercent(importExportReport1
							.getValue()
							.divide(importExportOrgSum, 4,
									BigDecimal.ROUND_HALF_UP)
							.multiply(new BigDecimal(100)));
				} else {
					importExportReport1.setPercent(new BigDecimal(0));
				}

			}

			// 按点击数倒序
			Collections.sort(importExportReportList,
					new Comparator<ImportExportReport>() {
						public int compare(ImportExportReport arg0,
										   ImportExportReport arg1) {
							BigDecimal hits0 = arg0.getValue();
							BigDecimal hits1 = arg1.getValue();
							if (hits0.compareTo(hits1) == -1) {
								return 1;
							} else if (hits0.compareTo(hits1) == 0) {
								return 0;
							} else {
								return -1;
							}
						}
					});

			int ii = 0;
			BigDecimal importExport = new BigDecimal(0);
			for (ImportExportReport importExportReport1 : importExportReportList) {
				if (ii < 4) {
					importExport = importExport.add(importExportReport1
							.getValue());
					importExportReportDisplay.add(importExportReport1);
				}
				ii++;
			}

			if (ii > 4) {
				BigDecimal bdTotalDisplay = new BigDecimal(0);
				BigDecimal subDisplay = new BigDecimal(0);
				subDisplay = importExportOrgSum.subtract(importExport);

				if (importExportOrgSum.compareTo(new BigDecimal(0)) != 0) {
					bdTotalDisplay = importExportOrgSum.subtract(importExport)
							.divide(importExportOrgSum, 2,
									BigDecimal.ROUND_HALF_UP);
				}

				ImportExportReport importExportReport2 = new ImportExportReport();
				importExportReport2.setValue(subDisplay);
				importExportReport2.setPercent(bdTotalDisplay);
				importExportReport2.setOrgName(MutiLangUtil.doMutiLang("home.other"));
				importExportReportDisplay.add(importExportReport2);
			}

		}

		if (importExportReportDisplay.size() == 0) {
			ImportExportReport importExportReport2 = new ImportExportReport();
			importExportReport2.setValue(new BigDecimal(0));
			importExportReport2.setPercent(new BigDecimal(0));
			importExportReport2.setOrgName(MutiLangUtil.doMutiLang("home.noData"));
			importExportReportDisplay.add(importExportReport2);
		}

		if (importExportReportList.size() == 0) {
			ImportExportReport importExportReport2 = new ImportExportReport();
			importExportReport2.setValue(new BigDecimal(0));
			importExportReport2.setPercent(new BigDecimal(0));
			importExportReport2.setOrgName(MutiLangUtil.doMutiLang("home.noData"));
			importExportReportList.add(importExportReport2);
		}

		String importExportReportInfo = JSONArray.fromObject(
				importExportReportDisplay).toString();

		request.setAttribute("importExportReportInfo", importExportReportInfo);
		request.setAttribute("importExportReportList", importExportReportList);

		// 过去7天
		paramsImportExport.put("startTime", DateUtils.date2Str(
				FormatUtil.addDaysToDate(d, -6),
				DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
		paramsImportExport.put("endTime", DateUtils.date2Str(
				FormatUtil.addDaysToDate(d, 0),
				DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));

		List<String> orgIds1 = new ArrayList<>();
		orgIds1.add(su.getOrgId());
		paramsImportExport.put("orgIds", orgIds1);
		List<ImportExportReport> importExportReportList7 = importExportReportService
				.findImportExportParamList(paramsImportExport);

		List<String> tvList = new ArrayList<String>();
		List<Double> importExportList = new ArrayList<Double>();

		if (importExportReportList7 != null
				&& importExportReportList7.size() > 0) {
			for (ImportExportReport importExportReport7 : importExportReportList7) {
				String time = importExportReport7.getTime();
				if (DateUtils.date2Str(FormatUtil.addDaysToDate(d, 0),
						DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG))
						.equals(time)) {
					importExportOrgYesterdayTotal = importExportReport7
							.getValue();
				}

				BigDecimal supplyValue = (BigDecimal) importExportReport7
						.getValue();
				importExportList.add(supplyValue.setScale(2,
						BigDecimal.ROUND_DOWN).doubleValue());

				tvList.add(DateUtils.date2Str(DateUtils.str2Date(time,
						DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)),
						DateTimeFormatterUtil
								.getSimpleDateFormat_DDMM(TIME_FLAG)));
			}
		}

		String tvListInfo = JSONArray.fromObject(tvList).toString();
		String importExportListInfo = JSONArray.fromObject(importExportList)
				.toString();

		if (importExportReportList7.size() == 0) {
			ImportExportReport importExportReport2 = new ImportExportReport();
			importExportReport2.setValue(new BigDecimal(0));
			importExportReport2.setTime(DateUtils.date2Str(
					FormatUtil.addDaysToDate(d, 0),
					DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
			importExportReportList7.add(importExportReport2);
		}
		request.setAttribute("importExportOrgYesterdayTotal",
				importExportOrgYesterdayTotal);
		request.setAttribute("tvList", tvListInfo);
		request.setAttribute("importExportDiagramList", importExportListInfo);
		request.setAttribute("importExportTableList", importExportReportList7);

		request.setAttribute("orgId", su.getOrgId());
		paramsImportExport.put("startTime", DateUtils.date2Str(
				FormatUtil.addDaysToDate(d, 0),
				DateTimeFormatterUtil.getSimpleDateFormat("5")));
		paramsImportExport.put("endTime", DateUtils.date2Str(
				FormatUtil.addDaysToDate(d, 0),
				DateTimeFormatterUtil.getSimpleDateFormat("5")));

		paramsImportExport.put("idType", USER_TYPE_FLAG + "");
		paramsImportExport.put("orgIdList", orgIds1);

		List<DataStatisticsCustomer> dataStatisticsCustomerListByUserType = dataStatisticsCustomerService
				.findDataStatisticsCustomerList(paramsImportExport);
		List<DataStatisticsCustomer> dataStatisticsCustomerListDisplayUserType = new ArrayList();
		// 按点击数倒序
		// 机构供电量总数
		BigDecimal importExportOrgSumByUserType = new BigDecimal(0);

		if (dataStatisticsCustomerListByUserType != null
				&& dataStatisticsCustomerListByUserType.size() > 0) {
			for (DataStatisticsCustomer dataStatisticsCustomer1 : dataStatisticsCustomerListByUserType) {
				BigDecimal countTotal = new BigDecimal(
						dataStatisticsCustomer1.getCountTotal());
				DecimalFormat df = new DecimalFormat("#,###");
				String countCurrentFormatter = df
						.format(dataStatisticsCustomer1.getCountCurrent());
				String countTotalFormatter = df.format(countTotal);

				dataStatisticsCustomer1
						.setCountTotalFormatter(countTotalFormatter);
				dataStatisticsCustomer1
						.setCountCurrentFormatter(countCurrentFormatter);

				importExportOrgSumByUserType = importExportOrgSumByUserType
						.add(dataStatisticsCustomer1.getCountCurrent());
			}
			if (importExportOrgSumByUserType.compareTo(new BigDecimal(0)) != 0) {
				for (DataStatisticsCustomer dataStatisticsCustomer1 : dataStatisticsCustomerListByUserType) {

					dataStatisticsCustomer1.setPercent(dataStatisticsCustomer1
							.getCountCurrent()
							.divide(importExportOrgSumByUserType, 2,
									BigDecimal.ROUND_HALF_UP)
							.multiply(new BigDecimal(100)));

				}
			}

			// 按点击数倒序
			Collections.sort(dataStatisticsCustomerListByUserType,
					new Comparator<DataStatisticsCustomer>() {
						public int compare(DataStatisticsCustomer arg0,
										   DataStatisticsCustomer arg1) {
							BigDecimal hits0 = arg0.getCountCurrent();
							BigDecimal hits1 = arg1.getCountCurrent();
							if (hits0.compareTo(hits1) == -1) {
								return 1;
							} else if (hits0.compareTo(hits1) == 0) {
								return 0;
							} else {
								return -1;
							}
						}
					});

			int ii = 0;
			BigDecimal importExport = new BigDecimal(0);
			for (DataStatisticsCustomer dataStatisticsCustomer1 : dataStatisticsCustomerListByUserType) {
				if (ii < 4) {
					importExport = importExport.add(dataStatisticsCustomer1
							.getCountCurrent());
					dataStatisticsCustomerListDisplayUserType
							.add(dataStatisticsCustomer1);
				}
				ii++;
			}

			if (ii > 4) {
				BigDecimal bdTotalDisplay = new BigDecimal(0);
				BigDecimal subDisplay = new BigDecimal(0);
				subDisplay = importExportOrgSumByUserType
						.subtract(importExport);

				if (importExportOrgSumByUserType.compareTo(new BigDecimal(0)) != 0) {
					bdTotalDisplay = importExportOrgSumByUserType.subtract(
							importExport).divide(importExportOrgSumByUserType,
							2, BigDecimal.ROUND_HALF_UP);
				}

				DataStatisticsCustomer dataStatisticsCustomer2 = new DataStatisticsCustomer();
				dataStatisticsCustomer2.setCountCurrent(subDisplay);
				dataStatisticsCustomer2.setPercent(bdTotalDisplay);
				dataStatisticsCustomer2.setName(MutiLangUtil.doMutiLang("home.other"));
				dataStatisticsCustomerListDisplayUserType
						.add(dataStatisticsCustomer2);
			}

		}

		if (dataStatisticsCustomerListDisplayUserType.size() == 0) {
			DataStatisticsCustomer dataStatisticsCustomer2 = new DataStatisticsCustomer();
			dataStatisticsCustomer2.setCountCurrent(new BigDecimal(0));
			dataStatisticsCustomer2.setPercent(new BigDecimal(0));
			dataStatisticsCustomer2.setName(MutiLangUtil.doMutiLang("home.noData"));
			dataStatisticsCustomerListDisplayUserType
					.add(dataStatisticsCustomer2);
		}

		if (dataStatisticsCustomerListByUserType == null
				|| dataStatisticsCustomerListByUserType.size() == 0) {
			dataStatisticsCustomerListByUserType = new ArrayList<>();
			DataStatisticsCustomer dataStatisticsCustomer2 = new DataStatisticsCustomer();
			dataStatisticsCustomer2.setCountCurrent(new BigDecimal(0));
			dataStatisticsCustomer2.setPercent(new BigDecimal(0));
			dataStatisticsCustomer2.setName(MutiLangUtil.doMutiLang("home.noData"));
			dataStatisticsCustomerListByUserType.add(dataStatisticsCustomer2);
		}

		String dataStatisticsCustomerInfoByUserType = JSONArray.fromObject(
				dataStatisticsCustomerListDisplayUserType).toString();

		request.setAttribute("importExportReportInfoByType",
				dataStatisticsCustomerInfoByUserType);
		request.setAttribute("importExportReportListByType",
				dataStatisticsCustomerListByUserType);

		return new ModelAndView("/main/home");
	}



//	public ModelAndView home(HttpServletRequest request, Model model) {
//        String defaultId = request.getParameter("defaultId");
//        System.out.println(defaultId);
//
//        String selectProfileCycleType = request.getParameter("selectProfileCycleType");
//
//        // 获取昨日日期字符串
//        Date d = new Date();
//        Map<String, Object> paramsPie = new HashMap<String, Object>();
//        SysUser su = TokenManager.getToken();
//
//        List<SysOrg> sysOrgList = sysOrgService.getAllList();
//        List<SysOrg> sysOrgListDisplay = new ArrayList<>();
//
//        String profileCycleTypeListStr = ResourceUtil.getSessionattachmenttitle("profile.cycle.type.list");
//
//        List<String> profileCycleListDispaly = null;
//        if (profileCycleTypeListStr == null || "".equals(profileCycleTypeListStr)) {
//            profileCycleListDispaly = new ArrayList<>();
//            profileCycleListDispaly.add("Total");
//            profileCycleListDispaly.add("Minutely");
//            profileCycleListDispaly.add("Daily");
//            profileCycleListDispaly.add("Monthly");
//        } else {
//            String[] splits = profileCycleTypeListStr.split(",");
//            profileCycleListDispaly = new ArrayList<>(Arrays.asList(splits));
//        }
//
//        if (selectProfileCycleType == null || "".equals(selectProfileCycleType)) {
//            selectProfileCycleType = profileCycleListDispaly.get(0);
//        }
//        request.setAttribute("profileCycleTypeDefault", selectProfileCycleType);
//
//        String[] orgIdsN = su.getOrgId().split(",");
//        boolean isAdmin = false;
//
//        if (orgIdsN.length > 0) {
//            for (String orgId : orgIdsN) {
//                for (SysOrg sysOrg : sysOrgList) {
//                    if (sysOrg.getParentOrgTid() != "" && sysOrg.getParentOrgTid().equals("0") && sysOrg.getId().equals(orgId)) {
//                        isAdmin = true;
//                        break;
//                    }
//                }
//            }
//        }
//
//
//        if (!isAdmin) {
//            if (orgIdsN.length > 0) {
//                for (String orgId : orgIdsN) {
//
//                    SysOrg cur_org = sysOrgService.getEntity(orgId);
//                    sysOrgListDisplay = sysOrgService.getListByOrgCode(cur_org);
//                    break;
//                    //for(SysOrg sysOrg : sysOrgList){
//                    //	if(sysOrg.getId().equals(orgId)){
//                    //		sysOrgListDisplay.add(sysOrg);
//                    //		break;
//                    //	}
//                    //}
//                }
//
//            }
//        } else {
//            sysOrgListDisplay = sysOrgList;
//        }
//        request.setAttribute("sysOrgListDisplay", sysOrgListDisplay);
//        request.setAttribute("profileCycleListDispaly", profileCycleListDispaly);
//
//
//        if (defaultId != null) {
//            request.setAttribute("orgIdDefault", defaultId);
//        } else {
//            defaultId = orgIdsN[0];
//            request.setAttribute("orgIdDefault", orgIdsN[0]);
//        }
//
//        d = FormatUtil.addDaysToDate(d, -1);
//        String tv = DateUtils.date2Str(d, DateUtils.date_sdf);
//        Map<String, Object> params = new HashMap<String, Object>();
//        Map<String, Object> result = new HashMap<String, Object>();
//
//
//        params.put("profileId", selectProfileCycleType);
//        params.put("tv", tv);
//        params.put("tvType", 1);
//        params.put("idTypes", "'5','6','7','8','9','10','11'");
//        List<String> orgIdDataIntegrityList = new ArrayList<String>();
//        orgIdDataIntegrityList.add(defaultId);
//        params.put("orgIdList", orgIdDataIntegrityList);
//
//        paramsPie.put("orgIdList", orgIdDataIntegrityList);
//
//        List<Map<String, Object>> diList = dataIntegrityService
//                .getDataIntegrityByMap(params);
//
//        //init 新增一个profileId，在不改变原模式的情况，可以在这个位置判断是否是多个profileId用来想除
//
//        if (diList != null && diList.size() > 0) {
//
//            for (Map<String, Object> di : diList) {
//                BigDecimal five = new BigDecimal(0);
//                BigDecimal integrity_t = new BigDecimal(0);
//                BigDecimal cactual = (BigDecimal) di.get("COUNT_ACTUAL");
//                BigDecimal ctotal = (BigDecimal) di.get("COUNT_TOTAL");
//
//                String idType = (String) di.get("ID_TYPE");
//                BigDecimal integrity = (BigDecimal) di.get("INTEGRITY");
//                //COUNT_ACTUAL/COUNT_TOTAL*100
//
//
//                if (integrity == null) {
//                    integrity = new BigDecimal(0);
//                }
//                if (StringUtil.isEmpty(idType)) {
//                    continue;
//                }
//                if (idType.equals("5")) {
//                    result.put("dayIntegrity",
//                            integrity.setScale(2, BigDecimal.ROUND_DOWN).toString());
//
//                } else if (idType.equals("6")) {
//
//                    result.put("hundredPercent",
//                            integrity.setScale(2, BigDecimal.ROUND_DOWN)
//                                    .toString());
//
//                } else if (idType.equals("7")) {
//                    result.put("middlePercent",
//                            integrity.setScale(2, BigDecimal.ROUND_DOWN)
//                                    .toString());
//
//                } else if (idType.equals("8")) {
//                    result.put("zeroPercent",
//                            integrity.setScale(2, BigDecimal.ROUND_DOWN)
//                                    .toString());
//                } else if (idType.equals("9")) {
//                    result.put("hundredTotal", integrity.intValue());
//                } else if (idType.equals("10")) {
//                    result.put("middleTotal", integrity.intValue());
//                } else if (idType.equals("11")) {
//                    result.put("zeroTotal", integrity.intValue());
//                }
//            }
//        }
//        if (result.get("dayIntegrity") == null) {
//            result.put("dayIntegrity",
//                    new BigDecimal("0").setScale(2, BigDecimal.ROUND_DOWN)
//                            .toString());
//        }
//        if (result.get("hundredPercent") == null) {
//            result.put("hundredPercent",
//                    new BigDecimal("0").setScale(2, BigDecimal.ROUND_DOWN)
//                            .toString());
//        }
//        if (result.get("middlePercent") == null) {
//            result.put("middlePercent",
//                    new BigDecimal("0").setScale(2, BigDecimal.ROUND_DOWN)
//                            .toString());
//        }
//        if (result.get("zeroPercent") == null) {
//            result.put("zeroPercent",
//                    new BigDecimal("0").setScale(2, BigDecimal.ROUND_DOWN)
//                            .toString());
//        }
//        if (result.get("hundredTotal") == null) {
//            result.put("hundredTotal", 0);
//        }
//        if (result.get("middleTotal") == null) {
//            result.put("middleTotal", 0);
//        }
//        if (result.get("zeroTotal") == null) {
//            result.put("zeroTotal", 0);
//        }
//        request.setAttribute("dayIntegrity", result.get("dayIntegrity")); // 昨日抄读数据百分比
//        request.setAttribute("hundredPercent", result.get("dayIntegrity")); // 昨日抄读数据百分比
////		request.setAttribute("hundredPercent", result.get("hundredPercent")); // 已抄读到的数据量
//        request.setAttribute("middlePercent", result.get("middlePercent")); // 已抄读数据百分比
//        request.setAttribute("zeroPercent", result.get("zeroPercent")); // 丢失数据量
//        request.setAttribute("hundredTotal", result.get("hundredTotal")); // 丢失数据百分比
//        request.setAttribute("middleTotal", result.get("middleTotal")); // 抄读失败数据量
//        request.setAttribute("zeroTotal", result.get("zeroTotal")); // 抄读失败数据百分比
//        // 日数据和月数据报表的默认时间
//        request.setAttribute(
//                "endDay",
//                DateUtils.date2Str(d,
//                        DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
//        request.setAttribute("startDay", DateUtils.date2Str(
//                FormatUtil.addDaysToDate(d, -6),
//                DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
//        request.setAttribute("start15Days", DateUtils.date2Str(
//                FormatUtil.addDaysToDate(d, -14),
//                DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
//        request.setAttribute("start30Days", DateUtils.date2Str(
//                FormatUtil.addDaysToDate(d, -29),
//                DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
//        SimpleDateFormat sdf = new SimpleDateFormat(" MMMM, yyyy",
//                Locale.ENGLISH);
//        request.setAttribute("endMonth", DateUtils.date2Str(d, sdf));
//        request.setAttribute("startMonth",
//                DateUtils.date2Str(FormatUtil.addMonthsToDate(d, -11), sdf));
//
//        request.setAttribute("start6Months",
//                DateUtils.date2Str(FormatUtil.addMonthsToDate(d, -5), sdf));
//        request.setAttribute("start12Months",
//                DateUtils.date2Str(FormatUtil.addMonthsToDate(d, -11), sdf));
//
//
//        // 以上为完整率数据 下面开始写表计数量的获取
//
//        paramsPie.put("tvType", 1);// day
//
//        // name=manufacturer;model;commType
//        paramsPie.put("tv", tv);
//        paramsPie.put("idType", "2");
//
//
//        List<Map<String, Object>> dataStatisticsDeviceList = dataStatisticsDeviceService
//                .getDataStatisticsDeviceByMaps(paramsPie);
//
//
//        paramsPie.put("idType", "1");
//        List<Map<String, Object>> dataStatisticsDeviceListTotal = dataStatisticsDeviceService
//                .getDataStatisticsDeviceByMaps(paramsPie);
//        paramsPie.put("idType", "11");
//        List<Map<String, Object>> dataStatisticsConcentratorListTotal = dataStatisticsDeviceService
//                .getDataStatisticsDeviceByMaps(paramsPie);
//        paramsPie.put("idType", "21");
//        List<Map<String, Object>> onlineMeterTotal = dataStatisticsDeviceService
//                .getDataStatisticsDeviceByMaps(paramsPie);
//        paramsPie.put("idType", "22");
//        List<Map<String, Object>> onlineCommunicatorTotal = dataStatisticsDeviceService
//                .getDataStatisticsDeviceByMaps(paramsPie);
//        if (onlineMeterTotal != null && onlineMeterTotal.size() > 0) {
//            for (Map<String, Object> di : onlineMeterTotal) {
//                BigDecimal countCurrent = (BigDecimal) di.get("COUNT_CURRENT");
//                BigDecimal percent = (BigDecimal) di.get("PERCENT");
//                request.setAttribute("onlineMeterTotal", countCurrent);
//                request.setAttribute("onlineMeterPercent", percent);
//            }
//        } else {
//            request.setAttribute("onlineMeterTotal", "0");
//            request.setAttribute("onlineMeterPercent", "0");
//        }
//
//        if (onlineCommunicatorTotal != null
//                && onlineCommunicatorTotal.size() > 0) {
//            for (Map<String, Object> di : onlineCommunicatorTotal) {
//                BigDecimal countCurrent = (BigDecimal) di.get("COUNT_CURRENT");
//                BigDecimal percent = (BigDecimal) di.get("PERCENT");
//                request.setAttribute("onlineCommunicatorTotal", countCurrent);
//                request.setAttribute("onlineCommunicatorPercent", percent);
//            }
//        } else {
//            request.setAttribute("onlineCommunicatorTotal", "0");
//            request.setAttribute("onlineCommunicatorPercent", "0");
//        }
//
//
//        List<DataStatisticsDevice> dataStatisticsDeviceListPie = new ArrayList();
//        List<DataStatisticsDevice> dataStatisticsDeviceListPieTotal = new ArrayList();
//        List<DataStatisticsDevice> dataStatisticsConcentratorListPieTotal = new ArrayList();
//        if (dataStatisticsDeviceList != null
//                && dataStatisticsDeviceList.size() > 0) {
//            for (Map<String, Object> di : dataStatisticsDeviceList) {
//                BigDecimal countCurrent = (BigDecimal) di.get("COUNT_CURRENT");
//                BigDecimal percent = (BigDecimal) di.get("PERCENT");
//                String name = (String) di.get("NAME");
//                DataStatisticsDevice dataStatisticsDevice = new DataStatisticsDevice();
//                dataStatisticsDevice.setCountCurrent(countCurrent);
//                dataStatisticsDevice.setPercent(percent);
//                dataStatisticsDevice.setName(name);
//                dataStatisticsDeviceListPie.add(dataStatisticsDevice);
//            }
//        }
//
//        if (dataStatisticsDeviceListTotal != null
//                && dataStatisticsDeviceListTotal.size() > 0) {
//            for (Map<String, Object> di : dataStatisticsDeviceListTotal) {
//                BigDecimal countCurrent = (BigDecimal) di.get("COUNT_CURRENT");
//                DataStatisticsDevice dataStatisticsDevice = new DataStatisticsDevice();
//                dataStatisticsDevice.setCountCurrent(countCurrent);
//                dataStatisticsDeviceListPieTotal.add(dataStatisticsDevice);
//            }
//        }
//
//        if (dataStatisticsConcentratorListTotal != null
//                && dataStatisticsConcentratorListTotal.size() > 0) {
//            for (Map<String, Object> di : dataStatisticsConcentratorListTotal) {
//                BigDecimal countCurrent = (BigDecimal) di.get("COUNT_CURRENT");
//                DataStatisticsDevice dataStatisticsDevice = new DataStatisticsDevice();
//                dataStatisticsDevice.setCountCurrent(countCurrent);
//                dataStatisticsConcentratorListPieTotal
//                        .add(dataStatisticsDevice);
//            }
//        }
//
//
//        // 按点击数倒序
//        Collections.sort(dataStatisticsDeviceListPie,
//                new Comparator<DataStatisticsDevice>() {
//                    public int compare(DataStatisticsDevice arg0,
//                                       DataStatisticsDevice arg1) {
//                        BigDecimal hits0 = arg0.getCountCurrent();
//                        BigDecimal hits1 = arg1.getCountCurrent();
//                        if (hits0.compareTo(hits1) == -1) {
//                            return 1;
//                        } else if (hits0.compareTo(hits1) == 0) {
//                            return 0;
//                        } else {
//                            return -1;
//                        }
//                    }
//                });
//
//        BigDecimal deviceTotal = new BigDecimal(0);
//        BigDecimal deviceTotal1 = new BigDecimal(0);
//        if (StringUtil.isNotEmpty(dataStatisticsDeviceListPieTotal)
//                && dataStatisticsDeviceListPieTotal.size() > 0) {
//            deviceTotal = dataStatisticsDeviceListPieTotal.get(0)
//                    .getCountCurrent();
//            deviceTotal1 = dataStatisticsDeviceListPieTotal.get(0)
//                    .getCountCurrent();
//            request.setAttribute("dataStatisticsDeviceListPieTotal",
//                    dataStatisticsDeviceListPieTotal.get(0).getCountCurrent());
//        }
//
//        List<DataStatisticsDevice> dataStatisticsDeviceListDisplay = new ArrayList();
//        int k = 0;
//        BigDecimal eventDeviceContent = new BigDecimal(0);
//        for (DataStatisticsDevice dataStatisticsDevice1 : dataStatisticsDeviceListPie) {
//            if (k < 4) {
//                eventDeviceContent = eventDeviceContent
//                        .add(dataStatisticsDevice1.getCountCurrent());
//                dataStatisticsDeviceListDisplay.add(dataStatisticsDevice1);
//            }
//            k++;
//        }
//
//        if (k > 4) {
//            BigDecimal deviceTotalDisplay = new BigDecimal(0);
//            BigDecimal subDisplay = new BigDecimal(0);
//            subDisplay = deviceTotal.subtract(eventDeviceContent);
//
//            if (deviceTotal1.compareTo(new BigDecimal(0)) != 0) {
//                deviceTotalDisplay = deviceTotal.subtract(eventDeviceContent)
//                        .divide(deviceTotal1, 2, BigDecimal.ROUND_HALF_UP);
//            }
//
//            DataStatisticsDevice dataStatisticsDevice2 = new DataStatisticsDevice();
//            dataStatisticsDevice2.setPercent(deviceTotalDisplay);
//            dataStatisticsDevice2.setCountCurrent(subDisplay);
//            dataStatisticsDevice2.setName(MutiLangUtil.doMutiLang("home.other"));
//            dataStatisticsDeviceListDisplay.add(dataStatisticsDevice2);
//        }
//
//        if (dataStatisticsDeviceListPie.size() == 0) {
//            DataStatisticsDevice dataStatisticsDevice = new DataStatisticsDevice();
//            dataStatisticsDevice.setPercent(new BigDecimal(0));
//            dataStatisticsDevice.setCountCurrent(new BigDecimal(0));
//            dataStatisticsDevice.setName(MutiLangUtil.doMutiLang("home.noData"));
//            dataStatisticsDeviceListPie.add(dataStatisticsDevice);
//        }
//
//        if (dataStatisticsDeviceListDisplay.size() == 0) {
//            DataStatisticsDevice dataStatisticsDevice = new DataStatisticsDevice();
//            dataStatisticsDevice.setPercent(new BigDecimal(0));
//            dataStatisticsDevice.setCountCurrent(new BigDecimal(0));
//            dataStatisticsDevice.setName(MutiLangUtil.doMutiLang("home.noData"));
//            dataStatisticsDeviceListDisplay.add(dataStatisticsDevice);
//        }
//
//        String deviceInfo = JSONArray.fromObject(
//                dataStatisticsDeviceListDisplay).toString();
//        request.setAttribute("dataStatisticsDeviceListPie",
//                dataStatisticsDeviceListPie);
//        request.setAttribute("deviceInfo", deviceInfo);
//
//        if (StringUtil.isNotEmpty(dataStatisticsConcentratorListPieTotal)
//                && dataStatisticsConcentratorListPieTotal.size() > 0) {
//            request.setAttribute("dataStatisticsConcentratorListPieTotal",
//                    dataStatisticsConcentratorListPieTotal.get(0)
//                            .getCountCurrent());
//        }
//
//        // 以上为表计数量 下面报警数据
//        DataStatisticsEvent dataStatisticsEvent = new DataStatisticsEvent();
//        dataStatisticsEvent.setTvTime(tv);
//        dataStatisticsEvent.setOrgIdList(orgIdDataIntegrityList);
//        List<DataStatisticsEvent> dataStatisticsEventList = dataStatisticsEventService
//                .getListForOrgIds(dataStatisticsEvent);
//        List<DataStatisticsEvent> dataStatisticsEventListNew = new ArrayList();
//        BigDecimal bdTotal = new BigDecimal(0);
//        BigDecimal bdTotal1 = new BigDecimal(0);
//        for (DataStatisticsEvent dataStatisticsEvent1 : dataStatisticsEventList) {
//            if (!"total".equalsIgnoreCase(dataStatisticsEvent1.getEventName())) {
//                dataStatisticsEventListNew.add(dataStatisticsEvent1);
//            } else {
//                bdTotal = dataStatisticsEvent1.getCountCurrent();
//                bdTotal1 = dataStatisticsEvent1.getCountCurrent();
//            }
//        }
//
//        // 按点击数倒序
//        Collections.sort(dataStatisticsEventListNew,
//                new Comparator<DataStatisticsEvent>() {
//                    public int compare(DataStatisticsEvent arg0,
//                                       DataStatisticsEvent arg1) {
//                        BigDecimal hits0 = arg0.getCountCurrent();
//                        BigDecimal hits1 = arg1.getCountCurrent();
//                        if (hits0.compareTo(hits1) == -1) {
//                            return 1;
//                        } else if (hits0.compareTo(hits1) == 0) {
//                            return 0;
//                        } else {
//                            return -1;
//                        }
//                    }
//                });
//
//        List<DataStatisticsEvent> dataStatisticsEventListDisplay = new ArrayList();
//        int i = 0;
//        BigDecimal eventContent = new BigDecimal(0);
//        for (DataStatisticsEvent dataStatisticsEvent1 : dataStatisticsEventListNew) {
////			if (i < 4) {
//            eventContent = eventContent.add(dataStatisticsEvent1
//                    .getCountCurrent());
//            dataStatisticsEventListDisplay.add(dataStatisticsEvent1);
////			}
//            i++;
//        }
//
////		if (i > 4) {
////			BigDecimal bdTotalDisplay = new BigDecimal(0);
////			BigDecimal subDisplay = new BigDecimal(0);
////			subDisplay = bdTotal.subtract(eventContent);
////
////			if (bdTotal1.compareTo(new BigDecimal(0)) != 0) {
////				bdTotalDisplay = bdTotal.subtract(eventContent).divide(
////						bdTotal1, 2, BigDecimal.ROUND_HALF_UP);
////			}
////
////			DataStatisticsEvent dataStatisticsEvent2 = new DataStatisticsEvent();
////			dataStatisticsEvent2.setCountCurrent(subDisplay);
////			dataStatisticsEvent2.setPercent(bdTotalDisplay);
////			dataStatisticsEvent2.setEventName(MutiLangUtil.doMutiLang("home.other"));
////			dataStatisticsEventListDisplay.add(dataStatisticsEvent2);
////		}
////
////		if (dataStatisticsEventListDisplay.size() == 0) {
////			DataStatisticsEvent dataStatisticsEvent1 = new DataStatisticsEvent();
////			dataStatisticsEvent1.setCountCurrent(new BigDecimal(0));
////			dataStatisticsEvent1.setPercent(new BigDecimal(0));
////			dataStatisticsEvent1.setEventName(MutiLangUtil.doMutiLang("home.noData"));
////			dataStatisticsEventListDisplay.add(dataStatisticsEvent1);
////		}
////
////		if (dataStatisticsEventListNew.size() == 0) {
////			DataStatisticsEvent dataStatisticsEvent1 = new DataStatisticsEvent();
////			dataStatisticsEvent1.setCountCurrent(new BigDecimal(0));
////			dataStatisticsEvent1.setPercent(new BigDecimal(0));
////			dataStatisticsEvent1.setEventName(MutiLangUtil.doMutiLang("home.noData"));
////			dataStatisticsEventListNew.add(dataStatisticsEvent1);
////		}
//
//        String eventInfo = JSONArray.fromObject(dataStatisticsEventListDisplay)
//                .toString();
//
//        request.setAttribute("bdTotal", bdTotal);
//        request.setAttribute("eventInfo", eventInfo);
//        request.setAttribute("dataStatisticsEventList",
//                dataStatisticsEventListNew);
//
//        // second page begin 第二页
//
//        Map<String, Object> paramsImportExport = new HashMap<String, Object>();
//        paramsImportExport.put("entityType", "5");
//        paramsImportExport.put("timeType", "7");
//        paramsImportExport.put("timePattern", TIME_FLAG);
//
//        paramsImportExport.put("dataType", "2");
//        paramsImportExport.put("startTime", DateUtils.date2Str(
//                FormatUtil.addDaysToDate(d, 0),
//                DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
//        paramsImportExport.put("endTime", DateUtils.date2Str(
//                FormatUtil.addDaysToDate(d, 0),
//                DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
//        List<String> orgIds = OrganizationUtils.assembleOrgIds(su.getOrgId(),
//                sysOrgService, true, 0);
//        paramsImportExport.put("orgIds", orgIds);
//
//        List<ImportExportReport> importExportReportList = importExportReportService
//                .findImportExportParamList(paramsImportExport);
//        BigDecimal importExportOrgSum = new BigDecimal(0);
//        BigDecimal importExportOrgYesterdayTotal = new BigDecimal(0);
//        List<ImportExportReport> importExportReportDisplay = new ArrayList();
//        if (importExportReportList != null && importExportReportList.size() > 0) {
//            for (ImportExportReport importExportReport1 : importExportReportList) {
//                importExportOrgSum = importExportOrgSum.add(importExportReport1
//                        .getValue());
//            }
//
//            for (ImportExportReport importExportReport1 : importExportReportList) {
//
//                if (importExportOrgSum.compareTo(new BigDecimal(0)) != 0) {
//                    importExportReport1.setPercent(importExportReport1
//                            .getValue()
//                            .divide(importExportOrgSum, 4,
//                                    BigDecimal.ROUND_HALF_UP)
//                            .multiply(new BigDecimal(100)));
//                } else {
//                    importExportReport1.setPercent(new BigDecimal(0));
//                }
//
//            }
//
//            // 按点击数倒序
//            Collections.sort(importExportReportList,
//                    new Comparator<ImportExportReport>() {
//                        public int compare(ImportExportReport arg0,
//                                           ImportExportReport arg1) {
//                            BigDecimal hits0 = arg0.getValue();
//                            BigDecimal hits1 = arg1.getValue();
//                            if (hits0.compareTo(hits1) == -1) {
//                                return 1;
//                            } else if (hits0.compareTo(hits1) == 0) {
//                                return 0;
//                            } else {
//                                return -1;
//                            }
//                        }
//                    });
//
//            int ii = 0;
//            BigDecimal importExport = new BigDecimal(0);
//            for (ImportExportReport importExportReport1 : importExportReportList) {
//                if (ii < 4) {
//                    importExport = importExport.add(importExportReport1
//                            .getValue());
//                    importExportReportDisplay.add(importExportReport1);
//                }
//                ii++;
//            }
//
//            if (ii > 4) {
//                BigDecimal bdTotalDisplay = new BigDecimal(0);
//                BigDecimal subDisplay = new BigDecimal(0);
//                subDisplay = importExportOrgSum.subtract(importExport);
//
//                if (importExportOrgSum.compareTo(new BigDecimal(0)) != 0) {
//                    bdTotalDisplay = importExportOrgSum.subtract(importExport)
//                            .divide(importExportOrgSum, 2,
//                                    BigDecimal.ROUND_HALF_UP);
//                }
//
//                ImportExportReport importExportReport2 = new ImportExportReport();
//                importExportReport2.setValue(subDisplay);
//                importExportReport2.setPercent(bdTotalDisplay);
//                importExportReport2.setOrgName(MutiLangUtil.doMutiLang("home.other"));
//                importExportReportDisplay.add(importExportReport2);
//            }
//
//        }
//
//        if (importExportReportDisplay.size() == 0) {
//            ImportExportReport importExportReport2 = new ImportExportReport();
//            importExportReport2.setValue(new BigDecimal(0));
//            importExportReport2.setPercent(new BigDecimal(0));
//            importExportReport2.setOrgName(MutiLangUtil.doMutiLang("home.noData"));
//            importExportReportDisplay.add(importExportReport2);
//        }
//
//        if (importExportReportList.size() == 0) {
//            ImportExportReport importExportReport2 = new ImportExportReport();
//            importExportReport2.setValue(new BigDecimal(0));
//            importExportReport2.setPercent(new BigDecimal(0));
//            importExportReport2.setOrgName(MutiLangUtil.doMutiLang("home.noData"));
//            importExportReportList.add(importExportReport2);
//        }
//
//        String importExportReportInfo = JSONArray.fromObject(
//                importExportReportDisplay).toString();
//
//        request.setAttribute("importExportReportInfo", importExportReportInfo);
//        request.setAttribute("importExportReportList", importExportReportList);
//
//        // 过去7天
//        paramsImportExport.put("startTime", DateUtils.date2Str(
//                FormatUtil.addDaysToDate(d, -6),
//                DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
//        paramsImportExport.put("endTime", DateUtils.date2Str(
//                FormatUtil.addDaysToDate(d, 0),
//                DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
//
//        List<String> orgIds1 = new ArrayList<>();
//        orgIds1.add(su.getOrgId());
//        paramsImportExport.put("orgIds", orgIds1);
//        List<ImportExportReport> importExportReportList7 = importExportReportService
//                .findImportExportParamList(paramsImportExport);
//
//        List<String> tvList = new ArrayList<String>();
//        List<Double> importExportList = new ArrayList<Double>();
//
//        if (importExportReportList7 != null
//                && importExportReportList7.size() > 0) {
//            for (ImportExportReport importExportReport7 : importExportReportList7) {
//                String time = importExportReport7.getTime();
//                if (DateUtils.date2Str(FormatUtil.addDaysToDate(d, 0),
//                        DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG))
//                        .equals(time)) {
//                    importExportOrgYesterdayTotal = importExportReport7
//                            .getValue();
//                }
//
//                BigDecimal supplyValue = (BigDecimal) importExportReport7
//                        .getValue();
//                importExportList.add(supplyValue.setScale(2,
//                        BigDecimal.ROUND_DOWN).doubleValue());
//
//                tvList.add(DateUtils.date2Str(DateUtils.str2Date(time,
//                        DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)),
//                        DateTimeFormatterUtil
//                                .getSimpleDateFormat_DDMM(TIME_FLAG)));
//            }
//        }
//
//        String tvListInfo = JSONArray.fromObject(tvList).toString();
//        String importExportListInfo = JSONArray.fromObject(importExportList)
//                .toString();
//
//        if (importExportReportList7.size() == 0) {
//            ImportExportReport importExportReport2 = new ImportExportReport();
//            importExportReport2.setValue(new BigDecimal(0));
//            importExportReport2.setTime(DateUtils.date2Str(
//                    FormatUtil.addDaysToDate(d, 0),
//                    DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
//            importExportReportList7.add(importExportReport2);
//        }
//        request.setAttribute("importExportOrgYesterdayTotal",
//                importExportOrgYesterdayTotal);
//        request.setAttribute("tvList", tvListInfo);
//        request.setAttribute("importExportDiagramList", importExportListInfo);
//        request.setAttribute("importExportTableList", importExportReportList7);
//
//        request.setAttribute("orgId", su.getOrgId());
//        paramsImportExport.put("startTime", DateUtils.date2Str(
//                FormatUtil.addDaysToDate(d, 0),
//                DateTimeFormatterUtil.getSimpleDateFormat("5")));
//        paramsImportExport.put("endTime", DateUtils.date2Str(
//                FormatUtil.addDaysToDate(d, 0),
//                DateTimeFormatterUtil.getSimpleDateFormat("5")));
//
//        paramsImportExport.put("idType", USER_TYPE_FLAG + "");
//        paramsImportExport.put("orgIdList", orgIds1);
//
//        List<DataStatisticsCustomer> dataStatisticsCustomerListByUserType = dataStatisticsCustomerService
//                .findDataStatisticsCustomerList(paramsImportExport);
//        List<DataStatisticsCustomer> dataStatisticsCustomerListDisplayUserType = new ArrayList();
//        // 按点击数倒序
//        // 机构供电量总数
//        BigDecimal importExportOrgSumByUserType = new BigDecimal(0);
//
//        if (dataStatisticsCustomerListByUserType != null
//                && dataStatisticsCustomerListByUserType.size() > 0) {
//            for (DataStatisticsCustomer dataStatisticsCustomer1 : dataStatisticsCustomerListByUserType) {
//                BigDecimal countTotal = new BigDecimal(
//                        dataStatisticsCustomer1.getCountTotal());
//                DecimalFormat df = new DecimalFormat("#,###");
//                String countCurrentFormatter = df
//                        .format(dataStatisticsCustomer1.getCountCurrent());
//                String countTotalFormatter = df.format(countTotal);
//
//                dataStatisticsCustomer1
//                        .setCountTotalFormatter(countTotalFormatter);
//                dataStatisticsCustomer1
//                        .setCountCurrentFormatter(countCurrentFormatter);
//
//                importExportOrgSumByUserType = importExportOrgSumByUserType
//                        .add(dataStatisticsCustomer1.getCountCurrent());
//            }
//            if (importExportOrgSumByUserType.compareTo(new BigDecimal(0)) != 0) {
//                for (DataStatisticsCustomer dataStatisticsCustomer1 : dataStatisticsCustomerListByUserType) {
//
//                    dataStatisticsCustomer1.setPercent(dataStatisticsCustomer1
//                            .getCountCurrent()
//                            .divide(importExportOrgSumByUserType, 2,
//                                    BigDecimal.ROUND_HALF_UP)
//                            .multiply(new BigDecimal(100)));
//
//                }
//            }
//
//            // 按点击数倒序
//            Collections.sort(dataStatisticsCustomerListByUserType,
//                    new Comparator<DataStatisticsCustomer>() {
//                        public int compare(DataStatisticsCustomer arg0,
//                                           DataStatisticsCustomer arg1) {
//                            BigDecimal hits0 = arg0.getCountCurrent();
//                            BigDecimal hits1 = arg1.getCountCurrent();
//                            if (hits0.compareTo(hits1) == -1) {
//                                return 1;
//                            } else if (hits0.compareTo(hits1) == 0) {
//                                return 0;
//                            } else {
//                                return -1;
//                            }
//                        }
//                    });
//
//            int ii = 0;
//            BigDecimal importExport = new BigDecimal(0);
//            for (DataStatisticsCustomer dataStatisticsCustomer1 : dataStatisticsCustomerListByUserType) {
//                if (ii < 4) {
//                    importExport = importExport.add(dataStatisticsCustomer1
//                            .getCountCurrent());
//                    dataStatisticsCustomerListDisplayUserType
//                            .add(dataStatisticsCustomer1);
//                }
//                ii++;
//            }
//
//            if (ii > 4) {
//                BigDecimal bdTotalDisplay = new BigDecimal(0);
//                BigDecimal subDisplay = new BigDecimal(0);
//                subDisplay = importExportOrgSumByUserType
//                        .subtract(importExport);
//
//                if (importExportOrgSumByUserType.compareTo(new BigDecimal(0)) != 0) {
//                    bdTotalDisplay = importExportOrgSumByUserType.subtract(
//                            importExport).divide(importExportOrgSumByUserType,
//                            2, BigDecimal.ROUND_HALF_UP);
//                }
//
//                DataStatisticsCustomer dataStatisticsCustomer2 = new DataStatisticsCustomer();
//                dataStatisticsCustomer2.setCountCurrent(subDisplay);
//                dataStatisticsCustomer2.setPercent(bdTotalDisplay);
//                dataStatisticsCustomer2.setName(MutiLangUtil.doMutiLang("home.other"));
//                dataStatisticsCustomerListDisplayUserType
//                        .add(dataStatisticsCustomer2);
//            }
//
//        }
//
//        if (dataStatisticsCustomerListDisplayUserType.size() == 0) {
//            DataStatisticsCustomer dataStatisticsCustomer2 = new DataStatisticsCustomer();
//            dataStatisticsCustomer2.setCountCurrent(new BigDecimal(0));
//            dataStatisticsCustomer2.setPercent(new BigDecimal(0));
//            dataStatisticsCustomer2.setName(MutiLangUtil.doMutiLang("home.noData"));
//            dataStatisticsCustomerListDisplayUserType
//                    .add(dataStatisticsCustomer2);
//        }
//
//        if (dataStatisticsCustomerListByUserType == null
//                || dataStatisticsCustomerListByUserType.size() == 0) {
//            dataStatisticsCustomerListByUserType = new ArrayList<>();
//            DataStatisticsCustomer dataStatisticsCustomer2 = new DataStatisticsCustomer();
//            dataStatisticsCustomer2.setCountCurrent(new BigDecimal(0));
//            dataStatisticsCustomer2.setPercent(new BigDecimal(0));
//            dataStatisticsCustomer2.setName(MutiLangUtil.doMutiLang("home.noData"));
//            dataStatisticsCustomerListByUserType.add(dataStatisticsCustomer2);
//        }
//
//        String dataStatisticsCustomerInfoByUserType = JSONArray.fromObject(
//                dataStatisticsCustomerListDisplayUserType).toString();
//
//        request.setAttribute("importExportReportInfoByType",
//                dataStatisticsCustomerInfoByUserType);
//        request.setAttribute("importExportReportListByType",
//                dataStatisticsCustomerListByUserType);
//
//        return new ModelAndView("/main/home");
//	}
	
	@RequestMapping(value  = "getSupplyStatistic")
	@ResponseBody
    public AjaxJson getSupplyStatistic(String startTv, String endTv) {
		AjaxJson j = new AjaxJson();
		
		Date d = new Date();
		d = FormatUtil.addDaysToDate(d, -1);
		SysUser su = TokenManager.getToken();
		
		Map<String, Object> paramsImportExport = new HashMap<String, Object>();
    	paramsImportExport.put("entityType", "5");
    	paramsImportExport.put("timeType", "1");
    	paramsImportExport.put("timePattern", TIME_FLAG);
    	paramsImportExport.put("dataType", "2");
    	
    	Date stratDate = DateUtils.str2Date(startTv, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG));
    	Date endDate = DateUtils.str2Date(endTv, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG));
		paramsImportExport.put("startTime", DateUtils.date2Str(stratDate, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
    	paramsImportExport.put("endTime", DateUtils.date2Str(endDate, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
    	
    	List<String> orgIds1 = new ArrayList();
    	orgIds1.add(su.getOrgId());
    	paramsImportExport.put("orgIds", orgIds1);
    	
    	List<ImportExportReport> importExportReportList7 = importExportReportService.findImportExportParamList(paramsImportExport);
     	
		List<String> tvList = new ArrayList<String>();
		List<Double> importExportList = new ArrayList<Double>();
    	
		if (importExportReportList7 != null && importExportReportList7.size() > 0) {
			for (ImportExportReport  importExportReport7 : importExportReportList7) {
				String time = importExportReport7.getTime();
				
				BigDecimal supplyValue = (BigDecimal) importExportReport7.getValue();
				importExportList.add(supplyValue.setScale(2, BigDecimal.ROUND_DOWN)
						.doubleValue());
				
				tvList.add(DateUtils.date2Str(
						DateUtils.str2Date(time, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)),
						DateTimeFormatterUtil.getSimpleDateFormat_DDMM(TIME_FLAG)));		
			}
		}
		
		if(importExportReportList7.size() == 0){
			ImportExportReport importExportReport2 = new ImportExportReport();
			importExportReport2.setValue(new BigDecimal(0));
            importExportReport2.setTime(DateUtils.date2Str(FormatUtil.addDaysToDate(d, 0), DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
            importExportReportList7.add(importExportReport2);    
		}
		
		Map<String, Object> attributes = new HashMap<String, Object>();
		attributes.put("tvList", tvList);
		attributes.put("importExportDiagramList", importExportReportList7);
		j.setAttributes(attributes);
		
		return j;
	}
	
	@RequestMapping(value  = "getSupplyStatisticMonth")
	@ResponseBody
    public AjaxJson getSupplyStatisticMonth(String startMonth, String endMonth) {
		AjaxJson j = new AjaxJson();
		
		Date d = new Date();
		d = FormatUtil.addDaysToDate(d, -1);
		SysUser su = TokenManager.getToken();
		Map<String, Object> paramsImportExport = new HashMap<String, Object>();
		paramsImportExport.put("entityType", "5");
		paramsImportExport.put("timeType", "2");
		paramsImportExport.put("timePattern", TIME_FLAG);
		paramsImportExport.put("dataType", "2");
		

		SimpleDateFormat sdf = new SimpleDateFormat(" MMMM, yyyy",
					Locale.ENGLISH);
	
		
		Date stratDate = DateUtils.str2Date(startMonth, sdf);
		Date endDate = DateUtils.str2Date(endMonth, sdf);
		paramsImportExport.put("startTime", DateUtils.date2Str(stratDate, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
		paramsImportExport.put("endTime", DateUtils.date2Str(endDate, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
		
		List<String> orgIds1 = new ArrayList();
		orgIds1.add(su.getOrgId());
		paramsImportExport.put("orgIds", orgIds1);
		
		List<ImportExportReport> importExportReportList7 = importExportReportService.findImportExportParamList(paramsImportExport);
	 	
		List<String> tvList = new ArrayList<String>();
		List<Double> importExportList = new ArrayList<Double>();
		if (importExportReportList7 != null && importExportReportList7.size() > 0) {
			for (ImportExportReport  importExportReport7 : importExportReportList7) {
				String time = importExportReport7.getTime();
				
				BigDecimal supplyValue = (BigDecimal) importExportReport7.getValue();
				importExportList.add(supplyValue.setScale(2, BigDecimal.ROUND_DOWN)
						.doubleValue());
				SimpleDateFormat MMyyyy = new SimpleDateFormat("MM-yyyy");
				SimpleDateFormat MMyyyy1 = new SimpleDateFormat("MM/yyyy");
				tvList.add(DateUtils.date2Str(
						
						DateUtils.str2Date(time, MMyyyy1),
						MMyyyy));		
			}
		}
	
		if(importExportReportList7.size() == 0){
			ImportExportReport importExportReport2 = new ImportExportReport();
			importExportReport2.setValue(new BigDecimal(0));
	        importExportReport2.setTime(DateUtils.date2Str(FormatUtil.addDaysToDate(d, 0), DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
	        importExportReportList7.add(importExportReport2);    
		}
		
		Map<String, Object> attributes = new HashMap<String, Object>();
		attributes.put("tvList", tvList);
		attributes.put("importExportDiagramList", importExportReportList7);
		j.setAttributes(attributes);
		
		return j;
	}
		
	@RequestMapping(value  = "meterCount")
	@ResponseBody
    public AjaxJson meterCount(HttpServletRequest request,  String name, Model model) {
		//获取昨日日期字符串
		Date d = new Date();
		Map<String, Object> paramsPie = new HashMap<String, Object>();
		d = FormatUtil.addDaysToDate(d, -1);
		String tv = DateUtils.date2Str(d, DateUtils.date_sdf);
		//以上为完整率数据 下面开始写表计数量的获取
		paramsPie.put("tvType", 1);// day
		// name=manufacturer;model;commType
		if (StringUtil.isNotEmpty(name)) {
			if (name.equals("manufacturer")) {
				paramsPie.put("idType", "2");
			} else if (name.equals("model")) {
				paramsPie.put("idType", "3");
			} else if (name.equals("commType")) {
				paramsPie.put("idType", "4");
			}else if (name.equals("organization")) {
				paramsPie.put("idType", "1");
				paramsPie.put("idTypeOrg", "21");
			}
		}
		
		paramsPie.put("tv",tv);
		SysUser su = TokenManager.getToken();

		if(name.equals("organization")){			
			List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,true,1);
			orgIdList.add(0, su.getOrgId());
			paramsPie.put("orgIdList", orgIdList);
		}else{
			List<String> orgIdDataIntegrityList = new ArrayList();
			orgIdDataIntegrityList.add(su.getOrgId());
			paramsPie.put("orgIdList", orgIdDataIntegrityList);
		}

		List<Map<String, Object>> dataStatisticsDeviceList = dataStatisticsDeviceService.getDataStatisticsDeviceByMaps(paramsPie);

		List<Map<String, Object>> dataStatisticsDeviceListTotal = dataStatisticsDeviceService.getDataStatisticsDeviceByMaps(paramsPie);
		List<DataStatisticsDevice> dataStatisticsDeviceListPieTotal = new ArrayList();
		List<DataStatisticsDevice> dataStatisticsDeviceListPie = new ArrayList();

		if (dataStatisticsDeviceList != null && dataStatisticsDeviceList.size() > 0) {
			BigDecimal sonCount = new BigDecimal(0);
			Map<String, Object> myDi = null;
			BigDecimal myCount = null;
			for (Map<String, Object> di : dataStatisticsDeviceList) {
				BigDecimal countCurrent = (BigDecimal) di.get("COUNT_CURRENT");
				String ID =  (String) di.get("ID");
				if (!su.getOrgId().equals(ID)) {
					sonCount = sonCount.add(countCurrent);
				} else {
					myDi = di;
					myCount = countCurrent;
				}
			}
			if (myDi != null) {
				
				myDi.put("COUNT_CURRENT", myCount.subtract(sonCount));
			}
			
			for (Map<String, Object> di : dataStatisticsDeviceList) {
				BigDecimal countCurrent = (BigDecimal) di.get("COUNT_CURRENT");
				BigDecimal percent = (BigDecimal) di.get("PERCENT");
				String name1 = (String) di.get("NAME");
				DataStatisticsDevice dataStatisticsDevice = new DataStatisticsDevice();
		
				DecimalFormat df = new DecimalFormat("#,###");
				String countCurrentFormatter = df.format(countCurrent);
	
				dataStatisticsDevice.setCountCurrentFormatter(countCurrentFormatter);
				dataStatisticsDevice.setCountCurrent(countCurrent);
				dataStatisticsDevice.setPercent(percent);
				dataStatisticsDevice.setName(name1);
				if(countCurrent.compareTo(new BigDecimal(0)) != 0 ){
					dataStatisticsDeviceListPie.add(dataStatisticsDevice);
				}
			
			}
		}
		
		
		// 按点击数倒序
        Collections.sort(dataStatisticsDeviceListPie, new Comparator<DataStatisticsDevice>() {
            public int compare(DataStatisticsDevice arg0, DataStatisticsDevice arg1) {
            	BigDecimal hits0 = arg0.getCountCurrent();
            	BigDecimal hits1 = arg1.getCountCurrent();
                if (hits0.compareTo(hits1) == -1) {
                    return 1;
                } else if (hits0.compareTo(hits1) == 0) {
                    return 0;
                } else {
                    return -1;
                }
            }
        });
		
		if (dataStatisticsDeviceListTotal != null && dataStatisticsDeviceListTotal.size() > 0) {
			BigDecimal totalPlus = new BigDecimal(0);
			for (Map<String, Object> di : dataStatisticsDeviceListTotal) {
				BigDecimal countCurrent = (BigDecimal) di.get("COUNT_CURRENT");
				totalPlus = totalPlus.add(countCurrent);
			}
			DataStatisticsDevice dataStatisticsDevice = new DataStatisticsDevice();
			dataStatisticsDevice.setCountCurrent(totalPlus);
			dataStatisticsDeviceListPieTotal.add(dataStatisticsDevice);
		}
		
		BigDecimal deviceTotal = new BigDecimal(0);
    	BigDecimal deviceTotal1 = new BigDecimal(0);
		if(StringUtil.isNotEmpty(dataStatisticsDeviceListPieTotal) && dataStatisticsDeviceListPieTotal.size() > 0){
			deviceTotal = dataStatisticsDeviceListPieTotal.get(0).getCountCurrent();
			deviceTotal1 = dataStatisticsDeviceListPieTotal.get(0).getCountCurrent();
			request.setAttribute("dataStatisticsDeviceListPieTotal", dataStatisticsDeviceListPieTotal.get(0).getCountCurrent());	
		}
	
	  	List<DataStatisticsDevice> dataStatisticsDeviceListDisplay = new ArrayList();
        int k =0;
    	BigDecimal eventDeviceContent = new BigDecimal(0);
        for(DataStatisticsDevice dataStatisticsDevice1 : dataStatisticsDeviceListPie){
        	if(k < 4){
        		eventDeviceContent = eventDeviceContent.add(dataStatisticsDevice1.getCountCurrent());
        		dataStatisticsDeviceListDisplay.add(dataStatisticsDevice1);
        	}
        	k++;
        }
        
        if(k > 4){
            BigDecimal deviceTotalDisplay = new BigDecimal(0);
            BigDecimal subDisplay = new BigDecimal(0);
            subDisplay = deviceTotal.subtract(eventDeviceContent);
            if(deviceTotal1.compareTo(new BigDecimal(0)) != 0 ){
            	deviceTotalDisplay = deviceTotal.subtract(eventDeviceContent).divide(deviceTotal1, 2, BigDecimal.ROUND_HALF_UP);
			}
	     
	        DataStatisticsDevice dataStatisticsDevice2 = new DataStatisticsDevice();
	        dataStatisticsDevice2.setCountCurrent(subDisplay);
	        dataStatisticsDevice2.setPercent(deviceTotalDisplay);
	        dataStatisticsDevice2.setName(MutiLangUtil.doMutiLang("home.other"));
	        dataStatisticsDeviceListDisplay.add(dataStatisticsDevice2);
        }

        if(dataStatisticsDeviceListDisplay.size() == 0){
        	DataStatisticsDevice dataStatisticsDevice2 = new DataStatisticsDevice();
   	        dataStatisticsDevice2.setCountCurrent(new BigDecimal(0));
   	        dataStatisticsDevice2.setPercent(new BigDecimal(0));
   	        dataStatisticsDevice2.setName(MutiLangUtil.doMutiLang("home.noData"));
   	        dataStatisticsDeviceListDisplay.add(dataStatisticsDevice2);
        }
        
		String deviceInfo = JSONArray.fromObject(dataStatisticsDeviceListDisplay).toString();

		AjaxJson j=new AjaxJson();
		Map<String, Object> attributes = new HashMap<>();
		
		if(name.equals("organization")){		
			BigDecimal totalCount = new BigDecimal(0);
			
			for(DataStatisticsDevice dataStatisticsDevice : dataStatisticsDeviceListPie){
				totalCount = totalCount.add(dataStatisticsDevice.getCountCurrent());
			}
			
			for(DataStatisticsDevice dataStatisticsDevice : dataStatisticsDeviceListPie){
				dataStatisticsDevice.setPercent(dataStatisticsDevice.getCountCurrent().divide(totalCount, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)));
			}
			
		}
		
		if(dataStatisticsDeviceListPie.size() == 0 ){
			DataStatisticsDevice dataStatisticsDevice  = new DataStatisticsDevice();
			dataStatisticsDevice.setCountCurrent(new BigDecimal(0));
			dataStatisticsDevice.setPercent(new BigDecimal(0));
			dataStatisticsDevice.setName(MutiLangUtil.doMutiLang("home.noData"));
			dataStatisticsDeviceListPie.add(dataStatisticsDevice);
		}
		
		attributes.put("dataStatisticsDeviceListPie", dataStatisticsDeviceListPie);
		attributes.put("deviceInfo", deviceInfo);
	    j.setAttributes(attributes);
	    return j;
    }

	/**
	 * 语言设置
	 * @return
	 */
	public List<String> getLanguageList(){
		List<String> languageList = new ArrayList<>();
		String defaultParame = "en-US";
		List<String> types = new ArrayList<>();
		types.add("language.zh-CN");
		types.add("language.ru");
		types.add("language.ar");
		types.add("language.es");
		types.add("language.fr");
		languageList.add(defaultParame);
		try{
			for (String item : types){
				if(!ResourceUtil.getSessionattachmenttitle(item).isEmpty()){
					languageList.add(ResourceUtil.getSessionattachmenttitle(item));
				}
			}
		}  catch (Exception e) {
				e.printStackTrace();
		}
		return languageList;
	}

	/**
	 * 登录页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 * @throws UnsupportedEncodingException 
	 */
	@RequestMapping(value = "login")
	public ModelAndView login(HttpServletRequest request, Model model) throws UnsupportedEncodingException {
		String version = ResourceUtil.getSessionattachmenttitle("hesweb.system.version");
		String defaultLanguage = ResourceUtil.getSessionattachmenttitle("hesweb.system.language");

		request.getSession(true).setAttribute("languageConf", getLanguageList());

		request.getSession(true).setAttribute(Globals.HESWEB_SYSTEM_VERSION,version);
		
		 Cookie cookies[] = request.getCookies(); // 获取Cookie中的所有内容
	        Cookie sCookie = null;
	        if (cookies != null) {
	            for (int i = 0; i < cookies.length; i++ ) {
	                sCookie = cookies[i];
	                if (sCookie != null) {
	                    if (sCookie.getName().equals("hesWebLanguage")) {
	                        String language = URLDecoder.decode(sCookie.getValue(), "UTF-8");
	                        // System.out.println("language:"+language);
	                        request.getSession(true).setAttribute("language", language);
	                    }
	                }
	            }
	        }else{
	        	  request.getSession(true).setAttribute("language", defaultLanguage);	
	        }	
	        MutiLangUtil.setRequest(request);
	        
	        request.getSession(true).setAttribute("loginName",
	                MutiLangUtil.doMutiLang("login.username"));
	            // System.out.println(MutiLangUtil.doMutiLang("login.loginName"));
	            request.getSession(true).setAttribute("loginPassword",
	                MutiLangUtil.doMutiLang("login.pswd"));
		
		return new ModelAndView("/login/login");
	}

	/**
	 * 提示未授权界面
	 * 
	 * @return
	 */
	@RequestMapping(value = "to403", method = RequestMethod.GET)
	public ModelAndView to403() {
		return new ModelAndView("/common/403");
	}
	
	@RequestMapping(value = "to404", method = RequestMethod.GET)
	public ModelAndView to404() {
		return new ModelAndView("/common/404");
	}
	
	@RequestMapping(value = "to500", method = RequestMethod.GET)
	public ModelAndView to505() {
		return new ModelAndView("/common/500");
	}
	
	@RequestMapping(value = "noPermissions", method = RequestMethod.GET)
	public ModelAndView noPermissions() {
		return new ModelAndView("/common/noPermissions");
	}

	/**
	 * 登录
	 * 
	 * @param su
	 * @param rememberMe
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "doLogin", method = RequestMethod.POST)
	@ResponseBody
	public AjaxJson doLogin(SysUser su,String language, Boolean rememberMe,
			HttpServletRequest request, HttpServletResponse response) {
		AjaxJson j = new AjaxJson();
		try {
			su.setUsername(su.getUsername().trim());
			SysUser s=sysUserService.get(su);

			if(s==null){
				j.setErrorMsg(MutiLangUtil.doMutiLang("login.userDontExist"));
				return j;
			}
			
//			Date d = new Date();
	       //首次登陆修改密码
			
			
//			DataUserLog dataUserLog  = new DataUserLog();
//			
//			String[] userIds = {s.getId()};
//			String[] logTypes = {"UserResetPassword"};
//			String[] logSubTypes = {"UserResetPassword"};
//	
//			dataUserLog.setUserIds(userIds);
//			dataUserLog.setLogTypes(logTypes);
//			dataUserLog.setLogSubTypes(logSubTypes);
//			Date datePre15 = DateUtils.getDateAdd(d, 5, -90);
//			dataUserLog.setStartDate(datePre15);
//			dataUserLog.setEndDate(d);
//			List<DataUserLog> dataUserLogList = dataUserLogService.getList(dataUserLog);
//			if(dataUserLogList == null || dataUserLogList.size() == 0){
//				j.setErrorMsg(MutiLangUtil.doMutiLang("login.toRevisePwd"));
//				return j;
//			}
						
			
			// 默认不使用记住我这个功能。
			rememberMe = true;
			
			su = TokenManager.login(su, rememberMe);
		
			SavedRequest savedRequest = WebUtils.getSavedRequest(request);
			String url = null;
			if (null != savedRequest) {
				url = savedRequest.getRequestUrl();
			}
			Cookie cookie = new Cookie("rememberMe", rememberMe.toString());
			response.addCookie(cookie);
			if (StringUtil.isBlank(url)) {
//				String path = request.getContextPath();
//				String basePath ="http://" + request.getServerName() + ":8080" + path;
//				
//				url = basePath + "/systemController/index.do";
			
				url = request.getContextPath() + "/systemController/index.do";
			}
			HttpSession session = request.getSession(true);
			session.setAttribute(Globals.SESSION_LOGIN_SYS_USER, su);

			MutiLangUtil.setUserLangCode(StringUtil.lowerCase(language));
			
			// 获取权限；
			List<DictMenu> menus = sysMenuService.getMenusByUserId(su);
//			DictMenu dictMenu = sysMenuService.getEntity("1e858f11beda11e79bb968f728c516f9");
//			DictMenu dictMenu1 = new DictMenu();
//			dictMenu1.setId("1e858f11beda11e79bb968f728c516f9");
//			List<DictMenu> menus1 = sysMenuService.getList(dictMenu1);
			session.setAttribute("menus", menus);
			List<String> menuUrls=sysMenuService.getMenusUrlUserId(su);
			session.setAttribute("menuUrls", menuUrls);
			
			//获取全部菜单
			List<String> allMenuUrls=sysMenuService.getMenusUrlUserId(null);
			session.setAttribute("allMenuUrls", allMenuUrls);
			
			//菜单TAB隐藏项
//			Map<String, String> hideTab = sysMenuService.getMenusHideTab();
//			session.setAttribute("hideTab", hideTab);
			
			
			// 获取配置参数列表并保存在session

			dataUserLogService.insertDataUserLog(su.getId(), "User Login", "Login", "Login in to HES (IP Address="+ IpUtil.getIpAddr(request)+")");
			j.setMsg("OK！");
			j.setObj(url);
		} catch (AccountException e) {
			j.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			j.setErrorMsg(e.getMessage());
		}
		return j;
	}
	
	

	/**
	 * 登录
	 * 
	 * @param su
	 * @param rememberMe
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "doLoginGet", method = RequestMethod.GET)
	@ResponseBody
	public ModelAndView doLoginGet(SysUser su,String language, Boolean rememberMe,
			HttpServletRequest request, HttpServletResponse response) {
		AjaxJson j = new AjaxJson();
		try {
			su.setUsername(su.getUsername().trim());
			SysUser s=sysUserService.get(su);

			if(s==null){
				j.setErrorMsg(MutiLangUtil.doMutiLang("login.userDontExist"));
			//	return j;
			}
			
			// 默认不使用记住我这个功能。
			rememberMe = false;
			
			su = TokenManager.login(su, rememberMe);
		
			SavedRequest savedRequest = WebUtils.getSavedRequest(request);
			String url = null;
			if (null != savedRequest) {
				url = savedRequest.getRequestUrl();
			}
			Cookie cookie = new Cookie("rememberMe", rememberMe.toString());
			response.addCookie(cookie);
			if (StringUtil.isBlank(url)) {
				url = request.getContextPath() + "/systemController/index.do";
			}
			HttpSession session = request.getSession(true);
			session.setAttribute(Globals.SESSION_LOGIN_SYS_USER, su);

			MutiLangUtil.setUserLangCode(StringUtil.lowerCase(language));
			
			// 获取权限；
			List<DictMenu> menus = sysMenuService.getMenusByUserId(su);
			session.setAttribute("menus", menus);
			List<String> menuUrls=sysMenuService.getMenusUrlUserId(su);
			session.setAttribute("menuUrls", menuUrls);
			
			//获取全部菜单
			List<String> allMenuUrls=sysMenuService.getMenusUrlUserId(null);
			session.setAttribute("allMenuUrls", allMenuUrls);
			// 获取配置参数列表并保存在session

			dataUserLogService.insertDataUserLog(su.getId(), "User Login", "Login", "Login in to HES (IP Address="+ IpUtil.getIpAddr(request)+")");
			j.setMsg("OK！");
			j.setObj(url);
		} catch (AccountException e) {
			j.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			j.setErrorMsg(e.getMessage());
		}
		
		 return new ModelAndView("/main/index");
	}
	
	/**
	 * 添加收藏
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	@RequestMapping(value = "addIsCollect", method = RequestMethod.POST)
	@ResponseBody
	public AjaxJson addIsCollect(String searchType,String id) throws UnsupportedEncodingException {
		AjaxJson j = new AjaxJson();
		SysUser su = TokenManager.getToken();
		//Meter  //Commnuicator //Line //Transformer //Customer
	    //1,2,3,4,6
		try {
			if(searchType.equals("Meter")){
				AssetFavorite entity=new AssetFavorite();
				entity.setId(id);
				entity.setUserId(su.getId());
				entity.setIdType(1);
				if(assetFavoriteService.getCount(entity)>0){
					assetFavoriteService.delete(entity);
				}else{
					assetFavoriteService.save(entity);
				}
				
			}else if(searchType.equals("Commnuicator")){
				AssetFavorite entity=new AssetFavorite();
				entity.setId(id);
				entity.setUserId(su.getId());
				entity.setIdType(2);
				if(assetFavoriteService.getCount(entity)>0){
					assetFavoriteService.delete(entity);
				}else{
					assetFavoriteService.save(entity);
				}
			}else if(searchType.equals("Line")){
				AssetFavorite entity=new AssetFavorite();
				entity.setId(id);
				entity.setUserId(su.getId());
				entity.setIdType(3);
				if(assetFavoriteService.getCount(entity)>0){
					assetFavoriteService.delete(entity);
				}else{
					assetFavoriteService.save(entity);
				}
			}else if(searchType.equals("Transformer")){
				AssetFavorite entity=new AssetFavorite();
				entity.setId(id);
				entity.setUserId(su.getId());
				entity.setIdType(4);
				if(assetFavoriteService.getCount(entity)>0){
					assetFavoriteService.delete(entity);
				}else{
					assetFavoriteService.save(entity);
				}
			}else if(searchType.equals("Customer")){
				AssetFavorite entity=new AssetFavorite();
				entity.setId(id);
				entity.setUserId(su.getId());
				entity.setIdType(6);
				if(assetFavoriteService.getCount(entity)>0){
					assetFavoriteService.delete(entity);
				}else{
					assetFavoriteService.save(entity);
				}
			}
		} catch (Exception e) {
			j.setErrorMsg(e.getMessage());
		}
		return j;
	}

	/**
	 * 退出
	 * 
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	@RequestMapping(value = "logout", method = RequestMethod.GET)
	@ResponseBody
	public AjaxJson logout() throws UnsupportedEncodingException {
		AjaxJson j = new AjaxJson();
		try {
			TokenManager.logout();
		} catch (Exception e) {
			j.setErrorMsg(e.getMessage());
		}
		return j;
	}
	/**
	 * 在线校验
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	@RequestMapping(value = "onlineVerification", method = RequestMethod.POST)
	@ResponseBody
	public AjaxJson onlineVerification() throws UnsupportedEncodingException {
		AjaxJson j = new AjaxJson();
		try {
			SysUser token = TokenManager.getToken();
			j.put("sysUser", token);
		} catch (Exception e) {
			j.setErrorMsg(e.getMessage());
		}
		return j;
	}

	
	@RequestMapping(value  = "typeCount")
	@ResponseBody
    public AjaxJson typeCount(HttpServletRequest request,  String name, Model model) {
		//获取昨日日期字符串
		Date d = new Date();
		Map<String, Object> paramsPie = new HashMap<String, Object>();
		d = FormatUtil.addDaysToDate(d, -1);
		String tv = DateUtils.date2Str(d, DateUtils.date_sdf);
		//以上为完整率数据 下面开始写表计数量的获取
		paramsPie.put("tvType", 1);// day
		// name=manufacturer;model;commType
		if (StringUtil.isNotEmpty(name)) {
			if (name.equals("customerType")) {
				paramsPie.put("idType", 12);
			} else if (name.equals("industryType")) {
				paramsPie.put("idType", 13);
			} 
		}
		
		AjaxJson j=new AjaxJson();
		Map<String, Object> attributes = new HashMap<>();
		
		paramsPie.put("startTime",DateUtils.date2Str(FormatUtil.addDaysToDate(d, 0), DateTimeFormatterUtil.getSimpleDateFormat("5")));
		paramsPie.put("endTime",DateUtils.date2Str(FormatUtil.addDaysToDate(d, 0), DateTimeFormatterUtil.getSimpleDateFormat("5")));
		
		SysUser su = TokenManager.getToken();

		List<String> orgIdDataIntegrityList = new ArrayList();
		orgIdDataIntegrityList.add(su.getOrgId());
		paramsPie.put("orgIdList", orgIdDataIntegrityList);
		
		List<DataStatisticsCustomer> dataStatisticsCustomerListByUserType = dataStatisticsCustomerService.findDataStatisticsCustomerList(paramsPie);
    	List<DataStatisticsCustomer> dataStatisticsCustomerListDisplayUserType = new ArrayList();
    	// 按点击数倒序
    	 //机构供电量总数
    	BigDecimal importExportOrgSumByUserType = new BigDecimal(0);

    	if(dataStatisticsCustomerListByUserType != null && dataStatisticsCustomerListByUserType.size() > 0){
    		for(DataStatisticsCustomer dataStatisticsCustomer1 : dataStatisticsCustomerListByUserType){
    	    	BigDecimal countTotal = new BigDecimal(dataStatisticsCustomer1.getCountTotal());
    			DecimalFormat df = new DecimalFormat("#,###");
				String countCurrentFormatter = df.format(dataStatisticsCustomer1.getCountCurrent());
				String countTotalFormatter = df.format(countTotal);
				
				dataStatisticsCustomer1.setCountTotalFormatter(countTotalFormatter);
				dataStatisticsCustomer1.setCountCurrentFormatter(countCurrentFormatter);
    			
    			importExportOrgSumByUserType = importExportOrgSumByUserType.add(dataStatisticsCustomer1.getCountCurrent());
    		}
    		 if(importExportOrgSumByUserType.compareTo(new BigDecimal(0)) != 0 ){
    			 for(DataStatisticsCustomer dataStatisticsCustomer1 : dataStatisticsCustomerListByUserType){
    		        	
    	    			dataStatisticsCustomer1.setPercent(dataStatisticsCustomer1.getCountCurrent().divide(importExportOrgSumByUserType, 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)));	
    	    			
    	    		} 
    		 }
    		
    		// 按点击数倒序
	        Collections.sort(dataStatisticsCustomerListByUserType, new Comparator<DataStatisticsCustomer>() {
	            public int compare(DataStatisticsCustomer arg0, DataStatisticsCustomer arg1) {
	            	BigDecimal hits0 = arg0.getCountCurrent();
	            	BigDecimal hits1 = arg1.getCountCurrent();
	                if (hits0.compareTo(hits1) == -1) {
	                    return 1;
	                } else if (hits0.compareTo(hits1) == 0) {
	                    return 0;
	                } else {
	                    return -1;
	                }
	            }
	        });
	      
	        
	        int ii =0;
	    	BigDecimal importExport = new BigDecimal(0);
	        for(DataStatisticsCustomer dataStatisticsCustomer1 : dataStatisticsCustomerListByUserType){
	        	if(ii < 4){
	        		importExport = importExport.add(dataStatisticsCustomer1.getCountCurrent());
	        		dataStatisticsCustomerListDisplayUserType.add(dataStatisticsCustomer1);
	        	}
	        	ii++;
	        }
	        
	        if(ii > 4){
	        	 BigDecimal bdTotalDisplay = new BigDecimal(0);
	        	 BigDecimal subDisplay = new BigDecimal(0);
	        	 subDisplay = importExportOrgSumByUserType.subtract(importExport);
	        	 
	        	 if(importExportOrgSumByUserType.compareTo(new BigDecimal(0)) != 0 ){
	                  bdTotalDisplay = importExportOrgSumByUserType.subtract(importExport).divide(importExportOrgSumByUserType, 2, BigDecimal.ROUND_HALF_UP);
				 }

	        	 DataStatisticsCustomer dataStatisticsCustomer2 = new DataStatisticsCustomer();
	        	 dataStatisticsCustomer2.setCountCurrent(subDisplay);
	        	 dataStatisticsCustomer2.setPercent(bdTotalDisplay);
	        	 dataStatisticsCustomer2.setName(MutiLangUtil.doMutiLang("home.other"));
	        	 dataStatisticsCustomerListDisplayUserType.add(dataStatisticsCustomer2);
	        }
	        
    	}
    	
    	if(dataStatisticsCustomerListByUserType == null || dataStatisticsCustomerListByUserType.size() == 0){
   		 DataStatisticsCustomer dataStatisticsCustomer2 = new DataStatisticsCustomer();
       	 dataStatisticsCustomer2.setCountCurrent(new BigDecimal(0));
       	 dataStatisticsCustomer2.setPercent(new BigDecimal(0));
       	 dataStatisticsCustomer2.setName(MutiLangUtil.doMutiLang("home.noData"));
       	 dataStatisticsCustomerListByUserType.add(dataStatisticsCustomer2);
    	}
    	
    	if(dataStatisticsCustomerListDisplayUserType == null || dataStatisticsCustomerListDisplayUserType.size() == 0){
      		 DataStatisticsCustomer dataStatisticsCustomer2 = new DataStatisticsCustomer();
          	 dataStatisticsCustomer2.setCountCurrent(new BigDecimal(0));
          	 dataStatisticsCustomer2.setPercent(new BigDecimal(0));
          	 dataStatisticsCustomer2.setName(MutiLangUtil.doMutiLang("home.noData"));
          	dataStatisticsCustomerListDisplayUserType.add(dataStatisticsCustomer2);
       	}
    	
    	
    	
    	String dataStatisticsCustomerInfoByUserType = JSONArray.fromObject(dataStatisticsCustomerListDisplayUserType).toString();
    	
	
		attributes.put("importExportReportInfoByType", dataStatisticsCustomerInfoByUserType);
		attributes.put("importExportReportListByType", dataStatisticsCustomerListByUserType);
	    j.setAttributes(attributes);
	    return j;
    }
	
	/**
	 * 获取当前时间
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	@RequestMapping(value = "currentTime", method = RequestMethod.POST)
	@ResponseBody
	public AjaxJson currentTime() throws UnsupportedEncodingException {
		AjaxJson j = new AjaxJson();
		try {
			j.setMsg(DateUtils.date2Str(new Date(), DateTimeFormatterUtil.getSimpleDateFormat_HHmmss(TIME_FLAG)));
		} catch (Exception e) {
			j.setErrorMsg(e.getMessage());
		}
		return j;
	}

}