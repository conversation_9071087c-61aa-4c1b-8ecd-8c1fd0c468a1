package com.clou.esp.hes.app.web.service.asset;

import java.util.List;

import com.clou.esp.hes.app.web.model.asset.AssetCalcObj;
import com.clou.esp.hes.app.web.model.asset.AssetTransformer;
import com.clou.esp.hes.app.web.service.common.CommonService;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;


/**
 * @ClassName: AssetTransformerService
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年9月20日 上午9:04:48
 *
 */
public interface AssetTransformerService extends
		CommonService<AssetTransformer> {
	List<AssetCalcObj> getListByCalObj(JqGridSearchTo jqGridSearchTo);
	JqGridResponseTo getListByCalObjJqGrid(JqGridSearchTo jqGridSearchTo);
}
