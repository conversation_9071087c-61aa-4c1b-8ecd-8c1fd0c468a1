/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class PpmCustomerHistoryTariff{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2019-09-25 03:59:13
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.tariff;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.tariff.PpmCustomerHistoryTariffDao;
import com.clou.esp.hes.app.web.model.tariff.PpmCustomerHistoryTariff;
import com.clou.esp.hes.app.web.service.tariff.PpmCustomerHistoryTariffService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("ppmCustomerHistoryTariffService")
public class PpmCustomerHistoryTariffServiceImpl  extends CommonServiceImpl<PpmCustomerHistoryTariff>  implements PpmCustomerHistoryTariffService {

	@Resource
	private PpmCustomerHistoryTariffDao ppmCustomerHistoryTariffDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(ppmCustomerHistoryTariffDao);
    }
	@SuppressWarnings("rawtypes")
	public PpmCustomerHistoryTariffServiceImpl() {}
	
	
}