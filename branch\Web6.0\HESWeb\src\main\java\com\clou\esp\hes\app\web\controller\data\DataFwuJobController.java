/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataFwuJob{ } 
 * 
 * 摘    要： dataFwuJob
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-12-29 08:37:48
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.CreatePdf;
import com.clou.esp.hes.app.web.core.util.ExcelUtils;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.model.data.DataFwuJob;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.data.DataFwuJobService;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.clou.esp.hes.app.web.validation.ValidGroup3;
import com.power7000g.core.excel.ExcelDataFormatter;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;

/**
 * <AUTHOR>
 * @时间：2017-12-29 08:37:48
 * @描述：dataFwuJob类
 */
@Controller
@RequestMapping("/dataFwuJobController")
public class DataFwuJobController extends BaseController {

	@Resource
	private DataFwuJobService dataFwuJobService;
	@Resource
    private DataUserLogService dataUserLogService;
	@Resource
	private AssetMeterService assetMeterService;

	/**
	 * 打印或导出固件升级任务
	 * 
	 * @param type
	 * @param dfj
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@RequestMapping(value = "exportOrPrintJobReportJobList")
	@ResponseBody
	public void exportOrPrintJobReportJobList(String type, DataFwuJob dfj,String meterType,	HttpServletRequest request, HttpServletResponse response)	throws Exception {
		if (StringUtil.isEmpty(type)) {
			return;
		}
		JqGridSearchTo jqGridSearchTo  = new JqGridSearchTo();
		Map<String,Object> map =jqGridSearchTo.getMap();
		map.put("meterType", meterType);
		map.put("snDfj", dfj.getSn());
		map.put("manufacturerIdDfj", dfj.getManufacturerId());
		map.put("modelNameDfj", dfj.getModelName());
		map.put("currentVesionDfj", dfj.getCurrentVesion());
		map.put("newVersionDfj", dfj.getNewVersion());
		map.put("stateDfj", dfj.getState());
		map.put("deviceTypeDfj", dfj.getDeviceType());
		
		List<DataFwuJob> list = dataFwuJobService.getListByJq(jqGridSearchTo);
		ExcelDataFormatter edf = new ExcelDataFormatter();
		// 1:Runing,2:Done,3:Cancel,4:Waiting,5:Expired
		Map<String, String> trt = new HashMap<String, String>();
		trt.put("1", "Runing");
		trt.put("2", "Done");
		trt.put("3", "Cancel");
		trt.put("4", "Waiting");
		trt.put("5", "Expired");
		edf.set("state", trt);
		
		Map<String, String> deviceTypeMap = new HashMap<String, String>();
		deviceTypeMap.put("1", "Meter");
		deviceTypeMap.put("2", "Communicator");
		deviceTypeMap.put("3", "GPRS Module of Meter");
		deviceTypeMap.put("4", "Communication Module of Communicatior");
		deviceTypeMap.put("5", "(Broadcast) Meter");
		deviceTypeMap.put("6", "(Broadcast) Communication Module of Meter");
		deviceTypeMap.put("7", "(Broadcast) Communicatior");
		deviceTypeMap.put("8", "PLC Module of DCU");
		deviceTypeMap.put("9", "(Broadcast) PLC Module of Communicatior");
		deviceTypeMap.put("10", "(Broadcast) PLC Module of Meter");
		edf.set("deviceType", deviceTypeMap);
		
		// 要日期格式化使用以下方式
		String formatter = ResourceUtil.getSessionattachmenttitle("local.date.time.formatter");
		Map<String, String> tvs = new HashMap<String, String>();
		tvs.put("lastExecTime", formatter);
		edf.set("lastExecTime", tvs);
		
		Map<String, String> tvs1 = new HashMap<String, String>();
		tvs1.put("startTime", formatter);
		edf.set("startTime", tvs1);
		
		Map<String, String> tvs2 = new HashMap<String, String>();
		tvs2.put("expiryTime", formatter);
		edf.set("expiryTime", tvs2);
		
		
		if (type.equals("export")) {// export
			ExcelUtils.writeToFile(list, edf, System.currentTimeMillis()
					+ ".xlsx", response, ValidGroup3.class);
		} else {
			CreatePdf.printPdf(list, edf, ValidGroup3.class, request, response);
		}
	}

	/**
	 * 跳转到dataFwuJob列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "list")
	public ModelAndView list(HttpServletRequest request, Model model) {
		return new ModelAndView("/data/dataFwuJobList");
	}

	/**
	 * 跳转到dataFwuJob新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "dataFwuJob")
	public ModelAndView dataFwuJob(DataFwuJob dataFwuJob,
			HttpServletRequest request, Model model) {
		if (StringUtil.isNotEmpty(dataFwuJob.getId())) {
			try {
				dataFwuJob = dataFwuJobService.getEntity(dataFwuJob.getId());
			} catch (Exception e) {
				e.printStackTrace();
			}
			model.addAttribute("dataFwuJob", dataFwuJob);
		}
		return new ModelAndView("/data/dataFwuJob");
	}

	/**
	 * dataFwuJob查询分页
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrids")
	@ResponseBody
	public JqGridResponseTo datagrids(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
		TagUtil.setFieldValue(jqGridSearchTo, request); // 取传递的参数到JqGridSearchTo
		JqGridResponseTo j = null;
		try {
			j = dataFwuJobService.getForJqGrids(jqGridSearchTo);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return j;
	}

	/**
	 * dataFwuJob查询分页
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
	@ResponseBody
	public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,
			HttpServletRequest request) {
		TagUtil.setFieldValue(jqGridSearchTo, request); // 取传递的参数到JqGridSearchTo
		JqGridResponseTo j = null;
		String isLoadData = request.getParameter("isLoadData");
		if (isLoadData != null && isLoadData.equals("false")) {
			return j;
		}
		try {
			j = dataFwuJobService.getForJqGrid(jqGridSearchTo);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return j;
	}

	/**
	 * 取消dataFwuJob信息
	 * 
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "cancel")
	@ResponseBody
	public AjaxJson cancel(String id, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		SysUser su = TokenManager.getToken();
		try {
			if (StringUtil.isEmpty(id)) {
				j.setErrorMsg("Please enter Job ID!");
				return j;
			}
			DataFwuJob dfj = dataFwuJobService.getEntity(id);
			if (dfj == null) {
				j.setErrorMsg("No job found!");
				return j;
			}
			DataFwuJob dataFwuJob = new DataFwuJob();
			dataFwuJob.setId(id);
			if (dfj.getState() != null && dfj.getState().equals("4")) {
				dataFwuJob.setState("3");
				if (dataFwuJobService.update(dataFwuJob) > 0) {
					j.setMsg("Cancel success!");
					//添加操作日志
                    dataUserLogService.insertDataUserLog(su.getId(), "Firmware Upgrade", 
                    		"Cancel Job", "Cancel Job (Meter SN=" + assetMeterService.getEntity(dfj.getDeviceId()).getSn() + ")");
				} else {
					j.setErrorMsg("Cancel fail!");
				}
			} else {
				j.setErrorMsg("Cancel fail!");
			}
		} catch (Exception e) {
			e.printStackTrace();
			j.setErrorMsg(e.getMessage());
		}
		return j;
	}

	/**
	 * 保存dataFwuJob信息
	 * 
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "save")
	@ResponseBody
	public AjaxJson save(
			@Validated(value = { ValidGroup1.class }) DataFwuJob dataFwuJob,
			BindingResult bindingResult, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		DataFwuJob t = new DataFwuJob();
		try {
			SysUser su = TokenManager.getToken();
			if (StringUtil.isNotEmpty(dataFwuJob.getId())) {
				t = dataFwuJobService.getEntity(dataFwuJob.getId());
				MyBeanUtils.copyBeanNotNull2Bean(dataFwuJob, t);
				dataFwuJobService.update(t);
				j.setMsg("修改成功");

			} else {
				dataFwuJobService.save(dataFwuJob);
				j.setMsg("创建成功");

			}
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg("系统异常");
		}
		return j;
	}

}