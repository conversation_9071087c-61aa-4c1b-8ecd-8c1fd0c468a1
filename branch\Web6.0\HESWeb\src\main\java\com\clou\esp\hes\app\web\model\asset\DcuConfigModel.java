package com.clou.esp.hes.app.web.model.asset;

import com.alibaba.fastjson.annotation.JSONField;
import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Date;
import java.util.List;


/**
 * @ClassName: AssetCalcObj
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年9月5日 上午11:07:35
 *
 */
public class DcuConfigModel extends BaseEntity {

	@JSONField(name="children")
	@JsonProperty("children")
	private List<DcuConfigModel> children;
	@JSONField(name="level")
	@JsonProperty("level")
	private int level;
	@JSONField(name="mac")
	@JsonProperty("mac")
	private String mac;
	@JSONField(name="name")
	@JsonProperty("name")
	private String name;
	@JSONField(name="parentName")
	@JsonProperty("parentName")
	private String parentName;
	@JSONField(name="phaseInfo")
	@JsonProperty("phaseInfo")
	private String phaseInfo;
	@JSONField(name="shortAddress")
	@JsonProperty("shortAddress")
	private String shortAddress;
	@JSONField(name="signalIntensity")
	@JsonProperty("signalIntensity")
	private String signalIntensity;
	@JsonProperty("value")
	@JSONField(name="value")
	private String value;
	@JSONField(name="netStatus")
	@JsonProperty("netStatus")
	private int netStatus;

	public List<DcuConfigModel> getChildren() {
		return children;
	}

	public void setChildren(List<DcuConfigModel> children) {
		this.children = children;
	}

	public int getLevel() {
		return level;
	}

	public void setLevel(int level) {
		this.level = level;
	}

	public String getMac() {
		return mac;
	}

	public void setMac(String mac) {
		this.mac = mac;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getParentName() {
		return parentName;
	}

	public void setParentName(String parentName) {
		this.parentName = parentName;
	}

	public String getPhaseInfo() {
		return phaseInfo;
	}

	public void setPhaseInfo(String phaseInfo) {
		this.phaseInfo = phaseInfo;
	}

	public String getShortAddress() {
		return shortAddress;
	}

	public void setShortAddress(String shortAddress) {
		this.shortAddress = shortAddress;
	}

	public String getSignalIntensity() {
		return signalIntensity;
	}

	public void setSignalIntensity(String signalIntensity) {
		this.signalIntensity = signalIntensity;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public int getNetStatus() {
		return netStatus;
	}

	public void setNetStatus(int netStatus) {
		this.netStatus = netStatus;
	}


}
