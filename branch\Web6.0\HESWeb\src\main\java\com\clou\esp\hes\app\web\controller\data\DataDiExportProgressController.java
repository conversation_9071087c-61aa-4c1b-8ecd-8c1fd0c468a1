/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataDiExportProgress{ } 
 * 
 * 摘    要： dataDiExportProgress
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-04-04 06:12:00
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.data;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;

import com.clou.esp.hes.app.web.model.data.DataDiExportProgress;
import com.clou.esp.hes.app.web.service.data.DataDiExportProgressService;

/**
 * <AUTHOR>
 * @时间：2018-04-04 06:12:00
 * @描述：dataDiExportProgress类
 */
@Controller
@RequestMapping("/dataDiExportProgressController")
public class DataDiExportProgressController extends BaseController{

 	@Resource
    private DataDiExportProgressService dataDiExportProgressService;

	/**
	 * 跳转到dataDiExportProgress列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/data/dataDiExportProgressList");
    }

	/**
	 * 跳转到dataDiExportProgress新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dataDiExportProgress")
	public ModelAndView dataDiExportProgress(DataDiExportProgress dataDiExportProgress,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dataDiExportProgress.getId())){
			try {
                dataDiExportProgress=dataDiExportProgressService.getEntity(dataDiExportProgress.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dataDiExportProgress", dataDiExportProgress);
		}
		return new ModelAndView("/data/dataDiExportProgress");
	}


	/**
	 * dataDiExportProgress查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=dataDiExportProgressService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除dataDiExportProgress信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DataDiExportProgress dataDiExportProgress,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dataDiExportProgressService.deleteById(dataDiExportProgress.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存dataDiExportProgress信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DataDiExportProgress dataDiExportProgress,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DataDiExportProgress t=new  DataDiExportProgress();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dataDiExportProgress.getId())){
        	t=dataDiExportProgressService.getEntity(dataDiExportProgress.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dataDiExportProgress, t);
				dataDiExportProgressService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            dataDiExportProgressService.save(dataDiExportProgress);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
	
	
	
}