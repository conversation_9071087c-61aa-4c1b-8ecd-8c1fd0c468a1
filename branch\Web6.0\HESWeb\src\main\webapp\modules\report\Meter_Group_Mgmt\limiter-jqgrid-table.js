$(document).ready(function() {
	$.jgrid.defaults.styleUI = "Bootstrap";
	$("#table_list2").jqGrid({
		treeGrid: true, //是否树形
		treeGridModel: 'adjacency', //treeGrid模式，跟json元数据有关 ,adjacency/nested  
		ExpandColumn: 'name',
		url: getRootPathWeb()+'/modules/report/Meter_Group_Mgmt/limiter_jqgrid_ztree.json',
		datatype: "json",
		width: "10%",
		autowidth: true,
		rowNum: 10,
		rowList: [10, 20, 30],
		colNames: ["Item","ID", "Value"],
		colModel: [
	
		{
			name: "name",
			index: "name",
			editable: true,
			search: true,
			align: "left",
			width: 80,
			frozen: true,
			stype: "select",
			
		}, 	{
			name: "id",
			index: "id",
			editable: true,
			width: 60,
			frozen: true,
			search: false,
			hidden: true,

		},{
			name: "sex",
			index: "sex",
			align: "left",
			editable: true,
			width: 80,
			editable: true,

		}],
		/*	pager: "#pager",*/
		//					rownumbers:true,
		viewrecords: true,
		/*shrinkToFit:true,  */
		autoScroll: true,
		
		tree_root_level:1,
		treeIcons: {leaf:'ui-icon-document-b'},
		caption: "Season  List",
		jsonReader: {
			repeatitems: false //不需要再去后台刷新，否则可能有问题，所以最好第一次就加载所有数据 
		},
		height: "200px",
		postData: {
			'field': 'id,name,sex'
		}
	});

	
	
$(window).bind("resize", function() {

		var widths = $("#tab_1_2 .col-md-12 .jqGrid_wrapper").width()-15;
		$("#table_list2").setGridWidth(widths);
	});
	$('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {

		var widths = $("#tab_1_2 .col-md-12 .jqGrid_wrapper").width()-15;
		$("#table_list2").setGridWidth(widths);
	})
	/*添加操作按钮*/
	var content = " <div class=' titleBtnItem '><div class='btn_wrapper' title='Add'><div class='ui-title-btn'><span class='glyphicon glyphicon-plus'></span></div></div><div class='btn_wrapper padding-top8' title='Delete'><div class='ui-title-btn'><span class='glyphicon glyphicon-trash'></span></div></div></div>"
	$("#gbox_table .ui-jqgrid-titlebar").append(content);
	$("#gbox_table1 .ui-jqgrid-titlebar").append(content);
	$("#gbox_table2 .ui-jqgrid-titlebar").append(content);
	$("#gbox_table3 .ui-jqgrid-titlebar").append(content);

});