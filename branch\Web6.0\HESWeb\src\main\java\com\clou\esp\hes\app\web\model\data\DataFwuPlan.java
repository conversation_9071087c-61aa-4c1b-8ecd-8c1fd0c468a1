/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataFwuPlan{ } 
 * 
 * 摘    要： dataFwuPlan
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-12-29 08:37:48
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.data;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.excel.Excel;

public class DataFwuPlan extends BaseEntity {

	/**
	 * 意义，目的和功能，以及被用到的地方<br>
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DataFwuPlan() {
	}

	/** introduction */
	@Excel(name = "Planning Description", width = 15, groups = ValidGroup1.class)
	@NotBlank(message = "Please enter Plan Description!")
	@Length(min = 0, max = 200, message = "Plan Description must be of maximum 200 characters!")
	private java.lang.String introduction;
	/** manufacturerId */
	@Excel(name = "Manufacturer", width = 15, groups = ValidGroup1.class) 
	//@NotBlank(message = "Please select Manufacturer!")
	@Length(min = 0, max = 32, message = "Manufacturer must be of maximum 32 characters!")
	private java.lang.String manufacturerId;
	@Excel(name = "Model", width = 15, groups = ValidGroup1.class)
	private String modelName;
	/**
	 * 如果选择多个版本用分号隔开 1.01;1.02
	 */
	@Excel(name = "Current Version", width = 15, groups = ValidGroup1.class)
	@Length(min = 0, max = 64, message = "Current Version must be of maximum 64 characters!")
	private java.lang.String currentVesion;
	/** newVersion */
	@Excel(name = "New Version", width = 15, groups = ValidGroup1.class)
	@NotBlank(message = "Please enter New Version!")
	private java.lang.String newVersion;
	/** startTime */
	@Excel(name = "Start Time", width = 15, groups = ValidGroup1.class)
	@NotNull(message = "Please select Plan Start Time!")
	private java.util.Date startTime;
	/** expiryTime */
	@Excel(name = "Expiry Time", width = 15, groups = ValidGroup1.class)
	@NotNull(message = "Please select Plan Expiry Time!")
	private java.util.Date expiryTime;
	/**
	 * 1: Running -uci 2: Done - uci 3: Cancel - apps 4: Waiting -- apps
	 */
	@Excel(name = "Status", width = 15, groups = ValidGroup1.class)
	@NotNull(message = "Please enter State!")
	@Length(min = 0, max = 1, message = "State must be of maximum 1 characters!")
	private java.lang.String state;
	@Excel(name = "Done", width = 15, groups = ValidGroup1.class)
	private Long done;
	@Excel(name = "Expired", width = 15, groups = ValidGroup1.class)
	private Long expired;
	@Excel(name = "Running", width = 15, groups = ValidGroup1.class)
	private Long running;
	@Excel(name = "Waiting", width = 15, groups = ValidGroup1.class)
	private Long waiting;
	/**
	 * 1: meter 2: manufacturer
	 */
	@NotBlank(message = "Please select Device Type")
	//@Length(min = 0, max = 1, message = "Device Type must be of maximum 1 characters!")
	private java.lang.String deviceType;
	private java.lang.String deviceTypeCopy;
	/** deviceModel */
	// @NotBlank(message = "Please select Model!")
	@Length(min = 0, max = 32, message = "Model must be of maximum 32 characters!")
	private java.lang.String deviceModel;

	private String sn;

	/** filePath */
	@NotBlank(message = "Please select Firmware File!")
	@Length(min = 0, max = 200, message = "Firmware File must be of maximum 200 characters!")
	private java.lang.String filePath;
	/** taskStartTime */
	@NotNull(message = "Please select Task Start Time!")
	private java.util.Date taskStartTime;
	private String taskStartTimeStr;
	/** taskEndTime */
	@NotNull(message = "Please select Task End Time!")
	private java.util.Date taskEndTime;
	private String taskEndTimeStr;
	/** taskCycle */
	@NotNull(message = "Please select Task Cycle!")
	private java.lang.Integer taskCycle;
	/** operationId */
	@NotBlank(message = "Please select OperationId!")
	@Length(min = 0, max = 32, message = "OperationId must be of maximum 32 characters!")
	private java.lang.String operationId;
	@NotBlank(message = "Please enter Image Identifier!")
	@Length(min = 0, max = 128, message = "OperationId must be of maximum 128 characters!")
	private String imageIdentifier;
	
	private String firmwareFileName;
	
	private String jobState;

	private String filePathServer;
	
	private String versionDataitemId;
	
	private String condSnDeviceType;
	
	public String getCondSnDeviceType() {
		return condSnDeviceType;
	}

	public void setCondSnDeviceType(String condSnDeviceType) {
		this.condSnDeviceType = condSnDeviceType;
	}

	public String getVersionDataitemId() {
		return versionDataitemId;
	}

	public void setVersionDataitemId(String versionDataitemId) {
		this.versionDataitemId = versionDataitemId;
	}

	public String getFilePathServer() {
		return filePathServer;
	}

	public void setFilePathServer(String filePathServer) {
		this.filePathServer = filePathServer;
	}

	/**
	 * 1: meter 2: manufacturer
	 * 
	 * @return the value of DATA_FWU_PLAN.DEVICE_TYPE
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.lang.String getDeviceType() {
		return deviceType;
	}

	/**
	 * 1: meter 2: manufacturer
	 * 
	 * @param deviceType
	 *            the value for DATA_FWU_PLAN.DEVICE_TYPE
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setDeviceType(java.lang.String deviceType) {
		this.deviceType = deviceType;
	}

	/**
	 * manufacturerId
	 * 
	 * @return the value of DATA_FWU_PLAN.MANUFACTURER_ID
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.lang.String getManufacturerId() {
		return manufacturerId;
	}

	/**
	 * manufacturerId
	 * 
	 * @param manufacturerId
	 *            the value for DATA_FWU_PLAN.MANUFACTURER_ID
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setManufacturerId(java.lang.String manufacturerId) {
		this.manufacturerId = manufacturerId;
	}

	/**
	 * deviceModel
	 * 
	 * @return the value of DATA_FWU_PLAN.DEVICE_MODEL
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.lang.String getDeviceModel() {
		return deviceModel;
	}

	/**
	 * deviceModel
	 * 
	 * @param deviceModel
	 *            the value for DATA_FWU_PLAN.DEVICE_MODEL
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setDeviceModel(java.lang.String deviceModel) {
		this.deviceModel = deviceModel;
	}

	/**
	 * 如果选择多个版本用分号隔开 1.01;1.02
	 * 
	 * @return the value of DATA_FWU_PLAN.CURRENT_VESION
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.lang.String getCurrentVesion() {
		return currentVesion;
	}

	/**
	 * 如果选择多个版本用分号隔开 1.01;1.02
	 * 
	 * @param currentVesion
	 *            the value for DATA_FWU_PLAN.CURRENT_VESION
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setCurrentVesion(java.lang.String currentVesion) {
		this.currentVesion = currentVesion;
	}

	/**
	 * introduction
	 * 
	 * @return the value of DATA_FWU_PLAN.INTRODUCTION
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.lang.String getIntroduction() {
		return introduction;
	}

	/**
	 * introduction
	 * 
	 * @param introduction
	 *            the value for DATA_FWU_PLAN.INTRODUCTION
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setIntroduction(java.lang.String introduction) {
		this.introduction = introduction;
	}

	/**
	 * startTime
	 * 
	 * @return the value of DATA_FWU_PLAN.START_TIME
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.util.Date getStartTime() {
		return startTime;
	}

	/**
	 * startTime
	 * 
	 * @param startTime
	 *            the value for DATA_FWU_PLAN.START_TIME
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setStartTime(java.util.Date startTime) {
		this.startTime = startTime;
	}

	/**
	 * expiryTime
	 * 
	 * @return the value of DATA_FWU_PLAN.EXPIRY_TIME
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.util.Date getExpiryTime() {
		return expiryTime;
	}

	/**
	 * expiryTime
	 * 
	 * @param expiryTime
	 *            the value for DATA_FWU_PLAN.EXPIRY_TIME
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setExpiryTime(java.util.Date expiryTime) {
		this.expiryTime = expiryTime;
	}

	/**
	 * newVersion
	 * 
	 * @return the value of DATA_FWU_PLAN.NEW_VERSION
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.lang.String getNewVersion() {
		return newVersion;
	}

	/**
	 * newVersion
	 * 
	 * @param newVersion
	 *            the value for DATA_FWU_PLAN.NEW_VERSION
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setNewVersion(java.lang.String newVersion) {
		this.newVersion = newVersion;
	}

	/**
	 * filePath
	 * 
	 * @return the value of DATA_FWU_PLAN.FILE_PATH
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.lang.String getFilePath() {
		return filePath;
	}

	/**
	 * filePath
	 * 
	 * @param filePath
	 *            the value for DATA_FWU_PLAN.FILE_PATH
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setFilePath(java.lang.String filePath) {
		this.filePath = filePath;
	}

	/**
	 * taskStartTime
	 * 
	 * @return the value of DATA_FWU_PLAN.TASK_START_TIME
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.util.Date getTaskStartTime() {
		return taskStartTime;
	}

	/**
	 * taskStartTime
	 * 
	 * @param taskStartTime
	 *            the value for DATA_FWU_PLAN.TASK_START_TIME
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setTaskStartTime(java.util.Date taskStartTime) {
		this.taskStartTime = taskStartTime;
	}

	/**
	 * taskEndTime
	 * 
	 * @return the value of DATA_FWU_PLAN.TASK_END_TIME
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.util.Date getTaskEndTime() {
		return taskEndTime;
	}

	/**
	 * taskEndTime
	 * 
	 * @param taskEndTime
	 *            the value for DATA_FWU_PLAN.TASK_END_TIME
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setTaskEndTime(java.util.Date taskEndTime) {
		this.taskEndTime = taskEndTime;
	}

	/**
	 * taskCycle
	 * 
	 * @return the value of DATA_FWU_PLAN.TASK_CYCLE
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.lang.Integer getTaskCycle() {
		return taskCycle;
	}

	/**
	 * taskCycle
	 * 
	 * @param taskCycle
	 *            the value for DATA_FWU_PLAN.TASK_CYCLE
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setTaskCycle(java.lang.Integer taskCycle) {
		this.taskCycle = taskCycle;
	}

	/**
	 * operationId
	 * 
	 * @return the value of DATA_FWU_PLAN.OPERATION_ID
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.lang.String getOperationId() {
		return operationId;
	}

	/**
	 * operationId
	 * 
	 * @param operationId
	 *            the value for DATA_FWU_PLAN.OPERATION_ID
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setOperationId(java.lang.String operationId) {
		this.operationId = operationId;
	}

	/**
	 * 1: Running -uci 2: Done - uci 3: Cancel - apps 4: Waiting -- apps
	 * 
	 * @return the value of DATA_FWU_PLAN.STATE
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.lang.String getState() {
		return state;
	}

	/**
	 * 1: Running -uci 2: Done - uci 3: Cancel - apps 4: Waiting -- apps
	 * 
	 * @param state
	 *            the value for DATA_FWU_PLAN.STATE
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setState(java.lang.String state) {
		this.state = state;
	}

	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}

	public DataFwuPlan(java.lang.String deviceType,
			java.lang.String manufacturerId, java.lang.String deviceModel,
			java.lang.String currentVesion, java.lang.String introduction,
			java.util.Date startTime, java.util.Date expiryTime,
			java.lang.String newVersion, java.lang.String filePath,
			java.util.Date taskStartTime, java.util.Date taskEndTime,
			java.lang.Integer taskCycle, java.lang.String operationId,
			java.lang.String state) {
		super();
		this.deviceType = deviceType;
		this.manufacturerId = manufacturerId;
		this.deviceModel = deviceModel;
		this.currentVesion = currentVesion;
		this.introduction = introduction;
		this.startTime = startTime;
		this.expiryTime = expiryTime;
		this.newVersion = newVersion;
		this.filePath = filePath;
		this.taskStartTime = taskStartTime;
		this.taskEndTime = taskEndTime;
		this.taskCycle = taskCycle;
		this.operationId = operationId;
		this.state = state;
	}

	public String getModelName() {
		return modelName;
	}

	public void setModelName(String modelName) {
		this.modelName = modelName;
	}

	public Long getDone() {
		return done;
	}

	public void setDone(Long done) {
		this.done = done;
	}

	public Long getExpired() {
		return expired;
	}

	public void setExpired(Long expired) {
		this.expired = expired;
	}

	public Long getRunning() {
		return running;
	}

	public void setRunning(Long running) {
		this.running = running;
	}

	public String getImageIdentifier() {
		return imageIdentifier;
	}

	public void setImageIdentifier(String imageIdentifier) {
		this.imageIdentifier = imageIdentifier;
	}

	public Long getWaiting() {
		return waiting;
	}

	public void setWaiting(Long waiting) {
		this.waiting = waiting;
	}

	public String getTaskStartTimeStr() {
		return taskStartTimeStr;
	}

	public void setTaskStartTimeStr(String taskStartTimeStr) {
		this.taskStartTimeStr = taskStartTimeStr;
	}

	public String getTaskEndTimeStr() {
		return taskEndTimeStr;
	}

	public void setTaskEndTimeStr(String taskEndTimeStr) {
		this.taskEndTimeStr = taskEndTimeStr;
	}

	public String getFirmwareFileName() {
		return firmwareFileName;
	}

	public void setFirmwareFileName(String firmwareFileName) {
		this.firmwareFileName = firmwareFileName;
	}

	public String getJobState() {
		return jobState;
	}

	public void setJobState(String jobState) {
		this.jobState = jobState;
	}

	public java.lang.String getDeviceTypeCopy() {
		return this.deviceType;
	}


}