/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysOrg{ } 
 * 
 * 摘    要： 组织机构表
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-10-27 08:42:10
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.system;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.service.asset.AssetLineManagementService;
import com.clou.esp.hes.app.web.service.system.SysOrgService;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

/**
 * <AUTHOR>
 * @时间：2017-10-27 08:42:10
 * @描述：组织机构表类
 */
@Controller
@RequestMapping("/assetCalcObjectController")
public class AssetCalcObjectController extends BaseController{

 	@Resource
    private SysOrgService sysOrgService;
 	
 	@Resource
 	private AssetLineManagementService assetLineManagementService;
 	

	@RequestMapping(value = "findCalObjForJqGrid")
    @ResponseBody
    public JqGridResponseTo findCalObjForJqGrid(JqGridSearchTo jqGridSearchTo,String orgId,String entityType,HttpServletRequest request) {
		if(StringUtils.isNotEmpty(orgId)) {
			jqGridSearchTo.put("orgId", orgId);
		}
		if(StringUtils.isNotEmpty(entityType)) {
			jqGridSearchTo.put("entityType", entityType);
		}
		JqGridResponseTo response =  assetLineManagementService.findCalObjectsForJqGrid(jqGridSearchTo);
		return response;
	}

	
}