/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictOperation{ } 
 * 
 * 摘    要： 操作权限字典表
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-01 09:42:07
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.system;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DictOperation  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DictOperation() {
	}

	/**操作名称*/
	private java.lang.String operationname;
	/**functionId*/
	private java.lang.String functionId;
	/**operationurl*/
	private java.lang.String operationurl;
	/**ishide*/
	private java.lang.Integer ishide;
	/**DESCRIPTION*/
	private java.lang.String description;

	/**是否被选中*/
	private java.lang.Integer isSelect = 0;
	/**
	 * 操作名称
	 * @return the value of DICT_OPERATION.OPERATIONNAME
	 * @mbggenerated 2017-11-01 09:42:07
	 */
	public java.lang.String getOperationname() {
		return operationname;
	}

	/**
	 * 操作名称
	 * @param operationname the value for DICT_OPERATION.OPERATIONNAME
	 * @mbggenerated 2017-11-01 09:42:07
	 */
    	public void setOperationname(java.lang.String operationname) {
		this.operationname = operationname;
	}
	/**
	 * functionId
	 * @return the value of DICT_OPERATION.FUNCTION_ID
	 * @mbggenerated 2017-11-01 09:42:07
	 */
	public java.lang.String getFunctionId() {
		return functionId;
	}

	/**
	 * functionId
	 * @param functionId the value for DICT_OPERATION.FUNCTION_ID
	 * @mbggenerated 2017-11-01 09:42:07
	 */
    	public void setFunctionId(java.lang.String functionId) {
		this.functionId = functionId;
	}
	/**
	 * operationurl
	 * @return the value of DICT_OPERATION.OPERATIONURL
	 * @mbggenerated 2017-11-01 09:42:07
	 */
	public java.lang.String getOperationurl() {
		return operationurl;
	}

	/**
	 * operationurl
	 * @param operationurl the value for DICT_OPERATION.OPERATIONURL
	 * @mbggenerated 2017-11-01 09:42:07
	 */
    	public void setOperationurl(java.lang.String operationurl) {
		this.operationurl = operationurl;
	}
	/**
	 * ishide
	 * @return the value of DICT_OPERATION.ISHIDE
	 * @mbggenerated 2017-11-01 09:42:07
	 */
	public java.lang.Integer getIshide() {
		return ishide;
	}

	/**
	 * ishide
	 * @param ishide the value for DICT_OPERATION.ISHIDE
	 * @mbggenerated 2017-11-01 09:42:07
	 */
    	public void setIshide(java.lang.Integer ishide) {
		this.ishide = ishide;
	}

	public DictOperation(java.lang.String operationname 
	,java.lang.String functionId 
	,java.lang.String operationurl 
	,java.lang.Integer ishide ) {
		super();
		this.operationname = operationname;
		this.functionId = functionId;
		this.operationurl = operationurl;
		this.ishide = ishide;
	}

	public java.lang.String getDescription() {
		return description;
	}

	public void setDescription(java.lang.String description) {
		this.description = description;
	}

	public java.lang.Integer getIsSelect() {
		return isSelect;
	}

	public void setIsSelect(java.lang.Integer isSelect) {
		this.isSelect = isSelect;
	}

}