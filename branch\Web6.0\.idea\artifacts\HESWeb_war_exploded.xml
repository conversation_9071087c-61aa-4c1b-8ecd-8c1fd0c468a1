<component name="ArtifactManager">
  <artifact type="exploded-war" name="HESWeb:war exploded">
    <output-path>$PROJECT_DIR$/HESWeb/target/HESWeb</output-path>
    <properties id="maven-jee-properties">
      <options>
        <exploded>true</exploded>
        <module>HESWeb</module>
        <packaging>war</packaging>
        <unpackNestedArchives>false</unpackNestedArchives>
      </options>
    </properties>
    <root id="root">
      <element id="directory" name="WEB-INF">
        <element id="directory" name="classes">
          <element id="module-output" name="HESWeb6" />
        </element>
        <element id="directory" name="lib">
          <element id="library" level="project" name="Maven: org.flywaydb:flyway-core:4.2.0" />
          <element id="library" level="project" name="Maven: nl.justobjects.pushlet:pushlet:2.0.4" />
          <element id="library" level="project" name="Maven: org.xhtmlrenderer:core-renderer:R8pre2" />
          <element id="library" level="project" name="Maven: com.lowagie:itext:2.1.0" />
          <element id="library" level="project" name="Maven: bouncycastle:bcmail-jdk14:136" />
          <element id="library" level="project" name="Maven: bouncycastle:bcprov-jdk14:136" />
          <element id="library" level="project" name="Maven: com.itextpdf:itextpdf:5.4.3" />
          <element id="library" level="project" name="Maven: org.springframework:spring-core:4.0.9.RELEASE" />
          <element id="library" level="project" name="Maven: commons-logging:commons-logging:1.1.3" />
          <element id="library" level="project" name="Maven: org.springframework:spring-web:4.0.9.RELEASE" />
          <element id="library" level="project" name="Maven: org.springframework:spring-beans:4.0.9.RELEASE" />
          <element id="library" level="project" name="Maven: org.springframework:spring-context:4.0.9.RELEASE" />
          <element id="library" level="project" name="Maven: org.springframework:spring-tx:4.0.9.RELEASE" />
          <element id="library" level="project" name="Maven: org.springframework:spring-jdbc:4.0.9.RELEASE" />
          <element id="library" level="project" name="Maven: org.springframework:spring-webmvc:4.0.9.RELEASE" />
          <element id="library" level="project" name="Maven: org.springframework:spring-expression:4.0.9.RELEASE" />
          <element id="library" level="project" name="Maven: org.springframework:spring-aop:4.0.9.RELEASE" />
          <element id="library" level="project" name="Maven: aopalliance:aopalliance:1.0" />
          <element id="library" level="project" name="Maven: org.springframework:spring-context-support:4.0.9.RELEASE" />
          <element id="library" level="project" name="Maven: org.springframework:spring-test:4.0.9.RELEASE" />
          <element id="library" level="project" name="Maven: clouesp.hes.common.logger:logger:0.0.1-SNAPSHOT" />
          <element id="library" level="project" name="Maven: org.apache.logging.log4j:log4j-api:2.17.0" />
          <element id="library" level="project" name="Maven: org.apache.logging.log4j:log4j-core:2.17.0" />
          <element id="library" level="project" name="Maven: org.apache.lucene:lucene-queryparser:5.0.0" />
          <element id="library" level="project" name="Maven: org.apache.lucene:lucene-sandbox:5.0.0" />
          <element id="library" level="project" name="Maven: org.apache.lucene:lucene-queries:5.0.0" />
          <element id="library" level="project" name="Maven: org.apache.lucene:lucene-core:5.0.0" />
          <element id="library" level="project" name="Maven: org.apache.lucene:lucene-highlighter:5.0.0" />
          <element id="library" level="project" name="Maven: org.apache.lucene:lucene-join:5.0.0" />
          <element id="library" level="project" name="Maven: org.apache.lucene:lucene-memory:5.0.0" />
          <element id="library" level="project" name="Maven: org.apache.lucene:lucene-analyzers-common:5.0.0" />
          <element id="library" level="project" name="Maven: clouesp.hes.common.logger:loggerquery:0.0.1-SNAPSHOT" />
          <element id="library" level="project" name="Maven: com.power7000g:power7000g-core:1.0.1" />
          <element id="library" level="project" name="Maven: com.google.guava:guava:17.0" />
          <element id="library" level="project" name="Maven: javax.servlet:servlet-api:2.5" />
          <element id="file-copy" path="$MAVEN_REPOSITORY$/javax/servlet/jstl/1.2/jstl-1.2.jar" output-file-name="javax.servlet-jstl-1.2.jar" />
          <element id="library" level="project" name="Maven: taglibs:standard:1.1.2" />
          <element id="library" level="project" name="Maven: org.hibernate:hibernate-validator:5.4.0.Final" />
          <element id="library" level="project" name="Maven: javax.validation:validation-api:1.1.0.Final" />
          <element id="library" level="project" name="Maven: org.jboss.logging:jboss-logging:3.3.0.Final" />
          <element id="library" level="project" name="Maven: com.fasterxml:classmate:1.3.1" />
          <element id="library" level="project" name="Maven: org.hibernate:hibernate-validator-annotation-processor:5.4.0.Final" />
          <element id="library" level="project" name="Maven: org.springframework:spring-orm:4.0.9.RELEASE" />
          <element id="library" level="project" name="Maven: com.github.pagehelper:pagehelper:4.0.0" />
          <element id="library" level="project" name="Maven: com.github.jsqlparser:jsqlparser:0.9.1" />
          <element id="library" level="project" name="Maven: net.sf.json-lib:json-lib:jdk15:2.4" />
          <element id="library" level="project" name="Maven: commons-collections:commons-collections:3.2.1" />
          <element id="library" level="project" name="Maven: net.sf.ezmorph:ezmorph:1.0.6" />
          <element id="library" level="project" name="Maven: com.googlecode.json-simple:json-simple:1.1" />
          <element id="library" level="project" name="Maven: commons-logging:commons-logging-api:1.1" />
          <element id="library" level="project" name="Maven: commons-net:commons-net:3.3" />
          <element id="library" level="project" name="Maven: log4j:log4j:1.2.16" />
          <element id="library" level="project" name="Maven: commons-lang:commons-lang:2.6" />
          <element id="library" level="project" name="Maven: org.apache.poi:poi:3.16" />
          <element id="library" level="project" name="Maven: org.apache.commons:commons-collections4:4.1" />
          <element id="library" level="project" name="Maven: org.apache.poi:poi-ooxml:3.16" />
          <element id="library" level="project" name="Maven: com.github.virtuald:curvesapi:1.04" />
          <element id="library" level="project" name="Maven: org.apache.poi:poi-ooxml-schemas:3.16" />
          <element id="library" level="project" name="Maven: org.apache.xmlbeans:xmlbeans:2.6.0" />
          <element id="library" level="project" name="Maven: stax:stax-api:1.0.1" />
          <element id="library" level="project" name="Maven: org.apache.poi:poi-scratchpad:3.16" />
          <element id="library" level="project" name="Maven: org.xdemo.app:SuperUtility:1.1.0" />
          <element id="library" level="project" name="Maven: com.google.code.gson:gson:2.2.4" />
          <element id="library" level="project" name="Maven: org.mybatis:mybatis:3.2.6" />
          <element id="library" level="project" name="Maven: org.mybatis:mybatis-spring:1.2.2" />
          <element id="library" level="project" name="Maven: javax:javaee-api:7.0" />
          <element id="library" level="project" name="Maven: com.sun.mail:javax.mail:1.5.0" />
          <element id="library" level="project" name="Maven: javax.activation:activation:1.1" />
          <element id="library" level="project" name="Maven: mysql:mysql-connector-java:8.0.26" />
          <element id="library" level="project" name="Maven: com.google.protobuf:protobuf-java:3.11.4" />
          <element id="library" level="project" name="Maven: com.oracle:ojdbc6:11.2.0.3" />
          <element id="library" level="project" name="Maven: commons-dbcp:commons-dbcp:1.4" />
          <element id="file-copy" path="$MAVEN_REPOSITORY$/jstl/jstl/1.2/jstl-1.2.jar" output-file-name="jstl-jstl-1.2.jar" />
          <element id="library" level="project" name="Maven: redis.clients:jedis:2.8.0" />
          <element id="library" level="project" name="Maven: org.apache.commons:commons-pool2:2.3" />
          <element id="library" level="project" name="Maven: com.fasterxml.jackson.core:jackson-core:2.4.0" />
          <element id="library" level="project" name="Maven: com.fasterxml.jackson.core:jackson-annotations:2.4.0" />
          <element id="library" level="project" name="Maven: com.fasterxml.jackson.core:jackson-databind:2.4.0" />
          <element id="library" level="project" name="Maven: org.apache.shiro:shiro-core:1.3.2" />
          <element id="library" level="project" name="Maven: commons-beanutils:commons-beanutils:1.8.3" />
          <element id="library" level="project" name="Maven: org.apache.shiro:shiro-web:1.3.2" />
          <element id="library" level="project" name="Maven: org.apache.shiro:shiro-spring:1.3.2" />
          <element id="library" level="project" name="Maven: org.apache.shiro:shiro-all:1.3.2" />
          <element id="library" level="project" name="Maven: org.apache.shiro:shiro-guice:1.3.2" />
          <element id="library" level="project" name="Maven: com.google.inject:guice:3.0" />
          <element id="library" level="project" name="Maven: javax.inject:javax.inject:1" />
          <element id="library" level="project" name="Maven: com.google.inject.extensions:guice-multibindings:3.0" />
          <element id="library" level="project" name="Maven: org.apache.shiro:shiro-quartz:1.3.2" />
          <element id="library" level="project" name="Maven: org.opensymphony.quartz:quartz:1.6.1" />
          <element id="library" level="project" name="Maven: org.apache.shiro:shiro-ehcache:1.3.2" />
          <element id="library" level="project" name="Maven: net.sf.ehcache:ehcache-core:2.5.3" />
          <element id="library" level="project" name="Maven: org.slf4j:slf4j-log4j12:1.7.7" />
          <element id="library" level="project" name="Maven: asm:asm:3.3.1" />
          <element id="library" level="project" name="Maven: org.codehaus.jackson:jackson-core-asl:1.9.0" />
          <element id="library" level="project" name="Maven: org.apache.cxf:cxf-rt-frontend-jaxws:3.1.6" />
          <element id="library" level="project" name="Maven: xml-resolver:xml-resolver:1.2" />
          <element id="library" level="project" name="Maven: org.ow2.asm:asm:5.0.4" />
          <element id="library" level="project" name="Maven: org.apache.cxf:cxf-core:3.1.6" />
          <element id="library" level="project" name="Maven: org.apache.ws.xmlschema:xmlschema-core:2.2.1" />
          <element id="library" level="project" name="Maven: org.apache.cxf:cxf-rt-bindings-soap:3.1.6" />
          <element id="library" level="project" name="Maven: org.apache.cxf:cxf-rt-wsdl:3.1.6" />
          <element id="library" level="project" name="Maven: wsdl4j:wsdl4j:1.6.3" />
          <element id="library" level="project" name="Maven: org.apache.cxf:cxf-rt-databinding-jaxb:3.1.6" />
          <element id="library" level="project" name="Maven: com.sun.xml.bind:jaxb-impl:2.2.11" />
          <element id="library" level="project" name="Maven: com.sun.xml.bind:jaxb-core:2.2.11" />
          <element id="library" level="project" name="Maven: org.apache.cxf:cxf-rt-bindings-xml:3.1.6" />
          <element id="library" level="project" name="Maven: org.apache.cxf:cxf-rt-frontend-simple:3.1.6" />
          <element id="library" level="project" name="Maven: org.apache.cxf:cxf-rt-ws-addr:3.1.6" />
          <element id="library" level="project" name="Maven: org.apache.cxf:cxf-rt-ws-policy:3.1.6" />
          <element id="library" level="project" name="Maven: org.apache.neethi:neethi:3.0.3" />
          <element id="library" level="project" name="Maven: org.apache.cxf:cxf-rt-transports-http:3.1.6" />
          <element id="library" level="project" name="Maven: org.codehaus.woodstox:stax2-api:3.1.0" />
          <element id="library" level="project" name="Maven: javax.xml.stream:stax-api:1.0-2" />
          <element id="library" level="project" name="Maven: org.codehaus.woodstox:woodstox-core-asl:4.2.1" />
          <element id="library" level="project" name="Maven: com.alibaba:fastjson:1.1.41" />
          <element id="library" level="project" name="Maven: org.slf4j:slf4j-api:1.7.7" />
          <element id="library" level="project" name="Maven: org.codehaus.jackson:jackson-mapper-asl:1.9.13" />
          <element id="library" level="project" name="Maven: commons-fileupload:commons-fileupload:1.3.1" />
          <element id="library" level="project" name="Maven: commons-io:commons-io:2.4" />
          <element id="library" level="project" name="Maven: commons-codec:commons-codec:1.9" />
          <element id="library" level="project" name="Maven: commons-pool:commons-pool:1.6" />
          <element id="library" level="project" name="Maven: com.alibaba:druid:1.0.13" />
          <element id="library" level="project" name="Maven: com.bstek.ureport:ureport2-console:2.2.4" />
          <element id="library" level="project" name="Maven: org.apache.velocity:velocity:1.7" />
          <element id="library" level="project" name="Maven: com.bstek.ureport:ureport2-core:2.2.4" />
          <element id="library" level="project" name="Maven: org.apache.commons:commons-lang3:3.5" />
          <element id="library" level="project" name="Maven: commons-beanutils:commons-beanutils-core:1.8.3" />
          <element id="library" level="project" name="Maven: org.antlr:antlr4-runtime:4.5.3" />
          <element id="library" level="project" name="Maven: dom4j:dom4j:1.6.1" />
          <element id="library" level="project" name="Maven: xml-apis:xml-apis:1.0.b2" />
          <element id="library" level="project" name="Maven: org.apache.poi:ooxml-schemas:1.3" />
          <element id="library" level="project" name="Maven: com.google.zxing:core:3.3.1" />
          <element id="library" level="project" name="Maven: com.bstek.ureport:ureport2-font:2.0.1" />
        </element>
      </element>
      <element id="directory" name="META-INF">
        <element id="file-copy" path="$PROJECT_DIR$/HESWeb/target/HESWeb/META-INF/MANIFEST.MF" />
      </element>
      <element id="javaee-facet-resources" facet="HESWeb/web/Web" />
    </root>
  </artifact>
</component>