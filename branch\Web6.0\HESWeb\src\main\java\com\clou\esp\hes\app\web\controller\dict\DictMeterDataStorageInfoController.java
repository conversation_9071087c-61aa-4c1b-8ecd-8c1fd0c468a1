/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictMeterDataStorageInfo{ } 
 * 
 * 摘    要： dictMeterDataStorageInfo
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.dict;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.transaction.Transactional;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.dict.DictMeterDataStorageInfo;
import com.clou.esp.hes.app.web.model.dict.DictMeterDataStorageTable;
import com.clou.esp.hes.app.web.model.dict.DictProfile;
import com.clou.esp.hes.app.web.service.dict.DictMeterDataStorageInfoService;
import com.clou.esp.hes.app.web.service.dict.DictMeterDataStorageTableService;
import com.clou.esp.hes.app.web.service.dict.DictProfileService;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;

/**
 * <AUTHOR>
 * @时间：2017-11-22 03:30:03
 * @描述：dictMeterDataStorageInfo类
 */
@Controller
@RequestMapping("/dictMeterDataStorageInfoController")
public class DictMeterDataStorageInfoController extends BaseController{

 	@Resource
    private DictMeterDataStorageInfoService dictMeterDataStorageInfoService;
 	@Resource
 	private DictMeterDataStorageTableService dictMeterDataStorageTableService;
 	@Resource
 	private DictProfileService               dictProfileService;

	/**
	 * 跳转到dictMeterDataStorageInfo列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/dict/dictMeterDataStorageInfoList");
    }

	/**
	 * 跳转到dictMeterDataStorageInfo新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dictMeterDataStorageInfo")
	public ModelAndView dictMeterDataStorageInfo(String tableId,String profileId,HttpServletRequest request, Model model) {
			model.addAttribute("tableId", tableId);
			model.addAttribute("profileId", profileId);
			model.addAttribute("channelTypeReplace", DictDataitemController.channelTypeReplace);
		return new ModelAndView("/dict/dictMeterDataStorageInfo");
	}


	/**
	 * dictMeterDataStorageInfo查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request
    		,String tableId,String dataitemId,String dataitemName,String protocolCode,String protocolId,String dataitemType,Boolean select) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        if(select==null||!select) {
        	return j;
        }
        try {
        	jqGridSearchTo.put("tableId", tableId);
        	jqGridSearchTo.put("dataitemId", dataitemId);
        	jqGridSearchTo.put("dataitemName", dataitemName);
        	jqGridSearchTo.put("protocolCode", protocolCode);
        	jqGridSearchTo.put("protocolId", protocolId);
        	jqGridSearchTo.put("dataitemType", dataitemType);
        	j=dictMeterDataStorageInfoService.getForJqGrid(jqGridSearchTo);
        }catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除dictMeterDataStorageInfo信息
     * 
     * @param id
     * @return
     */
	@Transactional
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(String id,String tableId,Long fieldIndex,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
        	if(StringUtils.isNotEmpty(id)&&StringUtils.isNotEmpty(tableId)){
        		//查询tableId对应的字段
				Long maxIndex = this.dictMeterDataStorageInfoService.getMaxColumnIndex(tableId);
				if(fieldIndex.equals(maxIndex)) {
					this.dictMeterDataStorageInfoService.deleteById(id);
					//更新table表 ColumnCount
					DictMeterDataStorageTable table = this.dictMeterDataStorageTableService.getEntity(tableId);
					if(table.getColumnCount()>0) {
						table.setColumnCount(table.getColumnCount()-1);
						table.setOldId(table.getId());
						this.dictMeterDataStorageTableService.update(table);
						this.dictMeterDataStorageTableService.deleteTableColumn(table.getName(), "VALUE"+maxIndex);
					}
				}
        	}
        	j.setSuccess(true);
	        j.setMsg(MutiLangUtil.doMutiLang("system.delSuccess"));
	    } catch (Exception e) {
	    	e.printStackTrace();
	    	j.setSuccess(false);
	    	j.setMsg(MutiLangUtil.doMutiLang("system.delFail"));
	    }
	    return j;
    }
    
    @Transactional
	@RequestMapping(value = "save")
	@ResponseBody
	public AjaxJson save( HttpServletRequest request,String tableId,String items) {
		AjaxJson j = new AjaxJson();
		try {
			JSONArray curArray = null;
			if(StringUtils.isNotEmpty(items)) {
				curArray= JSON.parseArray(items);
				DictMeterDataStorageInfo item = new DictMeterDataStorageInfo();
				//查询tableId对应的字段
				Long maxIndex = this.dictMeterDataStorageInfoService.getMaxColumnIndex(tableId);
				if(maxIndex==null) {
					maxIndex=0l;
				}
				if(curArray!=null) {
					//更新table表字段
					DictMeterDataStorageInfo  info = new DictMeterDataStorageInfo();
					info.setTableId(tableId);
					long count = this.dictMeterDataStorageInfoService.getCount(info);
					
					DictMeterDataStorageTable table = new DictMeterDataStorageTable();
					table.setOldId(tableId);
					table.setColumnCount(Long.valueOf(count).intValue()+curArray.size());
					this.dictMeterDataStorageTableService.update(table);
					table = this.dictMeterDataStorageTableService.getEntity(tableId);
					//更新info表
					for(int i=0;i<curArray.size();i++) {
						maxIndex ++;
						JSONObject jsonRestTemp = curArray.getJSONObject(i);
						String dataItemId = jsonRestTemp.getString("id");
						item.setId(dataItemId);
						item.setTableId(tableId);
						item.setFieldIndex(Long.valueOf(maxIndex).intValue());
						this.dictMeterDataStorageInfoService.save(item);//添加表对应的字段
						this.dictMeterDataStorageTableService.addTableColumn(table.getName(), "VALUE"+maxIndex);
					}

					
					j.setMsg(MutiLangUtil.doMutiLang("system.addSucc"));
				}
			}
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			j.setSuccess(false);
			j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
		}
		
		return j;
	}
	
    @RequestMapping(value = "unBindDatagrid")
	@ResponseBody
	public JqGridResponseTo unBindDatagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request
			,String profileId,String tableId,
			String id,String name,String protocolCode,String dataitemType,Boolean select) {
		TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
		JqGridResponseTo j=null;
		if(select==null||!select) {
			return j;
		}
		try {
			//根据曲线Id查出规约Id
			DictProfile profile= this.dictProfileService.getEntity(profileId);
			
			jqGridSearchTo.put("protocolId", profile.getProtocolId());
			
			jqGridSearchTo.put("profileId", profileId);
			jqGridSearchTo.put("tableId", tableId);
			jqGridSearchTo.put("dataitemId", id);
			jqGridSearchTo.put("dataitemName", name);
			jqGridSearchTo.put("protocolCode", protocolCode);
			jqGridSearchTo.put("dataitemType", dataitemType);
			j = this.dictMeterDataStorageInfoService.unBindForJqGrid(jqGridSearchTo);
		}catch (Exception e) {
			e.printStackTrace();
		}
		return j;
	}
			
	
}