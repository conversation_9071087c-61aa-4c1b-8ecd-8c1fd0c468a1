<?xml version="1.0" encoding="UTF-8"?><ureport><cell expand="None" name="A1" row="1" col="1"><cell-style font-size="10" forecolor="0,0,0" font-family="宋体" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Row]]></simple-value></cell><cell expand="None" name="B1" row="1" col="2"><cell-style font-size="10" forecolor="7,7,7" font-family="宋体" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Meter SN]]></simple-value></cell><cell expand="None" name="C1" row="1" col="3"><cell-style font-size="10" forecolor="7,7,7" font-family="宋体" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Event Time]]></simple-value></cell><cell expand="None" name="D1" row="1" col="4"><cell-style font-size="10" forecolor="7,7,7" font-family="宋体" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Status]]></simple-value></cell><cell expand="Down" name="A2" row="2" col="1"><cell-style font-size="10" forecolor="0,0,0" font-family="宋体" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="DataMeterSwitchStatus" aggregate="group" property="ROWNUM" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="B2" row="2" col="2"><cell-style font-size="10" forecolor="7,7,7" font-family="宋体" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="DataMeterSwitchStatus" aggregate="group" property="SN" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="C2" row="2" col="3"><cell-style font-size="10" forecolor="7,7,7" font-family="宋体" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="DataMeterSwitchStatus" aggregate="group" property="UPDATE_TV" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="D2" row="2" col="4"><cell-style font-size="10" forecolor="7,7,7" font-family="宋体" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="DataMeterSwitchStatus" aggregate="group" property="STATUS" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="A3" row="3" col="1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Connect Number:]]></simple-value></cell><cell expand="Down" name="B3" row="3" col="2"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="Conn_COUNT" aggregate="group" property="CONN" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="C3" row="3" col="3"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Disconnect Number:]]></simple-value></cell><cell expand="Down" name="D3" row="3" col="4"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="DISCONN_ACCOUNT" aggregate="group" property="DISCONN" order="none" mapping-type="simple"></dataset-value></cell><row row-number="1" height="18"/><row row-number="2" height="18"/><row row-number="3" height="19"/><column col-number="1" width="74"/><column col-number="2" width="103"/><column col-number="3" width="123"/><column col-number="4" width="123"/><datasource name="HesDataSource" type="jdbc" username="hes" password="hes" url="*****************************************" driver="oracle.jdbc.OracleDriver"><dataset name="DataMeterSwitchStatus" type="sql"><sql><![CDATA[select t1.*,rownum,t1.sn,t.meter_id,t.update_tv,decode(t.switch_status,1,'Connected','DisConnected') as status from data_meter_switch_status t
inner join asset_meter t1 on t1.id = t.meter_id
inner join sys_org t2 on t1.org_id = t2.id
inner join asset_communicator t3 on t1.communicator_id = t3.id and t3.sn=:sn
where 1=1
]]></sql><field name="ID"/><field name="SN"/><field name="NAME"/><field name="UTILITY_ID"/><field name="ORG_ID"/><field name="COMMUNICATOR_ID"/><field name="MODEL"/><field name="MANUFACTURER"/><field name="PASSWORD"/><field name="HLS_AK"/><field name="HLS_EK"/><field name="INDEX_DCU"/><field name="MAC"/><field name="COM_TYPE"/><field name="IS_ENCRYPT"/><field name="AUTH_TYPE"/><field name="FW_VERSION"/><field name="REMOVE_FLAG"/><field name="ADDR"/><field name="LONGITUDE"/><field name="LATITUDE"/><field name="COM_PORT"/><field name="CT"/><field name="PT"/><field name="IS_VIP"/><field name="KEY_FLAG"/><field name="SRC_ADDR"/><field name="ROWNUM"/><field name="SN"/><field name="METER_ID"/><field name="UPDATE_TV"/><field name="STATUS"/><parameter name="sn" type="String" default-value="004422bd"/><parameter name="orgIds" type="List" default-value="ХОВД,Darkhan,СҮХБААТАР,mining industrial,ХӨТӨЛ,ЖАРГАЛАНТ,ЗҮҮНХАРАА,showroom,МТАА,ШАРЫН ГОЛ,ББЗХ"/><parameter name="time" type="String" default-value="06-14-2019"/></dataset><dataset name="Conn_COUNT" type="sql"><sql><![CDATA[select count(t.switch_status) as conn from data_meter_switch_status t
inner join asset_meter t1 on t1.id = t.meter_id
inner join sys_org t2 on t1.org_id = t2.id
inner join asset_communicator t3 on t1.communicator_id = t3.id and t3.sn=:sn
where t.switch_status='1'
]]></sql><field name="CONN"/><parameter name="sn" type="String" default-value="********"/></dataset><dataset name="DISCONN_ACCOUNT" type="sql"><sql><![CDATA[select count(t.switch_status) as disconn from data_meter_switch_status t
inner join asset_meter t1 on t1.id = t.meter_id
inner join sys_org t2 on t1.org_id = t2.id
inner join asset_communicator t3 on t1.communicator_id = t3.id and t3.sn=:sn
where t.switch_status='2']]></sql><field name="DISCONN"/><parameter name="sn" type="String" default-value="********"/></dataset></datasource><paper type="A4" left-margin="90" right-margin="90"
    top-margin="72" bottom-margin="72" paging-mode="fitpage" fixrows="0"
    width="595" height="842" orientation="portrait" html-report-align="left" bg-image="" html-interval-refresh-value="0" column-enabled="false"></paper></ureport>