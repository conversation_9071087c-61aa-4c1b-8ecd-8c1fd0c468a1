/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictServiceAttribute{ } 
 * 
 * 摘    要： dictServiceAttribute
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-03-29 03:19:41
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.dict;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.model.data.DataStatisticsDevice;
import com.clou.esp.hes.app.web.model.dict.DictServiceAttribute;
import com.clou.esp.hes.app.web.service.dict.DictServiceAttributeService;

/**
 * <AUTHOR>
 * @时间：2018-03-29 03:19:41
 * @描述：dictServiceAttribute类
 */
@Controller
@RequestMapping("/dictServiceAttributeController")
public class DictServiceAttributeController extends BaseController{

 	@Resource
    private DictServiceAttributeService dictServiceAttributeService;

	/**
	 * 跳转到dictServiceAttribute列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/dict/dictServiceAttributeList");
    }

	/**
	 * 跳转到dictServiceAttribute新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dictServiceAttribute")
	public ModelAndView dictServiceAttribute(DictServiceAttribute dictServiceAttribute,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dictServiceAttribute.getId())){
			try {
                dictServiceAttribute=dictServiceAttributeService.getEntity(dictServiceAttribute.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dictServiceAttribute", dictServiceAttribute);
		}
		return new ModelAndView("/dict/dictServiceAttribute");
	}


	/**
	 * dictServiceAttribute查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=dictServiceAttributeService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除dictServiceAttribute信息
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DictServiceAttribute dictServiceAttribute,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dictServiceAttributeService.deleteById(dictServiceAttribute.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存dictServiceAttribute信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DictServiceAttribute dictServiceAttribute,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DictServiceAttribute t=new  DictServiceAttribute();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dictServiceAttribute.getId())){
        	t=dictServiceAttributeService.getEntity(dictServiceAttribute.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dictServiceAttribute, t);
				dictServiceAttributeService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            dictServiceAttributeService.save(dictServiceAttribute);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
	
    /**
     * 根据实体内的数据获取list
     * @param id
     * @return
     */
    @RequestMapping(value = "getListByEntity")
    @ResponseBody
    public AjaxJson getListByEntity(DictServiceAttribute dictServiceAttribute, HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        try {
//            SysUser su = TokenManager.getToken();
        	if(StringUtil.isNotEmpty(dictServiceAttribute.getServiceId())){
        		List<DictServiceAttribute> list1 = dictServiceAttributeService.getListByServiceId(dictServiceAttribute.getServiceId());
        		if(list1.size() > 0){
        			List<DictServiceAttribute> listOld = new ArrayList();
        			if(dictServiceAttribute.getServiceType() != 0){
        				
        				List<DictServiceAttribute> list2 = dictServiceAttributeService.getList(dictServiceAttribute);
        				if(list2.size() > 0){
        					//service无数据的情况下，使用默认的初始数据
        					for (DictServiceAttribute dictServiceAttribute2 : list2) {
        						boolean isExist = false;
        						for(DictServiceAttribute dictServiceAttribute1 : list1){
        							if(dictServiceAttribute1.getAttributeName().equals(dictServiceAttribute2.getAttributeName())){
        								isExist = true;
        							}
        						}
        						
        						if(!isExist){
        							dictServiceAttribute2.setAttributeValue(dictServiceAttribute2.getAttributeDefault());	
        							listOld.add(dictServiceAttribute2);
        						}
        						
        					}
        				}
        			}
        			if(listOld.size() > 0){
        				list1.addAll(listOld);
        			}
        			
        			j.setObj(list1);
        		}else{
        			if(dictServiceAttribute.getServiceType() != 0){
        				List<DictServiceAttribute> list2 = dictServiceAttributeService.getList(dictServiceAttribute);
        				if(list2.size() > 0){
        					//service无数据的情况下，使用默认的初始数据
        					for (int i = 0; i < list2.size(); i++) {
        						list2.get(i).setAttributeValue(list2.get(i).getAttributeDefault());
        					}
        					j.setObj(list2);
        				}
        			}
        		}
        	}
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        if(j.getObj() != null){
            List<DictServiceAttribute> list3 =  (List<DictServiceAttribute>) j.getObj();
            if(list3 != null && list3.size() > 0){
            	// 按点击数倒序
                Collections.sort(list3, new Comparator<DictServiceAttribute>() {
                    public int compare(DictServiceAttribute arg0, DictServiceAttribute arg1) {
                    	BigDecimal hits0 = new BigDecimal(arg0.getSortId());
                    	BigDecimal hits1 = new BigDecimal(arg1.getSortId());
                        if (hits0.compareTo(hits1) == -1) {
                            return -1;
                        } else if (hits0.compareTo(hits1) == 0) {
                            return 0;
                        } else {
                            return 1;
                        }
                    }
                });
            	
            }
        }
    
        
        return j;
    }
    /**
     * 根据实体内的数据获取list
     * @param id
     * @return
     */
    @RequestMapping(value = "checkAttributeValue")
    @ResponseBody
    public AjaxJson checkAttributeValue(DictServiceAttribute entity){
    	AjaxJson j = new AjaxJson();
        try {
        	String[] strArr = entity.getId().split("-");
        	entity.setServiceType(Integer.parseInt(strArr[0]));
        	entity.setAttributeName(strArr[1]);
        	List<DictServiceAttribute> list = dictServiceAttributeService.getList(entity);
        	if(list.size() > 0){
        		DictServiceAttribute newEntity = list.get(0);
        		if("Integer".equals(newEntity.getAttributeType())){
        			int min = Integer.parseInt(newEntity.getAttributeMin());
        			int max = Integer.parseInt(newEntity.getAttributeMax());
        			int number = Integer.parseInt(entity.getAttributeValue());
        			if(number < min || number > max){
        				j.setMsg("Please enter a number between " + min + " and " + max + "!");
        				j.setSuccess(false);
        			}
        		}
        	}
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
    
    /**
	 * 编辑channel 的值
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "toEditChannel")
    public ModelAndView toEditChannel(DictServiceAttribute entity, HttpServletRequest request, Model model) {
		model.addAttribute("entity", entity);
        return new ModelAndView("/system/deploymentAndCluster/editChannel");
    }
}