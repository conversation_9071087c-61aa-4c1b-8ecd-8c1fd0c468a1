delete from SYS_MENU;
delete from DICT_USER_LOG;
delete from DICT_SERVICE_ATTRIBUTE;
delete from DICT_PROFILE_DATA_ITEM;
delete from DICT_PROFILE;
delete from DICT_OPERATION;
delete from DICT_METER_DATA_STORAGE_INFO;
delete from DICT_METER_DATA_STORAGE_TABLE;
delete from DICT_FUNCTION;
delete from DICT_DEVICE_TYPE;
delete from DICT_DEVICE_MODEL;
delete from DICT_PROTOCOL;
delete from DICT_MANUFACTURER;
delete from DICT_DATAITEM_PARSE_DLMS;
delete from DICT_DATAITEM_GROUP_MAP;
delete from DICT_DATAITEM_GROUP;
delete from DICT_DATAITEM;
delete from DICT_COMMUNICATION_TYPE;
delete from SYS_VERSION;
delete from SYS_UTILITY;
delete from SYS_SERVICE_ATTRIBUTE;
delete from SYS_SERVICE;
delete from SYS_SERVER;
delete from SYS_ROLE_MENU;
delete from SYS_ROLE;
delete from SYS_ORG;
delete from SYS_DATAITEM_EXPORT;

insert into DICT_COMMUNICATION_TYPE (id, name, introduction) values ('100', 'GPRS', null);
insert into DICT_COMMUNICATION_TYPE (id, name, introduction) values ('101', 'Ethernet', null);
insert into DICT_COMMUNICATION_TYPE (id, name, introduction) values ('102', '3G', null);
insert into DICT_COMMUNICATION_TYPE (id, name, introduction) values ('103', '4G', null);
insert into DICT_COMMUNICATION_TYPE (id, name, introduction) values ('200', 'RS485', null);
insert into DICT_COMMUNICATION_TYPE (id, name, introduction) values ('201', 'G3 PLC', null);
insert into DICT_COMMUNICATION_TYPE (id, name, introduction) values ('202', 'RF', null);
insert into DICT_COMMUNICATION_TYPE (id, name, introduction) values ('203', 'FSK PLC', null);

insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.2.13','100','Update unit charge','113#0.0.19.10.2.255#1',null,'A',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.2.14','100','Activate passive unit charge','113#0.0.19.10.2.255#2',null,'A',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.2.15','100','Collect','113#0.0.19.10.2.255#3',null,'A',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.2.16','100','Update total amount remaining','113#0.0.19.10.2.255#4',null,'A',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.2.17','100','Set total amount remaining','113#0.0.19.10.2.255#5',null,'A',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.1.1.12.0.0.0.0.0.0.0.224.3.72.0','100','Active energy import (+A) [Unit: kWh]','3#1.0.1.8.0.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.1.1.12.0.0.0.0.0.0.0.224.3.72.0','100','Active energy import (+A) (Daily) [Unit: kWh]','3#1.0.1.8.0.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.1.1.12.0.0.0.0.0.0.0.224.3.72.0','100','Active energy import (+A) (Monthly) [Unit: kWh]','3#1.0.1.8.0.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.1.1.12.0.0.0.0.1.0.0.224.3.72.0','100','Active energy import (+A) rate 1 [Unit: kWh]','3#1.0.1.8.1.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.1.1.12.0.0.0.0.1.0.0.224.3.72.0','100','Active energy import (+A) rate 1 (Daily) [Unit: kWh]','3#1.0.1.8.1.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.1.1.12.0.0.0.0.1.0.0.224.3.72.0','100','Active energy import (+A) rate 1 (Monthly) [Unit: kWh]','3#1.0.1.8.1.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.1.1.12.0.0.0.0.2.0.0.224.3.72.0','100','Active energy import (+A) rate 2 [Unit: kWh]','3#1.0.1.8.2.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.1.1.12.0.0.0.0.2.0.0.224.3.72.0','100','Active energy import (+A) rate 2 (Daily) [Unit: kWh]','3#1.0.1.8.2.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.1.1.12.0.0.0.0.2.0.0.224.3.72.0','100','Active energy import (+A) rate 2 (Monthly) [Unit: kWh]','3#1.0.1.8.2.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.1.1.12.0.0.0.0.3.0.0.224.3.72.0','100','Active energy import (+A) rate 3 [Unit: kWh]','3#1.0.1.8.3.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.1.1.12.0.0.0.0.3.0.0.224.3.72.0','100','Active energy import (+A) rate 3 (Daily) [Unit: kWh]','3#1.0.1.8.3.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.1.1.12.0.0.0.0.3.0.0.224.3.72.0','100','Active energy import (+A) rate 3 (Monthly) [Unit: kWh]','3#1.0.1.8.3.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.1.1.12.0.0.0.0.4.0.0.224.3.72.0','100','Active energy import (+A) rate 4 [Unit: kWh]','3#1.0.1.8.4.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.1.1.12.0.0.0.0.4.0.0.224.3.72.0','100','Active energy import (+A) rate 4 (Daily) [Unit: kWh]','3#1.0.1.8.4.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.1.1.12.0.0.0.0.4.0.0.224.3.72.0','100','Active energy import (+A) rate 4 (Monthly) [Unit: kWh]','3#1.0.1.8.4.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.19.1.12.0.0.0.0.0.0.0.224.3.72.0','100','Active energy export (-A) [Unit: kWh]','3#1.0.2.8.0.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.19.1.12.0.0.0.0.0.0.0.224.3.72.0','100','Active energy export (-A) (Daily) [Unit: kWh]','3#1.0.2.8.0.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.19.1.12.0.0.0.0.0.0.0.224.3.72.0','100','Active energy export (-A) (Monthly) [Unit: kWh]','3#1.0.2.8.0.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.19.1.12.0.0.0.0.1.0.0.224.3.72.0','100','Active energy export (-A) rate 1 [Unit: kWh]','3#1.0.2.8.1.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.19.1.12.0.0.0.0.1.0.0.224.3.72.0','100','Active energy export (-A) rate 1 (Daily) [Unit: kWh]','3#1.0.2.8.1.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.19.1.12.0.0.0.0.1.0.0.224.3.72.0','100','Active energy export (-A) rate 1 (Monthly) [Unit: kWh]','3#1.0.2.8.1.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.19.1.12.0.0.0.0.2.0.0.224.3.72.0','100','Active energy export (-A) rate 2 [Unit: kWh]','3#1.0.2.8.2.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.19.1.12.0.0.0.0.2.0.0.224.3.72.0','100','Active energy export (-A) rate 2 (Daily) [Unit: kWh]','3#1.0.2.8.2.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.19.1.12.0.0.0.0.2.0.0.224.3.72.0','100','Active energy export (-A) rate 2 (Monthly) [Unit: kWh]','3#1.0.2.8.2.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.19.1.12.0.0.0.0.3.0.0.224.3.72.0','100','Active energy export (-A) rate 3 [Unit: kWh]','3#1.0.2.8.3.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.19.1.12.0.0.0.0.3.0.0.224.3.72.0','100','Active energy export (-A) rate 3 (Daily) [Unit: kWh]','3#1.0.2.8.3.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.19.1.12.0.0.0.0.3.0.0.224.3.72.0','100','Active energy export (-A) rate 3 (Monthly) [Unit: kWh]','3#1.0.2.8.3.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.19.1.12.0.0.0.0.4.0.0.224.3.72.0','100','Active energy export (-A) rate 4 [Unit: kWh]','3#1.0.2.8.4.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.19.1.12.0.0.0.0.4.0.0.224.3.72.0','100','Active energy export (-A) rate 4 (Daily) [Unit: kWh]','3#1.0.2.8.4.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.19.1.12.0.0.0.0.4.0.0.224.3.72.0','100','Active energy export (-A) rate 4 (Monthly) [Unit: kWh]','3#1.0.2.8.4.255#2','Wh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.5.1.12.0.0.0.0.0.0.0.224.3.73.0','100','Reactive energy import (+R) (QI+QII) [Unit: kVarh]','3#1.0.3.8.0.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.5.1.12.0.0.0.0.0.0.0.224.3.73.0','100','Reactive energy import (+R) (QI+QII) (Daily) [Unit: kVarh]','3#1.0.3.8.0.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.5.1.12.0.0.0.0.0.0.0.224.3.73.0','100','Reactive energy import (+R) (QI+QII) (Monthly) [Unit: kVarh]','3#1.0.3.8.0.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.5.1.12.0.0.0.0.1.0.0.224.3.73.0','100','Reactive energy import (+R) (QI+QII) rate 1 [Unit: kVarh]','3#1.0.3.8.1.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.5.1.12.0.0.0.0.1.0.0.224.3.73.0','100','Reactive energy import (+R) (QI+QII) rate 1 (Daily) [Unit: kVarh]','3#1.0.3.8.1.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.5.1.12.0.0.0.0.1.0.0.224.3.73.0','100','Reactive energy import (+R) (QI+QII) rate 1 (Monthly) [Unit: kVarh]','3#1.0.3.8.1.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.5.1.12.0.0.0.0.2.0.0.224.3.73.0','100','Reactive energy import (+R) (QI+QII) rate 2 [Unit: kVarh]','3#1.0.3.8.2.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.5.1.12.0.0.0.0.2.0.0.224.3.73.0','100','Reactive energy import (+R) (QI+QII) rate 2 (Daily) [Unit: kVarh]','3#1.0.3.8.2.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.5.1.12.0.0.0.0.2.0.0.224.3.73.0','100','Reactive energy import (+R) (QI+QII) rate 2 (Monthly) [Unit: kVarh]','3#1.0.3.8.2.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.5.1.12.0.0.0.0.3.0.0.224.3.73.0','100','Reactive energy import (+R) (QI+QII) rate 3 [Unit: kVarh]','3#1.0.3.8.3.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.5.1.12.0.0.0.0.3.0.0.224.3.73.0','100','Reactive energy import (+R) (QI+QII) rate 3 (Daily) [Unit: kVarh]','3#1.0.3.8.3.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.5.1.12.0.0.0.0.3.0.0.224.3.73.0','100','Reactive energy import (+R) (QI+QII) rate 3 (Monthly) [Unit: kVarh]','3#1.0.3.8.3.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.5.1.12.0.0.0.0.4.0.0.224.3.73.0','100','Reactive energy import (+R) (QI+QII) rate 4 [Unit: kVarh]','3#1.0.3.8.4.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.5.1.12.0.0.0.0.4.0.0.224.3.73.0','100','Reactive energy import (+R) (QI+QII) rate 4 (Daily) [Unit: kVarh]','3#1.0.3.8.4.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.5.1.12.0.0.0.0.4.0.0.224.3.73.0','100','Reactive energy import (+R) (QI+QII) rate 4 (Monthly) [Unit: kVarh]','3#1.0.3.8.4.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.13.1.12.0.0.0.0.0.0.0.224.3.73.0','100','Reactive energy export (-R) (QIII+QIV) [Unit: kVarh]','3#1.0.4.8.0.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.13.1.12.0.0.0.0.0.0.0.224.3.73.0','100','Reactive energy export (-R) (QIII+QIV) (Daily) [Unit: kVarh]','3#1.0.4.8.0.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.13.1.12.0.0.0.0.0.0.0.224.3.73.0','100','Reactive energy export (-R) (QIII+QIV) (Monthly) [Unit: kVarh]','3#1.0.4.8.0.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.13.1.12.0.0.0.0.1.0.0.224.3.73.0','100','Reactive energy (-R) (QIII+QIV) rate 1 [Unit: kVarh]','3#1.0.4.8.1.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.13.1.12.0.0.0.0.1.0.0.224.3.73.0','100','Reactive energy (-R) (QIII+QIV) rate 1 (Daily) [Unit: kVarh]','3#1.0.4.8.1.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.13.1.12.0.0.0.0.1.0.0.224.3.73.0','100','Reactive energy (-R) (QIII+QIV) rate 1 (Monthly) [Unit: kVarh]','3#1.0.4.8.1.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.13.1.12.0.0.0.0.2.0.0.224.3.73.0','100','Reactive energy (-R) (QIII+QIV) rate 2 [Unit: kVarh]','3#1.0.4.8.2.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.13.1.12.0.0.0.0.2.0.0.224.3.73.0','100','Reactive energy (-R) (QIII+QIV) rate 2 (Daily) [Unit: kVarh]','3#1.0.4.8.2.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.13.1.12.0.0.0.0.2.0.0.224.3.73.0','100','Reactive energy (-R) (QIII+QIV) rate 2 (Monthly) [Unit: kVarh]','3#1.0.4.8.2.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.13.1.12.0.0.0.0.3.0.0.224.3.73.0','100','Reactive energy (-R) (QIII+QIV) rate 3 [Unit: kVarh]','3#1.0.4.8.3.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.13.1.12.0.0.0.0.3.0.0.224.3.73.0','100','Reactive energy (-R) (QIII+QIV) rate 3 (Daily) [Unit: kVarh]','3#1.0.4.8.3.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.13.1.12.0.0.0.0.3.0.0.224.3.73.0','100','Reactive energy (-R) (QIII+QIV) rate 3 (Monthly) [Unit: kVarh]','3#1.0.4.8.3.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.13.1.12.0.0.0.0.4.0.0.224.3.73.0','100','Reactive energy (-R) (QIII+QIV) rate 4 [Unit: kVarh]','3#1.0.4.8.4.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.13.1.12.0.0.0.0.4.0.0.224.3.73.0','100','Reactive energy (-R) (QIII+QIV) rate 4 (Daily) [Unit: kVarh]','3#1.0.4.8.4.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.13.1.12.0.0.0.0.4.0.0.224.3.73.0','100','Reactive energy (-R) (QIII+QIV) rate 4 (Monthly) [Unit: kVarh]','3#1.0.4.8.4.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.15.1.12.0.0.0.0.0.0.0.224.3.73.0','100','Reactive energy QI (+Ri) [Unit: kVarh]','3#1.0.5.8.0.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.15.1.12.0.0.0.0.0.0.0.224.3.73.0','100','Reactive energy QI (+Ri) (Daily) [Unit: kVarh]','3#1.0.5.8.0.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.15.1.12.0.0.0.0.0.0.0.224.3.73.0','100','Reactive energy QI (+Ri) (Monthly) [Unit: kVarh]','3#1.0.5.8.0.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.15.1.12.0.0.0.0.1.0.0.224.3.73.0','100','Reactive energy QI (+Ri) rate 1 [Unit: kVarh]','3#1.0.5.8.1.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.15.1.12.0.0.0.0.1.0.0.224.3.73.0','100','Reactive energy QI (+Ri) rate 1 (Daily) [Unit: kVarh]','3#1.0.5.8.1.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.15.1.12.0.0.0.0.1.0.0.224.3.73.0','100','Reactive energy QI (+Ri) rate 1 (Monthly) [Unit: kVarh]','3#1.0.5.8.1.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.15.1.12.0.0.0.0.2.0.0.224.3.73.0','100','Reactive energy QI (+Ri) rate 2 [Unit: kVarh]','3#1.0.5.8.2.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.15.1.12.0.0.0.0.2.0.0.224.3.73.0','100','Reactive energy QI (+Ri) rate 2 (Daily) [Unit: kVarh]','3#1.0.5.8.2.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.15.1.12.0.0.0.0.2.0.0.224.3.73.0','100','Reactive energy QI (+Ri) rate 2 (Monthly) [Unit: kVarh]','3#1.0.5.8.2.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.15.1.12.0.0.0.0.3.0.0.224.3.73.0','100','Reactive energy QI (+Ri) rate 3 [Unit: kVarh]','3#1.0.5.8.3.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.15.1.12.0.0.0.0.3.0.0.224.3.73.0','100','Reactive energy QI (+Ri) rate 3 (Daily) [Unit: kVarh]','3#1.0.5.8.3.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.15.1.12.0.0.0.0.3.0.0.224.3.73.0','100','Reactive energy QI (+Ri) rate 3 (Monthly) [Unit: kVarh]','3#1.0.5.8.3.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.15.1.12.0.0.0.0.4.0.0.224.3.73.0','100','Reactive energy QI (+Ri) rate 4 [Unit: kVarh]','3#1.0.5.8.4.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.15.1.12.0.0.0.0.4.0.0.224.3.73.0','100','Reactive energy QI (+Ri) rate 4 (Daily) [Unit: kVarh]','3#1.0.5.8.4.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.15.1.12.0.0.0.0.4.0.0.224.3.73.0','100','Reactive energy QI (+Ri) rate 4 (Monthly) [Unit: kVarh]','3#1.0.5.8.4.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.16.1.12.0.0.0.0.0.0.0.224.3.73.0','100','Reactive energy QII (+Rc) [Unit: kVarh]','3#1.0.6.8.0.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.16.1.12.0.0.0.0.0.0.0.224.3.73.0','100','Reactive energy QII (+Rc) (Daily) [Unit: kVarh]','3#1.0.6.8.0.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.16.1.12.0.0.0.0.0.0.0.224.3.73.0','100','Reactive energy QII (+Rc) (Monthly) [Unit: kVarh]','3#1.0.6.8.0.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.16.1.12.0.0.0.0.1.0.0.224.3.73.0','100','Reactive energy QII (+Rc) rate 1 [Unit: kVarh]','3#1.0.6.8.1.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.16.1.12.0.0.0.0.1.0.0.224.3.73.0','100','Reactive energy QII (+Rc) rate 1(Daily) [Unit: kVarh]','3#1.0.6.8.1.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.16.1.12.0.0.0.0.1.0.0.224.3.73.0','100','Reactive energy QII (+Rc) rate 1 (Monthly) [Unit: kVarh]','3#1.0.6.8.1.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.16.1.12.0.0.0.0.2.0.0.224.3.73.0','100','Reactive energy QII (+Rc) rate 2 [Unit: kVarh]','3#1.0.6.8.2.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.16.1.12.0.0.0.0.2.0.0.224.3.73.0','100','Reactive energy QII (+Rc) rate 2(Daily) [Unit: kVarh]','3#1.0.6.8.2.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.16.1.12.0.0.0.0.2.0.0.224.3.73.0','100','Reactive energy QII (+Rc) rate 2 (Monthly) [Unit: kVarh]','3#1.0.6.8.2.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.16.1.12.0.0.0.0.3.0.0.224.3.73.0','100','Reactive energy QII (+Rc) rate 3 [Unit: kVarh]','3#1.0.6.8.3.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.16.1.12.0.0.0.0.3.0.0.224.3.73.0','100','Reactive energy QII (+Rc) rate 3(Daily) [Unit: kVarh]','3#1.0.6.8.3.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.16.1.12.0.0.0.0.3.0.0.224.3.73.0','100','Reactive energy QII (+Rc) rate 3 (Monthly) [Unit: kVarh]','3#1.0.6.8.3.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.16.1.12.0.0.0.0.4.0.0.224.3.73.0','100','Reactive energy QII (+Rc) rate 4 [Unit: kVarh]','3#1.0.6.8.4.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.16.1.12.0.0.0.0.4.0.0.224.3.73.0','100','Reactive energy QII (+Rc) rate 4(Daily) [Unit: kVarh]','3#1.0.6.8.4.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.16.1.12.0.0.0.0.4.0.0.224.3.73.0','100','Reactive energy QII (+Rc) rate 4 (Monthly) [Unit: kVarh]','3#1.0.6.8.4.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.17.1.12.0.0.0.0.0.0.0.224.3.73.0','100','Reactive energy QIII (-Ri) [Unit: kVarh]','3#1.0.7.8.0.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.17.1.12.0.0.0.0.0.0.0.224.3.73.0','100','Reactive energy QIII (-Ri) (Daily) [Unit: kVarh]','3#1.0.7.8.0.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.17.1.12.0.0.0.0.0.0.0.224.3.73.0','100','Reactive energy QIII (-Ri) (Monthly) [Unit: kVarh]','3#1.0.7.8.0.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.17.1.12.0.0.0.0.1.0.0.224.3.73.0','100','Reactive energy QIII (-Ri) rate 1 [Unit: kVarh]','3#1.0.7.8.1.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.17.1.12.0.0.0.0.1.0.0.224.3.73.0','100','Reactive energy QIII (-Ri) rate 1 (Daily) [Unit: kVarh]','3#1.0.7.8.1.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.17.1.12.0.0.0.0.1.0.0.224.3.73.0','100','Reactive energy QIII (-Ri) rate 1 (Monthly) [Unit: kVarh]','3#1.0.7.8.1.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.17.1.12.0.0.0.0.2.0.0.224.3.73.0','100','Reactive energy QIII (-Ri) rate 2 [Unit: kVarh]','3#1.0.7.8.2.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.17.1.12.0.0.0.0.2.0.0.224.3.73.0','100','Reactive energy QIII (-Ri) rate 2 (Daily) [Unit: kVarh]','3#1.0.7.8.2.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.17.1.12.0.0.0.0.2.0.0.224.3.73.0','100','Reactive energy QIII (-Ri) rate 2 (Monthly) [Unit: kVarh]','3#1.0.7.8.2.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.17.1.12.0.0.0.0.3.0.0.224.3.73.0','100','Reactive energy QIII (-Ri) rate 3 [Unit: kVarh]','3#1.0.7.8.3.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.17.1.12.0.0.0.0.3.0.0.224.3.73.0','100','Reactive energy QIII (-Ri) rate 3 (Daily) [Unit: kVarh]','3#1.0.7.8.3.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.17.1.12.0.0.0.0.3.0.0.224.3.73.0','100','Reactive energy QIII (-Ri) rate 3 (Monthly) [Unit: kVarh]','3#1.0.7.8.3.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.17.1.12.0.0.0.0.4.0.0.224.3.73.0','100','Reactive energy QIII (-Ri) rate 4 [Unit: kVarh]','3#1.0.7.8.4.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.17.1.12.0.0.0.0.4.0.0.224.3.73.0','100','Reactive energy QIII (-Ri) rate 4 (Daily) [Unit: kVarh]','3#1.0.7.8.4.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.17.1.12.0.0.0.0.4.0.0.224.3.73.0','100','Reactive energy QIII (-Ri) rate 4 (Monthly) [Unit: kVarh]','3#1.0.7.8.4.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.18.1.12.0.0.0.0.0.0.0.224.3.73.0','100','Reactive energy QIV (-Rc) [Unit: kVarh]','3#1.0.8.8.0.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.18.1.12.0.0.0.0.0.0.0.224.3.73.0','100','Reactive energy QIV (-Rc) (Daily) [Unit: kVarh]','3#1.0.8.8.0.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.18.1.12.0.0.0.0.0.0.0.224.3.73.0','100','Reactive energy QIV (-Rc) (Monthly) [Unit: kVarh]','3#1.0.8.8.0.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.18.1.12.0.0.0.0.1.0.0.224.3.73.0','100','Reactive energy QIV (-Rc) rate 1 [Unit: kVarh]','3#1.0.8.8.1.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.18.1.12.0.0.0.0.1.0.0.224.3.73.0','100','Reactive energy QIV (-Rc) rate 1 (Daily) [Unit: kVarh]','3#1.0.8.8.1.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.18.1.12.0.0.0.0.1.0.0.224.3.73.0','100','Reactive energy QIV (-Rc) rate 1 (Monthly) [Unit: kVarh]','3#1.0.8.8.1.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.18.1.12.0.0.0.0.2.0.0.224.3.73.0','100','Reactive energy QIV (-Rc) rate 2 [Unit: kVarh]','3#1.0.8.8.2.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.18.1.12.0.0.0.0.2.0.0.224.3.73.0','100','Reactive energy QIV (-Rc) rate 2 (Daily) [Unit: kVarh]','3#1.0.8.8.2.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.18.1.12.0.0.0.0.2.0.0.224.3.73.0','100','Reactive energy QIV (-Rc) rate 2 (Monthly) [Unit: kVarh]','3#1.0.8.8.2.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.18.1.12.0.0.0.0.3.0.0.224.3.73.0','100','Reactive energy QIV (-Rc) rate 3 [Unit: kVarh]','3#1.0.8.8.3.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.18.1.12.0.0.0.0.3.0.0.224.3.73.0','100','Reactive energy QIV (-Rc) rate 3 (Daily) [Unit: kVarh]','3#1.0.8.8.3.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.18.1.12.0.0.0.0.3.0.0.224.3.73.0','100','Reactive energy QIV (-Rc) rate 3 (Monthly) [Unit: kVarh]','3#1.0.8.8.3.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.18.1.12.0.0.0.0.4.0.0.224.3.73.0','100','Reactive energy QIV (-Rc) rate 4 [Unit: kVarh]','3#1.0.8.8.4.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.26.0.1.18.1.12.0.0.0.0.4.0.0.224.3.73.0','100','Reactive energy QIV (-Rc) rate 4 (Daily) [Unit: kVarh]','3#1.0.8.8.4.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.26.0.1.18.1.12.0.0.0.0.4.0.0.224.3.73.0','100','Reactive energy QIV (-Rc) rate 4 (Monthly) [Unit: kVarh]','3#1.0.8.8.4.255#2','Varh','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.0.0.12.0.1.38.0.0.0.0.0.0.0.0.0.65.0','100','Instantaneous Power factor (+A/+VA)','3#1.0.13.7.0.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.0.0.12.0.1.38.0.0.0.0.0.0.0.128.0.65.0','100','Instantaneous power factor (PF) L1','3#1.0.33.7.0.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.0.0.12.0.1.38.0.0.0.0.0.0.0.64.0.65.0','100','Instantaneous power factor (PF) L2','3#1.0.53.7.0.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.0.0.12.0.1.38.0.0.0.0.0.0.0.32.0.65.0','100','Instantaneous power factor (PF) L3','3#1.0.73.7.0.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.0.0.12.1.1.37.0.0.0.0.0.0.0.224.3.38.0','100','Instantaneous active import power (+A) [Unit: kW]','3#1.0.1.7.0.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.0.0.12.19.1.37.0.0.0.0.0.0.0.224.3.38.0','100','Instantaneous active export power (-A) [Unit: kW]','3#1.0.2.7.0.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.0.0.12.1.1.37.0.0.0.0.0.0.0.224.3.63.0','100','Instantaneous reactive import power (+R) [Unit: kVar]','3#1.0.3.7.0.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.0.0.12.19.1.37.0.0.0.0.0.0.0.224.3.63.0','100','Instantaneous reactive export power (-R) [Unit: kVar]','3#1.0.4.7.0.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.0.0.12.0.1.4.0.0.0.0.0.0.0.128.0.5.0','100','Instantaneous current L1 [Unit: A]','3#1.0.31.7.0.255#2','A','R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.0.0.12.0.1.4.0.0.0.0.0.0.0.64.0.5.0','100','Instantaneous current L2 [Unit: A]','3#1.0.51.7.0.255#2','A','R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.0.0.12.0.1.4.0.0.0.0.0.0.0.32.0.5.0','100','Instantaneous current L3 [Unit: A]','3#1.0.71.7.0.255#2','A','R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.2.2.0.0.1.4.0.0.0.0.0.0.0.0.0.5.0','100','Current [Unit: A]','3#1.0.11.7.0.255#2','A','R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.2.2.0.0.1.4.0.0.0.0.0.0.0.16.0.5.0','100','Neutral current [Unit: A]','3#1.0.91.7.0.255#2','A','R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.0.0.12.0.1.54.0.0.0.0.0.0.0.128.0.29.0','100','Instantaneous voltage L1 [Unit: V] ','3#1.0.32.7.0.255#2','V','R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.0.0.12.0.1.54.0.0.0.0.0.0.0.64.0.29.0','100','Instantaneous voltage L2 [Unit: V]','3#1.0.52.7.0.255#2','V','R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.0.0.12.0.1.54.0.0.0.0.0.0.0.32.0.29.0','100','Instantaneous voltage L3 [Unit: V]','3#1.0.72.7.0.255#2','V','R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.2.2.0.0.1.54.0.0.0.0.0.0.0.0.0.29.0','100','Voltage [Unit: V]','3#1.0.12.7.0.255#2','V','R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.0.0.12.0.1.15.0.0.0.0.0.0.0.224.0.33.0','100','Frequency [Unit: Hz]','3#1.0.14.7.0.255#2','Hz','R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('37.0.1.2','100','calendar_name_active','20#0.0.13.0.0.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('37.0.1.3','100','season_profile_active','20#0.0.13.0.0.255#3',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('37.0.1.4','100','week_profile_table_active','20#0.0.13.0.0.255#4',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('37.0.1.5','100','day_profile_table_active','20#0.0.13.0.0.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('37.0.1.6','100','calendar_name_passive','20#0.0.13.0.0.255#6',null,'RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('37.0.1.7','100','season_profile_passive','20#0.0.13.0.0.255#7',null,'RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('37.0.1.8','100','week_profile_table_passive','20#0.0.13.0.0.255#8',null,'RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('37.0.1.9','100','day_profile_table_passive','20#0.0.13.0.0.255#9',null,'RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('37.0.1.10','100','activate_passive_calendar_time','20#0.0.13.0.0.255#10',null,'RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('37.1.1.1','100','avtivate_passive_calendar','20#0.0.13.0.0.255#1',null,'A',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('37.0.1.6.0.0.0','100','Passive Calendar',null,null,'RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('37.2.1.1','100','Special day','11#0.0.11.0.0.255#2',null,'RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.8','100','Under Limit Threshold of miss Voltage [Unit: V]','3#1.0.12.39.0.255#2','V','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.15','100','Time threshold for Over Limit of Voltage [Unit: Sec]','3#1.0.12.44.0.255#2','Sec','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.16','100','Time threshold for under Limit of miss Voltage [Unit: Sec]','3#1.0.12.45.0.255#2','Sec','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.17','100','Under Limit Threshold of power factor','3#1.0.13.31.0.255#2',null,'RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.18','100','Time threshold for Under Limit Threshold of power factor [Unit: Sec]','3#1.0.13.43.0.255#2','Sec','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.19','100','Under Limit Threshold of frequency [Unit: Hz]','3#1.0.14.31.0.255#2','Hz','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.20','100','Time threshold for Under Limit Threshold of frequency [Unit: Sec]','3#1.0.14.43.0.255#2','Sec','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.21','100','Over Limit Threshold of frequency [Unit: Hz]','3#1.0.14.35.0.255#2','Hz','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.22','100','Time threshold for Over Limit Threshold of frequency [Unit: sec]','3#1.0.14.44.0.255#2','Sec','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.23','100','Magnitic dectect time threshold [Unit: sec]','3#0.0.96.20.129.255#2','Sec','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.24','100','Current reverse time threshold [Unit: sec]','3#0.0.96.20.132.255#2','Sec','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.25','100','Time threshold for Long Power Failure [Unit: Sec]','3#0.0.96.7.20.255#2','Sec','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.26','100','Over load power threshold [Unit: kW]','3#1.0.15.35.0.255#2','W','RW',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.27','100','Time threshold for over load Power [Unit: Sec]','3#1.0.15.44.0.255#2','Sec','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.28','100','Thereshold for current low(by pass) [Unit: %]','3#1.0.11.31.0.255#2','%','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.29','100','Thereshold for current overflow [Unit: A]','3#1.0.11.35.0.255#2','A','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.30','100','Thereshold for current missing(ct open) [Unit: %]','3#1.0.11.39.0.255#2','%','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.31','100','Thereshold for current unbalance [Unit: %]','3#1.0.11.129.0.255#2','%','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.32','100','Thereshold for voltage unbalance [Unit: %]','3#1.0.12.129.0.255#2','%','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.33','100','Time threshold for Low current(by pass) [Unit: Sec]','3#1.0.11.43.0.255#2','Sec','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.34','100','Time threshold for Low current overflow [Unit: Sec]','3#1.0.11.44.0.255#2','Sec','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.35','100','Time threshold for Low current missing [Unit: Sec]','3#1.0.11.45.0.255#2','Sec','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.36','100','Time threshold for unbalance of Current [Unit: Sec]','3#1.0.11.129.1.255#2','Sec','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.37','100','Time threshold for unbalance of Voltage [Unit: Sec]','3#1.0.12.129.1.255#2','Sec','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.38','100','Under Limit Threshold of Voltage [Unit: V]','3#1.0.12.31.0.255#2','V','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.39','100','Over Limit Threshold of Voltage [Unit: V]','3#1.0.12.35.0.255#2','V','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('38.0.0.40','100','Time threshold for Under Limit of Voltage [Unit: Sec]','3#1.0.12.43.0.255#2','Sec','RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.0.285','100','Power Down','7#0.0.99.98.0.255#2#1',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.0.216','100','Power Up','7#0.0.99.98.0.255#2#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.36.56.76','100','Daylight Saving Time Enabled/Disable','7#0.0.99.98.0.255#2#3',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.36.116.58','100','Clock Adjust (old date/time)','7#0.0.99.98.0.255#2#4',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.36.116.59','100','Clock Adjust (new date/time)','7#0.0.99.98.0.255#2#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.36.0.35','100','Clock Invalid','7#0.0.99.98.0.255#2#6',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.2.0.284','100','Replace Battery','7#0.0.99.98.0.255#2#7',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.2.22.150','100','Battery voltage low','7#0.0.99.98.0.255#2#8',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.20.121.4','100','TOU activated','7#0.0.99.98.0.255#2#9',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.0.89.28','100','Error Register Cleared','7#0.0.99.98.0.255#2#10',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.17.285.28','100','Alarm Register Cleared','7#0.0.99.98.0.255#2#11',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.18.83.79','100','Meter Program Memory Error','7#0.0.99.98.0.255#2#12',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.18.85.79','100','RAM Error','7#0.0.99.98.0.255#2#13',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.18.72.79','100','NV Memory Error','7#0.0.99.98.0.255#2#14',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.37.0.79','100','Watchdog Error','7#0.0.99.98.0.255#2#15',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.21.67.79','100','Measurement System Error','7#0.0.99.98.0.255#2#16',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.11.17.280','100','Firmware ready for activation','7#0.0.99.98.0.255#2#17',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.11.17.4','100','Firmware activated','7#0.0.99.98.0.255#2#18',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.20.121.213','100','Passive ToU programmed','7#0.0.99.98.0.255#2#19',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.7.75.24','100','Parameter(s) Changed','7#0.0.99.98.0.255#2#47',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.12.32.24','100','Security key(s) changed','7#0.0.99.98.0.255#2#48',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.11.46.35','100','Firmware verification failed','7#0.0.99.98.0.255#2#51',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.8.0.40','100','Unexpected consumption','7#0.0.99.98.0.255#2#52',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.7.25.91','100','Phase sequence reversal','7#0.0.99.98.0.255#2#88',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.137.85','100','Missing neutral','7#0.0.99.98.0.255#2#89',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.19.212.85','100','No connection timeout','7#0.0.99.98.5.255#2#140',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.19.298.85','100','Modem Initialization Failure','7#0.0.99.98.5.255#2#141',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.19.69.85','100','SIM Card Failure','7#0.0.99.98.5.255#2#142',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.19.69.37','100','SIM Card Ok','7#0.0.99.98.5.255#2#143',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.19.90.85','100','GSM Registration Failure','7#0.0.99.98.5.255#2#144',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.19.91.85','100','GPRS Registration Failure','7#0.0.99.98.5.255#2#145',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.19.0.214','100','Modem SW Reset','7#0.0.99.98.5.255#2#149',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.19.0.42','100','GSM Outgoing Connection','7#0.0.99.98.5.255#2#151',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.19.1.42','100','GSM Incoming Connection','7#0.0.99.98.5.255#2#152',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.19.0.68','100','GSM Hang-up','7#0.0.99.98.5.255#2#153',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.19.299.85','100','User Initialization Failure','7#0.0.99.98.5.255#2#155',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.23.0.47','100','Signal Quality Low','7#0.0.99.98.5.255#2#156',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.12.141.39','100','Terminal Cover opened','7#0.0.99.98.1.255#2#40',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.12.141.16','100','Terminal Cover closed','7#0.0.99.98.1.255#2#41',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.12.66.257','100','Strong DC field detected','7#0.0.99.98.1.255#2#42',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.12.66.212','100','Strong DC field removed','7#0.0.99.98.1.255#2#43',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.12.29.39','100','Meter cover opened','7#0.0.99.98.1.255#2#44',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('**********','100','Meter cover closed','7#0.0.99.98.1.255#2#45',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.23.74.85','100','Association authentication failure after n times','7#0.0.99.98.1.255#2#46',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.12.36.85','100','Decryption or Message Authentication failure','7#0.0.99.98.1.255#2#49',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.0.48.219','100','Active Power Reversal','7#0.0.99.98.1.255#2#91',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.31.0.280','100','Disconnector in "Ready for Reconnection"','7#0.0.99.98.2.255#2#59',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('*********','100','Manual Disconnection','7#0.0.99.98.2.255#2#60',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('*********','100','Manual Connection','7#0.0.99.98.2.255#2#61',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('***********','100','Remote Disconnection','7#0.0.99.98.2.255#2#62',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('***********','100','Remote Connection','7#0.0.99.98.2.255#2#63',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('*********','100','Local Disconnection','7#0.0.99.98.2.255#2#64',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.8.261.139','100','Limiter threshold exceed','7#0.0.99.98.2.255#2#65',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.8.261.37','100','Limiter threshold OK','7#0.0.99.98.2.255#2#66',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.8.0.295','100','Limiter threshold changed','7#0.0.99.98.2.255#2#67',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.31.0.67','100','Disconnect /Reconnect failure','7#0.0.99.98.2.255#2#68',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.31.0.49','100','Local Reconnection','7#0.0.99.98.2.255#2#69',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.8.287.139','100','Fuse supervision L1, threshold exceeded','7#0.0.99.98.2.255#2#70',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.8.287.37','100','Fuse supervision L1, threshold OK','7#0.0.99.98.2.255#2#71',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.8.288.139','100','Fuse supervision L2, threshold exceeded','7#0.0.99.98.2.255#2#72',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.8.288.37','100','Fuse supervision L2, threshold OK','7#0.0.99.98.2.255#2#73',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.8.289.139','100','Fuse supervision L3, threshold exceeded','7#0.0.99.98.2.255#2#74',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.8.289.37','100','Fuse supervision L3, threshold OK','7#0.0.99.98.2.255#2#75',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.131.223','100','Under voltage (voltage SAG) L1','7#0.0.99.98.4.255#2#76',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.132.223','100','Under voltage (voltage SAG) L2','7#0.0.99.98.4.255#2#77',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.133.223','100','Under voltage (voltage SAG) L3','7#0.0.99.98.4.255#2#78',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.131.248','100','Over voltage (voltage SWELL) L1','7#0.0.99.98.4.255#2#79',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.132.248','100','Overvoltage (voltage SWELL) L2','7#0.0.99.98.4.255#2#80',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.133.248','100','Overvoltage (voltage SWELL) L3','7#0.0.99.98.4.255#2#81',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.131.285','100','Missing Voltage (Voltage Cut) L1','7#0.0.99.98.4.255#2#82',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.132.285','100','Missing Voltage (Voltage Cut) L2','7#0.0.99.98.4.255#2#83',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.133.285','100','Missing Voltage (Voltage Cut) L3','7#0.0.99.98.4.255#2#84',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.131.37','100','Normal Voltage L1','7#0.0.99.98.4.255#2#85',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.132.37','100','Normal Voltage L2','7#0.0.99.98.4.255#2#86',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.133.37','100','Normal Voltage L3','7#0.0.99.98.4.255#2#87',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.0.98','100','Phase Asymmetry','7#0.0.99.98.4.255#2#90',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.131.40','100','Bad Voltage Quality L1','7#0.0.99.98.4.255#2#92',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.132.40','100','Bad Voltage Quality L2','7#0.0.99.98.4.255#2#93',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.133.40','100','Bad Voltage Quality L3','7#0.0.99.98.4.255#2#94',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.6.152','100','Over current L1 started','7#0.0.99.98.4.255#2#209',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.6.153','100','Over current L1 stopped','7#0.0.99.98.4.255#2#210',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.7.152','100','Over current L2 started','7#0.0.99.98.4.255#2#211',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.7.153','100','Over current L2 stopped','7#0.0.99.98.4.255#2#212',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.8.152','100','Over current L3 started','7#0.0.99.98.4.255#2#213',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.8.153','100','Over current L3 stopped','7#0.0.99.98.4.255#2#214',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.6.285','100','Loss current L1 started','7#0.0.99.98.4.255#2#215',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.6.286','100','Loss current L1 stopped','7#0.0.99.98.4.255#2#216',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.7.285','100','Loss current L2 started','7#0.0.99.98.4.255#2#217',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.7.286','100','Loss current L2 stopped','7#0.0.99.98.4.255#2#218',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.8.285','100','Loss current L3 started','7#0.0.99.98.4.255#2#219',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.8.286','100','Loss current L3 stopped','7#0.0.99.98.4.255#2#220',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.26.285','100','Active Power Reverse L1 started','7#0.0.99.98.4.255#2#221',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.26.286','100','Active Power Reverse L1 stopped','7#0.0.99.98.4.255#2#222',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.27.285','100','Active Power Reverse L2 started','7#0.0.99.98.4.255#2#223',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.27.286','100','Active Power Reverse L2 stopped','7#0.0.99.98.4.255#2#224',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.28.285','100','Active Power Reverse L3 started','7#0.0.99.98.4.255#2#225',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.28.286','100','Active Power Reverse L3 stopped','7#0.0.99.98.4.255#2#226',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.26.152','100','Power Overload L1 started','7#0.0.99.98.4.255#2#227',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.26.153','100','Power Overload L1 stopped','7#0.0.99.98.4.255#2#228',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.27.152','100','Power Overload L2 started','7#0.0.99.98.4.255#2#229',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.27.153','100','Power Overload L2 stopped','7#0.0.99.98.4.255#2#230',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.28.152','100','Power Overload L3 started','7#0.0.99.98.4.255#2#231',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.28.153','100','Power Overload L3 stopped','7#0.0.99.98.4.255#2#232',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.33.98','100','Voltage unbalance started','7#0.0.99.98.4.255#2#235',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.33.99','100','Voltage unbalance stopped','7#0.0.99.98.4.255#2#236',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.131.152','100','Phase Failure L1 started','7#0.0.99.98.4.255#2#237',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.131.153','100','Phase Failure L1 stopped','7#0.0.99.98.4.255#2#238',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.132.152','100','Phase Failure L2 started','7#0.0.99.98.4.255#2#239',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.132.153','100','Phase Failure L2 stopped','7#0.0.99.98.4.255#2#240',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.133.152','100','Phase Failure L3 started','7#0.0.99.98.4.255#2#241',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.133.153','100','Phase Failure L3 stopped','7#0.0.99.98.4.255#2#242',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.27.219','100','Low Power Factor started','7#0.0.99.98.4.255#2#295',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.26.27.220','100','Low Power Factor stopped','7#0.0.99.98.4.255#2#296',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.1.1.219','100','High Temperature startted','7#0.0.99.98.6.255#2#263',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.1.1.220','100','High Temperature stopped','7#0.0.99.98.6.255#2#264',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.1.2.219','100','Module Cover removed','7#0.0.99.98.6.255#2#300',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.1.2.220','100','Module Cover closed','7#0.0.99.98.6.255#2#301',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.1.3.219','100','CT bypass startted','7#0.0.99.98.6.255#2#302',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.1.3.220','100','CT bypass stopped','7#0.0.99.98.6.255#2#303',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.1.4.219','100','Low frequency startted','7#0.0.99.98.6.255#2#304',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.1.4.220','100','Low frequency stopped','7#0.0.99.98.6.255#2#305',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.1.5.219','100','High frequency startted','7#0.0.99.98.6.255#2#306',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.1.5.220','100','High frequency stopped','7#0.0.99.98.6.255#2#307',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.1.1.8.0.0.0.0.0.0.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) [Unit: kW]','4#1.0.1.6.0.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.1.1.8.0.0.0.0.0.0.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) (Daily) [Unit: kW]','4#1.0.1.6.0.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.1.1.8.0.0.0.0.0.0.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) (Monthly) [Unit: kW]','4#1.0.1.6.0.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.1.1.8.0.0.0.0.0.1.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) occurred time','4#1.0.1.6.0.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.1.1.8.0.0.0.0.0.1.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) occurred time (Daily)','4#1.0.1.6.0.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.1.1.8.0.0.0.0.0.1.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) occurred time (Monthly)','4#1.0.1.6.0.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.1.1.8.0.0.0.0.1.0.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) rate 1 [Unit: kW]','4#1.0.1.6.1.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.1.1.8.0.0.0.0.1.0.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) rate 1 (Daily) [Unit: kW]','4#1.0.1.6.1.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.1.1.8.0.0.0.0.1.0.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) rate 1 (Monthly) [Unit: kW]','4#1.0.1.6.1.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.1.1.8.0.0.0.0.1.1.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) rate 1 occurred time','4#1.0.1.6.1.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.1.1.8.0.0.0.0.1.1.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) rate 1 occurred time (Daily)','4#1.0.1.6.1.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.1.1.8.0.0.0.0.1.1.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) rate 1 occurred time (Monthly)','4#1.0.1.6.1.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.1.1.8.0.0.0.0.2.0.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) rate 2 [Unit: kW]','4#1.0.1.6.2.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.1.1.8.0.0.0.0.2.0.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) rate 2 (Daily) [Unit: kW]','4#1.0.1.6.2.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.1.1.8.0.0.0.0.2.0.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) rate 2 (Monthly) [Unit: kW]','4#1.0.1.6.2.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.1.1.8.0.0.0.0.2.1.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) rate 2 occurred time','4#1.0.1.6.2.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.1.1.8.0.0.0.0.2.1.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) rate 2 occurred time (Daily)','4#1.0.1.6.2.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.1.1.8.0.0.0.0.2.1.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) rate 2 occurred time (Monthly)','4#1.0.1.6.2.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.1.1.8.0.0.0.0.3.0.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) rate 3 [Unit: kW]','4#1.0.1.6.3.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.1.1.8.0.0.0.0.3.0.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) rate 3 (Daily) [Unit: kW]','4#1.0.1.6.3.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.1.1.8.0.0.0.0.3.0.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) rate 3 (Monthly) [Unit: kW]','4#1.0.1.6.3.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.1.1.8.0.0.0.0.3.1.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) rate 3 occurred time','4#1.0.1.6.3.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.1.1.8.0.0.0.0.3.1.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) rate 3 occurred time (Daily)','4#1.0.1.6.3.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.1.1.8.0.0.0.0.3.1.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) rate 3 occurred time (Monthly)','4#1.0.1.6.3.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.1.1.8.0.0.0.0.4.0.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) rate 4 [Unit: kW]','4#1.0.1.6.4.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.1.1.8.0.0.0.0.4.0.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) rate 4 (Daily) [Unit: kW]','4#1.0.1.6.4.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.1.1.8.0.0.0.0.4.0.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) rate 4 (Monthly) [Unit: kW]','4#1.0.1.6.4.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.1.1.8.0.0.0.0.4.1.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) rate 4 occurred time','4#1.0.1.6.4.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.1.1.8.0.0.0.0.4.1.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) rate 4 occurred time (Daily)','4#1.0.1.6.4.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.1.1.8.0.0.0.0.4.1.0.224.3.38.0','100','Maximum Demand Register 1 -Active energy import(+A) rate 4 occurred time (Monthly)','4#1.0.1.6.4.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.19.1.8.0.0.0.0.0.0.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) [Unit: kW]','4#1.0.2.6.0.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.19.1.8.0.0.0.0.0.0.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) (Daily) [Unit: kW]','4#1.0.2.6.0.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.19.1.8.0.0.0.0.0.0.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) (Monthly) [Unit: kW]','4#1.0.2.6.0.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.19.1.8.0.0.0.0.0.1.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) occurred time','4#1.0.2.6.0.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.19.1.8.0.0.0.0.0.1.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) occurred time (Daily)','4#1.0.2.6.0.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.19.1.8.0.0.0.0.0.1.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) occurred time (Monthly)','4#1.0.2.6.0.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.19.1.8.0.0.0.0.1.0.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) rate 1 [Unit: kW]','4#1.0.2.6.1.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.19.1.8.0.0.0.0.1.0.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) rate 1 (Daily) [Unit: kW]','4#1.0.2.6.1.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.19.1.8.0.0.0.0.1.0.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) rate 1 (Monthly) [Unit: kW]','4#1.0.2.6.1.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.19.1.8.0.0.0.0.1.1.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) rate 1 occurred time','4#1.0.2.6.1.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.19.1.8.0.0.0.0.1.1.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) rate 1 occurred time (Daily)','4#1.0.2.6.1.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.19.1.8.0.0.0.0.1.1.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) rate 1 occurred time (Monthly)','4#1.0.2.6.1.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.19.1.8.0.0.0.0.2.0.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) rate 2 [Unit: kW]','4#1.0.2.6.2.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.19.1.8.0.0.0.0.2.0.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) rate 2 (Daily) [Unit: kW]','4#1.0.2.6.2.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.19.1.8.0.0.0.0.2.0.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) rate 2 (Monthly) [Unit: kW]','4#1.0.2.6.2.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.19.1.8.0.0.0.0.2.1.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) rate 2 occurred time','4#1.0.2.6.2.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.19.1.8.0.0.0.0.2.1.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) rate 2 occurred time (Daily)','4#1.0.2.6.2.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.19.1.8.0.0.0.0.2.1.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) rate 2 occurred time (Monthly)','4#1.0.2.6.2.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.19.1.8.0.0.0.0.3.0.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) rate 3 [Unit: kW]','4#1.0.2.6.3.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.19.1.8.0.0.0.0.3.0.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) rate 3 (Daily) [Unit: kW]','4#1.0.2.6.3.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.19.1.8.0.0.0.0.3.0.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) rate 3 (Monthly) [Unit: kW]','4#1.0.2.6.3.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.19.1.8.0.0.0.0.3.1.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) rate 3 occurred time','4#1.0.2.6.3.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.19.1.8.0.0.0.0.3.1.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) rate 3 occurred time (Daily)','4#1.0.2.6.3.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.19.1.8.0.0.0.0.3.1.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) rate 3 occurred time (Monthly)','4#1.0.2.6.3.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.19.1.8.0.0.0.0.4.0.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) rate 4 [Unit: kW]','4#1.0.2.6.4.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.19.1.8.0.0.0.0.4.0.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) rate 4 (Daily) [Unit: kW]','4#1.0.2.6.4.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.19.1.8.0.0.0.0.4.0.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) rate 4 (Monthly) [Unit: kW]','4#1.0.2.6.4.255#2','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.19.1.8.0.0.0.0.4.1.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) rate 4 occurred time','4#1.0.2.6.4.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.19.1.8.0.0.0.0.4.1.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) rate 4 occurred time (Daily)','4#1.0.2.6.4.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.19.1.8.0.0.0.0.4.1.0.224.3.38.0','100','Maximum Demand Register 6 -Active energy export(-A) rate 4 occurred time (Monthly)','4#1.0.2.6.4.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.1.1.8.0.0.0.0.0.0.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) [Unit: kVar]','4#1.0.3.6.0.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.1.1.8.0.0.0.0.0.0.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) (Daily) [Unit: kVar]','4#1.0.3.6.0.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.1.1.8.0.0.0.0.0.0.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) (Monthly) [Unit: kVar]','4#1.0.3.6.0.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.1.1.8.0.0.0.0.0.1.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) occurred time','4#1.0.3.6.0.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.1.1.8.0.0.0.0.0.1.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) occurred time (Daily)','4#1.0.3.6.0.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.1.1.8.0.0.0.0.0.1.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) occurred time (Monthly)','4#1.0.3.6.0.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.1.1.8.0.0.0.0.1.0.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) rate 1 [Unit: kVar]','4#1.0.3.6.1.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.1.1.8.0.0.0.0.1.0.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) rate 1 (Daily) [Unit: kVar]','4#1.0.3.6.1.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.1.1.8.0.0.0.0.1.0.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) rate 1 (Monthly) [Unit: kVar]','4#1.0.3.6.1.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.1.1.8.0.0.0.0.1.1.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) rate 1 occurred time','4#1.0.3.6.1.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.1.1.8.0.0.0.0.1.1.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) rate 1 occurred time (Daily)','4#1.0.3.6.1.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.1.1.8.0.0.0.0.1.1.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) rate 1 occurred time (Monthly)','4#1.0.3.6.1.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.1.1.8.0.0.0.0.2.0.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) rate 2 [Unit: kVar]','4#1.0.3.6.2.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.1.1.8.0.0.0.0.2.0.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) rate 2 (Daily) [Unit: kVar]','4#1.0.3.6.2.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.1.1.8.0.0.0.0.2.0.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) rate 2 (Monthly) [Unit: kVar]','4#1.0.3.6.2.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.1.1.8.0.0.0.0.2.1.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) rate 2 occurred time','4#1.0.3.6.2.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.1.1.8.0.0.0.0.2.1.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) rate 2 occurred time (Daily)','4#1.0.3.6.2.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.1.1.8.0.0.0.0.2.1.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) rate 2 occurred time (Monthly)','4#1.0.3.6.2.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.1.1.8.0.0.0.0.3.0.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) rate 3 [Unit: kVar]','4#1.0.3.6.3.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.1.1.8.0.0.0.0.3.0.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) rate 3 (Daily) [Unit: kVar]','4#1.0.3.6.3.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.1.1.8.0.0.0.0.3.0.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) rate 3 (Monthly) [Unit: kVar]','4#1.0.3.6.3.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.1.1.8.0.0.0.0.3.1.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) rate 3 occurred time','4#1.0.3.6.3.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.1.1.8.0.0.0.0.3.1.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) rate 3 occurred time (Daily)','4#1.0.3.6.3.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.1.1.8.0.0.0.0.3.1.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) rate 3 occurred time (Monthly)','4#1.0.3.6.3.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.1.1.8.0.0.0.0.4.0.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) rate 4 [Unit: kVar]','4#1.0.3.6.4.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.1.1.8.0.0.0.0.4.0.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) rate 4 (Daily) [Unit: kVar]','4#1.0.3.6.4.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.1.1.8.0.0.0.0.4.0.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) rate 4 (Monthly) [Unit: kVar]','4#1.0.3.6.4.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.1.1.8.0.0.0.0.4.1.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) rate 4 occurred time','4#1.0.3.6.4.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.1.1.8.0.0.0.0.4.1.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) rate 4 occurred time (Daily)','4#1.0.3.6.4.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.1.1.8.0.0.0.0.4.1.0.224.3.63.0','100','Maximum Demand Register 11 -Reactive energy import (+R) rate 4 occurred time (Monthly)','4#1.0.3.6.4.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.19.1.8.0.0.0.0.0.0.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) [Unit: kVar]','4#1.0.4.6.0.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.19.1.8.0.0.0.0.0.0.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) (Daily) [Unit: kVar]','4#1.0.4.6.0.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.19.1.8.0.0.0.0.0.0.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) (Monthly) [Unit: kVar]','4#1.0.4.6.0.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.19.1.8.0.0.0.0.0.1.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) occurred time','4#1.0.4.6.0.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.19.1.8.0.0.0.0.0.1.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) occurred time (Daily)','4#1.0.4.6.0.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.19.1.8.0.0.0.0.0.1.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) occurred time (Monthly)','4#1.0.4.6.0.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.19.1.8.0.0.0.0.1.0.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) rate 1 [Unit: kVar]','4#1.0.4.6.1.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.19.1.8.0.0.0.0.1.0.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) rate 1 (Daily) [Unit: kVar]','4#1.0.4.6.1.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.19.1.8.0.0.0.0.1.0.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) rate 1 (Monthly) [Unit: kVar]','4#1.0.4.6.1.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.19.1.8.0.0.0.0.1.1.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) rate 1 occurred time','4#1.0.4.6.1.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.19.1.8.0.0.0.0.1.1.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) rate 1 occurred time (Daily)','4#1.0.4.6.1.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.19.1.8.0.0.0.0.1.1.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) rate 1 occurred time (Monthly)','4#1.0.4.6.1.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.19.1.8.0.0.0.0.2.0.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) rate 2 [Unit: kVar]','4#1.0.4.6.2.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.19.1.8.0.0.0.0.2.0.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) rate 2 (Daily) [Unit: kVar]','4#1.0.4.6.2.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.19.1.8.0.0.0.0.2.0.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) rate 2 (Monthly) [Unit: kVar]','4#1.0.4.6.2.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.19.1.8.0.0.0.0.2.1.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) rate 2 occurred time','4#1.0.4.6.2.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.19.1.8.0.0.0.0.2.1.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) rate 2 occurred time (Daily)','4#1.0.4.6.2.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.19.1.8.0.0.0.0.2.1.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) rate 2 occurred time (Monthly)','4#1.0.4.6.2.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.19.1.8.0.0.0.0.3.0.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) rate 3 [Unit: kVar]','4#1.0.4.6.3.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.19.1.8.0.0.0.0.3.0.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) rate 3 (Daily) [Unit: kVar]','4#1.0.4.6.3.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.19.1.8.0.0.0.0.3.0.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) rate 3 (Monthly) [Unit: kVar]','4#1.0.4.6.3.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.19.1.8.0.0.0.0.3.1.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) rate 3 occurred time','4#1.0.4.6.3.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.19.1.8.0.0.0.0.3.1.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) rate 3 occurred time (Daily)','4#1.0.4.6.3.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.19.1.8.0.0.0.0.3.1.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) rate 3 occurred time (Monthly)','4#1.0.4.6.3.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.19.1.8.0.0.0.0.4.0.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) rate 4 [Unit: kVar]','4#1.0.4.6.4.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.19.1.8.0.0.0.0.4.0.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) rate 4 (Daily) [Unit: kVar]','4#1.0.4.6.4.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.19.1.8.0.0.0.0.4.0.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) rate 4 (Monthly) [Unit: kVar]','4#1.0.4.6.4.255#2','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.8.0.6.19.1.8.0.0.0.0.4.1.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) rate 4 occurred time','4#1.0.4.6.4.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.8.0.6.19.1.8.0.0.0.0.4.1.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) rate 4 occurred time (Daily)','4#1.0.4.6.4.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.8.0.6.19.1.8.0.0.0.0.4.1.0.224.3.63.0','100','Maximum Demand Register 16 -Reactive energy export (-R) rate 4 occurred time (Monthly)','4#1.0.4.6.4.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.0.73.6.1.1.8.0.0.0.0.0.0.0.224.3.38.0','100','Import active demand [Unit:kW]','5#1.0.1.4.0.255#3','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.0.73.6.19.1.8.0.0.0.0.0.0.0.224.3.38.0','100','Export active demand [Unit:kW]','5#1.0.2.4.0.255#3','W','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.0.73.6.1.1.8.0.0.0.0.0.0.0.224.3.63.0','100','Import reactive demand [Unit:kVar]','5#1.0.3.4.0.255#3','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.0.73.6.19.1.8.0.0.0.0.0.0.0.224.3.63.0','100','Export reactive demand [Unit:kVar]','5#1.0.4.4.0.255#3','Var','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('6.36.0.0','100','Clock','8#0.0.1.0.0.255#2',null,'RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('6.36.0.1','100','Profile status 1','1#0.0.96.10.1.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('41.96.11.0','100','Event code of standard  event log','1#0.0.96.11.0.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('41.96.11.1','100','Event code of fraud event log','1#0.0.96.11.1.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('41.96.11.2','100','Event code of relay control event log','1#0.0.96.11.2.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('41.96.11.4','100','Event code of power quality event log','1#0.0.96.11.4.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('41.96.11.5','100','Event code of communication event log','1#0.0.96.11.5.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('41.96.11.6','100','Event code of self define event log','1#0.0.96.11.6.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('39.0.0.3','100','Logic Device Name','1#0.0.42.0.0.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('39.0.0.1','100','Meter Serial Number','1#0.0.96.1.0.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('39.0.0.4','100','Firmware Version','1#1.0.0.2.0.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('39.0.0.5','100','Clock','8#0.0.1.0.0.255#2',null,'RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('39.0.0.2','100','Manufactory Identifier','1#0.0.96.1.1.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.9.1.12.0.0.0.0.0.0.0.224.3.73.0','100','Apparent energy import (+E) [Unit: kVah]','3#1.0.9.8.0.255#2','Vah','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.26.0.1.10.1.12.0.0.0.0.0.0.0.224.3.73.0','100','Apparent energy export (-E) [Unit: kVah]','3#1.0.10.8.0.255#2','Vah','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.0.0.12.9.1.37.0.0.0.0.0.0.0.224.3.38.0','100','Instantaneous apparent import power [Unit: kVa]','3#1.0.9.7.0.255#2','Va','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('0.0.0.12.10.1.37.0.0.0.0.0.0.0.224.3.38.0','100','Instantaneous apparent export power [Unit: kVa]','3#1.0.10.7.0.255#2','Va','R',0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('39.0.0.6','100','Relay Control State','70#0.0.96.3.10.255#3',null,'RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('39.0.0.7','100','Relay Control Mode','70#0.0.96.3.10.255#4',null,'RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.7.75.25','100','External alert detected','7#0.0.99.98.0.255#2#20',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.40.0.1','100','Meter data cleared','7#0.0.99.98.0.255#2#253',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.40.0.2','100','Load profile cleared','7#0.0.99.98.0.255#2#254',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.40.0.3','100','Event log cleared','7#0.0.99.98.0.255#2#255',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.40.0.4','100','Event log cleared','7#0.0.99.98.0.255#2#256',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.12.38.1','100','Relay attack','7#0.0.99.98.1.255#2#50',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.41.0.1','100','Event log cleared','7#0.0.99.98.1.255#2#255',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.42.0.1','100','Event log cleared','7#0.0.99.98.2.255#2#255',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.41.4.1','100','Event log cleared','7#0.0.99.98.4.255#2#255',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.19.92.85','100','PDP context established','7#0.0.99.98.5.255#2#146',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.19.93.85','100','PDP context destoryed','7#0.0.99.98.5.255#2#147',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.19.94.85','100','PDP context failure','7#0.0.99.98.5.255#2#148',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.19.0.215','100','Modem HW Reset','7#0.0.99.98.5.255#2#150',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.19.0.72','100','Diagnostic failure','7#0.0.99.98.5.255#2#154',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.23.0.48','100','Auto answer number of calls exceeded','7#0.0.99.98.5.255#2#157',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('3.23.0.49','100','Local communication attempt','7#0.0.99.98.5.255#2#158',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.0.1','100','Currently active step','1#0.0.96.14.1.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.0.2','100','Currently active price','1#0.0.96.14.128.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.0.3','100','Max credit limit','1#0.0.19.50.1.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.0.4','100','Max vend limit','1#0.0.19.50.2.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.0.5','100','Key type','1#0.0.19.129.0.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.0.6','100','Key Revision number','1#0.0.19.129.1.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.0.7','100','Tariff index','1#0.0.19.129.2.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.0.8','100','Key expiry number','1#0.0.19.129.3.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.0.9','100','Supply group code','1#0.0.19.129.4.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.0.10','100','STS software versopm','1#0.0.19.129.5.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.0.11','100','Current time purchase credit','1#0.0.19.130.0.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.0.12','100','Cumulative purchase credit','1#0.0.19.130.2.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.0.13','100','Residual credit use days','1#0.0.19.130.3.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.0.14','100','Cumulative consume credit','1#0.0.19.131.0.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.0.15','100','Current month consume credit','1#0.0.19.133.0.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.0.16','100','Current day consume credit','1#0.0.19.134.0.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.0.17','100','Date of passive step active','1#0.0.96.54.0.255#2',null,'RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.1.1','100','Account mode and status','111#0.0.19.0.0.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.1.2','100','Current credit in use','111#0.0.19.0.0.255#3',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.1.3','100','Current credit status','111#0.0.19.0.0.255#4',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.1.4','100','Available credit','111#0.0.19.0.0.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.1.5','100','Amount to clear','111#0.0.19.0.0.255#6',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.1.6','100','Clearance threshold','111#0.0.19.0.0.255#7',null,'RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.1.7','100','Aggregated debt','111#0.0.19.0.0.255#8',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.1.8','100','Credit reference list','111#0.0.19.0.0.255#9',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.1.9','100','Charge reference list','111#0.0.19.0.0.255#10',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.1.10','100','Credit charge configuration','111#0.0.19.0.0.255#11',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.1.11','100','Token gateway configuration','111#0.0.19.0.0.255#12',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.1.12','100','Account activation time','111#0.0.19.0.0.255#13',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.1.13','100','Account closure time','111#0.0.19.0.0.255#14',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.1.14','100','Currency','111#0.0.19.0.0.255#15',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.1.15','100','Low credit threshold','111#0.0.19.0.0.255#16',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.1.16','100','Next credit available threshold','111#0.0.19.0.0.255#17',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.1.17','100','Max provision','111#0.0.19.0.0.255#18',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.1.18','100','Max provision period','111#0.0.19.0.0.255#19',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.1.19','100','Activate account ','111#0.0.19.0.0.255#1',null,'A',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.1.20','100','Close account','111#0.0.19.0.0.255#2',null,'A',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.1.21','100','Reset account','111#0.0.19.0.0.255#3',null,'A',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.2.1','100','Total amount paid','113#0.0.19.20.0.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.2.2','100','Charge type','113#0.0.19.20.0.255#3',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.2.3','100','Priority','113#0.0.19.20.0.255#4',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.2.4','100','Unit charge active','113#0.0.19.20.0.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.2.5','100','Unit charge passive','113#0.0.19.20.0.255#6',null,'RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.2.6','100','Unit charge activation time','113#0.0.19.20.0.255#7',null,'RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.2.7','100','Period','113#0.0.19.20.0.255#8',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.2.8','100','Charge configuration','113#0.0.19.20.0.255#9',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.2.9','100','Last collection time','113#0.0.19.20.0.255#10',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.2.10','100','Last collection amount','113#0.0.19.20.0.255#11',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.2.11','100','Total amount remaining','113#0.0.19.20.0.255#12',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.2.12','100','Proporition','113#0.0.19.20.0.255#13',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.3.1','100','Step tariff','21#0.0.16.0.2.255#2',null,'RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.3.2','100','Step tariff activation time','1#0.0.96.54.0.255#2',null,'RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('15.13.112.78','100','Token','115#0.0.19.40.0.255#1',null,'A',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.4.1','100','Current credit amount','112#0.0.19.10.0.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.4.2','100','Credit type','112#0.0.19.10.0.255#3',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.4.3','100','Priority','112#0.0.19.10.0.255#4',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.4.4','100','Warning threshold','112#0.0.19.10.0.255#5',null,'RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.4.5','100','Limit','112#0.0.19.10.0.255#6',null,'RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.4.6','100','Credit configuration','112#0.0.19.10.0.255#7',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.4.7','100','Credit status','112#0.0.19.10.0.255#8',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.4.8','100','Preset credit amount','112#0.0.19.10.0.255#9',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.4.9','100','Credit avaliable threshold','112#0.0.19.10.0.255#10',null,'RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.4.10','100','Period','112#0.0.19.10.0.255#11',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.4.11','100','Update amount','112#0.0.19.10.0.255#1',null,'A',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.4.12','100','Set amount to value','112#0.0.19.10.0.255#2',null,'A',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.4.13','100','Invoke credit','112#0.0.19.10.0.255#3',null,'A',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.5.1','100','Current credit amount','112#0.0.19.10.1.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.5.2','100','Credit type','112#0.0.19.10.1.255#3',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.5.3','100','Priority','112#0.0.19.10.1.255#4',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.5.4','100','Warning threshold','112#0.0.19.10.1.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.5.5','100','Limit','112#0.0.19.10.1.255#6',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.5.6','100','Credit configuration','112#0.0.19.10.1.255#7',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.5.7','100','Credit status','112#0.0.19.10.1.255#8',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.5.8','100','Preset credit amount','112#0.0.19.10.1.255#9',null,'RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.5.9','100','Credit avaliable threshold','112#0.0.19.10.1.255#10',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.5.10','100','Period','112#0.0.19.10.1.255#11',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.5.11','100','Update amount','112#0.0.19.10.1.255#1',null,'A',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.5.12','100','Set amount to value','112#0.0.19.10.1.255#2',null,'A',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.5.13','100','Invoke credit','112#0.0.19.10.1.255#3',null,'A',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.6.1','100','Current credit amount','112#0.0.19.10.2.255#2',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.6.2','100','Credit type','112#0.0.19.10.2.255#3',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.6.3','100','Priority','112#0.0.19.10.2.255#4',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.6.4','100','Warning threshold','112#0.0.19.10.2.255#5',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.6.5','100','Limit','112#0.0.19.10.2.255#6',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.6.6','100','Credit configuration','112#0.0.19.10.2.255#7',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.6.7','100','Credit status','112#0.0.19.10.2.255#8',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.6.8','100','Preset credit amount','112#0.0.19.10.2.255#9',null,'RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.6.9','100','Credit avaliable threshold','112#0.0.19.10.2.255#10',null,'R',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.6.10','100','Period','112#0.0.19.10.2.255#11',null,'RW',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.6.11','100','Update amount','112#0.0.19.10.2.255#1',null,'A',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.6.12','100','Set amount to value','112#0.0.19.10.2.255#2',null,'A',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('40.0.6.13','100','Invoke credit','112#0.0.19.10.2.255#3',null,'A',1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('42.0.0.0','100','Meter Configuration Plan',null,null,null,1);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.0.2.4.1.1.12.0.0.0.0.0.0.0.224.3.72.0','100','Active energy import (+A) (interval) (Daily) [Unit: kWh]','3#1.0.1.29.0.255#2','kWh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.0.2.4.1.1.12.0.0.0.0.1.0.0.224.3.72.0','100','Active energy import (+A) rate1 (interval) (Daily) [Unit: kWh]','3#1.0.1.29.1.255#2','kWh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.0.2.4.1.1.12.0.0.0.0.2.0.0.224.3.72.0','100','Active energy import (+A) rate2 (interval) (Daily) [Unit: kWh]','3#1.0.1.29.2.255#2','kWh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.0.2.4.1.1.12.0.0.0.0.3.0.0.224.3.72.0','100','Active energy import (+A) rate3 (interval) (Daily) [Unit: kWh]','3#1.0.1.29.3.255#2','kWh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.0.2.4.1.1.12.0.0.0.0.4.0.0.224.3.72.0','100','Active energy import (+A) rate4 (interval) (Daily) [Unit: kWh]','3#1.0.1.29.4.255#2','kWh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.0.2.4.19.1.12.0.0.0.0.0.0.0.224.3.72.0','100','Active energy export (-A) (interval) (Daily) [Unit: kWh]','3#1.0.2.29.0.255#2','kWh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.0.2.4.19.1.12.0.0.0.0.1.0.0.224.3.72.0','100','Active energy export (-A) rate1 (interval) (Daily) [Unit: kWh]','3#1.0.2.29.1.255#2','kWh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.0.2.4.19.1.12.0.0.0.0.2.0.0.224.3.72.0','100','Active energy export (-A) rate2 (interval) (Daily) [Unit: kWh]','3#1.0.2.29.2.255#2','kWh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.0.2.4.19.1.12.0.0.0.0.3.0.0.224.3.72.0','100','Active energy export (-A) rate3 (interval) (Daily) [Unit: kWh]','3#1.0.2.29.3.255#2','kWh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.0.2.4.19.1.12.0.0.0.0.4.0.0.224.3.72.0','100','Active energy export (-A) rate4 (interval) (Daily) [Unit: kWh]','3#1.0.2.29.4.255#2','kWh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.0.2.4.5.1.12.0.0.0.0.0.0.0.224.3.73.0','100','Reactive energy import (+R)(QI+QII) (interval) (Daily) [Unit: kVarh]','3#1.0.3.29.0.255#2','kVarh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.0.2.4.5.1.12.0.0.0.0.1.0.0.224.3.73.0','100','Reactive energy import (+R)(QI+QII) rate1 (interval) (Daily) [Unit: kVarh]','3#1.0.3.29.1.255#2','kVarh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.0.2.4.5.1.12.0.0.0.0.2.0.0.224.3.73.0','100','Reactive energy import (+R)(QI+QII) rate2 (interval) (Daily) [Unit: kVarh]','3#1.0.3.29.2.255#2','kVarh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.0.2.4.5.1.12.0.0.0.0.3.0.0.224.3.73.0','100','Reactive energy import (+R)(QI+QII) rate3 (interval) (Daily) [Unit: kVarh]','3#1.0.3.29.3.255#2','kVarh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.0.2.4.5.1.12.0.0.0.0.4.0.0.224.3.73.0','100','Reactive energy import (+R)(QI+QII) rate4 (interval) (Daily) [Unit: kVarh]','3#1.0.3.29.4.255#2','kVarh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.0.2.4.13.1.12.0.0.0.0.0.0.0.224.3.73.0','100','Reactive energy export (-R)(QIII+QIV) (interval) (Daily) [Unit: kVarh]','3#1.0.4.29.0.255#2','kVarh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.0.2.4.13.1.12.0.0.0.0.1.0.0.224.3.73.0','100','Reactive energy export (-R)(QIII+QIV) rate1 (interval) (Daily) [Unit: kVarh]','3#1.0.4.29.1.255#2','kVarh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.0.2.4.13.1.12.0.0.0.0.2.0.0.224.3.73.0','100','Reactive energy export (-R)(QIII+QIV) rate2 (interval) (Daily) [Unit: kVarh]','3#1.0.4.29.2.255#2','kVarh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.0.2.4.13.1.12.0.0.0.0.3.0.0.224.3.73.0','100','Reactive energy export (-R)(QIII+QIV) rate3 (interval) (Daily) [Unit: kVarh]','3#1.0.4.29.3.255#2','kVarh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('11.0.2.4.13.1.12.0.0.0.0.4.0.0.224.3.73.0','100','Reactive energy export (-R)(QIII+QIV) rate4 (interval) (Daily) [Unit: kVarh]','3#1.0.4.29.4.255#2','kVarh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.0.2.4.1.1.12.0.0.0.0.0.0.0.224.3.72.0','100','Active energy import (+A) (interval) (Monthly) [Unit: kWh]','3#1.0.1.29.0.255#2','kWh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.0.2.4.1.1.12.0.0.0.0.1.0.0.224.3.72.0','100','Active energy import (+A) (interval) rate1 (Monthly) [Unit: kWh]','3#1.0.1.29.1.255#2','kWh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.0.2.4.1.1.12.0.0.0.0.2.0.0.224.3.72.0','100','Active energy import (+A) (interval) rate2 (Monthly) [Unit: kWh]','3#1.0.1.29.2.255#2','kWh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.0.2.4.1.1.12.0.0.0.0.3.0.0.224.3.72.0','100','Active energy import (+A) (interval) rate3 (Monthly) [Unit: kWh]','3#1.0.1.29.3.255#2','kWh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.0.2.4.1.1.12.0.0.0.0.4.0.0.224.3.72.0','100','Active energy import (+A) (interval) rate4 (Monthly) [Unit: kWh]','3#1.0.1.29.4.255#2','kWh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.0.2.4.19.1.12.0.0.0.0.0.0.0.224.3.72.0','100','Active energy export (-A) (interval) (Monthly) [Unit: kWh]','3#1.0.2.29.0.255#2','kWh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.0.2.4.19.1.12.0.0.0.0.1.0.0.224.3.72.0','100','Active energy export (-A) (interval) rate1 (Monthly) [Unit: kWh]','3#1.0.2.29.1.255#2','kWh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.0.2.4.19.1.12.0.0.0.0.2.0.0.224.3.72.0','100','Active energy export (-A) (interval) rate2 (Monthly) [Unit: kWh]','3#1.0.2.29.2.255#2','kWh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.0.2.4.19.1.12.0.0.0.0.3.0.0.224.3.72.0','100','Active energy export (-A) (interval) rate3 (Monthly) [Unit: kWh]','3#1.0.2.29.3.255#2','kWh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.0.2.4.19.1.12.0.0.0.0.4.0.0.224.3.72.0','100','Active energy export (-A) (interval) rate4 (Monthly) [Unit: kWh]','3#1.0.2.29.4.255#2','kWh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.0.2.4.5.1.12.0.0.0.0.0.0.0.224.3.73.0','100','Reactive energy import (+R)(QI+QII) (interval) (Monthly) [Unit: kVarh]','3#1.0.3.29.0.255#2','kVarh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.0.2.4.5.1.12.0.0.0.0.1.0.0.224.3.73.0','100','Reactive energy import (+R)(QI+QII) rate1 (interval) (Monthly) [Unit: kVarh]','3#1.0.3.29.1.255#2','kVarh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.0.2.4.5.1.12.0.0.0.0.2.0.0.224.3.73.0','100','Reactive energy import (+R)(QI+QII) rate2 (interval) (Monthly) [Unit: kVarh]','3#1.0.3.29.2.255#2','kVarh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.0.2.4.5.1.12.0.0.0.0.3.0.0.224.3.73.0','100','Reactive energy import (+R)(QI+QII) rate3 (interval) (Monthly) [Unit: kVarh]','3#1.0.3.29.3.255#2','kVarh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.0.2.4.5.1.12.0.0.0.0.4.0.0.224.3.73.0','100','Reactive energy import (+R)(QI+QII) rate4 (interval) (Monthly) [Unit: kVarh]','3#1.0.3.29.4.255#2','kVarh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.0.2.4.13.1.12.0.0.0.0.0.0.0.224.3.73.0','100','Reactive energy export (-R)(QIII+QIV) (interval) (Monthly) [Unit: kVarh]','3#1.0.4.29.0.255#2','kVarh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.0.2.4.13.1.12.0.0.0.0.1.0.0.224.3.73.0','100','Reactive energy export (-R)(QIII+QIV) rate1 (interval) (Monthly) [Unit: kVarh]','3#1.0.4.29.1.255#2','kVarh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.0.2.4.13.1.12.0.0.0.0.2.0.0.224.3.73.0','100','Reactive energy export (-R)(QIII+QIV) rate2 (interval) (Monthly) [Unit: kVarh]','3#1.0.4.29.2.255#2','kVarh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.0.2.4.13.1.12.0.0.0.0.3.0.0.224.3.73.0','100','Reactive energy export (-R)(QIII+QIV) rate3 (interval) (Monthly) [Unit: kVarh]','3#1.0.4.29.3.255#2','kVarh',null,0.001);
insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT) values ('13.0.2.4.13.1.12.0.0.0.0.4.0.0.224.3.73.0','100','Reactive energy export (-R)(QIII+QIV) rate4 (interval) (Monthly) [Unit: kVarh]','3#1.0.4.29.4.255#2','kVarh',null,0.001);

commit;
/*-============================================-*/
/*----Protocol data item group --*/
/*============================================*/
insert into DICT_DATAITEM_GROUP (ID,NAME,APP_TYPE,PROTOCOL_ID,SORT_ID) values ('1003005','Prepaid','3','100',4);
insert into DICT_DATAITEM_GROUP (ID,NAME,APP_TYPE,PROTOCOL_ID,SORT_ID) values ('1002006','Self Define Log','2','100',6);
insert into DICT_DATAITEM_GROUP (ID,NAME,APP_TYPE,PROTOCOL_ID,SORT_ID) values ('1001012','Load Profile Daily','1','100',4);
insert into DICT_DATAITEM_GROUP (ID,NAME,APP_TYPE,PROTOCOL_ID,SORT_ID) values ('1003004','Other','3','100',5);
insert into DICT_DATAITEM_GROUP (ID,NAME,APP_TYPE,PROTOCOL_ID,SORT_ID) values ('1001009','Load Profile Minutely','1','100',1);
insert into DICT_DATAITEM_GROUP (ID,NAME,APP_TYPE,PROTOCOL_ID,SORT_ID) values ('1001010','Instantaneous Profile Minutely','1','100',2);
insert into DICT_DATAITEM_GROUP (ID,NAME,APP_TYPE,PROTOCOL_ID,SORT_ID) values ('1004003','Step Traiff','4','100',3);
insert into DICT_DATAITEM_GROUP (ID,NAME,APP_TYPE,PROTOCOL_ID,SORT_ID) values ('1001015','Interval Data Daily','1','100',6);
insert into DICT_DATAITEM_GROUP (ID,NAME,APP_TYPE,PROTOCOL_ID,SORT_ID) values ('1001016','Interval Data Monthly','1','100',7);
insert into DICT_DATAITEM_GROUP (ID,NAME,APP_TYPE,PROTOCOL_ID,SORT_ID) values ('1003001','Energy','3','100',1);
insert into DICT_DATAITEM_GROUP (ID,NAME,APP_TYPE,PROTOCOL_ID,SORT_ID) values ('1003002','Current Demand','3','100',3);
insert into DICT_DATAITEM_GROUP (ID,NAME,APP_TYPE,PROTOCOL_ID,SORT_ID) values ('1003003','Instantaneous','3','100',2);
insert into DICT_DATAITEM_GROUP (ID,NAME,APP_TYPE,PROTOCOL_ID,SORT_ID) values ('1001011','Demand Profile Minutely','1','100',3);
insert into DICT_DATAITEM_GROUP (ID,NAME,APP_TYPE,PROTOCOL_ID,SORT_ID) values ('1002001','Standard Event Log','2','100',1);
insert into DICT_DATAITEM_GROUP (ID,NAME,APP_TYPE,PROTOCOL_ID,SORT_ID) values ('1002002','Fraud Event Log','2','100',5);
insert into DICT_DATAITEM_GROUP (ID,NAME,APP_TYPE,PROTOCOL_ID,SORT_ID) values ('1002003','Relay Control Log','2','100',2);
insert into DICT_DATAITEM_GROUP (ID,NAME,APP_TYPE,PROTOCOL_ID,SORT_ID) values ('1002004','Power Quality Log','2','100',3);
insert into DICT_DATAITEM_GROUP (ID,NAME,APP_TYPE,PROTOCOL_ID,SORT_ID) values ('1002005','Communication Log','2','100',4);
insert into DICT_DATAITEM_GROUP (ID,NAME,APP_TYPE,PROTOCOL_ID,SORT_ID) values ('1004001','TOU','4','100',1);
insert into DICT_DATAITEM_GROUP (ID,NAME,APP_TYPE,PROTOCOL_ID,SORT_ID) values ('1004002','Limiter','4','100',2);
insert into DICT_DATAITEM_GROUP (ID,NAME,APP_TYPE,PROTOCOL_ID,SORT_ID) values ('1001013','Demand Billing Profile Monthly','1','100',6);
insert into DICT_DATAITEM_GROUP (ID,NAME,APP_TYPE,PROTOCOL_ID,SORT_ID) values ('1001014','Energy Billing Profile Monthly','1','100',5);
commit;
/*-============================================-*/
/*----Protocol and data item map--*/
/*============================================*/
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.19.1.8.0.0.0.0.4.0.0.224.3.38.0',19);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.19.1.8.0.0.0.0.0.1.0.224.3.38.0',12);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.19.1.8.0.0.0.0.1.1.0.224.3.38.0',14);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.19.1.8.0.0.0.0.2.1.0.224.3.38.0',16);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.19.1.8.0.0.0.0.3.1.0.224.3.38.0',18);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.19.1.8.0.0.0.0.4.1.0.224.3.38.0',20);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.1.1.8.0.0.0.0.0.0.0.224.3.63.0',21);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.1.1.8.0.0.0.0.1.0.0.224.3.63.0',23);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.1.1.8.0.0.0.0.2.0.0.224.3.63.0',25);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.1.1.8.0.0.0.0.3.0.0.224.3.63.0',27);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.1.1.8.0.0.0.0.4.0.0.224.3.63.0',29);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.1.1.8.0.0.0.0.0.1.0.224.3.63.0',22);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.1.1.8.0.0.0.0.1.1.0.224.3.63.0',24);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.1.1.8.0.0.0.0.2.1.0.224.3.63.0',26);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.1.1.8.0.0.0.0.3.1.0.224.3.63.0',28);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.1.1.8.0.0.0.0.4.1.0.224.3.63.0',30);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.19.1.8.0.0.0.0.0.0.0.224.3.63.0',31);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.19.1.8.0.0.0.0.1.0.0.224.3.63.0',33);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.19.1.8.0.0.0.0.2.0.0.224.3.63.0',35);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.19.1.8.0.0.0.0.3.0.0.224.3.63.0',37);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.19.1.8.0.0.0.0.4.0.0.224.3.63.0',39);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.19.1.8.0.0.0.0.0.1.0.224.3.63.0',32);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.19.1.8.0.0.0.0.1.1.0.224.3.63.0',34);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.19.1.8.0.0.0.0.2.1.0.224.3.63.0',36);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.19.1.8.0.0.0.0.3.1.0.224.3.63.0',38);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.19.1.8.0.0.0.0.4.1.0.224.3.63.0',40);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.1.1.12.0.0.0.0.0.0.0.224.3.72.0',1);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.1.1.12.0.0.0.0.1.0.0.224.3.72.0',2);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.1.1.12.0.0.0.0.2.0.0.224.3.72.0',3);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.1.1.12.0.0.0.0.3.0.0.224.3.72.0',4);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.1.1.12.0.0.0.0.4.0.0.224.3.72.0',5);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.19.1.12.0.0.0.0.0.0.0.224.3.72.0',6);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.19.1.12.0.0.0.0.1.0.0.224.3.72.0',7);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.19.1.12.0.0.0.0.2.0.0.224.3.72.0',8);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.19.1.12.0.0.0.0.3.0.0.224.3.72.0',9);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.19.1.12.0.0.0.0.4.0.0.224.3.72.0',10);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.5.1.12.0.0.0.0.0.0.0.224.3.73.0',11);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.5.1.12.0.0.0.0.1.0.0.224.3.73.0',12);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.5.1.12.0.0.0.0.2.0.0.224.3.73.0',13);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.5.1.12.0.0.0.0.3.0.0.224.3.73.0',14);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.5.1.12.0.0.0.0.4.0.0.224.3.73.0',15);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.13.1.12.0.0.0.0.0.0.0.224.3.73.0',16);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.13.1.12.0.0.0.0.1.0.0.224.3.73.0',17);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.13.1.12.0.0.0.0.2.0.0.224.3.73.0',18);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.13.1.12.0.0.0.0.3.0.0.224.3.73.0',19);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.13.1.12.0.0.0.0.4.0.0.224.3.73.0',20);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.15.1.12.0.0.0.0.0.0.0.224.3.73.0',21);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.15.1.12.0.0.0.0.1.0.0.224.3.73.0',22);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.15.1.12.0.0.0.0.2.0.0.224.3.73.0',23);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.15.1.12.0.0.0.0.3.0.0.224.3.73.0',24);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.15.1.12.0.0.0.0.4.0.0.224.3.73.0',25);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.16.1.12.0.0.0.0.0.0.0.224.3.73.0',26);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.16.1.12.0.0.0.0.1.0.0.224.3.73.0',27);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.16.1.12.0.0.0.0.2.0.0.224.3.73.0',28);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.16.1.12.0.0.0.0.3.0.0.224.3.73.0',29);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.16.1.12.0.0.0.0.4.0.0.224.3.73.0',30);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.17.1.12.0.0.0.0.0.0.0.224.3.73.0',31);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.17.1.12.0.0.0.0.1.0.0.224.3.73.0',32);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.17.1.12.0.0.0.0.2.0.0.224.3.73.0',33);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.17.1.12.0.0.0.0.3.0.0.224.3.73.0',34);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.17.1.12.0.0.0.0.4.0.0.224.3.73.0',35);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.18.1.12.0.0.0.0.0.0.0.224.3.73.0',36);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.18.1.12.0.0.0.0.1.0.0.224.3.73.0',37);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.18.1.12.0.0.0.0.2.0.0.224.3.73.0',38);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.18.1.12.0.0.0.0.3.0.0.224.3.73.0',39);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001014','13.26.0.1.18.1.12.0.0.0.0.4.0.0.224.3.73.0',40);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.131.285',82);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.132.285',83);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.133.285',84);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.131.37',85);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.132.37',86);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.133.37',87);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.0.98',90);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.131.40',92);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.132.40',93);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.133.40',94);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.6.152',209);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.6.153',210);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.7.152',211);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.7.153',212);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.8.152',213);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.8.153',214);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.6.285',215);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.6.286',216);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.7.285',217);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.7.286',218);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.8.285',219);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.8.286',220);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.26.285',221);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.26.286',222);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.27.285',223);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.27.286',224);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.28.285',225);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.28.286',226);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.26.152',227);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.26.153',228);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.27.152',229);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.27.153',230);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.28.152',231);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.28.153',232);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.33.98',235);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.33.99',236);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.131.152',237);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.131.153',238);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.132.152',239);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.132.153',240);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.133.152',241);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.133.153',242);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.27.219',295);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.27.220',296);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002006','3.1.4.219',304);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002006','3.1.4.220',305);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002006','3.1.5.219',306);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002006','3.1.5.220',307);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.1.1.12.0.0.0.0.2.0.0.224.3.72.0',3);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.1.1.12.0.0.0.0.3.0.0.224.3.72.0',4);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.1.1.12.0.0.0.0.4.0.0.224.3.72.0',5);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.19.1.12.0.0.0.0.0.0.0.224.3.72.0',6);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.19.1.12.0.0.0.0.1.0.0.224.3.72.0',7);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.19.1.12.0.0.0.0.2.0.0.224.3.72.0',8);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.19.1.12.0.0.0.0.3.0.0.224.3.72.0',9);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.19.1.12.0.0.0.0.4.0.0.224.3.72.0',10);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.5.1.12.0.0.0.0.0.0.0.224.3.73.0',11);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.5.1.12.0.0.0.0.1.0.0.224.3.73.0',12);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.5.1.12.0.0.0.0.2.0.0.224.3.73.0',13);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.5.1.12.0.0.0.0.3.0.0.224.3.73.0',14);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.5.1.12.0.0.0.0.4.0.0.224.3.73.0',15);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.13.1.12.0.0.0.0.0.0.0.224.3.73.0',16);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.13.1.12.0.0.0.0.1.0.0.224.3.73.0',17);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.13.1.12.0.0.0.0.2.0.0.224.3.73.0',18);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.13.1.12.0.0.0.0.3.0.0.224.3.73.0',19);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.13.1.12.0.0.0.0.4.0.0.224.3.73.0',20);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.15.1.12.0.0.0.0.0.0.0.224.3.73.0',21);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.15.1.12.0.0.0.0.1.0.0.224.3.73.0',22);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.15.1.12.0.0.0.0.2.0.0.224.3.73.0',23);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.15.1.12.0.0.0.0.3.0.0.224.3.73.0',24);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.15.1.12.0.0.0.0.4.0.0.224.3.73.0',25);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.16.1.12.0.0.0.0.0.0.0.224.3.73.0',26);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.16.1.12.0.0.0.0.1.0.0.224.3.73.0',27);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.16.1.12.0.0.0.0.2.0.0.224.3.73.0',28);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.16.1.12.0.0.0.0.3.0.0.224.3.73.0',29);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.16.1.12.0.0.0.0.4.0.0.224.3.73.0',30);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.17.1.12.0.0.0.0.0.0.0.224.3.73.0',31);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.17.1.12.0.0.0.0.1.0.0.224.3.73.0',32);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.17.1.12.0.0.0.0.2.0.0.224.3.73.0',33);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.17.1.12.0.0.0.0.3.0.0.224.3.73.0',34);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.17.1.12.0.0.0.0.4.0.0.224.3.73.0',35);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.1.1.12.0.0.0.0.0.0.0.224.3.72.0',1);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.1.1.12.0.0.0.0.1.0.0.224.3.72.0',2);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.1.1.12.0.0.0.0.2.0.0.224.3.72.0',3);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.1.1.12.0.0.0.0.3.0.0.224.3.72.0',4);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.1.1.12.0.0.0.0.4.0.0.224.3.72.0',5);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.19.1.12.0.0.0.0.0.0.0.224.3.72.0',6);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.19.1.12.0.0.0.0.1.0.0.224.3.72.0',7);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.19.1.12.0.0.0.0.2.0.0.224.3.72.0',8);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.19.1.12.0.0.0.0.3.0.0.224.3.72.0',9);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.19.1.12.0.0.0.0.4.0.0.224.3.72.0',10);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.5.1.12.0.0.0.0.0.0.0.224.3.73.0',11);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.5.1.12.0.0.0.0.1.0.0.224.3.73.0',12);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.5.1.12.0.0.0.0.2.0.0.224.3.73.0',13);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.5.1.12.0.0.0.0.3.0.0.224.3.73.0',14);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.5.1.12.0.0.0.0.4.0.0.224.3.73.0',15);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.13.1.12.0.0.0.0.0.0.0.224.3.73.0',16);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.13.1.12.0.0.0.0.1.0.0.224.3.73.0',17);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.13.1.12.0.0.0.0.2.0.0.224.3.73.0',18);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.13.1.12.0.0.0.0.3.0.0.224.3.73.0',19);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.13.1.12.0.0.0.0.4.0.0.224.3.73.0',20);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.18.1.12.0.0.0.0.0.0.0.224.3.73.0',36);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003004','39.0.0.1',1);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003004','39.0.0.2',2);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003004','39.0.0.3',3);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003004','39.0.0.4',4);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003004','39.0.0.5',5);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003003','0.0.0.12.9.1.37.0.0.0.0.0.0.0.224.3.38.0',12);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003003','0.0.0.12.10.1.37.0.0.0.0.0.0.0.224.3.38.0',13);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.9.1.12.0.0.0.0.0.0.0.224.3.73.0',41);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.10.1.12.0.0.0.0.0.0.0.224.3.73.0',42);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.7.75.25',20);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.40.0.1',253);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.40.0.2',254);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.40.0.3',255);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.40.0.4',256);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002002','3.12.38.1',50);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002002','3.41.0.1',255);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002003','3.42.0.1',255);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.41.4.1',255);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001010','0.0.0.12.0.1.54.0.0.0.0.0.0.0.64.0.29.0',11);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001010','0.0.0.12.0.1.54.0.0.0.0.0.0.0.32.0.29.0',12);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001010','0.0.0.12.0.1.4.0.0.0.0.0.0.0.64.0.5.0',13);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001010','0.0.0.12.0.1.4.0.0.0.0.0.0.0.32.0.5.0',14);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004003','40.0.3.1',1);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004003','40.0.3.2',2);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004003','40.0.2.5',3);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004003','40.0.2.6',4);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.18.1.12.0.0.0.0.1.0.0.224.3.73.0',37);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.18.1.12.0.0.0.0.2.0.0.224.3.73.0',38);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.18.1.12.0.0.0.0.3.0.0.224.3.73.0',39);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.18.1.12.0.0.0.0.4.0.0.224.3.73.0',40);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003003','0.0.0.12.0.1.54.0.0.0.0.0.0.0.32.0.29.0',3);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003003','0.0.0.12.0.1.54.0.0.0.0.0.0.0.64.0.29.0',2);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003003','0.0.0.12.0.1.4.0.0.0.0.0.0.0.128.0.5.0',4);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003003','0.0.0.12.0.1.4.0.0.0.0.0.0.0.32.0.5.0',6);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003003','0.0.0.12.0.1.4.0.0.0.0.0.0.0.64.0.5.0',5);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003003','0.0.0.12.1.1.37.0.0.0.0.0.0.0.224.3.38.0',8);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003003','0.0.0.12.19.1.37.0.0.0.0.0.0.0.224.3.38.0',9);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003003','0.0.0.12.1.1.37.0.0.0.0.0.0.0.224.3.63.0',10);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003003','0.0.0.12.19.1.37.0.0.0.0.0.0.0.224.3.63.0',11);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003003','0.0.0.12.0.1.38.0.0.0.0.0.0.0.0.0.65.0',14);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003003','0.0.0.12.0.1.38.0.0.0.0.0.0.0.128.0.65.0',15);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003003','0.0.0.12.0.1.38.0.0.0.0.0.0.0.32.0.65.0',17);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003003','0.0.0.12.0.1.38.0.0.0.0.0.0.0.64.0.65.0',16);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003003','0.0.0.12.0.1.15.0.0.0.0.0.0.0.224.0.33.0',18);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.15',2);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.16',3);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.17',4);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.18',5);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.19',6);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.20',7);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.21',8);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.22',9);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.23',10);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.24',11);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.25',12);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.26',13);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.27',14);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.28',15);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.29',16);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.30',17);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.31',18);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.32',19);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.33',20);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.34',21);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.35',22);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.36',23);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.37',24);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.38',25);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.39',26);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.40',27);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003003','0.2.2.0.0.1.4.0.0.0.0.0.0.0.16.0.5.0',7);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002005','3.19.92.85',146);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002005','3.19.93.85',147);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002005','3.19.94.85',148);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002005','3.19.0.215',150);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002005','3.19.0.72',154);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002005','3.23.0.48',157);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002005','3.23.0.49',158);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.26.0.285',1);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.36.56.76',3);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.36.116.58',4);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.36.116.59',5);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.36.0.35',6);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.2.0.284',7);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.2.22.150',8);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.20.121.4',9);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.0.89.28',10);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.17.285.28',11);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.18.83.79',12);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.18.85.79',13);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.18.72.79',14);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.37.0.79',15);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.21.67.79',16);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.11.17.280',17);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.11.17.4',18);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.20.121.213',19);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.7.75.24',47);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.12.32.24',48);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.11.46.35',51);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.8.0.40',52);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.7.25.91',88);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.26.137.85',89);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002002','3.12.141.39',40);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002001','3.26.0.216',2);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002003','***********',62);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002003','*********',61);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002003','***********',63);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002003','*********',60);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002003','3.31.0.280',59);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002002','3.0.48.219',91);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002002','3.12.36.85',49);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002002','3.23.74.85',46);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002002','**********',45);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002002','3.12.29.39',44);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002002','3.12.66.212',43);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002002','3.12.66.257',42);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002002','3.12.141.16',41);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.132.248',80);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.131.248',79);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.133.223',78);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.133.248',81);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002003','3.31.0.67',68);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002003','3.8.0.295',67);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002003','3.8.261.37',66);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002003','3.8.261.139',65);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002003','3.8.288.139',72);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002003','*********',64);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.1.1.12.0.0.0.0.0.0.0.224.3.72.0',1);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003001','0.26.0.1.1.1.12.0.0.0.0.1.0.0.224.3.72.0',2);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003002','0.0.73.6.19.1.8.0.0.0.0.0.0.0.224.3.63.0',4);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003002','0.0.73.6.1.1.8.0.0.0.0.0.0.0.224.3.38.0',1);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003002','0.0.73.6.19.1.8.0.0.0.0.0.0.0.224.3.38.0',2);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003002','0.0.73.6.1.1.8.0.0.0.0.0.0.0.224.3.63.0',3);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003004','39.0.0.6',6);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003004','39.0.0.7',7);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003005','40.0.0.1',1);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003005','40.0.0.2',1);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003005','40.0.0.3',3);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003005','40.0.0.4',5);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003005','40.0.0.5',7);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003005','40.0.0.6',8);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003005','40.0.0.7',2);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003005','40.0.0.8',4);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003005','40.0.0.9',6);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003003','0.0.0.12.0.1.54.0.0.0.0.0.0.0.128.0.29.0',1);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003005','40.0.0.10',8);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004001','37.0.1.6.0.0.0',1);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004001','37.2.1.1',2);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003005','40.0.0.11',9);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001011','0.0.73.6.19.1.8.0.0.0.0.0.0.0.224.3.38.0',6);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001011','0.0.73.6.1.1.8.0.0.0.0.0.0.0.224.3.63.0',7);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001011','0.0.73.6.19.1.8.0.0.0.0.0.0.0.224.3.63.0',8);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.15.1.12.0.0.0.0.0.0.0.224.3.73.0',21);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.15.1.12.0.0.0.0.1.0.0.224.3.73.0',22);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.15.1.12.0.0.0.0.2.0.0.224.3.73.0',23);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.15.1.12.0.0.0.0.3.0.0.224.3.73.0',24);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003005','40.0.0.12',10);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004002','38.0.0.8',1);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.15.1.12.0.0.0.0.4.0.0.224.3.73.0',25);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003005','40.0.0.13',11);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003005','40.0.0.14',12);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.16.1.12.0.0.0.0.0.0.0.224.3.73.0',26);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003005','40.0.0.15',13);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003005','40.0.0.16',14);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003005','40.0.0.17',15);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003005','40.0.0.18',16);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1003005','40.0.0.19',17);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001015','11.0.2.4.1.1.12.0.0.0.0.0.0.0.224.3.72.0',1);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001015','11.0.2.4.1.1.12.0.0.0.0.1.0.0.224.3.72.0',2);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001015','11.0.2.4.1.1.12.0.0.0.0.2.0.0.224.3.72.0',3);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001015','11.0.2.4.1.1.12.0.0.0.0.3.0.0.224.3.72.0',4);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.16.1.12.0.0.0.0.1.0.0.224.3.73.0',27);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.16.1.12.0.0.0.0.2.0.0.224.3.73.0',28);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.131.223',76);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002003','3.8.289.37',75);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002004','3.26.132.223',77);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002003','3.31.0.49',69);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002003','3.8.287.139',70);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002003','3.8.287.37',71);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002003','3.8.288.37',73);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002003','3.8.289.139',74);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002005','3.19.0.214',149);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002005','3.19.0.42',151);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002005','3.19.299.85',155);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002005','3.19.212.85',140);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002005','3.19.298.85',141);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002005','3.19.91.85',145);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002005','3.19.69.37',143);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002005','3.19.90.85',144);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002005','3.19.69.85',142);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002005','3.23.0.47',156);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002006','3.1.1.219',263);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002006','3.1.1.220',264);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002006','3.1.2.219',300);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002006','3.1.2.220',301);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002005','3.19.0.68',153);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002005','3.19.1.42',152);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002006','3.1.3.219',302);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1002006','3.1.3.220',303);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001015','11.0.2.4.1.1.12.0.0.0.0.4.0.0.224.3.72.0',5);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001015','11.0.2.4.19.1.12.0.0.0.0.0.0.0.224.3.72.0',6);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001015','11.0.2.4.19.1.12.0.0.0.0.1.0.0.224.3.72.0',7);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001015','11.0.2.4.19.1.12.0.0.0.0.2.0.0.224.3.72.0',8);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001015','11.0.2.4.19.1.12.0.0.0.0.3.0.0.224.3.72.0',9);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001015','11.0.2.4.19.1.12.0.0.0.0.4.0.0.224.3.72.0',10);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001009','0.26.0.1.19.1.12.0.0.0.0.0.0.0.224.3.72.0',2);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001015','11.0.2.4.5.1.12.0.0.0.0.0.0.0.224.3.73.0',11);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001015','11.0.2.4.5.1.12.0.0.0.0.1.0.0.224.3.73.0',12);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001015','11.0.2.4.5.1.12.0.0.0.0.2.0.0.224.3.73.0',13);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001015','11.0.2.4.5.1.12.0.0.0.0.3.0.0.224.3.73.0',14);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001009','0.26.0.1.5.1.12.0.0.0.0.0.0.0.224.3.73.0',3);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001015','11.0.2.4.5.1.12.0.0.0.0.4.0.0.224.3.73.0',15);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001015','11.0.2.4.13.1.12.0.0.0.0.0.0.0.224.3.73.0',16);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001015','11.0.2.4.13.1.12.0.0.0.0.1.0.0.224.3.73.0',17);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001015','11.0.2.4.13.1.12.0.0.0.0.2.0.0.224.3.73.0',18);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001009','0.26.0.1.13.1.12.0.0.0.0.0.0.0.224.3.73.0',4);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001015','11.0.2.4.13.1.12.0.0.0.0.3.0.0.224.3.73.0',19);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001015','11.0.2.4.13.1.12.0.0.0.0.4.0.0.224.3.73.0',20);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001016','13.0.2.4.1.1.12.0.0.0.0.0.0.0.224.3.72.0',1);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001016','13.0.2.4.1.1.12.0.0.0.0.1.0.0.224.3.72.0',2);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001016','13.0.2.4.1.1.12.0.0.0.0.2.0.0.224.3.72.0',3);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001016','13.0.2.4.1.1.12.0.0.0.0.3.0.0.224.3.72.0',4);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001016','13.0.2.4.1.1.12.0.0.0.0.4.0.0.224.3.72.0',5);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001016','13.0.2.4.19.1.12.0.0.0.0.0.0.0.224.3.72.0',6);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001016','13.0.2.4.19.1.12.0.0.0.0.1.0.0.224.3.72.0',7);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001016','13.0.2.4.19.1.12.0.0.0.0.2.0.0.224.3.72.0',8);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001016','13.0.2.4.19.1.12.0.0.0.0.3.0.0.224.3.72.0',9);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001016','13.0.2.4.19.1.12.0.0.0.0.4.0.0.224.3.72.0',10);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001016','13.0.2.4.5.1.12.0.0.0.0.0.0.0.224.3.73.0',11);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001016','13.0.2.4.5.1.12.0.0.0.0.1.0.0.224.3.73.0',12);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001016','13.0.2.4.5.1.12.0.0.0.0.2.0.0.224.3.73.0',13);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001016','13.0.2.4.5.1.12.0.0.0.0.3.0.0.224.3.73.0',14);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001016','13.0.2.4.5.1.12.0.0.0.0.4.0.0.224.3.73.0',15);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001016','13.0.2.4.13.1.12.0.0.0.0.0.0.0.224.3.73.0',16);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001016','13.0.2.4.13.1.12.0.0.0.0.1.0.0.224.3.73.0',17);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001016','13.0.2.4.13.1.12.0.0.0.0.2.0.0.224.3.73.0',18);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001016','13.0.2.4.13.1.12.0.0.0.0.3.0.0.224.3.73.0',19);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001016','13.0.2.4.13.1.12.0.0.0.0.4.0.0.224.3.73.0',20);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001010','0.0.0.12.0.1.4.0.0.0.0.0.0.0.128.0.5.0',10);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001010','0.0.0.12.0.1.54.0.0.0.0.0.0.0.128.0.29.0',9);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001011','0.0.73.6.1.1.8.0.0.0.0.0.0.0.224.3.38.0',5);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.16.1.12.0.0.0.0.3.0.0.224.3.73.0',29);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.16.1.12.0.0.0.0.4.0.0.224.3.73.0',30);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.17.1.12.0.0.0.0.0.0.0.224.3.73.0',31);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.17.1.12.0.0.0.0.1.0.0.224.3.73.0',32);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.17.1.12.0.0.0.0.2.0.0.224.3.73.0',33);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.17.1.12.0.0.0.0.3.0.0.224.3.73.0',34);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.17.1.12.0.0.0.0.4.0.0.224.3.73.0',35);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.18.1.12.0.0.0.0.0.0.0.224.3.73.0',36);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.18.1.12.0.0.0.0.1.0.0.224.3.73.0',37);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.18.1.12.0.0.0.0.2.0.0.224.3.73.0',38);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.18.1.12.0.0.0.0.3.0.0.224.3.73.0',39);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001012','11.26.0.1.18.1.12.0.0.0.0.4.0.0.224.3.73.0',40);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001009','0.26.0.1.1.1.12.0.0.0.0.0.0.0.224.3.72.0',1);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.1.1.8.0.0.0.0.0.0.0.224.3.38.0',1);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.1.1.8.0.0.0.0.1.0.0.224.3.38.0',3);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.1.1.8.0.0.0.0.2.0.0.224.3.38.0',5);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.1.1.8.0.0.0.0.3.0.0.224.3.38.0',7);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.1.1.8.0.0.0.0.4.0.0.224.3.38.0',8);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.1.1.8.0.0.0.0.0.1.0.224.3.38.0',2);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.1.1.8.0.0.0.0.1.1.0.224.3.38.0',4);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.1.1.8.0.0.0.0.2.1.0.224.3.38.0',6);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.1.1.8.0.0.0.0.3.1.0.224.3.38.0',8);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.1.1.8.0.0.0.0.4.1.0.224.3.38.0',10);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.19.1.8.0.0.0.0.0.0.0.224.3.38.0',11);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.19.1.8.0.0.0.0.1.0.0.224.3.38.0',13);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.19.1.8.0.0.0.0.2.0.0.224.3.38.0',15);
insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1001013','13.8.0.6.19.1.8.0.0.0.0.3.0.0.224.3.38.0',17);
commit;
/*-============================================-*/
/*----DLMS Protocol data item parse--*/
/*============================================*/
insert into DICT_DATAITEM_PARSE_DLMS (CODE,PARSE_TYPE,PARSE_LEN,SCALE) values ('3#1.0.31.27.0.255#2', 'double-long-unsigned', 4, 3);
insert into DICT_DATAITEM_PARSE_DLMS (CODE,PARSE_TYPE,PARSE_LEN,SCALE) values ('3#1.0.51.27.0.255#2', 'double-long-unsigned', 4, 3);
insert into DICT_DATAITEM_PARSE_DLMS (CODE,PARSE_TYPE,PARSE_LEN,SCALE) values ('3#1.0.71.27.0.255#2', 'double-long-unsigned', 4, 3);
insert into DICT_DATAITEM_PARSE_DLMS (CODE,PARSE_TYPE,PARSE_LEN,SCALE) values ('3#1.0.32.27.0.255#2', 'long-unsigned', 2, 1);
insert into DICT_DATAITEM_PARSE_DLMS (CODE,PARSE_TYPE,PARSE_LEN,SCALE) values ('3#1.0.15.8.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (CODE,PARSE_TYPE,PARSE_LEN,SCALE) values ('3#1.0.15.19.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (CODE,PARSE_TYPE,PARSE_LEN,SCALE) values ('3#1.0.13.0.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (CODE,PARSE_TYPE,PARSE_LEN,SCALE) values ('4#1.0.15.6.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (CODE,PARSE_TYPE,PARSE_LEN,SCALE) values ('4#1.0.15.6.0.255#5', 'date-time', 12, 0);
insert into DICT_DATAITEM_PARSE_DLMS (CODE,PARSE_TYPE,PARSE_LEN,SCALE) values ('4#1.0.9.6.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (CODE,PARSE_TYPE,PARSE_LEN,SCALE) values ('4#1.0.9.6.0.255#5', 'date-time', 12, 0);
insert into DICT_DATAITEM_PARSE_DLMS (CODE,PARSE_TYPE,PARSE_LEN,SCALE) values ('4#1.0.10.6.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (CODE,PARSE_TYPE,PARSE_LEN,SCALE) values ('4#1.0.10.6.0.255#5', 'date-time', 12, 0);
insert into DICT_DATAITEM_PARSE_DLMS (CODE,PARSE_TYPE,PARSE_LEN,SCALE) values ('3#1.0.15.9.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (CODE,PARSE_TYPE,PARSE_LEN,SCALE) values ('3#1.0.15.2.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (CODE,PARSE_TYPE,PARSE_LEN,SCALE) values ('1#0.0.96.1.2.255#2', 'octet-string', 0, 0);
insert into DICT_DATAITEM_PARSE_DLMS (CODE,PARSE_TYPE,PARSE_LEN,SCALE) values ('1#0.0.96.91.9.255#2', 'unsigned', 0, 0);
insert into DICT_DATAITEM_PARSE_DLMS (CODE,PARSE_TYPE,PARSE_LEN,SCALE) values ('1#0.0.96.91.11.255#2', 'unsigned', 0, 0);
insert into DICT_DATAITEM_PARSE_DLMS (CODE,PARSE_TYPE,PARSE_LEN,SCALE) values ('1#0.0.96.91.12.255#2', 'visible-string', 0, 0);
insert into DICT_DATAITEM_PARSE_DLMS (CODE,PARSE_TYPE,PARSE_LEN,SCALE) values ('1#0.0.96.1.4.255#2', 'octet-string', 0, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.96.14.1.255#2', 'unsigned', 1, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.96.14.128.255#2', 'unsigned', 1, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.19.50.1.255#2', 'double-long-unsigned', 4, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.19.50.2.255#2', 'double-long-unsigned', 4, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.19.129.0.255#2', 'unsigned', 1, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.19.129.1.255#2', 'unsigned', 1, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.19.129.2.255#2', 'unsigned', 1, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.19.129.3.255#2', 'unsigned', 1, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.19.129.4.255#2', 'octet-string', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.19.129.5.255#2', 'unsigned', 1, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.19.130.0.255#2', 'float32', 4, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.19.130.2.255#2', 'float32', 4, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.19.130.3.255#2', 'float32', 4, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.19.131.0.255#2', 'float32', 4, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.19.133.0.255#2', 'float32', 4, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.19.134.0.255#2', 'float32', 4, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.96.54.0.255#2', 'octet-string', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.96.54.2.255#2', 'special', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.96.54.3.255#2', 'bit-string', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('111#0.0.19.0.0.255#2', 'special', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('111#0.0.19.0.0.255#3', 'unsigned', 1, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('111#0.0.19.0.0.255#4', 'bit-string', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('111#0.0.19.0.0.255#5', 'double-long', 4, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('111#0.0.19.0.0.255#6', 'double-long', 4, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('111#0.0.19.0.0.255#7', 'double-long', 4, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('111#0.0.19.0.0.255#8', 'double-long', 4, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('111#0.0.19.0.0.255#9', 'special', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('111#0.0.19.0.0.255#10', 'special', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('111#0.0.19.0.0.255#11', 'special', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('111#0.0.19.0.0.255#12', 'special', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('111#0.0.19.0.0.255#13', 'octet-string', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('111#0.0.19.0.0.255#14', 'octet-string', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('111#0.0.19.0.0.255#15', 'special', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('111#0.0.19.0.0.255#16', 'double-long', 4, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('111#0.0.19.0.0.255#17', 'double-long', 4, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('111#0.0.19.0.0.255#18', 'long-unsigned', 2, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('111#0.0.19.0.0.255#19', 'double-long', 4, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('113#0.0.19.20.0.255#2', 'double-long', 4, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('113#0.0.19.20.0.255#3', 'enum', 1, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('113#0.0.19.20.0.255#4', 'unsigned', 1, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('113#0.0.19.20.0.255#5', 'special', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('113#0.0.19.20.0.255#6', 'special', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('113#0.0.19.20.0.255#7', 'octet-string', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('113#0.0.19.20.0.255#8', 'double-long-unsigned', 4, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('113#0.0.19.20.0.255#9', 'bit-string', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('113#0.0.19.20.0.255#10', 'date-time', 12, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('113#0.0.19.20.0.255#11', 'double-long', 4, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('113#0.0.19.20.0.255#12', 'double-long', 4, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('113#0.0.19.20.0.255#13', 'long-unsigned', 2, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('21#0.0.16.0.2.255#2', 'special', null, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('115#0.0.11.0.1.255#1', 'special', null, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.42.0.0.255#2', 'visible-string', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.96.1.0.255#2', 'visible-string', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.96.1.1.255#2', 'visible-string', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#1.0.0.2.0.255#2', 'visible-string', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.9.7.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.9.10.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.10.7.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.9.8.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.10.8.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('70#0.0.96.3.10.255#3', 'enum', 1, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('70#0.0.96.3.10.255#4', 'enum', 1, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('115#0.0.19.40.0.255#2', 'special', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('11#0.0.11.0.2.255#2', 'special', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.0.255#2', 'octet-string', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.0.255#3', 'double-long', 4, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.0.255#4', 'enum', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.0.255#5', 'unsigned', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.0.255#6', 'double-long', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.0.255#7', 'bit-string', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.0.255#8', 'enum', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.0.255#9', 'double-long', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.0.255#10', 'double-long', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.0.255#11', 'date-time', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.1.255#2', 'octet-string', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.1.255#3', 'double-long', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.1.255#4', 'enum', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.1.255#5', 'unsigned', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.1.255#6', 'double-long', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.1.255#7', 'bit-string', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.1.255#8', 'enum', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.1.255#9', 'double-long', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.1.255#10', 'double-long', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.1.255#11', 'date-time', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.2.255#2', 'octet-string', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.2.255#3', 'double-long', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.2.255#4', 'enum', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.2.255#5', 'unsigned', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.2.255#6', 'double-long', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.2.255#7', 'bit-string', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.2.255#8', 'enum', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.2.255#9', 'double-long', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.2.255#10', 'double-long', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('112#0.0.19.10.2.255#11', 'date-time', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.96.11.0.255#2', 'long-unsigned', 2, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.96.11.1.255#2', 'long-unsigned', 2, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.96.11.2.255#2', 'long-unsigned', 2, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.96.11.3.255#2', 'long-unsigned', 2, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.96.11.4.255#2', 'long-unsigned', 2, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.96.11.5.255#2', 'long-unsigned', 2, null);
commit;
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.96.11.6.255#2', 'long-unsigned', 2, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.96.11.7.255#2', 'long-unsigned', 2, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('20#0.0.13.0.0.255#2', 'octet-string', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('20#0.0.13.0.0.255#3', 'special', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('20#0.0.13.0.0.255#4', 'special', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('20#0.0.13.0.0.255#5', 'special', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('20#0.0.13.0.0.255#6', 'octet-string', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('20#0.0.13.0.0.255#7', 'special', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('20#0.0.13.0.0.255#8', 'special', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('20#0.0.13.0.0.255#9', 'special', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('20#0.0.13.0.0.255#10', 'special', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('20#0.0.13.0.0.255#11', 'date-time', 12, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('20#0.0.13.0.0.255#12', null, null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.15.35.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.15.44.0.255#2', 'long-unsigned', 2, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#0.0.96.7.19.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#0.0.96.7.20.255#2', 'long-unsigned', 2, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.11.31.0.255#2', 'long-unsigned', 2, 2);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.11.35.0.255#2', 'long-unsigned', 2, 1);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.11.39.0.255#2', 'long-unsigned', 2, 2);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.11.129.0.255#2', 'long-unsigned', 2, 2);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.12.129.0.255#2', 'long-unsigned', 2, 2);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.11.43.0.255#2', 'long-unsigned', 2, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.11.44.0.255#2', 'long-unsigned', 2, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.11.45.0.255#2', 'long-unsigned', 2, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.11.129.1.255#2', 'long-unsigned', 2, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.12.129.1.255#2', 'long-unsigned', 2, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.12.31.0.255#2', 'long-unsigned', 2, 1);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.12.35.0.255#2', 'long-unsigned', 2, 1);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.12.39.0.255#2', 'long-unsigned', 2, 1);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.12.43.0.255#2', 'long-unsigned', 2, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.12.44.0.255#2', 'long-unsigned', 2, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.12.45.0.255#2', 'long-unsigned', 2, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.13.31.0.255#2', 'long-unsigned', 2, 3);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.13.43.0.255#2', 'long-unsigned', 2, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.14.31.0.255#2', 'long-unsigned', 2, 2);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.14.43.0.255#2', 'long-unsigned', 2, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.14.35.0.255#2', 'long-unsigned', 2, 2);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.14.44.0.255#2', 'long-unsigned', 2, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#0.0.96.20.129.255#2', 'long-unsigned', 2, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#0.0.96.20.131.255#2', 'long-unsigned', 2, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#0.0.96.20.132.255#2', 'long-unsigned', 2, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('11#0.0.11.0.0.255#2', 'special', null, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('8#0.0.1.0.0.255#2', 'date-time', 12, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.96.10.1.255#2', 'unsigned', 1, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.12.7.0.255#2', 'long-unsigned', 2, 1);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.11.7.0.255#2', 'double-long-unsigned', 4, 3);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.91.7.0.255#2', 'double-long-unsigned', 4, 3);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.14.7.0.255#2', 'long-unsigned', 2, 2);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('5#1.0.1.4.0.255#3', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('5#1.0.2.4.0.255#3', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('5#1.0.3.4.0.255#3', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('5#1.0.4.4.0.255#3', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.1.8.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.13.7.0.255#2', 'long', 2, 3);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.2.8.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.2.8.4.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.3.8.3.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.53.7.0.255#2', 'long-unsigned', 2, 3);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.72.7.0.255#2', 'long-unsigned', 2, 1);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.2.6.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.1.8.3.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.3.8.1.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.3.8.4.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.4.7.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.4.8.4.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.1.8.4.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.3.8.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.33.7.0.255#2', 'long-unsigned', 2, 3);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.4.8.1.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.4.8.2.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.5.8.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.2.6.0.255#5', 'date-time', 12, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.1.8.1.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.1.8.2.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.32.7.0.255#2', 'long-unsigned', 2, 1);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.4.8.3.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.51.7.0.255#2', 'double-long-unsigned', 4, 3);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.52.7.0.255#2', 'long-unsigned', 2, 1);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.71.7.0.255#2', 'double-long-unsigned', 4, 3);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.4.6.0.255#5', 'date-time', 12, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.2.8.2.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.2.8.3.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.6.8.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.8.8.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.4.6.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.1.7.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.2.7.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.3.7.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.7.8.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.3.6.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.2.8.1.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.31.7.0.255#2', 'double-long-unsigned', 4, 3);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.73.7.0.255#2', 'long-unsigned', 2, 3);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.1.6.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.3.8.2.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.4.8.0.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.1.6.0.255#5', 'date-time', 12, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.3.6.0.255#5', 'date-time', 12, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.96.15.0.255#2', 'long-unsigned', 2, null);
commit;
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.96.15.1.255#2', 'long-unsigned', 2, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.96.15.2.255#2', 'long-unsigned', 2, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.96.15.3.255#2', 'long-unsigned', 2, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.96.15.4.255#2', 'long-unsigned', 2, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.96.15.5.255#2', 'long-unsigned', 2, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.96.15.6.255#2', 'long-unsigned', 2, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('1#0.0.96.15.7.255#2', 'long-unsigned', 2, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.1.6.1.255#5', 'date-time', 12, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.1.6.2.255#5', 'date-time', 12, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.1.6.3.255#5', 'date-time', 12, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.1.6.4.255#5', 'date-time', 12, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.2.6.1.255#5', 'date-time', 12, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.2.6.2.255#5', 'date-time', 12, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.2.6.3.255#5', 'date-time', 12, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.2.6.4.255#5', 'date-time', 12, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.3.6.1.255#5', 'date-time', 12, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.3.6.2.255#5', 'date-time', 12, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.3.6.3.255#5', 'date-time', 12, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.3.6.4.255#5', 'date-time', 12, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.4.6.1.255#5', 'date-time', 12, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.4.6.2.255#5', 'date-time', 12, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.4.6.3.255#5', 'date-time', 12, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.4.6.4.255#5', 'date-time', 12, null);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.1.6.1.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.1.6.2.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.1.6.3.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.1.6.4.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.2.6.1.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.2.6.2.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.2.6.3.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.2.6.4.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.3.6.1.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.3.6.2.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.3.6.3.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.3.6.4.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.4.6.1.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.4.6.2.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.4.6.3.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('4#1.0.4.6.4.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.5.8.1.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.5.8.2.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.5.8.3.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.5.8.4.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.6.8.1.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.6.8.2.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.6.8.3.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.6.8.4.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.7.8.1.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.7.8.2.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.7.8.3.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.7.8.4.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.8.8.1.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.8.8.2.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.8.8.3.255#2', 'double-long-unsigned', 4, 0);
insert into DICT_DATAITEM_PARSE_DLMS (code, parse_type, parse_len, scale)
values ('3#1.0.8.8.4.255#2', 'double-long-unsigned', 4, 0);

/*-============================================-*/
/*----Define manufacturer  --*/
/*============================================*/
insert into DICT_MANUFACTURER (id, name, introduction)
values ('101', 'Clou', 'SHENZHEN CLOU ELECTRONICS CO., LTD.');
insert into DICT_MANUFACTURER (id, name, introduction)
values ('102', 'Siemens', null);
insert into DICT_MANUFACTURER (id, name, introduction)
values ('103', 'LandisGyr', null);

/*-============================================-*/
/*----Define default menu --*/
/*============================================*/
insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1e858f11beda11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',1,'Data Collection',1,null,'0',null,null,null);
insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('265ddc0cbeda11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',1,'Tools',5,null,'0',null,null,null);
insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('2dd2e5a5beda11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',1,'System',6,null,'0',null,null,null);
insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('2e858f11beda11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',1,'Provisioning',2,null,'0',null,null,null);
insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('3dd9ea8cbd4111e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',1,'Integration',4,null,'0',null,null,null);
insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40422339bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Meter Data Report',3,'meterDataReportController/list.do','1e858f11beda11e79bb968f728c516f9','1','1001',null);
insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('404227d7bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Meter Event Report',4,'dataMeterEventController/list.do','1e858f11beda11e79bb968f728c516f9','1','1002',null);
insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40422a45bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Schedule Reads Report',1,'dataIntegrityController/scheduleReadsReport.do','1e858f11beda11e79bb968f728c516f9','1','1003',null);
insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40422c62bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Miss Data Tracing',2,'dataIntegrityController/list.do','1e858f11beda11e79bb968f728c516f9','1','1004',null);
insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40422ec4bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Collection Scheme Management',5,'assetScheduleSchemeDetailController/list.do','1e858f11beda11e79bb968f728c516f9','1','1005',null);
insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('4057793ebedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Meter Group Management',2,'assetMeterGroupController/meterGroupMgmt.do','2e858f11beda11e79bb968f728c516f9','1','1006',null);
insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40577c5abedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Meter Configuration',3,'meterConfigurationController/toMeterConfiguration.do','2e858f11beda11e79bb968f728c516f9','1','1007',null);
insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40577ff3bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Firmware Upgrade',2,'dataFwuPlanController/list.do','265ddc0cbeda11e79bb968f728c516f9','1','1009',null);
insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40578309bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'On Demand Reads',1,'assetMeterController/onDemandReads.do','265ddc0cbeda11e79bb968f728c516f9','1','1011',null);
insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('4057848ebedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Connect / Disconnect',4,'meterConnectOrDisconnectController/toConnectOrDisconnect.do','265ddc0cbeda11e79bb968f728c516f9','1','1012',null);
insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('4057860cbedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Asset Management',1,'assetMeterController/assetManagementList.do','2e858f11beda11e79bb968f728c516f9','1','1013',null);
insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40578791bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Deployment & Cluster Management',3,'sysServerController/list.do','2dd2e5a5beda11e79bb968f728c516f9','1','1014',null);
insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('4057890cbedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Log Explorer',2,'sysLogController/list.do','2dd2e5a5beda11e79bb968f728c516f9','1','1015',null);
insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40578a88bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Service Request Job Tracing',1,'sysLogController/list.do?type=3','3dd9ea8cbd4111e79bb968f728c516f9','1','1016',null);
insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40578c0dbedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Permission',1,'sysUserController/sysUserAndRoleList.do','2dd2e5a5beda11e79bb968f728c516f9','1','1017',null);
insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40578a88bedc11e79bb968f728c51688','29018328bd4011e79bb968f728c516f9',2,'Meter Data & Event Export',2,'sysDataitemExportController/list.do','3dd9ea8cbd4111e79bb968f728c516f9','1','1018',null);
insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('6f088b1d22b545b78aac411aa5fd2161','29018328bd4011e79bb968f728c516f9',2,'Meter Group Upgrade',3,'dataParameterPlanController/meterGroupUpgradeList.do','265ddc0cbeda11e79bb968f728c516f9','1','1019',null);
insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('2dd2e5a5beda11e79bb968f728c53333','29018328bd4011e79bb968f728c516f9',1,'Reports',3,null,'0',null,null,null);
/*-============================================-*/
/*----Define protocol  --*/
/*============================================*/
insert into DICT_PROTOCOL (id, name, introduction)
values ('100', 'DLMS/COSEM', 'IEC 62056 DLMS-47 for TCP/IP');

/*-============================================-*/
/*----Define device info  --*/
/*============================================*/
insert into DICT_DEVICE_MODEL (id, name, manufacturer_id, protocol_id, introduction, device_type_icon, device_type)
values ('101001', 'CL710K22', '101', '100', 'Clou Single Phase Meter', '/theme/img/product.png', 101);
insert into DICT_DEVICE_MODEL (id, name, manufacturer_id, protocol_id, introduction, device_type_icon, device_type)
values ('101002', 'CL730S22', '101', '100', 'Clou Three Phase Metet ', '/theme/img/product.png', 101);
insert into DICT_DEVICE_MODEL (id, name, manufacturer_id, protocol_id, introduction, device_type_icon, device_type)
values ('101003', 'CL818C', '101', '100', 'Clou Gateway', '/theme/img/product.png', 202);
insert into DICT_DEVICE_MODEL (id, name, manufacturer_id, protocol_id, introduction, device_type_icon, device_type)
values ('102001', 'IM100', '102', '100', 'Siemens Single Phase Metet ', '/theme/img/product.png', 101);
insert into DICT_DEVICE_MODEL (id, name, manufacturer_id, protocol_id, introduction, device_type_icon, device_type)
values ('102002', 'IM300', '102', '100', 'Siemens Three Phase Meter', '/theme/img/product.png', 101);
insert into DICT_DEVICE_MODEL (id, name, manufacturer_id, protocol_id, introduction, device_type_icon, device_type)
values ('102003', 'IM700', '102', '100', 'Siemens Gateway', '/theme/img/product.png', 203);
insert into DICT_DEVICE_MODEL (id, name, manufacturer_id, protocol_id, introduction, device_type_icon, device_type)
values ('103001', 'E550', '103', '100', 'LandisGyr Three Phase Meter', '/theme/img/product.png', 101);
insert into DICT_DEVICE_MODEL (id, name, manufacturer_id, protocol_id, introduction, device_type_icon, device_type)
values ('103002', 'DC450', '103', '100', 'LandisGyr Data Concentrator', '/theme/img/product.png', 203);

insert into DICT_DEVICE_TYPE (id, name)
values (101, 'Meter');
insert into DICT_DEVICE_TYPE (id, name)
values (201, 'GPRS Module');
insert into DICT_DEVICE_TYPE (id, name)
values (202, 'Gateway');
insert into DICT_DEVICE_TYPE (id, name)
values (203, 'Data Concentrator');

insert INTO dict_function (id,functionname,functionurl,function_intro) VALUES ('1001', 'Meter Data Report', null, 'Data Collection');
insert INTO dict_function (id,functionname,functionurl,function_intro) VALUES ('1002', 'Meter Event Report', null, 'Data Collection');
insert INTO dict_function (id,functionname,functionurl,function_intro) VALUES ('1003', 'Schedule Reads Report', null, 'Data Collection');
insert INTO dict_function (id,functionname,functionurl,function_intro) VALUES ('1004', 'Miss Data Tracing', null, 'Data Collection');
insert INTO dict_function (id,functionname,functionurl,function_intro) VALUES ('1005', 'Collection Schedule Management', null, 'Data Collection');
insert INTO dict_function (id,functionname,functionurl,function_intro) VALUES ('1006', 'Meter Group Management', null, 'Provisioning');
insert INTO dict_function (id,functionname,functionurl,function_intro) VALUES ('1007', 'Meter Configuration', null, 'Provisioning');
insert INTO dict_function (id,functionname,functionurl,function_intro) VALUES ('1008', 'DCU Configuration', null, 'Provisioning');
insert INTO dict_function (id,functionname,functionurl,function_intro) VALUES ('1009', 'Firmware Upgrade', null, 'Tools');
insert INTO dict_function (id,functionname,functionurl,function_intro) VALUES ('1011', 'On Demand Reads', null, 'Tools');
insert INTO dict_function (id,functionname,functionurl,function_intro) VALUES ('1012', 'Connect / Disconnect', null, 'Tools');
insert INTO dict_function (id,functionname,functionurl,function_intro) VALUES ('1013', 'Asset Management', null, 'Provisioning');
insert INTO dict_function (id,functionname,functionurl,function_intro) VALUES ('1014', 'Deployment & Cluster Management', null, 'System');
insert INTO dict_function (id,functionname,functionurl,function_intro) VALUES ('1015', 'Log Explorer', null, 'System');
insert INTO dict_function (id,functionname,functionurl,function_intro) VALUES ('1016', 'Service Request Job Tracing', null, 'System Integration');
insert INTO dict_function (id,functionname,functionurl,function_intro) VALUES ('1017', 'Permission', 'sysRoleController/list.do', 'System');
insert INTO dict_function (id,functionname,functionurl,function_intro) VALUES ('1018', 'Meter Data & Event Export', null, 'System Integration');


insert into DICT_METER_DATA_STORAGE_TABLE (ID,NAME,COLUMN_COUNT) values ('1015','data_md_interval_daily',20);
insert into DICT_METER_DATA_STORAGE_TABLE (ID,NAME,COLUMN_COUNT) values ('1016','data_md_interval_monthly',20);
insert into DICT_METER_DATA_STORAGE_TABLE (ID,NAME,COLUMN_COUNT) values ('1003','data_md_profile_minutely',14);
insert into DICT_METER_DATA_STORAGE_TABLE (ID,NAME,COLUMN_COUNT) values ('1004','data_md_profile_daily',40);
insert into DICT_METER_DATA_STORAGE_TABLE (ID,NAME,COLUMN_COUNT) values ('1005','data_md_demand_monthly',40);
insert into DICT_METER_DATA_STORAGE_TABLE (ID,NAME,COLUMN_COUNT) values ('1006','data_md_energy_monthly',40);

insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.1.1.8.0.0.0.0.1.0.0.224.3.63.0','1005',23);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.1.1.8.0.0.0.0.1.1.0.224.3.63.0','1005',24);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.1.1.8.0.0.0.0.2.0.0.224.3.63.0','1005',25);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.1.1.8.0.0.0.0.2.1.0.224.3.63.0','1005',26);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.1.1.8.0.0.0.0.3.0.0.224.3.63.0','1005',27);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.1.1.8.0.0.0.0.3.1.0.224.3.63.0','1005',28);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.1.1.8.0.0.0.0.4.0.0.224.3.63.0','1005',29);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.1.1.8.0.0.0.0.4.1.0.224.3.63.0','1005',30);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.19.1.8.0.0.0.0.0.0.0.224.3.63.0','1005',31);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.19.1.8.0.0.0.0.0.1.0.224.3.63.0','1005',32);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.19.1.8.0.0.0.0.1.0.0.224.3.63.0','1005',33);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.19.1.8.0.0.0.0.1.1.0.224.3.63.0','1005',34);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.19.1.8.0.0.0.0.2.0.0.224.3.63.0','1005',35);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.19.1.8.0.0.0.0.2.1.0.224.3.63.0','1005',36);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.19.1.8.0.0.0.0.3.0.0.224.3.63.0','1005',37);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.19.1.8.0.0.0.0.3.1.0.224.3.63.0','1005',38);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.19.1.8.0.0.0.0.4.0.0.224.3.63.0','1005',39);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.19.1.8.0.0.0.0.4.1.0.224.3.63.0','1005',40);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.1.1.12.0.0.0.0.0.0.0.224.3.72.0','1006',1);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.1.1.12.0.0.0.0.1.0.0.224.3.72.0','1006',2);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.1.1.12.0.0.0.0.2.0.0.224.3.72.0','1006',3);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.1.1.12.0.0.0.0.3.0.0.224.3.72.0','1006',4);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.1.1.12.0.0.0.0.4.0.0.224.3.72.0','1006',5);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.19.1.12.0.0.0.0.0.0.0.224.3.72.0','1006',6);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.19.1.12.0.0.0.0.1.0.0.224.3.72.0','1006',7);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.19.1.12.0.0.0.0.2.0.0.224.3.72.0','1006',8);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.19.1.12.0.0.0.0.3.0.0.224.3.72.0','1006',9);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.19.1.12.0.0.0.0.4.0.0.224.3.72.0','1006',10);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.5.1.12.0.0.0.0.0.0.0.224.3.73.0','1006',11);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.5.1.12.0.0.0.0.1.0.0.224.3.73.0','1006',12);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.5.1.12.0.0.0.0.2.0.0.224.3.73.0','1006',13);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.5.1.12.0.0.0.0.3.0.0.224.3.73.0','1006',14);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.5.1.12.0.0.0.0.4.0.0.224.3.73.0','1006',15);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.13.1.12.0.0.0.0.0.0.0.224.3.73.0','1006',16);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.13.1.12.0.0.0.0.1.0.0.224.3.73.0','1006',17);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.13.1.12.0.0.0.0.2.0.0.224.3.73.0','1006',18);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.13.1.12.0.0.0.0.3.0.0.224.3.73.0','1006',19);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.13.1.12.0.0.0.0.4.0.0.224.3.73.0','1006',20);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.15.1.12.0.0.0.0.0.0.0.224.3.73.0','1006',21);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.15.1.12.0.0.0.0.1.0.0.224.3.73.0','1006',22);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.15.1.12.0.0.0.0.2.0.0.224.3.73.0','1006',23);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.15.1.12.0.0.0.0.3.0.0.224.3.73.0','1006',24);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.15.1.12.0.0.0.0.4.0.0.224.3.73.0','1006',25);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.16.1.12.0.0.0.0.0.0.0.224.3.73.0','1006',26);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.16.1.12.0.0.0.0.1.0.0.224.3.73.0','1006',27);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.16.1.12.0.0.0.0.2.0.0.224.3.73.0','1006',28);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.16.1.12.0.0.0.0.3.0.0.224.3.73.0','1006',29);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.16.1.12.0.0.0.0.4.0.0.224.3.73.0','1006',30);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.17.1.12.0.0.0.0.0.0.0.224.3.73.0','1006',31);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.17.1.12.0.0.0.0.1.0.0.224.3.73.0','1006',32);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.17.1.12.0.0.0.0.2.0.0.224.3.73.0','1006',33);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.17.1.12.0.0.0.0.3.0.0.224.3.73.0','1006',34);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.17.1.12.0.0.0.0.4.0.0.224.3.73.0','1006',35);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.18.1.12.0.0.0.0.0.0.0.224.3.73.0','1006',36);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.18.1.12.0.0.0.0.1.0.0.224.3.73.0','1006',37);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.18.1.12.0.0.0.0.2.0.0.224.3.73.0','1006',38);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.18.1.12.0.0.0.0.3.0.0.224.3.73.0','1006',39);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.26.0.1.18.1.12.0.0.0.0.4.0.0.224.3.73.0','1006',40);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('0.0.0.12.0.1.54.0.0.0.0.0.0.0.32.0.29.0','1003',13);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('0.0.0.12.0.1.4.0.0.0.0.0.0.0.64.0.5.0','1003',12);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('0.0.0.12.0.1.4.0.0.0.0.0.0.0.32.0.5.0','1003',14);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('0.0.0.12.0.1.54.0.0.0.0.0.0.0.64.0.29.0','1003',11);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.0.2.4.1.1.12.0.0.0.0.0.0.0.224.3.72.0','1015',1);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.0.2.4.1.1.12.0.0.0.0.1.0.0.224.3.72.0','1015',2);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.0.2.4.1.1.12.0.0.0.0.2.0.0.224.3.72.0','1015',3);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.0.2.4.1.1.12.0.0.0.0.3.0.0.224.3.72.0','1015',4);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.0.2.4.1.1.12.0.0.0.0.4.0.0.224.3.72.0','1015',5);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.0.2.4.19.1.12.0.0.0.0.0.0.0.224.3.72.0','1015',6);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.0.2.4.19.1.12.0.0.0.0.1.0.0.224.3.72.0','1015',7);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.0.2.4.19.1.12.0.0.0.0.2.0.0.224.3.72.0','1015',8);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.0.2.4.19.1.12.0.0.0.0.3.0.0.224.3.72.0','1015',9);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.0.2.4.19.1.12.0.0.0.0.4.0.0.224.3.72.0','1015',10);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.0.2.4.5.1.12.0.0.0.0.0.0.0.224.3.73.0','1015',11);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.0.2.4.5.1.12.0.0.0.0.1.0.0.224.3.73.0','1015',12);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.0.2.4.5.1.12.0.0.0.0.2.0.0.224.3.73.0','1015',13);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.0.2.4.5.1.12.0.0.0.0.3.0.0.224.3.73.0','1015',14);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.0.2.4.5.1.12.0.0.0.0.4.0.0.224.3.73.0','1015',15);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.0.2.4.13.1.12.0.0.0.0.0.0.0.224.3.73.0','1015',16);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.0.2.4.13.1.12.0.0.0.0.1.0.0.224.3.73.0','1015',17);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.0.2.4.13.1.12.0.0.0.0.2.0.0.224.3.73.0','1015',18);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.0.2.4.13.1.12.0.0.0.0.3.0.0.224.3.73.0','1015',19);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.0.2.4.13.1.12.0.0.0.0.4.0.0.224.3.73.0','1015',20);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.0.2.4.1.1.12.0.0.0.0.0.0.0.224.3.72.0','1016',1);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.0.2.4.1.1.12.0.0.0.0.1.0.0.224.3.72.0','1016',2);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.0.2.4.1.1.12.0.0.0.0.2.0.0.224.3.72.0','1016',3);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.0.2.4.1.1.12.0.0.0.0.3.0.0.224.3.72.0','1016',4);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.0.2.4.1.1.12.0.0.0.0.4.0.0.224.3.72.0','1016',5);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.0.2.4.19.1.12.0.0.0.0.0.0.0.224.3.72.0','1016',6);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.0.2.4.19.1.12.0.0.0.0.1.0.0.224.3.72.0','1016',7);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.0.2.4.19.1.12.0.0.0.0.2.0.0.224.3.72.0','1016',8);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.0.2.4.19.1.12.0.0.0.0.3.0.0.224.3.72.0','1016',9);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.0.2.4.19.1.12.0.0.0.0.4.0.0.224.3.72.0','1016',10);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.0.2.4.5.1.12.0.0.0.0.0.0.0.224.3.73.0','1016',11);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.0.2.4.5.1.12.0.0.0.0.1.0.0.224.3.73.0','1016',12);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.0.2.4.5.1.12.0.0.0.0.2.0.0.224.3.73.0','1016',13);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.0.2.4.5.1.12.0.0.0.0.3.0.0.224.3.73.0','1016',14);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.0.2.4.5.1.12.0.0.0.0.4.0.0.224.3.73.0','1016',15);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.0.2.4.13.1.12.0.0.0.0.0.0.0.224.3.73.0','1016',16);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.0.2.4.13.1.12.0.0.0.0.1.0.0.224.3.73.0','1016',17);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.0.2.4.13.1.12.0.0.0.0.2.0.0.224.3.73.0','1016',18);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.0.2.4.13.1.12.0.0.0.0.3.0.0.224.3.73.0','1016',19);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.0.2.4.13.1.12.0.0.0.0.4.0.0.224.3.73.0','1016',20);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('0.26.0.1.1.1.12.0.0.0.0.0.0.0.224.3.72.0','1003',1);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('0.26.0.1.19.1.12.0.0.0.0.0.0.0.224.3.72.0','1003',2);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('0.26.0.1.5.1.12.0.0.0.0.0.0.0.224.3.73.0','1003',3);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('0.26.0.1.13.1.12.0.0.0.0.0.0.0.224.3.73.0','1003',4);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('0.0.73.6.1.1.8.0.0.0.0.0.0.0.224.3.38.0','1003',5);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('0.0.73.6.19.1.8.0.0.0.0.0.0.0.224.3.38.0','1003',6);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('0.0.73.6.1.1.8.0.0.0.0.0.0.0.224.3.63.0','1003',7);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('0.0.73.6.19.1.8.0.0.0.0.0.0.0.224.3.63.0','1003',8);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('0.0.0.12.0.1.54.0.0.0.0.0.0.0.128.0.29.0','1003',9);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('0.0.0.12.0.1.4.0.0.0.0.0.0.0.128.0.5.0','1003',10);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.1.1.12.0.0.0.0.0.0.0.224.3.72.0','1004',1);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.1.1.12.0.0.0.0.1.0.0.224.3.72.0','1004',2);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.1.1.12.0.0.0.0.2.0.0.224.3.72.0','1004',3);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.1.1.12.0.0.0.0.3.0.0.224.3.72.0','1004',4);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.1.1.12.0.0.0.0.4.0.0.224.3.72.0','1004',5);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.19.1.12.0.0.0.0.0.0.0.224.3.72.0','1004',6);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.19.1.12.0.0.0.0.1.0.0.224.3.72.0','1004',7);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.19.1.12.0.0.0.0.2.0.0.224.3.72.0','1004',8);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.19.1.12.0.0.0.0.3.0.0.224.3.72.0','1004',9);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.19.1.12.0.0.0.0.4.0.0.224.3.72.0','1004',10);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.5.1.12.0.0.0.0.0.0.0.224.3.73.0','1004',11);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.5.1.12.0.0.0.0.1.0.0.224.3.73.0','1004',12);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.5.1.12.0.0.0.0.2.0.0.224.3.73.0','1004',13);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.5.1.12.0.0.0.0.3.0.0.224.3.73.0','1004',14);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.5.1.12.0.0.0.0.4.0.0.224.3.73.0','1004',15);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.13.1.12.0.0.0.0.0.0.0.224.3.73.0','1004',16);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.13.1.12.0.0.0.0.1.0.0.224.3.73.0','1004',17);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.13.1.12.0.0.0.0.2.0.0.224.3.73.0','1004',18);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.13.1.12.0.0.0.0.3.0.0.224.3.73.0','1004',19);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.13.1.12.0.0.0.0.4.0.0.224.3.73.0','1004',20);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.15.1.12.0.0.0.0.0.0.0.224.3.73.0','1004',21);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.15.1.12.0.0.0.0.1.0.0.224.3.73.0','1004',22);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.15.1.12.0.0.0.0.2.0.0.224.3.73.0','1004',23);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.15.1.12.0.0.0.0.3.0.0.224.3.73.0','1004',24);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.15.1.12.0.0.0.0.4.0.0.224.3.73.0','1004',25);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.16.1.12.0.0.0.0.0.0.0.224.3.73.0','1004',26);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.16.1.12.0.0.0.0.1.0.0.224.3.73.0','1004',27);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.16.1.12.0.0.0.0.2.0.0.224.3.73.0','1004',28);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.16.1.12.0.0.0.0.3.0.0.224.3.73.0','1004',29);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.16.1.12.0.0.0.0.4.0.0.224.3.73.0','1004',30);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.17.1.12.0.0.0.0.0.0.0.224.3.73.0','1004',31);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.17.1.12.0.0.0.0.1.0.0.224.3.73.0','1004',32);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.17.1.12.0.0.0.0.2.0.0.224.3.73.0','1004',33);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.17.1.12.0.0.0.0.3.0.0.224.3.73.0','1004',34);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.17.1.12.0.0.0.0.4.0.0.224.3.73.0','1004',35);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.18.1.12.0.0.0.0.0.0.0.224.3.73.0','1004',36);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.18.1.12.0.0.0.0.1.0.0.224.3.73.0','1004',37);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.18.1.12.0.0.0.0.2.0.0.224.3.73.0','1004',38);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.18.1.12.0.0.0.0.3.0.0.224.3.73.0','1004',39);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('11.26.0.1.18.1.12.0.0.0.0.4.0.0.224.3.73.0','1004',40);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.1.1.8.0.0.0.0.0.0.0.224.3.38.0','1005',1);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.1.1.8.0.0.0.0.0.1.0.224.3.38.0','1005',2);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.1.1.8.0.0.0.0.1.0.0.224.3.38.0','1005',3);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.1.1.8.0.0.0.0.1.1.0.224.3.38.0','1005',4);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.1.1.8.0.0.0.0.2.0.0.224.3.38.0','1005',5);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.1.1.8.0.0.0.0.2.1.0.224.3.38.0','1005',6);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.1.1.8.0.0.0.0.3.0.0.224.3.38.0','1005',7);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.1.1.8.0.0.0.0.4.0.0.224.3.38.0','1005',9);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.1.1.8.0.0.0.0.3.1.0.224.3.38.0','1005',8);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.1.1.8.0.0.0.0.4.1.0.224.3.38.0','1005',10);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.19.1.8.0.0.0.0.0.0.0.224.3.38.0','1005',11);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.19.1.8.0.0.0.0.0.1.0.224.3.38.0','1005',12);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.19.1.8.0.0.0.0.1.0.0.224.3.38.0','1005',13);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.19.1.8.0.0.0.0.1.1.0.224.3.38.0','1005',14);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.19.1.8.0.0.0.0.2.0.0.224.3.38.0','1005',15);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.19.1.8.0.0.0.0.2.1.0.224.3.38.0','1005',16);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.19.1.8.0.0.0.0.3.0.0.224.3.38.0','1005',17);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.19.1.8.0.0.0.0.3.1.0.224.3.38.0','1005',18);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.19.1.8.0.0.0.0.4.0.0.224.3.38.0','1005',19);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.19.1.8.0.0.0.0.4.1.0.224.3.38.0','1005',20);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.1.1.8.0.0.0.0.0.0.0.224.3.63.0','1005',21);
insert into DICT_METER_DATA_STORAGE_INFO (ID,TABLE_ID,FIELD_INDEX) values ('13.8.0.6.1.1.8.0.0.0.0.0.1.0.224.3.63.0','1005',22);

insert INTO dict_operation VALUES ('1003001', 'Read Overview Tab', '1003', 'readOverviewTab', '1', 'View and search data (UI will be restricted access if not checked.)');
insert INTO dict_operation VALUES ('1003002', 'Read Manufacturer Tab', '1003', 'readManufacturerTab', '1', 'View and search data (UI will be restricted access if not checked.)');
insert INTO dict_operation VALUES ('1003003', 'Read Model Tab', '1003', 'readModelTab', '1', 'View and search data (UI will be restricted access if not checked.)');
insert INTO dict_operation VALUES ('1003004', 'Read Communication Tab', '1003', 'readCommunicationTab', '1', 'View and search data (UI will be restricted access if not checked.)');
insert INTO dict_operation VALUES ('1005002', 'Add, Edit Scheme', '1005', '/assetScheduleSchemeController/assetScheduleScheme.do', '0', 'Add, Edit Scheme');
insert INTO dict_operation VALUES ('1005003', 'Delete Scheme', '1005', '/assetScheduleSchemeController/del.do', '0', 'Delete Scheme');
insert INTO dict_operation VALUES ('1005004', 'Add, Edit Task', '1005', '/assetScheduleSchemeDetailController/assetScheduleSchemeDetail.do', '0', 'Add, Edit Task');
insert INTO dict_operation VALUES ('1005005', 'Delete Task', '1005', '/assetScheduleSchemeDetailController/del.do', '0', 'Delete Task');
insert INTO dict_operation VALUES ('1006005', 'Add, Edit TOU Group', '1006', '/assetMeterGroupController/assetMeterGroup.do?type=2', '0', 'Add, Edit TOU Group');
insert INTO dict_operation VALUES ('1006006', 'Add, Edit Measurement Group', '1006', '/assetMeterGroupController/toMeasurementGroup.do?type=1', '0', 'Add, Edit Measurement Group');
insert INTO dict_operation VALUES ('1006007', 'Add, Edit Limiter Group', '1006', '/assetMeterGroupController/assetMeterGroup.do?type=3', '0', 'Add, Edit Limiter Group');
insert INTO dict_operation VALUES ('1006008', 'Delete TOU Group', '1006', 'deleteTouGroup', '0', 'Delete TOU Group');
insert INTO dict_operation VALUES ('1006009', 'Delete Limiter Group', '1006', 'deleteLimiterGroup', '0', 'Delete Limiter Group');
insert INTO dict_operation VALUES ('1006010', 'Delete Measurement Group', '1006', 'deleteMeasurementGroup', '0', 'Delete Measurement Group');
insert INTO dict_operation VALUES ('1006011', 'Delete Step Tariff Group', '1006', 'deleteStepTariffGroup', '0', 'Delete Step Tariff Group');
insert INTO dict_operation VALUES ('1006012', 'Add,Edit Step Tariff Group', '1006', '/assetMeterGroupController/addStepTariffGroup.do?type=4', '0', 'Add,Edit Step Tariff Group');
insert INTO dict_operation VALUES ('1007004', 'Excute Task', '1007', '/meterConfigurationController/excuteTask.do', '0', 'Excute Task');
insert INTO dict_operation VALUES ('1007005', 'Read', '1007', '/meterConfigurationController/getTouGroupData.do', '0', 'Read');
insert INTO dict_operation VALUES ('1009001', 'Add Plan', '1009', '/dataFwuPlanController/saveFWUPlan.do', '0', 'Add plan in Plan Tab');
insert INTO dict_operation VALUES ('1009002', 'Cancel Plan, Job', '1009', '/dataFwuPlanController/cancel.do', '0', 'Cancel plan, job in Plan Report/Job Report Tab');
insert INTO dict_operation VALUES ('1013002', 'Save Device', '1013', '/assetMeterController/save.do', '0', 'Save Device');
insert INTO dict_operation VALUES ('1013003', 'Delete Device', '1013', '/assetMeterController/del.do', '0', 'Delete Device');
insert INTO dict_operation VALUES ('1013004', 'Add Device', '1013', '/assetMeterController/addAssetDevice.do', '0', 'Add Device');
insert INTO dict_operation VALUES ('1014002', 'Add, Edit Server/Service', '1014', '/sysServerController/toAddSysServer.do', '0', 'Add, Edit Server/Service');
insert INTO dict_operation VALUES ('1014003', 'Delete Server/Service', '1014', '/sysServerController/delServer.do', '0', 'Delete Server/Service');
insert INTO dict_operation VALUES ('1017005', 'Add, Edit User', '1017', '/sysUserController/toAddSysUser.do', '0', 'Add, Edit User');
insert INTO dict_operation VALUES ('1017006', 'Delete User', '1017', '/sysUserController/del.do', '0', 'Delete User');
insert INTO dict_operation VALUES ('1017007', 'Add, Edit Role', '1017', '/sysRoleController/toAddSysRole.do', '0', 'Add, Edit Role');
insert INTO dict_operation VALUES ('1017008', 'Delete Role', '1017', '/sysRoleController/deleteRole.do', '0', 'Delete Role');
insert INTO dict_operation VALUES ('1017009', 'Add, Edit Organization', '1017', '/sysOrgController/toAddSysOrg.do', '0', 'Add, Edit Organization');
insert INTO dict_operation VALUES ('1017010', 'Delete Organization', '1017', '/sysOrgController/del.do', '0', 'Delete Organization');
insert INTO dict_operation VALUES ('1018002', 'Save Data & Event', '1018', '/sysDataitemExportController/saveMeterDataEventExport.do', '0', 'Save meter data and events to be exported');
insert INTO dict_operation VALUES ('1019001', 'Add Plan', '1019', '/dataParameterPlanController/saveMeterGroupUpgradePlan.do', '0', 'Add plan in Plan Tab');
insert INTO dict_operation VALUES ('1019002', 'Cancel Plan', '1019', 'sd', '0', 'Cancel plan in Plan Report Tab');


insert into DICT_PROFILE (id, name, protocol_id, protocol_code, profile_type)
values ('10053', 'Relay Control Event Log', '100', '7#0.0.99.98.2.255#2', 5);
insert into DICT_PROFILE (id, name, protocol_id, protocol_code, profile_type)
values ('10001', 'Load Profile (Minutely)', '100', '7#1.0.99.1.0.255#2', 1);
insert into DICT_PROFILE (id, name, protocol_id, protocol_code, profile_type)
values ('10002', 'Load Profile (Daily)', '100', '7#1.0.99.2.0.255#2', 1);
insert into DICT_PROFILE (id, name, protocol_id, protocol_code, profile_type)
values ('10003', 'Demand Billing Profile (Monthly)', '100', '7#1.0.98.2.0.255#2', 1);
insert into DICT_PROFILE (id, name, protocol_id, protocol_code, profile_type)
values ('10054', 'Power Quality Event Log', '100', '7#0.0.99.98.4.255#2', 5);
insert into DICT_PROFILE (id, name, protocol_id, protocol_code, profile_type)
values ('10055', 'Communication Event Log', '100', '7#0.0.99.98.5.255#2', 5);
insert into DICT_PROFILE (id, name, protocol_id, protocol_code, profile_type)
values ('10056', 'Manufacturer Specific Event Log', '100', '7#0.0.99.98.6.255#2', 5);
insert into DICT_PROFILE (id, name, protocol_id, protocol_code, profile_type)
values ('10041', 'Time Synchronization', '100', '8#0.0.1.0.0.255#2', 4);
insert into DICT_PROFILE (id, name, protocol_id, protocol_code, profile_type)
values ('10051', 'Standard Event Log', '100', '7#0.0.99.98.0.255#2', 5);
insert into DICT_PROFILE (id, name, protocol_id, protocol_code, profile_type)
values ('10052', 'Fraud Event Log', '100', '7#0.0.99.98.1.255#2', 5);
insert into DICT_PROFILE (id, name, protocol_id, protocol_code, profile_type)
values ('10004', 'Energy Billing Profile (Monthly)', '100', '7#0.0.98.1.0.255#2', 1);

insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.18.1.12.0.0.0.0.3.0.0.224.3.73.0', 40);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.18.1.12.0.0.0.0.4.0.0.224.3.73.0', 41);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '6.36.0.0', 1);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10051', '6.36.0.0', 1);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10051', '41.96.11.0', 2);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10052', '6.36.0.0', 1);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10052', '41.96.11.1', 2);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10053', '6.36.0.0', 1);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10053', '41.96.11.2', 2);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10054', '6.36.0.0', 1);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10054', '41.96.11.4', 2);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10054', '0.0.0.12.0.1.54.0.0.0.0.0.0.0.128.0.29.0', 3);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10054', '0.0.0.12.0.1.4.0.0.0.0.0.0.0.128.0.5.0', 6);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10054', '0.0.0.12.0.1.54.0.0.0.0.0.0.0.64.0.29.0', 4);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10054', '0.0.0.12.0.1.54.0.0.0.0.0.0.0.32.0.29.0', 5);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10054', '0.0.0.12.0.1.4.0.0.0.0.0.0.0.64.0.5.0', 7);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10054', '0.0.0.12.0.1.4.0.0.0.0.0.0.0.32.0.5.0', 8);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10001', '0.0.0.12.0.1.54.0.0.0.0.0.0.0.64.0.29.0', 11);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10001', '0.0.0.12.0.1.54.0.0.0.0.0.0.0.32.0.29.0', 12);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10001', '0.0.0.12.0.1.4.0.0.0.0.0.0.0.64.0.5.0', 14);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10001', '0.0.0.12.0.1.4.0.0.0.0.0.0.0.32.0.5.0', 15);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10055', '6.36.0.0', 1);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10055', '41.96.11.5', 2);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10056', '6.36.0.0', 1);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10056', '41.96.11.6', 2);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10001', '0.0.73.6.19.1.8.0.0.0.0.0.0.0.224.3.38.0', 7);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10001', '0.0.73.6.1.1.8.0.0.0.0.0.0.0.224.3.63.0', 8);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10001', '0.0.73.6.19.1.8.0.0.0.0.0.0.0.224.3.63.0', 9);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10001', '0.0.0.12.0.1.54.0.0.0.0.0.0.0.128.0.29.0', 10);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10001', '0.0.0.12.0.1.4.0.0.0.0.0.0.0.128.0.5.0', 13);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '6.36.0.0', 1);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.1.1.12.0.0.0.0.0.0.0.224.3.72.0', 2);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.1.1.12.0.0.0.0.1.0.0.224.3.72.0', 3);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.1.1.12.0.0.0.0.2.0.0.224.3.72.0', 4);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.1.1.12.0.0.0.0.3.0.0.224.3.72.0', 5);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.1.1.12.0.0.0.0.4.0.0.224.3.72.0', 6);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.19.1.12.0.0.0.0.0.0.0.224.3.72.0', 7);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.19.1.12.0.0.0.0.1.0.0.224.3.72.0', 8);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.19.1.12.0.0.0.0.2.0.0.224.3.72.0', 9);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.19.1.12.0.0.0.0.3.0.0.224.3.72.0', 10);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.19.1.12.0.0.0.0.4.0.0.224.3.72.0', 11);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.5.1.12.0.0.0.0.0.0.0.224.3.73.0', 12);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.5.1.12.0.0.0.0.1.0.0.224.3.73.0', 13);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.5.1.12.0.0.0.0.2.0.0.224.3.73.0', 14);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.5.1.12.0.0.0.0.3.0.0.224.3.73.0', 15);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.5.1.12.0.0.0.0.4.0.0.224.3.73.0', 16);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.13.1.12.0.0.0.0.0.0.0.224.3.73.0', 17);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.13.1.12.0.0.0.0.1.0.0.224.3.73.0', 18);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.13.1.12.0.0.0.0.2.0.0.224.3.73.0', 19);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.13.1.12.0.0.0.0.3.0.0.224.3.73.0', 20);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.13.1.12.0.0.0.0.4.0.0.224.3.73.0', 21);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.15.1.12.0.0.0.0.0.0.0.224.3.73.0', 22);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.15.1.12.0.0.0.0.1.0.0.224.3.73.0', 23);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.15.1.12.0.0.0.0.2.0.0.224.3.73.0', 24);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.15.1.12.0.0.0.0.3.0.0.224.3.73.0', 25);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.15.1.12.0.0.0.0.4.0.0.224.3.73.0', 26);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10001', '0.26.0.1.1.1.12.0.0.0.0.0.0.0.224.3.72.0', 2);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10001', '6.36.0.0', 1);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10001', '0.26.0.1.19.1.12.0.0.0.0.0.0.0.224.3.72.0', 3);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10001', '0.26.0.1.5.1.12.0.0.0.0.0.0.0.224.3.73.0', 4);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10001', '0.26.0.1.13.1.12.0.0.0.0.0.0.0.224.3.73.0', 5);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10001', '0.0.73.6.1.1.8.0.0.0.0.0.0.0.224.3.38.0', 6);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.16.1.12.0.0.0.0.0.0.0.224.3.73.0', 27);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.16.1.12.0.0.0.0.1.0.0.224.3.73.0', 28);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.16.1.12.0.0.0.0.2.0.0.224.3.73.0', 29);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.16.1.12.0.0.0.0.3.0.0.224.3.73.0', 30);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.16.1.12.0.0.0.0.4.0.0.224.3.73.0', 31);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.17.1.12.0.0.0.0.0.0.0.224.3.73.0', 32);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.17.1.12.0.0.0.0.1.0.0.224.3.73.0', 33);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.17.1.12.0.0.0.0.2.0.0.224.3.73.0', 34);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.17.1.12.0.0.0.0.3.0.0.224.3.73.0', 35);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.17.1.12.0.0.0.0.4.0.0.224.3.73.0', 36);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.18.1.12.0.0.0.0.0.0.0.224.3.73.0', 37);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.18.1.12.0.0.0.0.1.0.0.224.3.73.0', 38);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.18.1.12.0.0.0.0.2.0.0.224.3.73.0', 39);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.18.1.12.0.0.0.0.3.0.0.224.3.73.0', 40);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10002', '11.26.0.1.18.1.12.0.0.0.0.4.0.0.224.3.73.0', 41);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '6.36.0.0', 1);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.1.1.8.0.0.0.0.0.0.0.224.3.38.0', 2);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.1.1.8.0.0.0.0.0.1.0.224.3.38.0', 3);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.1.1.8.0.0.0.0.1.0.0.224.3.38.0', 4);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.1.1.8.0.0.0.0.1.1.0.224.3.38.0', 5);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.1.1.8.0.0.0.0.2.0.0.224.3.38.0', 6);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.1.1.8.0.0.0.0.2.1.0.224.3.38.0', 7);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.1.1.8.0.0.0.0.3.0.0.224.3.38.0', 8);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.1.1.8.0.0.0.0.4.0.0.224.3.38.0', 9);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.1.1.8.0.0.0.0.3.1.0.224.3.38.0', 10);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.1.1.8.0.0.0.0.4.1.0.224.3.38.0', 11);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.19.1.8.0.0.0.0.0.0.0.224.3.38.0', 12);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.19.1.8.0.0.0.0.0.1.0.224.3.38.0', 13);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.19.1.8.0.0.0.0.1.0.0.224.3.38.0', 14);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.19.1.8.0.0.0.0.1.1.0.224.3.38.0', 15);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.19.1.8.0.0.0.0.2.0.0.224.3.38.0', 16);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.19.1.8.0.0.0.0.2.1.0.224.3.38.0', 17);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.19.1.8.0.0.0.0.3.0.0.224.3.38.0', 18);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.19.1.8.0.0.0.0.3.1.0.224.3.38.0', 19);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.19.1.8.0.0.0.0.4.0.0.224.3.38.0', 20);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.19.1.8.0.0.0.0.4.1.0.224.3.38.0', 21);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.1.1.8.0.0.0.0.0.0.0.224.3.63.0', 22);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.1.1.8.0.0.0.0.0.1.0.224.3.63.0', 23);
commit;
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.1.1.8.0.0.0.0.1.0.0.224.3.63.0', 24);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.1.1.8.0.0.0.0.1.1.0.224.3.63.0', 25);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.1.1.8.0.0.0.0.2.0.0.224.3.63.0', 26);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.1.1.8.0.0.0.0.2.1.0.224.3.63.0', 27);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.1.1.8.0.0.0.0.3.0.0.224.3.63.0', 28);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.1.1.8.0.0.0.0.3.1.0.224.3.63.0', 29);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.1.1.8.0.0.0.0.4.0.0.224.3.63.0', 30);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.1.1.8.0.0.0.0.4.1.0.224.3.63.0', 31);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.19.1.8.0.0.0.0.0.0.0.224.3.63.0', 32);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.19.1.8.0.0.0.0.0.1.0.224.3.63.0', 33);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.19.1.8.0.0.0.0.1.0.0.224.3.63.0', 34);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.19.1.8.0.0.0.0.1.1.0.224.3.63.0', 35);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.19.1.8.0.0.0.0.2.0.0.224.3.63.0', 36);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.19.1.8.0.0.0.0.2.1.0.224.3.63.0', 37);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.19.1.8.0.0.0.0.3.0.0.224.3.63.0', 38);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.19.1.8.0.0.0.0.3.1.0.224.3.63.0', 39);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.19.1.8.0.0.0.0.4.0.0.224.3.63.0', 40);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10003', '13.8.0.6.19.1.8.0.0.0.0.4.1.0.224.3.63.0', 41);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.1.1.12.0.0.0.0.0.0.0.224.3.72.0', 2);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.1.1.12.0.0.0.0.1.0.0.224.3.72.0', 3);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.1.1.12.0.0.0.0.2.0.0.224.3.72.0', 4);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.1.1.12.0.0.0.0.3.0.0.224.3.72.0', 5);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.1.1.12.0.0.0.0.4.0.0.224.3.72.0', 6);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.19.1.12.0.0.0.0.0.0.0.224.3.72.0', 7);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.19.1.12.0.0.0.0.1.0.0.224.3.72.0', 8);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.19.1.12.0.0.0.0.2.0.0.224.3.72.0', 9);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.19.1.12.0.0.0.0.3.0.0.224.3.72.0', 10);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.19.1.12.0.0.0.0.4.0.0.224.3.72.0', 11);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.5.1.12.0.0.0.0.0.0.0.224.3.73.0', 12);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.5.1.12.0.0.0.0.1.0.0.224.3.73.0', 13);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.5.1.12.0.0.0.0.2.0.0.224.3.73.0', 14);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.5.1.12.0.0.0.0.3.0.0.224.3.73.0', 15);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.5.1.12.0.0.0.0.4.0.0.224.3.73.0', 16);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.13.1.12.0.0.0.0.0.0.0.224.3.73.0', 17);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.13.1.12.0.0.0.0.1.0.0.224.3.73.0', 18);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.13.1.12.0.0.0.0.2.0.0.224.3.73.0', 19);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.13.1.12.0.0.0.0.3.0.0.224.3.73.0', 20);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.13.1.12.0.0.0.0.4.0.0.224.3.73.0', 21);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.15.1.12.0.0.0.0.0.0.0.224.3.73.0', 22);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.15.1.12.0.0.0.0.1.0.0.224.3.73.0', 23);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.15.1.12.0.0.0.0.2.0.0.224.3.73.0', 24);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.15.1.12.0.0.0.0.3.0.0.224.3.73.0', 25);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.15.1.12.0.0.0.0.4.0.0.224.3.73.0', 26);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.16.1.12.0.0.0.0.0.0.0.224.3.73.0', 27);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.16.1.12.0.0.0.0.1.0.0.224.3.73.0', 28);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.16.1.12.0.0.0.0.2.0.0.224.3.73.0', 29);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.16.1.12.0.0.0.0.3.0.0.224.3.73.0', 30);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.16.1.12.0.0.0.0.4.0.0.224.3.73.0', 31);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.17.1.12.0.0.0.0.0.0.0.224.3.73.0', 32);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.17.1.12.0.0.0.0.1.0.0.224.3.73.0', 33);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.17.1.12.0.0.0.0.2.0.0.224.3.73.0', 34);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.17.1.12.0.0.0.0.3.0.0.224.3.73.0', 35);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.17.1.12.0.0.0.0.4.0.0.224.3.73.0', 36);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.18.1.12.0.0.0.0.0.0.0.224.3.73.0', 37);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.18.1.12.0.0.0.0.1.0.0.224.3.73.0', 38);
insert into DICT_PROFILE_DATA_ITEM (profile_id, dataitem_id, sort_id)
values ('10004', '13.26.0.1.18.1.12.0.0.0.0.2.0.0.224.3.73.0', 39);

insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (3,'Schedule.TimeSynchronizationRange','Time synchronization range (Unit: Second)','Integer','180','180','600',null,45);
insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Export.StartTime','Meter data/event export start time','Date','03:32:00',null,null,'HH:mm:ss',3);
insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Export.Cycle','Meter data/event export cycle (Unit: Hour)','Integer','4','3','12',null,4);
insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Data.StorageCycle','Meter data/event storage cycle (Unit: Month)','Integer','12','3','65535',null,6);
insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (2,'MQBus.IP','Message bus server IP address','String','127.0.0.1','1','1',null,1);
insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (2,'MQBus.Port','Message bus server listen port','Integer','61616','0','65535',null,2);
insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (1,'Channel.AssetReloadCycle','Service reload asset from database (Unit: Hour)','Integer','12','6','65535',null,3);
insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (1,'Channel.DLMS.ListenPort','DLMS meter/DCU listen port','Integer','9800','0','65535',null,1);
insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (1,'Channel.Hall.ListenPort','Hall service listen port','Integer','0','0','65535',null,2);
insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (3,'Schedule.AssetReloadCycle','Service reload asset from database (Unit: Hour)','Integer','12','6','65535',null,4);
insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (3,'Schedule.RequestRetryTimes','The times of resending request after timeout','Integer','1','1','2',null,3);
insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (3,'Schedule.RequestTimeout','Request timeout (Unit: Second)','Integer','60','30','120',null,2);
insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (3,'Schedule.Concurrency','The count of concurrency task','Integer','5000','1000','50000',null,1);
insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (4,'UCI.AssetReloadCycle','Service reload asset from database (Unit: Hour)','Integer','12','6','65535',null,4);
insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (4,'UCI.RequestRetryTimes','The times of resending request after timeout','Integer','1','1','3',null,3);
insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (4,'UCI.RequestTimeout','Request timeout (Unit: Second)','Integer','60','30','120',null,2);
insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (4,'UCI.Concurrency','The count of concurrency task','Integer','1000','1000','50000',null,1);
insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Task.Export','If execute export task','Enum','Enable',null,null,'Enable;Disable',1);
insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Task.Integrity','If execute integrity calculation task','Enum','Enable',null,null,'Enable;Disable',5);
insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Export.FtpUrl','Meter data/event export FTP server URL','String','ftp://127.0.0.1/ExportData',null,null,null,2);
insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (6,'Application.UCI.Interface.Url','UCI Interface service URL','String','http://127.0.0.1:8080/UCI-1.0-SNAPSHOT',null,null,null,1);
commit;

insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('User Login','Login',1);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Asset Managerment','Add Meter',1);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Collection Scheme Mgmt','Edit Scheme',2);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Collection Scheme Mgmt','Delete Scheme',3);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Meter Group Mgmt','Add Group',1);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Meter Group Mgmt','Edit Group',2);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Meter Group Mgmt','Delete Group',3);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Meter Configuration','Set Parameter',1);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Meter Data & Event Export','Edit Data Channel',1);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Firmware Upgrade','Add Plan',1);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Firmware Upgrade','Cancel Plan',2);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Firmware Upgrade','Cancel Job',3);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission','Add User',1);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission','Edit User',2);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission','Delete User',3);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Deployment & Cluster Mgmt','Edit Server',2);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Deployment & Cluster Mgmt','Delete Server',3);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission','Add Role',6);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission','Edit Role',7);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission','Delete Role',8);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission','Add Organization',9);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission','Delete Organization',11);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission','Edit Organization',10);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Deployment & Cluster Mgmt','Add Server',1);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Deployment & Cluster Mgmt','Add Service',4);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Deployment & Cluster Mgmt','Edit Service',5);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Deployment & Cluster Mgmt','Delete Service',6);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('User Login','Logout',2);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Asset Managerment','Add Communicator',4);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Asset Managerment','Edit Meter',2);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Asset Managerment','Delete Meter',3);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Asset Managerment','Edit Communicator',5);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Asset Managerment','Delete Communicator',6);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Collection Scheme Mgmt','Add Scheme',1);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Meter Group Upgrade','Add Plan',1);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Meter Group Upgrade','Cancel Plan',2);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Connect / Disconnect','Connect meter',1);
insert into DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Connect / Disconnect','Disconnect meter',2);

insert into SYS_DATAITEM_EXPORT (dataitem_id, dataitem_type) values ('11.26.0.1.1.1.12.0.0.0.0.0.0.0.224.3.72.0', 1);
insert into SYS_DATAITEM_EXPORT (dataitem_id, dataitem_type) values ('11.26.0.1.19.1.12.0.0.0.0.0.0.0.224.3.72.0', 1);
insert into SYS_DATAITEM_EXPORT (dataitem_id, dataitem_type) values ('11.26.0.1.5.1.12.0.0.0.0.0.0.0.224.3.73.0', 1);
insert into SYS_DATAITEM_EXPORT (dataitem_id, dataitem_type) values ('11.26.0.1.13.1.12.0.0.0.0.0.0.0.224.3.73.0', 1);
insert into SYS_DATAITEM_EXPORT (dataitem_id, dataitem_type) values ('13.26.0.1.1.1.12.0.0.0.0.0.0.0.224.3.72.0', 1);
insert into SYS_DATAITEM_EXPORT (dataitem_id, dataitem_type) values ('13.26.0.1.19.1.12.0.0.0.0.0.0.0.224.3.72.0', 1);
insert into SYS_DATAITEM_EXPORT (dataitem_id, dataitem_type) values ('13.26.0.1.5.1.12.0.0.0.0.0.0.0.224.3.73.0', 1);
insert into SYS_DATAITEM_EXPORT (dataitem_id, dataitem_type) values ('13.26.0.1.13.1.12.0.0.0.0.0.0.0.224.3.73.0', 1);
insert into SYS_DATAITEM_EXPORT (dataitem_id, dataitem_type) values ('3.26.0.285', 2);
insert into SYS_DATAITEM_EXPORT (dataitem_id, dataitem_type) values ('3.26.0.216', 2);
insert into SYS_DATAITEM_EXPORT (dataitem_id, dataitem_type) values ('3.12.29.39', 2);
insert into SYS_DATAITEM_EXPORT (dataitem_id, dataitem_type) values ('**********', 2);
insert into SYS_DATAITEM_EXPORT (dataitem_id, dataitem_type) values ('*********', 2);
insert into SYS_DATAITEM_EXPORT (dataitem_id, dataitem_type) values ('*********', 2);
insert into SYS_DATAITEM_EXPORT (dataitem_id, dataitem_type) values ('***********', 2);
insert into SYS_DATAITEM_EXPORT (dataitem_id, dataitem_type) values ('***********', 2);
insert into SYS_DATAITEM_EXPORT (dataitem_id, dataitem_type) values ('*********', 2);

insert into SYS_ORG (id, utility_id, name, description, parent_org_tid, org_code, org_type, mobile, fax, address, order_no)
values ('1', '1', 'Default Organization', 'Global', '0', '0006', null, '33399999', null, '', null);

insert into SYS_ROLE (id, utility_id, name, description)
values ('1', null, 'Operations', 'Operations & maintenance Role: read & write access to devices of all organizations and certain system features (including data read funtions) for project acceptance functional test purpose.');

insert into SYS_ROLE_MENU (role_id, menu_id, operation)
values ('1', '1e858f11beda11e79bb968f728c516f9', null);
insert into SYS_ROLE_MENU (role_id, menu_id, operation)
values ('1', '40422a45bedc11e79bb968f728c516f9', '1003001,1003002,1003003,1003004');
insert into SYS_ROLE_MENU (role_id, menu_id, operation)
values ('1', '40422c62bedc11e79bb968f728c516f9', null);
insert into SYS_ROLE_MENU (role_id, menu_id, operation)
values ('1', '40422339bedc11e79bb968f728c516f9', null);
insert into SYS_ROLE_MENU (role_id, menu_id, operation)
values ('1', '404227d7bedc11e79bb968f728c516f9', null);
insert into SYS_ROLE_MENU (role_id, menu_id, operation)
values ('1', '40422ec4bedc11e79bb968f728c516f9', '1005001,2f3f5d57ce86486780dd78218e374ba0,993d70819a8846dda3e4aa96bf296e31,e0532a62f3174e44b99d6b0a54d53559,eb11f69e1ff7440d91aa866742af765a');
insert into SYS_ROLE_MENU (role_id, menu_id, operation)
values ('1', '2e858f11beda11e79bb968f728c516f9', null);
insert into SYS_ROLE_MENU (role_id, menu_id, operation)
values ('1', '4057860cbedc11e79bb968f728c516f9', '1013001,31c5dd85ff2443eb93b20084ac44a715,34196b690b404d328f7ccec0a5f5c112,79ce62848407452e96f9832d61cf8feb');
insert into SYS_ROLE_MENU (role_id, menu_id, operation)
values ('1', '4057793ebedc11e79bb968f728c516f9', null);
insert into SYS_ROLE_MENU (role_id, menu_id, operation)
values ('1', '40577c5abedc11e79bb968f728c516f9', null);
insert into SYS_ROLE_MENU (role_id, menu_id, operation)
values ('1', '40577e4fbedc11e79bb968f728c516f9', null);
insert into SYS_ROLE_MENU (role_id, menu_id, operation)
values ('1', '3dd9ea8cbd4111e79bb968f728c516f9', null);
insert into SYS_ROLE_MENU (role_id, menu_id, operation)
values ('1', '40578a88bedc11e79bb968f728c516f9', null);
insert into SYS_ROLE_MENU (role_id, menu_id, operation)
values ('1', '40578a88bedc11e79bb968f728c51688', null);
insert into SYS_ROLE_MENU (role_id, menu_id, operation)
values ('1', '265ddc0cbeda11e79bb968f728c516f9', null);
insert into SYS_ROLE_MENU (role_id, menu_id, operation)
values ('1', '40578309bedc11e79bb968f728c516f9', null);
insert into SYS_ROLE_MENU (role_id, menu_id, operation)
values ('1', '40577ff3bedc11e79bb968f728c516f9', null);
insert into SYS_ROLE_MENU (role_id, menu_id, operation)
values ('1', '4057848ebedc11e79bb968f728c516f9', null);
insert into SYS_ROLE_MENU (role_id, menu_id, operation)
values ('1', '2dd2e5a5beda11e79bb968f728c516f9', null);
insert into SYS_ROLE_MENU (role_id, menu_id, operation)
values ('1', '40578c0dbedc11e79bb968f728c516f9', null);
insert into SYS_ROLE_MENU (role_id, menu_id, operation)
values ('1', '4057890cbedc11e79bb968f728c516f9', null);
insert into SYS_ROLE_MENU (role_id, menu_id, operation)
values ('1', '40578791bedc11e79bb968f728c516f9', null);

insert into SYS_SERVER (id, introduction, ip, ha_state, is_online)
values ('10010001', 'ClouESP HES Database Server', '************', 1, 1);
insert into SYS_SERVER (id, introduction, ip, ha_state, is_online)
values ('10010002', 'ClouESP HES AMR Server', '***********', 1, 1);
insert into SYS_SERVER (id, introduction, ip, ha_state, is_online)
values ('10010003', 'ClouESP HES Web Server', '************', 1, 1);

insert into SYS_SERVICE (id, introduction, host_id, service_type, is_online, server_id)
values ('20010006', 'Calculation', '0', 5, 1, '10010001');
insert into SYS_SERVICE (id, introduction, host_id, service_type, is_online, server_id)
values ('20010001', 'Channel', '1', 1, 1, '10010002');
insert into SYS_SERVICE (id, introduction, host_id, service_type, is_online, server_id)
values ('20010002', 'Schedule', '11', 3, 1, '10010002');
insert into SYS_SERVICE (id, introduction, host_id, service_type, is_online, server_id)
values ('20010003', 'UCI', '24', 4, 1, '10010002');
insert into SYS_SERVICE (id, introduction, host_id, service_type, is_online, server_id)
values ('20010004', 'Message Bus', '0', 2, 1, '10010002');
insert into SYS_SERVICE (id, introduction, host_id, service_type, is_online, server_id)
values ('20010005', 'Application', '0', 6, 1, '10010003');

insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010001','Channel.AssetReloadCycle','6');
insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010001','Channel.DLMS.ListenPort','9800');
insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010001','Channel.Hall.ListenPort','9801');
insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010002','Schedule.Concurrency','5000');
insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010002','Schedule.RequestTimeout','90');
insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010002','Schedule.RequestRetryTimes','0');
insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010002','Schedule.AssetReloadCycle','6');
insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010002','Schedule.TimeSynchronizationRange','180');
insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010003','UCI.Concurrency','5000');
insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010003','UCI.RequestTimeout','60');
insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010003','UCI.RequestRetryTimes','0');
insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010003','UCI.AssetReloadCycle','6');
insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010004','MQBus.IP','127.0.0.1');
insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010004','MQBus.Port','61616');
insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010005','Application.UCI.Interface.Url','http://127.0.0.1:8080/UCI-1.0-SNAPSHOT');
insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010006','Calculation.Task.Export','Enable');
insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010006','Calculation.Export.FtpUrl','ftp://127.0.0.1:21/Day Data File');
insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010006','Calculation.Export.StartTime','03:25:00');
insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010006','Calculation.Export.Cycle','4');
insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010006','Calculation.Data.StorageCycle','12');


insert into SYS_UTILITY (id, state, name, description)
values ('1', 1, 'Default Utility', 'Default Utility');

insert into SYS_VERSION (version_type, version_string, update_time)
values ('Application', '1.0', '2018-04-17 10:20:53');
insert into SYS_VERSION (version_type, version_string, update_time)
values ('Core', '1.0', '2018-04-17 10:20:53');
insert into SYS_VERSION (version_type, version_string, update_time)
values ('Database', '1.0', '2018-04-17 10:20:53');

insert INTO SYS_USER VALUES ('1', '1', '1', '1', 'Admin', 'admin', '9e5f9180802d96a05291d731fb8b3b72', null, null, null, '1', 0x1D86F16D242E6663, null, '0', '1', null);

update DICT_DATAITEM set SHOW_UNIT=1;
