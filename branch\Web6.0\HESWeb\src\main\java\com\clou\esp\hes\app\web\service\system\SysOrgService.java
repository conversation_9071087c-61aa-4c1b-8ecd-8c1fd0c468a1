/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysOrg{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-10-27 08:02:17
 * 最后修改时间：
 * 
*********************************************************************************************************/

package com.clou.esp.hes.app.web.service.system;

import java.util.List;

import com.clou.esp.hes.app.web.service.common.CommonService;
import com.clou.esp.hes.app.web.model.system.SysOrg;

public interface SysOrgService extends CommonService<SysOrg>{
	
	/**
	 * 根据父ID获取list，并且根据ORG_CODE逆序排序
	 * @Description 
	 * @param sysOrg
	 * @return List<SysOrg>
	 * <AUTHOR> 
	 * @Time 2018年3月22日 下午7:33:10
	 */
	public List<SysOrg> getListByparentOrgTid(SysOrg sysOrg);
	
	/**
	 * 修改子集下属数据的OrgCode信息, 查询子集下属数据，跟据OrgCode
	 * @param sysOrg
	 * @return
	 */
	public List<SysOrg> getListByOrgCode(SysOrg sysOrg);
}