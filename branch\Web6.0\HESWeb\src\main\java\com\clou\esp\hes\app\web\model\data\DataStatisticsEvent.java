/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataStatisticsEvent{ } 
 * 
 * 摘    要： dataStatisticsEvent
 * 版    本：1.0
 * 作    者：赵久霆
 * 创建于：2018-09-19 07:26:51
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.data;

import java.math.BigDecimal;
import java.util.List;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DataStatisticsEvent  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DataStatisticsEvent() {
	}
	/**eventId*/
	private java.lang.String eventId;
	/**eventName*/
	private java.lang.String eventName;
	/**tv*/
	private java.util.Date tv;
	/**percent*/
	private BigDecimal percent;
	/**countCurrent*/
	private BigDecimal countCurrent;
	/**tvType*/
	private java.lang.String tvType;
	/**tv*/
	private java.lang.String tvTime;
	/** orgId */
	private java.lang.String orgId;
	/** orgIds */
	private List orgIdList;
	
	public List getOrgIdList() {
		return orgIdList;
	}

	public void setOrgIdList(List orgIdList) {
		this.orgIdList = orgIdList;
	}

	/**
	 * eventName
	 * @return the value of DATA_STATISTICS_EVENT.EVENT_NAME
	 * @mbggenerated 2018-09-19 07:26:51
	 */
	public java.lang.String getEventName() {
		return MutiLangUtil.assemblyI18nData(MutiLangUtil.DATAITEM_I18N,eventName);
	}

	/**
	 * eventName
	 * @param eventName the value for DATA_STATISTICS_EVENT.EVENT_NAME
	 * @mbggenerated 2018-09-19 07:26:51
	 */
    public void setEventName(java.lang.String eventName) {
		this.eventName = eventName;
	}
    	
    	
	public java.lang.String getOrgId() {
		return orgId;
	}

	public void setOrgId(java.lang.String orgId) {
		this.orgId = orgId;
	}

	/**
	 * tv
	 * @return the value of DATA_STATISTICS_EVENT.TV
	 * @mbggenerated 2018-09-19 07:26:51
	 */
	public java.util.Date getTv() {
		return tv;
	}

	/**
	 * tv
	 * @param tv the value for DATA_STATISTICS_EVENT.TV
	 * @mbggenerated 2018-09-19 07:26:51
	 */
    	public void setTv(java.util.Date tv) {
    		this.tv = tv;
    	}
    	
    	
	public java.lang.String getTvTime() {
		return tvTime;
	}

	public void setTvTime(java.lang.String tvTime) {
		this.tvTime = tvTime;
	}
    
	
	public BigDecimal getPercent() {
		return percent;
	}

	public void setPercent(BigDecimal percent) {
		this.percent = percent;
	}

	/**
	 * tvType
	 * @return the value of DATA_STATISTICS_EVENT.TV_TYPE
	 * @mbggenerated 2018-09-19 07:26:51
	 */
	public java.lang.String getTvType() {
		return tvType;
	}

	/**
	 * tvType
	 * @param tvType the value for DATA_STATISTICS_EVENT.TV_TYPE
	 * @mbggenerated 2018-09-19 07:26:51
	 */
    	public void setTvType(java.lang.String tvType) {
		this.tvType = tvType;
	}
    	
    	

	public java.lang.String getEventId() {
		return eventId;
	}

	public void setEventId(java.lang.String eventId) {
		this.eventId = eventId;
	}

	
	public BigDecimal getCountCurrent() {
		return countCurrent;
	}

	public void setCountCurrent(BigDecimal countCurrent) {
		this.countCurrent = countCurrent;
	}

	public DataStatisticsEvent(java.lang.String eventName 
	,java.util.Date tv 
	,BigDecimal percent 
	,BigDecimal countCurrent 
	,java.lang.String tvType ) {
		super();
		this.eventName = eventName;
		this.tv = tv;
		this.percent = percent;
		this.countCurrent = countCurrent;
		this.tvType = tvType;
	}

}