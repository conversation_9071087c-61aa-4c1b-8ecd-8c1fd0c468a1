package com.clou.esp.hes.app.web.model.report;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.google.common.collect.Maps;
import com.power7000g.core.util.base.DateUtils;

public class MissDataReport extends BaseEntity {
	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    /**设备SN*/
	private java.lang.String serizlName;
	/**时间*/
	private java.util.Date times;
	/**时间*/
	private java.lang.String timeStr;

	/**数据值1*/
	private java.lang.String miss_value1;
	/**数据值2*/
	private java.lang.String miss_value2;
	/**数据值3*/
	private java.lang.String miss_value3;
	/**数据值4*/
	private java.lang.String miss_value4;
	/**数据值5*/
	private java.lang.String miss_value5;
	/**数据值6*/
	private java.lang.String miss_value6;
	/**数据值7*/
	private java.lang.String miss_value7;
	/**数据值8*/
	private java.lang.String miss_value8;
	/**数据值9*/
	private java.lang.String miss_value9;
	/**数据值10*/
	private java.lang.String miss_value10;
	/**数据值11*/
	private java.lang.String miss_value11;
	/**数据值12*/
	private java.lang.String miss_value12;
	/**数据值13*/
	private java.lang.String miss_value13;
	/**数据值14*/
	private java.lang.String miss_value14;
	/**数据值15*/
	private java.lang.String miss_value15;
	/**数据值16*/
	private java.lang.String miss_value16;
	/**数据值16*/
	private java.lang.String miss_value17;
	/**数据值16*/
	private java.lang.String miss_value18;
	/**数据值16*/
	private java.lang.String miss_value19;
	/**数据值16*/
	private java.lang.String miss_value20;
	/**数据值16*/
	private java.lang.String miss_value21;
	/**数据值16*/
	private java.lang.String miss_value22;
	/**数据值16*/
	private java.lang.String miss_value23;
	/**数据值16*/
	private java.lang.String miss_value24;
	/**数据值16*/
	private java.lang.String miss_value25;
	/**数据值16*/
	private java.lang.String miss_value26;
	/**数据值16*/
	private java.lang.String miss_value27;
	/**数据值16*/
	private java.lang.String miss_value28;
	/**数据值16*/
	private java.lang.String miss_value29;
	/**数据值16*/
	private java.lang.String miss_value30;
	/**数据值16*/
	private java.lang.String miss_value31;
	/**数据值16*/
	private java.lang.String miss_value32;
	/**数据值16*/
	private java.lang.String miss_value33;
	/**数据值16*/
	private java.lang.String miss_value34;
	/**数据值16*/
	private java.lang.String miss_value35;
	/**数据值16*/
	private java.lang.String miss_value36;
	/**数据值16*/
	private java.lang.String miss_value37;
	/**数据值16*/
	private java.lang.String miss_value38;
	/**数据值16*/
	private java.lang.String miss_value39;
	/**数据值16*/
	private java.lang.String miss_value40;
	/**数据值16*/
	private java.lang.String miss_value41;
	/**数据值16*/
	private java.lang.String miss_value42;
	/**数据值16*/
	private java.lang.String miss_value43;
	/**数据值16*/
	private java.lang.String miss_value44;
	/**数据值16*/
	private java.lang.String miss_value45;
	
	public java.lang.String getSerizlName() {
		return serizlName;
	}
	public void setSerizlName(java.lang.String serizlName) {
		this.serizlName = serizlName;
	}

	public java.lang.String getTimeStr() {
		return timeStr;
	}
	public void setTimeStr(java.lang.String timeStr) {
		this.timeStr = timeStr;
	}
	public java.util.Date getTimes() {
		return times;
	}
	public void setTimes(java.util.Date times) {
		this.times= times;
	}
	
	
	
	public java.lang.String getMiss_value1() {
		return miss_value1;
	}
	public void setMiss_value1(java.lang.String miss_value1) {
		this.miss_value1 = miss_value1;
	}
	public java.lang.String getMiss_value2() {
		return miss_value2;
	}
	public void setMiss_value2(java.lang.String miss_value2) {
		this.miss_value2 = miss_value2;
	}
	public java.lang.String getMiss_value3() {
		return miss_value3;
	}
	public void setMiss_value3(java.lang.String miss_value3) {
		this.miss_value3 = miss_value3;
	}
	public java.lang.String getMiss_value4() {
		return miss_value4;
	}
	public void setMiss_value4(java.lang.String miss_value4) {
		this.miss_value4 = miss_value4;
	}
	public java.lang.String getMiss_value5() {
		return miss_value5;
	}
	public void setMiss_value5(java.lang.String miss_value5) {
		this.miss_value5 = miss_value5;
	}
	public java.lang.String getMiss_value6() {
		return miss_value6;
	}
	public void setMiss_value6(java.lang.String miss_value6) {
		this.miss_value6 = miss_value6;
	}
	public java.lang.String getMiss_value7() {
		return miss_value7;
	}
	public void setMiss_value7(java.lang.String miss_value7) {
		this.miss_value7 = miss_value7;
	}
	public java.lang.String getMiss_value8() {
		return miss_value8;
	}
	public void setMiss_value8(java.lang.String miss_value8) {
		this.miss_value8 = miss_value8;
	}
	public java.lang.String getMiss_value9() {
		return miss_value9;
	}
	public void setMiss_value9(java.lang.String miss_value9) {
		this.miss_value9 = miss_value9;
	}
	public java.lang.String getMiss_value10() {
		return miss_value10;
	}
	public void setMiss_value10(java.lang.String miss_value10) {
		this.miss_value10 = miss_value10;
	}
	public java.lang.String getMiss_value11() {
		return miss_value11;
	}
	public void setMiss_value11(java.lang.String miss_value11) {
		this.miss_value11 = miss_value11;
	}
	public java.lang.String getMiss_value12() {
		return miss_value12;
	}
	public void setMiss_value12(java.lang.String miss_value12) {
		this.miss_value12 = miss_value12;
	}
	public java.lang.String getMiss_value13() {
		return miss_value13;
	}
	public void setMiss_value13(java.lang.String miss_value13) {
		this.miss_value13 = miss_value13;
	}
	public java.lang.String getMiss_value14() {
		return miss_value14;
	}
	public void setMiss_value14(java.lang.String miss_value14) {
		this.miss_value14 = miss_value14;
	}
	public java.lang.String getMiss_value15() {
		return miss_value15;
	}
	public void setMiss_value15(java.lang.String miss_value15) {
		this.miss_value15 = miss_value15;
	}
	public java.lang.String getMiss_value16() {
		return miss_value16;
	}
	public void setMiss_value16(java.lang.String miss_value16) {
		this.miss_value16 = miss_value16;
	}
	public java.lang.String getMiss_value17() {
		return miss_value17;
	}
	public void setMiss_value17(java.lang.String miss_value17) {
		this.miss_value17 = miss_value17;
	}
	public java.lang.String getMiss_value18() {
		return miss_value18;
	}
	public void setMiss_value18(java.lang.String miss_value18) {
		this.miss_value18 = miss_value18;
	}
	public java.lang.String getMiss_value19() {
		return miss_value19;
	}
	public void setMiss_value19(java.lang.String miss_value19) {
		this.miss_value19 = miss_value19;
	}
	public java.lang.String getMiss_value20() {
		return miss_value20;
	}
	public void setMiss_value20(java.lang.String miss_value20) {
		this.miss_value20 = miss_value20;
	}
	public java.lang.String getMiss_value21() {
		return miss_value21;
	}
	public void setMiss_value21(java.lang.String miss_value21) {
		this.miss_value21 = miss_value21;
	}
	public java.lang.String getMiss_value22() {
		return miss_value22;
	}
	public void setMiss_value22(java.lang.String miss_value22) {
		this.miss_value22 = miss_value22;
	}
	public java.lang.String getMiss_value23() {
		return miss_value23;
	}
	public void setMiss_value23(java.lang.String miss_value23) {
		this.miss_value23 = miss_value23;
	}
	public java.lang.String getMiss_value24() {
		return miss_value24;
	}
	public void setMiss_value24(java.lang.String miss_value24) {
		this.miss_value24 = miss_value24;
	}
	public java.lang.String getMiss_value25() {
		return miss_value25;
	}
	public void setMiss_value25(java.lang.String miss_value25) {
		this.miss_value25 = miss_value25;
	}
	public java.lang.String getMiss_value26() {
		return miss_value26;
	}
	public void setMiss_value26(java.lang.String miss_value26) {
		this.miss_value26 = miss_value26;
	}
	public java.lang.String getMiss_value27() {
		return miss_value27;
	}
	public void setMiss_value27(java.lang.String miss_value27) {
		this.miss_value27 = miss_value27;
	}
	public java.lang.String getMiss_value28() {
		return miss_value28;
	}
	public void setMiss_value28(java.lang.String miss_value28) {
		this.miss_value28 = miss_value28;
	}
	public java.lang.String getMiss_value29() {
		return miss_value29;
	}
	public void setMiss_value29(java.lang.String miss_value29) {
		this.miss_value29 = miss_value29;
	}
	public java.lang.String getMiss_value30() {
		return miss_value30;
	}
	public void setMiss_value30(java.lang.String miss_value30) {
		this.miss_value30 = miss_value30;
	}
	public java.lang.String getMiss_value31() {
		return miss_value31;
	}
	public void setMiss_value31(java.lang.String miss_value31) {
		this.miss_value31 = miss_value31;
	}
	public java.lang.String getMiss_value32() {
		return miss_value32;
	}
	public void setMiss_value32(java.lang.String miss_value32) {
		this.miss_value32 = miss_value32;
	}
	public java.lang.String getMiss_value33() {
		return miss_value33;
	}
	public void setMiss_value33(java.lang.String miss_value33) {
		this.miss_value33 = miss_value33;
	}
	public java.lang.String getMiss_value34() {
		return miss_value34;
	}
	public void setMiss_value34(java.lang.String miss_value34) {
		this.miss_value34 = miss_value34;
	}
	public java.lang.String getMiss_value35() {
		return miss_value35;
	}
	public void setMiss_value35(java.lang.String miss_value35) {
		this.miss_value35 = miss_value35;
	}
	public java.lang.String getMiss_value36() {
		return miss_value36;
	}
	public void setMiss_value36(java.lang.String miss_value36) {
		this.miss_value36 = miss_value36;
	}
	public java.lang.String getMiss_value37() {
		return miss_value37;
	}
	public void setMiss_value37(java.lang.String miss_value37) {
		this.miss_value37 = miss_value37;
	}
	public java.lang.String getMiss_value38() {
		return miss_value38;
	}
	public void setMiss_value38(java.lang.String miss_value38) {
		this.miss_value38 = miss_value38;
	}
	public java.lang.String getMiss_value39() {
		return miss_value39;
	}
	public void setMiss_value39(java.lang.String miss_value39) {
		this.miss_value39 = miss_value39;
	}
	public java.lang.String getMiss_value40() {
		return miss_value40;
	}
	public void setMiss_value40(java.lang.String miss_value40) {
		this.miss_value40 = miss_value40;
	}
	public java.lang.String getMiss_value41() {
		return miss_value41;
	}
	public void setMiss_value41(java.lang.String miss_value41) {
		this.miss_value41 = miss_value41;
	}
	public java.lang.String getMiss_value42() {
		return miss_value42;
	}
	public void setMiss_value42(java.lang.String miss_value42) {
		this.miss_value42 = miss_value42;
	}
	public java.lang.String getMiss_value43() {
		return miss_value43;
	}
	public void setMiss_value43(java.lang.String miss_value43) {
		this.miss_value43 = miss_value43;
	}
	public java.lang.String getMiss_value44() {
		return miss_value44;
	}
	public void setMiss_value44(java.lang.String miss_value44) {
		this.miss_value44 = miss_value44;
	}
	public java.lang.String getMiss_value45() {
		return miss_value45;
	}
	public void setMiss_value45(java.lang.String miss_value45) {
		this.miss_value45 = miss_value45;
	}
	public MissDataReport() {};
	
	public MissDataReport(String serizlName, Date times) {
		super();
		this.serizlName = serizlName;
		this.times = times;
		this.timeStr = DateUtils.date2Str(
				times,
				new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
	}

	public Map<String,String> getMap(SimpleDateFormat sdf ) {
		Map<String,String> map = Maps.newHashMap();
		map.put("serizlName", serizlName);
		map.put("times", sdf.format(times));
		map.put("miss_value1",miss_value1);
		map.put("miss_value2",miss_value2);
		map.put("miss_value3",miss_value3);
		map.put("miss_value4",miss_value4);
		map.put("miss_value5",miss_value5);
		map.put("miss_value6",miss_value6);
		map.put("miss_value7",miss_value7);
		map.put("miss_value8",miss_value8);
		map.put("miss_value9",miss_value9);
		map.put("miss_value10",miss_value10);
		map.put("miss_value11",miss_value11);
		map.put("miss_value12",miss_value12);
		map.put("miss_value13",miss_value13);
		map.put("miss_value14",miss_value14);
		map.put("miss_value15",miss_value15);
		map.put("miss_value16",miss_value16);
		map.put("miss_value17",miss_value17);
		map.put("miss_value18",miss_value18);
		map.put("miss_value19",miss_value19);
		map.put("miss_value20",miss_value20);
		map.put("miss_value21",miss_value21);
		map.put("miss_value22",miss_value22);
		map.put("miss_value23",miss_value23);
		map.put("miss_value24",miss_value24);
		map.put("miss_value25",miss_value25);
		map.put("miss_value26",miss_value26);
		map.put("miss_value27",miss_value27);
		map.put("miss_value28",miss_value28);
		map.put("miss_value29",miss_value29);
		map.put("miss_value30",miss_value30);
		map.put("miss_value31",miss_value31);
		map.put("miss_value32",miss_value32);
		map.put("miss_value33",miss_value33);
		map.put("miss_value34",miss_value34);
		map.put("miss_value35",miss_value35);
		map.put("miss_value36",miss_value36);
		map.put("miss_value37",miss_value37);
		map.put("miss_value38",miss_value38);
		map.put("miss_value39",miss_value39);
		map.put("miss_value40",miss_value40);
		map.put("miss_value41",miss_value41);
		map.put("miss_value42",miss_value42);
		map.put("miss_value43",miss_value43);
		map.put("miss_value44",miss_value44);
		map.put("miss_value45",miss_value45);
		return map;
	}
}
