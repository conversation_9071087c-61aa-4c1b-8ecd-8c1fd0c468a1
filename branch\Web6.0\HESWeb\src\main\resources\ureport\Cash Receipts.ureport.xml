<?xml version="1.0" encoding="UTF-8"?><ureport><cell expand="None" name="A1" row="1" col="1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Station Name]]></simple-value></cell><cell expand="None" name="B1" row="1" col="2"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Station Code]]></simple-value></cell><cell expand="None" name="C1" row="1" col="3"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Total Payment(GHC)]]></simple-value></cell><cell expand="Down" name="A2" row="2" col="1"><cell-style font-size="9" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="group" property="stationCode" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="B2" row="2" col="2"><cell-style font-size="9" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="group" property="stationName" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="C2" row="2" col="3"><cell-style font-size="9" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="group" property="totalPayment" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="A3" row="3" col="1"><cell-style font-size="9" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Total]]></simple-value></cell><cell expand="None" name="B3" row="3" col="2"><cell-style font-size="9" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="C3" row="3" col="3" top-cell="C1"><cell-style font-size="9" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="sum" property="totalPayment" order="none" mapping-type="simple"></dataset-value></cell><row row-number="1" height="19"/><row row-number="2" height="19"/><row row-number="3" height="19"/><column col-number="1" width="74"/><column col-number="2" width="80"/><column col-number="3" width="80"/><datasource name="cashReceiptsDataSource" type="spring" bean="cashReceiptsDataSource"><dataset name="report" type="bean" method="loadReportData" clazz="com.clou.esp.ppm.app.web.ureport.dataset.CashReceiptsDataSet"><field name="stationCode"/><field name="stationName"/><field name="totalPayment"/></dataset></datasource><paper type="A4" left-margin="90" right-margin="90"
    top-margin="72" bottom-margin="72" paging-mode="fitpage" fixrows="0"
    width="595" height="842" orientation="portrait" html-report-align="left" bg-image="" html-interval-refresh-value="0" column-enabled="false"></paper></ureport>