create table ASSET_METER_PREPARE
(
  id              VARCHAR2(32) not null,
  sn              VA<PERSON>HAR2(32) not null,
  name            <PERSON><PERSON><PERSON>R2(64),
  utility_id      VARCHAR2(32),
  org_id          VARCHAR2(32) not null,
  communicator_id VARCHAR2(32),
  model           <PERSON><PERSON><PERSON>R2(32) not null,
  manufacturer    VA<PERSON><PERSON>R2(32) not null,
  password        VARCHAR2(64) default 00000001,
  hls_ak          VARCHAR2(64) default 0000000000000000,
  hls_ek          VARCHAR2(64) default 0000000000000000,
  index_dcu       NUMBER(4) default 0,
  mac             VARCHAR2(32),
  com_type        VARCHAR2(32) not null,
  is_encrypt      VARCHAR2(3) default 1,
  auth_type       VARCHAR2(3) default 5,
  fw_version      VARCHAR2(32),
  remove_flag     NUMBER default 0,
  AD<PERSON>			  VARCHAR2(20),
  LONGITUDE		  NUMBER(10,4),
  LATITUDE		  NUMBER(10,4),
  COM_PORT		  NUMBER(4,0),
  CT			  NUMBER(4,0), 
  PT			  NUMBER(4,0),
  <PERSON><PERSON><PERSON>_FLAG		  NUMBER(4,0), 
  SRC_ADDR		  NUMBER(4,0),  
  primary key (id)
);
create index IDX_ASSET_METER_PREPARE_SN on ASSET_METER_PREPARE (SN);
create unique index IDX_ASSET_METER_PREPARE_MAC on ASSET_METER_PREPARE (MAC);