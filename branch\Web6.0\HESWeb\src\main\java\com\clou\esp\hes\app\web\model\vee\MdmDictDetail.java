/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class MdmDictDetail{ } 
 * 
 * 摘    要： MDM字典表
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2020-04-21 07:32:26
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.vee;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.common.BaseEntity;

import java.util.List;


public class MdmDictDetail extends BaseEntity {

	private static final long serialVersionUID = 4757708551152300062L;

	public MdmDictDetail() {}
	
	private 	String 		dictId;
	private 	String 		guiDisplayName;
	private 	String 		readme;
	private 	String 		innerValue;
	private		Integer     isDefault;
	private     Integer     orderIndex;
	
	private 	List<String> innerValueList;
	private 	List<String> dictIdList;
	
	
	public List<String> getInnerValueList() {
		return innerValueList;
	}

	public void setInnerValueList(List<String> innerValueList) {
		this.innerValueList = innerValueList;
	}

	public Integer getIsDefault() {
		return isDefault;
	}

	public void setIsDefault(Integer isDefault) {
		this.isDefault = isDefault;
	}

	public Integer getOrderIndex() {
		return orderIndex;
	}

	public void setOrderIndex(Integer orderIndex) {
		this.orderIndex = orderIndex;
	}

	public String getInnerValue() {
		return innerValue;
	}

	public void setInnerValue(String innerValue) {
		this.innerValue = innerValue;
	}

	public String getDictId() {
		return dictId;
	}

	public void setDictId(String dictId) {
		this.dictId = dictId;
	}

	
	public String getGuiDisplayName() {
		return MutiLangUtil.assemblyI18nData(MutiLangUtil.DICT_I18N, guiDisplayName);
	}

	public void setGuiDisplayName(String guiDisplayName) {
		this.guiDisplayName = guiDisplayName;
	}
	
	public String getReadme() {
		return readme;
	}

	public void setReadme(String readme) {
		this.readme = readme;
	}
	
	public MdmDictDetail(String guiDisplayName , String readme) {
		super();
		this.guiDisplayName = guiDisplayName;
		this.readme = readme;
	}

	public List<String> getDictIdList() {
		return dictIdList;
	}

	public void setDictIdList(List<String> dictIdList) {
		this.dictIdList = dictIdList;
	}

}