/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataMeterDataEnergyMinutely{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-24 03:20:08
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.data;

import java.util.List;
import java.util.Map;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;

import javax.annotation.Resource;

import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.dao.data.DataMdEnergyMinutelyDao;
import com.clou.esp.hes.app.web.model.asset.AssetMeasurementProfile;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroupMap;
import com.clou.esp.hes.app.web.model.data.DataIntegrityDetails;
import com.clou.esp.hes.app.web.model.data.DataMdEnergyMinutely;
import com.clou.esp.hes.app.web.service.asset.AssetMeasurementProfileService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterGroupMapService;
import com.clou.esp.hes.app.web.service.data.DataMdEnergyMinutelyService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@Component
@Service("dataMeterDataEnergyMinutelyService")
public class DataMdEnergyMinutelyServiceImpl  extends CommonServiceImpl<DataMdEnergyMinutely>  implements DataMdEnergyMinutelyService {

	@Resource
	private DataMdEnergyMinutelyDao 		dataMeterDataEnergyMinutelyDao;
	
	@Resource
	private AssetMeterGroupMapService 		assetMeterGroupMapService;
	
	@Resource
	private AssetMeasurementProfileService 	assetMeasurementProfileService;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dataMeterDataEnergyMinutelyDao);
    }
	@SuppressWarnings("rawtypes")
	public DataMdEnergyMinutelyServiceImpl() {}
	
	
	@Override
	public JqGridResponseTo getIntegrityDetailsForJqGrid(JqGridSearchTo jqGridSearchTo) {
		JqGridResponseTo j=null;
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
			String profileId=ResourceUtil.getSessionattachmenttitle("profileId");
			Map<String,Object>  jsonMap = jqGridSearchTo.getMap();
			String deviceId=(String)jsonMap.get("deviceId");
			String searchDate=(String)jsonMap.get("date");
			//找出采集频率   ASSET_METER_GROUP_MAP  ASSET_MEASUREMENT_PROFILE
			AssetMeterGroupMap group = new AssetMeterGroupMap();
			group.setId(deviceId);
			group.setType("1");
			AssetMeterGroupMap assetMeterGroupMap=	assetMeterGroupMapService.get(group);
			String groupId = assetMeterGroupMap.getGroupId();
			//找出曲线采集频率
			AssetMeasurementProfile profileTmp = new AssetMeasurementProfile();
			profileTmp.setMgId(groupId);
			profileTmp.setProfileId(profileId);
			AssetMeasurementProfile profile= assetMeasurementProfileService.get(profileTmp);
		
			Date startDate =sdf.parse(searchDate);
			Date endDate = DateUtils.addDays(startDate, 1);
			
			List<Date>  dates= Lists.newArrayList();
			List<Map<String,String>> listTmp = Lists.newArrayList();
			//组装时间数据
			String type=profile.getProfileCycleType().toLowerCase();
			int cycle=Integer.parseInt(profile.getProfileCycle());
			Date tmpDate= startDate;
			dates.add(tmpDate);
			if("minutely".equals(type)) {
				while(tmpDate.before(endDate)) {
					tmpDate=DateUtils.addMinutes(tmpDate, cycle);
					if(tmpDate.before(endDate)) {
						dates.add(tmpDate);	
					}
					
				}
			}else if("hourly".equals(type)) {
				while(tmpDate.before(endDate)) {
					tmpDate=DateUtils.addHours(tmpDate, cycle);
					if(tmpDate.before(endDate)) {
						dates.add(tmpDate);	
					}
				}
			}
			
			jsonMap.put("type", type);
			//查询meterName，model等信息
			jsonMap.put("date", searchDate.substring(0, 10));
			DataIntegrityDetails baseInfo=dataMeterDataEnergyMinutelyDao.getMeterBaseInfoById(deviceId);
			
			//List<DataIntegrityDetails> list = dataMeterDataEnergyMinutelyDao.getIntegrityDetailsForJqGrid(jqGridSearchTo);
			List<DataIntegrityDetails> list = dataMeterDataEnergyMinutelyDao.getDataIntegrityDetail(jqGridSearchTo);
			Map<Date,DataIntegrityDetails> dataMap = Maps.newHashMap();
			if(list!=null) {
				for(DataIntegrityDetails tmp:list) {
					dataMap.put(tmp.getTv(), tmp);
				}
			}
			
			for(Date date:dates) {
				String readStatus="0";
				if(dataMap.get(date)!=null) {
					readStatus="1";
				}
				DataIntegrityDetails detail = new DataIntegrityDetails(baseInfo.getSerizlNo(), date, 
						baseInfo.getManufacturer(),baseInfo.getModel(),baseInfo.getCommunication(),
						baseInfo.getProfile(), readStatus);
				Map<String,String> tempMap = detail.getMap(sdf);
				listTmp.add(tempMap);
			}
	
			j=JqGridHandler.GetJqGridPageJsonData(new PageInfo<DataIntegrityDetails>(), jqGridSearchTo);
			j.setRows(listTmp);
			
	//		List<DataIntegrityDetails> list = dataMeterDataEnergyMinutelyDao.getIntegrityDetailsForJqGrid(jqGridSearchTo);
	//		PageInfo<DataIntegrityDetails> pageInfo = new PageInfo<DataIntegrityDetails>(list);
	//		JqGridResponseTo j= JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
		
		} catch (ParseException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		return j;
	}
	
	@Override
	public List<DataIntegrityDetails> getDataIntegrityDetailList(DataIntegrityDetails dataIntegrityDetails) {
		List<DataIntegrityDetails> details = Lists.newArrayList();
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String profileId=ResourceUtil.getSessionattachmenttitle("profileId");
			String deviceId=(String) dataIntegrityDetails.getExtData().get("deviceId");
			String searchDate=(String) dataIntegrityDetails.getExtData().get("date");
			
			//找出采集频率   ASSET_METER_GROUP_MAP  ASSET_MEASUREMENT_PROFILE
			AssetMeterGroupMap group = new AssetMeterGroupMap();
			group.setId(deviceId);
			group.setType("0");
			AssetMeterGroupMap assetMeterGroupMap=	assetMeterGroupMapService.get(group);
			String groupId = assetMeterGroupMap.getGroupId();
			//找出曲线采集频率
			AssetMeasurementProfile profileTmp = new AssetMeasurementProfile();
			profileTmp.setMgId(groupId);
			profileTmp.setProfileId(profileId);
			AssetMeasurementProfile profile= assetMeasurementProfileService.get(profileTmp);
		
			Date startDate =sdf.parse(searchDate);
			Date endDate = DateUtils.addDays(startDate, 1);
			
			List<Date>  dates= Lists.newArrayList();
			List<Map<String,String>> listTmp = Lists.newArrayList();
			//组装时间数据
			String type=profile.getProfileCycleType().toLowerCase();
			int cycle=Integer.parseInt(profile.getProfileCycle());
			Date tmpDate= startDate;
			dates.add(tmpDate);
			if("minutely".equals(type)) {
				while(tmpDate.before(endDate)) {
					tmpDate=DateUtils.addMinutes(tmpDate, cycle);
					if(tmpDate.before(endDate)) {
						dates.add(tmpDate);	
					}
					
				}
			}else if("hourly".equals(type)) {
				while(tmpDate.before(endDate)) {
					tmpDate=DateUtils.addHours(tmpDate, cycle);
					if(tmpDate.before(endDate)) {
						dates.add(tmpDate);	
					}
				}
			}
			
			//查询meterName，model等信息
			DataIntegrityDetails baseInfo=dataMeterDataEnergyMinutelyDao.getMeterBaseInfoById(deviceId);
			List<DataIntegrityDetails> list = dataMeterDataEnergyMinutelyDao.getDataIntegrityDetailList(deviceId,searchDate.substring(0, 10));
			Map<Date,DataIntegrityDetails> dataMap = Maps.newHashMap();
			if(list!=null) {
				for(DataIntegrityDetails tmp:list) {
					dataMap.put(tmp.getTv(), tmp);
				}
			}
			
			for(Date date:dates) {
				String readStatus="0";
				if(dataMap.get(date)!=null) {
					readStatus="1";
				}
				DataIntegrityDetails detail = new DataIntegrityDetails(baseInfo.getSerizlNo(), date, 
						baseInfo.getManufacturer(),baseInfo.getModel(),baseInfo.getCommunication(),
						baseInfo.getProfile(), readStatus);
				details.add(detail);
			}
		}catch(Exception ex) {ex.printStackTrace();}
		return details;
	}
	
	
}