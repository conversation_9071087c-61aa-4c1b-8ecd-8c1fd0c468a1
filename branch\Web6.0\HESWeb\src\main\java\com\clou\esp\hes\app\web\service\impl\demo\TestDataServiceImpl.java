package com.clou.esp.hes.app.web.service.impl.demo;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.clou.esp.hes.app.web.dao.asset.AssetMeterDao;
import com.clou.esp.hes.app.web.dao.data.DataMdEnergyDailyDao;
import com.clou.esp.hes.app.web.dao.data.DataMdEnergyMinutelyDao;
import com.clou.esp.hes.app.web.dao.data.DataScheduleMissDataDao;
import com.clou.esp.hes.app.web.dao.data.DataScheduleProgressDao;
import com.clou.esp.hes.app.web.dao.demo.TestUserDao;
import com.clou.esp.hes.app.web.dao.dict.DictCommunicationTypeDao;
import com.clou.esp.hes.app.web.dao.dict.DictDeviceModelDao;
import com.clou.esp.hes.app.web.model.asset.AssetCommunicator;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.data.DataMeterEvent;
import com.clou.esp.hes.app.web.model.data.DataScheduleMissData;
import com.clou.esp.hes.app.web.model.data.DataScheduleProgress;
import com.clou.esp.hes.app.web.model.demo.TestUser;
import com.clou.esp.hes.app.web.model.dict.DictCommunicationType;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;
import com.clou.esp.hes.app.web.model.dict.DictDeviceModel;
import com.clou.esp.hes.app.web.service.asset.AssetCommunicatorService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.data.DataMeterEventService;
import com.clou.esp.hes.app.web.service.demo.TestDataService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.format.FormatUtil;
import com.power7000g.core.util.uuid.UUIDGenerator;

@Component
@Service("testDataService")
public class TestDataServiceImpl extends CommonServiceImpl<TestUser> implements
		TestDataService {

	@Resource
	private TestUserDao testUserDao;

	@Resource
	private DictDeviceModelDao dictDeviceModelDao;
	@Resource
	private DictCommunicationTypeDao dictCommunicationTypeDao;
	@Resource
	private AssetCommunicatorService assetCommunicatorService;
	@Resource
	private AssetMeterService assetMeterService;
	@Resource
	private AssetMeterDao assetMeterDao;
	@Resource
	private DataMdEnergyDailyDao dataMeterDataEnergyDailyDao;
	@Resource
	private DataMdEnergyMinutelyDao dataMeterDataEnergyMinutelyDao;
	@Resource
	private DataScheduleMissDataDao dataScheduleMissDataDao;
	@Resource
	private DataScheduleProgressDao dataScheduleProgressDao;
	@Resource
	private DictDataitemService dictDataitemService;
	@Resource
	private DataMeterEventService dataMeterEventService;

	@Autowired
	public void setCommonService() {
		super.setCommonService(testUserDao);
	}

	public TestDataServiceImpl() {
	}

	@Override
	public void insertMeterEventTestData() {
		// 查询200只电表循环插入就好了；
		// dataitem
		List<AssetMeter> list = assetMeterDao.getListByRows(200);
		int size = list.size();
		if (list == null || size == 0) {
			return;
		}
		List<DictDataitem> ddList = dictDataitemService.getAllList();
		int ddSize = ddList.size();
		if (ddList == null || ddSize == 0) {
			return;
		}
		Date d = new Date();
		Random r = new Random();
		StringBuffer sql = new StringBuffer();
		StringBuffer osql = new StringBuffer();
		List<DataMeterEvent> eList = new ArrayList<DataMeterEvent>();
		for (int i = 0; i < size; i++) {
			AssetMeter m = list.get(i);
			DataMeterEvent e = new DataMeterEvent();
			e.setDeviceId(m.getId());
			e.setTv(d);
			e.setEventId(ddList.get(r.nextInt(ddSize)).getId());
			eList.add(e);
			sql.append("('" + e.getDeviceId()
					+ "','" + DateUtils.date2Str(d, DateUtils.datetimeFormat)
					+ "','" + e.getEventId() + "'),");
			osql.append(" INTO DATA_METER_EVENT(DEVICE_ID,DEVICE_TYPE,TV,EVENT_ID)");
			osql.append(" VALUES('" + e.getDeviceId() + "',to_date('"
					+ DateUtils.date2Str(d, DateUtils.datetimeFormat)
					+ "','yyyy-mm-dd,hh24:mi:ss'),'" + e.getEventId() + "')");
			if (i >= 199) {
				osql.append("SELECT 1 FROM DUAL");
				String ssql = sql.toString();
				String ossql = osql.toString();
				if (StringUtil.isNotEmpty(ssql)) {
					ssql = ssql.substring(0, ssql.length() - 1);
				}
				// dataMeterEventService.batchSaves(ssql, ossql);
				dataMeterEventService.batchInsert(eList);
				break;
			}
		}

	}

	@Transactional
	public void insertMeterProgressMissData() {
		long start = System.currentTimeMillis();
		DataScheduleProgress sp = new DataScheduleProgress();
		dataScheduleProgressDao.delete(sp);
		dataScheduleProgressDao.batchInsert("");
		System.out.println("总耗时===" + (System.currentTimeMillis() - start));
	}

	@Override
	public void insertMeterTestData() {
		// 查询出所有电表数据
		// 便利电表
		// 1000条电表数据调用数据库插入一次
		// 计算出插入时间*100
		long stat = System.currentTimeMillis();
		/*
		 * List<AssetMeter> list = assetMeterService.getAllList(); int size =
		 * list.size(); if (list == null || size == 0) { return; } double dvalue
		 * = 0.0; double mvalue = 0.0; StringBuffer sqlm = new StringBuffer();
		 * StringBuffer sqld = new StringBuffer();
		 * 
		 * StringBuffer osqlm = new StringBuffer(); StringBuffer osqld = new
		 * StringBuffer(); for (int i = 1; i <= size; i++) { AssetMeter m =
		 * list.get(i - 1); if (StringUtil.isNotEmpty(m.getDvalue())) { dvalue =
		 * Double.parseDouble(m.getDvalue()); } if
		 * (StringUtil.isNotEmpty(m.getMvalue())) { mvalue =
		 * Double.parseDouble(m.getMvalue()); } sqld.append("('" + m.getId() +
		 * "',date_format(now(),'%Y-%m-%d 00:00:00'),'" + (dvalue + 1.1) + "','"
		 * + (dvalue + 1.2) + "','" + (dvalue + 1.3) + "','" + (dvalue + 1.4) +
		 * "'),"); osqld.append(
		 * " INTO DATA_MD_ENERGY_DAILY(DEVICE_ID,TV,VALUE1,VALUE2,VALUE3,VALUE4) "
		 * ); osqld.append(" VALUES('" + m.getId() +
		 * "',to_date ( TO_CHAR( sysdate,'yyyy-mm-dd') , 'yyyy-mm-dd' )," +
		 * (dvalue + 1.1) + "," + (dvalue + 1.2) + "," + (dvalue + 1.3) + "," +
		 * (dvalue + 1.4) + ")"); for (int j = 0; j < 24; j++) {
		 * sqlm.append("('" + m.getId() +
		 * "',date_add(date_format(now(),'%Y-%m-%d 00:00:00'), interval " + j +
		 * " hour),'" + (mvalue + 1.1 + j) + "','" + (mvalue + 1.2 + j) + "','"
		 * + (mvalue + 1.3 + j) + "','" + (mvalue + 1.4 + j) + "'),");
		 * osqlm.append(
		 * " INTO DATA_MD_ENERGY_MINUTELY (DEVICE_ID,TV,VALUE1,VALUE2,VALUE3,VALUE4) "
		 * ); osqlm.append(" VALUES('" + m.getId() +
		 * "',TO_DATE(TO_CHAR( sysdate,'yyyy-mm-dd')||' "+String.format("%0" + 2
		 * + "d", j)+":00:00','YYYY-MM-DD HH24:MI:SS'),'" + (mvalue + 1.1 + j) +
		 * "','" + (mvalue + 1.2 + j) + "','" + (mvalue + 1.3 + j) + "','" +
		 * (mvalue + 1.4 + j) + "')"); } if (i == size) {
		 * osqld.append("SELECT 1 FROM DUAL");
		 * osqlm.append("SELECT 1 FROM DUAL"); String ssqld = sqld.toString();
		 * String ssqlm = sqlm.toString(); String ossqld = osqld.toString();
		 * String ossqlm = osqlm.toString(); if (StringUtil.isNotEmpty(ssqld)) {
		 * ssqld = ssqld.substring(0, ssqld.length() - 1); } if
		 * (StringUtil.isNotEmpty(ssqlm)) { ssqlm = ssqlm.substring(0,
		 * ssqlm.length() - 1); }
		 * dataMeterDataEnergyDailyDao.batchInsert(ssqld,ossqld);
		 * dataMeterDataEnergyMinutelyDao.batchInsert(ssqlm,ossqlm); sqlm = new
		 * StringBuffer(); sqld = new StringBuffer(); osqlm = new
		 * StringBuffer(); osqld = new StringBuffer(); break; } if (i % 1000 ==
		 * 0) { osqld.append("SELECT 1 FROM DUAL");
		 * osqlm.append("SELECT 1 FROM DUAL"); String ssqld = sqld.toString();
		 * String ssqlm = sqlm.toString(); String ossqld = osqld.toString();
		 * String ossqlm = osqlm.toString(); if (StringUtil.isNotEmpty(ssqld)) {
		 * ssqld = ssqld.substring(0, ssqld.length() - 1); } if
		 * (StringUtil.isNotEmpty(ssqlm)) { ssqlm = ssqlm.substring(0,
		 * ssqlm.length() - 1); }
		 * dataMeterDataEnergyDailyDao.batchInsert(ssqld,ossqld);
		 * dataMeterDataEnergyMinutelyDao.batchInsert(ssqlm,ossqlm); sqlm = new
		 * StringBuffer(); sqld = new StringBuffer(); osqlm = new
		 * StringBuffer(); osqld = new StringBuffer(); } }
		 */
		String dateStr = DateUtils.formatDate("yyyy-MM-dd");
		dataMeterDataEnergyDailyDao.batchInsert(dateStr);
		for (int i = 0; i < 24; i++) {
			dataMeterDataEnergyMinutelyDao.batchInsert(dateStr,
					" " + String.format("%0" + 2 + "d", i));
		}
		/*
		 * for(int j=1;j<=1;j++){
		 * dataMeterDataEnergyDailyDao.batchInsert("2017-12-"+String.format("%0"
		 * + 2 + "d", j)); for(int i=0;i<24;i++){
		 * dataMeterDataEnergyMinutelyDao.
		 * batchInsert("2017-12-"+String.format("%0" + 2 + "d",
		 * j)," "+String.format("%0" + 2 + "d", i)); } }
		 */
		System.out.println("插入成功！耗时===" + (System.currentTimeMillis() - stat));
	}

	@Override
	public void createCommunicatorMeter(String meterModel, String mcommType,
			int meterCount, String communicatorModel, String ccommType,
			int communicatorCount) {
		if (communicatorCount <= 0) {
			return;
		}
		if (StringUtil.isEmpty(communicatorModel)) {
			return;
		}
		DictDeviceModel cddm = new DictDeviceModel();
		cddm.setName(communicatorModel);
		cddm = dictDeviceModelDao.get(cddm);
		if (cddm == null) {
			return;
		}
		if (StringUtil.isEmpty(ccommType)) {
			return;
		}
		DictCommunicationType cdct = new DictCommunicationType();
		cdct.setName(ccommType);
		cdct = dictCommunicationTypeDao.get(cdct);
		if (cdct == null) {
			return;
		}

		if (meterCount <= 0) {
			return;
		}
		if (StringUtil.isEmpty(meterModel)) {
			return;
		}
		DictDeviceModel mddm = new DictDeviceModel();
		mddm.setName(meterModel);
		mddm = dictDeviceModelDao.get(mddm);
		if (mddm == null) {
			return;
		}
		if (StringUtil.isEmpty(mcommType)) {
			return;
		}
		DictCommunicationType mdct = new DictCommunicationType();
		mdct.setName(mcommType);
		mdct = dictCommunicationTypeDao.get(mdct);
		if (mdct == null) {
			return;
		}

		List<AssetCommunicator> cList = new ArrayList<AssetCommunicator>();
		List<AssetMeter> meterList = new ArrayList<AssetMeter>();
		boolean flag = false;
		for (int i = 1; i <= communicatorCount; i++) {
			String random = DateUtils.date2Str(new Date(),
					new SimpleDateFormat("HHmmssSSS")) + (new Random().nextInt(89) + 10);
			AssetCommunicator c = new AssetCommunicator();
			c.setSn("sn" + random);
			c.setName("name" + random);
			c.setUtilityId("29018328bd4011e79bb968f728c516f9");
			c.setOrgId("bbcaf9b4bd4011e79bb968f728c516f9");

			c.setModel("101003");
			c.setManufacturer("101");
			c.setNetworkIp("************");
			c.setNetworkPort(5060);
			c.setPass("123456");

			c.setDeviceType(202);
			c.setMac("mac" + random);
			c.setComType("103");
			
			String cid = UUIDGenerator.generate();
			c.setId(cid);
			cList.add(c);
			if (meterCount == 1) {
				if (i == communicatorCount) {
					assetCommunicatorService.batchInsert(cList);
					cList.clear();
					flag = true;
				}
				if (i % 2000 == 0 && i != communicatorCount) {
					assetCommunicatorService.batchInsert(cList);
					cList.clear();
					flag = true;
				}
			} else {
				if (i == communicatorCount) {
					assetCommunicatorService.batchInsert(cList);
					cList.clear();
					flag = true;
				}
				if (i % 10 == 0 && i != communicatorCount) {
					assetCommunicatorService.batchInsert(cList);
					cList.clear();
					flag = true;
				}
			}
			// assetCommunicatorService.save(c);

			for (int j = 0; j < meterCount; j++) {
				AssetMeter meter = new AssetMeter();
				meter.setId(UUIDGenerator.generate());
				String mrandom = DateUtils.date2Str(new Date(),
						new SimpleDateFormat("HHmmssSSS")) + (new Random().nextInt(89) + 10);
				meter.setSn("sn" + mrandom);
				meter.setName("name" + mrandom);
				meter.setUtilityId("29018328bd4011e79bb968f728c516f9");
				meter.setOrgId("bbcaf9b4bd4011e79bb968f728c516f9");
				meter.setCommunicatorId(cid);
				
				meter.setModel("101001");
				meter.setManufacturer("101");
				meter.setPassword("123456");
				meter.setHlsAk("D0D1D2D3D4D5D6D7D8D9DADBDCDDDEDF");
				meter.setHlsEk("000102030405060708090A0B0C0D0E0F");
				meter.setIndexDcu(j);
				meter.setMac("mac" + mrandom);
				meter.setComType("201");
				meterList.add(meter);
				// assetMeterService.save(m);
			}
			if (flag) {
				assetMeterService.batchInsert(meterList);
				meterList.clear();
				flag = false;
			}
		}

	}

	@Override
	public void saveMeterProgressMissData() {
		long start = System.currentTimeMillis();
		List<AssetMeter> list = assetMeterService.getAllList();
		System.out.println("总耗时===" + (System.currentTimeMillis() - start));
		int size = list.size();
		if (list == null || size == 0) {
			return;
		}
		Random random = new Random();
		DataScheduleProgress sp = new DataScheduleProgress();
		Date d = new Date();
		Date md = FormatUtil.addDaysToDate(d, -1);
		dataScheduleMissDataDao.deleteByTv(FormatUtil.dateToStr(md,
				"yyyy-MM-dd"));
		dataScheduleProgressDao.delete(sp);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
		int hours = 0;
		List<DataScheduleProgress> pList = new ArrayList<DataScheduleProgress>();
		List<DataScheduleMissData> dsmList = new ArrayList<DataScheduleMissData>();
		int count = 1;
		for (int i = 1; i <= size; i++) {
			AssetMeter m = list.get(i - 1);
			DataScheduleProgress dsp = new DataScheduleProgress();
			dsp.setDeviceId(m.getId());
			dsp.setProfileId("10001");
			int days = 0;
			if (i >= 98200) {
				if (random.nextInt(100) <= 50) {
					hours = -random.nextInt(12);
				} else {
					hours = random.nextInt(23);
				}
				days = -1;
			}
			Date tv = FormatUtil
					.addDaysToDate(
							(FormatUtil.addHoursToDate(
									DateUtils.str2Date(
											DateUtils.date2Str(d, sdf), sdf),
									hours)), days);
			dsp.setTv(tv);

			if (i >= 98200) {
				int missCount = FormatUtil.hoursBetween(tv, md);
				if (missCount > 24) {
					missCount = 24;
				}
				if (missCount > 0) {
					missCount = random.nextInt(missCount);
				}
				if (missCount > 0) {
					for (int j = 0; j < missCount; j++) {
						DataScheduleMissData dsm = new DataScheduleMissData();
						dsm.setDeviceId(m.getId());
						dsm.setProfileId("1");
						dsm.setTv(FormatUtil.addHoursToDate(DateUtils.str2Date(
								DateUtils.date2Str(md, sdf), sdf), j));
						dsmList.add(dsm);
					}
					count++;
				}
				if (i == size) {
					if (dsmList != null && dsmList.size() > 0) {
						dataScheduleMissDataDao.batchSave(dsmList);
						dsmList.clear();
					}
				}
				if (i != size && count % 300 == 0) {
					if (dsmList != null && dsmList.size() > 0) {
						dataScheduleMissDataDao.batchSave(dsmList);
						dsmList.clear();
					}
				}
			}
			dsp.setLastTaskTv(d);
			int result = random.nextInt(100);
			if (result <= 2) {
				dsp.setTaskState("0");
				dsp.setFailedInfo("Network anomalies");
			} else {
				dsp.setTaskState("1");
				dsp.setFailedInfo("");
			}
			pList.add(dsp);
			if (i == size) {
				if (pList != null && pList.size() > 0) {
					dataScheduleProgressDao.batchSave(pList);
					pList.clear();
				}
			}
			if (i != size && i % 3000 == 0) {
				if (pList != null && pList.size() > 0) {
					dataScheduleProgressDao.batchSave(pList);
					pList.clear();
				}
			}
		}

		// d = FormatUtil.addDaysToDate(d, -1);
		// int count = 1;
		// dataScheduleMissDataDao.deleteByTv(FormatUtil
		// .dateToStr(d, "yyyy-MM-dd"));
		// for (int i = size; i >= 0; i--) {
		// AssetMeter m = list.get(i - 1);
		// int probability = random.nextInt(100); // 5%一点也获取不到的; // 10%获取到
		// // 19-20; // 10%获取到4-5; //
		// // 75%百分百获取;
		// int missCount = 0;
		// if (i >= 98200) {
		// if (probability <= 30) {
		// missCount = 24;
		// } else if (probability > 30 && probability <= 60) {
		// missCount = random.nextInt(4) + 1;
		// } else if (probability > 60 && probability <= 100) {
		// missCount = random.nextInt(4) + 19;
		// }
		// }
		// if (missCount > 0) {
		// for (int j = 0; j < missCount; j++) {
		// DataScheduleMissData dsm = new DataScheduleMissData();
		// dsm.setDeviceId(m.getId());
		// dsm.setProfileId("1");
		// dsm.setTv(FormatUtil.addHoursToDate(
		// DateUtils.str2Date(DateUtils.date2Str(d, sdf), sdf),
		// j));
		// dsmList.add(dsm);
		// }
		// count++;
		// }
		// if (i <= 98200) {
		// if (dsmList != null && dsmList.size() > 0) {
		// dataScheduleMissDataDao.batchSave(dsmList);
		// dsmList.clear();
		// }
		// break;
		// }
		// if (count % 300 == 0) {
		// if (dsmList != null && dsmList.size() > 0) {
		// dataScheduleMissDataDao.batchSave(dsmList);
		// dsmList.clear();
		// }
		// continue;
		// }
		//
		// }

		System.out.println("总耗时===" + (System.currentTimeMillis() - start));
	}
}