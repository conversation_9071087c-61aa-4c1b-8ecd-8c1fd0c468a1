package com.clou.esp.hes.app.web.listener.system;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.ServletContext;

import org.apache.commons.io.IOUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.web.context.WebApplicationContext;

import com.bstek.ureport.exception.ReportException;
import com.bstek.ureport.provider.report.ReportFile;
import com.bstek.ureport.provider.report.ReportProvider;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.model.report.AssetReportTemplate;
import com.clou.esp.hes.app.web.service.report.AssetReportTemplateService;
/**
 * <AUTHOR>
 * @since 2019年3月21日
 */
public class FileReportProvider implements ReportProvider,ApplicationContextAware{
    private String prefix="file:";
    private String fileStoreDir =ResourceUtil.getSessionattachmenttitle("hes.ureport.fileStoreDir");
    private boolean disabled;
    private String selfDir = "";
 	@Resource
    private AssetReportTemplateService assetReportTemplateService;
 	
    @Override
    public InputStream loadReport(String file) {
        if(file.startsWith(prefix)){
            file=file.substring(prefix.length(),file.length());
        }
        
        String absolutionPath = new File(this.getClass().getResource(fileStoreDir).getPath()).getAbsolutePath();
        String fullPath=absolutionPath+"/"+file;
        try {
            return new FileInputStream(fullPath);
        } catch (FileNotFoundException e) {
            throw new ReportException(e);
        }
    }
     
    @Override
    public void deleteReport(String file) {
        if(file.startsWith(prefix)){
            file=file.substring(prefix.length(),file.length());
        }
        
        String absolutionPath = new File(this.getClass().getResource(fileStoreDir).getPath()).getAbsolutePath();
        String fullPath=absolutionPath+"/"+file;
        File f=new File(fullPath);
        if(f.exists()){
            if(f.delete()){
            	 AssetReportTemplate assetReportTemplate = new AssetReportTemplate();
                 assetReportTemplate.setTemplateFile(file);
                 List<AssetReportTemplate> lists = assetReportTemplateService.getList(assetReportTemplate);
                 for(AssetReportTemplate assetReportTemplate1 : lists){
                 	 assetReportTemplateService.delete(assetReportTemplate1);
                 }
            }
        }
    }
    @Override
    public List<ReportFile> getReportFiles() {
    	String absolutionPath = new File(this.getClass().getResource(fileStoreDir).getPath()).getAbsolutePath();
        File file=new File(absolutionPath);
        List<ReportFile> list=new ArrayList<ReportFile>();
        for(File f:file.listFiles()){
            Calendar calendar=Calendar.getInstance();
            calendar.setTimeInMillis(f.lastModified());
            list.add(new ReportFile(f.getName(),calendar.getTime()));
        }
        Collections.sort(list, new Comparator<ReportFile>(){
            @Override
            public int compare(ReportFile f1, ReportFile f2) {
                return f2.getUpdateDate().compareTo(f1.getUpdateDate());
            }
        });
        return list;
    }
    @Override
    public String getName() {
        return "Server File System";
    }
     
    @Override
    public void saveReport(String file,String content) {
        if(file.startsWith(prefix)){
            file=file.substring(prefix.length(),file.length());
        }
        String absolutionPath = new File(this.getClass().getResource(fileStoreDir).getPath()).getAbsolutePath();
        String fullPath=absolutionPath+"/"+file;
        FileOutputStream outStream=null;
        try{
            outStream=new FileOutputStream(new File(fullPath));
            IOUtils.write(content, outStream,"utf-8");
        }catch(Exception ex){
            throw new ReportException(ex);
        }finally{
            if(outStream!=null){
                try {
                    outStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
         
    }
    @Override
    public boolean disabled() {
        return disabled;
    }
     
    public void setDisabled(boolean disabled) {
        this.disabled = disabled;
    }
     
    public void setFileStoreDir(String fileStoreDir) {
        this.fileStoreDir = fileStoreDir;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        String absolutionPath = new File(this.getClass().getResource(fileStoreDir).getPath()).getAbsolutePath();
        File file=new File(absolutionPath);
        if(file.exists()){

            return;
        }
        if(applicationContext instanceof WebApplicationContext){
            WebApplicationContext context=(WebApplicationContext)applicationContext;
            ServletContext servletContext=context.getServletContext();
            String basePath=servletContext.getRealPath("/");
            fileStoreDir=basePath+fileStoreDir;
            file=new File(fileStoreDir);
            if(!file.exists()){
                file.mkdirs();
            }
        }
    }
    @Override
    public String getPrefix() {
        return prefix;
    }
}