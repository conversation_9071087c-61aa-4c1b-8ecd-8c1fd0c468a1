  CREATE TABLE PPM_ASSET_METER 
   (	ID VARCHAR2(32 BYTE), 
	STS_MAC VARCHAR2(20 BYTE), 
	SGC VARCHAR2(20 BYTE), 
	DEPLOY_TV DATE, 
	PAYMENT_TYPE NUMBER(4,0) DEFAULT 1
   ) ;

   CREATE TABLE PPM_ASSET_METER_CHANGE 
   (	CUSTOMER_ID VARCHAR2(32 BYTE), 
	TIME_TAG DATE, 
	OLD_METER_ID VARCHAR2(32 BYTE), 
	NEW_METER_ID VARCHAR2(32 BYTE), 
	OLD_SERIAL_NUMBER VARCHAR2(32 BYTE), 
	NEW_SERIAL_NUMBER VARCHAR2(32 BYTE), 
	DATA_SOURCE VARCHAR2(20 BYTE), 
	OLD_COMBINED_ACTIVE_ENERGY NUMBER(18,4) DEFAULT '0.0000', 
	OLD_TOTAL_IMPORT_ACTIVE NUMBER(18,4) DEFAULT NULL, 
	OLD_TOTAL_EXPORT_ACTIVE NUMBER(18,4) DEFAULT NULL, 
	OLD_TOTAL_IMPORT_REACTIVE NUMBER(18,4) DEFAULT NULL, 
	OLD_TOTAL_EXPORT_REACTIVE NUMBER(18,4) DEFAULT NULL, 
	OLD_IMPORT_ACTIVE_TARIFF1 NUMBER(18,4) DEFAULT NULL, 
	OLD_IMPORT_ACTIVE_TARIFF2 NUMBER(18,4) DEFAULT NULL, 
	OLD_IMPORT_ACTIVE_TARIFF3 NUMBER(18,4) DEFAULT NULL, 
	OLD_IMPORT_ACTIVE_TARIFF4 NUMBER(18,4) DEFAULT NULL, 
	OLD_EXPORT_ACTIVE_TARIFF1 NUMBER(18,4) DEFAULT NULL, 
	OLD_EXPORT_ACTIVE_TARIFF2 NUMBER(18,4) DEFAULT NULL, 
	OLD_EXPORT_ACTIVE_TARIFF3 NUMBER(18,4) DEFAULT NULL, 
	OLD_EXPORT_ACTIVE_TARIFF4 NUMBER(18,4) DEFAULT NULL, 
	OLD_IMPORT_REACTIVE_TARIFF1 NUMBER(18,4) DEFAULT NULL, 
	OLD_IMPORT_REACTIVE_TARIFF2 NUMBER(18,4) DEFAULT NULL, 
	OLD_IMPORT_REACTIVE_TARIFF3 NUMBER(18,4) DEFAULT NULL, 
	OLD_IMPORT_REACTIVE_TARIFF4 NUMBER(18,4) DEFAULT NULL, 
	OLD_EXPORT_REACTIVE_TARIFF1 NUMBER(18,4) DEFAULT NULL, 
	OLD_EXPORT_REACTIVE_TARIFF2 NUMBER(18,4) DEFAULT NULL, 
	OLD_EXPORT_REACTIVE_TARIFF3 NUMBER(18,4) DEFAULT NULL, 
	OLD_EXPORT_REACTIVE_TARIFF4 NUMBER(18,4) DEFAULT NULL, 
	NEW_COMBINED_ACTIVE_ENERGY NUMBER(18,4) DEFAULT '0.0000', 
	NEW_TOTAL_IMPORT_ACTIVE NUMBER(18,4) DEFAULT NULL, 
	NEW_TOTAL_EXPORT_ACTIVE NUMBER(18,4) DEFAULT NULL, 
	NEW_TOTAL_IMPORT_REACTIVE NUMBER(18,4) DEFAULT NULL, 
	NEW_TOTAL_EXPORT_REACTIVE NUMBER(18,4) DEFAULT NULL, 
	NEW_IMPORT_ACTIVE_TARIFF1 NUMBER(18,4) DEFAULT NULL, 
	NEW_IMPORT_ACTIVE_TARIFF2 NUMBER(18,4) DEFAULT NULL, 
	NEW_IMPORT_ACTIVE_TARIFF3 NUMBER(18,4) DEFAULT NULL, 
	NEW_IMPORT_ACTIVE_TARIFF4 NUMBER(18,4) DEFAULT NULL, 
	NEW_EXPORT_ACTIVE_TARIFF1 NUMBER(18,4) DEFAULT NULL, 
	NEW_EXPORT_ACTIVE_TARIFF2 NUMBER(18,4) DEFAULT NULL, 
	NEW_EXPORT_ACTIVE_TARIFF3 NUMBER(18,4) DEFAULT NULL, 
	NEW_EXPORT_ACTIVE_TARIFF4 NUMBER(18,4) DEFAULT NULL, 
	NEW_IMPORT_REACTIVE_TARIFF1 NUMBER(18,4) DEFAULT NULL, 
	NEW_IMPORT_REACTIVE_TARIFF2 NUMBER(18,4) DEFAULT NULL, 
	NEW_IMPORT_REACTIVE_TARIFF3 NUMBER(18,4) DEFAULT NULL, 
	NEW_IMPORT_REACTIVE_TARIFF4 NUMBER(18,4) DEFAULT NULL, 
	NEW_EXPORT_REACTIVE_TARIFF1 NUMBER(18,4) DEFAULT NULL, 
	NEW_EXPORT_REACTIVE_TARIFF2 NUMBER(18,4) DEFAULT NULL, 
	NEW_EXPORT_REACTIVE_TARIFF3 NUMBER(18,4) DEFAULT NULL, 
	NEW_EXPORT_REACTIVE_TARIFF4 NUMBER(18,4) DEFAULT NULL, 
	ABNORMAL_PERIOD_COMBINED_ACTIVE_ENERGY NUMBER(18,4) DEFAULT '0.0000', 
	OLD_LAST_SURPLUS_MONEY NUMBER(18,2) DEFAULT NULL, 
	PROCESSING_TYPE NUMBER(4,0) DEFAULT '2', 
	CREATE_DATE DATE DEFAULT CURRENT_TIMESTAMP, 
	SAVE_DB_DATE DATE DEFAULT CURRENT_TIMESTAMP, 
	README VARCHAR2(256 BYTE) DEFAULT NULL, 
	LAST_SURPLUS_MONEY NUMBER(10,2) DEFAULT NULL, 
	CHANGE_TYPE NUMBER(4,0) DEFAULT NULL
   ) ;

   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.TIME_TAG IS '换表时间';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.OLD_METER_ID IS '旧表ID';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.NEW_METER_ID IS '新表ID';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.OLD_SERIAL_NUMBER IS '旧表资产编号';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.NEW_SERIAL_NUMBER IS '新表资产编号';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.DATA_SOURCE IS '数据来源: 默认为 CLouESP PPM';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.OLD_TOTAL_IMPORT_ACTIVE IS '旧表_正向有功总示度';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.OLD_TOTAL_EXPORT_ACTIVE IS '旧表_反向有功总示度';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.OLD_TOTAL_IMPORT_REACTIVE IS '旧表_正向无功总示度';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.OLD_TOTAL_EXPORT_REACTIVE IS '旧表_反向无功总示度';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.OLD_IMPORT_ACTIVE_TARIFF1 IS '旧表_正向有功费率1示度(尖)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.OLD_IMPORT_ACTIVE_TARIFF2 IS '旧表_正向有功费率2示度(峰)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.OLD_IMPORT_ACTIVE_TARIFF3 IS '旧表_正向有功费率3示度(平)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.OLD_IMPORT_ACTIVE_TARIFF4 IS '旧表_正向有功费率4示度(谷)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.OLD_EXPORT_ACTIVE_TARIFF1 IS '旧表_反向有功费率1示度(尖)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.OLD_EXPORT_ACTIVE_TARIFF2 IS '旧表_反向有功费率2示度(峰)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.OLD_EXPORT_ACTIVE_TARIFF3 IS '旧表_反向有功费率3示度(平)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.OLD_EXPORT_ACTIVE_TARIFF4 IS '旧表_反向有功费率4示度(谷)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.OLD_IMPORT_REACTIVE_TARIFF1 IS '旧表_正向无功费率1示度(尖)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.OLD_IMPORT_REACTIVE_TARIFF2 IS '旧表_正向无功费率2示度(峰)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.OLD_IMPORT_REACTIVE_TARIFF3 IS '旧表_正向无功费率3示度(平)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.OLD_IMPORT_REACTIVE_TARIFF4 IS '旧表_正向无功费率4示度(谷)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.OLD_EXPORT_REACTIVE_TARIFF1 IS '旧表_反向无功费率1示度(尖)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.OLD_EXPORT_REACTIVE_TARIFF2 IS '旧表_反向无功费率2示度(峰)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.OLD_EXPORT_REACTIVE_TARIFF3 IS '旧表_反向无功费率3示度(平)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.OLD_EXPORT_REACTIVE_TARIFF4 IS '旧表_反向无功费率4示度(谷)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.NEW_COMBINED_ACTIVE_ENERGY IS '新表_组合有功用电量';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.NEW_TOTAL_IMPORT_ACTIVE IS '新表_正向有功总示度';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.NEW_TOTAL_EXPORT_ACTIVE IS '新表_反向有功总示度';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.NEW_TOTAL_IMPORT_REACTIVE IS '新表_正向无功总示度';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.NEW_TOTAL_EXPORT_REACTIVE IS '新表_反向无功总示度';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.NEW_IMPORT_ACTIVE_TARIFF1 IS '新表_正向有功费率1示度(尖)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.NEW_IMPORT_ACTIVE_TARIFF2 IS '新表_正向有功费率2示度(峰)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.NEW_IMPORT_ACTIVE_TARIFF3 IS '新表_正向有功费率3示度(平)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.NEW_IMPORT_ACTIVE_TARIFF4 IS '新表_正向有功费率4示度(谷)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.NEW_EXPORT_ACTIVE_TARIFF1 IS '新表_反向有功费率1示度(尖)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.NEW_EXPORT_ACTIVE_TARIFF2 IS '新表_反向有功费率2示度(峰)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.NEW_EXPORT_ACTIVE_TARIFF3 IS '新表_反向有功费率3示度(平)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.NEW_EXPORT_ACTIVE_TARIFF4 IS '新表_反向有功费率4示度(谷)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.NEW_IMPORT_REACTIVE_TARIFF1 IS '新表_正向无功费率1示度(尖)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.NEW_IMPORT_REACTIVE_TARIFF2 IS '新表_正向无功费率2示度(峰)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.NEW_IMPORT_REACTIVE_TARIFF3 IS '新表_正向无功费率3示度(平)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.NEW_IMPORT_REACTIVE_TARIFF4 IS '新表_正向无功费率4示度(谷)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.NEW_EXPORT_REACTIVE_TARIFF1 IS '新表_反向无功费率1示度(尖)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.NEW_EXPORT_REACTIVE_TARIFF2 IS '新表_反向无功费率2示度(峰)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.NEW_EXPORT_REACTIVE_TARIFF3 IS '新表_反向无功费率3示度(平)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.NEW_EXPORT_REACTIVE_TARIFF4 IS '新表_反向无功费率4示度(谷)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.ABNORMAL_PERIOD_COMBINED_ACTIVE_ENERGY IS '表异常期间组合有功用电量--(电表故障开始到发现故障换新表这段时间客户使用的组合有功电量)';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.OLD_LAST_SURPLUS_MONEY IS '旧表剩余金额';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.PROCESSING_TYPE IS '处理类别: 字典表90COMMENT ON COLUMN ppm_asset_meter_change.1=已成功处理,2=未处理,3=处理失败, 4=作废';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.CREATE_DATE IS '建立时间';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.SAVE_DB_DATE IS '数据存入数据库时间';
   COMMENT ON COLUMN PPM_ASSET_METER_CHANGE.README IS '备注';
   COMMENT ON TABLE PPM_ASSET_METER_CHANGE  IS 'ppm_asset_meter_change 换表日志表';

  CREATE TABLE PPM_ASSET_TARIFF_GROUP 
   (	ID VARCHAR2(32 BYTE), 
	NAME VARCHAR2(64 BYTE), 
	TYPE NUMBER(4,0), 
	ACTIVATE_TV DATE, 
	DESCR VARCHAR2(256 BYTE)
   ) ;

   COMMENT ON COLUMN PPM_ASSET_TARIFF_GROUP.TYPE IS '1=单费率  2=阶梯费率  3=分时电价';

  CREATE TABLE PPM_ASSET_TARIFF_STEP 
   (	GROUP_ID VARCHAR2(32 BYTE), 
	STEP_INDEX NUMBER(4,0), 
	START_QUANTITY NUMBER(10,4), 
	END_QUANTITY NUMBER(10,4), 
	PRICE NUMBER(10,4)
   ) ;

   COMMENT ON COLUMN PPM_ASSET_TARIFF_STEP.STEP_INDEX IS '1-10';

  CREATE TABLE PPM_ASSET_TARIFF_STEP_DETAIL 
   (	GROUP_ID VARCHAR2(32 BYTE), 
	STEP_INDEX NUMBER(4,0), 
	TAX_TYPE NUMBER(4,0), 
	TARIFF_NAME VARCHAR2(256 BYTE), 
	THE_FORMULA VARCHAR2(256 BYTE), 
	VARIABLE_VALUE NUMBER(10,4), 
	CHARGE_MODE NUMBER(4,0), 
	DESCR VARCHAR2(256 BYTE), 
	CALCULATED VARCHAR2(20 BYTE)
   ) ;

   COMMENT ON COLUMN PPM_ASSET_TARIFF_STEP_DETAIL.TAX_TYPE IS '1=街道照明税费   2=政府税费  3=政府津贴  4=补贴  5=增值税  6=国家保险计划征税  7=增值税作补贴  8=服务费  9=补助1  10=补助2    11=税费%*用电量';
   COMMENT ON COLUMN PPM_ASSET_TARIFF_STEP_DETAIL.CHARGE_MODE IS '1=按月收取  2=按次收取';
   COMMENT ON COLUMN PPM_ASSET_TARIFF_STEP_DETAIL.CALCULATED IS '1=是(参与计算)   2=否(不参与计算) 特殊情况';

  CREATE TABLE PPM_CUSTOMER_HISTORY_TARIFF 
   (	ID VARCHAR2(32 BYTE), 
	TYPE NUMBER(4,0), 
	GROUP_ID VARCHAR2(32 BYTE), 
	ACTIVE_TV DATE
   ) ;

  CREATE TABLE PPM_CUSTOMER_TARIFF_MAP 
   (	ID VARCHAR2(32 BYTE), 
	TYPE NUMBER(4,0), 
	GROUP_ID VARCHAR2(32 BYTE)
   ) ;

   COMMENT ON COLUMN PPM_CUSTOMER_TARIFF_MAP.TYPE IS '1=单费率  2=阶梯费率  3=分时电价';
   COMMENT ON COLUMN PPM_CUSTOMER_TARIFF_MAP.GROUP_ID IS '当前使用费率';

  CREATE TABLE PPM_DATA_PREPAID_PROGRESS 
   (	CUSTOMER_ID VARCHAR2(32 BYTE), 
	LAST_PAYMENT_TV DATE, 
	METER_COMMISSIONING_DATE DATE
   ) ;

   COMMENT ON COLUMN PPM_DATA_PREPAID_PROGRESS.LAST_PAYMENT_TV IS '上次充值时间';
   COMMENT ON COLUMN PPM_DATA_PREPAID_PROGRESS.METER_COMMISSIONING_DATE IS '客户与电表的关联时间';

  CREATE TABLE PPM_DATA_STATISTICS_CUSTOMER 
   (	ORG_ID VARCHAR2(32 BYTE), 
	TV DATE, 
	TV_TYPE NUMBER, 
	DATA_TYPE NUMBER(10,2), 
	TOTAL_VALUE NUMBER(12,2)
   ) ;

   COMMENT ON COLUMN PPM_DATA_STATISTICS_CUSTOMER.TV_TYPE IS '1:日 2：月';
   COMMENT ON COLUMN PPM_DATA_STATISTICS_CUSTOMER.DATA_TYPE IS '1:Sale 2 Consumption';

  CREATE TABLE PPM_DATA_USER_LOG 
   (	
    TV datetime NOT NULL, 
	USER_ID VARCHAR(32) NOT NULL, 
	LOG_TYPE VARCHAR(64) NOT NULL, 
	LOG_SUB_TYPE VARCHAR(64) NOT NULL, 
	DETAIL VARCHAR(256),
	PRIMARY KEY (TV, USER_ID, LOG_TYPE, LOG_SUB_TYPE)
   );

   CREATE TABLE PPM_DICT_DETAIL 
   (	DICT_ID VARCHAR2(32 BYTE), 
	INNER_VALUE NUMBER(11,0), 
	GUI_DISPLAY_NAME VARCHAR2(256 BYTE), 
	README VARCHAR2(256 BYTE)
   ) ;

   COMMENT ON COLUMN PPM_DICT_DETAIL.DICT_ID IS '字典ID';
   COMMENT ON COLUMN PPM_DICT_DETAIL.INNER_VALUE IS '内部值(程序引用序号)';
   COMMENT ON COLUMN PPM_DICT_DETAIL.GUI_DISPLAY_NAME IS '字典明细名称';
   COMMENT ON TABLE PPM_DICT_DETAIL  IS 'zzz_dict_detail 字典明细表';

  CREATE TABLE PPM_DICT_FUNCTION 
   (	ID VARCHAR2(32 BYTE), 
	FUNCTIONNAME VARCHAR2(64 BYTE), 
	FUNCTIONURL VARCHAR2(128 BYTE), 
	FUNCTION_INTRO VARCHAR2(128 BYTE)
   ) ;

  CREATE TABLE PPM_DICT_OPERATION 
   (	ID VARCHAR2(32), 
	OPERATIONNAME VARCHAR2(50), 
	FUNCTION_ID VARCHAR2(32), 
	OPERATIONURL VARCHAR2(128), 
	ISHIDE NUMBER(4,0), 
	DESCRIPTION VARCHAR2(255 BYTE)
   ) ;

  CREATE TABLE PPM_DICT_USER_LOG 
   (	LOG_TYPE VARCHAR2(64 BYTE), 
	LOG_SUB_TYPE VARCHAR2(1024 BYTE), 
	SORT_ID NUMBER
   ) ;

  CREATE TABLE PPM_INTEGRATION_LOG 
   (	ID VARCHAR2(32 BYTE), 
	METER_ID VARCHAR2(32 BYTE), 
	SERIAL_NUMBER VARCHAR2(32 BYTE), 
	TV DATE, 
	DATA_SOURCE VARCHAR2(32 BYTE), 
	MESSAGE_TYPE VARCHAR2(64 BYTE), 
	LOG_TYPE VARCHAR2(10 BYTE), 
	MESSAGE_SUB_TYPE VARCHAR2(64 BYTE), 
	MESSAGE_ID VARCHAR2(128 BYTE), 
	MESSAGE_RESULT VARCHAR2(64 BYTE), 
	MESSAGE_DETAIL VARCHAR2(4000 BYTE), 
	SAVE_DB_DATE DATE DEFAULT CURRENT_TIMESTAMP, 
	README VARCHAR2(256 BYTE)
   ) ;

   COMMENT ON COLUMN PPM_INTEGRATION_LOG.ID IS '日志ID';
   COMMENT ON COLUMN PPM_INTEGRATION_LOG.METER_ID IS '电表ID';
   COMMENT ON COLUMN PPM_INTEGRATION_LOG.SERIAL_NUMBER IS '资产编号--对应的电表实体的资产编号';
   COMMENT ON COLUMN PPM_INTEGRATION_LOG.TV IS '消息时间';
   COMMENT ON COLUMN PPM_INTEGRATION_LOG.DATA_SOURCE IS '数据来源(这里记录的是 数据源系统的名称 例如：ClouESP_HES 或 ClouESP_PPM )';
   COMMENT ON COLUMN PPM_INTEGRATION_LOG.MESSAGE_TYPE IS '消息类型 (这里记录的是 IEC61968-9接口执行的功能名称)';
   COMMENT ON COLUMN PPM_INTEGRATION_LOG.LOG_TYPE IS '日志类型（固定数据类型：Tx 为 发送的数据  Rx 为接收的数据）';
   COMMENT ON COLUMN PPM_INTEGRATION_LOG.MESSAGE_SUB_TYPE IS '消息子类型（这里记录的是 IEC61968-9接口执行的功能名称）';
   COMMENT ON COLUMN PPM_INTEGRATION_LOG.MESSAGE_ID IS '消息id';
   COMMENT ON COLUMN PPM_INTEGRATION_LOG.MESSAGE_RESULT IS '消息结果';
   COMMENT ON COLUMN PPM_INTEGRATION_LOG.MESSAGE_DETAIL IS 'log明细';
   COMMENT ON TABLE PPM_INTEGRATION_LOG  IS 'log_IEC61968_communication IEC61968-9接口通讯日志';

  CREATE TABLE PPM_SGC_KEYFILE 
   (	DEVICE_ID VARCHAR2(32 BYTE), 
	SUPPLY_GROUP_CODE VARCHAR2(32 BYTE), 
	SUPPLY_GROUP_NAME VARCHAR2(256 BYTE), 
	IS_DEFAULT NUMBER(2,0) DEFAULT 0, 
	KEY_REGISTER_NUMBER VARCHAR2(20 BYTE), 
	KEY_REVISION_NUMBER VARCHAR2(1 BYTE), 
	KEY_EXPIRY_NUMBER VARCHAR2(4 BYTE)
   ) ;

  CREATE TABLE PPM_SYS_MENU 
   (	ID VARCHAR2(32), 
	UTILITY_IDS VARCHAR2(600), 
	MENULEVEL NUMBER(6,0), 
	MENUNAME VARCHAR2(50), 
	MENUORDER NUMBER(11,0), 
	FUNCTIONURL VARCHAR2(100), 
	PARENTMENUID VARCHAR2(32), 
	MENU_INTRO VARCHAR2(300), 
	FUNCTIONID VARCHAR2(32), 
	HIDE_TAB VARCHAR2(64 BYTE)
   ) ;

  CREATE TABLE PPM_SYS_ROLE 
   (	ID VARCHAR2(32), 
	UTILITY_ID VARCHAR2(32), 
	NAME VARCHAR2(64), 
	DESCRIPTION VARCHAR2(256)
   ) ;

  CREATE TABLE PPM_SYS_ROLE_MENU 
   (	ROLE_ID VARCHAR2(32), 
	MENU_ID VARCHAR2(32), 
	OPERATION VARCHAR2(1000)
   ) ;

  CREATE TABLE PPM_SYS_USER 
   (	ID VARCHAR2(32), 
	UTILITY_ID VARCHAR2(32), 
	ORG_ID VARCHAR2(500), 
	ROLE_ID VARCHAR2(32), 
	NAME VARCHAR2(64), 
	USERNAME VARCHAR2(32), 
	PASSWORD VARCHAR2(32), 
	EMAIL VARCHAR2(64), 
	MOBILE_PHONE VARCHAR2(32), 
	PROFILE_FILE VARCHAR2(32), 
	USER_TYPE NUMBER(4,0), 
	SIGNATURE BLOB, 
	USERKEY VARCHAR2(200), 
	DELETE_FLAG NUMBER(6,0), 
	USER_STATE NUMBER(4,0), 
	LAST_LOGIN_TIME DATE, 
	STATION_ID VARCHAR2(32 BYTE)
   ) ;

  CREATE TABLE PPM_TOTAL_MONTH_ELECTRICITY_CONSUMPTION 
   (	ID VARCHAR2(32 BYTE), 
	ORG_ID VARCHAR2(32 BYTE), 
	MONTHS VARCHAR2(32 BYTE), 
	MONTH_ELECTRICITY VARCHAR2(32 BYTE), 
	CREATE_DATE DATE, 
	SAVE_DB_DATE DATE, 
	README VARCHAR2(200 BYTE)
   ) ;

  CREATE TABLE PPM_TOTAL_MONTH_SALES_AMOUNT 
   (	ID VARCHAR2(32), 
	ORG_ID VARCHAR2(32), 
	MONTHS VARCHAR2(32), 
	MONTH_AMOUNT VARCHAR2(32), 
	CREATE_DATE DATE, 
	SAVE_DB_DATE DATE DEFAULT sysdate, 
	README VARCHAR2(200)
   ) ;

  CREATE TABLE PPM_VEND_CUSTOMER_DEBT 
   (	CUSTOMER_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	ID VARCHAR2(32 BYTE), 
	DEBT_DATE DATE, 
	DEBT_TOTAL NUMBER(14,2) DEFAULT NULL, 
	DEBT_PERIOD NUMBER(3,0) DEFAULT NULL, 
	PAYED_PERIOD NUMBER(3,0) DEFAULT NULL, 
	LEFT_PERIOD NUMBER(3,0) DEFAULT NULL, 
	MONEY_PERIOD NUMBER(14,2) DEFAULT NULL, 
	DEBT_MONEY NUMBER(14,2) DEFAULT NULL, 
	PAYED_TOTAL NUMBER(14,2) DEFAULT NULL, 
	DIVIDE_REMAINDER NUMBER(14,2) DEFAULT '0.00', 
	DEBT_TYPE NUMBER(2,0), 
	RECORD_STATE NUMBER(2,0), 
	DEBT_COLLECT_MONTH NUMBER(2,0), 
	DEBT_COLLECT_DATE DATE, 
	USER_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	README VARCHAR2(256 BYTE) DEFAULT NULL
   ) ;

   COMMENT ON COLUMN PPM_VEND_CUSTOMER_DEBT.CUSTOMER_ID IS '客户ID';
   COMMENT ON COLUMN PPM_VEND_CUSTOMER_DEBT.ID IS '债务ID';
   COMMENT ON COLUMN PPM_VEND_CUSTOMER_DEBT.DEBT_DATE IS '日期';
   COMMENT ON COLUMN PPM_VEND_CUSTOMER_DEBT.DEBT_TOTAL IS '债务总额';
   COMMENT ON COLUMN PPM_VEND_CUSTOMER_DEBT.DEBT_PERIOD IS '分期期数';
   COMMENT ON COLUMN PPM_VEND_CUSTOMER_DEBT.PAYED_PERIOD IS '已还期数';
   COMMENT ON COLUMN PPM_VEND_CUSTOMER_DEBT.LEFT_PERIOD IS '剩余期数';
   COMMENT ON COLUMN PPM_VEND_CUSTOMER_DEBT.MONEY_PERIOD IS '每期还款金额';
   COMMENT ON COLUMN PPM_VEND_CUSTOMER_DEBT.DEBT_MONEY IS '剩余欠款';
   COMMENT ON COLUMN PPM_VEND_CUSTOMER_DEBT.PAYED_TOTAL IS '已还总额';
   COMMENT ON COLUMN PPM_VEND_CUSTOMER_DEBT.DIVIDE_REMAINDER IS '债务零头，总额除分期期数的债务余数---债务尾款';
   COMMENT ON COLUMN PPM_VEND_CUSTOMER_DEBT.DEBT_TYPE IS '债务类型.字典表 74：1=初始债务 2=换表债务 3=其它债务';
   COMMENT ON COLUMN PPM_VEND_CUSTOMER_DEBT.RECORD_STATE IS '记录状态：字典表 75： 1= 正常.2=作废';
   COMMENT ON COLUMN PPM_VEND_CUSTOMER_DEBT.DEBT_COLLECT_MONTH IS '债务收取开始时间(字典表88 :1=当月开始收取,2=下月开始收取)';
   COMMENT ON COLUMN PPM_VEND_CUSTOMER_DEBT.DEBT_COLLECT_DATE IS '债务开始收取时间 当售电的时候扣取按这个时间的月份开始进行扣取';
   COMMENT ON COLUMN PPM_VEND_CUSTOMER_DEBT.USER_ID IS '操作员ID';
   COMMENT ON COLUMN PPM_VEND_CUSTOMER_DEBT.README IS '备注';
   COMMENT ON TABLE PPM_VEND_CUSTOMER_DEBT  IS 'ppm_vend_meter_debt 电表债务历史记录表';

  CREATE TABLE PPM_VEND_CUSTOMER_DEBT_RESTORE 
   (	CUSTOMER_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	ID VARCHAR2(32 BYTE), 
	METER_DEBT_ID VARCHAR2(32 BYTE), 
	DEBT_PAYED_PERIOD NUMBER(3,0) DEFAULT NULL, 
	DEBT_PAYED_TIME DATE DEFAULT NULL, 
	DEBT_PAYED_PERIOD_MONEY NUMBER(14,2) DEFAULT NULL, 
	DEBT_PAYED_MONEY NUMBER(14,2) DEFAULT NULL, 
	RECORD_STATE NUMBER(2,0), 
	SALES_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	README VARCHAR2(256 BYTE) DEFAULT NULL
   ) ;

  CREATE TABLE PPM_VEND_FREE_TOKEN_MANAGE 
   (	ID VARCHAR2(32 BYTE), 
	RECEIPT_NO VARCHAR2(32 BYTE) DEFAULT NULL, 
	ORG_ID VARCHAR2(100 BYTE) DEFAULT NULL, 
	STATION_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	CUSTOMER_NAME VARCHAR2(256 BYTE), 
	METER_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	SERIAL_NUMBER VARCHAR2(30 BYTE), 
	COMM_ADDRESS VARCHAR2(24 BYTE), 
	USER_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	USER_NAME VARCHAR2(30 BYTE), 
	TOKEN_TYPE VARCHAR2(4 BYTE) DEFAULT NULL, 
	TOKEN VARCHAR2(20 BYTE) DEFAULT NULL, 
	RECEIPT_STATE NUMBER(2,0) DEFAULT NULL, 
	TOTAL_AMOUNT NUMBER(10,4) DEFAULT '0.0000', 
	FREE_TOKEN_DESCRIPTION VARCHAR2(500 BYTE)
   ) ;

   COMMENT ON COLUMN PPM_VEND_FREE_TOKEN_MANAGE.RECEIPT_NO IS '流水号，来源自时间戳';
   COMMENT ON COLUMN PPM_VEND_FREE_TOKEN_MANAGE.USER_ID IS '操作员ID';
   COMMENT ON COLUMN PPM_VEND_FREE_TOKEN_MANAGE.USER_NAME IS '操作员姓名';
   COMMENT ON COLUMN PPM_VEND_FREE_TOKEN_MANAGE.TOKEN_TYPE IS 'Token类型(字典类型54： 0=设置最大负荷Token 1=清除电量Token  3=充值Token 4=变更密钥Token 5=清除窃电状态Token 15=设置卸载电量Token）';
   COMMENT ON COLUMN PPM_VEND_FREE_TOKEN_MANAGE.RECEIPT_STATE IS '单据状态 字典表 75： 1= 正常  2=作废';
   COMMENT ON COLUMN PPM_VEND_FREE_TOKEN_MANAGE.TOTAL_AMOUNT IS '总金额';
   COMMENT ON TABLE PPM_VEND_FREE_TOKEN_MANAGE  IS 'ppm_vend_free_token_manage 免费Token管理';

  CREATE TABLE PPM_VEND_HISTORICAL_INFO 
   (	ID VARCHAR2(32 BYTE), 
	ORG_ID VARCHAR2(100 BYTE) DEFAULT NULL, 
	STATION_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	CUSTOMER_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	METER_ID VARCHAR2(32 BYTE), 
	TARIFF_SOLUTION_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	USER_ID VARCHAR2(32 BYTE), 
	USER_NAME VARCHAR2(50 BYTE) DEFAULT NULL, 
	SALES_DATE DATE DEFAULT NULL, 
	PAYMENT_TYPE VARCHAR2(20 BYTE) DEFAULT NULL, 
	CUSTOMER_PAYMENT_AMOUNT NUMBER(10,4) DEFAULT '0.0000', 
	DEBT_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	PAYMENT_DEBT_AMOUNT NUMBER(10,4) DEFAULT '0.0000', 
	TAXES NUMBER(10,4) DEFAULT '0.0000', 
	NET_AMOUNT NUMBER(10,4) DEFAULT '0.0000', 
	OTHER_EXPENSES_TOTAL_AMOUNT NUMBER(10,4) DEFAULT NULL, 
	REATER_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	TOTAL_RATE_MONEY NUMBER(10,4) DEFAULT NULL, 
	LAST_PAYMENT_DATE DATE DEFAULT NULL, 
	IS_EMERGENCY_SELL_ELECTRICITY VARCHAR2(2 BYTE) DEFAULT '0', 
	IS_MONTH_FIRST NUMBER(1,0) DEFAULT '0', 
	IS_FULFILL VARCHAR2(1 BYTE) DEFAULT NULL, 
	RECEIPT_STATE NUMBER(2,0) DEFAULT NULL, 
	RECHARGE_TYPE NUMBER(2,0) DEFAULT NULL, 
	UNINSTALL_AMOUNT NUMBER(10,4) DEFAULT '0.0000', 
	CORRECT_README VARCHAR2(512 BYTE) DEFAULT NULL, 
	ORIGINAL_SALES_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	UPDATE_DATE DATE DEFAULT NULL, 
	UPDATE_USER_ID VARCHAR2(20 BYTE) DEFAULT NULL, 
	UPDATE_USER_NAME VARCHAR2(50 BYTE) DEFAULT NULL, 
	SAVE_DB_DATE DATE DEFAULT CURRENT_TIMESTAMP, 
	README VARCHAR2(256 BYTE) DEFAULT NULL, 
	CHEQUE_NO VARCHAR2(100 BYTE) DEFAULT NULL
   ) ;

   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.TARIFF_SOLUTION_ID IS '费率ID';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.SALES_DATE IS '售电时间';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.PAYMENT_TYPE IS '支付类型 (字典表 73：1=现金 2=支票 3=信用卡 4=借记卡) ';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.CUSTOMER_PAYMENT_AMOUNT IS '客户付款';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.DEBT_ID IS '收取债务记录ID';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.PAYMENT_DEBT_AMOUNT IS '收取债务金额';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.TAXES IS '税金';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.NET_AMOUNT IS '净金额';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.OTHER_EXPENSES_TOTAL_AMOUNT IS '其它费用总金额(包括手续费等等其他费用的总和)';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.REATER_ID IS '费率切换--退款编号';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.TOTAL_RATE_MONEY IS '费率切换--退款总金额';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.LAST_PAYMENT_DATE IS '上次售电缴费时间';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.IS_EMERGENCY_SELL_ELECTRICITY IS '是否紧急售电 0：否 1：是';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.IS_MONTH_FIRST IS '是否为本月第一次售电 0：否 1：是';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.IS_FULFILL IS '充值记录是否完成 (0=未完成 1=已完成)';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.RECEIPT_STATE IS '单据状态：0 正常数据  1 过期数据（被卸载的记录）';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.RECHARGE_TYPE IS '充值类型，0表示正常充值，1表示卸载。';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.UNINSTALL_AMOUNT IS '卸载金额';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.CORRECT_README IS '卸载电量的备注';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.ORIGINAL_SALES_ID IS '原始售电ID';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.UPDATE_DATE IS '最后一次修改时间';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.UPDATE_USER_ID IS '最后修改用户ID';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.UPDATE_USER_NAME IS '最后修改用户姓名';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.SAVE_DB_DATE IS '数据存入数据库时间';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.README IS '备注';
   COMMENT ON COLUMN PPM_VEND_HISTORICAL_INFO.CHEQUE_NO IS '支票号码';
   COMMENT ON TABLE PPM_VEND_HISTORICAL_INFO  IS 'ppm_vend_historical_info 售电历史记录';

  CREATE TABLE PPM_VEND_INITIAL_CREDIT_AMOUNT 
   (	ID VARCHAR2(32 BYTE), 
	METER_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	INIT_CREDIT_AMOUNT NUMBER(10,0) DEFAULT '0', 
	PAYMENT_TIME DATE DEFAULT NULL, 
	STATE NUMBER(2,0), 
	SALES_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	CREATE_DATE DATE DEFAULT NULL, 
	README VARCHAR2(256 BYTE) DEFAULT NULL
   ) ;

   COMMENT ON COLUMN PPM_VEND_INITIAL_CREDIT_AMOUNT.ID IS '初始信用ID';
   COMMENT ON COLUMN PPM_VEND_INITIAL_CREDIT_AMOUNT.METER_ID IS '电表ID';
   COMMENT ON COLUMN PPM_VEND_INITIAL_CREDIT_AMOUNT.INIT_CREDIT_AMOUNT IS '预付费表预置金额';
   COMMENT ON COLUMN PPM_VEND_INITIAL_CREDIT_AMOUNT.PAYMENT_TIME IS '缴费时间';
   COMMENT ON COLUMN PPM_VEND_INITIAL_CREDIT_AMOUNT.STATE IS '状态：0表示未处理、1表示已经缴费处理完毕';
   COMMENT ON COLUMN PPM_VEND_INITIAL_CREDIT_AMOUNT.SALES_ID IS '售电历史ID：此ID 记录的是对应收取该费用的售电记录，便于检索。';
   COMMENT ON COLUMN PPM_VEND_INITIAL_CREDIT_AMOUNT.CREATE_DATE IS '创建时间';
   COMMENT ON COLUMN PPM_VEND_INITIAL_CREDIT_AMOUNT.README IS '备注';

  CREATE TABLE PPM_VEND_INVOICE 
   (	ID VARCHAR2(32 BYTE), 
	RECEIPT_NO VARCHAR2(32 BYTE) DEFAULT NULL, 
	SALES_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	SALES_DATE DATE DEFAULT NULL, 
	STATION_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	CUSTOMER_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	CUSTOMER_NAME VARCHAR2(256 BYTE) DEFAULT NULL, 
	METER_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	SERIAL_NUMBER VARCHAR2(30 BYTE) DEFAULT NULL, 
	COMM_ADDRESS VARCHAR2(24 BYTE) DEFAULT NULL, 
	CUSTOMER_PAYMENT_AMOUNT NUMBER(14,2), 
	TOTAL_FEES NUMBER(14,2) DEFAULT NULL, 
	RECHARGE_AMOUNT NUMBER(14,2), 
	VENDING_TOKEN VARCHAR2(20 BYTE) DEFAULT NULL, 
	PAYMENT_TYPE VARCHAR2(50 BYTE) DEFAULT NULL, 
	DAYS_FROM_LAST_CHARGE NUMBER(10,0) DEFAULT NULL, 
	UPDATE_DATE DATE DEFAULT NULL
   ) ;

   COMMENT ON COLUMN PPM_VEND_INVOICE.ID IS '售电单据打印ID';
   COMMENT ON COLUMN PPM_VEND_INVOICE.RECEIPT_NO IS '售电单据流水号';
   COMMENT ON COLUMN PPM_VEND_INVOICE.SALES_ID IS '售电ID';
   COMMENT ON COLUMN PPM_VEND_INVOICE.SALES_DATE IS '售电时间';
   COMMENT ON COLUMN PPM_VEND_INVOICE.STATION_ID IS '售电站ID';
   COMMENT ON COLUMN PPM_VEND_INVOICE.CUSTOMER_ID IS '客户ID';
   COMMENT ON COLUMN PPM_VEND_INVOICE.CUSTOMER_NAME IS '客户名称';
   COMMENT ON COLUMN PPM_VEND_INVOICE.METER_ID IS '电表ID';
   COMMENT ON COLUMN PPM_VEND_INVOICE.SERIAL_NUMBER IS '电表资产编号';
   COMMENT ON COLUMN PPM_VEND_INVOICE.COMM_ADDRESS IS '电表通讯地址';
   COMMENT ON COLUMN PPM_VEND_INVOICE.CUSTOMER_PAYMENT_AMOUNT IS '已付款金额';
   COMMENT ON COLUMN PPM_VEND_INVOICE.TOTAL_FEES IS '总综合收费(包含要扣取的所有费用)';
   COMMENT ON COLUMN PPM_VEND_INVOICE.RECHARGE_AMOUNT IS '充值金额(充值到电表的实际金额)';
   COMMENT ON COLUMN PPM_VEND_INVOICE.VENDING_TOKEN IS '售电Token';
   COMMENT ON COLUMN PPM_VEND_INVOICE.PAYMENT_TYPE IS '支付类型 (字典表 73：1=现金 2=支票 3=信用卡 4=借记卡) ';
   COMMENT ON COLUMN PPM_VEND_INVOICE.DAYS_FROM_LAST_CHARGE IS '距最后一次售电间隔天数';
   COMMENT ON COLUMN PPM_VEND_INVOICE.UPDATE_DATE IS '最后一次修改时间';
   COMMENT ON TABLE PPM_VEND_INVOICE  IS 'ppm_vend_invoice 售电单据打印';

  CREATE TABLE PPM_VEND_INVOICE_DETAILS 
   (	ID VARCHAR2(32 BYTE), 
	INVOICE_PRINT_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	SALES_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	DETAILS_FEES_NAME VARCHAR2(256 BYTE) DEFAULT NULL, 
	DETAILS_VALUE NUMBER(14,2), 
	DETAILS_DATE DATE, 
	TAX_TYPE NUMBER(2,0) DEFAULT NULL, 
	COMPUTE_ENABLE NUMBER(2,0) DEFAULT '1'
   ) ;

   COMMENT ON COLUMN PPM_VEND_INVOICE_DETAILS.ID IS '售电明细ID';
   COMMENT ON COLUMN PPM_VEND_INVOICE_DETAILS.INVOICE_PRINT_ID IS '售电单据打印ID';
   COMMENT ON COLUMN PPM_VEND_INVOICE_DETAILS.SALES_ID IS '售电ID';
   COMMENT ON COLUMN PPM_VEND_INVOICE_DETAILS.DETAILS_FEES_NAME IS '费用名称';
   COMMENT ON COLUMN PPM_VEND_INVOICE_DETAILS.DETAILS_VALUE IS '收费金额';
   COMMENT ON COLUMN PPM_VEND_INVOICE_DETAILS.DETAILS_DATE IS '日期';
   COMMENT ON COLUMN PPM_VEND_INVOICE_DETAILS.TAX_TYPE IS '税费类型 （字典表60： 1=街道照明税费   2=政府税费  3=政府津贴  4=补贴  5=增值税  6=国家保险计划征税  7=增值税作补贴  8=服务费  9=补助1  10=补助2    11=税费%*用电量    等等）';
   COMMENT ON COLUMN PPM_VEND_INVOICE_DETAILS.COMPUTE_ENABLE IS '是否参与计算(字典表 6：1=是(参与计算) 2=否(不参与计算)特殊情况，如用电量为0的时候，可能不需要计算电费 3 =计算仅作为小票显示使用,不参与扣费';
   COMMENT ON TABLE PPM_VEND_INVOICE_DETAILS  IS 'vend_invoice_details 售电单据打印-收费详情';

  CREATE TABLE PPM_VEND_INVOICE_FREE_TOKEN 
   (	ID VARCHAR2(32 BYTE), 
	RECEIPT_NO VARCHAR2(32 BYTE) DEFAULT NULL, 
	FREE_TOKEN_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	STATION_ID VARCHAR2(32 BYTE), 
	CUSTOMER_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	CUSTOMER_NAME VARCHAR2(256 BYTE), 
	METER_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	SERIAL_NUMBER VARCHAR2(30 BYTE), 
	COMM_ADDRESS VARCHAR2(24 BYTE), 
	FREE_RECHARGE_AMOUNT NUMBER(14,2), 
	FREE_TOKEN VARCHAR2(20 BYTE) DEFAULT NULL, 
	PAYMENT_TYPE VARCHAR2(50 BYTE) DEFAULT NULL, 
	USER_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	USER_NAME VARCHAR2(50 BYTE), 
	CREATE_DATE DATE DEFAULT CURRENT_TIMESTAMP, 
	SAVE_DB_DATE DATE DEFAULT CURRENT_TIMESTAMP
   ) ;

   COMMENT ON COLUMN PPM_VEND_INVOICE_FREE_TOKEN.ID IS '售电单据打印ID';
   COMMENT ON COLUMN PPM_VEND_INVOICE_FREE_TOKEN.RECEIPT_NO IS '售电单据流水号，来源自时间戳';
   COMMENT ON COLUMN PPM_VEND_INVOICE_FREE_TOKEN.FREE_TOKEN_ID IS '免费TokenID';
   COMMENT ON COLUMN PPM_VEND_INVOICE_FREE_TOKEN.STATION_ID IS '售电站ID';
   COMMENT ON COLUMN PPM_VEND_INVOICE_FREE_TOKEN.CUSTOMER_ID IS '客户ID';
   COMMENT ON COLUMN PPM_VEND_INVOICE_FREE_TOKEN.CUSTOMER_NAME IS '客户名称';
   COMMENT ON COLUMN PPM_VEND_INVOICE_FREE_TOKEN.METER_ID IS '电表ID';
   COMMENT ON COLUMN PPM_VEND_INVOICE_FREE_TOKEN.SERIAL_NUMBER IS '电表资产编号';
   COMMENT ON COLUMN PPM_VEND_INVOICE_FREE_TOKEN.COMM_ADDRESS IS '电表通讯地址';
   COMMENT ON COLUMN PPM_VEND_INVOICE_FREE_TOKEN.FREE_RECHARGE_AMOUNT IS '充值金额(充值到电表的实际金额)';
   COMMENT ON COLUMN PPM_VEND_INVOICE_FREE_TOKEN.FREE_TOKEN IS '售电Token';
   COMMENT ON COLUMN PPM_VEND_INVOICE_FREE_TOKEN.PAYMENT_TYPE IS '支付类型 (字典表 73：1=现金 2=支票 3=信用卡 4=借记卡 5=免费) ';
   COMMENT ON COLUMN PPM_VEND_INVOICE_FREE_TOKEN.USER_ID IS '用户ID';
   COMMENT ON COLUMN PPM_VEND_INVOICE_FREE_TOKEN.USER_NAME IS '用户姓名';
   COMMENT ON COLUMN PPM_VEND_INVOICE_FREE_TOKEN.CREATE_DATE IS '建立时间';
   COMMENT ON COLUMN PPM_VEND_INVOICE_FREE_TOKEN.SAVE_DB_DATE IS '数据存入数据库时间';
   COMMENT ON TABLE PPM_VEND_INVOICE_FREE_TOKEN  IS 'ppm_vend_invoice_free_token 免费Token单据打印';

   CREATE TABLE PPM_VEND_STATION_RECHARGE 
   (	ID VARCHAR2(32 BYTE), 
	STATION_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	DEALING_TIME DATE DEFAULT NULL, 
	RECHARGE_BY NUMBER(2,0) DEFAULT NULL, 
	RECHARGE_TYPE NUMBER(2,0) DEFAULT '1', 
	RECEIPT_NO VARCHAR2(32 BYTE) DEFAULT NULL, 
	RECHARGE_AMOUNT NUMBER(14,4) DEFAULT NULL, 
	COMMISSION NUMBER(14,4) DEFAULT NULL, 
	TAX NUMBER(14,4) DEFAULT NULL, 
	TOTAL_AMOUNT NUMBER(14,4) DEFAULT NULL, 
	RECHARGE_AMOUNT_INPUT NUMBER(14,4) DEFAULT NULL, 
	ACCOUNT_BALANCE NUMBER(14,4) DEFAULT NULL, 
	OPERATOR_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	README VARCHAR2(256 BYTE) DEFAULT NULL, 
	CHEQUE_NO VARCHAR2(100 BYTE) DEFAULT NULL, 
	RECEIPT_STATE NUMBER(2,0), 
	CREATE_DATE DATE DEFAULT CURRENT_TIMESTAMP, 
	SAVE_DB_DATE DATE DEFAULT CURRENT_TIMESTAMP
   ) ;

   COMMENT ON COLUMN PPM_VEND_STATION_RECHARGE.ID IS '售电站充值ID';
   COMMENT ON COLUMN PPM_VEND_STATION_RECHARGE.STATION_ID IS '售电站ID';
   COMMENT ON COLUMN PPM_VEND_STATION_RECHARGE.DEALING_TIME IS '充值时间';
   COMMENT ON COLUMN PPM_VEND_STATION_RECHARGE.RECHARGE_BY IS '充值方式(字典表73：1=现金 2=支票 3=信用卡 4=借记卡)';
   COMMENT ON COLUMN PPM_VEND_STATION_RECHARGE.RECHARGE_TYPE IS '充值类型（字典表72: 固定为 1 售电站充值)';
   COMMENT ON COLUMN PPM_VEND_STATION_RECHARGE.RECEIPT_NO IS '充值小票号码';
   COMMENT ON COLUMN PPM_VEND_STATION_RECHARGE.RECHARGE_AMOUNT IS '充值金额：客户缴费实际金额';
   COMMENT ON COLUMN PPM_VEND_STATION_RECHARGE.COMMISSION IS '佣金（劳务费）：充值金额 * 佣金百分比 =佣金（佣金百分比在售电站中获取)';
   COMMENT ON COLUMN PPM_VEND_STATION_RECHARGE.TAX IS '税费(佣金 乘以 税费百分比 --税费百分比在售电站中获取)';
   COMMENT ON COLUMN PPM_VEND_STATION_RECHARGE.TOTAL_AMOUNT IS '充值总金额(加上佣金 减去 税费)';
   COMMENT ON COLUMN PPM_VEND_STATION_RECHARGE.RECHARGE_AMOUNT_INPUT IS '充值前账户金额';
   COMMENT ON COLUMN PPM_VEND_STATION_RECHARGE.ACCOUNT_BALANCE IS '充值后账户金额';
   COMMENT ON COLUMN PPM_VEND_STATION_RECHARGE.OPERATOR_ID IS '操作员ID';
   COMMENT ON COLUMN PPM_VEND_STATION_RECHARGE.README IS '备注';
   COMMENT ON COLUMN PPM_VEND_STATION_RECHARGE.CHEQUE_NO IS '支票号码';
   COMMENT ON COLUMN PPM_VEND_STATION_RECHARGE.RECEIPT_STATE IS '单据状态：0 正常数据  1 过期数据（被卸载的记录）';
   COMMENT ON COLUMN PPM_VEND_STATION_RECHARGE.SAVE_DB_DATE IS '数据存入数据库时间';
   COMMENT ON TABLE PPM_VEND_STATION_RECHARGE  IS 'ppm_vend_station_recharge 售电站充值记录';

  CREATE TABLE PPM_VEND_STATION_STREAM 
   (	ID VARCHAR2(32 BYTE), 
	VEND_STATION_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	VEND_STATION_RECHARGE_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	SALES_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	BEFORE_AMOUNT NUMBER(14,4) DEFAULT NULL, 
	AFTER_AMOUNT NUMBER(14,4) DEFAULT NULL, 
	CHANGE_TYPE NUMBER(4,0) DEFAULT NULL, 
	CHANGE_AMOUNT NUMBER(14,4) DEFAULT NULL, 
	USER_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	CREATE_DATE DATE DEFAULT CURRENT_TIMESTAMP, 
	SAVE_DB_DATE DATE DEFAULT CURRENT_TIMESTAMP
   ) ;

   COMMENT ON COLUMN PPM_VEND_STATION_STREAM.ID IS '编号';
   COMMENT ON COLUMN PPM_VEND_STATION_STREAM.VEND_STATION_ID IS '售电站ID';
   COMMENT ON COLUMN PPM_VEND_STATION_STREAM.VEND_STATION_RECHARGE_ID IS '售电站充值记录ID';
   COMMENT ON COLUMN PPM_VEND_STATION_STREAM.SALES_ID IS '售电ID';
   COMMENT ON COLUMN PPM_VEND_STATION_STREAM.BEFORE_AMOUNT IS '变动前总额';
   COMMENT ON COLUMN PPM_VEND_STATION_STREAM.AFTER_AMOUNT IS '变动后总额';
   COMMENT ON COLUMN PPM_VEND_STATION_STREAM.CHANGE_TYPE IS '金额变动类型 （字典表 72： 1=售电站充值，2=用户充值，3=免费token，4=用户充值撤销COMMENT ON COLUMN ppm_vend_station_stream. 5=售电站充值撤销)';
   COMMENT ON COLUMN PPM_VEND_STATION_STREAM.CHANGE_AMOUNT IS '变动金额';
   COMMENT ON COLUMN PPM_VEND_STATION_STREAM.USER_ID IS '操作员ID';
   COMMENT ON COLUMN PPM_VEND_STATION_STREAM.CREATE_DATE IS '创建时间';
   COMMENT ON COLUMN PPM_VEND_STATION_STREAM.SAVE_DB_DATE IS '数据存入数据库时间';
   COMMENT ON TABLE PPM_VEND_STATION_STREAM  IS 'ppm_vend_station_stream 售电站金额变动流水';

  CREATE TABLE PPM_VEND_STATION_STREAM_EXCEPTION 
   (	ID VARCHAR2(32 BYTE), 
	VEND_STATION_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	VEND_STATION_RECHARGE_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	SALES_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	BEFORE_AMOUNT NUMBER(14,4) DEFAULT NULL, 
	AFTER_AMOUNT NUMBER(14,4) DEFAULT NULL, 
	CHANGE_TYPE NUMBER(4,0) DEFAULT NULL, 
	CHANGE_AMOUNT NUMBER(14,4) DEFAULT NULL, 
	USER_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	CREATE_DATE DATE DEFAULT CURRENT_TIMESTAMP, 
	SAVE_DB_DATE DATE DEFAULT CURRENT_TIMESTAMP, 
	README VARCHAR2(500 BYTE) DEFAULT NULL
   ) ;

   COMMENT ON COLUMN PPM_VEND_STATION_STREAM_EXCEPTION.ID IS '异常金额变动流水ID';
   COMMENT ON COLUMN PPM_VEND_STATION_STREAM_EXCEPTION.VEND_STATION_ID IS '售电站ID';
   COMMENT ON COLUMN PPM_VEND_STATION_STREAM_EXCEPTION.VEND_STATION_RECHARGE_ID IS '售电站充值记录ID';
   COMMENT ON COLUMN PPM_VEND_STATION_STREAM_EXCEPTION.SALES_ID IS '售电ID';
   COMMENT ON COLUMN PPM_VEND_STATION_STREAM_EXCEPTION.BEFORE_AMOUNT IS '变动前总额';
   COMMENT ON COLUMN PPM_VEND_STATION_STREAM_EXCEPTION.AFTER_AMOUNT IS '变动后总额';
   COMMENT ON COLUMN PPM_VEND_STATION_STREAM_EXCEPTION.CHANGE_TYPE IS '金额变动类型 （字典表 72： 1=售电站充值，2=用户充值，3=免费token，4=用户充值撤销. 5=售电站充值撤销)';
   COMMENT ON COLUMN PPM_VEND_STATION_STREAM_EXCEPTION.CHANGE_AMOUNT IS '变动金额';
   COMMENT ON COLUMN PPM_VEND_STATION_STREAM_EXCEPTION.USER_ID IS '操作员ID';
   COMMENT ON COLUMN PPM_VEND_STATION_STREAM_EXCEPTION.CREATE_DATE IS '创建时间';
   COMMENT ON COLUMN PPM_VEND_STATION_STREAM_EXCEPTION.SAVE_DB_DATE IS '数据存入数据库时间';
   COMMENT ON COLUMN PPM_VEND_STATION_STREAM_EXCEPTION.README IS '异常说明';
   COMMENT ON TABLE PPM_VEND_STATION_STREAM_EXCEPTION  IS 'ppm_vend_station_stream_exception 售电站金额变动流水异常记录';

  CREATE TABLE PPM_VEND_SWITCH_PAYMENT 
   (	ID VARCHAR2(32 BYTE), 
	CUSTOMER_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	METER_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	SERIAL_NUMBER VARCHAR2(24 BYTE) DEFAULT NULL, 
	TOKEN_TYPE VARCHAR2(4 BYTE) DEFAULT NULL, 
	TOKEN VARCHAR2(45 BYTE) DEFAULT NULL, 
	TOKEN_EXECUTION_STATUS VARCHAR2(45 BYTE) DEFAULT NULL, 
	COMBINED_ACTIVE_ENERGY NUMBER(18,4) DEFAULT NULL, 
	DEBT_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	METER_PREPAIDBALANCE NUMBER(18,4) DEFAULT NULL, 
	BILL_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	USER_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	CREATE_DATE DATE DEFAULT CURRENT_TIMESTAMP, 
	SAVE_DB_DATE DATE DEFAULT CURRENT_TIMESTAMP, 
	README VARCHAR2(256 BYTE) DEFAULT NULL
   ) ;

   COMMENT ON COLUMN PPM_VEND_SWITCH_PAYMENT.ID IS '切换记录ID';
   COMMENT ON COLUMN PPM_VEND_SWITCH_PAYMENT.CUSTOMER_ID IS '客户ID';
   COMMENT ON COLUMN PPM_VEND_SWITCH_PAYMENT.METER_ID IS '电表ID';
   COMMENT ON COLUMN PPM_VEND_SWITCH_PAYMENT.SERIAL_NUMBER IS '电表资产编号';
   COMMENT ON COLUMN PPM_VEND_SWITCH_PAYMENT.TOKEN_TYPE IS 'Token类型(字典类型：54COMMENT ON COLUMN ppm_vend_switch_payment.21=后付费转预付费Token 22=预付费转后付费Token ）';
   COMMENT ON COLUMN PPM_VEND_SWITCH_PAYMENT.TOKEN IS '20位Token值';
   COMMENT ON COLUMN PPM_VEND_SWITCH_PAYMENT.TOKEN_EXECUTION_STATUS IS 'Tokne执行状态（字典表93 0= invaild无效 1= valid 有效 2=to be confirmed 待确认）';
   COMMENT ON COLUMN PPM_VEND_SWITCH_PAYMENT.COMBINED_ACTIVE_ENERGY IS '组合有功用电量 （此值仅在后付费转预付费有用），此字段是后付费转预付费时，读取电表中的组合有功，再根据当月的费率计算出电费，以便生产债务，下月收取。';
   COMMENT ON COLUMN PPM_VEND_SWITCH_PAYMENT.DEBT_ID IS '债务ID--后付费模式转预付费模式，所生成的债务编号';
   COMMENT ON COLUMN PPM_VEND_SWITCH_PAYMENT.METER_PREPAIDBALANCE IS '电表预付费余额 （此值仅在预付费转后付费有用），此字段是预付费转后付费时，读取电表中的剩余电费，在下个月的后付费账单中进行抵扣。';
   COMMENT ON COLUMN PPM_VEND_SWITCH_PAYMENT.BILL_ID IS '账单编号 --预付费转后付费，进行抵扣的后付费账单ID';
   COMMENT ON COLUMN PPM_VEND_SWITCH_PAYMENT.USER_ID IS '操作员ID';
   COMMENT ON COLUMN PPM_VEND_SWITCH_PAYMENT.CREATE_DATE IS '创建日期';
   COMMENT ON COLUMN PPM_VEND_SWITCH_PAYMENT.SAVE_DB_DATE IS '数据存入数据库时间';
   COMMENT ON COLUMN PPM_VEND_SWITCH_PAYMENT.README IS '备注';
   COMMENT ON TABLE PPM_VEND_SWITCH_PAYMENT  IS 'ppm_vend_switch_payment 电表付费切换记录表';

   CREATE TABLE PPM_VEND_TOKEN_MANAGE 
   (	UTILITY_ID VARCHAR2(32 BYTE), 
	ID VARCHAR2(32 BYTE), 
	ORG_ID VARCHAR2(100 BYTE) DEFAULT NULL, 
	STATION_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	METER_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	USER_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	SALES_ID VARCHAR2(32 BYTE) DEFAULT NULL, 
	TOKEN_TYPE VARCHAR2(4 BYTE) DEFAULT NULL, 
	TOKEN VARCHAR2(254 BYTE) DEFAULT NULL, 
	CREATE_TOKEN_DATE DATE DEFAULT NULL, 
	CONSUMER_PAYMENT_MONEY NUMBER(10,2) DEFAULT NULL, 
	DEDUCTIONS_OTHER_MONEY NUMBER(10,2) DEFAULT NULL, 
	ACTUAL_RECHARGE_AMOUNT NUMBER(10,2) DEFAULT NULL, 
	DEDUCTIONS_OTHER_MONEY_DETAILS VARCHAR2(1000 BYTE) DEFAULT NULL, 
	SET_LOAD_THRESHOLD NUMBER(10,2) DEFAULT '0.00', 
	SAVE_DB_DATE DATE DEFAULT CURRENT_TIMESTAMP, 
	README VARCHAR2(256 BYTE) DEFAULT NULL
   ) ;

   COMMENT ON COLUMN PPM_VEND_TOKEN_MANAGE.UTILITY_ID IS '租户ID,通过租户ID 区分系统中 信息的归属。';

   CREATE TABLE PPM_VENDING_STATION 
   (	ID VARCHAR2(32 BYTE), 
	ORG_ID VARCHAR2(32 BYTE), 
	STATION_CODE VARCHAR2(32 BYTE), 
	STATION_NAME VARCHAR2(256 BYTE), 
	STATION_TYPE NUMBER(4,0) DEFAULT '0', 
	STATION_ADDRESS VARCHAR2(256 BYTE), 
	LONGITUGE VARCHAR2(20 BYTE), 
	LATITUDE VARCHAR2(20 BYTE), 
	TELEPHONE_NO VARCHAR2(20 BYTE), 
	STATION_STATUS DECIMAL(2,0), 
	COMMISSION DECIMAL(14,4) DEFAULT '0.0000', 
	TAX DECIMAL(14,4) DEFAULT '0.0000', 
	MAX_LIMT_AMOUNT DECIMAL(14,4), 
	AVAILABLE_ELECTRICITY DECIMAL(14,4) DEFAULT '0.0000', 
	VERSION_OPTIMISTIC_LOCKING VARCHAR2(32 BYTE)
   ) ;

   COMMENT ON COLUMN PPM_VENDING_STATION.STATION_TYPE IS '1=营业大厅 2=直属售电点 3=代售点';
   COMMENT ON COLUMN PPM_VENDING_STATION.STATION_STATUS IS '1=启用  2=失效 3=作废';
   COMMENT ON COLUMN PPM_VENDING_STATION.COMMISSION IS '佣金比例';
   COMMENT ON COLUMN PPM_VENDING_STATION.TAX IS '税费比例';
   COMMENT ON COLUMN PPM_VENDING_STATION.MAX_LIMT_AMOUNT IS '最大限额';
   COMMENT ON COLUMN PPM_VENDING_STATION.AVAILABLE_ELECTRICITY IS '剩余金额';
   COMMENT ON COLUMN PPM_VENDING_STATION.VERSION_OPTIMISTIC_LOCKING IS 'uuid乐观锁';

  CREATE UNIQUE INDEX PPM_ASSET_METER_UK1 ON PPM_ASSET_METER (STS_MAC) 
  ;

  CREATE UNIQUE INDEX PPM_ASSET_METER_PK ON PPM_ASSET_METER (ID) 
  ;

  CREATE UNIQUE INDEX SYS_C0016768 ON PPM_ASSET_METER_CHANGE (CUSTOMER_ID, TIME_TAG) 
  ;

  CREATE UNIQUE INDEX PPM_ASSET_TARIFF_SOLUTION_PK ON PPM_ASSET_TARIFF_GROUP (ID) 
  ;

  CREATE UNIQUE INDEX PPM_ASSET_TARIFF_STEP_PK ON PPM_ASSET_TARIFF_STEP (GROUP_ID, STEP_INDEX) 
  ;

  CREATE UNIQUE INDEX PPM_ASSET_TARIFF_STEP_DETA_PK ON PPM_ASSET_TARIFF_STEP_DETAIL (GROUP_ID, STEP_INDEX, TAX_TYPE) 
  ;

  CREATE UNIQUE INDEX PPM_CUSTOMER_HISTORY_TARIF_PK ON PPM_CUSTOMER_HISTORY_TARIFF (ID, ACTIVE_TV) 
  ;

  CREATE UNIQUE INDEX PPM_CUSTOMER_TARIFF_MAP_PK ON PPM_CUSTOMER_TARIFF_MAP (ID) 
  ;

  CREATE UNIQUE INDEX PPM_DATA_PREPAID_PROGRESS_PK ON PPM_DATA_PREPAID_PROGRESS (CUSTOMER_ID) 
  ;

  CREATE UNIQUE INDEX PPM_DATA_STATISTICS_CUSTOM_PK ON PPM_DATA_STATISTICS_CUSTOMER (ORG_ID, TV, TV_TYPE, DATA_TYPE) 
  ;

  CREATE UNIQUE INDEX PPM_DICT_DETAIL_PK ON PPM_DICT_DETAIL (DICT_ID, INNER_VALUE) 
  ;

  CREATE UNIQUE INDEX PPM_DICT_FUNCTION_PK ON PPM_DICT_FUNCTION (ID) 
  ;

  CREATE UNIQUE INDEX PPM_DICT_OPERATION_PK ON PPM_DICT_OPERATION (ID) 
  ;

  CREATE UNIQUE INDEX SYS_C008965 ON PPM_INTEGRATION_LOG (ID) 
  ;

  CREATE UNIQUE INDEX PPM_SGC_KEYFILE_PK ON PPM_SGC_KEYFILE (DEVICE_ID, SUPPLY_GROUP_CODE) 
  ;

  CREATE UNIQUE INDEX PPM_SYS_MENU_PK ON PPM_SYS_MENU (ID) 
  ;

  CREATE UNIQUE INDEX PPM_SYS_ROLE_PK ON PPM_SYS_ROLE (ID) 
  ;

  CREATE UNIQUE INDEX PPM_SYS_ROLE_MENU_PK ON PPM_SYS_ROLE_MENU (ROLE_ID, MENU_ID) 
  ;

  CREATE UNIQUE INDEX PPM_SYS_USER_PK ON PPM_SYS_USER (ID) 
  ;

  CREATE UNIQUE INDEX PK_TOTAL_CONSUMPTION ON PPM_TOTAL_MONTH_ELECTRICITY_CONSUMPTION (ID) 
  ;

  CREATE UNIQUE INDEX SYS_C0015557 ON PPM_TOTAL_MONTH_SALES_AMOUNT (ID) 
  ;

  CREATE UNIQUE INDEX SYS_C0013009 ON PPM_VEND_CUSTOMER_DEBT (ID) 
  ;

  CREATE INDEX PPM_VEND_CUSTOMER_DEBT_INDEX1 ON PPM_VEND_CUSTOMER_DEBT (CUSTOMER_ID) 
  ;

  CREATE UNIQUE INDEX SYS_C0013562 ON PPM_VEND_CUSTOMER_DEBT_RESTORE (ID) 
  ;

  CREATE UNIQUE INDEX SYS_C0012980 ON PPM_VEND_FREE_TOKEN_MANAGE (ID) 
  ;

  CREATE UNIQUE INDEX SYS_C0012984 ON PPM_VEND_HISTORICAL_INFO (ID) 
  ;

  CREATE INDEX IDX_VEND_HISTORICAL_INFO_METER_ID ON PPM_VEND_HISTORICAL_INFO (METER_ID) 
  ;

  CREATE INDEX IDX_VEND_HISTORICAL_INFO_CUSTOMER_ID ON PPM_VEND_HISTORICAL_INFO (CUSTOMER_ID) 
  ;

  CREATE INDEX IDX_VEND_HISTORICAL_INFO_USER_ID ON PPM_VEND_HISTORICAL_INFO (USER_ID) 
  ;

  CREATE INDEX IDX_VEND_HISTORICAL_INFO_PAYMENT_TYPE ON PPM_VEND_HISTORICAL_INFO (PAYMENT_TYPE) 
  ;

  CREATE INDEX IDX_VEND_HISTORICAL_INFO_RECHARGE_TYPE ON PPM_VEND_HISTORICAL_INFO (RECHARGE_TYPE) 
  ;

  CREATE UNIQUE INDEX SYS_C0013017 ON PPM_VEND_INITIAL_CREDIT_AMOUNT (ID) 
  ;

  CREATE UNIQUE INDEX SYS_C0012996 ON PPM_VEND_INVOICE (ID) 
  ;

  CREATE UNIQUE INDEX SYS_C0013000 ON PPM_VEND_INVOICE_DETAILS (ID) 
  ;

  CREATE INDEX IDX_REPORT_VEND_INVOICE_DETAILS_SALES_ID ON PPM_VEND_INVOICE_DETAILS (SALES_ID) 
  ;

  CREATE INDEX IDX_REPORT_VEND_INVOICE_DETAILS_TAX_TYPE ON PPM_VEND_INVOICE_DETAILS (TAX_TYPE) 
  ;

  CREATE UNIQUE INDEX SYS_C0012992 ON PPM_VEND_INVOICE_FREE_TOKEN (ID) 
  ;

  CREATE UNIQUE INDEX SYS_C0013021 ON PPM_VEND_STATION_RECHARGE (ID) 
  ;

  CREATE UNIQUE INDEX SYS_C0013023 ON PPM_VEND_STATION_STREAM (ID) 
  ;

  CREATE UNIQUE INDEX SYS_C0013025 ON PPM_VEND_STATION_STREAM_EXCEPTION (ID) 
  ;

  CREATE UNIQUE INDEX SYS_C0013019 ON PPM_VEND_SWITCH_PAYMENT (ID) 
  ;

  CREATE UNIQUE INDEX SYS_C0013028 ON PPM_VEND_TOKEN_MANAGE (ID) 
  ;

  CREATE UNIQUE INDEX PPM_VENDING_STATION_PK ON PPM_VENDING_STATION (ID) 
  ;

  ALTER TABLE PPM_ASSET_METER MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_ASSET_METER ADD CONSTRAINT PPM_ASSET_METER_PK PRIMARY KEY (ID)
  USING INDEX  ENABLE;
  ALTER TABLE PPM_ASSET_METER ADD CONSTRAINT PPM_ASSET_METER_UK1 UNIQUE (STS_MAC)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_ASSET_METER_CHANGE MODIFY (CUSTOMER_ID NOT NULL ENABLE);
  ALTER TABLE PPM_ASSET_METER_CHANGE MODIFY (TIME_TAG NOT NULL ENABLE);
  ALTER TABLE PPM_ASSET_METER_CHANGE MODIFY (OLD_METER_ID NOT NULL ENABLE);
  ALTER TABLE PPM_ASSET_METER_CHANGE MODIFY (NEW_METER_ID NOT NULL ENABLE);
  ALTER TABLE PPM_ASSET_METER_CHANGE MODIFY (OLD_SERIAL_NUMBER NOT NULL ENABLE);
  ALTER TABLE PPM_ASSET_METER_CHANGE MODIFY (NEW_SERIAL_NUMBER NOT NULL ENABLE);
  ALTER TABLE PPM_ASSET_METER_CHANGE MODIFY (DATA_SOURCE NOT NULL ENABLE);
  ALTER TABLE PPM_ASSET_METER_CHANGE MODIFY (OLD_COMBINED_ACTIVE_ENERGY NOT NULL ENABLE);
  ALTER TABLE PPM_ASSET_METER_CHANGE MODIFY (NEW_COMBINED_ACTIVE_ENERGY NOT NULL ENABLE);
  ALTER TABLE PPM_ASSET_METER_CHANGE MODIFY (ABNORMAL_PERIOD_COMBINED_ACTIVE_ENERGY NOT NULL ENABLE);
  ALTER TABLE PPM_ASSET_METER_CHANGE MODIFY (PROCESSING_TYPE NOT NULL ENABLE);
  ALTER TABLE PPM_ASSET_METER_CHANGE ADD PRIMARY KEY (CUSTOMER_ID, TIME_TAG)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_ASSET_TARIFF_GROUP MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_ASSET_TARIFF_GROUP ADD CONSTRAINT PPM_ASSET_TARIFF_SOLUTION_PK PRIMARY KEY (ID)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_ASSET_TARIFF_STEP MODIFY (GROUP_ID NOT NULL ENABLE);
  ALTER TABLE PPM_ASSET_TARIFF_STEP MODIFY (STEP_INDEX NOT NULL ENABLE);
  ALTER TABLE PPM_ASSET_TARIFF_STEP ADD CONSTRAINT PPM_ASSET_TARIFF_STEP_PK PRIMARY KEY (GROUP_ID, STEP_INDEX)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_ASSET_TARIFF_STEP_DETAIL MODIFY (TAX_TYPE NOT NULL ENABLE);
  ALTER TABLE PPM_ASSET_TARIFF_STEP_DETAIL MODIFY (GROUP_ID NOT NULL ENABLE);
  ALTER TABLE PPM_ASSET_TARIFF_STEP_DETAIL MODIFY (STEP_INDEX NOT NULL ENABLE);
  ALTER TABLE PPM_ASSET_TARIFF_STEP_DETAIL MODIFY (TARIFF_NAME NOT NULL ENABLE);
  ALTER TABLE PPM_ASSET_TARIFF_STEP_DETAIL ADD CONSTRAINT PPM_ASSET_TARIFF_STEP_DETA_PK PRIMARY KEY (GROUP_ID, STEP_INDEX, TARIFF_NAME)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_CUSTOMER_HISTORY_TARIFF MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_CUSTOMER_HISTORY_TARIFF MODIFY (TYPE NOT NULL ENABLE);
  ALTER TABLE PPM_CUSTOMER_HISTORY_TARIFF MODIFY (ACTIVE_TV NOT NULL ENABLE);
  ALTER TABLE PPM_CUSTOMER_HISTORY_TARIFF ADD CONSTRAINT PPM_CUSTOMER_HISTORY_TARIF_PK PRIMARY KEY (ID, ACTIVE_TV)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_CUSTOMER_TARIFF_MAP MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_CUSTOMER_TARIFF_MAP MODIFY (TYPE NOT NULL ENABLE);
  ALTER TABLE PPM_CUSTOMER_TARIFF_MAP ADD CONSTRAINT PPM_CUSTOMER_TARIFF_MAP_PK PRIMARY KEY (ID)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_DATA_PREPAID_PROGRESS MODIFY (CUSTOMER_ID NOT NULL ENABLE);
  ALTER TABLE PPM_DATA_PREPAID_PROGRESS MODIFY (LAST_PAYMENT_TV NOT NULL ENABLE);
  ALTER TABLE PPM_DATA_PREPAID_PROGRESS ADD CONSTRAINT PPM_DATA_PREPAID_PROGRESS_PK PRIMARY KEY (CUSTOMER_ID)
  USING INDEX  ENABLE;
  ALTER TABLE PPM_DATA_PREPAID_PROGRESS MODIFY (METER_COMMISSIONING_DATE NOT NULL ENABLE);

  ALTER TABLE PPM_DATA_STATISTICS_CUSTOMER MODIFY (ORG_ID NOT NULL ENABLE);
  ALTER TABLE PPM_DATA_STATISTICS_CUSTOMER MODIFY (DATA_TYPE NOT NULL ENABLE);
  ALTER TABLE PPM_DATA_STATISTICS_CUSTOMER MODIFY (TV NOT NULL ENABLE);
  ALTER TABLE PPM_DATA_STATISTICS_CUSTOMER MODIFY (TV_TYPE NOT NULL ENABLE);
  ALTER TABLE PPM_DATA_STATISTICS_CUSTOMER ADD CONSTRAINT PPM_DATA_STATISTICS_CUSTOM_PK PRIMARY KEY (ORG_ID, TV, TV_TYPE, DATA_TYPE)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_DATA_USER_LOG MODIFY (TV NOT NULL ENABLE);
  ALTER TABLE PPM_DATA_USER_LOG MODIFY (USER_ID NOT NULL ENABLE);
  ALTER TABLE PPM_DATA_USER_LOG MODIFY (LOG_TYPE NOT NULL ENABLE);
  ALTER TABLE PPM_DATA_USER_LOG MODIFY (LOG_SUB_TYPE NOT NULL ENABLE);

  ALTER TABLE PPM_DICT_DETAIL MODIFY (DICT_ID NOT NULL ENABLE);
  ALTER TABLE PPM_DICT_DETAIL MODIFY (INNER_VALUE NOT NULL ENABLE);
  ALTER TABLE PPM_DICT_DETAIL ADD CONSTRAINT PPM_DICT_DETAIL_PK PRIMARY KEY (DICT_ID, INNER_VALUE)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_DICT_FUNCTION MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_DICT_FUNCTION MODIFY (FUNCTIONNAME NOT NULL ENABLE);
  ALTER TABLE PPM_DICT_FUNCTION ADD CONSTRAINT PPM_DICT_FUNCTION_PK PRIMARY KEY (ID)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_DICT_OPERATION ADD CONSTRAINT PPM_DICT_OPERATION_PK PRIMARY KEY (ID)
  USING INDEX  ENABLE;
  ALTER TABLE PPM_DICT_OPERATION MODIFY (ID NOT NULL ENABLE);

  ALTER TABLE PPM_DICT_USER_LOG MODIFY (LOG_TYPE NOT NULL ENABLE);
  ALTER TABLE PPM_DICT_USER_LOG MODIFY (LOG_SUB_TYPE NOT NULL ENABLE);

  ALTER TABLE PPM_INTEGRATION_LOG MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_INTEGRATION_LOG ADD PRIMARY KEY (ID)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_SGC_KEYFILE MODIFY (DEVICE_ID NOT NULL ENABLE);
  ALTER TABLE PPM_SGC_KEYFILE MODIFY (SUPPLY_GROUP_CODE NOT NULL ENABLE);
  ALTER TABLE PPM_SGC_KEYFILE ADD CONSTRAINT PPM_SGC_KEYFILE_PK PRIMARY KEY (DEVICE_ID, SUPPLY_GROUP_CODE)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_SYS_MENU MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_SYS_MENU MODIFY (MENUNAME NOT NULL ENABLE);
  ALTER TABLE PPM_SYS_MENU ADD CONSTRAINT PPM_SYS_MENU_PK PRIMARY KEY (ID)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_SYS_ROLE MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_SYS_ROLE MODIFY (NAME NOT NULL ENABLE);
  ALTER TABLE PPM_SYS_ROLE ADD CONSTRAINT PPM_SYS_ROLE_PK PRIMARY KEY (ID)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_SYS_ROLE_MENU MODIFY (ROLE_ID NOT NULL ENABLE);
  ALTER TABLE PPM_SYS_ROLE_MENU MODIFY (MENU_ID NOT NULL ENABLE);
  ALTER TABLE PPM_SYS_ROLE_MENU ADD CONSTRAINT PPM_SYS_ROLE_MENU_PK PRIMARY KEY (ROLE_ID, MENU_ID)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_SYS_USER MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_SYS_USER MODIFY (NAME NOT NULL ENABLE);
  ALTER TABLE PPM_SYS_USER MODIFY (USERNAME NOT NULL ENABLE);
  ALTER TABLE PPM_SYS_USER MODIFY (PASSWORD NOT NULL ENABLE);
  ALTER TABLE PPM_SYS_USER ADD CONSTRAINT PPM_SYS_USER_PK PRIMARY KEY (ID)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_TOTAL_MONTH_ELECTRICITY_CONSUMPTION MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_TOTAL_MONTH_ELECTRICITY_CONSUMPTION MODIFY (ORG_ID NOT NULL ENABLE);
  ALTER TABLE PPM_TOTAL_MONTH_ELECTRICITY_CONSUMPTION ADD CONSTRAINT PK_TOTAL_CONSUMPTION PRIMARY KEY (ID)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_TOTAL_MONTH_SALES_AMOUNT MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_TOTAL_MONTH_SALES_AMOUNT ADD PRIMARY KEY (ID)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_VEND_CUSTOMER_DEBT MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_CUSTOMER_DEBT MODIFY (DEBT_DATE NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_CUSTOMER_DEBT MODIFY (RECORD_STATE NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_CUSTOMER_DEBT MODIFY (DEBT_COLLECT_MONTH NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_CUSTOMER_DEBT MODIFY (DEBT_COLLECT_DATE NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_CUSTOMER_DEBT ADD PRIMARY KEY (ID)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_VEND_CUSTOMER_DEBT_RESTORE MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_CUSTOMER_DEBT_RESTORE MODIFY (METER_DEBT_ID NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_CUSTOMER_DEBT_RESTORE MODIFY (RECORD_STATE NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_CUSTOMER_DEBT_RESTORE ADD PRIMARY KEY (ID)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_VEND_FREE_TOKEN_MANAGE MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_FREE_TOKEN_MANAGE MODIFY (CUSTOMER_NAME NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_FREE_TOKEN_MANAGE MODIFY (SERIAL_NUMBER NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_FREE_TOKEN_MANAGE MODIFY (COMM_ADDRESS NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_FREE_TOKEN_MANAGE MODIFY (USER_NAME NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_FREE_TOKEN_MANAGE MODIFY (FREE_TOKEN_DESCRIPTION NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_FREE_TOKEN_MANAGE ADD PRIMARY KEY (ID)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_VEND_HISTORICAL_INFO MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_HISTORICAL_INFO MODIFY (METER_ID NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_HISTORICAL_INFO MODIFY (USER_ID NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_HISTORICAL_INFO ADD PRIMARY KEY (ID)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_VEND_INITIAL_CREDIT_AMOUNT MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_INITIAL_CREDIT_AMOUNT MODIFY (STATE NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_INITIAL_CREDIT_AMOUNT ADD PRIMARY KEY (ID)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_VEND_INVOICE MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_INVOICE MODIFY (CUSTOMER_PAYMENT_AMOUNT NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_INVOICE MODIFY (RECHARGE_AMOUNT NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_INVOICE ADD PRIMARY KEY (ID)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_VEND_INVOICE_DETAILS MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_INVOICE_DETAILS MODIFY (DETAILS_VALUE NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_INVOICE_DETAILS MODIFY (DETAILS_DATE NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_INVOICE_DETAILS ADD PRIMARY KEY (ID)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_VEND_INVOICE_FREE_TOKEN MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_INVOICE_FREE_TOKEN MODIFY (STATION_ID NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_INVOICE_FREE_TOKEN MODIFY (CUSTOMER_NAME NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_INVOICE_FREE_TOKEN MODIFY (SERIAL_NUMBER NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_INVOICE_FREE_TOKEN MODIFY (COMM_ADDRESS NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_INVOICE_FREE_TOKEN MODIFY (FREE_RECHARGE_AMOUNT NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_INVOICE_FREE_TOKEN MODIFY (USER_NAME NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_INVOICE_FREE_TOKEN ADD PRIMARY KEY (ID)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_VEND_STATION_RECHARGE MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_STATION_RECHARGE ADD PRIMARY KEY (ID)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_VEND_STATION_STREAM MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_STATION_STREAM ADD PRIMARY KEY (ID)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_VEND_STATION_STREAM_EXCEPTION MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_STATION_STREAM_EXCEPTION ADD PRIMARY KEY (ID)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_VEND_SWITCH_PAYMENT MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_SWITCH_PAYMENT ADD PRIMARY KEY (ID)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_VEND_TOKEN_MANAGE MODIFY (UTILITY_ID NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_TOKEN_MANAGE MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_VEND_TOKEN_MANAGE ADD PRIMARY KEY (ID)
  USING INDEX  ENABLE;

  ALTER TABLE PPM_VENDING_STATION MODIFY (ID NOT NULL ENABLE);
  ALTER TABLE PPM_VENDING_STATION ADD CONSTRAINT PPM_VENDING_STATION_PK PRIMARY KEY (ID)
  USING INDEX  ENABLE;
