/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataVeeEvent{ } 
 * 
 * 摘    要： dataVeeEvent
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-12-20 04:22:44
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.vee;

import java.util.Date;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DataVeeEvent  extends BaseEntity {
	private static final long serialVersionUID = -8989600490461317054L;

	private Date	tv;
	private String 	eventId;
	private String 	eventDetail;
	private String 	ruleId;
	
	
	private String  meterSn;
	private String  classId;
	private String  eventName;
	private String  ruleName;
	
	
	
	public DataVeeEvent() {	}
	
	
	public String getMeterSn() {
		return meterSn;
	}


	public void setMeterSn(String meterSn) {
		this.meterSn = meterSn;
	}


	public String getClassId() {
		return classId;
	}


	public void setClassId(String classId) {
		this.classId = classId;
	}


	public String getEventName() {
		return eventName;
	}


	public void setEventName(String eventName) {
		this.eventName = eventName;
	}


	public String getRuleName() {
		return ruleName;
	}


	public void setRuleName(String ruleName) {
		this.ruleName = ruleName;
	}


	public String getRuleId() {
		return ruleId;
	}

	public void setRuleId(String ruleId) {
		this.ruleId = ruleId;
	}

	
	public java.util.Date getTv() {
		return tv;
	}
	
	public void setTv(java.util.Date tv) {
		this.tv = tv;
	}
	
	public String getEventId() {
		return eventId;
	}

	public void setEventId(String eventId) {
		this.eventId = eventId;
	}
	public String getEventDetail() {
		return eventDetail;
	}

	public void setEventDetail(String eventDetail) {
		this.eventDetail = eventDetail;
	}

	public DataVeeEvent(java.util.Date tv ,String eventId ,String eventDetail ) {
		super();
		this.tv = tv;
		this.eventId = eventId;
		this.eventDetail = eventDetail;
	}

}