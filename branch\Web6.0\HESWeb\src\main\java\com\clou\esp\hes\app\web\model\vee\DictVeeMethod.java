/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictVeeMethod{ } 
 * 
 * 摘    要： dictVeeMethod
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2019-03-04 09:36:59
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.vee;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DictVeeMethod  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DictVeeMethod() {
	}

	/**name*/
	private java.lang.String name;
	/**sortId*/
	private java.math.BigDecimal sortId;
	/**type*/
	private java.math.BigDecimal type;
	/**packageId*/
	private java.lang.String packageId;

	/**
	 * name
	 * @return the value of DICT_VEE_METHOD.NAME
	 * @mbggenerated 2019-03-04 09:36:59
	 */
	public java.lang.String getName() {
		return name;
	}

	/**
	 * name
	 * @param name the value for DICT_VEE_METHOD.NAME
	 * @mbggenerated 2019-03-04 09:36:59
	 */
    	public void setName(java.lang.String name) {
		this.name = name;
	}
	/**
	 * sortId
	 * @return the value of DICT_VEE_METHOD.SORT_ID
	 * @mbggenerated 2019-03-04 09:36:59
	 */
	public java.math.BigDecimal getSortId() {
		return sortId;
	}

	/**
	 * sortId
	 * @param sortId the value for DICT_VEE_METHOD.SORT_ID
	 * @mbggenerated 2019-03-04 09:36:59
	 */
    	public void setSortId(java.math.BigDecimal sortId) {
		this.sortId = sortId;
	}
	/**
	 * type
	 * @return the value of DICT_VEE_METHOD.TYPE
	 * @mbggenerated 2019-03-04 09:36:59
	 */
	public java.math.BigDecimal getType() {
		return type;
	}

	/**
	 * type
	 * @param type the value for DICT_VEE_METHOD.TYPE
	 * @mbggenerated 2019-03-04 09:36:59
	 */
    	public void setType(java.math.BigDecimal type) {
		this.type = type;
	}
	/**
	 * packageId
	 * @return the value of DICT_VEE_METHOD.PACKAGE_ID
	 * @mbggenerated 2019-03-04 09:36:59
	 */
	public java.lang.String getPackageId() {
		return packageId;
	}

	/**
	 * packageId
	 * @param packageId the value for DICT_VEE_METHOD.PACKAGE_ID
	 * @mbggenerated 2019-03-04 09:36:59
	 */
    	public void setPackageId(java.lang.String packageId) {
		this.packageId = packageId;
	}

	public DictVeeMethod(java.lang.String name 
	,java.math.BigDecimal sortId 
	,java.math.BigDecimal type 
	,java.lang.String packageId ) {
		super();
		this.name = name;
		this.sortId = sortId;
		this.type = type;
		this.packageId = packageId;
	}

}