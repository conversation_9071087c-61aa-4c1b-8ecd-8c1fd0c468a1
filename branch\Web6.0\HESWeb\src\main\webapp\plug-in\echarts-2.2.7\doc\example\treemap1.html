<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="ECharts">
    <meta name="author" content="<EMAIL>">
    <title>ECharts · Example</title>

    <link rel="shortcut icon" href="../asset/ico/favicon.png">

    <link href="../asset/css/font-awesome.min.css" rel="stylesheet">
    <link href="../asset/css/bootstrap.css" rel="stylesheet">
    <link href="../asset/css/carousel.css" rel="stylesheet">
    <link href="../asset/css/echartsHome.css" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->

    <script src="./www/js/echarts.js"></script>
    <script src="../asset/js/codemirror.js"></script>
    <script src="../asset/js/javascript.js"></script>

    <link href="../asset/css/codemirror.css" rel="stylesheet">
    <link href="../asset/css/monokai.css" rel="stylesheet">
</head>

<body>
    <!-- Fixed navbar -->
    <div class="navbar navbar-default navbar-fixed-top" role="navigation" id="head"></div>

    <div class="container-fluid">
        <div class="row-fluid example">
            <div id="sidebar-code" class="col-md-4">
                <div class="well sidebar-nav">
                    <div class="nav-header"><a href="#" onclick="autoResize()" class="glyphicon glyphicon-resize-full" id ="icon-resize" ></a>option</div>
                    <textarea id="code" name="code">
option = {
    title : {
        text: '手机占有率',
        subtext: '数据下钻例子，虚构数据'
    },
    tooltip : {
        trigger: 'item',
        formatter: "{b}: {c}"
    },
    toolbox: {
        show : true,
        feature : {
            mark : {show: true},
            dataView : {show: true, readOnly: false},
            restore : {show: true},
            saveAsImage : {show: true}
        }
    },
    calculable : false,
    series : [
        {
            name:'手机占有率',
            type:'treemap',
            itemStyle: {
                normal: {
                    label: {
                        show: true,
                        formatter: "{b}"
                    },
                    borderWidth: 1,
                    borderColor: '#ccc'
                },
                emphasis: {
                    label: {
                        show: true
                    },
                    color: '#cc99cc',
                    borderWidth: 3,
                    borderColor: '#996699'
                }
            },
            data:[
                {
                    name: '三星',
                    itemStyle: {
                        normal: {
                            color: '#99cccc',
                        }
                    },
                    value: 6,
                    children: [
                        {
                            name: 'Galaxy S4',
                            value: 2
                        },
                        {
                            name: 'Galaxy S5',
                            value: 3
                        },
                        {
                            name: 'Galaxy S6',
                            value: 3
                        },
                        {
                            name: 'Galaxy Tab',
                            value: 1
                        }
                    ]
                },
                {
                    name: '小米',
                    itemStyle: {
                        normal: {
                            color: '#99ccff',
                        }
                    },
                    value: 4,
                    children: [
                        {
                            name: '小米3',
                            value: 6
                        },
                        {
                            name: '小米4',
                            value: 6
                        },
                        {
                            name: '红米',
                            value: 4
                        }
                    ]
                },
                {
                    name: '苹果',
                    itemStyle: {
                        normal: {
                            color: '#9999cc',
                        }
                    },
                    value: 4,
                    children: [
                        {
                            name: 'iPhone 5s',
                            value: 6
                        },
                        {
                            name: 'iPhone 6',
                            value: 3
                        },
                        {
                            name: 'iPhone 6+',
                            value: 3
                        }
                    ]
                },
                {
                    name: '魅族',
                    itemStyle: {
                        normal: {
                            color: '#ccff99',
                        }
                    },
                    value: 1,
                    children: [
                        {
                            name: 'MX4',
                            itemStyle: {
                                normal: {
                                    color: '#ccccff',
                                }
                            },
                            value: 6
                        },
                        {
                            name: 'MX3',
                            itemStyle: {
                                normal: {
                                    color: '#99ccff',
                                }
                            },
                            value: 6
                        },
                        {
                            name: '魅蓝note',
                            itemStyle: {
                                normal: {
                                    color: '#9999cc',
                                }
                            },
                            value: 4
                        },
                        {
                            name: 'MX4 pro',
                            itemStyle: {
                                normal: {
                                    color: '#99cccc',
                                }
                            },
                            value: 3
                        }
                    ]
                },
                {
                    name: '华为',
                    itemStyle: {
                        normal: {
                            color: '#ccffcc',
                        }
                    },
                    value: 2
                },
                {
                    name: '联想',
                    itemStyle: {
                        normal: {
                            color: '#ccccff',
                        }
                    },
                    value: 2
                },
                {
                    name: '中兴',
                    itemStyle: {
                        normal: {
                            color: '#ffffcc',
                        }
                    },
                    value: 1,
                    children: [
                        {
                            name: 'V5',
                            value: 16
                        },
                        {
                            name: '努比亚',
                            value: 6
                        },
                        {
                            name: '功能机',
                            value: 4
                        },
                        {
                            name: '青漾',
                            value: 4
                        },
                        {
                            name: '星星',
                            value: 4
                        },
                        {
                            name: '儿童机',
                            value: 1
                        }
                    ]
                }
            ]
        }
    ]
};
                    </textarea>
              </div><!--/.well -->
            </div><!--/span-->
            <div id="graphic" class="col-md-8">
                <div id="main" class="main"></div>
                <div>
                    <button type="button" class="btn btn-sm btn-success" onclick="refresh(true)">刷 新</button>
                    <span class="text-primary">切换主题</span>
                    <select id="theme-select"></select>

                    <span id='wrong-message' style="color:red"></span>
                </div>
            </div><!--/span-->
        </div><!--/row-->
        
        </div><!--/.fluid-container-->

    <footer id="footer"></footer>
    <!-- Le javascript
    ================================================== -->
    <!-- Placed at the end of the document so the pages load faster -->
    <script src="../asset/js/jquery.min.js"></script>
    <script type="text/javascript" src="../asset/js/echartsHome.js"></script>
    <script src="../asset/js/bootstrap.min.js"></script>
    <script src="../asset/js/echartsExample.js"></script>
</body>
</html>
