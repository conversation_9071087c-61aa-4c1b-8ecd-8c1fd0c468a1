package clouesp.hes.core.uci.soap.custom.webservice.common;


/**
 * @ClassName: BaudType
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年8月7日 上午10:39:41
 *
 */
public enum BaudRateEnum {
	Baud_0(0,"300"),
	<PERSON><PERSON>_600(1,"600"),
	Ba<PERSON>_1200(2,"1200"),
	Baud_2400(3,"2400"),
	Baud_4800(4,"4800"),
	Baud_9600(5,"9600"),
	Baud_19200(6,"19200"),
	Baud_38400(7,"38400"),
	Baud_57600(8,"57600"),
	Baud_115200(9,"115200");
	
	private BaudRateEnum(int index,String baud) {
		this.index = index;
		this.baud = baud;
	}
	
	public static BaudRateEnum parseBaudRate(String baud) {
		for (BaudRateEnum type : values()) {
			if(type.baud.equals(baud))
				return type;
		}
		return null;
	}
	
	private int index;
	private String baud;
	
	public int getIndex() {
		return index;
	}
	public String getBaud() {
		return baud;
	}
	
	
	
}
