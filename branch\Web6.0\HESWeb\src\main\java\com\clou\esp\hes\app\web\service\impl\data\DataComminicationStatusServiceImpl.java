/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataComminicationStatus{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：赵久霆
 * 创建于：2018-11-19 07:34:34
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.data;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.data.DataComminicationStatusDao;
import com.clou.esp.hes.app.web.model.data.DataComminicationStatus;
import com.clou.esp.hes.app.web.service.data.DataComminicationStatusService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dataComminicationStatusService")
public class DataComminicationStatusServiceImpl  extends CommonServiceImpl<DataComminicationStatus>  implements DataComminicationStatusService {

	@Resource
	private DataComminicationStatusDao dataComminicationStatusDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dataComminicationStatusDao);
    }
	@SuppressWarnings("rawtypes")
	public DataComminicationStatusServiceImpl() {}
	
	
	@Override
	public List<DataComminicationStatus> getCommStatus(List<String> sns) {
		return this.dataComminicationStatusDao.getCommStatus(sns);
	}
	@Override
	public List<DataComminicationStatus> getListData(Map<String, Object> map) {
		return this.dataComminicationStatusDao.getListData(map);
	}
	
	
}