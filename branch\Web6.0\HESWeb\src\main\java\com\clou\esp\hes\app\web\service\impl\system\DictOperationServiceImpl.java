/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictOperation{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-01 09:42:07
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.system;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.system.DictOperationDao;
import com.clou.esp.hes.app.web.model.system.DictOperation;
import com.clou.esp.hes.app.web.service.system.DictOperationService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dictOperationService")
public class DictOperationServiceImpl  extends CommonServiceImpl<DictOperation>  implements DictOperationService {

	@Resource
	private DictOperationDao dictOperationDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dictOperationDao);
    }
	@SuppressWarnings("rawtypes")
	public DictOperationServiceImpl() {}
	
	
}