package clouesp.hes.core.uci.soap.custom.webservice.common;


/**
 * @ClassName: BaudType
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年8月7日 上午10:39:41
 *
 */
public enum BaudType {
	
	Baud_0(0,""),
	Ba<PERSON>_600(1,"600"),
	<PERSON><PERSON>_1200(2,"1200"),
	Baud_2400(3,"2400"),
	<PERSON><PERSON>_4800(4,"4800"),
	Baud_7200(5,"7200"),
	Baud_9600(6,"9600"),
	Baud_19200(7,"19200");
	
	private BaudType(int index,String baud) {
		this.index = index;
		this.baud = baud;
	}
	
	public static BaudType parseBaud(String baud) {
		for (BaudType type : values()) {
			if(type.baud.equals(baud))
				return type;
		}
		return null;
	}
	
	private int index;
	private String baud;
	
	public int getIndex() {
		return index;
	}
	public String getBaud() {
		return baud;
	}
	
	
}
