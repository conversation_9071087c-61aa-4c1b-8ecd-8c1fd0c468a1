package com.clou.esp.hes.app.web.model.asset;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.excel.Excel;

/**
 * 拉合闸 列表打印 model
 * <AUTHOR>
 * @date 2018/05/11
 */
public class ConnOrDisconn extends BaseEntity{
	
	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public ConnOrDisconn() {
	}
	
	@Excel(name = "Meter SN", width = 30, groups = ValidGroup1.class)
	private String sn;
	
	@Excel(name = "Communicator SN", width = 30, groups = ValidGroup1.class)
	private String commSN;
	
	@Excel(name = "Communication", width = 30, groups = ValidGroup1.class)
	private String communication;
	
	@Excel(name = "Command", width = 30, groups = ValidGroup1.class)
	private String command;
	
	@Excel(name = "Status", width = 30, groups = ValidGroup1.class)
	private String status;
	
	@Excel(name = "Request Time", width = 30, groups = ValidGroup1.class)
	private String requestTime;
	
	@Excel(name = "Response Time", width = 30, groups = ValidGroup1.class)
	private String responseTime;
	
	@Excel(name = "Reason", width = 30, groups = ValidGroup1.class)
	private String reason;

	public String getSn() {
		return sn;
	}
	public void setSn(String sn) {
		this.sn = sn;
	}
	public String getCommSN() {
		return commSN;
	}
	public void setCommSN(String commSN) {
		this.commSN = commSN;
	}
	public String getCommunication() {
		return communication;
	}
	public void setCommunication(String communication) {
		this.communication = communication;
	}
	public String getCommand() {
		return command;
	}
	public void setCommand(String command) {
		this.command = command;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getRequestTime() {
		return requestTime;
	}
	public void setRequestTime(String requestTime) {
		this.requestTime = requestTime;
	}
	public String getResponseTime() {
		return responseTime;
	}
	public void setResponseTime(String responseTime) {
		this.responseTime = responseTime;
	}
	public String getReason() {
		return reason;
	}
	public void setReason(String reason) {
		this.reason = reason;
	}

}
