<?xml version="1.0" encoding="UTF-8"?><ureport><cell expand="None" name="A1" row="1" col="1" col-span="8"><cell-style font-size="10" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Energy Data Daily Report]]></simple-value></cell><cell expand="None" name="A2" row="2" col="1" col-span="6"><cell-style font-size="10" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="G2" row="2" col="7"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Time:]]></simple-value></cell><cell expand="None" name="H2" row="2" col="8"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><expression-value><![CDATA[param("reportTime")]]></expression-value></cell><cell expand="None" name="A3" row="3" col="1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Index]]></simple-value></cell><cell expand="None" name="B3" row="3" col="2"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Meter SN]]></simple-value></cell><cell expand="None" name="C3" row="3" col="3"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[PT]]></simple-value></cell><cell expand="None" name="D3" row="3" col="4"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[CT]]></simple-value></cell><cell expand="None" name="E3" row="3" col="5"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Data Item]]></simple-value></cell><cell expand="None" name="F3" row="3" col="6"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Start Code]]></simple-value></cell><cell expand="None" name="G3" row="3" col="7"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[End Code]]></simple-value></cell><cell expand="None" name="H3" row="3" col="8"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Electric Quantity(Unit:kWh)]]></simple-value></cell><cell expand="Down" name="A4" row="4" col="1"><cell-style font-size="10" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="M1" aggregate="group" property="index" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="B4" row="4" col="2"><cell-style font-size="10" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="M1" aggregate="group" property="meterSn" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="C4" row="4" col="3"><cell-style font-size="10" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="M1" aggregate="group" property="pt" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="D4" row="4" col="4"><cell-style font-size="10" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="M1" aggregate="group" property="ct" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="E4" row="4" col="5"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="M1" aggregate="group" property="dataName" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="F4" row="4" col="6"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="M1" aggregate="group" property="startCode" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="G4" row="4" col="7"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="M1" aggregate="group" property="endCode" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="H4" row="4" col="8"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="M1" aggregate="group" property="electricQuantity" order="none" mapping-type="simple"></dataset-value></cell><row row-number="1" height="18"/><row row-number="2" height="18"/><row row-number="3" height="19"/><row row-number="4" height="18"/><column col-number="1" width="80"/><column col-number="2" width="80"/><column col-number="3" width="80"/><column col-number="4" width="80"/><column col-number="5" width="74"/><column col-number="6" width="74"/><column col-number="7" width="74"/><column col-number="8" width="74"/><datasource name="test" type="spring" bean="eneryDataDailyReportBean"><dataset name="M1" type="bean" method="loadReportData" clazz=""><field name="index"/><field name="meterSn"/><field name="pt"/><field name="ct"/><field name="dataName"/><field name="startCode"/><field name="endCode"/><field name="electricQuantity"/></dataset></datasource><paper type="A4" left-margin="90" right-margin="90"
    top-margin="72" bottom-margin="72" paging-mode="fitpage" fixrows="0"
    width="595" height="842" orientation="portrait" html-report-align="left" bg-image="" html-interval-refresh-value="0" column-enabled="false"></paper></ureport>