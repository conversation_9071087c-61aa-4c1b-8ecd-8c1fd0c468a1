
			/*多选函数*/
			$(function() {
				$('#ms').change(function() {
					console.log($(this).val());
				}).multipleSelect({
					width: '100%'
				});
				$('#userStatus').change(function() {
					console.log($(this).val());
				}).multipleSelect({
					width: '100%'
				});$('#Manufacturer').change(function() {
					console.log($(this).val());
				}).multipleSelect({
					width: '100%'
				});
				$('#Model').change(function() {
					console.log($(this).val());
				}).multipleSelect({
					width: '100%'
				});
				$('#Old_Version').change(function() {
					console.log($(this).val());
				}).multipleSelect({
					width: '100%'
				});
			});