
package ch.iec.tc57._2011.meterreadschedule_;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the ch.iec.tc57._2011.meterreadschedule_ package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _MeterReadSchedule_QNAME = new QName("http://iec.ch/TC57/2011/MeterReadSchedule#", "MeterReadSchedule");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: ch.iec.tc57._2011.meterreadschedule_
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link UsagePointGroup }
     * 
     */
    public UsagePointGroup createUsagePointGroup() {
        return new UsagePointGroup();
    }

    /**
     * Create an instance of {@link UsagePointGroup.Names }
     * 
     */
    public UsagePointGroup.Names createUsagePointGroupNames() {
        return new UsagePointGroup.Names();
    }

    /**
     * Create an instance of {@link UsagePointGroup.Names.NameType }
     * 
     */
    public UsagePointGroup.Names.NameType createUsagePointGroupNamesNameType() {
        return new UsagePointGroup.Names.NameType();
    }

    /**
     * Create an instance of {@link UsagePoint }
     * 
     */
    public UsagePoint createUsagePoint() {
        return new UsagePoint();
    }

    /**
     * Create an instance of {@link UsagePoint.Names }
     * 
     */
    public UsagePoint.Names createUsagePointNames() {
        return new UsagePoint.Names();
    }

    /**
     * Create an instance of {@link UsagePoint.Names.NameType }
     * 
     */
    public UsagePoint.Names.NameType createUsagePointNamesNameType() {
        return new UsagePoint.Names.NameType();
    }

    /**
     * Create an instance of {@link TimeSchedule }
     * 
     */
    public TimeSchedule createTimeSchedule() {
        return new TimeSchedule();
    }

    /**
     * Create an instance of {@link TimeSchedule.TimePoints }
     * 
     */
    public TimeSchedule.TimePoints createTimeScheduleTimePoints() {
        return new TimeSchedule.TimePoints();
    }

    /**
     * Create an instance of {@link ReadingType }
     * 
     */
    public ReadingType createReadingType() {
        return new ReadingType();
    }

    /**
     * Create an instance of {@link ReadingType.Names }
     * 
     */
    public ReadingType.Names createReadingTypeNames() {
        return new ReadingType.Names();
    }

    /**
     * Create an instance of {@link ReadingType.Names.NameType }
     * 
     */
    public ReadingType.Names.NameType createReadingTypeNamesNameType() {
        return new ReadingType.Names.NameType();
    }

    /**
     * Create an instance of {@link EndDeviceGroup }
     * 
     */
    public EndDeviceGroup createEndDeviceGroup() {
        return new EndDeviceGroup();
    }

    /**
     * Create an instance of {@link EndDeviceGroup.Names }
     * 
     */
    public EndDeviceGroup.Names createEndDeviceGroupNames() {
        return new EndDeviceGroup.Names();
    }

    /**
     * Create an instance of {@link EndDeviceGroup.Names.NameType }
     * 
     */
    public EndDeviceGroup.Names.NameType createEndDeviceGroupNamesNameType() {
        return new EndDeviceGroup.Names.NameType();
    }

    /**
     * Create an instance of {@link EndDevice }
     * 
     */
    public EndDevice createEndDevice() {
        return new EndDevice();
    }

    /**
     * Create an instance of {@link EndDevice.Names }
     * 
     */
    public EndDevice.Names createEndDeviceNames() {
        return new EndDevice.Names();
    }

    /**
     * Create an instance of {@link EndDevice.Names.NameType }
     * 
     */
    public EndDevice.Names.NameType createEndDeviceNamesNameType() {
        return new EndDevice.Names.NameType();
    }

    /**
     * Create an instance of {@link CustomerAgreement }
     * 
     */
    public CustomerAgreement createCustomerAgreement() {
        return new CustomerAgreement();
    }

    /**
     * Create an instance of {@link CustomerAgreement.Names }
     * 
     */
    public CustomerAgreement.Names createCustomerAgreementNames() {
        return new CustomerAgreement.Names();
    }

    /**
     * Create an instance of {@link CustomerAgreement.Names.NameType }
     * 
     */
    public CustomerAgreement.Names.NameType createCustomerAgreementNamesNameType() {
        return new CustomerAgreement.Names.NameType();
    }

    /**
     * Create an instance of {@link CustomerAccount }
     * 
     */
    public CustomerAccount createCustomerAccount() {
        return new CustomerAccount();
    }

    /**
     * Create an instance of {@link CustomerAccount.Names }
     * 
     */
    public CustomerAccount.Names createCustomerAccountNames() {
        return new CustomerAccount.Names();
    }

    /**
     * Create an instance of {@link CustomerAccount.Names.NameType }
     * 
     */
    public CustomerAccount.Names.NameType createCustomerAccountNamesNameType() {
        return new CustomerAccount.Names.NameType();
    }

    /**
     * Create an instance of {@link MeterReadSchedule }
     * 
     */
    public MeterReadSchedule createMeterReadSchedule() {
        return new MeterReadSchedule();
    }

    /**
     * Create an instance of {@link UsagePointGroup.Names.NameType.NameTypeAuthority }
     * 
     */
    public UsagePointGroup.Names.NameType.NameTypeAuthority createUsagePointGroupNamesNameTypeNameTypeAuthority() {
        return new UsagePointGroup.Names.NameType.NameTypeAuthority();
    }

    /**
     * Create an instance of {@link UsagePoint.Names.NameType.NameTypeAuthority }
     * 
     */
    public UsagePoint.Names.NameType.NameTypeAuthority createUsagePointNamesNameTypeNameTypeAuthority() {
        return new UsagePoint.Names.NameType.NameTypeAuthority();
    }

    /**
     * Create an instance of {@link TimeSchedule.ScheduleInterval }
     * 
     */
    public TimeSchedule.ScheduleInterval createTimeScheduleScheduleInterval() {
        return new TimeSchedule.ScheduleInterval();
    }

    /**
     * Create an instance of {@link TimeSchedule.TimePoints.Window }
     * 
     */
    public TimeSchedule.TimePoints.Window createTimeScheduleTimePointsWindow() {
        return new TimeSchedule.TimePoints.Window();
    }

    /**
     * Create an instance of {@link ReadingType.Names.NameType.NameTypeAuthority }
     * 
     */
    public ReadingType.Names.NameType.NameTypeAuthority createReadingTypeNamesNameTypeNameTypeAuthority() {
        return new ReadingType.Names.NameType.NameTypeAuthority();
    }

    /**
     * Create an instance of {@link EndDeviceGroup.Names.NameType.NameTypeAuthority }
     * 
     */
    public EndDeviceGroup.Names.NameType.NameTypeAuthority createEndDeviceGroupNamesNameTypeNameTypeAuthority() {
        return new EndDeviceGroup.Names.NameType.NameTypeAuthority();
    }

    /**
     * Create an instance of {@link EndDevice.Names.NameType.NameTypeAuthority }
     * 
     */
    public EndDevice.Names.NameType.NameTypeAuthority createEndDeviceNamesNameTypeNameTypeAuthority() {
        return new EndDevice.Names.NameType.NameTypeAuthority();
    }

    /**
     * Create an instance of {@link CustomerAgreement.Names.NameType.NameTypeAuthority }
     * 
     */
    public CustomerAgreement.Names.NameType.NameTypeAuthority createCustomerAgreementNamesNameTypeNameTypeAuthority() {
        return new CustomerAgreement.Names.NameType.NameTypeAuthority();
    }

    /**
     * Create an instance of {@link CustomerAccount.Names.NameType.NameTypeAuthority }
     * 
     */
    public CustomerAccount.Names.NameType.NameTypeAuthority createCustomerAccountNamesNameTypeNameTypeAuthority() {
        return new CustomerAccount.Names.NameType.NameTypeAuthority();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MeterReadSchedule }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://iec.ch/TC57/2011/MeterReadSchedule#", name = "MeterReadSchedule")
    public JAXBElement<MeterReadSchedule> createMeterReadSchedule(MeterReadSchedule value) {
        return new JAXBElement<MeterReadSchedule>(_MeterReadSchedule_QNAME, MeterReadSchedule.class, null, value);
    }

}
