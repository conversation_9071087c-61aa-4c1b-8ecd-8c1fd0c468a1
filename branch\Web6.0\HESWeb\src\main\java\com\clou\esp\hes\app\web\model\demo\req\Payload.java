package com.clou.esp.hes.app.web.model.demo.req;

import java.io.Serializable;

import javax.jws.soap.SOAPBinding;
import javax.jws.soap.SOAPBinding.Style;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * 孟加拉接口项目请求数据头数据对象
 * 
 * <AUTHOR>
 * 
 */
@SOAPBinding(style = Style.RPC)
@XmlRootElement(name = "Payload")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = { "meterConfig" })
public class Payload implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public Payload() {
		super();
	}

	public Payload(MeterConfig meterConfig) {
		super();
		this.meterConfig = meterConfig;
	}

	public MeterConfig meterConfig;

	public MeterConfig getMeterConfig() {
		return meterConfig;
	}

	public void setMeterConfig(MeterConfig meterConfig) {
		this.meterConfig = meterConfig;
	}

	@Override
	public String toString() {
		return "Payload [meterConfig=" + meterConfig + "]";
	}

}
