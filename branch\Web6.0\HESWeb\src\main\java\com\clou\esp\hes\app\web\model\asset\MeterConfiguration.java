/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class onDemandRead{ } 
 * 
 * 摘    要： onDemandRead
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-1-2 09:41:41
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.asset;

import java.util.Date;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.excel.Excel;
/**
 * Meter Configuration实体
 * <AUTHOR>
 * @date 2018/01/21
 */
public class MeterConfiguration extends  BaseEntity implements Comparable<MeterConfiguration> {

	private static final long serialVersionUID = 1L;
	
	/** sn */
	@Excel(name = "Meter Serial Number", width = 30, groups = ValidGroup1.class)
	private java.lang.String sn;
	/** Parameter Type */
	@Excel(name = "Parameter Type", width = 30, groups = ValidGroup1.class)
	private java.lang.String parameterType;
	/**Parameter Item**/
	@Excel(name = "Parameter Item", width = 30, groups = ValidGroup1.class)
	private java.lang.String parameterItem;
	/** Meter Group */
	@Excel(name = "Meter Group", width = 30, groups = ValidGroup1.class)
	private java.lang.String meterGroup;
	/** requestTime */
	@Excel(name = "Request Time", width = 30, groups = ValidGroup1.class)
	private java.util.Date requestTime;
	/** status */
	@Excel(name = "Status", width = 30, groups = ValidGroup1.class)
	private java.lang.String status;
	/** reponseTime */
	@Excel(name = "Reponse Time", width = 30, groups = ValidGroup1.class)
	private java.util.Date reponseTime;
	/** Reason */
	@Excel(name = "Reason", width = 30, groups = ValidGroup1.class)
	private java.lang.String reason;
	/** model */
	private java.lang.String model;
	/** profile */
	private java.lang.String profile;
	/** Parameter Item Id */
	private java.lang.String parameterItemId;
	/** Parameter Item Parent Id */
	private java.lang.String parameterItemParentId;
	
	/** Meter Group Id*/
	private java.lang.String meterGroupId;
	
	public MeterConfiguration() {
		super();
	}
	
	public MeterConfiguration(String sn, String parameterType, String parameterItem,
			String meterGroup, Date requestTime, String status, Date reponseTime, 
			String reason, String model, String profile, String parameterItemId) {
		super();
		this.sn = sn;
		this.parameterType = parameterType;
		this.parameterItem = parameterItem;
		this.meterGroup = meterGroup;
		this.requestTime = requestTime;
		this.status = status;
		this.reponseTime = reponseTime;
		this.reason = reason;
		this.model = model;
		this.profile = profile;
		this.parameterItemId = parameterItemId;
	}

	public java.lang.String getSn() {
		return sn;
	}

	public void setSn(java.lang.String sn) {
		this.sn = sn;
	}

	public java.lang.String getParameterType() {
		return parameterType;
	}

	public void setParameterType(java.lang.String parameterType) {
		this.parameterType = parameterType;
	}

	public java.lang.String getParameterItem() {
		return parameterItem;
	}

	public void setParameterItem(java.lang.String parameterItem) {
		this.parameterItem = parameterItem;
	}

	public java.lang.String getMeterGroup() {
		return meterGroup;
	}

	public void setMeterGroup(java.lang.String meterGroup) {
		this.meterGroup = meterGroup;
	}

	public java.util.Date getRequestTime() {
		return requestTime;
	}

	public void setRequestTime(java.util.Date requestTime) {
		this.requestTime = requestTime;
	}

	public java.lang.String getStatus() {
		return status;
	}

	public void setStatus(java.lang.String status) {
		this.status = status;
	}

	public java.util.Date getReponseTime() {
		return reponseTime;
	}

	public void setReponseTime(java.util.Date reponseTime) {
		this.reponseTime = reponseTime;
	}

	public java.lang.String getReason() {
		return reason;
	}

	public void setReason(java.lang.String reason) {
		this.reason = reason;
	}

	public java.lang.String getModel() {
		return model;
	}

	public void setModel(java.lang.String model) {
		this.model = model;
	}

	public java.lang.String getProfile() {
		return profile;
	}

	public void setProfile(java.lang.String profile) {
		this.profile = profile;
	}

	@Override
	public int compareTo(MeterConfiguration o) {
		return this.getSn().compareTo(o.getSn());
	}

	public java.lang.String getParameterItemId() {
		return parameterItemId;
	}

	public void setParameterItemId(java.lang.String parameterItemId) {
		this.parameterItemId = parameterItemId;
	}

	public java.lang.String getParameterItemParentId() {
		return parameterItemParentId;
	}

	public void setParameterItemParentId(java.lang.String parameterItemParentId) {
		this.parameterItemParentId = parameterItemParentId;
	}

	public java.lang.String getMeterGroupId() {
		return meterGroupId;
	}

	public void setMeterGroupId(java.lang.String meterGroupId) {
		this.meterGroupId = meterGroupId;
	}
	
}
