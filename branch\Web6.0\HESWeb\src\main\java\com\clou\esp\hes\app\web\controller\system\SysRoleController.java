/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysRole{ } 
 * 
 * 摘    要： 角色表
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-10-27 08:42:10
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.system;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.json.ValidForm;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.tag.vo.ZtreeUtil;
import com.clou.esp.hes.app.web.core.util.Globals;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.dict.DictMenu;
import com.clou.esp.hes.app.web.model.system.DictOperation;
import com.clou.esp.hes.app.web.model.system.SysRole;
import com.clou.esp.hes.app.web.model.system.SysRoleMenu;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.system.DictOperationService;
import com.clou.esp.hes.app.web.service.system.SysMenuService;
import com.clou.esp.hes.app.web.service.system.SysOrgService;
import com.clou.esp.hes.app.web.service.system.SysRoleMenuService;
import com.clou.esp.hes.app.web.service.system.SysRoleService;
import com.clou.esp.hes.app.web.service.system.SysUserService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.google.common.collect.Lists;
import com.power7000g.core.util.oConvertUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.redis.JedisUtils;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * <AUTHOR>
 * @时间：2017-10-27 08:42:10
 * @描述：角色表类
 */
@Controller
@RequestMapping("/sysRoleController")
public class SysRoleController extends BaseController{

 	@Resource
    private SysRoleService sysRoleService;
 	@Resource
    private SysUserService sysUserService;
 	@Resource
    private SysOrgService sysOrgService;
 	@Resource
    private SysMenuService sysMenuService;
 	@Resource
 	private SysRoleMenuService sysRoleMenuService;
 	@Resource
 	private DictOperationService dictOperationService;
 	
 	@Resource
    private DataUserLogService dataUserLogService;

	/**
	 * 跳转到角色表列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/system/sysUserAndRoleList");
    }

	/**
	 * 跳转到角色表新增界面
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "toAddSysRole")
	public ModelAndView sysRole(SysRole sysRole,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(sysRole.getId())){
			try {
                sysRole=sysRoleService.getEntity(sysRole.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("sysRole", sysRole);
		}
		return new ModelAndView("/system/sysRole");
	}


	/**
	 * 角色表查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
            j=sysRoleService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除角色表信息, 以及SysRoleMenu表中的数据
     * @param id
     * @return
     */
	@Transactional
    @RequestMapping(value = "deleteRole")
    @ResponseBody
    public AjaxJson deleteRole(SysRole sysRole,HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        try {
            SysUser su = TokenManager.getToken();
        	//查询用户是否挂靠组织机构子集
            SysUser delUser = new SysUser();
            delUser.setRoleId(sysRole.getId());
            List<SysUser> userList = sysUserService.getList(delUser);
            if(userList.size() > 0){
            	j.setSuccess(false);
                j.setMsg(MutiLangUtil.doMutiLang("sysRoleList.unableDel_user"));
                return j;
            }
            SysRole entity = sysRoleService.getEntity(sysRole.getId());
            if(sysRoleService.deleteById(sysRole.getId())>0){
            	//删除Role在SysRoleMenu表中的原有数据
	        	int deleteNum = sysRoleMenuService.deleteById(sysRole.getId());
	        	//添加操作日志
                dataUserLogService.insertDataUserLog(su.getId(), 
                		"Permission", "Delete Role", "Delete Role (Name="+ entity.getName() +")");
	        	System.out.println("Role删除的数据量----> " + deleteNum);
                j.setMsg(MutiLangUtil.doMutiLang("system.delSuccess"));
            }else{
                j.setSuccess(false);
                j.setMsg(MutiLangUtil.doMutiLang("system.delFail"));
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
    
    /**
     * 保存角色表信息
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })SysRole sysRole,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        SysRole t=new  SysRole();
        try {
        	SysUser su = TokenManager.getToken();
        	//如果存在id，则是修改数据
	        if(StringUtil.isNotEmpty(sysRole.getId())){
	        	t=sysRoleService.getEntity(sysRole.getId());
				MyBeanUtils.copyBeanNotNull2Bean(sysRole, t);
				sysRoleService.update(t);
				//添加操作日志
                dataUserLogService.insertDataUserLog(su.getId(), 
                		"Permission", "Edit Role", "Edit Role (Name="+ t.getName() +")");
				j.setMsg(MutiLangUtil.doMutiLang("system.updateSucc"));
			}else{
	            sysRoleService.save(sysRole);
	            //添加操作日志
                dataUserLogService.insertDataUserLog(su.getId(), 
                		"Permission", "Add Role", "Add Role (Name="+ sysRole.getName() +")");
	            j.setMsg(MutiLangUtil.doMutiLang("system.addSucc"));
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
    
    /**
	 * 获取角色下属menu 信息
	 * @param searchName
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getRoleMenuZtree")
	@ResponseBody
	public AjaxJson getRoleMenuZtree(SysRole sysRole, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		List<Map<String, Object>> list = new ArrayList<>();
		DictMenu sysMenu = new DictMenu();
		sysMenu.setMenulevel(1);
		List<DictMenu> parentMenuList = sysMenuService.getList(sysMenu);			//所有的menu
		sysRole.setMenuLevel("1");
    	List<DictMenu> roleMenuList = sysMenuService.getMenuListByRole(sysRole);	//角色所属的menu
		for(DictMenu sm : parentMenuList){
			Map<String, Object> parentMap = new HashMap<String, Object>();
			parentMap.put("id", sm.getId());
			parentMap.put("name", sm.getMenuname());
			parentMap.put("parent", "");
			parentMap.put("expanded", true);
			parentMap.put("checked", false);	//判断是否属于角色
			for (int i = 0; i < roleMenuList.size(); i++) {
				if(roleMenuList.get(i).getId().equals(sm.getId())){
					parentMap.put("checked", true);	//判断是否属于角色
				}
			}
			sysMenu.setMenulevel(2);
			sysMenu.setParentmenuid(sm.getId());	//设置父ID
			List<DictMenu> sonMenuList = sysMenuService.getList(sysMenu);			//查询父级menu的子集
			if (sonMenuList.size() == 0) {
				SysRoleMenu srmTemp = new SysRoleMenu();
				srmTemp.setRoleId(sysRole.getId());
				srmTemp.setMenuId(sm.getId());
				SysRoleMenu srm = null;
				srm = sysRoleMenuService.getEntityByRole(srmTemp);

				if (srm != null) {
					String opeartionStr = srm.getOperation();
					JedisUtils.set(srmTemp.getRoleId() + srmTemp.getMenuId(), opeartionStr, 24 * 60 * 60);
				}
			} else 
			{
				sysRole.setMenuLevel("2");
		    	List<DictMenu> sonRoleMenuList = sysMenuService.getMenuListByRole(sysRole);	//角色所属的子集menu
				List<Map<String, Object>> sonMenuMapList = new ArrayList<>();
				for(DictMenu ssm : sonMenuList){
						Map<String, Object> sonMap = new HashMap<String, Object>();
						sonMap.put("id", ssm.getId());
						sonMap.put("name", ssm.getMenuname());
						sonMap.put("parent", sm.getId());	//父级ID
						if(StringUtils.isNotBlank(ssm.getFunctionurl())){
	
							sonMap.put("expanded", false);
						}else{
							sonMap.put("expanded", true);
						}
						sonMap.put("checked", false);		//判断是否属于角色
						for (int i = 0; i < sonRoleMenuList.size(); i++) {
							//判断是否包含角色的子集menu，包含则判断选中
							if(sonRoleMenuList.get(i).getId().equals(ssm.getId())){
								sonMap.put("checked", true);	//判断是否属于角色
								
								/*
								 * 角色所有子集menu的operation在点击加载的时候全部缓存入redis中
								 * 为了在保存操作的时候可以统一从redis中获取存入数据库
								 */
								SysRoleMenu redisTemp = new SysRoleMenu();
								redisTemp.setRoleId(sysRole.getId());
								redisTemp.setMenuId(ssm.getId());
								SysRoleMenu srm = sysRoleMenuService.getEntityByRole(redisTemp);
								if (srm != null) {
									if (StringUtil.isNotEmpty(srm.getOperation())) {
										JedisUtils.set(sysRole.getId() + ssm.getId(),
												srm.getOperation(),
												24 * 60 * 60);
									}
								}
							}
						}
						sonMenuMapList.add(sonMap);
						
						//第三级菜单begin
						sysMenu.setMenulevel(3);
						sysMenu.setParentmenuid(ssm.getId());	//设置父ID
						List<DictMenu> grandSonMenuList = sysMenuService.getList(sysMenu);			//查询父级menu的子集
						sysRole.setMenuLevel("3");
				    	List<DictMenu> grandSonRoleMenuList = sysMenuService.getMenuListByRole(sysRole);	//角色所属的子集menu
						List<Map<String, Object>> grandSonMenuMapList = new ArrayList<>();
						for(DictMenu gssm : grandSonMenuList){
							Map<String, Object> grandSonMap = new HashMap<String, Object>();
							grandSonMap.put("id", gssm.getId());
							grandSonMap.put("name", gssm.getMenuname());
							grandSonMap.put("parent", ssm.getId());	//父级ID
							grandSonMap.put("expanded", false);
							grandSonMap.put("checked", false);		//判断是否属于角色
							for (int i = 0; i < grandSonRoleMenuList.size(); i++) {
								//判断是否包含角色的子集menu，包含则判断选中
								if(grandSonRoleMenuList.get(i).getId().equals(gssm.getId())){
									grandSonMap.put("checked", true);	//判断是否属于角色
									
									/*
									 * 角色所有子集menu的operation在点击加载的时候全部缓存入redis中
									 * 为了在保存操作的时候可以统一从redis中获取存入数据库
									 */
									SysRoleMenu redisTemp = new SysRoleMenu();
									redisTemp.setRoleId(sysRole.getId());
									redisTemp.setMenuId(gssm.getId());
									if(StringUtil.isNotEmpty(sysRoleMenuService.getEntityByRole(redisTemp).getOperation())){
										JedisUtils.set(sysRole.getId()+gssm.getId(), sysRoleMenuService.getEntityByRole(redisTemp).getOperation(), 24*60*60);
									}
								}
							}
							grandSonMenuMapList.add(grandSonMap);
						}
						
	
						if(grandSonMenuMapList.size() > 0){
							sonMenuMapList.addAll(grandSonMenuMapList);
						}
				}
				if(sonMenuMapList.size() > 0){
					parentMap.put("list", sonMenuMapList);
				}
			}
			
			list.add(parentMap);
		}
		ZtreeUtil jtu = new ZtreeUtil();
		jtu.setIdFname("id");
		jtu.setNameFname("name");
		jtu.setpIdFname("parent");
		jtu.setSubsetFieldName("list");
		jtu.setOpenFname("expanded");
		jtu.setCheckedFname("checked");
		j.setObj(jtu.getZtreeNodeData(list));
		return j;
	}
	
	/**
     * 获取role menu的Meter Configuration数据
     * @param id
     * @return
     */
    @RequestMapping(value = "getRoleMenuConfiguration")
    @ResponseBody
    public AjaxJson getRoleMenuConfiguration(SysRoleMenu sysRoleMenu, HttpServletRequest request) {
    	  AjaxJson j = new AjaxJson();
          List<DictOperation> doList = new ArrayList<>();
          try {
              SysRoleMenu srm = sysRoleMenuService.getEntityByRole(sysRoleMenu);
              //获取单个功能项的所有operation
              DictOperation dictOperation = new DictOperation();
              DictMenu smDoListTemp = sysMenuService.getEntity(sysRoleMenu.getMenuId());
              if(StringUtil.isNotEmpty(smDoListTemp.getFunctionId())){
              	dictOperation.setFunctionId(smDoListTemp.getFunctionId());
                  doList = dictOperationService.getList(dictOperation);
              }
              
              //从缓存里面取数据
            	String opeartionStr = JedisUtils.get(sysRoleMenu.getRoleId() + sysRoleMenu.getMenuId());
    			List<String> operList = Lists.newArrayList();
    			if(opeartionStr!=null&&!"".equals(opeartionStr)) {
    				operList=Arrays.asList(opeartionStr.split(","));
    			}
    			
                if(srm != null){
    	            if(StringUtil.isNotEmpty(srm.getOperation())){
//    	            	if(srm.getOperation().indexOf(",") > 0){
    	            		String[] strArr = srm.getOperation().split(",");
    	            		for (int i = 0; i < strArr.length; i++) {
    	            			//从缓存里面取数据
    	            			for (int k = 0; k < doList.size(); k++) {
    	        					//判断是否包含角色的子集menu，包含则判断选中
    	            				if(opeartionStr!=null&&!"".equals(opeartionStr)) {
    	            					if(operList.contains(doList.get(k).getId())) {
    	            						doList.get(k).setIsSelect(1);
    	            					}
    	            				}
    	        				}
    						}
//    	            	}
//    	            	else{
//    	            		DictOperation entity = dictOperationService.getEntity(srm.getOperation());
//    	            		for (int k = 0; k < doList.size(); k++) {
//            					//判断是否包含角色的子集menu，包含则判断选中
//            					if(doList.get(k).getId().equals(entity.getId())){
//            						doList.get(k).setIsSelect(1);	//判断是否属于角色
//            					}
//            				}
//    	            	}
    	            }
                }else {
                	
        			for (int k = 0; k < doList.size(); k++) {
    					//判断是否包含角色的子集menu，包含则判断选中
        				if(opeartionStr!=null&&!"".equals(opeartionStr)) {
        					if(operList.contains(doList.get(k).getId())) {
        						doList.get(k).setIsSelect(1);
        					}
        				}
    				}
                }
                j.setObj(doList); 
            }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
    
    /**
     * 选中或者取消Meter Configuration表中数据，缓存入redis，方便统一保存
     * @param id
     * @return
     */
    @RequestMapping(value = "checkMeterConfiguration")
    @ResponseBody
    public AjaxJson checkMeterConfiguration(SysRoleMenu sysRoleMenu, HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        try {
            if(StringUtil.isNotEmpty(sysRoleMenu.getOperation())){
            	//把修改后的operation缓存入redis中，主键为roleId+menuId
            	JedisUtils.set(sysRoleMenu.getRoleId()+sysRoleMenu.getMenuId(), sysRoleMenu.getOperation(), 24*60*60);
            }else{
            	JedisUtils.set(sysRoleMenu.getRoleId()+sysRoleMenu.getMenuId(), "", 24*60*60);
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
    
    /**
     * 保存role的所有menu和operation的数据
     * menu的数据从界面zTree中获取，operation的数据从缓存好的redis中获取
     * @param id
     * @return
     */
    @Transactional
    @RequestMapping(value = "saveRoleMenuAndOperationData")
    @ResponseBody
    public AjaxJson saveRoleMenuAndOperationData(String roleId, String menuList, HttpServletRequest request) {
        AjaxJson json = new AjaxJson();
        try { 
        	//获取界面的数据
	        if(StringUtil.isNotEmpty(menuList)){
	        	JSONArray menuListJsons = JSONArray.fromObject(menuList);
	        	if(!(menuListJsons.size() > 0)){
	        		json.setSuccess(false);
		    		json.setMsg(MutiLangUtil.doMutiLang("sysRoleList.selectRole"));
		    		return json;
	        	}
	        	//删除Role在SysRoleMenu表中的原有数据
	        	int deleteNum = sysRoleMenuService.deleteById(roleId);
	        	System.out.println("Role删除的数据量----> " + deleteNum);
	        	for (int i = 0; i < menuListJsons.size(); i++) {
	        		JSONObject menuObj = menuListJsons.getJSONObject(i);
	        		if("true".equals(menuObj.getString("checked"))){
	        			SysRoleMenu parentMenu = new SysRoleMenu();
	        			parentMenu.setRoleId(roleId);
	        			parentMenu.setMenuId(menuObj.getString("id"));
	        			parentMenu.setOperation("");
	        			sysRoleMenuService.save(parentMenu);	//保存父级menu数据
	        			/*
	        			 * 判断是否存在child字段，存在则获取子集数据，保存到数据库
	        			 */
	        			if(menuObj.has("children")){
	        				JSONArray operationJsons = JSONArray.fromObject(menuObj.getString("children"));
	        				if(operationJsons.size() > 0){
	        					for (int j = 0; j < operationJsons.size(); j++) {
	        						JSONObject operationObj = operationJsons.getJSONObject(j);
	        						if("true".equals(operationObj.getString("checked"))){
	        							SysRoleMenu childrenMenu = new SysRoleMenu();
	        							childrenMenu.setRoleId(roleId);
	        							childrenMenu.setMenuId(operationObj.getString("id"));
	    
	        							//从redis获取缓存的数据信息
	        							String opeartionStr = JedisUtils.get(roleId + operationObj.getString("id"));
	        							childrenMenu.setOperation(opeartionStr);
	        							sysRoleMenuService.save(childrenMenu);	//保存子级menu数据
	        						}
	        					}
	        				}
	        			}else{
	        				if("Home".equalsIgnoreCase(menuObj.getString("name"))){
	        					SysRoleMenu childrenMenu = new SysRoleMenu();
    							childrenMenu.setRoleId(roleId);
    							childrenMenu.setMenuId(menuObj.getString("id"));

    							//从redis获取缓存的数据信息
    							String opeartionStr = JedisUtils.get(roleId + menuObj.getString("id"));
    							childrenMenu.setOperation(opeartionStr);
    							sysRoleMenuService.update(childrenMenu);	//保存子级menu数据
	        				}
	        			}
	        		}
	        	}
	        	json.setMsg(MutiLangUtil.doMutiLang("system.saveSucc"));
	        }else{
	        	json.setSuccess(false);
	    		json.setMsg(MutiLangUtil.doMutiLang("system.saveFail"));
	        }
        }
        catch (Exception e) {
            e.printStackTrace();
            json.setSuccess(false);
            json.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return json;
    }
    /**
     * 校验role name唯一性
     * @param id5
     * @return
     */
    @RequestMapping(value = "checkRoleName")
    @ResponseBody
    public ValidForm checkRoleName(HttpServletRequest request) {
    	ValidForm v = new ValidForm();
		String roleName = oConvertUtils.getString(request.getParameter("param"));
		List<SysRole> list = sysRoleService.vaildRoleName(roleName);
		if (list.size() > 0) {
			v.setInfo(MutiLangUtil.doMutiLang("sysRoleList.roleNameExist"));
			v.setStatus("n");
		}
		return v;
    }
}