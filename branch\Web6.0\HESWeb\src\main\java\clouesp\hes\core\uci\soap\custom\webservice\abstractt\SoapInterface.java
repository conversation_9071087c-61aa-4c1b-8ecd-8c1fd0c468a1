package clouesp.hes.core.uci.soap.custom.webservice.abstractt;

import com.clou.esp.hes.app.web.enums.system.TaskState;
import com.power7000g.core.util.json.AjaxJson;

import ch.iec.tc57._2011.schema.message.HeaderType;


/**
 * @ClassName: SoapInterface
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年7月18日 上午11:32:58
 *
 */
public interface SoapInterface {
	void send();
	HeaderType createHeader();
	Object createPayload();
	
	void pushlet(AjaxJson json);
	
	void recv(Object object);
	void parseHeader();
	void parsePayload();
	void parseReply();
	
	void saveToRedis(String id,String dateItemId,TaskState state,Object value);
}
