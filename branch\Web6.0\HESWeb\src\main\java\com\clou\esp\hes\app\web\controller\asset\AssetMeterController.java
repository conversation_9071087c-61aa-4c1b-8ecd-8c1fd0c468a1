/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeter{ } 
 * 
 * 摘    要： assetMeter
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-06 08:04:55
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.asset;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.Map.Entry;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSON;
import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.controller.system.SysOrgController;
import com.clou.esp.hes.app.web.core.json.ValidForm;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.CreatePdf;
import com.clou.esp.hes.app.web.core.util.ExcelUtils;
import com.clou.esp.hes.app.web.core.util.Globals;
import com.clou.esp.hes.app.web.core.util.IDUtils;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.OrganizationUtils;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.core.util.SpringContextUtil;
import com.clou.esp.hes.app.web.core.util.UciInterfaceUtil;
import com.clou.esp.hes.app.web.model.asset.AssetCommunicator;
import com.clou.esp.hes.app.web.model.asset.AssetCustomer;
import com.clou.esp.hes.app.web.model.asset.AssetLine;
import com.clou.esp.hes.app.web.model.asset.AssetMeasurementProfile;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.asset.AssetMeterDto;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroup;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroupMap;
import com.clou.esp.hes.app.web.model.asset.AssetMeterTemplate;
import com.clou.esp.hes.app.web.model.asset.AssetRouter;
import com.clou.esp.hes.app.web.model.asset.AssetScheduleScheme;
import com.clou.esp.hes.app.web.model.asset.AssetTransformer;
import com.clou.esp.hes.app.web.model.asset.ImportAssetGprsMeter;
import com.clou.esp.hes.app.web.model.asset.OnDemandRead;
import com.clou.esp.hes.app.web.model.asset.OnDemandReadSchedulerJob;
import com.clou.esp.hes.app.web.model.asset.VendMeterInitialCreditAmount;
import com.clou.esp.hes.app.web.model.data.DataIntegrity;
import com.clou.esp.hes.app.web.model.data.DataScheduleMissData;
import com.clou.esp.hes.app.web.model.data.DataScheduleProgress;
import com.clou.esp.hes.app.web.model.data.DataShipmentMeter;
import com.clou.esp.hes.app.web.model.dict.DictCommunicationType;
import com.clou.esp.hes.app.web.model.dict.DictCustomerIndustry;
import com.clou.esp.hes.app.web.model.dict.DictCustomerType;
import com.clou.esp.hes.app.web.model.dict.DictDataitemGroup;
import com.clou.esp.hes.app.web.model.dict.DictDeviceModel;
import com.clou.esp.hes.app.web.model.dict.DictDeviceType;
import com.clou.esp.hes.app.web.model.dict.DictManufacturer;
import com.clou.esp.hes.app.web.model.dict.DictProfile;
import com.clou.esp.hes.app.web.model.system.SysOrg;
import com.clou.esp.hes.app.web.model.system.SysService;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.asset.AssetCommunicatorService;
import com.clou.esp.hes.app.web.service.asset.AssetCustomerService;
import com.clou.esp.hes.app.web.service.asset.AssetLineManagementService;
import com.clou.esp.hes.app.web.service.asset.AssetMeasurementProfileService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterGroupMapService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterGroupService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.asset.AssetRouterService;
import com.clou.esp.hes.app.web.service.asset.AssetScheduleSchemeService;
import com.clou.esp.hes.app.web.service.asset.AssetTransformerService;
import com.clou.esp.hes.app.web.service.data.DataIntegrityService;
import com.clou.esp.hes.app.web.service.data.DataMdEnergyDailyService;
import com.clou.esp.hes.app.web.service.data.DataMdEnergyMinutelyService;
import com.clou.esp.hes.app.web.service.data.DataMeterEventService;
import com.clou.esp.hes.app.web.service.data.DataScheduleMissDataService;
import com.clou.esp.hes.app.web.service.data.DataScheduleProgressService;
import com.clou.esp.hes.app.web.service.data.DataShipmentMeterService;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.dict.DictCommunicationTypeService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemGroupService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemService;
import com.clou.esp.hes.app.web.service.dict.DictDeviceModelService;
import com.clou.esp.hes.app.web.service.dict.DictDeviceTypeService;
import com.clou.esp.hes.app.web.service.dict.DictManufacturerService;
import com.clou.esp.hes.app.web.service.dict.DictProfileService;
import com.clou.esp.hes.app.web.service.system.SysOrgService;
import com.clou.esp.hes.app.web.service.system.SysServiceAttributeService;
import com.clou.esp.hes.app.web.service.system.SysServiceService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.clou.esp.hes.app.web.core.util.XMLUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.power7000g.core.excel.ExcelDataFormatter;
import com.power7000g.core.util.oConvertUtils;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;
import com.power7000g.core.util.redis.JedisUtils;
import com.power7000g.core.util.uuid.UUIDGenerator;

import ch.iec.tc57._2011.enddevicecontrols.FaultMessage;
import ch.iec.tc57._2011.enddevicecontrols.RequestEndDeviceControlsPort;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControls; 
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl.EndDeviceControlType;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl.EndDevices;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl.EndDevices.Names;
import ch.iec.tc57._2011.enddevicecontrolsmessage.EndDeviceControlsPayloadType;
import ch.iec.tc57._2011.enddevicecontrolsmessage.EndDeviceControlsRequestMessageType;
import ch.iec.tc57._2011.enddevicecontrolsmessage.EndDeviceControlsResponseMessageType;
import ch.iec.tc57._2011.getmeterreadings_.EndDevice;
import ch.iec.tc57._2011.getmeterreadings_.GetMeterReadings;
import ch.iec.tc57._2011.getmeterreadings_.Name;
import ch.iec.tc57._2011.getmeterreadings_.NameType;
import ch.iec.tc57._2011.getmeterreadings_.ReadingType;
import ch.iec.tc57._2011.meterreadings.GetMeterReadingsPort;
import ch.iec.tc57._2011.meterreadingsmessage.GetMeterReadingsPayloadType;
import ch.iec.tc57._2011.meterreadingsmessage.MeterReadingsRequestMessageType;
import ch.iec.tc57._2011.meterreadingsmessage.MeterReadingsResponseMessageType;
import ch.iec.tc57._2011.meterreadschedule.MeterReadSchedulePort;
import ch.iec.tc57._2011.meterreadschedule_.MeterReadSchedule;
import ch.iec.tc57._2011.meterreadschedule_.TimeSchedule;
import ch.iec.tc57._2011.meterreadschedulemessage.MeterReadSchedulePayloadType;
import ch.iec.tc57._2011.meterreadschedulemessage.MeterReadScheduleRequestMessageType;
import ch.iec.tc57._2011.meterreadschedulemessage.MeterReadScheduleResponseMessageType;
import ch.iec.tc57._2011.schema.message.ErrorType;
import ch.iec.tc57._2011.schema.message.HeaderType;
import ch.iec.tc57._2011.schema.message.ReplyType;
import ch.iec.tc57._2011.schema.message.RequestType;
import ch.iec.tc57._2011.schema.message.UserType;
import clouesp.hes.core.uci.soap.custom.asset_refresh.AssetRefreshPort;
import clouesp.hes.core.uci.soap.custom.asset_refresh.RefreshMessage;
import clouesp.hes.core.uci.soap.custom.job_cancel.JobCancelPort;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import com.power7000g.core.util.encrypt.PasswordUtil;

/**
 * <AUTHOR>
 * @时间：2017-11-06 08:04:55
 * @描述：assetMeter类
 */
@Controller
@RequestMapping("/assetMeterController")
public class AssetMeterController extends BaseController {

	@Resource
	private AssetMeterService assetMeterService;
	@Resource
	private DictDataitemGroupService dictDataitemGroupService;
	@Resource
	private DictDataitemService dictDataitemService;
	@Resource
	private DictCommunicationTypeService dictCommunicationTypeService;
	@Resource
	private AssetMeasurementProfileService assetMeasurementProfileService;
	@Resource
	private AssetMeterGroupMapService assetMeterGroupMapService;
	@Resource
	private DictDeviceModelService dictDeviceModelService;
	@Resource
	private DictProfileService dictProfileService;
	@Resource
	private AssetCommunicatorService assetCommunicatorService;
	
	@Resource
	private DataIntegrityService dataIntegrityService;
	@Resource
	private DataScheduleMissDataService dataScheduleMissDataService;
	@Resource
	private DataScheduleProgressService dataScheduleProgressService;
	@Resource
    private SysServiceService sysServiceService;
	@Resource
    private AssetRouterService assetRouterService;
	@Resource
	private DataUserLogService dataUserLogService; 
	
	
	@Resource
    private SysServiceAttributeService sysServiceAttributeService;
	@Resource
    private DataMdEnergyDailyService dataMeterDataEnergyDailyService;
	@Resource
    private DataMdEnergyMinutelyService dataMeterDataEnergyMinutelyService;
	@Resource
	private DataMeterEventService dataMeterEventService;

	@Resource
	private AssetMeterGroupService 		assetMeterGroupService;
	@Resource
	private AssetScheduleSchemeService  assetScheduleSchemeService;
	@Resource
	private DictManufacturerService     dictManufacturerService;
	@Resource
	private SysOrgService 				sysOrgService;
	@Resource
	private DictDeviceTypeService		dictDeviceTypeService;
	@Resource
	private AssetCustomerService        assetCustomerService;
	@Resource
	private DataShipmentMeterService    dataShipmentMeterService;
	
	@Resource
	private AssetLineManagementService assetLineManagementService;
	
	@Resource
	AssetTransformerService assetTransformerService;
	
	//手动事务控制
	@Resource(name="transactionManager")
	private DataSourceTransactionManager transactionManager;
	
	/**
	 * Asset Management
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "assetManagementList")
    public ModelAndView assetManagementList(HttpServletRequest request, Model model) {
		model.addAttribute("orgTypeReplace", SysOrgController.getOrgTypeReplace());   
		List<DictCustomerType> dictCustomerTypes=assetCustomerService.getDictCustomerType();
		String customerTypeReplace=RoletoJson.listToReplaceStr(dictCustomerTypes, "id", "name", ",");
		List<DictCustomerIndustry> dictCustomerIndustrys=assetCustomerService.getDictCustomerIndustry();
		String customerIndustryReplace=RoletoJson.listToReplaceStr(dictCustomerIndustrys, "id", "name", ",");
		model.addAttribute("customerIndustryReplace",customerIndustryReplace);
		model.addAttribute("customerTypeReplace",customerTypeReplace);
        return new ModelAndView("/asset/assetManagementList");
    }
	
	/**
	 * Asset Management
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "assetManagementForm")
    public ModelAndView assetManagementForm(String id,String searchType,String communicatorId,String sn,String referenceId,HttpServletRequest request, Model model) {
		model.addAttribute("id", id);
		model.addAttribute("searchType", searchType);
		
		//如果是增加初始化
		if(StringUtils.isEmpty(id)) {
			AjaxJson ajaxJson =this.createDeviceNext(communicatorId, sn, referenceId, searchType, request);
			model.addAttribute("ajaxJson", ajaxJson.toString());
		}
		
		List<DictCommunicationType> dctl = dictCommunicationTypeService.getAllList();
		String comms = RoletoJson.listToReplaceStr(dctl, "id", "name");
		model.addAttribute("commTypeReplace", comms);
		
		List<Integer> dictDeviceTypeIds = Lists.newArrayList();
		dictDeviceTypeIds.add(202);
		dictDeviceTypeIds.add(203);
		dictDeviceTypeIds.add(204);
		dictDeviceTypeIds.add(205);
		List<DictDeviceType> dictDeviceTypes = this.dictDeviceTypeService.getListByIds(dictDeviceTypeIds);
		String deviceTypeReplace = RoletoJson.listToReplaceStr(dictDeviceTypes, "id", "name");
		model.addAttribute("deviceTypeReplace", deviceTypeReplace);
		
		List<AssetMeterGroup> meterGroupList=assetMeterGroupService.getAllList();
		Map<String,List<AssetMeterGroup>> map = Maps.newHashMap();
		if(meterGroupList!=null) {
			for(AssetMeterGroup group:meterGroupList) {
				String type = group.getType();
				List<AssetMeterGroup> tmp = map.get(type);
				if(tmp==null) {
					tmp=Lists.newArrayList();
				}
				tmp.add(group);
				map.put(type, tmp);
			}
		}
		
		//编辑进来，非gprs表
//		if(searchType.equals("Meter")) {
//			List<AssetCommunicator> communicators= assetCommunicatorService.getListNoGprs();
//			String commReplace = RoletoJson.listToReplaceStr(communicators, "id", "sn");
//			model.addAttribute("commReplace", commReplace);
//		}
//		
		
		String meaGroups = map.get("1")!=null?RoletoJson.listToReplaceStr(map.get("1"), "id", "name"):"";
		String touGroups = map.get("2")!=null?RoletoJson.listToReplaceStr(map.get("2"), "id", "name"):"";
		String limitGroups =map.get("3")!=null?RoletoJson.listToReplaceStr(map.get("3"), "id", "name"):"";
		String stepGroups = map.get("4")!=null?RoletoJson.listToReplaceStr(map.get("4"), "id", "name"):"";
		String otherGroups = map.get("5")!=null?RoletoJson.listToReplaceStr(map.get("5"), "id", "name"):"";
		model.addAttribute("meaGroupReplace", meaGroups);
		model.addAttribute("touGroupReplace", touGroups);
		model.addAttribute("limitGroupReplace", limitGroups);
		model.addAttribute("stepGroupReplace", stepGroups);
		model.addAttribute("otherGroupReplace", otherGroups);

		Map<String,List<SysService>> serviceMap = Maps.newHashMap();
		List<SysService> sysServicelist = sysServiceService.getAllList();
		if(sysServicelist!=null) {
			for(SysService service:sysServicelist) {
				String serviceType=service.getServiceType();
				List<SysService> tmp = serviceMap.get(serviceType);
				if(tmp==null) {
					tmp= Lists.newArrayList();
				}
				tmp.add(service);
				serviceMap.put(serviceType, tmp);
			}
		}
		
		String channels = RoletoJson.listToReplaceStr(serviceMap.get("1"), "id", "introduction");
		model.addAttribute("channelReplace", channels);
		
		String schedules = RoletoJson.listToReplaceStr(serviceMap.get("3"), "id", "introduction");
		model.addAttribute("scheduleReplace", schedules);
		
		List<AssetScheduleScheme> scheduleSchemeList = assetScheduleSchemeService.getAllList();
		String scheduleSchemes = RoletoJson.listToReplaceStr(scheduleSchemeList, "id", "name");
		model.addAttribute("scheduleSchemeReplace", scheduleSchemes);
		
		List<DictManufacturer> manufacturerList = dictManufacturerService.getAllList();
		String manufacturers = RoletoJson.listToReplaceStr(manufacturerList, "id", "name");
		model.addAttribute("manufacturerReplace", manufacturers);
		 
        return new ModelAndView("/asset/assetManagementForm");
    }
	
	
	/**
	 * 添加电表档案
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "addAssetDevice")
    public ModelAndView addAssetDevice(String communicatorId,String communicatorSn,String deviceType,HttpServletRequest request, Model model) {
		model.addAttribute("communicatorId", communicatorId);
		model.addAttribute("communicatorSn", communicatorSn);
		if(deviceType.equals("1")){
			return new ModelAndView("/asset/addAssetGPRSMeter");
		}else if(deviceType.equals("2")){
			return new ModelAndView("/asset/addAssetMeterCommunictor");
		}else if(deviceType.equals("4")){
			return new ModelAndView("/asset/importAssetGPRSMeter");
		}else if (deviceType.equals("5")) {
			List<DictCommunicationType> dctl = dictCommunicationTypeService.getAllList();
			String comms = RoletoJson.listToReplaceStr(dctl, "id", "name");
			model.addAttribute("commTypeReplace", comms);
			
			List<Integer> dictDeviceTypeIds = Lists.newArrayList();
			dictDeviceTypeIds.add(202);
			dictDeviceTypeIds.add(203);
			dictDeviceTypeIds.add(204);
			dictDeviceTypeIds.add(205);
			List<DictDeviceType> dictDeviceTypes = this.dictDeviceTypeService.getListByIds(dictDeviceTypeIds);
			String deviceTypeReplace = RoletoJson.listToReplaceStr(dictDeviceTypes, "id", "name");
			model.addAttribute("deviceTypeReplace", deviceTypeReplace);
			
			List<AssetMeterGroup> meterGroupList=assetMeterGroupService.getAllList();
			Map<String,List<AssetMeterGroup>> map = Maps.newHashMap();
			if(meterGroupList!=null) {
				for(AssetMeterGroup group:meterGroupList) {
					String type = group.getType();
					List<AssetMeterGroup> tmp = map.get(type);
					if(tmp==null) {
						tmp=Lists.newArrayList();
					}
					tmp.add(group);
					map.put(type, tmp);
				}
			}
			
			String meaGroups = map.get("1")!=null?RoletoJson.listToReplaceStr(map.get("1"), "id", "name"):"";
			String touGroups = map.get("2")!=null?RoletoJson.listToReplaceStr(map.get("2"), "id", "name"):"";
			String limitGroups =map.get("3")!=null?RoletoJson.listToReplaceStr(map.get("3"), "id", "name"):"";
			String stepGroups = map.get("4")!=null?RoletoJson.listToReplaceStr(map.get("4"), "id", "name"):"";
			String otherGroups = map.get("5")!=null?RoletoJson.listToReplaceStr(map.get("5"), "id", "name"):"";
			model.addAttribute("meaGroupReplace", meaGroups);
			model.addAttribute("touGroupReplace", touGroups);
			model.addAttribute("limitGroupReplace", limitGroups);
			model.addAttribute("stepGroupReplace", stepGroups);
			model.addAttribute("otherGroupReplace", otherGroups);

			Map<String,List<SysService>> serviceMap = Maps.newHashMap();
			List<SysService> sysServicelist = sysServiceService.getAllList();
			if(sysServicelist!=null) {
				for(SysService service:sysServicelist) {
					String serviceType=service.getServiceType();
					List<SysService> tmp = serviceMap.get(serviceType);
					if(tmp==null) {
						tmp= Lists.newArrayList();
					}
					tmp.add(service);
					serviceMap.put(serviceType, tmp);
				}
			}
			
			String channels = RoletoJson.listToReplaceStr(serviceMap.get("1"), "id", "introduction");
			model.addAttribute("channelReplace", channels);
			
			String schedules = RoletoJson.listToReplaceStr(serviceMap.get("3"), "id", "introduction");
			model.addAttribute("scheduleReplace", schedules);
			
			List<AssetScheduleScheme> scheduleSchemeList = assetScheduleSchemeService.getAllList();
			String scheduleSchemes = RoletoJson.listToReplaceStr(scheduleSchemeList, "id", "name");
			model.addAttribute("scheduleSchemeReplace", scheduleSchemes);
			
			List<DictManufacturer> manufacturerList = dictManufacturerService.getAllList();
			String manufacturers = RoletoJson.listToReplaceStr(manufacturerList, "id", "name");
			model.addAttribute("manufacturerReplace", manufacturers);
			
			return new ModelAndView("/asset/importAssetTempGPRSMeter");
		}else if(deviceType.equals("6")){
			return new ModelAndView("/asset/assetRelateMeterToDCU");
		}else if (deviceType.equals("8")) {
			List<Integer> dictDeviceTypeIds = Lists.newArrayList();
			dictDeviceTypeIds.add(202);
			dictDeviceTypeIds.add(203);
			dictDeviceTypeIds.add(204);
			dictDeviceTypeIds.add(205);
			List<DictDeviceType> dictDeviceTypes = this.dictDeviceTypeService.getListByIds(dictDeviceTypeIds);
			String deviceTypeReplace = RoletoJson.listToReplaceStr(dictDeviceTypes, "id", "name");
			model.addAttribute("deviceTypeReplace", deviceTypeReplace);
			
			List<DictCommunicationType> dctl = dictCommunicationTypeService.getAllList();
			String comms = RoletoJson.listToReplaceStr(dctl, "id", "name");
			model.addAttribute("commTypeReplace", comms);
			
			List<DictManufacturer> manufacturerList = dictManufacturerService.getAllList();
			String manufacturers = RoletoJson.listToReplaceStr(manufacturerList, "id", "name");
			model.addAttribute("manufacturerReplace", manufacturers);
			
			Map<String,List<SysService>> serviceMap = Maps.newHashMap();
			List<SysService> sysServicelist = sysServiceService.getAllList();
			if(sysServicelist!=null) {
				for(SysService service:sysServicelist) {
					String serviceType=service.getServiceType();
					List<SysService> tmp = serviceMap.get(serviceType);
					if(tmp==null) {
						tmp= Lists.newArrayList();
					}
					tmp.add(service);
					serviceMap.put(serviceType, tmp);
				}
			}
			
			String channels = RoletoJson.listToReplaceStr(serviceMap.get("1"), "id", "introduction");
			model.addAttribute("channelReplace", channels);
			
			String schedules = RoletoJson.listToReplaceStr(serviceMap.get("3"), "id", "introduction");
			model.addAttribute("scheduleReplace", schedules);
			return new ModelAndView("/asset/importAssetTempCommunicator");
		}else{
			return new ModelAndView("/asset/addAssetCommunicator");
		}
    }
	/**
	 * 添加电表档案
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "toChangeComType")
	public ModelAndView toChangeComType(String meterId,String deviceType,HttpServletRequest request, Model model) {
		
		List<DictCommunicationType> dctl = dictCommunicationTypeService.getAllList();
		String comms = RoletoJson.listToReplaceStr(dctl, "id", "name");
		
		model.addAttribute("commTypeReplace", comms);
		model.addAttribute("meterId", meterId);
		model.addAttribute("deviceType", deviceType);
		return new ModelAndView("/asset/assetMeterChange");
	}
	
	/**
	 * 跳转到assetMeter列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "list")
	public ModelAndView list(HttpServletRequest request, Model model) {
		return new ModelAndView("/asset/assetMeterList");
	}

	/**
	 * 实时抄读
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "onDemandReads")
	public ModelAndView onDemandReads(HttpServletRequest request, Model model) {
		String channelIds = "";
		String channelNames = "";
		DictDataitemGroup entity = new DictDataitemGroup();
		entity.setAppType("3");
		entity.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
		List<DictDataitemGroup> ddgList = dictDataitemGroupService
				.getList(entity);
		/*
		 * if(ddgList.size()>0){ DictDataitem dd=new DictDataitem(); Map<String,
		 * Object> qm = new HashMap<String, Object>(); qm.put("groupId",
		 * ddgList.get(0).getId()); qm.put("appType", "3"); dd.setExtData(qm);
		 * dd.setProtocolId("100"); channelIds+=ddgList.get(0).getId();
		 * List<DictDataitem> ddList=dictDataitemService.getList(dd);
		 * for(DictDataitem d:ddList){ channelIds+=","+d.getId();
		 * if(StringUtil.isNotEmpty(channelNames)){
		 * channelNames+=","+d.getName(); }else{ channelNames+=d.getName(); } }
		 * }
		 */
		model.addAttribute("channelIds", channelIds);
		model.addAttribute("channelNames", channelNames);

		List<DictCommunicationType> dctl = dictCommunicationTypeService
				.getAllList();
		String comms = RoletoJson.listToReplaceStr(dctl, "id", "name");
		model.addAttribute("commReplace", comms);
		return new ModelAndView("asset/OnDemandReads");
	}
	
	
	/**
	 * 实时抄读
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "onDemandReads376")
	public ModelAndView onDemandReads376(HttpServletRequest request, Model model) {
		String channelIds = "";
		String channelNames = "";
		DictDataitemGroup entity = new DictDataitemGroup();
		entity.setAppType("3");
		entity.setProtocolId("200");
		List<DictDataitemGroup> ddgList = dictDataitemGroupService
				.getList(entity);
		/*
		 * if(ddgList.size()>0){ DictDataitem dd=new DictDataitem(); Map<String,
		 * Object> qm = new HashMap<String, Object>(); qm.put("groupId",
		 * ddgList.get(0).getId()); qm.put("appType", "3"); dd.setExtData(qm);
		 * dd.setProtocolId("100"); channelIds+=ddgList.get(0).getId();
		 * List<DictDataitem> ddList=dictDataitemService.getList(dd);
		 * for(DictDataitem d:ddList){ channelIds+=","+d.getId();
		 * if(StringUtil.isNotEmpty(channelNames)){
		 * channelNames+=","+d.getName(); }else{ channelNames+=d.getName(); } }
		 * }
		 */
		model.addAttribute("channelIds", channelIds);
		model.addAttribute("channelNames", channelNames);

		List<DictCommunicationType> dctl = dictCommunicationTypeService
				.getAllList();
		String comms = RoletoJson.listToReplaceStr(dctl, "id", "name");
		model.addAttribute("commReplace", comms);
		return new ModelAndView("asset/OnDemandReads376");
	}
	
	/**
	 * 实时抄读
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "onDemandReads300")
	public ModelAndView onDemandReads300(HttpServletRequest request, Model model) {
		String channelIds = "";
		String channelNames = "";
		DictDataitemGroup entity = new DictDataitemGroup();
		entity.setAppType("3");
		entity.setProtocolId("300");
		List<DictDataitemGroup> ddgList = dictDataitemGroupService
				.getList(entity);
		/*
		 * if(ddgList.size()>0){ DictDataitem dd=new DictDataitem(); Map<String,
		 * Object> qm = new HashMap<String, Object>(); qm.put("groupId",
		 * ddgList.get(0).getId()); qm.put("appType", "3"); dd.setExtData(qm);
		 * dd.setProtocolId("100"); channelIds+=ddgList.get(0).getId();
		 * List<DictDataitem> ddList=dictDataitemService.getList(dd);
		 * for(DictDataitem d:ddList){ channelIds+=","+d.getId();
		 * if(StringUtil.isNotEmpty(channelNames)){
		 * channelNames+=","+d.getName(); }else{ channelNames+=d.getName(); } }
		 * }
		 */
		model.addAttribute("channelIds", channelIds);
		model.addAttribute("channelNames", channelNames);

		List<DictCommunicationType> dctl = dictCommunicationTypeService
				.getAllList();
		String comms = RoletoJson.listToReplaceStr(dctl, "id", "name");
		model.addAttribute("commReplace", comms);
		return new ModelAndView("asset/OnDemandReads300");
	}

	/**
	 * 传入电表和抄读项折做抄读请求 assetMeter查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "startReadsDatagrid")
	@ResponseBody
	public JqGridResponseTo startReadsDatagrid(JqGridSearchTo jqGridSearchTo,
			HttpServletRequest request) {
		TagUtil.setFieldValue(jqGridSearchTo, request); // 取传递的参数到JqGridSearchTo
		JqGridResponseTo j = null;
		JSONObject o = new JSONObject();
		String[] snsl = request.getParameterValues("snsl[]");
		String[] channelIdl = request.getParameterValues("channelIdl[]");
		String[] communicators = request.getParameterValues("communicators[]");
		String[] channelNames = request.getParameterValues("channelNames[]");
		String[] commmunications = request.getParameterValues("commmunications[]");
		List<OnDemandRead> list = new ArrayList<OnDemandRead>();
		if (snsl == null || channelIdl == null) {
			PageInfo<OnDemandRead> pageInfo = new PageInfo<OnDemandRead>(list);
			j = JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
			return j;
		}
		try {
			Map<String, Object> odrData = new HashMap<String, Object>();
			
			//http://localhost:8080/HESWeb/interfaces/ReplyMeterReadings?wsdl
			String basePath = ResourceUtil.getUciBasePath(request);
			//	String basePath ="http://linan1607.f3322.net:20000/HESWeb";
			SysUser su = TokenManager.getToken();
			MeterReadingsRequestMessageType mr = new MeterReadingsRequestMessageType();
			HeaderType ht = new HeaderType();
			Date date = new Date();
			ht.setVerb("get");
			ht.setNoun("MeterReadings");
			ht.setTimestamp(DateUtils.dateToXmlDate(date));
			ht.setSource("ClouESP HES");
			ht.setMessageID(DateUtils.formatDate("yyyyMMdd") + su.getId() + "channels");
			ht.setAsyncReplyFlag(true);
			ht.setReplyAddress(basePath + "/interfaces/ReplyMeterReadings?wsdl");
			ht.setAckRequired(true);
			UserType u = new UserType();
			u.setOrganization(su.getOrgId());
			u.setUserID(su.getId());
			ht.setUser(u);
			mr.setHeader(ht);
			GetMeterReadings getGetMeterReadings = new GetMeterReadings();
			for (int i = 0; i < snsl.length; i++) {
				ReadingType readingType = new ReadingType();
				for (int k = 0; k < channelIdl.length; k++) {
					Name n = new Name();
					NameType nt = new NameType();
					n.setName(channelIdl[k]);
					nt.setName("ReadingType");
					n.setNameType(nt);
					readingType.getNames().add(n);
					OnDemandRead odr = new OnDemandRead();
					odr.setId(channelIdl[k] + snsl[i]);
					odr.setSn(snsl[i]);
					AssetMeter am=assetMeterService.getEntityBySN(snsl[i]);
					odr.setMeterName(am.getName());
					odr.setAddr(am.getAddr());
					DictDeviceModel model=dictDeviceModelService.getEntity(am.getModel());
					odr.setModel(model.getName());
					odr.setRequestTime(date);
					odr.setStatus("1");
					odr.setCommunicator(communicators[i]);
					odr.setCommmunication(commmunications[i]);
					odr.setDataChannel(MutiLangUtil.assemblyI18nData(MutiLangUtil.DATAITEM_I18N,channelNames[k]));
					list.add(odr);
					odrData.put(odr.getId(), odr);
				}
				getGetMeterReadings.getReadingType().add(readingType);
				EndDevice ed = new EndDevice();
				ed.setMRID(snsl[i]);
				getGetMeterReadings.getEndDevice().add(ed);
			}
			JedisUtils.delObject(DateUtils.formatDate(DateUtils.getDateAdd(date, 5, -1), "yyyyMMdd") + su.getId());
			GetMeterReadingsPayloadType gmrpt = new GetMeterReadingsPayloadType();
			gmrpt.setGetMeterReadings(getGetMeterReadings);
			mr.setPayload(gmrpt);
			o.put("status", true);
			try {
				GetMeterReadingsPort port = (GetMeterReadingsPort) UciInterfaceUtil.getInterface("GetMeterReadingsPort", GetMeterReadingsPort.class,sysServiceAttributeService);
				MeterReadingsResponseMessageType s = port.getMeterReadings(mr);
				if (s == null || s.getReply() == null || StringUtil.isEmpty(s.getReply().getResult())) {
					for (OnDemandRead dr : list) {
						dr.setRequestTime(DateUtils.xmlDate2Date(s.getHeader().getTimestamp()));
						dr.setStatus("3");
						dr.setValue(MutiLangUtil.doMutiLang("deviceList.abnormalRequestFromUCI"));
						odrData.put(dr.getId(), dr);
					}
					o.put("status", false);
				} else {
					if (!s.getReply().getResult().toUpperCase().equals("OK")) {
						for (OnDemandRead dr : list) {
							dr.setRequestTime(DateUtils.xmlDate2Date(s.getHeader().getTimestamp()));
							dr.setStatus("3");
							dr.setValue(s.getReply().getResult());
							odrData.put(dr.getId(), dr);
						}
						o.put("status", false);
					} else {
						for (OnDemandRead dr : list) {
							dr.setRequestTime(DateUtils.xmlDate2Date(s.getHeader().getTimestamp()));
							odrData.put(dr.getId(), dr);
						}
					}
				}
			} catch (Exception e) {
				for (OnDemandRead dr : list) {
					dr.setStatus("3");
					dr.setValue(MutiLangUtil.doMutiLang("deviceList.abnormalRequestFromUCI"));
					odrData.put(dr.getId(), dr);
				}
				o.put("status", false);
			}
			JedisUtils.setObject(ht.getMessageID(), odrData, 0);
			JedisUtils.setObject("setReadsIsView" + ht.getMessageID(), false, 0);
			PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
			PageInfo<OnDemandRead> pageInfo = new PageInfo<OnDemandRead>();
			pageInfo.setList(list);
			j = JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
			o.put("messageId", ht.getMessageID());
			j.setJson(o);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return j;
	}
	
	
	/**
	 * 传入电表和抄读项折做抄读请求 assetMeter查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "startReadsDatagrid376")
	@ResponseBody
	public JqGridResponseTo startReadsDatagrid376(JqGridSearchTo jqGridSearchTo,
			HttpServletRequest request) {
		TagUtil.setFieldValue(jqGridSearchTo, request); // 取传递的参数到JqGridSearchTo
		JqGridResponseTo j = null;
		JSONObject o = new JSONObject();
		String[] snsl = request.getParameterValues("snsl[]");
		String[] channelIdl = request.getParameterValues("channelIdl[]");
		String[] communicators = request.getParameterValues("communicators[]");
		String[] channelNames = request.getParameterValues("channelNames[]");
		String[] commmunications = request.getParameterValues("commmunications[]");
		List<OnDemandRead> list = new ArrayList<OnDemandRead>();
		if (snsl == null || channelIdl == null) {
			PageInfo<OnDemandRead> pageInfo = new PageInfo<OnDemandRead>(list);
			j = JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
			return j;
		}
		try {
			Map<String, Object> odrData = new HashMap<String, Object>();
			String basePath = ResourceUtil.getUciBasePath(request);
			SysUser su = TokenManager.getToken();
			MeterReadingsRequestMessageType mr = new MeterReadingsRequestMessageType();
			HeaderType ht = new HeaderType();
			Date date = new Date();
			ht.setVerb("get");
			ht.setNoun("MeterReadings");
			ht.setTimestamp(DateUtils.dateToXmlDate(date));
			ht.setSource("ClouESP HES");
			ht.setMessageID(DateUtils.formatDate("yyyyMMdd") + su.getId() + "channels");
			ht.setAsyncReplyFlag(true);
			ht.setReplyAddress(basePath + "/interfaces/ReplyMeterReadings?wsdl");
			ht.setAckRequired(true);
			UserType u = new UserType();
			u.setOrganization(su.getOrgId());
			u.setUserID(su.getId());
			ht.setUser(u);
			mr.setHeader(ht);
			GetMeterReadings getGetMeterReadings = new GetMeterReadings();
			for (int i = 0; i < snsl.length; i++) {
				ReadingType readingType = new ReadingType();
				for (int k = 0; k < channelIdl.length; k++) {
					Name n = new Name();
					NameType nt = new NameType();
					n.setName(channelIdl[k]);
					nt.setName("ReadingType");
					n.setNameType(nt);
					readingType.getNames().add(n);
					OnDemandRead odr = new OnDemandRead();
					odr.setId(channelIdl[k] + snsl[i]);
					odr.setSn(snsl[i]);
					odr.setRequestTime(date);
					odr.setStatus("1");
					odr.setCommunicator(communicators[i]);
					odr.setCommmunication(commmunications[i]);
					odr.setDataChannel(MutiLangUtil.assemblyI18nData(MutiLangUtil.DATAITEM_I18N,channelNames[k]));
					list.add(odr);
					odrData.put(odr.getId(), odr);
				}
				getGetMeterReadings.getReadingType().add(readingType);
				EndDevice ed = new EndDevice();
				ed.setMRID(snsl[i]);
				getGetMeterReadings.getEndDevice().add(ed);
			}
			JedisUtils.delObject(DateUtils.formatDate(DateUtils.getDateAdd(date, 5, -1), "yyyyMMdd") + su.getId());
			GetMeterReadingsPayloadType gmrpt = new GetMeterReadingsPayloadType();
			gmrpt.setGetMeterReadings(getGetMeterReadings);
			mr.setPayload(gmrpt);
			o.put("status", true);
			try {
				GetMeterReadingsPort port = (GetMeterReadingsPort) UciInterfaceUtil.getInterface("GetMeterReadingsPort", GetMeterReadingsPort.class,sysServiceAttributeService);
				MeterReadingsResponseMessageType s = port.getMeterReadings(mr);
				if (s == null || s.getReply() == null || StringUtil.isEmpty(s.getReply().getResult())) {
					for (OnDemandRead dr : list) {
						dr.setRequestTime(DateUtils.xmlDate2Date(s.getHeader().getTimestamp()));
						dr.setStatus("3");
						dr.setValue(MutiLangUtil.doMutiLang("deviceList.abnormalRequestFromUCI"));
						odrData.put(dr.getId(), dr);
					}
					o.put("status", false);
				} else {
					if (!s.getReply().getResult().toUpperCase().equals("OK")) {
						for (OnDemandRead dr : list) {
							dr.setRequestTime(DateUtils.xmlDate2Date(s.getHeader().getTimestamp()));
							dr.setStatus("3");
							dr.setValue(s.getReply().getResult());
							odrData.put(dr.getId(), dr);
						}
						o.put("status", false);
					} else {
						for (OnDemandRead dr : list) {
							dr.setRequestTime(DateUtils.xmlDate2Date(s.getHeader().getTimestamp()));
							odrData.put(dr.getId(), dr);
						}
					}
				}
			} catch (Exception e) {
				for (OnDemandRead dr : list) {
					dr.setStatus("3");
					dr.setValue(MutiLangUtil.doMutiLang("deviceList.abnormalRequestFromUCI"));
					odrData.put(dr.getId(), dr);
				}
				o.put("status", false);
			}
			JedisUtils.setObject(ht.getMessageID(), odrData, 0);
			JedisUtils.setObject("setReadsIsView" + ht.getMessageID(), false, 0);
			PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
			PageInfo<OnDemandRead> pageInfo = new PageInfo<OnDemandRead>();
			pageInfo.setList(list);
			j = JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
			o.put("messageId", ht.getMessageID());
			j.setJson(o);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return j;
	}
	
	
	@RequestMapping(value = "startReadsDatagrid300")
	@ResponseBody
	public JqGridResponseTo startReadsDatagrid300(JqGridSearchTo jqGridSearchTo,
			HttpServletRequest request) {
		TagUtil.setFieldValue(jqGridSearchTo, request); // 取传递的参数到JqGridSearchTo
		JqGridResponseTo j = null;
		JSONObject o = new JSONObject();
		String[] snsl = request.getParameterValues("snsl[]");
		String[] channelIdl = request.getParameterValues("channelIdl[]");
		String[] communicators = request.getParameterValues("communicators[]");
		String[] channelNames = request.getParameterValues("channelNames[]");
		String[] commmunications = request.getParameterValues("commmunications[]");
		List<OnDemandRead> list = new ArrayList<OnDemandRead>();
		if (snsl == null || channelIdl == null) {
			PageInfo<OnDemandRead> pageInfo = new PageInfo<OnDemandRead>(list);
			j = JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
			return j;
		}
		try {
			Map<String, Object> odrData = new HashMap<String, Object>();
			String basePath = ResourceUtil.getUciBasePath(request);
			SysUser su = TokenManager.getToken();
			MeterReadingsRequestMessageType mr = new MeterReadingsRequestMessageType();
			HeaderType ht = new HeaderType();
			Date date = new Date();
			ht.setVerb("get");
			ht.setNoun("MeterReadings");
			ht.setTimestamp(DateUtils.dateToXmlDate(date));
			ht.setSource("ClouESP HES");
			ht.setMessageID(DateUtils.formatDate("yyyyMMdd") + su.getId() + "channels");
			ht.setAsyncReplyFlag(true);
			ht.setReplyAddress(basePath + "/interfaces/ReplyMeterReadings?wsdl");
			ht.setAckRequired(true);
			UserType u = new UserType();
			u.setOrganization(su.getOrgId());
			u.setUserID(su.getId());
			ht.setUser(u);
			mr.setHeader(ht);
			GetMeterReadings getGetMeterReadings = new GetMeterReadings();
			for (int i = 0; i < snsl.length; i++) {
				ReadingType readingType = new ReadingType();
				for (int k = 0; k < channelIdl.length; k++) {
					Name n = new Name();
					NameType nt = new NameType();
					n.setName(channelIdl[k]);
					nt.setName("ReadingType");
					n.setNameType(nt);
					readingType.getNames().add(n);
					OnDemandRead odr = new OnDemandRead();
					odr.setId(channelIdl[k] + snsl[i]);
					odr.setSn(snsl[i]);
					odr.setRequestTime(date);
					odr.setStatus("1");
					odr.setCommunicator(communicators[i]);
					odr.setCommmunication(commmunications[i]);
					odr.setDataChannel(MutiLangUtil.assemblyI18nData(MutiLangUtil.DATAITEM_I18N,channelNames[k]));
					list.add(odr);
					odrData.put(odr.getId(), odr);
				}
				getGetMeterReadings.getReadingType().add(readingType);
				EndDevice ed = new EndDevice();
				ed.setMRID(snsl[i]);
				getGetMeterReadings.getEndDevice().add(ed);
			}
			JedisUtils.delObject(DateUtils.formatDate(DateUtils.getDateAdd(date, 5, -1), "yyyyMMdd") + su.getId());
			GetMeterReadingsPayloadType gmrpt = new GetMeterReadingsPayloadType();
			gmrpt.setGetMeterReadings(getGetMeterReadings);
			mr.setPayload(gmrpt);
			o.put("status", true);
			try {
				GetMeterReadingsPort port = (GetMeterReadingsPort) UciInterfaceUtil.getInterface("GetMeterReadingsPort", GetMeterReadingsPort.class,sysServiceAttributeService);
				MeterReadingsResponseMessageType s = port.getMeterReadings(mr);
				if (s == null || s.getReply() == null || StringUtil.isEmpty(s.getReply().getResult())) {
					for (OnDemandRead dr : list) {
						dr.setRequestTime(DateUtils.xmlDate2Date(s.getHeader().getTimestamp()));
						dr.setStatus("3");
						dr.setValue(MutiLangUtil.doMutiLang("deviceList.abnormalRequestFromUCI"));
						odrData.put(dr.getId(), dr);
					}
					o.put("status", false);
				} else {
					if (!s.getReply().getResult().toUpperCase().equals("OK")) {
						for (OnDemandRead dr : list) {
							dr.setRequestTime(DateUtils.xmlDate2Date(s.getHeader().getTimestamp()));
							dr.setStatus("3");
							dr.setValue(s.getReply().getResult());
							odrData.put(dr.getId(), dr);
						}
						o.put("status", false);
					} else {
						for (OnDemandRead dr : list) {
							dr.setRequestTime(DateUtils.xmlDate2Date(s.getHeader().getTimestamp()));
							odrData.put(dr.getId(), dr);
						}
					}
				}
			} catch (Exception e) {
				for (OnDemandRead dr : list) {
					dr.setStatus("3");
					dr.setValue(MutiLangUtil.doMutiLang("deviceList.abnormalRequestFromUCI"));
					odrData.put(dr.getId(), dr);
				}
				o.put("status", false);
			}
			JedisUtils.setObject(ht.getMessageID(), odrData, 0);
			JedisUtils.setObject("setReadsIsView" + ht.getMessageID(), false, 0);
			PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
			PageInfo<OnDemandRead> pageInfo = new PageInfo<OnDemandRead>();
			pageInfo.setList(list);
			j = JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
			o.put("messageId", ht.getMessageID());
			j.setJson(o);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return j;
	}
	
	/**
	 * 取消任务
	 * @param messageId
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "cancelJob")
	@ResponseBody
	public AjaxJson cancelJob(String messageId, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			JobCancelPort port = (JobCancelPort)UciInterfaceUtil.getInterface("JobCancelPort", JobCancelPort.class,sysServiceAttributeService);
			List<String> messageIds=new ArrayList<String>();
			messageIds.add(messageId);
			if(!port.cancelJob(messageIds)){
				j.setErrorMsg(MutiLangUtil.doMutiLang("device_read_channel_list.cancelFailure"));
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		return j;
	}

	/**
	 * 表示页面已经展示完成
	 * 
	 * @param msgId
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "setReadsIsView")
	@ResponseBody
	public AjaxJson setReadsIsView(String msgId, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			JedisUtils.setObject("setReadsIsView" + msgId, true, 0);
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg(MutiLangUtil.doMutiLang("system.systemException"));
		}
		return j;
	}
	
	/**
	 * 查询指定类型的service列表
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getServiceList")
	@ResponseBody
	public AjaxJson getServiceList(String serviceType,HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			SysService ss=new SysService();
			ss.setServiceType(serviceType);
			List<SysService> sslist = sysServiceService.getList(ss);
			j.setObj(sslist);
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg(MutiLangUtil.doMutiLang("system.systemException"));
		}
		return j;
	}
	
	/**
	 * 根据mac返回meter信息
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getMeterByMacs")
	@ResponseBody
	public AjaxJson getMeterByMacs(@RequestParam("macs[]") List<String> macs ,HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			List<AssetMeter> list = assetMeterService.getByMacs(macs);
			Map<String,AssetMeter> macMap = Maps.newHashMap();
			if(list!=null) {
				for(AssetMeter meter:list) {
					macMap.put(meter.getMac(), new AssetMeter(meter.getId(),meter.getSn(),meter.getMac()));
				}
			}
			j.setObj(macMap);
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg(MutiLangUtil.doMutiLang("system.systemException"));
		}
		return j;
	}

	/**
	 * 导出
	 * 
	 * @param dataIntegrity
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "getExcelResultData")
	@ResponseBody
	public void getExcelResultData(String messageId,
			HttpServletRequest request, HttpServletResponse response) {
		if (StringUtil.isEmpty(messageId)) {
			return;
		}
		try {
			Map<String, Object> map = (Map<String, Object>) JedisUtils
					.getObject(messageId);
			List<OnDemandRead> odrs = new ArrayList<OnDemandRead>();
			if (map == null) {
				OnDemandRead odr = new OnDemandRead();
				odrs.add(odr);
			} else {

				for (Entry<String, Object> entry : map.entrySet()) {
					OnDemandRead odr = (OnDemandRead) entry.getValue();
					odrs.add(odr);
				}
			}
			if (odrs.size() <= 0) {
				OnDemandRead odr = new OnDemandRead();
				odrs.add(odr);
			}
			ExcelDataFormatter edf = new ExcelDataFormatter();
			Map<String, String> requestTime = new HashMap<String, String>();
			requestTime.put("requestTime", "MM/dd/yyyy HH:mm:ss");
			edf.set("requestTime", requestTime);
			Map<String, String> reponseTime = new HashMap<String, String>();
			reponseTime.put("reponseTime", "MM/dd/yyyy HH:mm:ss");
			edf.set("reponseTime", reponseTime);
			Map<String, String> status = new HashMap<String, String>();
			status.put("1", "Processing");
			status.put("2", "Success");
			status.put("3", "Failed");
			status.put("4", "Timeout");
			edf.set("status", status);
			ExcelUtils.writeToFile(odrs, edf, "excelResultDataList.xlsx",
					response, ValidGroup1.class);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 打印
	 * 
	 * @param dataIntegrity
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "getPrintResultData")
	@ResponseBody
	public void getPrintResultData(String messageId,
			HttpServletRequest request, HttpServletResponse response) {
		if (StringUtil.isEmpty(messageId)) {
			return;
		}
		try {
			Map<String, Object> map = (Map<String, Object>) JedisUtils
					.getObject(messageId);
			List<OnDemandRead> odrs = new ArrayList<OnDemandRead>();
			if (map == null) {
				OnDemandRead odr = new OnDemandRead();
				odrs.add(odr);
			} else {
				for (Entry<String, Object> entry : map.entrySet()) {
					OnDemandRead odr = (OnDemandRead) entry.getValue();
					odrs.add(odr);
				}
			}
			if (odrs.size() <= 0) {
				OnDemandRead odr = new OnDemandRead();
				odrs.add(odr);
			}
			Collections.sort(odrs);
			ExcelDataFormatter edf = new ExcelDataFormatter();
			Map<String, String> requestTime = new HashMap<String, String>();
			requestTime.put("requestTime", "MM/dd/yyyy HH:mm:ss");
			edf.set("requestTime", requestTime);
			Map<String, String> reponseTime = new HashMap<String, String>();
			reponseTime.put("reponseTime", "MM/dd/yyyy HH:mm:ss");
			edf.set("reponseTime", reponseTime);
			Map<String, String> status = new HashMap<String, String>();
			status.put("1", "Processing");
			status.put("2", "Success");
			status.put("3", "Failed");
			status.put("4", "Timeout");
			edf.set("status", status);
			CreatePdf.printPdf(odrs, edf, ValidGroup1.class, request, response);
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	/**
	 * 跳转到assetMeter新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "assetMeter")
	public ModelAndView assetMeter(AssetMeter assetMeter,
			HttpServletRequest request, Model model) {
		if (StringUtil.isNotEmpty(assetMeter.getId())) {
			try {
				assetMeter = assetMeterService.getEntity(assetMeter.getId());
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			model.addAttribute("assetMeter", assetMeter);
		}
		return new ModelAndView("/asset/assetMeter");
	}

	/**
	 * assetMeter查询分页
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
	@ResponseBody
	public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,
			HttpServletRequest request) {
		TagUtil.setFieldValue(jqGridSearchTo, request); // 取传递的参数到JqGridSearchTo
		JqGridResponseTo j = null;
		try {
			j = assetMeterService.getForJqGrid(jqGridSearchTo);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return j;
	}

	/**
	 * 删除assetMeter信息
	 * 
	 * @param id
	 * @return
	 */
	@Transactional
	@RequestMapping(value = "del")
	@ResponseBody
	public AjaxJson del(String deviceId,String deviceType, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			SysUser su = TokenManager.getToken();
			// 1 GPRS,2,Communicator,3 普通表
			AssetRefreshPort arp=(AssetRefreshPort)UciInterfaceUtil.getInterface("AssetRefreshPort", AssetRefreshPort.class,sysServiceAttributeService);
			List<RefreshMessage> msgs=new ArrayList<RefreshMessage>();
			AssetMeter copyPrepera = assetMeterService.getEntity(deviceId);
			if(copyPrepera==null){
				//j.setErrorMsg(MutiLangUtil.doMutiLang("system.meterNullMsg"));
				//return j;
			}
			if(deviceType.equals("1")){
				AssetMeterDto logDto = this.assetMeterService.getMeterDtoInfo(deviceId);
				AssetMeter am = assetMeterService.getEntity(deviceId);
				if(am==null){
					j.setErrorMsg(MutiLangUtil.doMutiLang("system.meterNullMsg"));
					return j;
				}
				AssetCommunicator ac = assetCommunicatorService.getEntity(am.getCommunicatorId());
				if(ac==null){
					j.setErrorMsg(MutiLangUtil.doMutiLang("system.commnuicatorNullMsg"));
					return j;
				}
				//完整率
				DataIntegrity dis=new DataIntegrity();
				dis.setId(deviceId);
				dis.setIdType("1");
				dataIntegrityService.delete(dis);
				//漏点记录
				DataScheduleMissData dsmds=new DataScheduleMissData();
				dsmds.setDeviceId(deviceId);
				dataScheduleMissDataService.delete(dsmds);
				//采集进度
				DataScheduleProgress dsps=new DataScheduleProgress();
				dsps.setDeviceId(deviceId);
				dataScheduleProgressService.delete(dsps);
//				报警事件
//				DataMeterEvent dme=new DataMeterEvent();
//				dme.setDeviceId(deviceId);
//				dataMeterEventService.delete(dme);
				//分组
				AssetMeterGroupMap amgm=new AssetMeterGroupMap();
				amgm.setId(deviceId);
				//minutely
//				dataMeterDataEnergyMinutelyService.deleteById(deviceId);

				assetMeterService.delete(am);
				this.deletePpmAssetMeter(deviceId);//删除ppm关联表
				assetMeterGroupMapService.deleteById(am.getId());
				assetRouterService.deleteById(ac.getId());

				assetCommunicatorService.delete(ac);
				dataUserLogService.insertDataUserLog(su.getId(), "Asset Managerment", "Delete Meter",logDto.getLog("Delete Meter"));
				dataUserLogService.insertDataUserLog(su.getId(), "Asset Managerment", "Delete Communicator","[Delete Communicator:DCU SN:"+ac.getSn()+",Logic Name"+ac.getMac()+"]");
				}else if(deviceType.equals("2")){
				AssetCommunicator ac = assetCommunicatorService.getEntity(deviceId);
				if(ac==null){
					j.setErrorMsg(MutiLangUtil.doMutiLang("system.commnuicatorNullMsg"));
					return j;
				}
				AssetMeter am=new AssetMeter();
				am.setCommunicatorId(ac.getId());
				List<AssetMeter> list = assetMeterService.getList(am);
				if(list!=null&&list.size()>0){
					j.setErrorMsg(MutiLangUtil.doMutiLang("system.tCommunHChild"));
					return j;
				}
				ac.setRemoveFlag(1);
				assetRouterService.deleteById(ac.getId());
				assetCommunicatorService.delete(ac);
				dataUserLogService.insertDataUserLog(su.getId(), "Asset Managerment", "Delete Communicator","[Delete Communicator:DCU SN:"+ac.getSn()+",Logic Name"+ac.getMac()+"]");
			}else if(deviceType.equals("3")){
				//添加操作日志
				AssetMeterDto logDto = this.assetMeterService.getMeterDtoInfo(deviceId);
				AssetMeter am = assetMeterService.getEntity(deviceId);
				if(am==null){
					j.setErrorMsg(MutiLangUtil.doMutiLang("system.meterNullMsg"));
					return j;
				}
				//完整率
				DataIntegrity dis=new DataIntegrity();
				dis.setId(deviceId);
				dis.setIdType("1");
				dataIntegrityService.delete(dis);
				//采集进度
				DataScheduleProgress dsps=new DataScheduleProgress();
				dsps.setDeviceId(deviceId);
				dataScheduleProgressService.delete(dsps);
//				//报警事件
//				DataMeterEvent dme=new DataMeterEvent();
//				dme.setDeviceId(deviceId);
//				dataMeterEventService.delete(dme);
				//漏点记录
				DataScheduleMissData dsmds=new DataScheduleMissData();
				dsmds.setDeviceId(deviceId);
				dataScheduleMissDataService.delete(dsmds);
//				//minutely
//				dataMeterDataEnergyMinutelyService.deleteById(deviceId);
				//分组
				AssetMeterGroupMap amgm=new AssetMeterGroupMap();
				amgm.setMeterId(deviceId);
				assetMeterGroupMapService.deleteById(deviceId);

				assetMeterService.delete(am);
				this.deletePpmAssetMeter(deviceId);//删除ppm关联表
				dataUserLogService.insertDataUserLog(su.getId(), "Asset Managerment", "Delete Meter", logDto.getLog("Delete Meter"));
			}
			//判断 是否为自动注册模式
			String meterMode =  ResourceUtil.getSessionattachmenttitle("meter.grps.mode");
			if(meterMode.equals("2")&&copyPrepera != null){
				assetMeterService.savePLCMeter(copyPrepera);
			}

			//删除表信息
			RefreshMessage refMsg=new RefreshMessage();
			refMsg.setType("meter");
			refMsg.setOpType("delete");
			refMsg.getIds().add(deviceId);
			msgs.clear();
			msgs.add(refMsg);
			try{
				if(!arp.refresh(msgs)){
					System.out.println("uci刷新删除失败"+deviceId);
				}
			} catch (Exception e) {
				e.printStackTrace();
				j.setSuccess(false);
				j.setErrorMsg(MutiLangUtil.doMutiLang("system.UCIException"));
				return j;
			}
			j.setMsg(MutiLangUtil.doMutiLang("system.updateSucc"));
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg(MutiLangUtil.doMutiLang("system.systemException")+":"+e.getMessage());
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
		}
		return j;
	}
	
	
	
	/**
	 * 保存assetMeter信息
	 * @param id
	 * @return
	 */
	//@Transactional
	@RequestMapping(value = "save")
	@ResponseBody
	public AjaxJson save( HttpServletRequest request) {
		
		DefaultTransactionDefinition transDefinition = new DefaultTransactionDefinition();
		//开启新事物
		transDefinition.setPropagationBehavior(DefaultTransactionDefinition.PROPAGATION_REQUIRES_NEW);
		TransactionStatus transStatus = transactionManager.getTransaction(transDefinition);
		AjaxJson j = new AjaxJson();
		boolean hasWarn = false;
		try {
			SysUser su = TokenManager.getToken();
			String deviceId=request.getParameter("txt_deviceId");
			String deviceType=request.getParameter("txt_deviceType");
			// 1 GPRS,2,Communicator,3 普通表
			if(StringUtil.isEmpty(deviceType)){
				j.setErrorMsg("Device Type is Empty!");
				return j;
			}
			if(StringUtil.isEmpty(deviceId)){
				j.setErrorMsg("Device ID is Empty!");
				return j;
			}
			String commId=request.getParameter("txt_communicatior");//非gprs表编辑切换集成器
			String sn=request.getParameter("txt_sn");
			String mac=request.getParameter("txt_mac");
			String name=request.getParameter("txt_name");
			String addr=request.getParameter("txt_addr");
			String latitude=request.getParameter("txt_latitude");
			String longitude=request.getParameter("txt_longitude");
			String simNum=request.getParameter("txt_simNum");
			String manufacturer=request.getParameter("txt_manufacturer");
			String model=request.getParameter("txt_model");
			String fwVersion=request.getParameter("txt_firmVer");
			String comType=request.getParameter("txt_communicationType");
			String ip=request.getParameter("txt_ip");
			String port=request.getParameter("txt_port");
			String orgId=request.getParameter("txt_org_id");
			String measurementGroupId=request.getParameter("txt_meaGroup");
			String scheduleSchemeId=request.getParameter("txt_schemeGroup");
			String touGroupId=request.getParameter("txt_tou");
			String limiterGroupId=request.getParameter("txt_limiter");
			String stepTariffId=request.getParameter("txt_stepTariffId");
			
			String channel=request.getParameter("txt_channel");
			String schedule=request.getParameter("txt_schedule");
			
			String authType=request.getParameter("txt_authType");
			String llsPwd=request.getParameter("txt_llsPwd");
			String ak=request.getParameter("txt_ak");
			String ek=request.getParameter("txt_ek");
			String isEncrypt=request.getParameter("txt_isEncrypt");

			String ct=request.getParameter("txt_ct");
			String pt=request.getParameter("txt_pt");
			String indexDcu=request.getParameter("txt_indexDcu");
			//String comPort=request.getParameter("txt_comPort");
			String commDeviceType=request.getParameter("txt_comm_deviceType");
			String keyFlag=request.getParameter("txt_keyFlag");
			String listenMode=request.getParameter("txt_listenMode");
			
			AssetCommunicator assetCommunicator=new AssetCommunicator();
			assetCommunicator.setSn(sn);
			assetCommunicator.setMac(mac);
			assetCommunicator.setComType(comType);
			assetCommunicator.setFwVersion(fwVersion);
			assetCommunicator.setNetworkIp(ip);
			if(StringUtil.isNotEmpty(port)) {
				assetCommunicator.setNetworkPort(Integer.parseInt(port));
			}
			assetCommunicator.setModel(model);
			assetCommunicator.setManufacturer(manufacturer);
			assetCommunicator.setPass(llsPwd);
			assetCommunicator.setSimNum(simNum);
			
			assetCommunicator.setHlsAk(ak);
			assetCommunicator.setHlsEk(ek);
			assetCommunicator.setAuthType(authType);
			assetCommunicator.setPass(llsPwd);
			//assetCommunicator.setDeviceType(Integer.parseInt(commDeviceType));
			if(StringUtil.isNotEmpty(isEncrypt)){
				assetCommunicator.setIsEncrypt(Integer.parseInt(isEncrypt));
			}
			assetCommunicator.setListenMode(Integer.parseInt(listenMode));
			
			AssetMeter assetMeter=new AssetMeter();
			assetMeter.setSn(sn);
			assetMeter.setMac(mac);
			assetMeter.setPassword(llsPwd);
			assetMeter.setAuthType(authType);
			assetMeter.setComType(comType);
			assetMeter.setFwVersion(fwVersion);
			assetMeter.setHlsAk(ak);
			assetMeter.setHlsEk(ek);
			assetMeter.setLimiterGroupId(limiterGroupId);
			assetMeter.setStepTariffId(stepTariffId);
			assetMeter.setTouGroupId(touGroupId);
			assetMeter.setScheduleSchemeId(scheduleSchemeId);
			assetMeter.setMeasurementGroupId(measurementGroupId);
			assetMeter.setModel(model);
			assetMeter.setManufacturer(manufacturer);
			assetMeter.setAddr(addr);
			assetMeter.setLatitude(StringUtils.isEmpty(latitude)?null:new BigDecimal(latitude));
			assetMeter.setLongitude(StringUtils.isEmpty(longitude)?null:new BigDecimal(longitude));
			assetMeter.setCt(StringUtils.isNotEmpty(ct)?Integer.parseInt(ct):null);
			assetMeter.setPt(StringUtils.isNotEmpty(pt)?Integer.parseInt(pt):null);
			assetMeter.setIndexDcu(StringUtils.isNotEmpty(indexDcu)?Integer.parseInt(indexDcu):null);
//			assetMeter.setComPort(StringUtils.isNotEmpty(comPort)?Integer.parseInt(comPort):null);
			if(StringUtils.isNotEmpty(keyFlag)) {
				assetMeter.setKeyFlag(Integer.parseInt(keyFlag));
			}
			if(StringUtil.isNotEmpty(isEncrypt))
			assetMeter.setIsEncrypt(Integer.parseInt(isEncrypt));
			AssetRefreshPort arp=(AssetRefreshPort)UciInterfaceUtil.getInterface("AssetRefreshPort", AssetRefreshPort.class,sysServiceAttributeService);
			List<RefreshMessage> msgs=new ArrayList<RefreshMessage>(); 
			
			if(deviceId.equals(sn)) {
				j.put("isAdd", "1");
			}else {
				j.put("isAdd", "0");
			}
			
			//判断sn是否存在
			if(this.checkDeviceSn(deviceId, deviceType, sn)) {
				j.setErrorMsg(MutiLangUtil.doMutiLang("deviceList.deviceNumExists"));
				return j;
			}
			// 1 GPRS,2,Communicator,3 普通表
			if(deviceType.equals("1")){
				assetMeter.setName(name);
				if(deviceId.equals(sn)){
					double percent = meterLicenseCheck(1);
					if (percent > 1) {
						j.setErrorMsg(MutiLangUtil.doMutiLang("deviceList.over100"));
						return j;
					} else if (percent > 0.9) {
						hasWarn = true;
					}
					
					assetCommunicator.setDeviceType(201);
					assetCommunicator.setUtilityId(su.getUtilityId());
					assetCommunicator.setOrgId(orgId);
					assetCommunicator.setRemoveFlag(0);
					
					//module 模块属性 固定值
					assetCommunicator.setAuthType("1");
					assetCommunicator.setPass("000000");
					assetCommunicator.setIsEncrypt(0);
					
				
					assetMeter.setUtilityId(su.getUtilityId());
					String acId = (String) assetCommunicatorService.save(assetCommunicator);
					if(StringUtil.isNotEmpty(acId)){
						assetMeter.setCommunicatorId(acId);
						assetMeter.setOrgId(orgId);
						assetMeter.setRemoveFlag(0);
						String amId=(String) assetMeterService.save(assetMeter);
						//插入信用金额
						DictDeviceModel deviceModel = dictDeviceModelService.getEntity(assetMeter.getModel());
						VendMeterInitialCreditAmount initCreditAmount = new VendMeterInitialCreditAmount(amId, deviceModel.getInitCreditAmount(), null, 0, null, new Date(), "hes新增电表");
						this.assetMeterService.insertCredit(initCreditAmount);
						if(StringUtil.isNotEmpty(amId)){
							deviceId=amId;
							fillMeterRelaInfo(assetMeter, amId);
						}
						AssetRouter ar=new AssetRouter();
						ar.setCommunicatorId(acId);
						ar.setChannelId(channel);
						ar.setScheduleId(schedule);
						assetRouterService.save(ar);
						j.put("assetRouter", ar);
						RefreshMessage refMsg=new RefreshMessage();
						refMsg.setType("meter");
						refMsg.setOpType("add");
						refMsg.getIds().add(amId);
						RefreshMessage refMsg1=new RefreshMessage();
						refMsg1.setType("communicator");
						refMsg1.setOpType("add");
						refMsg1.getIds().add(acId);
						msgs.clear();
						msgs.add(refMsg);
						msgs.add(refMsg1);
						
						dataUserLogService.insertDataUserLog(su.getId(), "Asset Managerment", "Add Meter","[Add Meter:Meter SN="+ assetMeter.getSn()+",DCU SN="+assetCommunicator.getSn()+"]");
						dataUserLogService.insertDataUserLog(su.getId(), "Asset Managerment", "Add Communicator", "[Add Communicator:DCU SN="+ assetCommunicator.getSn()+",Logic Name="+assetCommunicator.getMac()+"]");
						transactionManager.commit(transStatus);
						try{
							if(!arp.refresh(msgs)){
								System.out.println("刷新失败"+deviceId);
							}
						} catch (Exception e) {
							e.printStackTrace();
							j.setErrorMsg(MutiLangUtil.doMutiLang("system.UCIException"));
							return j;
						}
					}
					j.put("meter", assetMeter);
					j.put("commnuicator", assetCommunicator);
				}else{
					//添加操作日志
					AssetMeterDto logDto = this.assetMeterService.getMeterDtoInfo(deviceId);
					assetMeter.setOrgId(orgId);
					assetCommunicator.setOrgId(orgId);
					AssetMeter am = assetMeterService.getEntity(deviceId);
					if(am==null){
						j.setErrorMsg("Device is Empty!");
						return j;
					}
					
					List<DictDeviceModel> modelList=dictDeviceModelService.getAllList();
					List<DictCommunicationType> commTypeList = dictCommunicationTypeService.getAllList();
					Map<String,String> modelMap = Maps.newHashMap();
					Map<String,String> commMap = Maps.newHashMap();
					if(modelList!=null) {
						for(DictDeviceModel modelTmp:modelList) {
							modelMap.put(modelTmp.getId(), modelTmp.getName());
						}
					}
					
					if(commTypeList!=null) {
						for(DictCommunicationType typeTmp:commTypeList) {
							commMap.put(typeTmp.getId(), typeTmp.getName());
						}
					}
					
					AssetCommunicator ac = assetCommunicatorService.getEntity(am.getCommunicatorId());
					
					String editMeterLog =AssetMeterDto.getMeterEditLog(am, assetMeter, modelMap);
					String editCommLog = AssetMeterDto.getCommEditLog(ac, assetCommunicator, modelMap, commMap);
							
					//module 模块属性 固定值 update 不需要还原
					assetCommunicator.setAuthType("1");
					assetCommunicator.setPass("000000");
					assetCommunicator.setIsEncrypt(0);
					
					MyBeanUtils.copyBeanNotNull2Bean(assetCommunicator, ac);
					assetCommunicatorService.update(ac);
					MyBeanUtils.copyBeanNotNull2Bean(assetMeter, am);
					
					assetMeterService.update(am);
					AssetRouter entity=new AssetRouter();
					entity.setCommunicatorId(ac.getId());
					if(assetRouterService.getCount(entity)<=0){
						AssetRouter ar=new AssetRouter();
						ar.setCommunicatorId(ac.getId());
						ar.setChannelId(channel);
						ar.setScheduleId(schedule);
						assetRouterService.save(ar);
						j.put("assetRouter", ar);
					}else{
						AssetRouter ar=new AssetRouter();
						ar.setCommunicatorId(ac.getId());
						ar.setChannelId(channel);
						ar.setScheduleId(schedule);
						assetRouterService.update(ar);
						j.put("assetRouter", ar);
					}
					/**计量曲线分组 */
					if(StringUtil.isNotEmpty(assetMeter.getMeasurementGroupId())){
						AssetMeterGroupMap amgm=new AssetMeterGroupMap();
						amgm.setId(am.getId());
						amgm.setType("1");
						amgm.setGroupId(assetMeter.getMeasurementGroupId());
						assetMeterGroupMapService.saveOrUpdate(amgm);
					}else {
						assetMeterGroupMapService.deleteByMeterAndType(deviceId, "1");
					}
					/**TOU 分组*/
					if(StringUtil.isNotEmpty(assetMeter.getTouGroupId())){
						AssetMeterGroupMap amgm=new AssetMeterGroupMap();
						amgm.setId(am.getId());
						amgm.setType("2");
						amgm.setGroupId(assetMeter.getTouGroupId());
						assetMeterGroupMapService.saveOrUpdate(amgm);
					}else {
						assetMeterGroupMapService.deleteByMeterAndType(deviceId, "2");
					}
					/**Limiter 分组*/
					if(StringUtil.isNotEmpty(assetMeter.getLimiterGroupId())){
						AssetMeterGroupMap amgm=new AssetMeterGroupMap();
						amgm.setId(am.getId());
						amgm.setType("3");
						amgm.setGroupId(assetMeter.getLimiterGroupId());
						assetMeterGroupMapService.saveOrUpdate(amgm);
					}else {
						assetMeterGroupMapService.deleteByMeterAndType(deviceId, "3");
					}
					/**阶梯费率 后面增加页面参数*/
					if(StringUtil.isNotEmpty(assetMeter.getStepTariffId())){
						AssetMeterGroupMap amgm=new AssetMeterGroupMap();
						amgm.setId(am.getId());
						amgm.setType("4");
						amgm.setGroupId(assetMeter.getStepTariffId());
						assetMeterGroupMapService.saveOrUpdate(amgm);
					}else {
						assetMeterGroupMapService.deleteByMeterAndType(deviceId, "4");
					}
					/**友好时段 后面增加页面参数*/
					/*if(StringUtil.isNotEmpty(assetMeter.getFriendlyId())){
						AssetMeterGroupMap amgm=new AssetMeterGroupMap();
						amgm.setId(am.getId());
						amgm.setType("5");
						amgm.setGroupId(assetMeter.getFriendlyId());
						assetMeterGroupMapService.saveOrUpdate(amgm);
					}*/
					/**采集方案*/
					if(StringUtil.isNotEmpty(assetMeter.getScheduleSchemeId())){
						AssetMeterGroupMap amgm=new AssetMeterGroupMap();
						amgm.setId(am.getId());
						amgm.setType("0");
						amgm.setGroupId(assetMeter.getScheduleSchemeId());
						assetMeterGroupMapService.saveOrUpdate(amgm);
					}else {
						//如果为空,清空原来的
						assetMeterGroupMapService.deleteByMeterAndType(deviceId, "0");
					}
					j.put("meter", am);
					j.put("commnuicator", ac);
					RefreshMessage refMsg=new RefreshMessage();
					refMsg.setType("meter");
					refMsg.setOpType("update");
					refMsg.getIds().add(deviceId);
					RefreshMessage refMsg1=new RefreshMessage();
					refMsg1.setType("communicator");
					refMsg1.setOpType("update");
					refMsg1.getIds().add(ac.getId());
					msgs.clear();
					msgs.add(refMsg);
					msgs.add(refMsg1);
					
					dataUserLogService.insertDataUserLog(su.getId(), "Asset Managerment", "Edit Meter",logDto.getLog("Edit Meter")+(StringUtils.isEmpty(editMeterLog)?"":(";"+editMeterLog)));
					dataUserLogService.insertDataUserLog(su.getId(), "Asset Managerment", "Edit Communicator","Edit Communicator:DCU SN:"+ac.getSn()+",Logic Name"+ac.getMac()+"]"+(StringUtils.isEmpty(editCommLog)?"":(";"+editCommLog)));
					transactionManager.commit(transStatus);
					try{
						if(!arp.refresh(msgs)){
							System.out.println("刷新失败"+deviceId);
						}
					} catch (Exception e) {
						e.printStackTrace();
						j.setErrorMsg(MutiLangUtil.doMutiLang("system.UCIException"));
						return j;
					}
				}
				j.put("searchType", "Meter");
				j.put("meterType", "1");
			}else if(deviceType.equals("2")){
				assetCommunicator.setOrgId(orgId);
				assetCommunicator.setName(name);
				assetCommunicator.setDeviceType(Integer.parseInt(commDeviceType));
				if(deviceId.equals(sn)){
					assetCommunicator.setUtilityId(su.getUtilityId());
					assetCommunicator.setRemoveFlag(0);
					deviceId=(String) assetCommunicatorService.save(assetCommunicator);
					j.put("commnuicator", assetCommunicator);
					//添加Router关联
					AssetRouter ar=new AssetRouter();
					ar.setCommunicatorId(deviceId);
					ar.setChannelId(channel);
					ar.setScheduleId(schedule);
					assetRouterService.save(ar);
					j.put("assetRouter", ar);
					RefreshMessage refMsg=new RefreshMessage();
					refMsg.setType("communicator");
					refMsg.setOpType("add");
					refMsg.getIds().add(deviceId);
					msgs.clear();
					msgs.add(refMsg);
					dataUserLogService.insertDataUserLog(su.getId(), "Asset Managerment", "Add Communicator", "[Add Communicator:DCU SN="+assetCommunicator.getSn()+",Logic Name="+assetCommunicator.getMac()+"]");
					transactionManager.commit(transStatus);
					try{
						if(!arp.refresh(msgs)){
							System.out.println("刷新失败"+deviceId);
						}
					} catch (Exception e) {
						e.printStackTrace();
						j.setErrorMsg(MutiLangUtil.doMutiLang("system.UCIException"));
						return j;
					}
					
				}else{
					AssetCommunicator ac = assetCommunicatorService.getEntity(deviceId);
					
					AssetMeter amt=new AssetMeter();
					amt.setCommunicatorId(ac.getId());
					List<AssetMeter> list = assetMeterService.getList(amt);
					
					//日志start
					List<DictDeviceModel> modelList=dictDeviceModelService.getAllList();
					List<DictCommunicationType> commTypeList = dictCommunicationTypeService.getAllList();
					Map<String,String> modelMap = Maps.newHashMap();
					Map<String,String> commMap = Maps.newHashMap();
					if(modelList!=null) {
						for(DictDeviceModel modelTmp:modelList) {
							modelMap.put(modelTmp.getId(), modelTmp.getName());
						}
					}
					if(commTypeList!=null) {
						for(DictCommunicationType typeTmp:commTypeList) {
							commMap.put(typeTmp.getId(), typeTmp.getName());
						}
					}
					String editCommLog = AssetMeterDto.getCommEditLog(ac, assetCommunicator, modelMap, commMap);
					//日志end
					
					MyBeanUtils.copyBeanNotNull2Bean(assetCommunicator, ac);
					assetCommunicatorService.update(ac);
					j.put("commnuicator", ac);
					
					for(AssetMeter a:list){
						if(!a.getOrgId().equals(ac.getOrgId())){
							a.setOrgId(ac.getOrgId());
							assetMeterService.update(a);
						}
					}
					AssetRouter entity=new AssetRouter();
					entity.setCommunicatorId(ac.getId());
					if(assetRouterService.getCount(entity)<=0){
						AssetRouter ar=new AssetRouter();
						ar.setCommunicatorId(deviceId);
						ar.setChannelId(channel);
						ar.setScheduleId(schedule);
						assetRouterService.save(ar);
						j.put("assetRouter", ar);
					}else{
						AssetRouter ar=new AssetRouter();
						ar.setCommunicatorId(deviceId);
						ar.setChannelId(channel);
						ar.setScheduleId(schedule);
						assetRouterService.update(ar);
						j.put("assetRouter", ar);
					}
					RefreshMessage refMsg=new RefreshMessage();
					refMsg.setType("communicator");
					refMsg.setOpType("update");
					refMsg.getIds().add(deviceId);
					msgs.clear();
					msgs.add(refMsg);
					dataUserLogService.insertDataUserLog(su.getId(), "Asset Managerment", "Edit Communicator",("[Edit Communicator:DCU SN="+ ac.getSn()+",Logic Name="+ac.getMac()+"]")+(StringUtils.isEmpty(editCommLog)?"":(";"+editCommLog)));
					
					transactionManager.commit(transStatus);
					try{
						if(!arp.refresh(msgs)){
							System.out.println("刷新失败"+deviceId);
						}
					} catch (Exception e) {
						e.printStackTrace();
						j.setErrorMsg(MutiLangUtil.doMutiLang("system.UCIException"));
						return j;
					}
					
					
				}
				j.put("searchType", "Commnuicator");
			}else if(deviceType.equals("3")){
				assetMeter.setName(name);
				String communicatorId=request.getParameter("txt_communicatorId");
				if(deviceId.equals(sn)){
					double percent = meterLicenseCheck(1);
					if (percent > 1) {
						j.setErrorMsg(MutiLangUtil.doMutiLang("deviceList.over100"));
						return j;
					} else if (percent > 0.9) {
						hasWarn = true;
					}
					AssetCommunicator entity = assetCommunicatorService.getEntity(communicatorId);
					assetMeter.setUtilityId(su.getUtilityId());
					assetMeter.setCommunicatorId(communicatorId);
					assetMeter.setOrgId(entity.getOrgId());
					assetMeter.setRemoveFlag(0);
					deviceId = insertMeterAndCredit(assetMeter);
					fillMeterRelaInfo(assetMeter, deviceId);
					
					j.put("meter", assetMeter);
					j.put("commnuicator", entity);
					RefreshMessage refMsg=new RefreshMessage();
					refMsg.setType("meter");
					refMsg.setOpType("add");
					refMsg.getIds().add(deviceId);
					msgs.clear();
					msgs.add(refMsg);
					
					dataUserLogService.insertDataUserLog(su.getId(), "Asset Managerment", "Add Meter","[Add Meter:Meter SN="+ assetMeter.getSn()+",DCU SN="+entity.getSn()+"]");
					
					transactionManager.commit(transStatus);
					try{
						if(!arp.refresh(msgs)){
							System.out.println("刷新失败"+deviceId);
						}
					} catch (Exception e) {
						e.printStackTrace();
						j.setErrorMsg(MutiLangUtil.doMutiLang("system.UCIException"));
						return j;
					}
					
				}else{
					AssetMeter am = assetMeterService.getEntity(deviceId);
					
					//日志start
					List<DictDeviceModel> modelList=dictDeviceModelService.getAllList();
					AssetMeterDto logDto = this.assetMeterService.getMeterDtoInfo(deviceId);
					Map<String,String> modelMap = Maps.newHashMap();
					if(modelList!=null) {
						for(DictDeviceModel modelTmp:modelList) {
							modelMap.put(modelTmp.getId(), modelTmp.getName());
						}
					}
					String editMeterLog =AssetMeterDto.getMeterEditLog(am, assetMeter, modelMap);
					//日志end
					
					
					MyBeanUtils.copyBeanNotNull2Bean(assetMeter, am);
					//从页面获取commId,切换集成器
					if(StringUtils.isNotEmpty(commId)) {
						am.setCommunicatorId(commId);
					}
					assetMeterService.update(am);
					AssetCommunicator entity = assetCommunicatorService.getEntity(am.getCommunicatorId());
					assetMeter.setOrgId(entity.getOrgId());
					/**计量曲线分组 */
					if(StringUtil.isNotEmpty(assetMeter.getMeasurementGroupId())){
						AssetMeterGroupMap amgm=new AssetMeterGroupMap();
						amgm.setId(am.getId());
						amgm.setType("1");
						amgm.setGroupId(assetMeter.getMeasurementGroupId());
						assetMeterGroupMapService.saveOrUpdate(amgm);
					}else {
						assetMeterGroupMapService.deleteByMeterAndType(deviceId, "1");
					}
					/**TOU 分组*/
					if(StringUtil.isNotEmpty(assetMeter.getTouGroupId())){
						AssetMeterGroupMap amgm=new AssetMeterGroupMap();
						amgm.setId(am.getId());
						amgm.setType("2");
						amgm.setGroupId(assetMeter.getTouGroupId());
						assetMeterGroupMapService.saveOrUpdate(amgm);
					}else {
						assetMeterGroupMapService.deleteByMeterAndType(deviceId, "2");
					}
					/**Limiter 分组*/
					if(StringUtil.isNotEmpty(assetMeter.getLimiterGroupId())){
						AssetMeterGroupMap amgm=new AssetMeterGroupMap();
						amgm.setId(am.getId());
						amgm.setType("3");
						amgm.setGroupId(assetMeter.getLimiterGroupId());
						assetMeterGroupMapService.saveOrUpdate(amgm);
					}else {
						assetMeterGroupMapService.deleteByMeterAndType(deviceId, "3");
					}
					/**阶梯费率 后面增加页面参数*/
					if(StringUtil.isNotEmpty(assetMeter.getStepTariffId())){
						AssetMeterGroupMap amgm=new AssetMeterGroupMap();
						amgm.setId(am.getId());
						amgm.setType("4");
						amgm.setGroupId(assetMeter.getStepTariffId());
						assetMeterGroupMapService.saveOrUpdate(amgm);
					}else {
						assetMeterGroupMapService.deleteByMeterAndType(deviceId, "4");
					}
					/**友好时段 后面增加页面参数*/
					/*if(StringUtil.isNotEmpty(assetMeter.getFriendlyId())){
						AssetMeterGroupMap amgm=new AssetMeterGroupMap();
						amgm.setId(am.getId());
						amgm.setType("5");
						amgm.setGroupId(assetMeter.getFriendlyId());
						assetMeterGroupMapService.saveOrUpdate(amgm);
					}*/
					/**采集方案*/
					if(StringUtil.isNotEmpty(assetMeter.getScheduleSchemeId())){
						AssetMeterGroupMap amgm=new AssetMeterGroupMap();
						amgm.setId(am.getId());
						amgm.setType("0");
						amgm.setGroupId(assetMeter.getScheduleSchemeId());
						assetMeterGroupMapService.saveOrUpdate(amgm);
					}else {
						//如果为空,清空原来的
						assetMeterGroupMapService.deleteByMeterAndType(deviceId, "0");
					}
					
					j.put("meter", am);
					j.put("commnuicator", entity);
					RefreshMessage refMsg=new RefreshMessage();
					refMsg.setType("meter");
					refMsg.setOpType("update");
					refMsg.getIds().add(deviceId);
					msgs.clear();
					msgs.add(refMsg);
					
					dataUserLogService.insertDataUserLog(su.getId(), "Asset Managerment", "Edit Meter",logDto.getLog("Edit Meter")+(StringUtils.isEmpty(editMeterLog)?"":(";"+editMeterLog)));
					transactionManager.commit(transStatus);
					try{
						if(!arp.refresh(msgs)){
							System.out.println("刷新失败"+deviceId);
						}
					} catch (Exception e) {
						e.printStackTrace();
						j.setErrorMsg(MutiLangUtil.doMutiLang("system.UCIException"));
						return j;
					}
					
					
				}
				j.put("searchType", "Meter");
				j.put("meterType", "2");
			}
			j.put("deviceId", deviceId);
			j.put("deviceType", deviceType);
		} catch (Exception e) {
			e.printStackTrace();
			j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
			if(!transStatus.isCompleted()) {
				transactionManager.rollback(transStatus);
			}
//			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return j;
		}
		if(hasWarn) {
			j.setMsg(MutiLangUtil.doMutiLang("deviceList.over90"));
		}else {
			j.setMsg(MutiLangUtil.doMutiLang("system.operSucce"));
		}
		return j;
	}
	
	
	/**
	 * 添加（gprs）meter、comm、普通meter
	 */
	@RequestMapping(value = "createDevice")
	@ResponseBody
	public AjaxJson createDevice(String communicatorId,String sn,String referenceId,String deviceType,HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			
			j.put("communicatorId", communicatorId);
			j.put("sn", sn);
			j.put("referenceId", referenceId);
			j.put("deviceType", deviceType);
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg(MutiLangUtil.doMutiLang("system.systemException"));
		}
		return j;
	}
	
	/**
	 * 
	 * 分步添加第二步
	 * 
	 */
	public AjaxJson createDeviceNext(String communicatorId,String sn,String referenceId,String deviceType,HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			if(StringUtil.isEmpty(deviceType)){
				j.setErrorMsg("Device Type is Empty");
				return j;
			}
			String mac=sn;
			if(deviceType.equals("1")||deviceType.equals("3")) {
				DataShipmentMeter shipMeter = this.dataShipmentMeterService.getEntity(sn);    
				if(shipMeter!=null&&StringUtils.isNotEmpty(shipMeter.getLdn())) {
					mac=shipMeter.getLdn();
				}
			}
			// 1 GPRS,2,Communicator,3 普通表
			if(deviceType.equals("1")){
				if(StringUtil.isNotEmpty(referenceId)){
					AssetMeter t = assetMeterService.getEntity(referenceId);
					AssetCommunicator ac=null;
					if(StringUtil.isNotEmpty(t.getCommunicatorId())){
						ac=assetCommunicatorService.getEntity(t.getCommunicatorId());
					}
					ac.setId(sn);
					ac.setSn(sn);
					ac.setName(sn);
					ac.setMac(mac);
					t.setSn(sn);
					t.setName(sn);
					t.setMac(mac);
					t.setId(sn);
					j.put("meter", t);
					j.put("commnuicator", ac);
				}else{
					AssetCommunicator ac=new AssetCommunicator();
					ac.setSn(sn);
					ac.setName(sn);
					ac.setMac(mac);
					ac.setId(sn);
					AssetMeter assetMeter=new AssetMeter();
					assetMeter.setId(sn);
					assetMeter.setSn(sn);
					assetMeter.setName(sn);
					assetMeter.setMac(mac);
					assetMeter.setIsEncrypt(1);//默认选中加密
					j.put("meter", assetMeter);
					j.put("commnuicator", ac);
				}
				j.put("searchType", "Meter");
				j.put("meterType", "1");
			}else if(deviceType.equals("2")){
				if(StringUtil.isNotEmpty(referenceId)){
					AssetCommunicator entity = assetCommunicatorService.getEntity(referenceId);
					entity.setId(sn);
					entity.setSn(sn);
					entity.setName(sn);
					entity.setMac(sn);
					j.put("commnuicator", entity);
				}else{
					AssetCommunicator entity=new AssetCommunicator();
					entity.setId(sn);
					entity.setSn(sn);
					entity.setName(sn);
					entity.setMac(sn);
					entity.setIsEncrypt(1);//默认选中加密
					j.put("commnuicator", entity);
				}
				j.put("searchType", "Commnuicator");
			}else if(deviceType.equals("3")){
				if(StringUtil.isNotEmpty(referenceId)){
					AssetMeter t = assetMeterService.getEntity(referenceId);
					AssetCommunicator ac=assetCommunicatorService.getEntity(communicatorId);
					t.setSn(sn);
					t.setName(sn);
					t.setMac(mac);
					t.setId(sn);
					t.setCommunicatorId(ac.getId());
					j.put("meter", t);
					j.put("commnuicator", ac);
				}else{
					AssetCommunicator ac=assetCommunicatorService.getEntity(communicatorId);
					AssetMeter assetMeter=new AssetMeter();
					assetMeter.setId(sn);
					assetMeter.setSn(sn);
					assetMeter.setName(sn);
					assetMeter.setMac(mac);
					assetMeter.setCommunicatorId(ac.getId());
					assetMeter.setIsEncrypt(1);//默认选中加密
					j.put("meter", assetMeter);
					j.put("commnuicator", ac);
				}
				j.put("searchType", "Meter");
				j.put("meterType", "2");
			}
			
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg(MutiLangUtil.doMutiLang("system.systemException"));
		}
		return j;
	}
	
	public boolean checkDeviceSn(String deviceId,String deviceType, String deviceSn) {
		Long count=(long) 0;
		if(deviceType.equals("1")||"3".equals(deviceType)){
			AssetMeter entity=new AssetMeter();
			entity.setSn(deviceSn);
			if(StringUtil.isNotEmpty(deviceId)){
				entity.put("deviceId", deviceId);
			}
			count = assetMeterService.getCount(entity);
			if(count==0) {
				count=assetMeterService.getCountPrepare(entity); 
			}
		}else if(deviceType.equals("2")){
			AssetCommunicator entity=new AssetCommunicator();
			entity.setSn(deviceSn);
			if(StringUtil.isNotEmpty(deviceId)){
				entity.put("deviceId", deviceId);
			}
			count=assetCommunicatorService.getCount(entity);
		}
		if (count>0) {
			return true;
		}else {
			return false;
		}
	}
	/**
	 * 设备SN验证唯一
	 */
	@RequestMapping(value = "checkAssetDeviceSn")
	@ResponseBody
	public ValidForm checkAssetDeviceSn(String deviceId,String deviceType, HttpServletRequest request,
			HttpServletResponse response) {
		ValidForm v = new ValidForm();
		String param = oConvertUtils.getString(request.getParameter("param"));
		Long count=(long) 0;
		if(deviceType.equals("1")||"3".equals(deviceType)){
			AssetMeter entity=new AssetMeter();
			entity.setSn(param);
			if(StringUtil.isNotEmpty(deviceId)){
				entity.put("deviceId", deviceId);
			}
			count = assetMeterService.getCount(entity);
			if(count==0) {
				count=assetMeterService.getCountPrepare(entity); 
			}
		}else if(deviceType.equals("2")){
			AssetCommunicator entity=new AssetCommunicator();
			entity.setSn(param);
			if(StringUtil.isNotEmpty(deviceId)){
				entity.put("deviceId", deviceId);
			}
			count=assetCommunicatorService.getCount(entity);
		}
		if (count>0) {
			v.setInfo(MutiLangUtil.doMutiLang("deviceList.deviceNumExists"));
			v.setStatus("n");
		}
		return v;
	}
	/**
	 * 校验Mac
	 * @param deviceId
	 * @param deviceType
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "checkAssetDeviceMac")
	@ResponseBody
	public ValidForm checkAssetDeviceMac(String deviceId,String deviceType, HttpServletRequest request,
			HttpServletResponse response) {
		ValidForm v = new ValidForm();
		String param = oConvertUtils.getString(request.getParameter("param"));
		Long count=(long) 0;
		if(deviceType.equals("1")){
			AssetMeter entity=new AssetMeter();
			entity.setMac(param);
			if(StringUtil.isNotEmpty(deviceId)){
				entity.put("deviceId", deviceId);
			}
			count = assetMeterService.getCount(entity);
			if(count==0) {
				count=assetMeterService.getCountPrepare(entity); 
			}
		}else if(deviceType.equals("2")){
			AssetCommunicator entity=new AssetCommunicator();
			entity.setMac(param);
			if(StringUtil.isNotEmpty(deviceId)){
				entity.put("deviceId", deviceId);
			}
			count=assetCommunicatorService.getCount(entity);
		}
		if (count>0) {
			v.setInfo(MutiLangUtil.doMutiLang("deviceList.deviceNumExists"));
			v.setStatus("n");
		}
		return v;
	}
	
	/**
	 * 精确查找设备
	 * @param deviceSn
	 * @param deviceType
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "getAssetDevice")
	@ResponseBody
	public AjaxJson getAssetDevice(String deviceSn,String deviceType,
			HttpServletRequest request, Model model) {
		AjaxJson j = new AjaxJson();
		if (StringUtil.isNotEmpty(deviceSn)&&StringUtil.isNotEmpty(deviceType)) {
			try {
				String id="";
				if(deviceType.equals("1")||deviceType.equals("3")){
					AssetMeter entity=new AssetMeter();
					entity.setSn(deviceSn);
					 AssetMeter assetMeter = assetMeterService.get(entity);
					 if(assetMeter==null){
						 j.setErrorMsg(MutiLangUtil.doMutiLang("deviceList.noDeviceFound"));
						 return j;
					 }
					 AssetCommunicator ac=null;
					if(StringUtil.isNotEmpty(assetMeter.getCommunicatorId())){
						 ac=assetCommunicatorService.getEntity(assetMeter.getCommunicatorId());
					}
					Integer comType=Integer.parseInt(assetMeter.getComType());
					j.put("commnuicator", ac);
					//meterType 1 GPRS,2 普通表
					if((comType>=100&&comType<=199)||(ac!=null&&ac.getDeviceType()==201)){
						if(deviceType.equals("3")){
							j.setErrorMsg(MutiLangUtil.doMutiLang("addAssetGPRSMeter.devTypeMis"));
						}
					}else{
						if(deviceType.equals("1")){
							j.setErrorMsg(MutiLangUtil.doMutiLang("addAssetGPRSMeter.devTypeMis"));
						}
					}
					id=assetMeter.getId();
				}else if(deviceType.equals("2")){
					AssetCommunicator entity=new AssetCommunicator();
					entity.setSn(deviceSn);
					AssetCommunicator assetCommunicator = assetCommunicatorService.get(entity);
					if(assetCommunicator!=null){
						id=assetCommunicator.getId();
					}
				}
				if(StringUtil.isEmpty(id)){
					j.setErrorMsg(MutiLangUtil.doMutiLang("deviceList.noDeviceFound"));
				}else{
					j.setObj(id);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}else{
			j.setErrorMsg(MutiLangUtil.doMutiLang("deviceList.noDeviceFound"));
		}
		return j;
	}

	/**
	 * 精确查找设备
	 * @param deviceSn
	 * @param deviceType
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "getAssetMeter")
	@ResponseBody
	public AjaxJson getAssetMeter(String deviceSn,
			HttpServletRequest request, Model model) {
		AjaxJson j = new AjaxJson();
		try {
			AssetMeter entity=new AssetMeter();
			entity.setSn(deviceSn);
			 AssetMeter assetMeter = assetMeterService.get(entity);
			 if(assetMeter==null){
				 j.setErrorMsg(MutiLangUtil.doMutiLang("deviceList.noDeviceFound"));
				 return j;
			 }
			j.put("meter", assetMeter);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return j;
	}
	
	
	
	@RequestMapping(value = "findAssetCustomerBySn")
    @ResponseBody
    public AjaxJson findAssetCustomerById(HttpServletRequest request) {
        String sn = request.getParameter("sn");
        AjaxJson j = new AjaxJson();
        try {
        	JqGridSearchTo jqGridSearchTo = new JqGridSearchTo();
        	jqGridSearchTo.put("sn", sn);
        	
        	List<AssetMeter> list = assetMeterService.getMeter4Template(jqGridSearchTo);
        	if (list == null || list.size() == 0) {
        		j.setSuccess(false);
        		j.setErrorMsg("Can't find Meter by sn.");
        	} else {
        		AssetMeter meter = list.get(0);
        		AssetMeter m = assetMeterService.getEntity(meter.getId());
        		meter.setMeasurementGroupId(m.getMeasurementGroupId());
        		meter.setTouGroupId(m.getTouGroupId());
        		meter.setStepTariffId(m.getStepTariffId());
        		meter.setFriendlyId(m.getFriendlyId());
        		meter.setScheduleSchemeId(m.getScheduleSchemeId());
        		meter.setLimiterGroupId(m.getLimiterGroupId());
        		j.setObj(list.get(0));
        		
        		String scheduleSchemeId = meter.getScheduleSchemeId();
				AssetScheduleScheme assetSchedule =assetScheduleSchemeService.getEntity(scheduleSchemeId);
				if(assetSchedule!=null) {
					meter.setScheduleSchemeName(assetSchedule.getName());
				}
        		
        		List<AssetMeterGroup> meterGroupList=assetMeterGroupService.getAllList();
				Map<String,AssetMeterGroup> map = Maps.newHashMap();
				if(meterGroupList!=null) {
					for(AssetMeterGroup meterGroup:meterGroupList) {
						map.put(meterGroup.getId(), meterGroup);
					}
				}
				//分组信息
				meter.setMeasurementGroupName(map.get(meter.getMeasurementGroupId())!=null?map.get(meter.getMeasurementGroupId()).getName():"");
				meter.setTouGroupName(map.get(meter.getTouGroupId())!=null?map.get(meter.getTouGroupId()).getName():"");
				meter.setLimiterGroupName(map.get(meter.getLimiterGroupId())!=null?map.get(meter.getLimiterGroupId()).getName():"");
				meter.setStepTariffName(map.get(meter.getStepTariffId())!=null?map.get(meter.getStepTariffId()).getName():"");
        	}
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("assetCustomer.abnormalOperation"));
        }
        return j;
    }
	
	public void deletePpmAssetMeter(String meterId){
		try {
			this.assetMeterService.deletePpmAssetMeter(meterId);
		}catch (Exception e) {
		}
	}
	private AssetMeter getMeterDetail(String sn,String type) {
		
		String mode = ResourceUtil.getSessionattachmenttitle("meter.grps.mode");
		
		JqGridSearchTo jqGridSearchTo = new JqGridSearchTo();
    	jqGridSearchTo.put("sn", sn);
    	
    	AssetMeter meter = null;
    	
    	List<AssetMeter> list = null;
    	if ("PLC".equals(type)) {
    		list = assetMeterService.getForJqGridPLC(sn);
    	} else if ("GPRS".equals(type)){
    		if ("2".equals(mode)) {
    			list = assetMeterService.getForJqGridPLC(sn);
    		} else {
    			list = assetMeterService.getMeter4Template(jqGridSearchTo);
    		}
    	}
    	if (list == null || list.size() == 0) {
    		return meter;
    	} else {
    		meter = list.get(0);
    		
    		AssetMeter m = null;
    		if ("PLC".equals(type)) {
    			m = assetMeterService.getEntityPLCExtend(meter.getId());
        	} else if ("GPRS".equals(type)){
        		if ("2".equals(mode)) {
        			m = assetMeterService.getEntityPLCExtend(meter.getId());
        		} else {
        			m = assetMeterService.getEntity(meter.getId());
        		}
        	}
    		meter.setMeasurementGroupId(m.getMeasurementGroupId());
    		meter.setTouGroupId(m.getTouGroupId());
    		meter.setStepTariffId(m.getStepTariffId());
    		meter.setFriendlyId(m.getFriendlyId());
    		meter.setScheduleSchemeId(m.getScheduleSchemeId());
    		meter.setLimiterGroupId(m.getLimiterGroupId());
    		
    		String scheduleSchemeId = meter.getScheduleSchemeId();
			AssetScheduleScheme assetSchedule =assetScheduleSchemeService.getEntity(scheduleSchemeId);
			if(assetSchedule!=null) {
				meter.setScheduleSchemeName(assetSchedule.getName());
			}
    		
    		List<AssetMeterGroup> meterGroupList=assetMeterGroupService.getAllList();
			Map<String,AssetMeterGroup> map = Maps.newHashMap();
			if(meterGroupList!=null) {
				for(AssetMeterGroup meterGroup:meterGroupList) {
					map.put(meterGroup.getId(), meterGroup);
				}
			}
			//分组信息
			meter.setMeasurementGroupName(map.get(meter.getMeasurementGroupId())!=null?map.get(meter.getMeasurementGroupId()).getName():"");
			meter.setTouGroupName(map.get(meter.getTouGroupId())!=null?map.get(meter.getTouGroupId()).getName():"");
			meter.setLimiterGroupName(map.get(meter.getLimiterGroupId())!=null?map.get(meter.getLimiterGroupId()).getName():"");
			meter.setStepTariffName(map.get(meter.getStepTariffId())!=null?map.get(meter.getStepTariffId()).getName():"");
    	}
    	
    	return meter;
	}
	
	
	/**
	 * Profiles Read
	 * @param listStr
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "readSchedulerJob")
	@ResponseBody
	public JqGridResponseTo readSchedulerJob(JqGridSearchTo jqGridSearchTo, HttpServletRequest request) {
		TagUtil.setFieldValue(jqGridSearchTo, request); // 取传递的参数到JqGridSearchTo
		JqGridResponseTo j = null;
		try {
			List<OnDemandReadSchedulerJob> jobList = new ArrayList<OnDemandReadSchedulerJob>();
			String reqSn = request.getParameter("sn");
			String reqProfileId = request.getParameter("profileId");
			String reqStartTime = request.getParameter("startTime");
			String reqEndTime = request.getParameter("endTime");
			OnDemandReadSchedulerJob schedulerJob = getSchedulerJob(reqSn,reqProfileId,reqStartTime,reqEndTime);
			
			JSONObject o = new JSONObject();

			Map<String, Object> odrData = new HashMap<String, Object>();
			String basePath = ResourceUtil.getUciBasePath(request);
			SysUser su = TokenManager.getToken();
			MeterReadScheduleRequestMessageType mr = new MeterReadScheduleRequestMessageType();
			HeaderType ht = new HeaderType();
			Date date = new Date();
			ht.setVerb("get");
			ht.setNoun("MeterReadings");
			ht.setTimestamp(DateUtils.dateToXmlDate(date));
			ht.setSource("ClouESP HES");
			ht.setMessageID(DateUtils.formatDate("yyyyMMdd") + su.getId() + "profiles");
			ht.setAsyncReplyFlag(true);
			ht.setReplyAddress(basePath + "/interfaces/ReplyMeterReadings?wsdl");
			ht.setAckRequired(true);
			UserType u = new UserType();
			u.setOrganization(su.getOrgId());
			u.setUserID(su.getId());
			ht.setUser(u);
			mr.setHeader(ht);
			MeterReadSchedule meterReadSchedule = new MeterReadSchedule();
			if(schedulerJob!=null){
				String profileId = schedulerJob.getId();
				String profileName=schedulerJob.getProfileName();
				String sn = schedulerJob.getSn();
				String startTime = schedulerJob.getStartTime();
				String endTime = schedulerJob.getEndTime();
				String communication = schedulerJob.getCommunication();
				ch.iec.tc57._2011.meterreadschedule_.ReadingType readingType = new ch.iec.tc57._2011.meterreadschedule_.ReadingType();
				ch.iec.tc57._2011.meterreadschedule_.ReadingType.Names n = new ch.iec.tc57._2011.meterreadschedule_.ReadingType.Names();
				ch.iec.tc57._2011.meterreadschedule_.ReadingType.Names.NameType nt = new ch.iec.tc57._2011.meterreadschedule_.ReadingType.Names.NameType();
				n.setName(profileId);
				nt.setName("ReadingType");
				n.setNameType(nt);
				readingType.getNames().add(n);
				OnDemandReadSchedulerJob odr = new OnDemandReadSchedulerJob();
				odr.setId(profileId + sn + startTime + endTime);
				odr.setSn(sn);
				odr.setRequestTime(date);
				odr.setStatus("1");
				odr.setCommunication(communication);
				odr.setDataChannel("");
				odr.setProfile(profileId);
				odr.setProfileName(profileName);
				jobList.add(odr);
				odrData.put(odr.getId(), odr);
				meterReadSchedule.getReadingType().add(readingType);
				//时间
				TimeSchedule timeSchedule = new TimeSchedule();
				TimeSchedule.ScheduleInterval scheduleInterval = new TimeSchedule.ScheduleInterval();
				scheduleInterval.setStart(DateUtils.dateToXmlDate(DateUtils.parseDate(startTime, "MM/dd/yyyy HH:mm:ss")));
				scheduleInterval.setEnd(DateUtils.dateToXmlDate(DateUtils.parseDate(endTime, "MM/dd/yyyy HH:mm:ss")));
				timeSchedule.setScheduleInterval(scheduleInterval);
				meterReadSchedule.getTimeSchedule().add(timeSchedule);
				
				ch.iec.tc57._2011.meterreadschedule_.EndDevice ed = new ch.iec.tc57._2011.meterreadschedule_.EndDevice();
				ed.setMRID(sn);
				meterReadSchedule.getEndDevice().add(ed);
			}
			JedisUtils.delObject(DateUtils.formatDate(
					DateUtils.getDateAdd(date, 5, -1), "yyyyMMdd")
					+ su.getId());
			MeterReadSchedulePayloadType mrsp = new MeterReadSchedulePayloadType();
			mrsp.setMeterReadSchedule(meterReadSchedule);
			mr.setPayload(mrsp);
			o.put("status", true);
			try {
				MeterReadSchedulePort port = (MeterReadSchedulePort) SpringContextUtil
						.getBean("MeterReadSchedulePort");
				MeterReadScheduleResponseMessageType s = port.getMeterReadSchedule(mr);
				if (s == null || s.getReply() == null
						|| StringUtil.isEmpty(s.getReply().getResult())) {
					for (OnDemandReadSchedulerJob dr : jobList) {
						dr.setRequestTime(DateUtils.xmlDate2Date(s.getHeader()
								.getTimestamp()));
						dr.setStatus("3");
						dr.setValue("Abnormal request from UCI");
						odrData.put(dr.getId(), dr);
					}
					//o.put("status", false);
				} else {
					if (!s.getReply().getResult().toUpperCase().equals("OK")) {
						for (OnDemandReadSchedulerJob dr : jobList) {
							dr.setRequestTime(DateUtils.xmlDate2Date(s
									.getHeader().getTimestamp()));
							dr.setStatus("3");
							dr.setValue(s.getReply().getResult());
							odrData.put(dr.getId(), dr);
						}
						//o.put("status", false);
					} else {
						jobList.clear();
					}
				}
			} catch (Exception e) {
				for (OnDemandReadSchedulerJob dr : jobList) {
					dr.setStatus("3");
					dr.setValue("Abnormal request from UCI");
					odrData.put(dr.getId(), dr);
				}
				o.put("status", false);
			}
			JedisUtils.setObject(ht.getMessageID(), odrData, 0);
			JedisUtils.setObject("setReadsIsView" + ht.getMessageID(), false, 0);
			o.put("messageId", ht.getMessageID());
			PageHelper.startPage(jqGridSearchTo.getPage(),
					jqGridSearchTo.getRows());
			PageInfo<OnDemandReadSchedulerJob> pageInfo = new PageInfo<OnDemandReadSchedulerJob>();
			pageInfo.setList(jobList);
			j = JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
			o.put("messageId", ht.getMessageID());
			j.setJson(o);

		} catch (Exception e) {
			e.printStackTrace();
		}
		return j;
	}

	/**
	 * 返回抄读对象
	 * @param sn
	 * @param profileId
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public OnDemandReadSchedulerJob getSchedulerJob(String sn, String profileId,
			String startTime, String endTime) {
		try {
			OnDemandReadSchedulerJob job = new OnDemandReadSchedulerJob();
			job.setId(profileId + sn + startTime + endTime);
			job.setSn(sn);
			job.setProfile(profileId);

			// SimpleDateFormat sdf = new
			// SimpleDateFormat("MM/dd/yyyy hh:mm:ss");
			// Date startDate = sdf.parse(startTime);
			// Date endDate = sdf.parse(endTime);
			job.setStartTime(startTime);
			job.setEndTime(endTime);

			AssetMeter meter = new AssetMeter();
			meter.setSn(sn);
			meter = assetMeterService.get(meter);

			DictDeviceModel deviceModel = new DictDeviceModel();
			deviceModel.setId(meter.getModel());
			deviceModel = dictDeviceModelService.get(deviceModel);
			job.setModel(deviceModel.getName());

			DictCommunicationType cType = new DictCommunicationType();
			cType.setId(meter.getComType());
			cType = dictCommunicationTypeService.get(cType);
			job.setCommunication(cType.getName());

			DictProfile dp = new DictProfile();
			dp.setId(profileId);
			dp = dictProfileService.get(dp);
			job.setProfileName(dp.getName());
			return job;
		} catch (Exception e) {
			e.getStackTrace();
		}
		return null;
	}
	
	/**
	 * 获取设备信息
	 * @param sn
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getAssetManagement")
	@ResponseBody
	public AjaxJson getAssetManagement(String id,String searchType, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			//Meter  //Commnuicator
			j.put("searchType", searchType);
			if(searchType.equals("Meter")){
				AssetMeter am=assetMeterService.getEntity(id);
				if(am==null){
					j.setErrorMsg(MutiLangUtil.doMutiLang("deviceList.meterNullMsg"));
					return j;
				}
				j.put("meter", am);
				AssetCommunicator ac=null;
				if(StringUtil.isNotEmpty(am.getCommunicatorId())){
					 ac=assetCommunicatorService.getEntity(am.getCommunicatorId());
				}
				Integer comType=Integer.parseInt(am.getComType());
				j.put("commnuicator", ac);
				//meterType 1 GPRS,2 通讯器,3 普通表
				if((comType>=100&&comType<=199)||(ac!=null&&ac.getDeviceType()==201)){
					j.put("meterType", "1");
					AssetRouter entity=new AssetRouter();
					entity.setCommunicatorId(ac.getId());
					AssetRouter assetRouter = assetRouterService.get(entity);
					j.put("assetRouter", assetRouter);
				}else{
					AssetMeter amt=new AssetMeter();
					amt.setCommunicatorId(ac.getId());
					List<AssetMeter> list = assetMeterService.getList(amt);
					j.put("meterList", list);
					j.put("meterType", "3");
				}
			}else if(searchType.equals("Commnuicator")){
				AssetCommunicator ac=assetCommunicatorService.getEntity(id);
				if(ac==null){
					j.setErrorMsg(MutiLangUtil.doMutiLang("deviceList.commnuicatorNullMsg"));
					return j;
				}
				j.put("commnuicator", ac);
				AssetMeter am=new AssetMeter();
				am.setCommunicatorId(ac.getId());
				List<AssetMeter> list = assetMeterService.getList(am);
				j.put("meterList", list);
				AssetRouter entity=new AssetRouter();
				entity.setCommunicatorId(ac.getId());
				AssetRouter assetRouter = assetRouterService.get(entity);
				j.put("assetRouter", assetRouter);
			}
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg("Abnormal operation!");
		}
		return j;
	}
	
	/**
	 * 获取设备信息
	 * @param sn
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getAssetManagementName")
	@ResponseBody
	public AjaxJson getAssetManagementName(String id,String searchType, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		String formatter=ResourceUtil.getSessionattachmenttitle("local.date.time.formatter");
		try {
			//Meter  //Commnuicator
			j.put("searchType", searchType);
			
			List<SysService> sslist = sysServiceService.getAllList();
			Map<String,SysService>  sysServiceMap = Maps.newHashMap();
			if(sslist!=null) {
				for(SysService service:sslist) {
					sysServiceMap.put(service.getId(), service);
				}
			}
			
			if(searchType.equals("Meter")){
				AssetMeter am=assetMeterService.getEntity(id);
				if(am==null){
					j.setErrorMsg(MutiLangUtil.doMutiLang("deviceList.meterNullMsg"));
					return j;
				}
				if(am.getCreateTime()!=null) {
					am.setCreateTimeStr(DateUtils.date2Str(am.getCreateTime(), new SimpleDateFormat(formatter)));
				}
				Integer comType=Integer.parseInt(am.getComType());
				//厂家，型号， 通讯类型
				DictManufacturer man=dictManufacturerService.getEntity(am.getManufacturer());
				DictDeviceModel model=dictDeviceModelService.getEntity(am.getModel());
				DictCommunicationType commType=dictCommunicationTypeService.getEntity(am.getComType());
				
				am.setModelName(model.getName());
				am.setManufacturerName(man.getName());
				am.setComType(commType.getName());
				String scheduleSchemeId = am.getScheduleSchemeId();
				AssetScheduleScheme assetSchedule =assetScheduleSchemeService.getEntity(scheduleSchemeId);
				if(assetSchedule!=null) {
					am.setScheduleSchemeId(assetSchedule.getName());
				}
				List<AssetMeterGroup> meterGroupList=assetMeterGroupService.getAllList();
				Map<String,AssetMeterGroup> map = Maps.newHashMap();
				if(meterGroupList!=null) {
					for(AssetMeterGroup meterGroup:meterGroupList) {
						map.put(meterGroup.getId(), meterGroup);
					}
				}
				
				
				
				//分组信息
				am.setMeasurementGroupId(map.get(am.getMeasurementGroupId())!=null?map.get(am.getMeasurementGroupId()).getName():"");
				am.setTouGroupId(map.get(am.getTouGroupId())!=null?map.get(am.getTouGroupId()).getName():"");
				am.setLimiterGroupId(map.get(am.getLimiterGroupId())!=null?map.get(am.getLimiterGroupId()).getName():"");
				am.setStepTariffId(map.get(am.getStepTariffId())!=null?map.get(am.getStepTariffId()).getName():"");
				
				String orgId = am.getOrgId();
				if(StringUtils.isNotEmpty(orgId)) {
					SysOrg org=sysOrgService.getEntity(orgId);
					am.setOrgId(org.getName());
				}
				
				j.put("meter", am);
				AssetCommunicator ac=null;
				if(StringUtil.isNotEmpty(am.getCommunicatorId())){
					 ac=assetCommunicatorService.getEntity(am.getCommunicatorId());
				}
				
				j.put("commnuicator", ac);
				// 1 GPRS,2,Communicator,3 普通表
				if((comType>=100&&comType<=199)||(ac!=null&&ac.getDeviceType()==201)){
					j.put("meterType", "1");
					AssetRouter entity=new AssetRouter();
					entity.setCommunicatorId(ac.getId());
					AssetRouter assetRouter = assetRouterService.get(entity);
					//转换id为name
					if(assetRouter!=null) {
						assetRouter.setChannelId(sysServiceMap.get(assetRouter.getChannelId()).getIntroduction());
						assetRouter.setScheduleId(sysServiceMap.get(assetRouter.getScheduleId()).getIntroduction());
					}
					
					j.put("assetRouter", assetRouter);
				}else{
					j.put("meterType", "3");
				}
			}else if(searchType.equals("Commnuicator")){
				j.put("meterType", "2");
				AssetCommunicator ac=assetCommunicatorService.getEntity(id);
				if(ac==null){
					j.setErrorMsg(MutiLangUtil.doMutiLang("deviceList.commnuicatorNullMsg"));
					return j;
				}
				if(ac.getCreateTime()!=null) {
					ac.setCreateTimeStr(DateUtils.date2Str(ac.getCreateTime(), new SimpleDateFormat(formatter)));
				}
				//厂家，型号， 通讯类型
				DictManufacturer man=dictManufacturerService.getEntity(ac.getManufacturer());
				DictDeviceModel model=dictDeviceModelService.getEntity(ac.getModel());
				DictCommunicationType commType=dictCommunicationTypeService.getEntity(ac.getComType());
				
				ac.setModelName(model.getName());
				ac.setManufacturerName(man.getName());
				ac.setComType(commType.getName());
				
				
				String orgId=ac.getOrgId();
				if(StringUtils.isNotEmpty(orgId)) {
					SysOrg org=sysOrgService.getEntity(orgId);
					ac.setOrgId(org.getName());
				}
				
				j.put("commnuicator", ac);
				AssetMeter am=new AssetMeter();
				am.setCommunicatorId(ac.getId());
				List<AssetMeter> list = assetMeterService.getList(am);
				j.put("meterList", list);
				AssetRouter entity=new AssetRouter();
				entity.setCommunicatorId(ac.getId());
				AssetRouter assetRouter = assetRouterService.get(entity);
				if(assetRouter!=null) {
				assetRouter.setChannelId(sysServiceMap.get(assetRouter.getChannelId()).getIntroduction());
				assetRouter.setScheduleId(sysServiceMap.get(assetRouter.getScheduleId()).getIntroduction());
				}
				j.put("assetRouter", assetRouter);
			} else if(searchType.equals("Line")){
				//TODO 
				AssetLine assetLine = assetLineManagementService.getEntity(id);
				
				if (null == assetLine) {
					j.setErrorMsg(MutiLangUtil.doMutiLang("lineManagementList.nolineFound"));
					return j;
				}
				
				SysOrg sysOrg =  sysOrgService.getEntity(assetLine.getOrgId());
				assetLine.setOrgName(sysOrg.getName());
				j.put("assetLine", assetLine);
			} else if (searchType.equals("Transformer")) {
				AssetTransformer transformer = assetTransformerService.getEntity(id);
				
				if (null == transformer) {
					j.setErrorMsg(MutiLangUtil.doMutiLang("lineManagementList.nolineFound"));
					return j;
				}
				
				SysOrg sysOrg =  sysOrgService.getEntity(transformer.getOrgId());
				transformer.setOrgName(sysOrg.getName());
				
				j.put("transformer", transformer);
			} else if (searchType.equals("Customer")) {
				AssetCustomer customer =  assetCustomerService.getEntity(id);
				
				if (null == customer) {
					j.setErrorMsg(MutiLangUtil.doMutiLang("lineManagementList.nolineFound"));
					return j;
				}
				if(StringUtils.isNotEmpty(customer.getMeterId())) {
					AssetMeter am = this.assetMeterService.getEntity(customer.getMeterId());
					if(am!=null) {
						AssetCommunicator ac = null;
						if(StringUtils.isNotEmpty(am.getCommunicatorId())) {
							ac=this.assetCommunicatorService.getEntity(am.getCommunicatorId());
							j.put("commnuicator", ac);
						}
						Integer comType=Integer.parseInt(am.getComType());
						//厂家，型号， 通讯类型
						DictManufacturer man=dictManufacturerService.getEntity(am.getManufacturer());
						DictDeviceModel model=dictDeviceModelService.getEntity(am.getModel());
						DictCommunicationType commType=dictCommunicationTypeService.getEntity(am.getComType());
						am.setModelName(model.getName());
						am.setManufacturerName(man.getName());
						am.setComType(commType.getName());
						String scheduleSchemeId = am.getScheduleSchemeId();
						AssetScheduleScheme assetSchedule =assetScheduleSchemeService.getEntity(scheduleSchemeId);
						if(assetSchedule!=null) {
							am.setScheduleSchemeId(assetSchedule.getName());
						}
						List<AssetMeterGroup> meterGroupList=assetMeterGroupService.getAllList();
						Map<String,AssetMeterGroup> map = Maps.newHashMap();
						if(meterGroupList!=null) {
							for(AssetMeterGroup meterGroup:meterGroupList) {
								map.put(meterGroup.getId(), meterGroup);
							}
						}
						am.setMeasurementGroupId(map.get(am.getMeasurementGroupId())!=null?map.get(am.getMeasurementGroupId()).getName():"");
						am.setTouGroupId(map.get(am.getTouGroupId())!=null?map.get(am.getTouGroupId()).getName():"");
						am.setLimiterGroupId(map.get(am.getLimiterGroupId())!=null?map.get(am.getLimiterGroupId()).getName():"");
						am.setStepTariffId(map.get(am.getStepTariffId())!=null?map.get(am.getStepTariffId()).getName():"");
						
						String orgId = am.getOrgId();
						if(StringUtils.isNotEmpty(orgId)) {
							SysOrg org=sysOrgService.getEntity(orgId);
							am.setOrgId(org.getName());
						}
						// 1 GPRS,2,Communicator,3 普通表
						if((comType>=100&&comType<=199)||(ac!=null&&ac.getDeviceType()==201)){
							j.put("meterType", "1");
							AssetRouter entity=new AssetRouter();
							entity.setCommunicatorId(ac.getId());
							AssetRouter assetRouter = assetRouterService.get(entity);
							//转换id为name
							if(assetRouter!=null) {
								assetRouter.setChannelId(sysServiceMap.get(assetRouter.getChannelId()).getIntroduction());
								assetRouter.setScheduleId(sysServiceMap.get(assetRouter.getScheduleId()).getIntroduction());
							}
							
							j.put("assetRouter", assetRouter);
						}else{
							j.put("meterType", "3");
						}
						j.put("meter", am);
					}
				}
				j.put("customer", customer);
			}
			
			//Line // Transformer Customer
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg("Abnormal operation!");
		}
		return j;
	}

	/**
	 * 双击获取profile
	 * @param assetMeter
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getMeasurementProfile")
	@ResponseBody
	public AjaxJson getMeasurementProfile(String sn, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		AssetMeter meter = new AssetMeter();
		AssetMeasurementProfile profile = new AssetMeasurementProfile();
		try {
			meter.setSn(sn);
			meter = assetMeterService.get(meter);
			// 获取所有的profile类型
			AssetMeterGroupMap amgm = assetMeterGroupMapService.getEntity(meter.getId());
			profile.setProfileType("1");
			profile.setMgId(amgm.getMeasurementGroupId());
			List<AssetMeasurementProfile> list = assetMeasurementProfileService.getList(profile);
			j.setObj(list);
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg("Abnormal operation!");
		}
		return j;
	}
	
	/**
	 * 通过集中器ID获取meter list
	 * @param assetMeter
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getMeterListByCommnuicatorID")
	@ResponseBody
	public AjaxJson getMeterListByCommnuicatorID(AssetMeter meter, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			// 获取所有的profile类型
			List<AssetMeter> meterList = assetMeterService.getList(meter);
			if(meterList.size() > 0){
				j.setObj(meterList);
			}
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
		}
		return j;
	}
	
	/**
     * 上传和校验Excel文件信息
     * @Description 
     * @param multfile
     * @param request
     * @param response
     * @return AjaxJson
	 * @throws Exception
     * <AUTHOR> 
     * @Time 2018年8月16日 下午4:02:36
     */
    @RequestMapping("uploadImportNewMeterExcelAndCheckInfo")
    @ResponseBody
    public AjaxJson uploadImportNewMeterExcelAndCheckInfo(@RequestParam("excelTemplateFile") MultipartFile multfile, 
    		HttpServletRequest request, HttpServletResponse response){
    	AjaxJson json = new AjaxJson();
        String fileName = multfile.getOriginalFilename();				// 获取文件名
        String prefix = fileName.substring(fileName.lastIndexOf("."));	// 获取文件后缀
        int repeatCount = 0; 
        List<AssetMeterTemplate> meterList = new ArrayList<>();
        SysUser su = TokenManager.getToken();
        List<DictCommunicationType> commTypeList = dictCommunicationTypeService.getAllList();
        List<DictDeviceModel> deviceModelList = dictDeviceModelService.getAllList();
        //如果后缀不是xls或者xlsx，则返回失败，不是一个正确的文件
        if(".xls".equals(prefix) || ".xlsx".equals(prefix)){
        	File excelFile;
        	
        	try {
	        	excelFile = File.createTempFile(UUIDGenerator.generate(), prefix);
				multfile.transferTo(excelFile);
				
				AssetMeterTemplate entity = new AssetMeterTemplate();
				ExcelUtils<AssetMeterTemplate> utils = new ExcelUtils<AssetMeterTemplate>(entity);
				ExcelDataFormatter edf = new ExcelDataFormatter();
				meterList = utils.readFromFile(edf, excelFile);
				
				int batchCount = 20;
				if (meterList.size() > 10000) {
					batchCount = 100;
				}
				
				if (meterList.size() > 0) {
					// 每批次的记录数
					int num1 = 0;
					if (meterList.size() > batchCount) {
						num1 = meterList.size() / batchCount;
					} else {
						num1 = meterList.size();
					}
					
					// 当前批次第几个
					int num2 = 0; // 代表导入进度
					
					List<AssetMeter> batchMeterList = new ArrayList<AssetMeter>();
					for (int i = 0 ; i < meterList.size(); i++) {
						AssetMeterTemplate iag = meterList.get(i);
						int line = i + 2;
						
						String errorMsg = validateEmpty(iag);
						if (null != errorMsg) {
		    				json.setSuccess(false);
			    			json.setMsg("At line: " + line + " ," + errorMsg);
			    			this.deleteFile(excelFile);
			    			return json;
						}
						
						if (!comTypeValidate(commTypeList, iag)) {
		    				json.setSuccess(false);
			    			json.setMsg("At line: " + line +" ,Communication Type [" + iag.getComType() + "]have not exist,please check it.");
			    			this.deleteFile(excelFile);
			    			return json;
						}
						
						if (!deviceModelValidate(deviceModelList, iag)) {
		    				json.setSuccess(false);
			    			json.setMsg("At line: " + line +" ,Device Type [" + iag.getDeviceType() + "]have not exist,please check it.");
			    			this.deleteFile(excelFile);
			    			return json;
						}
						
						if (StringUtils.isNotEmpty(iag.getKeyMeter())) {
							if (!keyMeterValidate(iag)) {
			    				json.setSuccess(false);
				    			json.setMsg("At line: " + line +" ,Key Meter only be [Yes] or [No]");
				    			this.deleteFile(excelFile);
				    			return json;
							}
						}
						
						AssetMeter m = new AssetMeter();
						m.setSn(iag.getSerialNumber());
						m.setMac(iag.getMac());
						
						batchMeterList.add(m);
						
						if (meterList.size() > batchCount) {
							// 将一批次的记录处理
							if ((i+1) % num1 == 0 || (i == meterList.size() - 1) ) {
								++num2;
								JedisUtils.set(su.getId() + "checkMeterProgress",String.valueOf(num2 * (100 / batchCount)), 3600);
								
								int count1 = assetMeterService.batchGetList(batchMeterList);
								int count2 = assetMeterService.batchGetListPrepare(batchMeterList);
								repeatCount += count1;
								repeatCount += count2;
								batchMeterList.clear();
							}
						} else {
							++num2;
							JedisUtils.set(su.getId() + "checkMeterProgress",String.valueOf(num2 * (100 / batchCount)), 3600);
							int count1 = assetMeterService.batchGetList(batchMeterList);
							int count2 = assetMeterService.batchGetListPrepare(batchMeterList);
							repeatCount += count1;
							repeatCount += count2;
							batchMeterList.clear();
						}
						
						if (repeatCount > 0) {
							break;
						}
					}
				}
				
				if(meterList.size() > 0){
	    			if (repeatCount > 0) {
	    				json.put("repeatCount", repeatCount);
	    				json.setSuccess(false);
		    			json.setMsg("This Excel file have repeat records,please check it.");
	    			} else {
	    				//检验之后的电表数据，缓存入Redis中
	    				String uuid = UUIDGenerator.generate();
	    				JedisUtils.setObject(uuid, meterList, 24 * 60 * 60);
	    				json.put("uuid", uuid);
	    				json.setMsg(MutiLangUtil.doMutiLang("importGprsMeter.verifySuccess"));
	    			}
	    		}else{
	    			json.setSuccess(false);
	    			json.setMsg(MutiLangUtil.doMutiLang("importGprsMeter.noDataInExcel"));
	    		}
	    		//删除临时file文件
	    		this.deleteFile(excelFile);
        	} catch (Exception e) {
				json.setSuccess(false);
				json.setMsg(MutiLangUtil.doMutiLang("importGprsMeter.verifyFail"));
				e.printStackTrace();
			}
        } else{
        	json.setSuccess(false);
        	json.setMsg(MutiLangUtil.doMutiLang("importGprsMeter.selectFile"));
        }
    	return json;
    }

	private boolean keyMeterValidate(AssetMeterTemplate iag) {
		boolean isValid = true;
		if ("Yes".equals(iag.getKeyMeter())) {
			iag.setKeyMeterId(1);
		} else if ("No".equals(iag.getKeyMeter())) {
			iag.setKeyMeterId(0);
		} else {
			isValid = false;
		}
		return isValid;
	}

	private String validateEmpty(AssetMeterTemplate iag) {
		String errorMsg = null;
		String field = null;
		if (StringUtils.isEmpty(iag.getSerialNumber())) {
			field = "Serial Number";
		} else if (StringUtils.isEmpty(iag.getFirmwareVersion())) {
			field = "Meter FW";
		} else if(StringUtils.isEmpty(iag.getMac())) {
			field = "Logical Address";
		} else if(StringUtils.isEmpty(iag.getDeviceType())) {
			field = "Device Type";
		} else if(StringUtils.isEmpty(iag.getComType())) {
			field = "Communication Type";
		}
		
		if (field != null) {
			errorMsg = field + " is empty,Please check it.";
		}
		
		return errorMsg;
	}
	
//	private boolean deviceAuthValidate(AssetMeterTemplate iag) {
//		boolean isDeviceModelValidate = true;
//		
//		if ("NO_SECURITY".equals(iag.getAuthType())) {
//			iag.setAuthTypeId("0");
//		} else if ("LLS".equals(iag.getAuthType())) {
//			iag.setAuthTypeId("1");
//		} else if ("HLS_2".equals(iag.getAuthType())) {
//			iag.setAuthTypeId("2");
//		} else if ("HLS_MD5".equals(iag.getAuthType())) {
//			iag.setAuthTypeId("3");
//		} else if ("HLS_SHA1".equals(iag.getAuthType())) {
//			iag.setAuthTypeId("4");
//		} else if ("HLS_GMAC".equals(iag.getAuthType())) {
//			iag.setAuthTypeId("5");
//		} else {
//			isDeviceModelValidate = false;
//		}
//		return isDeviceModelValidate;
//	}

	private boolean deviceModelValidate(List<DictDeviceModel> deviceModelList, AssetMeterTemplate iag) {
		boolean isDeviceModelValidate = false;
		for (DictDeviceModel deviceModel : deviceModelList) {
			String deviceModelName = deviceModel.getName();
			if (deviceModelName != null) {
				if (deviceModelName.equals(iag.getDeviceType())) {
					iag.setDeviceTypeId(deviceModel.getId());
					isDeviceModelValidate = true;
					// 查找设备对应都厂商
					iag.setManufacturerId(deviceModel.getManufacturerId());
				}
			}
		}
		return isDeviceModelValidate;
	}

	private boolean comTypeValidate(List<DictCommunicationType> commTypeList, AssetMeterTemplate iag) {
		boolean comTypeValidate = false;
		for (DictCommunicationType commType : commTypeList) {
			String commTypeName = commType.getName();
			if (commTypeName != null) {
				if (commTypeName.equals(iag.getComType())) {
					iag.setComTypeId(commType.getId());
					comTypeValidate = true;
				}
			}
		}
		return comTypeValidate;
	}

	/**
     * 上传和校验Excel文件信息
     * @Description 
     * @param multfile
     * @param request
     * @param response
     * @return AjaxJson
	 * @throws Exception 
     * <AUTHOR> 
     * @Time 2018年8月16日 下午4:02:36
     */
    @RequestMapping("uploadImportMeterExcelAndCheckInfo")
    @ResponseBody
    public AjaxJson uploadImportMeterExcelAndCheckInfo(@RequestParam("excelTemplateFile") MultipartFile multfile, 
    		HttpServletRequest request, HttpServletResponse response){
    	AjaxJson json = new AjaxJson();
        String fileName = multfile.getOriginalFilename();				// 获取文件名
        String prefix = fileName.substring(fileName.lastIndexOf("."));	// 获取文件后缀
        int repeatCount = 0;
        List<ImportAssetGprsMeter> meterList = new ArrayList<>();
        SysUser su = TokenManager.getToken();
        
        //如果后缀不是xls或者xlsx，则返回失败，不是一个正确的文件
        if(".xls".equals(prefix) || ".xlsx".equals(prefix)){
    		// 用uuid作为文件名，防止生成的临时文件重复
    		File excelFile;
			try {
				excelFile = File.createTempFile(UUIDGenerator.generate(), prefix);
				multfile.transferTo(excelFile);
				// MultipartFile to File
				ImportAssetGprsMeter entity = new ImportAssetGprsMeter();
				ExcelUtils<ImportAssetGprsMeter> utils = new ExcelUtils<ImportAssetGprsMeter>(entity);
				ExcelDataFormatter edf = new ExcelDataFormatter();
				meterList = utils.readFromFile(edf, excelFile);
				
				// 校验是否有重复的记录
				int batchCount = 20;
				if (meterList.size() > 10000) {
					batchCount = 100;
				}
				
				if (meterList.size() > 0) {
					// 每批次的记录数
					int num1 = 0;
					if (meterList.size() > batchCount) {
						num1 = meterList.size() / batchCount;
					} else {
						num1 = meterList.size();
					}
					
					// 当前批次第几个
					int num2 = 0; // 代表导入进度
					
					List<AssetMeter> batchMeterList = new ArrayList<AssetMeter>();
					for (int i = 0 ; i < meterList.size(); i++) {
						ImportAssetGprsMeter iag = meterList.get(i);
						AssetMeter m = new AssetMeter();
						m.setSn(iag.getSerial_Number());
						m.setMac(iag.getLogical_address());
						
						batchMeterList.add(m);
						
						if (meterList.size() > batchCount) {
							// 将一批次的记录处理
							if ((i+1) % num1 == 0 || (i == meterList.size() - 1) ) {
								++num2;
								JedisUtils.set(su.getId() + "checkMeterProgress",String.valueOf(num2 * (100 / batchCount)), 3600);
								
								int count1 = assetMeterService.batchGetList(batchMeterList);
								int count2 = assetMeterService.batchGetListPrepare(batchMeterList);
								repeatCount += count1;
								repeatCount += count2;
								batchMeterList.clear();
							}
						} else {
							++num2;
							JedisUtils.set(su.getId() + "checkMeterProgress",String.valueOf(num2 * (100 / batchCount)), 3600);
							int count1 = assetMeterService.batchGetList(batchMeterList);
							int count2 = assetMeterService.batchGetListPrepare(batchMeterList);
							repeatCount += count1;
							repeatCount += count2;
							batchMeterList.clear();
						}
						
						if (repeatCount > 0) {
							break;
						}
					}
				}
				
	    		if(meterList.size() > 0){
	    			if (repeatCount > 0) {
	    				json.put("repeatCount", repeatCount);
	    				json.setSuccess(false);
		    			json.setMsg("This Excel file have repeat records,please check it.");
	    			} else {
	    				//检验之后的电表数据，缓存入Redis中
	    				String uuid = UUIDGenerator.generate();
	    				JedisUtils.setObject(uuid, meterList, 24 * 60 * 60);
	    				json.put("uuid", uuid);
	    				json.setMsg(MutiLangUtil.doMutiLang("importGprsMeter.verifySuccess"));
	    			}
	    		}else{
	    			json.setSuccess(false);
	    			json.setMsg(MutiLangUtil.doMutiLang("importGprsMeter.noDataInExcel"));
	    		}
	    		//删除临时file文件
	    		this.deleteFile(excelFile);
	    		
			} catch (Exception e) {
				json.setSuccess(false);
				json.setMsg(MutiLangUtil.doMutiLang("importGprsMeter.verifyFail"));
				e.printStackTrace();
			}
        }else{
        	json.setSuccess(false);
        	json.setMsg(MutiLangUtil.doMutiLang("importGprsMeter.selectFile"));
        }
    	return json;
    }

	/**
	 * 上传和校验Excel文件信息
	 * 
	 * @Description
	 * @param multfile
	 * @param request
	 * @param response
	 * @return AjaxJson
	 * @throws Exception
	 * <AUTHOR>
	 * @Time 2018年8月16日 下午4:02:36
	 */
	@RequestMapping("uploadImportCommExcelAndCheckInfo")
	@ResponseBody
	public AjaxJson uploadImportCommExcelAndCheckInfo(@RequestParam("excelTemplateFile") MultipartFile multfile,
			HttpServletRequest request, HttpServletResponse response) {
		AjaxJson json = new AjaxJson();
		String fileName = multfile.getOriginalFilename(); // 获取文件名
		String prefix = fileName.substring(fileName.lastIndexOf(".")); // 获取文件后缀

		List<ImportAssetGprsMeter> meterList = new ArrayList<>();
		SysUser su = TokenManager.getToken();

		// 如果后缀不是xls或者xlsx，则返回失败，不是一个正确的文件
		if (".xls".equals(prefix) || ".xlsx".equals(prefix)) {
			// 用uuid作为文件名，防止生成的临时文件重复
			File excelFile;
			try {
				excelFile = File.createTempFile(UUIDGenerator.generate(), prefix);
				multfile.transferTo(excelFile);
				// MultipartFile to File
				ImportAssetGprsMeter entity = new ImportAssetGprsMeter();
				ExcelUtils<ImportAssetGprsMeter> utils = new ExcelUtils<ImportAssetGprsMeter>(entity);
				ExcelDataFormatter edf = new ExcelDataFormatter();
				meterList = utils.readFromFile(edf, excelFile);

				if (meterList.size() > 0) {
					// 检验之后的电表数据，缓存入Redis中
					String uuid = UUIDGenerator.generate();
					JedisUtils.setObject(uuid, meterList, 24 * 60 * 60);
					json.put("uuid", uuid);
					json.setMsg(MutiLangUtil.doMutiLang("importGprsMeter.verifySuccess"));
				} else {
					json.setSuccess(false);
					json.setMsg(MutiLangUtil.doMutiLang("importGprsMeter.noDataInExcel"));
				}
				// 删除临时file文件
				this.deleteFile(excelFile);
			} catch (Exception e) {
				json.setSuccess(false);
				json.setMsg(MutiLangUtil.doMutiLang("importGprsMeter.verifyFail"));
				e.printStackTrace();
			}
		} else {
			json.setSuccess(false);
			json.setMsg(MutiLangUtil.doMutiLang("importGprsMeter.selectFile"));
		}
		return json;
	}
	
    /**  
     * 删除临时文件
     * @param files  
     */  
    private void deleteFile(File... files) {  
        for (File file : files) {  
            if (file.exists()) {  
                file.delete();  
            }  
        }  
    }
    
    
    /**
     * 导出模板
     * @param request
     * @param response
     * @throws IOException
     */
    @RequestMapping("downloadExcelTemplate")
	  public ResponseEntity<byte[]> downloadExcelTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException{
			String rootpath = request.getRealPath("/modules/meterTemplate/");//获取项目中的模板文件夹
			String fileName ="Meter_Batch_Template.xlsx"; 
			File file=new File(rootpath  +"/"+ fileName);  
			HttpHeaders headers = new HttpHeaders();    
			headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);  
			headers.set("Content-Disposition", "attachment; filename=\"" + fileName + "\"");  
			return new ResponseEntity<byte[]>(FileUtils.readFileToByteArray(file),headers, HttpStatus.OK);    
	  }

	  /**
		 * 导出模板
		 * 
		 * @param request
		 * @param response
		 * @throws IOException
		 */
		@RequestMapping("downloadCommunicatorExcelTemplate")
		public ResponseEntity<byte[]> downloadCommunicatorExcelTemplate(HttpServletRequest request,
				HttpServletResponse response) throws IOException {
			String rootpath = request.getRealPath("/modules/meterTemplate/");// 获取项目中的模板文件夹
			String fileName = "Communicator_Batch_Template.xlsx";
			File file = new File(rootpath + "/" + fileName);
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
			headers.set("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
			return new ResponseEntity<byte[]>(FileUtils.readFileToByteArray(file), headers, HttpStatus.OK);
		}
		
	/**
	 * 批量导入电表数据信息
	 * 
	 * @param id
	 * @return
	 */
	@Transactional
	@RequestMapping(value = "batchImportTemplateMeterData")
	@ResponseBody
	@SuppressWarnings("unchecked")
	public AjaxJson batchImportTemplateMeterData(String meterType, String ids, AssetMeter entity,
			HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			String[] importIds = ids.split(",");
			JSONArray array = new JSONArray();
			SysUser su = TokenManager.getToken();

			// 从redis中读取出之前文件导入的电表
			// 获取缓存的导入文件的信息
			List<ImportAssetGprsMeter> meterList = (List<ImportAssetGprsMeter>) JedisUtils.getObject(entity.getUuid());
			// 1. 增加批量处理
			if (meterList.size() > 0) {
				int num1 = 0;
				if (meterList.size() > 20) { // 把数据均分成二十份
					num1 = meterList.size() / 20;
				} else {
					num1 = meterList.size();
				}
				int num2 = 0; // 代表导入进度
				// 根据用户选中的id值，将这些电表插入到数据库中，同时使用entity中的属性填充字段。
				for (ImportAssetGprsMeter importAssetGprsMeter : meterList) {
					for (int i = 0; i < importIds.length; i++) {
						if (importAssetGprsMeter.getSerial_Number().equals(importIds[i])) {
							JSONObject obj = new JSONObject();
							obj.put("id", importIds[i]);
							obj.put("commAddress", importAssetGprsMeter.getComm_Address());
							// 虚拟集中器
							AssetCommunicator assetCommunicator = new AssetCommunicator();
							assetCommunicator.setSn(importAssetGprsMeter.getSerial_Number());
							assetCommunicator.setMac(importAssetGprsMeter.getLogical_address());
							assetCommunicator.setComType(entity.getComType());
							assetCommunicator.setFwVersion(
									StringUtil.isNotEmpty(entity.getFwVersion()) ? entity.getFwVersion() : "");
							assetCommunicator.setModel(entity.getModel());
							assetCommunicator.setManufacturer(entity.getManufacturer());
							assetCommunicator.setPass(
									StringUtil.isNotEmpty(entity.getPassword()) ? entity.getPassword() : "00000001");
							assetCommunicator.setName(importAssetGprsMeter.getSerial_Number());

							// 电表数据
							AssetMeter assetMeter = new AssetMeter();
							assetMeter.setSn(importAssetGprsMeter.getSerial_Number());
							assetMeter.setMac(importAssetGprsMeter.getLogical_address());
							assetMeter.setPassword(
									StringUtil.isNotEmpty(entity.getPassword()) ? entity.getPassword() : "00000001");
							assetMeter.setAuthType(
									StringUtil.isNotEmpty(entity.getAuthType()) ? entity.getAuthType() : "5");
							assetMeter.setComType(entity.getComType());
							assetMeter.setFwVersion(
									StringUtil.isNotEmpty(entity.getFwVersion()) ? entity.getFwVersion() : "");
							assetMeter.setHlsAk(
									StringUtil.isNotEmpty(entity.getHlsAk()) ? entity.getHlsAk() : "0000000000000000");
							assetMeter.setHlsEk(
									StringUtil.isNotEmpty(entity.getHlsEk()) ? entity.getHlsEk() : "0000000000000000");
							assetMeter.setScheduleSchemeId(entity.getScheduleSchemeId());
							assetMeter.setMeasurementGroupId(entity.getMeasurementGroupId());
							assetMeter.setModel(entity.getModel());
							assetMeter.setManufacturer(entity.getManufacturer());
							assetMeter.setIsEncrypt(
									StringUtil.isNotEmpty(entity.getIsEncrypt()) ? entity.getIsEncrypt() : 1);
							assetMeter.setName(importAssetGprsMeter.getSerial_Number());
							// 虚拟集中器 DCU索引为0，真实集中器为实际索引
							assetMeter.setIndexDcu(0);
							assetMeter.setCt(entity.getCt());
							assetMeter.setPt(entity.getPt());
							assetMeter.setTouGroupId(entity.getTouGroupId());
							assetMeter.setLimiterGroupId(entity.getLimiterGroupId());
							assetMeter.setStepTariffId(entity.getStepTariffId());
							assetMeter.setOrgId(entity.getOrgId());
							assetMeter.setUtilityId(entity.getUtilityId());

							// TODO 由于大批量导入的原因，此功能应该挪到uci内部去做，以节约时间
							AssetRefreshPort arp = (AssetRefreshPort) UciInterfaceUtil.getInterface("AssetRefreshPort",
									AssetRefreshPort.class, sysServiceAttributeService);
							List<RefreshMessage> msgs = new ArrayList<RefreshMessage>();

							// 判断sn是否已经存在
							AssetMeter m = assetMeterService.getEntityBySN(importAssetGprsMeter.getSerial_Number());
							if (m != null) {
								obj.put("sn", importAssetGprsMeter.getSerial_Number());
								obj.put("mac", importAssetGprsMeter.getLogical_address());
								obj.put("importStatus", "Failed");
								obj.put("importLog", "Sn already exists.");
								array.add(obj);
								continue;
							}

							if (isExistMac(importAssetGprsMeter.getLogical_address(), "GPRS")) {
								obj.put("sn", importAssetGprsMeter.getSerial_Number());
								obj.put("mac", importAssetGprsMeter.getLogical_address());
								obj.put("importStatus", "Failed");
								obj.put("importLog", "mac already exists.");
								array.add(obj);
								continue;
							}

							// 0: GPRS,1:PLC
							if ("0".equals(meterType)) {

								// mode=1,直接入asset_meter,mode=2，入asset_meter_prepaid,
								String mode = ResourceUtil.getSessionattachmenttitle("meter.grps.mode");
								if ("2".equals(mode)) {
									// 判断sn是否已经存在
									m = assetMeterService.getEntityPLC(importAssetGprsMeter.getSerial_Number());
									if (m != null) {
										obj.put("sn", importAssetGprsMeter.getSerial_Number());
										obj.put("mac", importAssetGprsMeter.getLogical_address());
										obj.put("importStatus", "Failed");
										obj.put("importLog", "Sn already exists.");
										array.add(obj);
										continue;
									}

									if (isExistMac(importAssetGprsMeter.getLogical_address(), "PLC")) {
										obj.put("sn", importAssetGprsMeter.getSerial_Number());
										obj.put("mac", importAssetGprsMeter.getLogical_address());
										obj.put("importStatus", "Failed");
										obj.put("importLog", "mac already exists.");
										array.add(obj);
										continue;
									}

									assetMeter.setCommunicatorId(null);
									assetMeterService.savePLCMeter(assetMeter);

									assetMeter = assetMeterService
											.getEntityPLC(importAssetGprsMeter.getSerial_Number());
									// meterId = assetMeter.getId();
								} else {
									// 保存Communicator信息
									assetCommunicator.setDeviceType(201);
									assetCommunicator.setUtilityId(su.getUtilityId());
									assetCommunicator.setOrgId(su.getOrgId());
									assetCommunicator.setRemoveFlag(0);
									String communicatorId = (String) assetCommunicatorService.save(assetCommunicator);

									if (StringUtil.isNotEmpty(communicatorId)) {
										// 保存meter 数据信息
										assetMeter.setUtilityId(su.getUtilityId());
										assetMeter.setCommunicatorId(communicatorId);
										assetMeter.setOrgId(su.getOrgId());
										assetMeter.setRemoveFlag(0);
										String meterId = (String) assetMeterService.save(assetMeter);
										assetMeter.setId(meterId);
										// 插入信用金额
										DictDeviceModel deviceModel = dictDeviceModelService
												.getEntity(assetMeter.getModel());
										VendMeterInitialCreditAmount initCreditAmount = new VendMeterInitialCreditAmount(
												meterId, deviceModel.getInitCreditAmount(), null, 0, null, new Date(),
												"hes新增电表");
										this.assetMeterService.insertCredit(initCreditAmount);

										if (StringUtil.isNotEmpty(meterId)) {
											fillMeterRelaInfo(assetMeter, meterId);
										}
										addRouter(communicatorId);

										// 调用UCI接口，刷新电表数据信息
										RefreshMessage refMsg = new RefreshMessage();
										refMsg.setType("meter");
										refMsg.setOpType("add");
										refMsg.getIds().add(meterId);
										RefreshMessage refMsg1 = new RefreshMessage();
										refMsg1.setType("communicator");
										refMsg1.setOpType("add");
										refMsg1.getIds().add(communicatorId);
										msgs.clear();
										msgs.add(refMsg);
										msgs.add(refMsg1);
										try {
											if (!arp.refresh(msgs)) {
												System.out.println("刷新失败" + meterId);
											}
										} catch (Exception e) {
											e.printStackTrace();
											j.setErrorMsg(MutiLangUtil.doMutiLang("system.UCIException"));
											return j;
										}
									}
								}

								// 加载出信息
								AssetMeter detailMeter = getMeterDetail(assetMeter.getSn(), "GPRS");
								if (detailMeter != null) {
									obj.putAll(JSONObject.fromObject(detailMeter));
								}

								obj.put("id", importIds[i]);
								obj.put("importStatus", "Success");
								obj.put("importLog", "Import Successful.");
								array.add(obj);
							} else if ("1".equals(meterType)) {
								// 电表数据
								assetMeter.setIndexDcu(1);
								// 保存meter 数据信息
								assetMeter.setUtilityId(su.getUtilityId());
								assetMeter.setOrgId(su.getOrgId());
								assetMeter.setRemoveFlag(0);

								String meterId = null;
								boolean isSetDcu = false;
								// 用户没有选择DCU,则将记录插入到ASSET_METER_PREPARE表中
								if (null == entity.getCommunicatorId() || "" == entity.getCommunicatorId().trim()
										|| "-1".equals(entity.getCommunicatorId())) {
									// 判断sn是否已经存在
									m = assetMeterService.getEntityPLC(importAssetGprsMeter.getSerial_Number());
									if (m != null) {
										obj.put("sn", importAssetGprsMeter.getSerial_Number());
										obj.put("mac", importAssetGprsMeter.getLogical_address());
										obj.put("importStatus", "Failed");
										obj.put("importLog", "Sn already exists.");
										array.add(obj);
										continue;
									}

									if (isExistMac(importAssetGprsMeter.getLogical_address(), "PLC")) {
										obj.put("sn", importAssetGprsMeter.getSerial_Number());
										obj.put("mac", importAssetGprsMeter.getLogical_address());
										obj.put("importStatus", "Failed");
										obj.put("importLog", "mac already exists.");
										array.add(obj);
										continue;
									}

									assetMeter.setCommunicatorId(null);
									assetMeterService.savePLCMeter(assetMeter);

									m = assetMeterService.getEntityPLC(importAssetGprsMeter.getSerial_Number());

									meterId = assetMeter.getId();

									isSetDcu = false;
								} else {
									assetMeter.setCommunicatorId(entity.getCommunicatorId());
									meterId = insertMeterAndCredit(assetMeter);

									isSetDcu = true;
								}
								if (StringUtil.isNotEmpty(meterId)) {
									fillMeterRelaInfo(assetMeter, meterId);

									if (isSetDcu) {
										// 由于是已存在的集中器，所以不需要在assetRoute再插入记录
										// --modify by liuyike
										// AssetRouter assetRouter = new
										// AssetRouter();
										// assetRouter.setCommunicatorId(entity.getCommunicatorId());
										// assetRouter.setChannelId("20010001");
										// assetRouter.setScheduleId("20010002");
										// assetRouterService.save(assetRouter);

										// 调用UCI接口，刷新电表数据信息
										RefreshMessage refMsg = new RefreshMessage();
										refMsg.setType("meter");
										refMsg.setOpType("add");
										refMsg.getIds().add(meterId);
										RefreshMessage refMsg1 = new RefreshMessage();
										refMsg1.setType("communicator");
										refMsg1.setOpType("add");
										refMsg1.getIds().add(entity.getCommunicatorId());
										msgs.clear();
										msgs.add(refMsg);
										msgs.add(refMsg1);
										try {
											if (!arp.refresh(msgs)) {
												System.out.println("刷新失败" + meterId);
											}
										} catch (Exception e) {
											e.printStackTrace();
											j.setErrorMsg(MutiLangUtil.doMutiLang("system.UCIException"));
											return j;
										}
									}
								}

								if (isSetDcu) {
									AssetMeter detailMeter = getMeterDetail(assetMeter.getSn(), "GPRS");
									if (detailMeter != null) {
										obj.putAll(JSONObject.fromObject(detailMeter));
									}
								} else {
									AssetMeter detailMeter = getMeterDetail(assetMeter.getSn(), "PLC");
									if (detailMeter != null) {
										obj.putAll(JSONObject.fromObject(detailMeter));
									}
								}
								obj.put("id", importIds[i]);
								obj.put("importStatus", "Success");
								obj.put("importLog", "Import Successful.");

								array.add(obj);
							}
						}
						// 更新电表导入进度，保存至Redis中
						if(meterList.size() > 20){
							if(i % num1 == 0){
								++num2;
								JedisUtils.set(su.getId() + "importComProgress", String.valueOf(num2 * 5), 3600);
							}
						}else{
							++num2;
							JedisUtils.set(su.getId() + "importComProgress", String.valueOf(num2 * 5), 3600);
						}
					}
				} // for end
			}
			j.setObj(array.toString());
		} catch (Exception ex) {
			ex.printStackTrace();
			j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return j;
		}
		return j;
	}
	
	/**
	 * 校验系统可使用电表数是否已经超过license许可
	 * @param appendMeterCount
	 * @return 新增此电表后，数据库中电表数为许可数的百分比。etc: 新增后为3000个，许可为2000个，则返回1.5
	 */
	private double meterLicenseCheck(int appendMeterCount) {
		double percent = 0.0;
		String meterNumber = "";
		if(Globals.isDevelop){
			meterNumber = "1000000";
		} else {
			meterNumber = PasswordUtil.decrypt(Globals.pcCodeMap.get("pc-code").split("-")[3], "root",PasswordUtil.getStaticSalt());
		}

		long licenseMeterCount = Long.parseLong(meterNumber);
		long assetMeterCount = assetMeterService.getCount(new AssetMeter());
		long assetMeterPrepare = assetMeterService.getCountPrepare(new AssetMeter());
		
		// 添加新增电表后，数据库中总的电表数
		long resultMeterCount = appendMeterCount + assetMeterCount + assetMeterPrepare;

		percent = new BigDecimal(resultMeterCount).divide(new BigDecimal(licenseMeterCount), 4, BigDecimal.ROUND_UP).doubleValue();
		
		return percent;
	}
	/**
	 * 批量导入电表数据信息
	 * 
	 * @param id
	 * @return
	 */
	@Transactional
	@RequestMapping(value = "batchNewDetailImportTemplateMeterData")
	@ResponseBody
	@SuppressWarnings("unchecked")
	public AjaxJson batchNewDetailImportTemplateMeterData(String meterType, String ids, AssetMeter entity,
			HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			
			boolean hasWarn = false;
			
			JSONArray array = new JSONArray();
			SysUser su = TokenManager.getToken();
			
			// 从redis中读取出之前文件导入的电表
			// 获取缓存的导入文件的信息
			List<AssetMeterTemplate> meterList = (List<AssetMeterTemplate>) JedisUtils.getObject(entity.getUuid());
			// 批次数
			int batchCount = 20;
			if (meterList.size() > 10000) {
				batchCount = 100;
			}
			
			if (meterList.size() > 0) {
				// 增加判断： 若已有电表数量大于license规定的数量，则不允许导入
				double percent = meterLicenseCheck(meterList.size());
				if (percent > 1) {
					j.setErrorMsg(MutiLangUtil.doMutiLang("deviceList.over100"));
					j.setObj(array.toString());
					return j;
				} else if (percent > 0.9) {
					hasWarn = true;
				}
				
				// 每批次数
				int num1 = 0;
				if (meterList.size() > batchCount) { // 把数据均分成二十份
					num1 = meterList.size() / batchCount;
				} else {
					num1 = meterList.size();
				}
				int num2 = 0; // 代表导入进度
				// 批处理
				List<AssetMeter> batchMeterList = new ArrayList<AssetMeter>();
				List<AssetCommunicator> batchCommList = new ArrayList<AssetCommunicator>();
				List<VendMeterInitialCreditAmount> batchInitialList = new ArrayList<VendMeterInitialCreditAmount>();
				List<AssetRouter> batchRouteList = new ArrayList<AssetRouter>();
				List<AssetMeterGroupMap> batchMetergroupMapList = new ArrayList<AssetMeterGroupMap>();
				boolean isPrepaidInsert = false;
				// 根据用户选中的id值，将这些电表插入到数据库中，同时使用entity中的属性填充字段。
				for (int i = 0; i < meterList.size(); i++) {
					AssetMeterTemplate importAssetGprsMeter = meterList.get(i);
					// 电表数据
					AssetMeter assetMeter = new AssetMeter();
					assetMeter.setId(IDUtils.getId());
					assetMeter.setSn(importAssetGprsMeter.getSerialNumber());
					assetMeter.setMac(importAssetGprsMeter.getMac());
					assetMeter.setStsSn(importAssetGprsMeter.getCommAddress());
					
					assetMeter.setPassword(StringUtil.isNotEmpty(entity.getPassword()) ? entity.getPassword() : "00000001");
					assetMeter.setAuthType(StringUtil.isNotEmpty(entity.getAuthType()) ? entity.getAuthType() : "5");
					assetMeter.setComType(importAssetGprsMeter.getComTypeId());
					assetMeter.setFwVersion(StringUtil.isNotEmpty(importAssetGprsMeter.getFirmwareVersion()) ? importAssetGprsMeter.getFirmwareVersion() : "");
					assetMeter.setHlsAk(StringUtil.isNotEmpty(importAssetGprsMeter.getAk()) ? importAssetGprsMeter.getAk() : "0000000000000000");
					assetMeter.setHlsEk(StringUtil.isNotEmpty(importAssetGprsMeter.getEk()) ? importAssetGprsMeter.getEk() : "0000000000000000");
					assetMeter.setScheduleSchemeId(entity.getScheduleSchemeId()); 
					assetMeter.setMeasurementGroupId(entity.getMeasurementGroupId());
					assetMeter.setModel(importAssetGprsMeter.getDeviceTypeId());
					assetMeter.setManufacturer(importAssetGprsMeter.getManufacturerId());
					assetMeter.setIsEncrypt(StringUtil.isNotEmpty(entity.getIsEncrypt()) ? entity.getIsEncrypt() : 1);
					assetMeter.setName(importAssetGprsMeter.getSerialNumber());
					// 虚拟集中器 DCU索引为0，真实集中器为实际索引
					assetMeter.setIndexDcu(0);
					if (importAssetGprsMeter.getKeyMeterId() != null && 1 == importAssetGprsMeter.getKeyMeterId()) {
						assetMeter.setKeyFlag(1);
					} else { 
						assetMeter.setKeyFlag(0);
					}
					
					assetMeter.setCt(entity.getCt());
					assetMeter.setPt(entity.getPt());
					assetMeter.setTouGroupId(entity.getTouGroupId());
					assetMeter.setLimiterGroupId(entity.getLimiterGroupId());
					assetMeter.setStepTariffId(entity.getStepTariffId());
					assetMeter.setOrgId(entity.getOrgId());
					assetMeter.setUtilityId(su.getUtilityId());
					assetMeter.setRemoveFlag(0);
					
					// 0: GPRS,1:PLC
					if ("0".equals(meterType)) {
						// mode=1,直接入asset_meter,mode=2，入asset_meter_prepaid,
						String mode = ResourceUtil.getSessionattachmenttitle("meter.grps.mode");
						// mode=2，导入的电表入asset_meter_prepaid表，uci自动注册
						if ("2".equals(mode)) {
							assetMeter.setCommunicatorId(null);
							batchMeterList.add(assetMeter);
							isPrepaidInsert = true;
							// mode=1，导入的电表入asset_meter表
						} else {
							// 增加虚拟集中器
							AssetCommunicator assetCommunicator = new AssetCommunicator();
							assetCommunicator.setId(IDUtils.getId());
							assetCommunicator.setSn(importAssetGprsMeter.getSerialNumber());
							assetCommunicator.setMac(importAssetGprsMeter.getMac());
							assetCommunicator.setComType(importAssetGprsMeter.getComTypeId());
							assetCommunicator.setFwVersion(StringUtil.isNotEmpty(importAssetGprsMeter.getFirmwareVersion()) ? importAssetGprsMeter.getFirmwareVersion() : "");
							assetCommunicator.setModel(importAssetGprsMeter.getDeviceTypeId());
							assetCommunicator.setManufacturer(importAssetGprsMeter.getManufacturerId());
							assetCommunicator.setPass(StringUtil.isNotEmpty(entity.getPassword()) ? entity.getPassword() : "000000");
							assetCommunicator.setIsEncrypt(0);
							assetCommunicator.setAuthType("1");
							assetCommunicator.setName(importAssetGprsMeter.getSerialNumber());
							assetCommunicator.setDeviceType(201); 
							assetCommunicator.setUtilityId(su.getUtilityId());
							assetCommunicator.setOrgId(entity.getOrgId());
							assetCommunicator.setRemoveFlag(0);
							// 保存meter 数据信息
							assetMeter.setUtilityId(su.getUtilityId()); 
							assetMeter.setCommunicatorId(assetCommunicator.getId());
							
							// 添加到批处理中
							batchCommList.add(assetCommunicator);
							batchMeterList.add(assetMeter);
							// 插入信用金额
							DictDeviceModel deviceModel = dictDeviceModelService.getEntity(assetMeter.getModel());
							VendMeterInitialCreditAmount initCreditAmount = new VendMeterInitialCreditAmount(
									assetMeter.getId(), deviceModel.getInitCreditAmount(), null, 0, null, new Date(),
									"hes新增电表");
							initCreditAmount.setId(IDUtils.getId());
							batchInitialList.add(initCreditAmount);
							
							AssetRouter assetRouter = new AssetRouter();
							assetRouter.setCommunicatorId(assetCommunicator.getId());
							assetRouter.setChannelId("20010001");
							assetRouter.setScheduleId("20010002");
							batchRouteList.add(assetRouter);
							
							isPrepaidInsert = false;
						}
						batchMetergroupMapList.addAll(fillMeterBatchRelaInfo(assetMeter, assetMeter.getId()));
					} else if ("1".equals(meterType)) {
						assetMeter.setIndexDcu(1);
						// 保存meter 数据信息
						assetMeter.setUtilityId(su.getUtilityId());
						assetMeter.setOrgId(entity.getOrgId());
						assetMeter.setRemoveFlag(0);
						
						String meterId = null;
						// 用户没有选择DCU,则将记录插入到ASSET_METER_PREPARE表中
						if (null == entity.getCommunicatorId() || "" == entity.getCommunicatorId().trim()
								|| "-1".equals(entity.getCommunicatorId())) {
							// 保存电表到临时表中，做自动注册
							assetMeter.setCommunicatorId(null);
							batchMeterList.add(assetMeter);
							
							isPrepaidInsert = true;
						} else {
							AssetCommunicator ac =  assetCommunicatorService.getEntity(entity.getCommunicatorId());
							assetMeter.setUtilityId(ac.getUtilityId());
							assetMeter.setOrgId(ac.getOrgId());
							assetMeter.setCommunicatorId(entity.getCommunicatorId());
							batchMeterList.add(assetMeter);
							// 插入信用金额
							DictDeviceModel deviceModel = dictDeviceModelService.getEntity(assetMeter.getModel());
							VendMeterInitialCreditAmount initCreditAmount = new VendMeterInitialCreditAmount(assetMeter.getId(),
									deviceModel.getInitCreditAmount(), null, 0, null, new Date(), "hes新增电表");
							initCreditAmount.setId(IDUtils.getId());
							batchInitialList.add(initCreditAmount);
							
							isPrepaidInsert = false;
						}
						batchMetergroupMapList.addAll(fillMeterBatchRelaInfo(assetMeter, assetMeter.getId()));
					}
					
					// 更新电表导入进度，保存至Redis中
					if (meterList.size() > batchCount) {
						if ((i+1) % num1 == 0 || (i == meterList.size() - 1) ) {
							++num2;
							JedisUtils.set(su.getId() + "importTempMeterProgress", String.valueOf(num2 * (100/batchCount)), 3600);
							
							if (CollectionUtils.isNotEmpty(batchCommList)) {
								assetCommunicatorService.batchInsert(batchCommList);
							}
							
							
							List<ImportAssetGprsMeter> meters = new ArrayList<ImportAssetGprsMeter>();
							// 创建shipmentfile
							if (CollectionUtils.isNotEmpty(batchMeterList)) {
								for (AssetMeter meter : batchMeterList) {
									ImportAssetGprsMeter e = new ImportAssetGprsMeter();
									e.setSerial_Number(meter.getSn());
									e.setLogical_address(meter.getMac());
									e.setComm_Address(meter.getStsSn());
									meters.add(e);
								}
							}
							
							// dataShipmentMeterService
							if (isPrepaidInsert) {
								if (CollectionUtils.isNotEmpty(batchMeterList)) {
									assetMeterService.batchInsertPrepaid(batchMeterList);
									// 批量导入shipment file
									dataShipmentMeterService.batchInsert(meters);
								}
							} else {
								if (CollectionUtils.isNotEmpty(batchMeterList)) {
									assetMeterService.batchInsert(batchMeterList);
									// 批量导入shipment file
									dataShipmentMeterService.batchInsert(meters);
								}
							}
							
							if (CollectionUtils.isNotEmpty(batchInitialList)) {
								assetMeterService.batchInsertCredit(batchInitialList);
							}
							
							if (CollectionUtils.isNotEmpty(batchRouteList)) {
								assetRouterService.batchInsert(batchRouteList);
							}
							
							if (CollectionUtils.isNotEmpty(batchMetergroupMapList)) {
								assetMeterGroupMapService.batchInsert(batchMetergroupMapList);
							}
							
							// 做完此批次后，将记录清空
							batchMeterList.clear();
							batchCommList.clear();
							batchInitialList.clear();
							batchRouteList.clear();
							batchMetergroupMapList.clear();
						}
					} else {
						++num2;
						JedisUtils.set(su.getId() + "importTempMeterProgress", String.valueOf(num2 * (100/meterList.size())), 3600);
						
						List<ImportAssetGprsMeter> meters = new ArrayList<ImportAssetGprsMeter>();
						// 创建shipmentfile
						if (CollectionUtils.isNotEmpty(batchMeterList)) {
							for (AssetMeter meter : batchMeterList) {
								ImportAssetGprsMeter e = new ImportAssetGprsMeter();
								e.setSerial_Number(meter.getSn());
								e.setLogical_address(meter.getMac());
								e.setComm_Address(meter.getStsSn());
								meters.add(e);
							}
						}
						
						// dataShipmentMeterService
						if (isPrepaidInsert) {
							if (CollectionUtils.isNotEmpty(batchMeterList)) {
								assetMeterService.batchInsertPrepaid(batchMeterList);
								// 批量导入shipment file
								dataShipmentMeterService.batchInsert(meters);
							}
						} else {
							if (CollectionUtils.isNotEmpty(batchMeterList)) {
								assetMeterService.batchInsert(batchMeterList);
								// 批量导入shipment file
								dataShipmentMeterService.batchInsert(meters);
							}
						}
						
						if (CollectionUtils.isNotEmpty(batchCommList)) {
							assetCommunicatorService.batchInsert(batchCommList);
						}
						
						if (CollectionUtils.isNotEmpty(batchInitialList)) {
							assetMeterService.batchInsertCredit(batchInitialList);
						}
						
						if (CollectionUtils.isNotEmpty(batchRouteList)) {
							assetRouterService.batchInsert(batchRouteList);
						}
						
						if (CollectionUtils.isNotEmpty(batchMetergroupMapList)) {
							assetMeterGroupMapService.batchInsert(batchMetergroupMapList);
						}
						
						// 做完此批次后，将记录清空
						batchMeterList.clear();
						batchCommList.clear();
						batchInitialList.clear();
						batchRouteList.clear();
						batchMetergroupMapList.clear();
					}
				} // for end
			}
			if (hasWarn) {
				j.setMsg(MutiLangUtil.doMutiLang("deviceList.over90"));
			} else {
				j.setMsg(MutiLangUtil.doMutiLang("system.addSucc"));
			}
			j.setObj(array.toString());
		} catch (Exception ex) {
			ex.printStackTrace();
			j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return j;
		}
		return j;
	}
	/**
	 * 批量导入电表数据信息
	 * 
	 * @param id
	 * @return
	 */
	@Transactional
	@RequestMapping(value = "batchNewImportTemplateMeterData")
	@ResponseBody
	@SuppressWarnings("unchecked")
	public AjaxJson batchNewImportTemplateMeterData(String meterType, String ids, AssetMeter entity,
			HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			
			boolean hasWarn = false;
			
			JSONArray array = new JSONArray();
			SysUser su = TokenManager.getToken();

			// 从redis中读取出之前文件导入的电表
			// 获取缓存的导入文件的信息
			List<ImportAssetGprsMeter> meterList = (List<ImportAssetGprsMeter>) JedisUtils.getObject(entity.getUuid());
			// 导入失败的记录，返回给用户
			List<ImportAssetGprsMeter> failedList = new ArrayList<ImportAssetGprsMeter>();
			// 导入成功的记录，返回给用户
			List<ImportAssetGprsMeter> successList = new ArrayList<ImportAssetGprsMeter>();
			// 1. 增加批量处理
			// 2. 如果数据已经存在，则直接忽略，不插入
			// 3. 若插入的时候发生错误，将失败的记录收集起来，将失败的记录通过excel返回给用户。
			
			// 批次数
			int batchCount = 20;
			if (meterList.size() > 10000) {
				batchCount = 100;
			}
			
			if (meterList.size() > 0) {
				// 增加判断： 若已有电表数量大于license规定的数量，则不允许导入
				double percent = meterLicenseCheck(meterList.size());
				if (percent > 1) {
					j.setErrorMsg(MutiLangUtil.doMutiLang("deviceList.over100"));
					j.setObj(array.toString());
					return j;
				} else if (percent > 0.9) {
					hasWarn = true;
				}
				
				// 每批次数
				int num1 = 0;
				if (meterList.size() > batchCount) { // 把数据均分成二十份
					num1 = meterList.size() / batchCount;
				} else {
					num1 = meterList.size();
				}
				int num2 = 0; // 代表导入进度
				// 批处理
				List<AssetMeter> batchMeterList = new ArrayList<AssetMeter>();
				List<AssetCommunicator> batchCommList = new ArrayList<AssetCommunicator>();
				List<VendMeterInitialCreditAmount> batchInitialList = new ArrayList<VendMeterInitialCreditAmount>();
				List<AssetRouter> batchRouteList = new ArrayList<AssetRouter>();
				List<AssetMeterGroupMap> batchMetergroupMapList = new ArrayList<AssetMeterGroupMap>();
				boolean isPrepaidInsert = false;
				// 根据用户选中的id值，将这些电表插入到数据库中，同时使用entity中的属性填充字段。
				for (int i = 0; i < meterList.size(); i++) {
					ImportAssetGprsMeter importAssetGprsMeter = meterList.get(i);
					// 电表数据
					AssetMeter assetMeter = new AssetMeter();
					assetMeter.setId(IDUtils.getId());
					assetMeter.setSn(importAssetGprsMeter.getSerial_Number());
					assetMeter.setMac(importAssetGprsMeter.getLogical_address());
					assetMeter.setPassword(StringUtil.isNotEmpty(entity.getPassword()) ? entity.getPassword() : "00000001");
					assetMeter.setAuthType(StringUtil.isNotEmpty(entity.getAuthType()) ? entity.getAuthType() : "5");
					assetMeter.setComType(entity.getComType());
					assetMeter.setFwVersion(StringUtil.isNotEmpty(entity.getFwVersion()) ? entity.getFwVersion() : "");
					assetMeter.setHlsAk(StringUtil.isNotEmpty(entity.getHlsAk()) ? entity.getHlsAk() : "0000000000000000");
					assetMeter.setHlsEk(StringUtil.isNotEmpty(entity.getHlsEk()) ? entity.getHlsEk() : "0000000000000000");
					assetMeter.setScheduleSchemeId(entity.getScheduleSchemeId());
					assetMeter.setMeasurementGroupId(entity.getMeasurementGroupId());
					assetMeter.setModel(entity.getModel());
					assetMeter.setManufacturer(entity.getManufacturer());
					assetMeter.setIsEncrypt(StringUtil.isNotEmpty(entity.getIsEncrypt()) ? entity.getIsEncrypt() : 1);
					assetMeter.setName(importAssetGprsMeter.getSerial_Number());
					// 虚拟集中器 DCU索引为0，真实集中器为实际索引
					assetMeter.setIndexDcu(0);
					if ("1".equals(importAssetGprsMeter.getKeyMeter())) {
						assetMeter.setKeyFlag(1);
					} else {
						assetMeter.setKeyFlag(0);
					}
					
					assetMeter.setCt(entity.getCt());
					assetMeter.setPt(entity.getPt());
					assetMeter.setTouGroupId(entity.getTouGroupId());
					assetMeter.setLimiterGroupId(entity.getLimiterGroupId());
					assetMeter.setStepTariffId(entity.getStepTariffId());
					assetMeter.setOrgId(entity.getOrgId());
					assetMeter.setUtilityId(entity.getUtilityId());
					assetMeter.setRemoveFlag(0);

					// 0: GPRS,1:PLC
					if ("0".equals(meterType)) {
						// mode=1,直接入asset_meter,mode=2，入asset_meter_prepaid,
						String mode = ResourceUtil.getSessionattachmenttitle("meter.grps.mode");
						// mode=2，导入的电表入asset_meter_prepaid表，uci自动注册
						if ("2".equals(mode)) {
							assetMeter.setCommunicatorId(null);
							batchMeterList.add(assetMeter);
							isPrepaidInsert = true;
						// mode=1，导入的电表入asset_meter表
						} else {
							// 增加虚拟集中器
							AssetCommunicator assetCommunicator = new AssetCommunicator();
							assetCommunicator.setId(IDUtils.getId());
							assetCommunicator.setSn(importAssetGprsMeter.getSerial_Number());
							assetCommunicator.setMac(importAssetGprsMeter.getLogical_address());
							assetCommunicator.setComType(entity.getComType());
							assetCommunicator.setFwVersion(StringUtil.isNotEmpty(entity.getFwVersion()) ? entity.getFwVersion() : "");
							assetCommunicator.setModel(entity.getModel());
							assetCommunicator.setManufacturer(entity.getManufacturer());
							assetCommunicator.setPass(StringUtil.isNotEmpty(entity.getPassword()) ? entity.getPassword() : "000000");
							assetCommunicator.setIsEncrypt(0);
							assetCommunicator.setAuthType("1");
							assetCommunicator.setName(importAssetGprsMeter.getSerial_Number());
							assetCommunicator.setDeviceType(201);
							assetCommunicator.setUtilityId(su.getUtilityId());
							assetCommunicator.setOrgId(entity.getOrgId());
							assetCommunicator.setRemoveFlag(0);
							// 保存meter 数据信息
							assetMeter.setUtilityId(su.getUtilityId());
							assetMeter.setCommunicatorId(assetCommunicator.getId());
							
							// 添加到批处理中
							batchCommList.add(assetCommunicator);
							batchMeterList.add(assetMeter);
							// 插入信用金额
							DictDeviceModel deviceModel = dictDeviceModelService.getEntity(assetMeter.getModel());
							VendMeterInitialCreditAmount initCreditAmount = new VendMeterInitialCreditAmount(
									assetMeter.getId(), deviceModel.getInitCreditAmount(), null, 0, null, new Date(),
									"hes新增电表");
							initCreditAmount.setId(IDUtils.getId());
							batchInitialList.add(initCreditAmount);
							
							AssetRouter assetRouter = new AssetRouter();
							assetRouter.setCommunicatorId(assetCommunicator.getId());
							assetRouter.setChannelId("20010001");
							assetRouter.setScheduleId("20010002");
							batchRouteList.add(assetRouter);
							
							isPrepaidInsert = false;
						}
						batchMetergroupMapList.addAll(fillMeterBatchRelaInfo(assetMeter, assetMeter.getId()));
					} else if ("1".equals(meterType)) {
						assetMeter.setIndexDcu(1);
						// 保存meter 数据信息
						assetMeter.setUtilityId(su.getUtilityId());
						assetMeter.setOrgId(entity.getOrgId());
						assetMeter.setRemoveFlag(0);

						String meterId = null;
						// 用户没有选择DCU,则将记录插入到ASSET_METER_PREPARE表中
						if (null == entity.getCommunicatorId() || "" == entity.getCommunicatorId().trim()
								|| "-1".equals(entity.getCommunicatorId())) {
							// 保存电表到临时表中，做自动注册
							assetMeter.setCommunicatorId(null);
							batchMeterList.add(assetMeter);
							
							isPrepaidInsert = true;
						} else {
							AssetCommunicator ac =  assetCommunicatorService.getEntity(entity.getCommunicatorId());
							assetMeter.setUtilityId(ac.getUtilityId());
							assetMeter.setOrgId(ac.getOrgId());
							assetMeter.setCommunicatorId(entity.getCommunicatorId());
							batchMeterList.add(assetMeter);
							// 插入信用金额
							DictDeviceModel deviceModel = dictDeviceModelService.getEntity(assetMeter.getModel());
							VendMeterInitialCreditAmount initCreditAmount = new VendMeterInitialCreditAmount(assetMeter.getId(),
									deviceModel.getInitCreditAmount(), null, 0, null, new Date(), "hes新增电表");
							initCreditAmount.setId(IDUtils.getId());
							batchInitialList.add(initCreditAmount);
							
							isPrepaidInsert = false;
						}
						batchMetergroupMapList.addAll(fillMeterBatchRelaInfo(assetMeter, assetMeter.getId()));
					}
					
					// 更新电表导入进度，保存至Redis中
					if (meterList.size() > batchCount) {
						if ((i+1) % num1 == 0 || (i == meterList.size() - 1) ) {
							++num2;
							JedisUtils.set(su.getId() + "importTempMeterProgress", String.valueOf(num2 * (100/batchCount)), 3600);
							
							if (CollectionUtils.isNotEmpty(batchCommList)) {
								assetCommunicatorService.batchInsert(batchCommList);
							}
							
							if (isPrepaidInsert) {
								if (CollectionUtils.isNotEmpty(batchMeterList)) {
									assetMeterService.batchInsertPrepaid(batchMeterList);
								}
							} else {
								if (CollectionUtils.isNotEmpty(batchMeterList)) {
									assetMeterService.batchInsert(batchMeterList);
								}
							}
							
							if (CollectionUtils.isNotEmpty(batchInitialList)) {
								assetMeterService.batchInsertCredit(batchInitialList);
							}
							
							if (CollectionUtils.isNotEmpty(batchRouteList)) {
								assetRouterService.batchInsert(batchRouteList);
							}
							
							if (CollectionUtils.isNotEmpty(batchMetergroupMapList)) {
								assetMeterGroupMapService.batchInsert(batchMetergroupMapList);
							}
							
							// 做完此批次后，将记录清空
							batchMeterList.clear();
							batchCommList.clear();
							batchInitialList.clear();
							batchRouteList.clear();
							batchMetergroupMapList.clear();
						}
					} else {
						++num2;
						JedisUtils.set(su.getId() + "importTempMeterProgress", String.valueOf(num2 * (100/meterList.size())), 3600);
						
						if (isPrepaidInsert) {
							assetMeterService.batchInsertPrepaid(batchMeterList);
						} else {
							assetMeterService.batchInsert(batchMeterList);
						}
						
						if (CollectionUtils.isNotEmpty(batchCommList)) {
							assetCommunicatorService.batchInsert(batchCommList);
						}
						
						if (CollectionUtils.isNotEmpty(batchInitialList)) {
							assetMeterService.batchInsertCredit(batchInitialList);
						}
						
						if (CollectionUtils.isNotEmpty(batchRouteList)) {
							assetRouterService.batchInsert(batchRouteList);
						}
						
						if (CollectionUtils.isNotEmpty(batchMetergroupMapList)) {
							assetMeterGroupMapService.batchInsert(batchMetergroupMapList);
						}
						
						// 做完此批次后，将记录清空
						batchMeterList.clear();
						batchCommList.clear();
						batchInitialList.clear();
						batchRouteList.clear();
						batchMetergroupMapList.clear();
					}
				} // for end
			}
			if (hasWarn) {
				j.setMsg(MutiLangUtil.doMutiLang("deviceList.over90"));
			} else {
				j.setMsg(MutiLangUtil.doMutiLang("system.addSucc"));
			}
			j.setObj(array.toString());
		} catch (Exception ex) {
			ex.printStackTrace();
			j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return j;
		}
		return j;
	}

	private String insertMeterAndCredit(AssetMeter assetMeter) {
		String meterId;
		meterId = (String) assetMeterService.save(assetMeter);
		// 新增电表需要 添加 初始化信用额度
		// 插入信用金额
		DictDeviceModel deviceModel = dictDeviceModelService.getEntity(assetMeter.getModel());
		VendMeterInitialCreditAmount initCreditAmount = new VendMeterInitialCreditAmount(meterId,
				deviceModel.getInitCreditAmount(), null, 0, null, new Date(), "hes新增电表");
		this.assetMeterService.insertCredit(initCreditAmount);
		return meterId;
	}

	private void addRouter(String communicatorId) {
		AssetRouter assetRouter = new AssetRouter();
		assetRouter.setCommunicatorId(communicatorId);
		assetRouter.setChannelId("20010001");
		assetRouter.setScheduleId("20010002");
		assetRouterService.save(assetRouter);
	}

	private List<AssetMeterGroupMap> fillMeterBatchRelaInfo(AssetMeter assetMeter, String meterId) {
		List<AssetMeterGroupMap> metergroupMapList = new ArrayList<AssetMeterGroupMap>();
		
		
		/** Measurement 分组 */
		if (StringUtil.isNotEmpty(assetMeter.getMeasurementGroupId())) {
			AssetMeterGroupMap amgm = new AssetMeterGroupMap();
			amgm.setId(meterId);
			amgm.setType("1");
			amgm.setGroupId(assetMeter.getMeasurementGroupId());
			metergroupMapList.add(amgm);
		}
		/** TOU 分组 */
		if (StringUtil.isNotEmpty(assetMeter.getTouGroupId())) {
			AssetMeterGroupMap amgm = new AssetMeterGroupMap();
			amgm.setId(meterId);
			amgm.setType("2");
			amgm.setGroupId(assetMeter.getTouGroupId());
			metergroupMapList.add(amgm);
		}
		/** Limiter 分组 */
		if (StringUtil.isNotEmpty(assetMeter.getLimiterGroupId())) {
			AssetMeterGroupMap amgm = new AssetMeterGroupMap();
			amgm.setId(meterId);
			amgm.setType("3");
			amgm.setGroupId(assetMeter.getLimiterGroupId());
			metergroupMapList.add(amgm);
		}
		/** 阶梯费率 后面增加页面参数 */
		if (StringUtil.isNotEmpty(assetMeter.getStepTariffId())) {
			AssetMeterGroupMap amgm = new AssetMeterGroupMap();
			amgm.setId(meterId);
			amgm.setType("4");
			amgm.setGroupId(assetMeter.getStepTariffId());
			metergroupMapList.add(amgm);
		}
		/** 采集方案 */
		if (StringUtil.isNotEmpty(assetMeter.getScheduleSchemeId())) {
			AssetMeterGroupMap amgm = new AssetMeterGroupMap();
			amgm.setId(meterId);
			amgm.setType("0");
			amgm.setGroupId(assetMeter.getScheduleSchemeId());
			metergroupMapList.add(amgm);
		}
		
		return metergroupMapList;
	}
	
	private void fillMeterRelaInfo(AssetMeter assetMeter, String meterId) {
		/** Measurement 分组 */
		if (StringUtil.isNotEmpty(assetMeter.getMeasurementGroupId())) {
			AssetMeterGroupMap amgm = new AssetMeterGroupMap();
			amgm.setId(meterId);
			amgm.setType("1");
			amgm.setGroupId(assetMeter.getMeasurementGroupId());
			assetMeterGroupMapService.save(amgm);
		}
		/** TOU 分组 */
		if (StringUtil.isNotEmpty(assetMeter.getTouGroupId())) {
			AssetMeterGroupMap amgm = new AssetMeterGroupMap();
			amgm.setId(meterId);
			amgm.setType("2");
			amgm.setGroupId(assetMeter.getTouGroupId());
			assetMeterGroupMapService.save(amgm);
		}
		/** Limiter 分组 */
		if (StringUtil.isNotEmpty(assetMeter.getLimiterGroupId())) {
			AssetMeterGroupMap amgm = new AssetMeterGroupMap();
			amgm.setId(meterId);
			amgm.setType("3");
			amgm.setGroupId(assetMeter.getLimiterGroupId());
			assetMeterGroupMapService.save(amgm);
		}
		/** 阶梯费率 后面增加页面参数 */
		if (StringUtil.isNotEmpty(assetMeter.getStepTariffId())) {
			AssetMeterGroupMap amgm = new AssetMeterGroupMap();
			amgm.setId(meterId);
			amgm.setType("4");
			amgm.setGroupId(assetMeter.getStepTariffId());
			assetMeterGroupMapService.save(amgm);
		}
		/** 采集方案 */
		if (StringUtil.isNotEmpty(assetMeter.getScheduleSchemeId())) {
			AssetMeterGroupMap amgm = new AssetMeterGroupMap();
			amgm.setId(meterId);
			amgm.setType("0");
			amgm.setGroupId(assetMeter.getScheduleSchemeId());
			assetMeterGroupMapService.save(amgm);
		}
	}
	
	private boolean isExistMac(String mac,String meterType) {
		boolean isExist = false;
		
		if ("GPRS".equals(meterType)) {
			AssetMeter entity = assetMeterService.getEntityByMac(mac);
			if (entity != null) {
				isExist = true;
			}
		} else if ("PLC".equals(meterType)) {
			AssetMeter entity = assetMeterService.getEntityByMacPLC(mac);
			if (entity != null) {
				isExist = true;
			}
		}
		return isExist;
	}
	
	
	
    /**
	 * 批量导入电表数据信息
	 * @param id
	 * @return
	 */
	@Transactional
	@RequestMapping(value = "batchImportMeterData")
	@ResponseBody
	@SuppressWarnings("unchecked")
	public AjaxJson batchImportMeterData(AssetMeter entity, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			SysUser su = TokenManager.getToken();
			// 1 GPRS,2,Communicator,3 普通表
			if(StringUtil.isEmpty(entity.getDeviceType())){
				j.setErrorMsg(MutiLangUtil.doMutiLang("importGprsMeter.deviceTypeIsEmpty"));
				return j;
			}
			//获取缓存的导入文件的信息
			List<ImportAssetGprsMeter> meterList = (List<ImportAssetGprsMeter>) JedisUtils.getObject(entity.getUuid());
			if(meterList.size() > 0){
				int num1 = 0; 
				if(meterList.size() > 20){	//把数据均分成二十份
					num1 = meterList.size() / 20;
				}else{
					num1 = meterList.size();
				}
				int num2 = 0;						//代表导入进度
				for (int i = 0; i < meterList.size(); i++) {
					//集中器数据
					AssetCommunicator assetCommunicator = new AssetCommunicator();
					assetCommunicator.setSn(meterList.get(i).getSerial_Number());
					assetCommunicator.setMac(meterList.get(i).getLogical_address());
					assetCommunicator.setComType(entity.getComType());
					assetCommunicator.setFwVersion(StringUtil.isNotEmpty(entity.getFwVersion()) ? entity.getFwVersion() : "");
					assetCommunicator.setModel(entity.getModel());
					assetCommunicator.setManufacturer(entity.getManufacturer());
					assetCommunicator.setPass(StringUtil.isNotEmpty(entity.getPassword()) ? entity.getPassword() : "00000001");
					assetCommunicator.setName(meterList.get(i).getSerial_Number());
					
					//电表数据
					AssetMeter assetMeter = new AssetMeter();
					assetMeter.setSn(meterList.get(i).getSerial_Number());
					assetMeter.setMac(meterList.get(i).getLogical_address());
					assetMeter.setPassword(StringUtil.isNotEmpty(entity.getPassword()) ? entity.getPassword() : "00000001");
					assetMeter.setAuthType(StringUtil.isNotEmpty(entity.getAuthType()) ? entity.getAuthType() : "5");
					assetMeter.setComType(entity.getComType());
					assetMeter.setFwVersion(StringUtil.isNotEmpty(entity.getFwVersion()) ? entity.getFwVersion() : "");
					assetMeter.setHlsAk(StringUtil.isNotEmpty(entity.getHlsAk()) ? entity.getHlsAk() : "0000000000000000");
					assetMeter.setHlsEk(StringUtil.isNotEmpty(entity.getHlsEk()) ? entity.getHlsEk() : "0000000000000000");
					assetMeter.setScheduleSchemeId(entity.getScheduleSchemeId());
					assetMeter.setMeasurementGroupId(entity.getMeasurementGroupId());
					assetMeter.setModel(entity.getModel());
					assetMeter.setManufacturer(entity.getManufacturer());
					assetMeter.setIsEncrypt(StringUtil.isNotEmpty(entity.getIsEncrypt()) ? entity.getIsEncrypt() : 1);
					assetMeter.setName(meterList.get(i).getSerial_Number());
					assetMeter.setIndexDcu(0);
					
					AssetRefreshPort arp = (AssetRefreshPort)UciInterfaceUtil.getInterface("AssetRefreshPort", AssetRefreshPort.class, sysServiceAttributeService);
					List<RefreshMessage> msgs = new ArrayList<RefreshMessage>(); 
					// 1 GPRS,2,Communicator,3 普通表
					if(entity.getDeviceType().equals("1")){
						//保存Communicator信息
						assetCommunicator.setDeviceType(201);
						assetCommunicator.setUtilityId(su.getUtilityId());
						assetCommunicator.setOrgId(su.getOrgId());
						assetCommunicator.setRemoveFlag(0);
						
						//module 模块属性 固定值 update 不需要还原
						assetCommunicator.setAuthType("1");
						assetCommunicator.setPass("000000");
						assetCommunicator.setIsEncrypt(0);
						
						String communicatorId = (String) assetCommunicatorService.save(assetCommunicator);
						
						if(StringUtil.isNotEmpty(communicatorId)){
							//保存meter 数据信息
							assetMeter.setUtilityId(su.getUtilityId());
							assetMeter.setCommunicatorId(communicatorId);
							assetMeter.setOrgId(su.getOrgId());
							assetMeter.setRemoveFlag(0);
							String meterId = (String) assetMeterService.save(assetMeter);
							if(StringUtil.isNotEmpty(meterId)){
								fillMeterRelaInfo(assetMeter, meterId);
								addRouter(communicatorId);
							}
							AssetRouter assetRouter = new AssetRouter();
							assetRouter.setCommunicatorId(communicatorId);
							assetRouter.setChannelId("20010001");
							assetRouter.setScheduleId("20010002");
							assetRouterService.save(assetRouter);
							
							//调用UCI接口，刷新电表数据信息
							RefreshMessage refMsg = new RefreshMessage();
							refMsg.setType("meter");
							refMsg.setOpType("add");
							refMsg.getIds().add(meterId);
							RefreshMessage refMsg1 = new RefreshMessage();
							refMsg1.setType("communicator");
							refMsg1.setOpType("add");
							refMsg1.getIds().add(communicatorId);
							msgs.clear();
							msgs.add(refMsg);
							msgs.add(refMsg1);
							try{
								if(!arp.refresh(msgs)){
									System.out.println("刷新失败" + meterId);
								}
							} catch (Exception e) {
								e.printStackTrace();
								j.setErrorMsg(MutiLangUtil.doMutiLang("system.UCIException"));
								return j;
							}
						}
						dataUserLogService.insertDataUserLog(su.getId(), "Asset Managerment", "Add Meter","Add Meter ( SN = "+ assetMeter.getSn() +" )");
						dataUserLogService.insertDataUserLog(su.getId(), "Asset Managerment", "Add Communicator", "Add Communicator ( SN = "+ assetCommunicator.getSn() +" )");
					}
					//更新电表导入进度，保存至Redis中
					if(meterList.size() > 20){
						if(i % num1 == 0){
							++num2;
							JedisUtils.set(su.getId() + "importProgress", String.valueOf(num2 * 5), 3600);
						}
					}else{
						++num2;
						JedisUtils.set(su.getId() + "importProgress", String.valueOf(num2 * 5), 3600);
					}
					
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return j;
		}
		j.setMsg(MutiLangUtil.doMutiLang("system.operSucce"));
		return j;
	}
	
	/**
	 * 获取导入电表的进度
	 * @param assetMeter
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getBatchImportMeterProgress")
	@ResponseBody
	public AjaxJson getBatchImportMeterProgress(HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			SysUser su = TokenManager.getToken();
			String progress = JedisUtils.get(su.getId() + "importProgress");
			if(StringUtil.isNotEmpty(progress)){
				j.setObj(progress);
			}else{
				j.setObj(0);
			}
		} catch (Exception e) {
			e.printStackTrace();     
			j.setSuccess(false);
			j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
		}
		return j;
	}
	
	/**
	 * 获取导入电表的进度
	 * @param assetMeter
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getBatchImportTempMeterProgress")
	@ResponseBody
	public AjaxJson getBatchImportTempMeterProgress(HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			SysUser su = TokenManager.getToken();
			String progress = JedisUtils.get(su.getId() + "importTempMeterProgress");
			if(StringUtil.isNotEmpty(progress)){
				j.setObj(progress);
			}else{
				j.setObj(0);
			}
		} catch (Exception e) {
			e.printStackTrace();     
			j.setSuccess(false);
			j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
		}
		return j;
	}
	
	/**
	 * 获取上传电表的进度
	 * @param assetMeter
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getBatchUploadTempMeterProgress")
	@ResponseBody
	public AjaxJson getBatchUploadTempMeterProgress(HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			SysUser su = TokenManager.getToken();
			String progress = JedisUtils.get(su.getId() + "checkMeterProgress");
			if(StringUtil.isNotEmpty(progress)){
				j.setObj(progress);
			}else{
				j.setObj(0);
			}
		} catch (Exception e) {
			e.printStackTrace();     
			j.setSuccess(false);
			j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
		}
		return j;
	}
	
	/**
	 * 企业园区的gis地图展示
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "baiduMap")
	public ModelAndView baiduMapDisplay(HttpServletRequest request,Model model) {
		 model.addAttribute("longitude",request.getParameter("longitude"));
		 model.addAttribute("latitude",request.getParameter("latitude"));
		 return new ModelAndView("/asset/baiduMap");
	}
	
	@RequestMapping(value = "setBingMap")
	public ModelAndView setBingMap(HttpServletRequest request,Model model) {
		model.addAttribute("longitude",request.getParameter("longitude"));
		model.addAttribute("latitude",request.getParameter("latitude"));
		return new ModelAndView("/asset/bingMap");
	}
	
	@RequestMapping(value = "getUploadTempList")
	@ResponseBody
	@SuppressWarnings("unchecked")
	public AjaxJson getUploadTempList(String uuid, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		
		try {
			SysUser su = TokenManager.getToken();
			// 获取之前上传的数据
			List<ImportAssetGprsMeter> templateList = (List<ImportAssetGprsMeter>) JedisUtils.getObject(uuid);
			JedisUtils.set(su.getId() + "importProgress", "0", 3600);//先清空原来的进度
			
			// 将数据传到前台
			j.put("templateList", templateList);
		} catch (Exception e) {
			e.printStackTrace();
			j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
			return j;
		}
		j.setMsg(MutiLangUtil.doMutiLang("system.operSucce"));
		
		return j;
	}

	@RequestMapping(value = "getRelateMeterDataGrid")
    @ResponseBody
    public JqGridResponseTo getRelateMeterDataGrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request,
    		String searchType, String meterSn,String commId) {
		SysUser su = TokenManager.getToken();
		List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
		List<AssetMeter> resultList=Lists.newArrayList();
		if("-1".equals(commId)) {
			commId="";
		}
		if(StringUtils.isNotEmpty(meterSn)||StringUtils.isNotEmpty(commId)) {	
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("meterSn", meterSn);
			params.put("commId", commId);
			params.put("orgIdList", orgIdList);
			params.put("comType", "NORMAL");//只查询DCU下通讯方式的表(PLC等) 
			if (searchType.equals("1")) {
				//从asset_meter表中查询已存档的正式表计档案
				resultList = this.assetMeterService.getListOfDCU(params);
			} else {
				//从asset_meter_prepare表中查询未归档的正式表计档案
				resultList = this.assetMeterService.getPreListOfDCU(params);
			}
		}
		
		JqGridResponseTo j= JqGridHandler.GetJqGridPageJsonData(new PageInfo<AssetMeter>(),jqGridSearchTo);
		j.setRows(resultList);
		return j;
	}
	
	
	/**
	 *把rf电表转换成gprs
	 * 
	 */
	@Transactional
	@RequestMapping(value = "changeComType")
	@ResponseBody
	public AjaxJson changeRfToGprs(HttpServletRequest request,String meterId,String commId,Integer indexDcu,String currComType) {
		AssetRefreshPort arp=(AssetRefreshPort)UciInterfaceUtil.getInterface("AssetRefreshPort", AssetRefreshPort.class,sysServiceAttributeService);
		List<RefreshMessage> msgs=new ArrayList<RefreshMessage>(); 
		
		SysUser su = TokenManager.getToken();
		AjaxJson j = new AjaxJson();
		AssetMeter assetMeter = this.assetMeterService.getEntity(meterId);
		if(assetMeter==null) {
			j.setErrorMsg("meter is null");
			return j;
		}
		
		RefreshMessage refMsg=new RefreshMessage();
		RefreshMessage refMsg1=new RefreshMessage();
		RefreshMessage refMsg2=new RefreshMessage();
		
		try {
			String comType=assetMeter.getComType();
			
			if((Integer.parseInt(comType)>=100&&Integer.parseInt(comType)<=199)) { //||(ac!=null&&ac.getDeviceType()==201)){
				//gprs转rf
				
				String oldCommId = assetMeter.getCommunicatorId();
				AssetCommunicator comm = this.assetCommunicatorService.getEntity(commId);
				//更新表计通讯类型
				assetMeter.setComType(currComType);
				assetMeter.setIndexDcu(indexDcu);
				assetMeter.setCommunicatorId(commId);
				assetMeter.setOrgId(comm.getOrgId());//把集中器的orgId，放入电表中
				this.assetMeterService.update(assetMeter);
				//删除原绑定关系
				
				assetRouterService.deleteById(oldCommId);
				assetCommunicatorService.deleteById(oldCommId);
				
				refMsg.setType("meter");
				refMsg.setOpType("update");
				refMsg.getIds().add(meterId);
				
				refMsg1.setType("communicator");
				refMsg1.setOpType("update");
				refMsg1.getIds().add(commId);
				
				refMsg2.setType("communicator");
				refMsg2.setOpType("delete");
				refMsg2.getIds().add(oldCommId);
				
			}else {
				//rf表转gprs
				
				//添加虚拟集中器
				AssetCommunicator assetCommunicator=new AssetCommunicator();
				assetCommunicator.setSn(assetMeter.getSn());
				assetCommunicator.setMac(assetMeter.getMac());
				assetCommunicator.setComType(currComType);
				assetCommunicator.setFwVersion(assetMeter.getFwVersion());
				assetCommunicator.setNetworkIp(assetMeter.getIpAddr());
				assetCommunicator.setNetworkPort(assetMeter.getIpPort()!=null?Integer.parseInt(assetMeter.getIpPort()):null);
				assetCommunicator.setModel(assetMeter.getModel());
				assetCommunicator.setManufacturer(assetMeter.getManufacturer());
				assetCommunicator.setSimNum(assetMeter.getSimNum());
				assetCommunicator.setHlsAk(assetMeter.getHlsAk());
				assetCommunicator.setHlsEk(assetMeter.getHlsEk());
				assetCommunicator.setAuthType(assetMeter.getAuthType());
				assetCommunicator.setPass(assetMeter.getPassword());
				assetCommunicator.setIsEncrypt(assetMeter.getIsEncrypt());
				
				assetCommunicator.setDeviceType(201);
				assetCommunicator.setUtilityId(su.getUtilityId());
				assetCommunicator.setOrgId(assetMeter.getOrgId());
				assetCommunicator.setRemoveFlag(0);
				
				assetMeter.setUtilityId(su.getUtilityId());
				
				//module 模块属性 固定值 update 不需要还原
				assetCommunicator.setAuthType("1");
				assetCommunicator.setPass("000000");
				assetCommunicator.setIsEncrypt(0);
				
				String acId = (String) assetCommunicatorService.save(assetCommunicator);
				String oldCommId = assetMeter.getCommunicatorId();//查询原来的 Router
				
				assetMeter.setCommunicatorId(acId);
				assetMeter.setComType(currComType);
				assetMeterService.update(assetMeter);
				AssetRouter tmpRouter=assetRouterService.getEntity(oldCommId);
				
				AssetRouter ar=new AssetRouter();
				ar.setCommunicatorId(acId);
				ar.setChannelId(tmpRouter.getChannelId());
				ar.setScheduleId(tmpRouter.getScheduleId());
				assetRouterService.save(ar);
				
				refMsg.setType("meter");
				refMsg.setOpType("update");
				refMsg.getIds().add(meterId);
				
				refMsg1.setType("communicator");
				refMsg1.setOpType("add");
				refMsg1.getIds().add(acId);
				
				refMsg2.setType("communicator");
				refMsg2.setOpType("update");
				refMsg2.getIds().add(oldCommId);
			}
			
			msgs.clear();
			msgs.add(refMsg);
			msgs.add(refMsg1);
			msgs.add(refMsg2);
			try{
				if(!arp.refresh(msgs)){
					System.out.println("刷新失败"+meterId);
				} 
			} catch (Exception e) {
				e.printStackTrace();
				j.setErrorMsg(MutiLangUtil.doMutiLang("system.UCIException"));
				return j;
			}
			j.setMsg(MutiLangUtil.doMutiLang("deviceList.changeSuccess"));
		
			
		}catch(Exception ex) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			j.setErrorMsg(ex.getMessage());
			ex.printStackTrace();
		}
		return j;
	}
	
	@RequestMapping(value = "publishToken")
    @ResponseBody
    public AjaxJson publishToken(String serialNumber, String token1, String newSgc, HttpServletRequest request) {
		AjaxJson ajax = new AjaxJson();
		
		SysUser sysUser = TokenManager.getToken();
		
		try {
			AssetMeter meter = new AssetMeter();
			meter.setSn(serialNumber);
			token1 = modifyToken(token1);
			
			// 下发TOKEN到电表中
			Map<String, Object> sendTokenResultMap = IEC61968_SendTokenToMeter(meter, sysUser, token1, request);
	        if (null != token1) {
	            token1 = token1.replace(" ", "");
	        }
            boolean sendTokenStatus = (boolean)sendTokenResultMap.get("status");
            if (sendTokenStatus) {
                ajax.setSuccess(true);
            } else { 
                // token下发失败的原因
                ajax.setSuccess(false);
                ajax.setMsg("Send Token" + " " + "Fail" + "\r\n" + "\r\n"
                            + "Reasons for failure:" + "\r\n" + sendTokenResultMap.get("errorMsg"));
            }
		}  catch (Exception e) {
            e.printStackTrace();
            ajax.setSuccess(false);
            ajax.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
		return ajax;
	}
	
	@RequestMapping(value = "batchPublishToken")
	@ResponseBody
	public AjaxJson batchPublishToken(String items, HttpServletRequest request) {
		AjaxJson ajax = new AjaxJson();
		SysUser sysUser = TokenManager.getToken();
		try {
			com.alibaba.fastjson.JSONArray curArray = null;
			StringBuffer message= new StringBuffer();
			if(StringUtils.isNotEmpty(items)) {
				curArray= JSON.parseArray(items);
				if(curArray!=null) {
					for(int i=0;i<curArray.size();i++) {
						com.alibaba.fastjson.JSONObject jsonRestTemp = curArray.getJSONObject(i);
						String sn = jsonRestTemp.getString("sn");
						String token1 = jsonRestTemp.getString("token");
						AssetMeter meter = new AssetMeter();
						meter.setSn(sn);
						token1 = modifyToken(token1);
						Map<String, Object> sendTokenResultMap = IEC61968_SendTokenToMeter(meter, sysUser, token1, request);
						boolean sendTokenStatus = (boolean)sendTokenResultMap.get("status");
						if (!sendTokenStatus) {
							message.append(sendTokenResultMap.get("errorMsg")+";");
						}
					}
				}
			}
			
			if(message.length()>0) {
				ajax.setSuccess(false);
				ajax.setMsg(message.toString());
			}else {
				ajax.setSuccess(true);
			}
			
			
		}catch (Exception e) {
			e.printStackTrace();
			ajax.setSuccess(false);
			ajax.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
		}
		return ajax;
	}
	
	private Map<String, Object> IEC61968_SendTokenToMeter(AssetMeter meter, SysUser sysUser, String token,HttpServletRequest request){
		 Map<String, Object> resultMap = new HashMap<>();
	        if (null != token) {
	            token = token.replace(" ", "");
	        }
		
		String path = request.getContextPath();
        String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()+ path;
//        RequestEndDeviceControlsPort requestEndDeviceControlsPort = (RequestEndDeviceControlsPort)SpringContextUtil.getBean("RequestEndDeviceControlsPort");
        RequestEndDeviceControlsPort requestEndDeviceControlsPort = (RequestEndDeviceControlsPort) UciInterfaceUtil
        		.getInterface("RequestEndDeviceControlsPort", RequestEndDeviceControlsPort.class, sysServiceAttributeService);
        EndDeviceControlsRequestMessageType createEndDeviceControlsRequestMessage = new EndDeviceControlsRequestMessageType();
		
        // Header
        HeaderType headerType = new HeaderType();
        Date date = new Date();
        headerType.setVerb("create");
        headerType.setNoun("EndDeviceControls");
        headerType.setTimestamp(DateUtils.dateToXmlDate(date));
        headerType.setSource("ClouESP PPM");
        headerType.setAsyncReplyFlag(true);
        headerType.setAckRequired(true);
        headerType.setReplyAddress(basePath);
        String messageId = UUID.randomUUID().toString().replace("-", "");
        headerType.setMessageID(messageId);
        headerType.setReplyAddress(basePath + "/interfaces/ReplyEndDeviceControlsPort?wsdl"); // UCI回调地址
        resultMap.put("messageId", messageId);
        
        UserType userType = new UserType();
        userType.setOrganization(sysUser.getOrgId()); // 操作员组织机构
        userType.setUserID(sysUser.getId()); // 操作员ID
        headerType.setUser(userType);
        // payload
        EndDeviceControlsPayloadType payload = new EndDeviceControlsPayloadType();
        EndDeviceControls endDeviceControls = new EndDeviceControls();
        EndDeviceControl endDeviceControl = new EndDeviceControl();
        endDeviceControl.setReason("SendTokens");
        EndDeviceControlType endDeviceControlType = new EndDeviceControlType();
        endDeviceControlType.setRef("************"); // dataitemId
        endDeviceControl.setEndDeviceControlType(endDeviceControlType);
        
        EndDevices endDevices = new EndDevices();
        endDevices.setMRID(meter.getSn());
        
        // 保存token
        Names names = new Names();
        ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl.EndDevices.Names.NameType nameType = new ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl.EndDevices.Names.NameType();
        nameType.setName(token);
        nameType.setDescription(UUID.randomUUID().toString().replace("-", ""));
        names.setNameType(nameType);
        endDevices.getNames().add(names);
        
        endDeviceControl.getEndDevices().add(endDevices);
        endDeviceControls.getEndDeviceControl().add(endDeviceControl);
        payload.setEndDeviceControls(endDeviceControls);
        
        // request
        RequestType requestType = null;
        createEndDeviceControlsRequestMessage.setHeader(headerType);
        createEndDeviceControlsRequestMessage.setPayload(payload);
        createEndDeviceControlsRequestMessage.setRequest(requestType);
        
        // token下发的数据转成XML
        String messageDetail = XMLUtil.convertToXml(createEndDeviceControlsRequestMessage);
        System.out.println(messageDetail);
        
        EndDeviceControlsResponseMessageType responseMessageType;
        
        // 调用接口，下发token
        try {
			responseMessageType = requestEndDeviceControlsPort.createEndDeviceControls(createEndDeviceControlsRequestMessage);
			ReplyType replyType = responseMessageType.getReply();
			String relayResult = replyType.getResult();
			List<ErrorType> errorCodes = replyType.getError();
			ErrorType errorType = errorCodes.get(0);
			String code = errorType.getCode();
			String messageResult = null;
			
			if ("OK".equals(relayResult) && ("0.0".equals(code) || "0.3".equals(code))) {
                resultMap.put("status", true);
                messageResult = replyType.getResult();
            }
            else {
                resultMap.put("status", false);
                messageResult = replyType.getResult();
                resultMap.put("errorMsg", messageResult);
            }
            return resultMap;
		} catch (FaultMessage e) {
			e.printStackTrace();
		}
        
        return resultMap;
	} 
	
	private String modifyToken(String token) {
        return token.replace(" ", "");
    }
	
}