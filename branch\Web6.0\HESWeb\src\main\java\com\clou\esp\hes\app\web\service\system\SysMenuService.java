/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysMenu{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-10-27 08:02:17
 * 最后修改时间：
 * 
*********************************************************************************************************/

package com.clou.esp.hes.app.web.service.system;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.clou.esp.hes.app.web.service.common.CommonService;
import com.clou.esp.hes.app.web.model.dict.DictMenu;
import com.clou.esp.hes.app.web.model.system.SysRole;
import com.clou.esp.hes.app.web.model.system.SysRoleMenu;
import com.clou.esp.hes.app.web.model.system.SysUser;

public interface SysMenuService extends CommonService<DictMenu>{
	/**
	 * 获取当前用户菜单权限
	 * @param id
	 * @return
	 */
	public List<DictMenu> getMenusByUserId(SysUser su);
	
	/**
	 * 获取当前用户菜单URL list
	 * @param id
	 * @return
	 */
	public List<String> getMenusUrlUserId(SysUser su);
	
	/**
	 * 获取菜单TAB隐藏项
	 * @param id
	 * @return
	 */
	public Map<String,String> getMenusHideTab();
	
	/**
	 * 根据role获取所有菜单
	 * @param id
	 * @return
	 */
	public List<DictMenu> getMenuListByRole(SysRole entity);

	/**
	 * 返回角色对应菜单
	 * @param userId
	 * @return
	 */
	public Set<String> getPermissionByRoleId(String roleId);
}