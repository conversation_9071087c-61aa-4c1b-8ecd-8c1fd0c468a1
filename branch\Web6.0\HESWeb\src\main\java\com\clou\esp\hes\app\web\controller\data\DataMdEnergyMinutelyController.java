/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataMeterDataEnergyMinutely{ } 
 * 
 * 摘    要： dataMeterDataEnergyMinutely
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-24 03:20:08
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import clouesp.hes.common.logger.loggerquery.LoggerLevel;
import clouesp.hes.common.logger.loggerquery.LoggerQuery;
import clouesp.hes.common.logger.loggerquery.QueryResult;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.Configuration;
import com.clou.esp.hes.app.web.core.util.CreatePdf;
import com.clou.esp.hes.app.web.core.util.ExcelUtils;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.data.DataIntegrityDetails;
import com.clou.esp.hes.app.web.model.data.DataMdEnergyMinutely;
import com.clou.esp.hes.app.web.model.data.DataReadsLog;
import com.clou.esp.hes.app.web.model.system.SysService;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.data.DataMdEnergyMinutelyService;
import com.clou.esp.hes.app.web.service.system.SysServiceService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.excel.ExcelDataFormatter;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;

/**
 * <AUTHOR>
 * @时间：2017-11-24 03:20:08
 * @描述：dataMeterDataEnergyMinutely类
 */
@Controller
@RequestMapping("/dataMdEnergyMinutelyController")
public class DataMdEnergyMinutelyController extends BaseController{

	@Resource
    private SysServiceService sysServiceService;
 	@Resource
    private DataMdEnergyMinutelyService dataMeterDataEnergyMinutelyService;
 	@Resource
    private AssetMeterService assetMeterService;

	/**
	 * 跳转到dataMeterDataEnergyMinutely列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/data/dataMdEnergyMinutelyList");
    }
	/**
	 * 抄表率详情
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dataTracingDetails")
    public ModelAndView dataTracingDetails(String deviceId,String date,HttpServletRequest request, Model model) {
		model.addAttribute("deviceId", deviceId);
		model.addAttribute("date", date);
		List<SysService> list= sysServiceService.getAllList();
		String serviceReplac =RoletoJson.listToReplaceStr(list, "id", "introduction");
		model.addAttribute("serviceReplac", serviceReplac);
        return new ModelAndView("/data/dataTracingDetails");
    }
	
	/**
	 * 查询抄读率详情
	 * @param jqGridSearchTo
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "dtDetailsdatagrid")
    @ResponseBody
    public JqGridResponseTo dtDetailsdatagrid(String deviceId,String date,JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
		jqGridSearchTo.setRows(99999);
		TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        Map<String,Object> m=jqGridSearchTo.getMap();
        m.put("deviceId", deviceId);
        m.put("date",date);
        JqGridResponseTo j=null;
        try {
             j=dataMeterDataEnergyMinutelyService.getIntegrityDetailsForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
	
	 /**
	  * 导出
	  * @param deviceId
	  * @param date
	  * @param dataIntegrityDetails
	  * @param request
	  * @param response
	  */
	 @RequestMapping(value = "getExcelIntegrityDetails")
	 @ResponseBody
	 public void getExcelIntegrityDetails(String deviceId,String date,DataIntegrityDetails dataIntegrityDetails,HttpServletRequest request, HttpServletResponse response) {
		 try {
		 	Map<String,Object> extData=new HashMap<String, Object>();
		 	extData.put("deviceId", deviceId);
		 	extData.put("date", date);
		 	dataIntegrityDetails.setExtData(extData);
	        List<DataIntegrityDetails> list=dataMeterDataEnergyMinutelyService.getDataIntegrityDetailList(dataIntegrityDetails);
	        ExcelDataFormatter edf = new ExcelDataFormatter();
	        Map<String,String> trt=new HashMap<String, String>();
	        trt.put("1", "Done");
	        trt.put("0", "Missing");
	        edf.set("readStatus", trt);
	        //要日期格式化使用以下方式
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("tv", "MM/dd/yyyy HH:mm:ss");
	        edf.set("tv", tvs);
			ExcelUtils.writeToFile(list, edf, "dataIntegrityDetailList.xlsx", response,
						ValidGroup1.class);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
	 }
	 
	 /**
	  * 导出
	  * @param deviceId
	  * @param date
	  * @param dataIntegrityDetails
	  * @param request
	  * @param response
	  */
	 @RequestMapping(value = "getPringIntegrityDetails")
	 @ResponseBody
	 public void getPringIntegrityDetails(String deviceId,String date,DataIntegrityDetails dataIntegrityDetails,HttpServletRequest request, HttpServletResponse response) {
		 try {
		 	Map<String,Object> extData=new HashMap<String, Object>();
		 	extData.put("deviceId", deviceId);
		 	extData.put("date", date);
		 	dataIntegrityDetails.setExtData(extData);
	        List<DataIntegrityDetails> list=dataMeterDataEnergyMinutelyService.getDataIntegrityDetailList(dataIntegrityDetails);
	        ExcelDataFormatter edf = new ExcelDataFormatter();
	        Map<String,String> trt=new HashMap<String, String>();
	        trt.put("1", "Done");
	        trt.put("0", "Missing");
	        edf.set("readStatus", trt);
	        //要日期格式化使用以下方式
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("tv", "MM/dd/yyyy HH:mm:ss");
	        edf.set("tv", tvs);
			CreatePdf.printPdf(list, edf, ValidGroup1.class, request, response);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
	 }
	 
 	/**
 	 * 查询Reads日志
 	 * @param deviceId
 	 * @param date
 	 * @param jqGridSearchTo
 	 * @param request
 	 * @return
 	 */
 	@RequestMapping(value = "dataLogdatagrid")
    @ResponseBody
    public JqGridResponseTo dataLogdatagrid(String deviceId,String date,JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        Map<String,Object> m=jqGridSearchTo.getMap();
        m.put("deviceId", deviceId);
        m.put("date",date);
        JqGridResponseTo j=null;
        try {
        	List<String> device=new ArrayList<String>();
        	if(StringUtil.isNotEmpty(deviceId)){
    		AssetMeter meter=assetMeterService.getEntity(deviceId);
	    		if(meter!=null){
	    			device.add(meter.getId());
	    			device.add(meter.getCommunicatorId());
	    		}
    		}
        	Date d=DateUtils.parseDate(date, "yyyy-MM-dd HH:mm:ss");
             String dStr=DateUtils.formatDate(d, "yyyy-MM-dd");
             Date sDate = DateUtils.parseDate(dStr+" 00:00:00", "yyyy-MM-dd HH:mm:ss");
             Date eDate = DateUtils.parseDate(dStr+" 23:59:59", "yyyy-MM-dd HH:mm:ss");
             int count = LoggerQuery.getInstance().getQueryCount(sDate, eDate, null, null, device,null, null);
             Vector<QueryResult> queryResults = LoggerQuery.getInstance().query(jqGridSearchTo.getPage(), PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows()).getPageSize(), 
            		 sDate, eDate, null, null, device, null, null);
    		List<DataReadsLog> list = new ArrayList<DataReadsLog>();
    		for(int i=0;i<queryResults.size();i++){
    			QueryResult qr=queryResults.get(i);
    			DataReadsLog drl=new DataReadsLog();
    			drl.setId(""+(i+1));
    			drl.setDate(qr.gettime());
    			drl.setServiceId(qr.getserviceId());
    			drl.setType(qr.getinfoType());
    			drl.setContent(qr.getinfo().replace("\r\n", ""));
    			drl.setLevel(qr.getlevel());
    			list.add(drl);
    		}
    		PageInfo<DataReadsLog> pageInfo = new PageInfo<DataReadsLog>(list);
    		pageInfo.setList(list);
    		//页数
    		int pages=count/PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows()).getPageSize();
    		if(count%PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows()).getPageSize()>0){
    			pages+=1;
    		}
    		j=JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
    		j.setRecords(count);
    		j.setTotal(pages);
    		j.setPage(jqGridSearchTo.getPage());
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
 	
 	

	 /**
	  * 导出
	  * @param deviceId
	  * @param date
	  * @param dataIntegrityDetails
	  * @param request
	  * @param response
	  */
	 @RequestMapping(value = "getExportDataLog")
	 @ResponseBody
	 public void getExportDataLog(String deviceId,String date,DataIntegrityDetails dataIntegrityDetails,HttpServletRequest request, HttpServletResponse response) {
		 try {
			List<String> device=new ArrayList<String>();
        	if(StringUtil.isNotEmpty(deviceId)){
    		AssetMeter meter=assetMeterService.getEntity(deviceId);
	    		if(meter!=null){
	    			device.add(meter.getId());
	    			device.add(meter.getCommunicatorId());
	    		}
    		}
        	Date d=DateUtils.parseDate(date, "yyyy-MM-dd HH:mm:ss");
             String dStr=DateUtils.formatDate(d, "yyyy-MM-dd");
             Date sDate = DateUtils.parseDate(dStr+" 00:00:00", "yyyy-MM-dd HH:mm:ss");
             Date eDate = DateUtils.parseDate(dStr+" 23:59:59", "yyyy-MM-dd HH:mm:ss");
             int count = LoggerQuery.getInstance().getQueryCount(sDate, eDate, null, null, device,null, null);
             Vector<QueryResult> queryResults = LoggerQuery.getInstance().query(1, count, 
            		 sDate, eDate, null, null, device, null, null);
    		List<DataReadsLog> list = new ArrayList<DataReadsLog>();
    		for(int i=0;i<queryResults.size();i++){
    			QueryResult qr=queryResults.get(i);
    			DataReadsLog drl=new DataReadsLog();
    			drl.setId(""+(i+1));
    			drl.setDate(qr.gettime());
    			drl.setServiceId(qr.getserviceId());
    			drl.setType(qr.getinfoType());
    			drl.setContent(qr.getinfo());
    			drl.setLevel(qr.getlevel());
    			list.add(drl);
    		}
	        ExcelDataFormatter edf = new ExcelDataFormatter();
	        //要日期格式化使用以下方式
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("date", "MM/dd/yyyy HH:mm:ss");
	        edf.set("date", tvs);
			ExcelUtils.writeToFile(list, edf, "dataLogList.xlsx", response,
						ValidGroup1.class);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
	 }
	 
	 /**
	  * 导出
	  * @param deviceId
	  * @param date
	  * @param dataIntegrityDetails
	  * @param request
	  * @param response
	  */
	 @RequestMapping(value = "getPringDataLog")
	 @ResponseBody
	 public void getPringDataLog(String deviceId,String date,DataIntegrityDetails dataIntegrityDetails,HttpServletRequest request, HttpServletResponse response) {
		 try {
			List<String> device=new ArrayList<String>();
        	if(StringUtil.isNotEmpty(deviceId)){
    		AssetMeter meter=assetMeterService.getEntity(deviceId);
	    		if(meter!=null){
	    			device.add(meter.getId());
	    			device.add(meter.getCommunicatorId());
	    		}
    		}
        	Date d=DateUtils.parseDate(date, "yyyy-MM-dd HH:mm:ss");
             String dStr=DateUtils.formatDate(d, "yyyy-MM-dd");
             Date sDate = DateUtils.parseDate(dStr+" 00:00:00", "yyyy-MM-dd HH:mm:ss");
             Date eDate = DateUtils.parseDate(dStr+" 23:59:59", "yyyy-MM-dd HH:mm:ss");
             int count = LoggerQuery.getInstance().getQueryCount(sDate, eDate, null, null, device,null, null);
             Vector<QueryResult> queryResults = LoggerQuery.getInstance().query(1, count, 
            		 sDate, eDate, null, null, device, null, null);
    		List<DataReadsLog> list = new ArrayList<DataReadsLog>();
    		for(int i=0;i<queryResults.size();i++){
    			QueryResult qr=queryResults.get(i);
    			DataReadsLog drl=new DataReadsLog();
    			drl.setId(""+(i+1));
    			drl.setDate(qr.gettime());
    			drl.setServiceId(qr.getserviceId());
    			drl.setType(qr.getinfoType());
    			drl.setContent(qr.getinfo());
    			drl.setLevel(qr.getlevel());
    			list.add(drl);
    		}
	        ExcelDataFormatter edf = new ExcelDataFormatter();
	        //要日期格式化使用以下方式
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("date", "MM/dd/yyyy HH:mm:ss");
	        edf.set("date", tvs);
	        CreatePdf.printPdf(list, edf, ValidGroup1.class, request, response);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
	 }

	/**
	 * 跳转到dataMeterDataEnergyMinutely新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dataMeterDataEnergyMinutely")
	public ModelAndView dataMeterDataEnergyMinutely(DataMdEnergyMinutely dataMeterDataEnergyMinutely,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dataMeterDataEnergyMinutely.getId())){
			try {
                dataMeterDataEnergyMinutely=dataMeterDataEnergyMinutelyService.getEntity(dataMeterDataEnergyMinutely.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dataMeterDataEnergyMinutely", dataMeterDataEnergyMinutely);
		}
		return new ModelAndView("/data/dataMeterDataEnergyMinutely");
	}


	/**
	 * dataMeterDataEnergyMinutely查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=dataMeterDataEnergyMinutelyService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除dataMeterDataEnergyMinutely信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DataMdEnergyMinutely dataMeterDataEnergyMinutely,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dataMeterDataEnergyMinutelyService.deleteById(dataMeterDataEnergyMinutely.getId())>0){
                j.setMsg("删除成功");
            }else{
                j.setSuccess(false);
                j.setMsg("删除失败");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
    
    /**
     * 保存dataMeterDataEnergyMinutely信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DataMdEnergyMinutely dataMeterDataEnergyMinutely,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DataMdEnergyMinutely t=new  DataMdEnergyMinutely();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dataMeterDataEnergyMinutely.getId())){
        	t=dataMeterDataEnergyMinutelyService.getEntity(dataMeterDataEnergyMinutely.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dataMeterDataEnergyMinutely, t);
				dataMeterDataEnergyMinutelyService.update(t);
				j.setMsg("修改成功");
				
			}else{
	            dataMeterDataEnergyMinutelyService.save(dataMeterDataEnergyMinutely);
	            j.setMsg("创建成功");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
	
	
	
}