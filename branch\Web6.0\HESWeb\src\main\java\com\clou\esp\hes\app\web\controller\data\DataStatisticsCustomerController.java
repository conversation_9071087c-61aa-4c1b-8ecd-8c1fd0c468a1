/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataStatisticsCustomer{ } 
 * 
 * 摘    要： dataStatisticsCustomer
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-11-20 06:36:35
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.util.CreatePdf;
import com.clou.esp.hes.app.web.core.util.ExcelUtils;
import com.clou.esp.hes.app.web.model.data.DataStatisticsCustomer;
import com.clou.esp.hes.app.web.model.dict.DictCustomerIndustry;
import com.clou.esp.hes.app.web.model.dict.DictCustomerType;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.asset.AssetCustomerService;
import com.clou.esp.hes.app.web.service.data.DataStatisticsCustomerService;
import com.clou.esp.hes.app.web.service.system.SysOrgService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.google.common.collect.Maps;
import com.power7000g.core.excel.ExcelDataFormatter;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

/**
 * <AUTHOR>
 * @时间：2018-11-20 06:36:35
 * @描述：dataStatisticsCustomer类
 */
@Controller
@RequestMapping("/dataStatisticsCustomerController")
public class DataStatisticsCustomerController extends BaseController{

 	@Resource
    private DataStatisticsCustomerService dataStatisticsCustomerService;
 	@Resource
 	private SysOrgService   			  sysOrgService;
 	@Resource
 	private AssetCustomerService          assetCustomerService;


	
	/**
	 * dataStatisticsCustomer查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "report")
    @ResponseBody
    public JqGridResponseTo report(JqGridSearchTo jqGridSearchTo,String orgId,String timeType,String startTime
    		,String endTime,String customerType,String industryType,String timePattern,HttpServletRequest request) {
        JqGridResponseTo j=null;
        try {
        	this.commJqGrid(jqGridSearchTo, orgId, timeType, startTime, endTime, customerType, industryType, timePattern, request);
            j=dataStatisticsCustomerService.findDataStatReport(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
	
	@RequestMapping(value = "export")
	@ResponseBody
	public void export(JqGridSearchTo jqGridSearchTo,String orgId,String timeType,String startTime
    		,String endTime,String customerType,String industryType,String timePattern,HttpServletRequest request,HttpServletResponse response) {
	    try {
	    	this.commJqGrid(jqGridSearchTo, orgId, timeType, startTime, endTime, customerType, industryType, timePattern, request);
	    	List<DataStatisticsCustomer> dataStatList = dataStatisticsCustomerService.findDataStatReportList(jqGridSearchTo);
	    	replaceName(dataStatList, (String)jqGridSearchTo.getMap().get("idType"), (String)jqGridSearchTo.getMap().get("idValue"), customerType);
	    	ExcelDataFormatter edf = new ExcelDataFormatter();
	        //要日期格式化使用以下方式
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("date", "MM/dd/yyyy");
	        edf.set("date", tvs);
	        ExcelUtils.writeToFile(dataStatList, edf, "dataImportAndExportList.xlsx", response);
		} catch (Exception e) {
			e.printStackTrace();
		}
    }
	
	@RequestMapping(value = "print")
	@ResponseBody
	public void print(JqGridSearchTo jqGridSearchTo,String orgId,String timeType,String startTime
    		,String endTime,String customerType,String industryType,String timePattern,HttpServletRequest request,HttpServletResponse response){
	    try {
	    	this.commJqGrid(jqGridSearchTo, orgId, timeType, startTime, endTime, customerType, industryType, timePattern, request);
	    	List<DataStatisticsCustomer> dataStatList = dataStatisticsCustomerService.findDataStatReportList(jqGridSearchTo);
	    	replaceName(dataStatList, (String)jqGridSearchTo.getMap().get("idType"), (String)jqGridSearchTo.getMap().get("idValue"), customerType);
	    	ExcelDataFormatter edf = new ExcelDataFormatter();
	        //要日期格式化使用以下方式
	        CreatePdf.printPdf(dataStatList, null, ValidGroup1.class, request, response);
		} catch (Exception e) {
			e.printStackTrace();
		}
    }

	
	 public JqGridSearchTo commJqGrid(JqGridSearchTo jqGridSearchTo,String orgId,String timeType,String startTime
	    		,String endTime,String customerType,String industryType,String timePattern,HttpServletRequest request) {
	        	if(StringUtils.isEmpty(orgId)) {
	        		 SysUser su=TokenManager.getToken();
	        		 orgId=su.getOrgId();
	        	}
	        	jqGridSearchTo.put("timeType", timeType);
	        	jqGridSearchTo.put("timePattern", timePattern);
	        	jqGridSearchTo.put("startTime", startTime);
	        	jqGridSearchTo.put("endTime", endTime);
	        	jqGridSearchTo.put("customerType", customerType);
	        	jqGridSearchTo.put("industryType", industryType);
	        	jqGridSearchTo.put("orgId", orgId);
	        	
	        	String idType="";
	        	String idValue="";
	        	if(StringUtils.isEmpty(customerType)&&StringUtils.isEmpty(industryType)){
	        		idType="11";//总数
	        	}else if(StringUtils.isNotEmpty(customerType)&&StringUtils.isEmpty(industryType)) {
	        		idType="12";//分行业
	        		idValue=customerType;
	        	}else if(StringUtils.isNotEmpty(customerType)&&StringUtils.isNotEmpty(industryType)) {
	        		idType="13";//分行业属性
	        		idValue=industryType;
	        	}
	        	
	        	
	        	jqGridSearchTo.put("idType", idType);
	        	jqGridSearchTo.put("idValue", idValue);
	        	return jqGridSearchTo;
	        }
   //添加名字
	 public void  replaceName(List<DataStatisticsCustomer> dataStatList,String idType,String idValue,String cutomerTypeId) {
		  	Map<String,String> customerMap = Maps.newHashMap();
	    	Map<String,String> industryMap = Maps.newHashMap();
	    	List<DictCustomerType> dictCustomerTypes=assetCustomerService.getDictCustomerType();
			List<DictCustomerIndustry> dictCustomerIndustrys=assetCustomerService.getDictCustomerIndustry();
			if(dictCustomerTypes!=null) {
				for(DictCustomerType cusotmer:dictCustomerTypes) {
					customerMap.put(cusotmer.getId(), cusotmer.getName());
				}
			}
			if(dictCustomerIndustrys!=null) {
				for(DictCustomerIndustry industry:dictCustomerIndustrys) {
					industryMap.put(industry.getId(), industry.getName());
				}
			}
		
			if(dataStatList!=null) {
				if("12".equals(idType)) {
					String customerType = customerMap.get(idValue);
					for(DataStatisticsCustomer stat:dataStatList) {
						stat.setCustomerType(customerType);
						stat.setTvType(stat.getTvType());
					}
				}else if("13".equals(idType)) {
					String customerType=customerMap.get(cutomerTypeId);
					String industryType = customerMap.get(idValue);
					for(DataStatisticsCustomer stat:dataStatList) {
						stat.setIndustryType(industryType);
						stat.setCustomerType(customerType);
						stat.setTvType(stat.getTvType());
					}
				}else if("11".equals(idType)) {
					for(DataStatisticsCustomer stat:dataStatList) {
						stat.setTvType(stat.getTvType());
					}
				}
			}
	 }
	 
	 
}