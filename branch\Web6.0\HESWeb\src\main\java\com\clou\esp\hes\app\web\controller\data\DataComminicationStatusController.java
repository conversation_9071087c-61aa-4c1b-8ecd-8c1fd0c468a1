/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataComminicationStatus{ } 
 * 
 * 摘    要： dataComminicationStatus
 * 版    本：1.0
 * 作    者：赵久霆
 * 创建于：2018-11-19 07:34:34
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.data;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.ExcelUtils;
import com.clou.esp.hes.app.web.core.util.OrganizationUtils;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
//import com.mysql.fabric.xmlrpc.base.Array;
import com.power7000g.core.excel.Excel;
import com.power7000g.core.excel.ExcelDataFormatter;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.data.DataComminicationStatus;
import com.clou.esp.hes.app.web.model.dict.DictCommunicationType;
import com.clou.esp.hes.app.web.model.report.MeterDataReport;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.data.DataComminicationStatusService;
import com.clou.esp.hes.app.web.service.dict.DictCommunicationTypeService;
import com.clou.esp.hes.app.web.service.system.SysOrgService;

/**
 * <AUTHOR>
 * @时间：2018-11-19 07:34:34
 * @描述：dataComminicationStatus类
 */
@Controller
@RequestMapping("/dataComminicationStatusController")
public class DataComminicationStatusController extends BaseController{

 	@Resource
    private DataComminicationStatusService 	 dataComminicationStatusService;
 	@Resource
 	private DictCommunicationTypeService	 dictCommunicationTypeService;
	@Resource
 	private AssetMeterService	 			 assetMeterService;
	@Resource
	private SysOrgService                    sysOrgService;

	/**
	 * 跳转到dataComminicationStatus列表页面
	 *
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model,String deviceType) {
		List<DictCommunicationType> dctl = dictCommunicationTypeService.getAllList();
		String comms = RoletoJson.listToReplaceStr(dctl, "id", "name");
		model.addAttribute("commTypeReplace", comms);
		model.addAttribute("deviceType", deviceType);
        return new ModelAndView("/data/dataComminicationStatusList");
    }

	/**
	 * 跳转到dataComminicationStatus新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dataComminicationStatus")
	public ModelAndView dataComminicationStatus(DataComminicationStatus dataComminicationStatus,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dataComminicationStatus.getId())){
			try {
                dataComminicationStatus=dataComminicationStatusService.getEntity(dataComminicationStatus.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dataComminicationStatus", dataComminicationStatus);
		}
		return new ModelAndView("/data/dataComminicationStatus");
	}


	/**
	 * dataComminicationStatus查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(String deviceType,String deviceId,String leftSearchType,String orgId,
    		String comStatus,String offlineDays, JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
        	if("1".equals(deviceType)) {
        		//meter
        		if(StringUtils.isEmpty(leftSearchType)||"1".equals(leftSearchType)) {
        			//meter
        			jqGridSearchTo.put("type", "1");
        		}else{
        			//查询comm下所有meter
        			jqGridSearchTo.put("type", "2");
        		}
        	}else if("2".equals(deviceType)) {
        		//查询comm
    			jqGridSearchTo.put("type", "2");
        		if("1".equals(leftSearchType)) {//如果左边是电表，查出电表的集中器
        			AssetMeter meter=assetMeterService.getEntity(deviceId);
        			deviceId=meter.getCommunicatorId();
        		}
        	}
        	jqGridSearchTo.put("deviceType", deviceType);
        	jqGridSearchTo.put("deviceId", deviceId);
        	jqGridSearchTo.put("comStatus", comStatus);
        	
        	if(StringUtils.isNotEmpty(offlineDays)) {
        		jqGridSearchTo.put("offlineTime",DateUtils.formatDate(DateUtils.getDateAdd(new Date(), Calendar.DATE, -Integer.parseInt(offlineDays)),"yyyy-MM-dd"));
        	}
        	List<String> orgIdList=OrganizationUtils.getOrgIds(sysOrgService,orgId);
        	jqGridSearchTo.put("orgIds", orgIdList);
            j=dataComminicationStatusService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
	
	/**
	 * dataComminicationStatus查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "getExcelData")
    @ResponseBody
    public void getExcelData(String deviceType,String deviceId,String leftSearchType,String comStatus,String orgId,
    		String offlineDays,HttpServletRequest request,HttpServletResponse response) {
        String type="";
	    Map<String,Object> m =Maps.newHashMap();
        try {
        	String deviceTypeStr="Meter";
        	if("1".equals(deviceType)) {
        		//meter
        		if(StringUtils.isEmpty(leftSearchType)||"1".equals(leftSearchType)) {
        			//meter
        			type= "1";
        		}else {
        			//查询comm下所有meter
        			type= "2";
        		}
        	}else if("2".equals(deviceType)) {
        		deviceTypeStr="Communicator";
        		//查询comm
    			type="2";
        		if("1".equals(leftSearchType)) {//如果左边是电表，查出电表的集中器
        			AssetMeter meter=assetMeterService.getEntity(deviceId);
        			deviceId=meter.getCommunicatorId();
        		}
        	}
        	
        	if(StringUtils.isNotEmpty(offlineDays)) {
        		m.put("offlineTime",DateUtils.formatDate(DateUtils.getDateAdd(new Date(), Calendar.DATE, -Integer.parseInt(offlineDays)),"yyyy-MM-dd"));
        	}
        	
        	List<String> orgIdList=OrganizationUtils.getOrgIds(sysOrgService,orgId);
    		m.put("orgIds", orgIdList);
			m.put("type",type);  
			m.put("deviceType", deviceType);
			m.put("deviceId", deviceId);
        	m.put("comStatus", comStatus);
        												
        	List<DataComminicationStatus> list = this.dataComminicationStatusService.getListData(m);
        	
        	List<DictCommunicationType> dctl = dictCommunicationTypeService.getAllList();
        	Map<String,String> typeMap = Maps.newHashMap();
        	if(dctl!=null) {
        		for(DictCommunicationType commType:dctl) {
        			typeMap.put(commType.getId(), commType.getName());
        		}
        	}
        	
        	if(list!=null) {
        		for(DataComminicationStatus comm:list) {
        			String commStatus = comm.getComStatus();
        			if("1".equals(commStatus)) {
        				comm.setComStatus("onLine");
        			}else {
        				comm.setComStatus("offLine");
        			}
        			
        			if(StringUtils.isNotEmpty(comm.getIpAddr())&&StringUtils.isNotEmpty(comm.getIpPort())) {
        				comm.setNetworkAddress(comm.getIpAddr()+":"+comm.getIpPort());
        			}
        			comm.setDeviceType(deviceTypeStr);
        			comm.setCommComType(typeMap.get(comm.getCommComType()));
        			comm.setMeterComType(typeMap.get(comm.getMeterComType()));
        		}
        	}
        	
        	Map<String,String> tvs=Maps.newHashMap();			
        	ExcelDataFormatter edf = new ExcelDataFormatter();
			tvs.put("updateTv", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
		    edf.set("updateTv", tvs);				        	
        	ExcelUtils.writeToFile(list, edf,"Communication Status Report.xlsx",response);
        }catch (Exception e) {
            e.printStackTrace();
        }
    }
    
	
    /**
     * 删除dataComminicationStatus信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DataComminicationStatus dataComminicationStatus,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dataComminicationStatusService.deleteById(dataComminicationStatus.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存dataComminicationStatus信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DataComminicationStatus dataComminicationStatus,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DataComminicationStatus t=new  DataComminicationStatus();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dataComminicationStatus.getId())){
        	t=dataComminicationStatusService.getEntity(dataComminicationStatus.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dataComminicationStatus, t);
				dataComminicationStatusService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            dataComminicationStatusService.save(dataComminicationStatus);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
	
	
 /**
     * 删除dataComminicationStatus信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "getCustomerStatus")
    @ResponseBody
    public AjaxJson getCustomerStatus(HttpServletRequest request,@RequestParam(value = "sns[]") String[] sns) {
        AjaxJson j=new AjaxJson();
        try {
        	if(sns!=null&&sns.length>0) {
        		List<DataComminicationStatus> statusList=dataComminicationStatusService.getCommStatus(Arrays.asList(sns));
                List<String> snList=null;
                if(statusList!=null) {
                	snList= Lists.newArrayList();
                	for(DataComminicationStatus status:statusList) {
                		snList.add(status.getCommSn());
                	}
                }
                j.setObj(snList);
        	}
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg("Abnormal operation");
        }
        return j;
    }
	
}