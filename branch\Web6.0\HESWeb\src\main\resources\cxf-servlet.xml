<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jaxws="http://cxf.apache.org/jaxws"
	xsi:schemaLocation=" http://www.springframework.org/schema/beans 
http://www.springframework.org/schema/beans/spring-beans.xsd 
http://cxf.apache.org/jaxws http://cxf.apache.org/schemas/jaxws.xsd">
	<import resource="classpath:META-INF/cxf/cxf.xml" />
	<bean class="com.clou.esp.hes.app.web.core.util.UciInterfaceUtil">
	 <property name="interfaces">
            <map>
            	<!-- 抄读接口 -->
                <entry key="GetMeterReadingsPort" value="/services/GetMeterReadings?wsdl"></entry>
                <!-- 升级接口 -->
                <entry key="RequestEndDeviceControlsPort" value="/services/RequestEndDeviceControls?wsdl"></entry>
                <!-- 下发ToU和Limiter接口 -->
                <entry key="MeterDefineConfigPort" value="/services/MeterDefineConfig?wsdl"></entry>
                <!-- 随抄Schedule -->
                <entry key="MeterReadSchedulePort" value="/services/GetMeterReadSchedule?wsdl"></entry>
                <!-- 上传固件升级文件 -->
                <entry key="FirmwareImageUploadPort" value="/services/FirmwareImageUpload?wsdl"></entry>
                <!-- 设备刷新接口 -->
                <entry key="AssetRefreshPort" value="/services/AssetRefresh?wsdl"></entry>
                <!-- 抄读取消接口 -->
                <entry key="JobCancelPort" value="/services/JobCancel?wsdl"></entry>
                <!-- 重新计算完整率或者线损 -->
                <entry key="DispatchTaskPort" value="/services/DispatchTask?wsdl"></entry>
            </map>
        </property>
    </bean>
   <!--  <jaxws:client id="GetMeterReadingsPort" serviceClass="ch.iec.tc57._2011.meterreadings.GetMeterReadingsPort" address="${uci.service.address}/services/GetMeterReadings?wsdl"/>
    <jaxws:client id="RequestEndDeviceControlsPort" serviceClass="ch.iec.tc57._2011.enddevicecontrols.RequestEndDeviceControlsPort" address="${uci.service.address}/services/RequestEndDeviceControls?wsdl"/>
    <jaxws:client id="MeterDefineConfigPort" serviceClass="ch.iec.tc57._2011.meterdefineconfig.MeterDefineConfigPort" address="${uci.service.address}/services/MeterDefineConfig?wsdl"/>
    <jaxws:client id="MeterReadSchedulePort" serviceClass="ch.iec.tc57._2011.meterreadschedule.MeterReadSchedulePort" address="${uci.service.address}/services/GetMeterReadSchedule?wsdl"/>
    <jaxws:client id="FirmwareImageUploadPort" serviceClass="clouesp.hes.core.uci.soap.custom.file_upload.FirmwareImageUploadPort" address="${uci.service.address}/services/FirmwareImageUpload?wsdl"/>
    <jaxws:client id="AssetRefreshPort" serviceClass="clouesp.hes.core.uci.soap.custom.asset_refresh.AssetRefreshPort" address="${uci.service.address}/services/AssetRefresh?wsdl"/>
    <jaxws:client id="JobCancelPort" serviceClass="clouesp.hes.core.uci.soap.custom.job_cancel.JobCancelPort" address="${uci.service.address}/services/JobCancel?wsdl"/> -->
    
	
	<!-- 接口的实现类声明 -->
	<jaxws:server id="ReplyMeterReadings"
		serviceClass="ch.iec.tc57._2011.meterreadings.ReplyMeterReadingsPort"
		address="/ReplyMeterReadings">
		<jaxws:serviceBean>
			<bean class="com.clou.esp.hes.app.web.service.impl.interfaces.ReplyMeterReadings_Port"></bean>
		</jaxws:serviceBean>
	</jaxws:server>
	<jaxws:server id="ReplyMeterDefineConfig"
		serviceClass="ch.iec.tc57._2011.meterdefineconfig.ReplyMeterDefineConfigPort"
		address="/ReplyMeterDefineConfigPort">
		<jaxws:serviceBean>
			<bean class="com.clou.esp.hes.app.web.service.impl.interfaces.ReplyMeterDefineConfigPort_port"></bean>
		</jaxws:serviceBean>
	</jaxws:server>
	<jaxws:server id="ReplyEndDeviceControls"
		serviceClass="ch.iec.tc57._2011.enddevicecontrols.ReplyEndDeviceControlsPort"
		address="/ReplyEndDeviceControlsPort">
		<jaxws:serviceBean>
			<bean class="com.clou.esp.hes.app.web.service.impl.interfaces.ReplyEndDeviceControlsPort_Port"></bean>
		</jaxws:serviceBean>
	</jaxws:server>
</beans>
