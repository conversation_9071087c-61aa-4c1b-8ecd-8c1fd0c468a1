/*友好时段数据项*/
DELETE FROM DICT_DATAITEM_GROUP_MAP WHERE GROUP_ID='1003005' AND DATAITEM_ID='40.0.0.18';
DELETE FROM DICT_DATAITEM_GROUP_MAP WHERE GROUP_ID='1003005' AND DATAITEM_ID='40.0.0.19';

insert into dict_dataitem values('40.0.7.104', 100, 'Meter list', '1#0.0.96.81.0.255#104', null, 'RW', 1, null, null);
insert into dict_dataitem values('38.0.0.50', 100, 'Low credit threshold', '1#0.0.96.54.4.255#2', null, 'RW', 1, null, null);
insert into dict_dataitem_parse_dlms values('1#0.0.96.54.4.255#2', 'double-long', 4, null);
insert into DICT_DATAITEM_GROUP_MAP values('1004002', '38.0.0.50', 29);
insert into DICT_DATAITEM_GROUP_MAP values('1004002', '40.0.4.4', 28);
insert into dict_dataitem values('38.0.0.51', 100, 'Overload threshold[Unit: kW]', '71#0.0.17.0.0.255#4', null, 'RW', 1, null, null);
insert into dict_dataitem_parse_dlms values('71#0.0.17.0.0.255#4', 'double-long-unsigned', 4, null);
insert into DICT_DATAITEM_GROUP_MAP values('1004002', '38.0.0.51', 30);

update dict_dataitem set id = '38.0.0.4' where id = '40.0.4.4';
update DICT_DATAITEM_GROUP_MAP set DATAITEM_ID = '38.0.0.4' where DATAITEM_ID = '40.0.4.4';
update ASSET_METER_GROUP_VALUE set DATAITEM_ID = '38.0.0.4' where DATAITEM_ID = '40.0.4.4';

insert into dict_dataitem select '41.0.3.1' as ID, PROTOCOL_ID, name, PROTOCOL_CODE, unit, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW from dict_dataitem where id = '40.0.3.1';
insert into dict_dataitem select '41.0.3.2' as ID, PROTOCOL_ID, name, PROTOCOL_CODE, unit, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW from dict_dataitem where id = '40.0.3.2';
insert into dict_dataitem select '41.0.2.5' as ID, PROTOCOL_ID, name, PROTOCOL_CODE, unit, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW from dict_dataitem where id = '40.0.2.5';

update dict_dataitem set name = 'Step tariff' where id = '41.0.0.0.0';
insert into dict_dataitem values ('41.0.0.0.0', 100, 'TOU tariff', null, null, 'RW', 1, null, null);

update DICT_DATAITEM_GROUP_MAP set SORT_ID = 3 where DATAITEM_ID = '40.0.0.18'; 
update DICT_DATAITEM_GROUP_MAP set SORT_ID = 4 where DATAITEM_ID = '40.0.0.19';
update DICT_DATAITEM_GROUP_MAP set SORT_ID = 5 where DATAITEM_ID = '40.0.0.20';
insert into DICT_DATAITEM_GROUP_MAP values ('1004003', '41.0.0.0.0', 2);

insert into dict_dataitem values('40.0.0.21', 100, 'Fixed fee', '1#0.0.96.54.5.255#2', null, 'RW', 1, null, null);
insert into dict_dataitem_parse_dlms values('1#0.0.96.54.5.255#2', 'special', null, null);
insert into DICT_DATAITEM_GROUP_MAP values('1004003', '40.0.0.21', 6);

insert into dict_dataitem select '41.0.2.4' as ID, PROTOCOL_ID, name, PROTOCOL_CODE, unit, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW from dict_dataitem where id = '40.0.2.4';
update dict_dataitem set name = 'Step tariff passive' where id = '40.0.3.1';
insert into dict_dataitem values('40.0.3.0', 100, 'Step tariff active', '21#0.0.16.0.1.255#2', null, 'R', 1, null, null);
insert into dict_dataitem values('41.0.3.0', 100, 'Step tariff active', '21#0.0.16.0.1.255#2', null, 'R', 1, null, null);
insert into dict_dataitem_parse_dlms values('21#0.0.16.0.1.255#2', 'special', null, 0);

alter table ppm_total_month_electricity_consumption MODIFY COLUMN SAVE_DB_DATE datetime DEFAULT CURRENT_TIMESTAMP; 

CREATE OR REPLACE PROCEDURE Total_month_electricity_consumption_One AS 
  s_ORG_ID VARCHAR (100);
  s_ORG_Code VARCHAR (100);  
  CURSOR cur_1 IS SELECT  ID,ORG_CODE  FROM sys_org  ORDER BY ID asc ;
BEGIN
  open cur_1;
  loop 
    fetch cur_1 into s_ORG_ID,s_ORG_Code;
    exit when cur_1%notfound;
    s_ORG_CODE := s_ORG_CODE||'%';
    Total_month_electricity_consumption_One(s_ORG_ID, s_ORG_Code);
  end loop;
  commit;
  CLOSE cur_1;
END TOTAL_MONTH_SALES_AMOUNT_ALL;

CREATE OR REPLACE PROCEDURE Total_month_electricity_consumption_One (
  INPUT_ORG_ID IN VARCHAR2 DEFAULT 100,  
  INPUT_ORG_CODE IN VARCHAR2 DEFAULT 100  
) AS
  s_DATE VARCHAR (30);
  s_Value VARCHAR (30);
  ID VARCHAR(32);
  CURSOR cur_1 IS 
	select dmim.TV, sum(dmim.value1)+sum(dmim.value6) as Month_Consumpiton 
	from data_md_interval_monthly dmim
	left join asset_meter am on am.id=dmim.DEVICE_ID
	left join sys_org so on am.org_id=so.id
	where dmim.TV = s_DATE and so.org_code like INPUT_ORG_CODE;
BEGIN
/**
        功能说明：根据传入的管理机构，计算上一个月的售电量，保存到 数据库表中
        作者：王波  创建日期：2019年11月20日
		调用方法：call `Total_month_electricity_consumption_One`(Input_Org_ID,Input_Org_Code);
	***/
    -- 在游标循环到最后会将 done 设置为 1
	s_DATE := to_date(add_months(trunc(sysdate),-1), '%Y-%m-01');  
  /* 打开游标 */
  open cur_1;
  loop 
    fetch cur_1 into s_DATE,s_Value;
    exit when cur_1%notfound;
    s_DATE := to_date(s_DATE,'%Y-%m');
    select lower(sys_guid()) INTO ID from dual;
	DELETE FROM ppm_total_month_electricity_consumption WHERE ORG_ID = Input_Org_ID AND Months = s_DATE;
	INSERT INTO ppm_total_month_electricity_consumption(ID,ORG_ID,Months,Month_Electricity) 
	VALUES(ID,INPUT_ORG_ID,s_DATE,s_Value);
  end loop;
  commit;
  CLOSE cur_1;
END Total_month_electricity_consumption_One;

CREATE OR REPLACE PROCEDURE Total_month_electricity_consumption_All AS 
  s_ORG_ID VARCHAR (100);
  s_ORG_Code VARCHAR (100);  
  CURSOR cur_1 IS SELECT  ID,ORG_CODE  FROM sys_org  ORDER BY ID asc ;
BEGIN
  open cur_1;
  loop 
    fetch cur_1 into s_ORG_ID,s_ORG_Code;
    exit when cur_1%notfound;
    s_ORG_CODE := s_ORG_CODE||'%';
    Total_month_electricity_consumption_One(s_ORG_ID, s_ORG_Code);
  end loop;
  commit;
  CLOSE cur_1;
END Total_month_electricity_consumption_All;

CREATE OR REPLACE PROCEDURE Total_Month_Sales_Amount_One (
  INPUT_ORG_ID IN VARCHAR2 DEFAULT 100,  
  INPUT_ORG_CODE IN VARCHAR2 DEFAULT 100  
) AS
  s_DATE VARCHAR (30);
  s_Value VARCHAR (30);
  ID VARCHAR(32);
  CURSOR cur_1 IS 
	SELECT to_date(vhi.SALES_DATE, '%Y-%m') AS StatMonth,
	   SUM(CASE WHEN recharge_type = 0 THEN vhi.Customer_Payment_Amount
		   WHEN recharge_type = 1 THEN - 1 * vhi.Uninstall_Amount
		   ELSE 0
		   END) AS SaleAmount
	FROM
		ppm_vend_historical_info vhi
		LEFT JOIN sys_org so on vhi.org_id=so.id
	WHERE
		vhi.SALES_DATE BETWEEN to_date(add_months(trunc(sysdate),-1), '%Y-%m-01') AND sysdate
	    AND vhi.Recharge_Type IN ('0')
	    AND vhi.Receipt_State IN ('0')
	    AND so.org_code like Input_Org_Code
	GROUP BY to_date(vhi.SALES_DATE, '%Y-%m');
BEGIN
	s_DATE := to_date(add_months(trunc(sysdate),-1), '%Y-%m-01'); 
 	/* 打开游标 */
  open cur_1;
  loop 
    fetch cur_1 into s_DATE,s_Value;
    exit when cur_1%notfound;
    s_DATE := to_date(s_DATE,'%Y-%m');
    select lower(sys_guid()) INTO ID from dual;
	DELETE FROM ppm_total_month_sales_amount WHERE ORG_ID = Input_Org_ID AND Months = s_DATE;
	INSERT INTO ppm_total_month_sales_amount(ID,ORG_ID,Months,Month_Amount) 
	VALUES(ID,INPUT_ORG_ID,s_DATE,s_Value);
  end loop;
  commit;
  CLOSE cur_1;
END Total_Month_Sales_Amount_One;

CREATE OR REPLACE PROCEDURE Total_Month_Sales_Amount_All AS
  s_ORG_ID VARCHAR (100);
  s_ORG_Code VARCHAR (100);  
  CURSOR cur_1 IS SELECT  ID,ORG_CODE  FROM sys_org  ORDER BY ID asc ;
BEGIN 
    /* 打开游标 */
  open cur_1;
  loop 
    fetch cur_1 into s_ORG_ID,s_ORG_Code;
    exit when cur_1%notfound;
    s_ORG_CODE := s_ORG_CODE||'%';
    Total_Month_Sales_Amount_One(s_ORG_ID, s_ORG_Code);
  end loop;
  commit;
  CLOSE cur_1;
END Total_Month_Sales_Amount_All;

Declare 
  i Integer; 
Begin 
   dbms_job.submit(i,'Total_Month_Sales_Amount_All;',Sysdate,'sysdate+1/24'); 
end; 

Declare 
  i Integer; 
Begin 
   dbms_job.submit(i,'Total_month_electricity_consumption_All;',Sysdate,'sysdate+1/24'); 
end; 