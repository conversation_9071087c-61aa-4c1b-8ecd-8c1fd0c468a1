package com.clou.esp.hes.app.web.model.demo.res;

import java.io.Serializable;

import javax.jws.soap.SOAPBinding;
import javax.jws.soap.SOAPBinding.Style;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * 孟加拉接口项目响应数据对象
 * 
 * <AUTHOR>
 * 
 */
@SOAPBinding(style = Style.RPC)
@XmlRootElement(name = "Reply")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = { "Result", "error" })
public class Reply implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public Reply(String result, Error error) {
		Result = result;
		this.error = error;
	}

	public Reply() {
		super();
	}

	public String Result;

	public Error error;

	public String getResult() {
		return Result;
	}

	public void setResult(String result) {
		Result = result;
	}

	public Error getError() {
		return error;
	}

	public void setError(Error error) {
		this.error = error;
	}

	@Override
	public String toString() {
		return "Reply [Result=" + Result + ", error=" + error + "]";
	}

}
