/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictDataitemGroupMap{ } 
 * 
 * 摘    要： 数据项分组映射
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.dict;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.dict.DictDataitemGroup;
import com.clou.esp.hes.app.web.model.dict.DictDataitemGroupMap;
import com.clou.esp.hes.app.web.model.dict.DictMeterDataStorageInfo;
import com.clou.esp.hes.app.web.model.dict.DictMeterDataStorageTable;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemGroupMapService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemGroupService;
import com.clou.esp.hes.app.web.service.dict.DictMeterDataStorageInfoService;
import com.clou.esp.hes.app.web.service.dict.DictMeterDataStorageTableService;
import com.google.common.collect.Lists;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;

/**
 * <AUTHOR>
 * @时间：2017-11-22 03:30:03
 * @描述：数据项分组映射类
 */
@Controller
@RequestMapping("/dictDataitemGroupMapController")
public class DictDataitemGroupMapController extends BaseController{

 	@Resource
    private DictDataitemGroupMapService 		dictDataitemGroupMapService;
 	@Resource
 	private DictDataitemGroupService 		    dictDataitemGroupService;
 	@Resource
 	private DictMeterDataStorageTableService  	tableService;  
 	@Resource
 	private DictMeterDataStorageInfoService  	infoService;
	@Resource
	private DataUserLogService dataUserLogService;
	/**
	 * 跳转到数据项分组映射列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/dict/dictDataitemGroupMapList");
    }

	/**
	 * 跳转到数据项分组映射新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dictDataitemGroupMap")
	public ModelAndView dictDataitemGroupMap(String groupId,HttpServletRequest request, Model model) {
		model.addAttribute("groupId", groupId);
		DictDataitemGroup group= this.dictDataitemGroupService.getEntity(groupId);
		model.addAttribute("appType", group.getAppType());
		model.addAttribute("channelTypeReplace", DictDataitemController.channelTypeReplace);
		return new ModelAndView("/dict/dictDataitemGroupMap");
	}
	@RequestMapping(value  = "sort")
	public ModelAndView sort(String groupId,HttpServletRequest request, Model model) {
		model.addAttribute("groupId", groupId);
		model.addAttribute("channelTypeReplace", DictDataitemController.channelTypeReplace);
		return new ModelAndView("/dict/dictDataitemGroupMapSort");
	}

	@RequestMapping(value = "unBindDatagrid")
	@ResponseBody
	public JqGridResponseTo unBindDatagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request
			,String groupId,String id,String name,String protocolCode,String protocolId,String dataitemType,Boolean select ,String tableName) {
		TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
		JqGridResponseTo j=null;
		if(select==null||!select) {
			return j;
		}
		try {
			jqGridSearchTo.put("groupId", groupId);
			jqGridSearchTo.put("dataitemId", id);
			jqGridSearchTo.put("dataitemName", name);
			jqGridSearchTo.put("protocolCode", protocolCode);
			jqGridSearchTo.put("protocolId", protocolId);
			jqGridSearchTo.put("dataitemType", dataitemType);
			jqGridSearchTo.put("tableName", tableName);
			j = this.dictDataitemGroupMapService.unBindForJqGrid(jqGridSearchTo);
		}catch (Exception e) {
			e.printStackTrace();
		}
		return j;
	}
	/**
	 * 数据项分组映射查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request
    		,String dataitemId,String dataitemName, String display, Boolean select) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        if(select==null||!select) {
        	return j;
        }
        try {
        	
        	jqGridSearchTo.put("dataitemId", dataitemId);
        	jqGridSearchTo.put("dataitemName", dataitemName);
        
        	jqGridSearchTo.put("protocolId", "100");
        
        	jqGridSearchTo.put("appType", 2);
            j=dictDataitemGroupMapService.getForJqGrid(jqGridSearchTo);
        }catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除数据项分组映射信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DictDataitemGroupMap dictDataitemGroupMap,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
         	if(StringUtils.isNotEmpty(dictDataitemGroupMap.getGroupId())&&StringUtils.isNotEmpty(dictDataitemGroupMap.getDataitemId())){
         		this.dictDataitemGroupMapService.delete(dictDataitemGroupMap);
         	}
 	        j.setMsg(MutiLangUtil.doMutiLang("system.delSuccess"));
 	    } catch (Exception e) {
 	    	e.printStackTrace();
 	    	j.setSuccess(false);
 	    	j.setMsg(MutiLangUtil.doMutiLang("system.delFail"));
 	    }
 	    return j;
    }
	/**
	 * 保存数据项分组映射信息
	 *
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "saveSort")
	@ResponseBody
	public AjaxJson saveSort(
			DictDataitemGroupMap dictDataitemGroupMap,
			BindingResult bindingResult, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		SysUser su = TokenManager.getToken();
		try {
			DictDataitemGroupMap data = this.dictDataitemGroupMapService.getItemGroupMapInfo(dictDataitemGroupMap);
			if(data.getSortId() > 0 && dictDataitemGroupMap.getSortIdType() == 2 || data.getSortId() < 0 && dictDataitemGroupMap.getSortIdType() == 1){  //正负相转
				dictDataitemGroupMap.setSortId((-1) * data.getSortId());
			}else{
				dictDataitemGroupMap.setSortId(data.getSortId());
			}
			this.dictDataitemGroupMapService.update(dictDataitemGroupMap);
			dataUserLogService.insertDataUserLog(su.getId(),
					"Group Management",
					"Sort Channel", "[Sort Channel:"  + dictDataitemGroupMap.getDataitemId() + "]");

			j.setMsg(MutiLangUtil.doMutiLang("system.updateSucc"));

		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
		}

		return j;
	}

	@RequestMapping(value = "getTables")
	@ResponseBody
	public AjaxJson getTables(String groupId,HttpServletRequest request){
		AjaxJson result = new AjaxJson();
		List<String> list = Lists.newArrayList();
		//如果子数据不为空 查出以前关联的table
		DictDataitemGroupMap tmp = new DictDataitemGroupMap();
		tmp.setGroupId(groupId);
		List<DictDataitemGroupMap> maps=this.dictDataitemGroupMapService.getList(tmp);
		if(maps!=null&&maps.size()>0) {
			DictMeterDataStorageInfo dictMeterDataStorageInfo =infoService.getEntity(maps.get(0).getDataitemId());
	    	if(dictMeterDataStorageInfo!=null){
		    	DictMeterDataStorageTable dictMeterDataStorageTable=tableService.getEntity(dictMeterDataStorageInfo.getTableId());
		    	list.add(dictMeterDataStorageTable.getName());
	    	}
		}else {
			List<DictMeterDataStorageTable> tables= this.tableService.getAllList();
			if(tables!=null) {
				for(DictMeterDataStorageTable table:tables) {
					list.add(table.getName());
				}
			}
		}
		result.setObj(list);
	return result;
}
	
}