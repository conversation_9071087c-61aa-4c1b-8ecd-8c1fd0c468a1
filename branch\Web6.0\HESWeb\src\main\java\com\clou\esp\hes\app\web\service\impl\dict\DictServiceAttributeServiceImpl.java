/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictServiceAttribute{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-03-29 03:19:41
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.dict;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.dict.DictServiceAttributeDao;
import com.clou.esp.hes.app.web.model.dict.DictServiceAttribute;
import com.clou.esp.hes.app.web.service.dict.DictServiceAttributeService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dictServiceAttributeService")
public class DictServiceAttributeServiceImpl  extends CommonServiceImpl<DictServiceAttribute>  implements DictServiceAttributeService {

	@Resource
	private DictServiceAttributeDao dictServiceAttributeDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dictServiceAttributeDao);
    }
	public DictServiceAttributeServiceImpl() {}
	
	@Override
	public List<DictServiceAttribute> getListByServiceId(String serviceId) {
		return dictServiceAttributeDao.getListByServiceId(serviceId);
	}
	
	
}