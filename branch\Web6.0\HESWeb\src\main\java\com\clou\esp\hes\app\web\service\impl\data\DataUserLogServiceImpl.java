package com.clou.esp.hes.app.web.service.impl.data;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.data.DataUserLogDao;
import com.clou.esp.hes.app.web.model.data.DataUserLog;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
@Service("dataUserLogService")
public class DataUserLogServiceImpl extends CommonServiceImpl<DataUserLog>
		implements DataUserLogService {

	@Resource
	private DataUserLogDao dataUserLogDao;
	
	@Autowired
	public void setCommonService() {
		// TODO Auto-generated method stub
		super.setCommonService(dataUserLogDao);
	}

	public DataUserLogServiceImpl(){
		
	}

}
