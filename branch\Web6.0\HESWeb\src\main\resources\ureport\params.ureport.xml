<?xml version="1.0" encoding="UTF-8"?><ureport><cell expand="None" name="A1" row="1" col="1" left-cell="B2"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><expression-value><![CDATA[row()]]></expression-value></cell><cell expand="Down" name="B1" row="1" col="2"><cell-style font-size="10" align="center" valign="middle"></cell-style><dataset-value dataset-name="test" aggregate="group" property="SN" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="C1" row="1" col="3"><cell-style font-size="10" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="D1" row="1" col="4"><cell-style font-size="10" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="A2" row="2" col="1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="B2" row="2" col="2"><cell-style font-size="10" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="C2" row="2" col="3"><cell-style font-size="10" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="D2" row="2" col="4"><cell-style font-size="10" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="A3" row="3" col="1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="B3" row="3" col="2"><cell-style font-size="10" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="C3" row="3" col="3"><cell-style font-size="10" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="D3" row="3" col="4"><cell-style font-size="10" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="A4" row="4" col="1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="B4" row="4" col="2"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="C4" row="4" col="3"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="D4" row="4" col="4"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><row row-number="1" height="18"/><row row-number="2" height="18"/><row row-number="3" height="18"/><row row-number="4" height="19"/><column col-number="1" width="74"/><column col-number="2" width="80"/><column col-number="3" width="80"/><column col-number="4" width="80"/><datasource name="test" type="jdbc" username="hes" password="hes" url="*****************************************" driver="oracle.jdbc.OracleDriver"><dataset name="test" type="sql"><sql><![CDATA[${
    if(param("assetType") == 'Commnuicator'){
        return "select * from asset_meter 
      where sn=\'000000013117\'";
    }else{
        return "select * from asset_meter where sn=\'007119000002\' or  sn=\'000000023417\'";
    }
}]]></sql><field name="ID"/><field name="SN"/><field name="NAME"/><field name="UTILITY_ID"/><field name="ORG_ID"/><field name="COMMUNICATOR_ID"/><field name="MODEL"/><field name="MANUFACTURER"/><field name="PASSWORD"/><field name="HLS_AK"/><field name="HLS_EK"/><field name="INDEX_DCU"/><field name="MAC"/><field name="COM_TYPE"/><field name="IS_ENCRYPT"/><field name="AUTH_TYPE"/><field name="FW_VERSION"/><field name="REMOVE_FLAG"/><field name="ADDR"/><field name="LONGITUDE"/><field name="LATITUDE"/><field name="COM_PORT"/><field name="CT"/><field name="PT"/><field name="KEY_FLAG"/><field name="SRC_ADDR"/></dataset></datasource><paper type="A4" left-margin="90" right-margin="90"
    top-margin="72" bottom-margin="72" paging-mode="fitpage" fixrows="0"
    width="595" height="842" orientation="portrait" html-report-align="left" bg-image="" html-interval-refresh-value="0" column-enabled="false"></paper></ureport>