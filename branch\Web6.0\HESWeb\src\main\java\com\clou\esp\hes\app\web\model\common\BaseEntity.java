package com.clou.esp.hes.app.web.model.common;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import com.clou.esp.hes.app.web.model.system.SysUser;

/**
 * Entity支持类
 * 
 * <AUTHOR>
 * @version 2016-12-09
 */
public abstract class BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 实体编号（唯一标识）
	 */
	protected String id;
	
	/** 扩展信息 */
	private Map<String, Object> extData = new HashMap<String, Object>();

	public BaseEntity() {
		super();
	}

	public BaseEntity(SysUser sysUser) {
		this.id=sysUser.getId();
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Map<String, Object> getExtData() {
		return extData;
	}

	public void setExtData(Map<String, Object> extData) {
		this.extData = extData;
	}

	/**
	 * 加入扩展信息 put 添加属性
	 * @param key
	 * @param value
	 */
	public void put(String key,Object value){
		this.extData.put(key, value);
	}
	/**
	 * 加入扩展信息 get 扩展属性
	 * @param key
	 * @param value
	 */
	public Object get(String key){
		return this.extData.get(key);
	}

}
