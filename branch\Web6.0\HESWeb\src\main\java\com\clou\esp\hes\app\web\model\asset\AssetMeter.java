/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeter{ } 
 * 
 * 摘    要： assetMeter
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-06 08:04:55
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.asset;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.excel.Excel;

public class AssetMeter extends BaseEntity {

	/**
	 * 意义，目的和功能，以及被用到的地方<br>
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public AssetMeter() {
	}

	/** sn */
	@Excel(name = "Serial Number", width = 30, groups = ValidGroup1.class)
	private java.lang.String sn;
	@Excel(name = "Manufacturer", width = 30, groups = ValidGroup1.class)
	private String manufacturerName;
	@Excel(name = "Model", width = 30, groups = ValidGroup1.class)
	private String modelName;
	@Excel(name = "Current Version", width = 30, groups = ValidGroup1.class)
	private String fwVersion;
	/** name */
	private java.lang.String name;
	/** utilityId */
	private java.lang.String utilityId;
	/** orgId */
	private java.lang.String orgId;
	/** communicatorId */
	private java.lang.String communicatorId;
	/** model */
	private java.lang.String model;
	/** manufacturer */
	private java.lang.String manufacturer;
	/** password */
	private java.lang.String password;
	/** hlsAk */
	private java.lang.String hlsAk;
	/** hlsEk */
	private java.lang.String hlsEk;
	
	/** mac */
	private java.lang.String mac;
	private String deviceTypeIcon;
	/**authType*/
	private String authType;
	/**采集方案**/
	private String scheduleSchemeId;
	/**计量曲线分组**/
	private String measurementGroupId;
	/**TOU分组**/
	private String touGroupId;
	/**Limter分组**/
	private String limiterGroupId;
	/**阶梯费率*/
	private String stepTariffId;
	/**有好时段*/
	private String friendlyId;
	
	
	/**采集方案**/
	private String scheduleSchemeName;
	/**计量曲线分组**/
	private String measurementGroupName;
	/**TOU分组**/
	private String touGroupName;
	/**Limter分组**/
	private String limiterGroupName;
	/**阶梯费率*/
	private String stepTariffName;
	/**有好时段*/
	private String friendlyName;
	
	private String comType;
	private String dvalue;
	private String mvalue;
	private long missDataCount;
	private Date dspTv;
	private String integrityId;
	private BigDecimal ave;
	private String currentVesionArray[];
	private String communicator;
	
	private Integer removeFlag;
	
	private Integer isEncrypt;
	
	/**是否收藏*/
	private Boolean isCollect;
	
	private String commSn;		//集中器sn
	private String communication;//通讯类型
	private String status;		//操作状态
	
	private String deviceType;
	private String uuid;
	private String addr;
	
	private Integer ct;        //默认1
	private Integer pt;		   //默认1
	private Integer comPort;   //默认1，GPRS Meter 没有
	private Integer indexDcu;  //默认1，GPRS Meter 没有
	
	private String simNum;
	private String comStatus;
	private String ipAddr;
	private String ipPort;
	private Date updateTv;
	private Integer keyFlag;
	
	private String orgName;
	
	private BigDecimal longitude ;
	private BigDecimal latitude  ;
	
	// 关联是line的SN
	private String lineName;
	
	private List<String> orgIdList;
	
	private Date createTime;
	private String createTimeStr;
	
	private String stsSn;
	
	public String getStsSn() {
		return stsSn;
	}

	public void setStsSn(String stsSn) {
		this.stsSn = stsSn;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getCreateTimeStr() {
		return createTimeStr;
	}

	public void setCreateTimeStr(String createTimeStr) {
		this.createTimeStr = createTimeStr;
	}

	public String getLineName() {
		return lineName;
	}

	public void setLineName(String lineName) {
		this.lineName = lineName;
	}

	public List<String> getOrgIdList() {
		return orgIdList;
	}

	public void setOrgIdList(List<String> orgIdList) {
		this.orgIdList = orgIdList;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	/**
	 * sn
	 * 
	 * @return the value of ASSET_METER.SN
	 * @mbggenerated 2017-11-06 08:04:55
	 */
	public java.lang.String getSn() {
		return sn;
	}

	/**
	 * sn
	 * 
	 * @param sn
	 *            the value for ASSET_METER.SN
	 * @mbggenerated 2017-11-06 08:04:55
	 */
	public void setSn(java.lang.String sn) {
		this.sn = sn;
	}

	/**
	 * name
	 * 
	 * @return the value of ASSET_METER.NAME
	 * @mbggenerated 2017-11-06 08:04:55
	 */
	public java.lang.String getName() {
		return name;
	}

	/**
	 * name
	 * 
	 * @param name
	 *            the value for ASSET_METER.NAME
	 * @mbggenerated 2017-11-06 08:04:55
	 */
	public void setName(java.lang.String name) {
		this.name = name;
	}

	/**
	 * utilityId
	 * 
	 * @return the value of ASSET_METER.VENDOR_ID
	 * @mbggenerated 2017-11-06 08:04:55
	 */
	public java.lang.String getUtilityId() {
		return utilityId;
	}

	/**
	 * utilityId
	 * 
	 * @param utilityId
	 *            the value for ASSET_METER.VENDOR_ID
	 * @mbggenerated 2017-11-06 08:04:55
	 */
	public void setUtilityId(java.lang.String utilityId) {
		this.utilityId = utilityId;
	}

	/**
	 * orgId
	 * 
	 * @return the value of ASSET_METER.ORG_ID
	 * @mbggenerated 2017-11-06 08:04:55
	 */
	public java.lang.String getOrgId() {
		return orgId;
	}

	/**
	 * orgId
	 * 
	 * @param orgId
	 *            the value for ASSET_METER.ORG_ID
	 * @mbggenerated 2017-11-06 08:04:55
	 */
	public void setOrgId(java.lang.String orgId) {
		this.orgId = orgId;
	}

	/**
	 * communicatorId
	 * 
	 * @return the value of ASSET_METER.COMMUNICATOR_ID
	 * @mbggenerated 2017-11-06 08:04:55
	 */
	public java.lang.String getCommunicatorId() {
		return communicatorId;
	}

	/**
	 * communicatorId
	 * 
	 * @param communicatorId
	 *            the value for ASSET_METER.COMMUNICATOR_ID
	 * @mbggenerated 2017-11-06 08:04:55
	 */
	public void setCommunicatorId(java.lang.String communicatorId) {
		this.communicatorId = communicatorId;
	}

	/**
	 * model
	 * 
	 * @return the value of ASSET_METER.MODEL
	 * @mbggenerated 2017-11-06 08:04:55
	 */
	public java.lang.String getModel() {
		return model;
	}

	/**
	 * model
	 * 
	 * @param model
	 *            the value for ASSET_METER.MODEL
	 * @mbggenerated 2017-11-06 08:04:55
	 */
	public void setModel(java.lang.String model) {
		this.model = model;
	}

	/**
	 * manufacturer
	 * 
	 * @return the value of ASSET_METER.MANUFACTURER
	 * @mbggenerated 2017-11-06 08:04:55
	 */
	public java.lang.String getManufacturer() {
		return manufacturer;
	}

	/**
	 * manufacturer
	 * 
	 * @param manufacturer
	 *            the value for ASSET_METER.MANUFACTURER
	 * @mbggenerated 2017-11-06 08:04:55
	 */
	public void setManufacturer(java.lang.String manufacturer) {
		this.manufacturer = manufacturer;
	}

	/**
	 * password
	 * 
	 * @return the value of ASSET_METER.PASSWORD
	 * @mbggenerated 2017-11-06 08:04:55
	 */
	public java.lang.String getPassword() {
		return password;
	}

	/**
	 * password
	 * 
	 * @param password
	 *            the value for ASSET_METER.PASSWORD
	 * @mbggenerated 2017-11-06 08:04:55
	 */
	public void setPassword(java.lang.String password) {
		this.password = password;
	}

	/**
	 * hlsAk
	 * 
	 * @return the value of ASSET_METER.HLS_AK
	 * @mbggenerated 2017-11-06 08:04:55
	 */
	public java.lang.String getHlsAk() {
		return hlsAk;
	}

	/**
	 * hlsAk
	 * 
	 * @param hlsAk
	 *            the value for ASSET_METER.HLS_AK
	 * @mbggenerated 2017-11-06 08:04:55
	 */
	public void setHlsAk(java.lang.String hlsAk) {
		this.hlsAk = hlsAk;
	}

	/**
	 * hlsEk
	 * 
	 * @return the value of ASSET_METER.HLS_EK
	 * @mbggenerated 2017-11-06 08:04:55
	 */
	public java.lang.String getHlsEk() {
		return hlsEk;
	}

	/**
	 * hlsEk
	 * 
	 * @param hlsEk
	 *            the value for ASSET_METER.HLS_EK
	 * @mbggenerated 2017-11-06 08:04:55
	 */
	public void setHlsEk(java.lang.String hlsEk) {
		this.hlsEk = hlsEk;
	}

	/**
	 * indexDcu
	 * 
	 * @return the value of ASSET_METER.INDEX_DCU
	 * @mbggenerated 2017-11-06 08:04:55
	 */
	public java.lang.Integer getIndexDcu() {
		return indexDcu;
	}

	/**
	 * indexDcu
	 * 
	 * @param indexDcu
	 *            the value for ASSET_METER.INDEX_DCU
	 * @mbggenerated 2017-11-06 08:04:55
	 */
	public void setIndexDcu(java.lang.Integer indexDcu) {
		this.indexDcu = indexDcu;
	}

	/**
	 * mac
	 * 
	 * @return the value of ASSET_METER.MAC
	 * @mbggenerated 2017-11-06 08:04:55
	 */
	public java.lang.String getMac() {
		return mac;
	}

	/**
	 * mac
	 * 
	 * @param mac
	 *            the value for ASSET_METER.MAC
	 * @mbggenerated 2017-11-06 08:04:55
	 */
	public void setMac(java.lang.String mac) {
		this.mac = mac;
	}

	public AssetMeter(java.lang.String sn, java.lang.String name,
			java.lang.String utilityId, java.lang.String orgId,
			java.lang.String communicatorId, java.lang.String model,
			java.lang.String manufacturer, java.lang.String password,
			java.lang.String hlsAk, java.lang.String hlsEk,
			java.lang.Integer indexDcu, java.lang.String mac) {
		super();
		this.sn = sn;
		this.name = name;
		this.utilityId = utilityId;
		this.orgId = orgId;
		this.communicatorId = communicatorId;
		this.model = model;
		this.manufacturer = manufacturer;
		this.password = password;
		this.hlsAk = hlsAk;
		this.hlsEk = hlsEk;
		this.indexDcu = indexDcu;
		this.mac = mac;
	}

	public String getDeviceTypeIcon() {
		return deviceTypeIcon;
	}

	public void setDeviceTypeIcon(String deviceTypeIcon) {
		this.deviceTypeIcon = deviceTypeIcon;
	}

	public String getModelName() {
		return modelName;
	}

	public void setModelName(String modelName) {
		this.modelName = modelName;
	}

	public String getComType() {
		return comType;
	}

	public void setComType(String comType) {
		this.comType = comType;
	}

	public String getDvalue() {
		return dvalue;
	}

	public void setDvalue(String dvalue) {
		this.dvalue = dvalue;
	}

	public String getMvalue() {
		return mvalue;
	}

	public void setMvalue(String mvalue) {
		this.mvalue = mvalue;
	}

	public long getMissDataCount() {
		return missDataCount;
	}

	public void setMissDataCount(long missDataCount) {
		this.missDataCount = missDataCount;
	}

	public Date getDspTv() {
		return dspTv;
	}

	public void setDspTv(Date dspTv) {
		this.dspTv = dspTv;
	}

	public String getIntegrityId() {
		return integrityId;
	}

	public void setIntegrityId(String integrityId) {
		this.integrityId = integrityId;
	}

	public BigDecimal getAve() {
		return ave;
	}

	public void setAve(BigDecimal ave) {
		this.ave = ave;
	}

	public String getFwVersion() {
		return fwVersion;
	}

	public void setFwVersion(String fwVersion) {
		this.fwVersion = fwVersion;
	}

	public String getManufacturerName() {
		return manufacturerName;
	}

	public void setManufacturerName(String manufacturerName) {
		this.manufacturerName = manufacturerName;
	}

	public String[] getCurrentVesionArray() {
		return currentVesionArray;
	}

	public void setCurrentVesionArray(String[] currentVesionArray) {
		this.currentVesionArray = currentVesionArray;
	}

	public String getCommunicator() {
		return communicator;
	}

	public void setCommunicator(String communicator) {
		this.communicator = communicator;
	}

	public Boolean getIsCollect() {
		return isCollect;
	}

	public void setIsCollect(Boolean isCollect) {
		this.isCollect = isCollect;
	}

	public String getAuthType() {
		return authType;
	}

	public void setAuthType(String authType) {
		this.authType = authType;
	}

	public String getScheduleSchemeId() {
		return scheduleSchemeId;
	}

	public String getMeasurementGroupId() {
		return measurementGroupId;
	}

	public String getTouGroupId() {
		return touGroupId;
	}

	public String getLimiterGroupId() {
		return limiterGroupId;
	}

	public void setScheduleSchemeId(String scheduleSchemeId) {
		this.scheduleSchemeId = scheduleSchemeId;
	}

	public void setMeasurementGroupId(String measurementGroupId) {
		this.measurementGroupId = measurementGroupId;
	}

	public void setTouGroupId(String touGroupId) {
		this.touGroupId = touGroupId;
	}

	public void setLimiterGroupId(String limiterGroupId) {
		this.limiterGroupId = limiterGroupId;
	}

	public Integer getRemoveFlag() {
		return removeFlag;
	}

	public void setRemoveFlag(Integer removeFlag) {
		this.removeFlag = removeFlag;
	}

	public Integer getIsEncrypt() {
		return isEncrypt;
	}

	public void setIsEncrypt(Integer isEncrypt) {
		this.isEncrypt = isEncrypt;
	}
	
	

	public String getStepTariffId() {
		return stepTariffId;
	}

	public String getFriendlyId() {
		return friendlyId;
	}

	public void setStepTariffId(String stepTariffId) {
		this.stepTariffId = stepTariffId;
	}

	public void setFriendlyId(String friendlyId) {
		this.friendlyId = friendlyId;
	}

	@Override
	public String toString() {
		return "[sn=" + sn + ", manufacturerName="
				+ manufacturerName + ", modelName=" + modelName
				+ ", fwVersion=" + fwVersion + ", name=" + name+"]";
	}

	public String getCommSn() {
		return commSn;
	}

	public void setCommSn(String commSn) {
		this.commSn = commSn;
	}

	public String getCommunication() {
		return communication;
	}

	public void setCommunication(String communication) {
		this.communication = communication;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}
	public String getDeviceType() {
		return deviceType;
	}

	public void setDeviceType(String deviceType) {
		this.deviceType = deviceType;
	}

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	public String getAddr() {
		return addr;
	}

	public void setAddr(String addr) {
		this.addr = addr;
	}

	public Integer getCt() {
		return ct;
	}

	public void setCt(Integer ct) {
		this.ct = ct;
	}

	public Integer getPt() {
		return pt;
	}

	public void setPt(Integer pt) {
		this.pt = pt;
	}

	public Integer getComPort() {
		return comPort;
	}

	public void setComPort(Integer comPort) {
		this.comPort = comPort;
	}

   

	public String getSimNum() {
		return simNum;
	}

	public void setSimNum(String simNum) {
		this.simNum = simNum;
	}

	public String getComStatus() {
		return comStatus;
	}

	public void setComStatus(String comStatus) {
		this.comStatus = comStatus;
	}

	public String getIpAddr() {
		return ipAddr;
	}

	public void setIpAddr(String ipAddr) {
		this.ipAddr = ipAddr;
	}

	public String getIpPort() {
		return ipPort;
	}

	public void setIpPort(String ipPort) {
		this.ipPort = ipPort;
	}

	public Date getUpdateTv() {
		return updateTv;
	}

	public void setUpdateTv(Date updateTv) {
		this.updateTv = updateTv;
	}

	public Integer getKeyFlag() {
		return keyFlag;
	}

	public void setKeyFlag(Integer keyFlag) {
		this.keyFlag = keyFlag;
	}

	public String getScheduleSchemeName() {
		return scheduleSchemeName;
	}

	public void setScheduleSchemeName(String scheduleSchemeName) {
		this.scheduleSchemeName = scheduleSchemeName;
	}

	public String getMeasurementGroupName() {
		return measurementGroupName;
	}

	public void setMeasurementGroupName(String measurementGroupName) {
		this.measurementGroupName = measurementGroupName;
	}

	public String getTouGroupName() {
		return touGroupName;
	}

	public void setTouGroupName(String touGroupName) {
		this.touGroupName = touGroupName;
	}

	public String getLimiterGroupName() {
		return limiterGroupName;
	}

	public void setLimiterGroupName(String limiterGroupName) {
		this.limiterGroupName = limiterGroupName;
	}

	public String getStepTariffName() {
		return stepTariffName;
	}

	public void setStepTariffName(String stepTariffName) {
		this.stepTariffName = stepTariffName;
	}

	public String getFriendlyName() {
		return friendlyName;
	}

	public void setFriendlyName(String friendlyName) {
		this.friendlyName = friendlyName;
	}

	public BigDecimal getLongitude() {
		return longitude;
	}

	public void setLongitude(BigDecimal longitude) {
		this.longitude = longitude;
	}

	public BigDecimal getLatitude() {
		return latitude;
	}

	public void setLatitude(BigDecimal latitude) {
		this.latitude = latitude;
	}

	public AssetMeter(String id,String sn, String mac) {
		super();
		this.id = id;
		this.sn = sn;
		this.mac = mac;
	}

}