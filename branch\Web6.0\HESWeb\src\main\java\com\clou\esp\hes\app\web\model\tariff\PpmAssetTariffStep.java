package com.clou.esp.hes.app.web.model.tariff;

import com.clou.esp.hes.app.web.model.common.BaseEntity;


/*********************************************************************************************************
 * @see Copyright© 2019 Shenzhen Clou Electronics CO., LTD.  All rights reserved. 
 * @see 
 * @see File Name:public class PpmAssetTariffStep{ } 
 * @see 
 * @see Description： ppmAssetTariffStep
 * @version *******
 * <AUTHOR>
 * @see Create Time：2019-09-17 10:04:56
 * @see Last modification Time：2019-09-17 10:04:56
 * 
*********************************************************************************************************/
public class PpmAssetTariffStep  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public PpmAssetTariffStep() {
	}

	/**
	 * 实体编号（唯一标识）
	 */
	protected String groupId;

	
	/**
	 * 实体编号（唯一标识）
	 */
	protected String stepTariffName;
	
	/**
	 * 实体编号（唯一标识）
	 */
	protected String stepIndexName;
	
	/**stepIndex*/
	private String stepIndexStr;
	/**stepIndex*/
	private java.lang.Integer stepIndex;
	/**startQuantity*/
	private java.math.BigDecimal startQuantity;
	/**endQuantity*/
	private java.math.BigDecimal endQuantity;
	/**price*/
	private java.math.BigDecimal price;

	
	

	public String getStepIndexStr() {
		return stepIndexStr;
	}

	public void setStepIndexStr(String stepIndexStr) {
		this.stepIndexStr = stepIndexStr;
	}

	public String getStepIndexName() {
		return stepIndexName;
	}

	public void setStepIndexName(String stepIndexName) {
		this.stepIndexName = stepIndexName;
	}

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}

	public String getStepTariffName() {
		return stepTariffName;
	}

	public void setStepTariffName(String stepTariffName) {
		this.stepTariffName = stepTariffName;
	}

	/**
	 * 实体编号（唯一标识）
	 */
	protected String name;
	
	
	
	
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}



	public java.lang.Integer getStepIndex() {
		return stepIndex;
	}

	public void setStepIndex(java.lang.Integer stepIndex) {
		this.stepIndex = stepIndex;
	}

	/**
	 * startQuantity
	 * @return the value of PPM_ASSET_TARIFF_STEP.START_QUANTITY
	 * @mbggenerated 2019-09-17 10:04:56
	 */
	public java.math.BigDecimal getStartQuantity() {
		return startQuantity;
	}

	/**
	 * startQuantity
	 * @param startQuantity the value for PPM_ASSET_TARIFF_STEP.START_QUANTITY
	 * @mbggenerated 2019-09-17 10:04:56
	 */
    	public void setStartQuantity(java.math.BigDecimal startQuantity) {
		this.startQuantity = startQuantity;
	}
	/**
	 * endQuantity
	 * @return the value of PPM_ASSET_TARIFF_STEP.END_QUANTITY
	 * @mbggenerated 2019-09-17 10:04:56
	 */
	public java.math.BigDecimal getEndQuantity() {
		return endQuantity;
	}

	/**
	 * endQuantity
	 * @param endQuantity the value for PPM_ASSET_TARIFF_STEP.END_QUANTITY
	 * @mbggenerated 2019-09-17 10:04:56
	 */
    	public void setEndQuantity(java.math.BigDecimal endQuantity) {
		this.endQuantity = endQuantity;
	}
	/**
	 * price
	 * @return the value of PPM_ASSET_TARIFF_STEP.PRICE
	 * @mbggenerated 2019-09-17 10:04:56
	 */
	public java.math.BigDecimal getPrice() {
		return price;
	}

	/**
	 * price
	 * @param price the value for PPM_ASSET_TARIFF_STEP.PRICE
	 * @mbggenerated 2019-09-17 10:04:56
	 */
    	public void setPrice(java.math.BigDecimal price) {
		this.price = price;
	}

	public PpmAssetTariffStep(java.lang.Integer stepIndex 
	,java.math.BigDecimal startQuantity 
	,java.math.BigDecimal endQuantity 
	,java.math.BigDecimal price ) {
		super();
		this.stepIndex = stepIndex;
		this.startQuantity = startQuantity;
		this.endQuantity = endQuantity;
		this.price = price;
	}

}