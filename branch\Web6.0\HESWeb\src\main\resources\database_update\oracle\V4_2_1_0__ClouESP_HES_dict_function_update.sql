alter table ASSET_COMMUNICATOR add (IS_ENCRYPT VARCHAR2(3 BYTE));
alter table ASSET_COMMUNICATOR add (AUTH_TYPE VARCHAR2(3 BYTE));
alter table ASSET_COMMUNICATOR add (HLS_AK VARCHAR2(64 BYTE));
alter table ASSET_COMMUNICATOR add (HLS_EK VARCHAR2(64 BYTE));

alter table DATA_TIME_SYNCHRONIZATION add (ID_TYPE NUMBER(4,0)  default 1);

delete from DICT_FUNCTION;
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1001','Meter Data Report','meterDataReportController/list.do','Data Collection');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1002','Meter Event Report','dataMeterEventController/list.do','Data Collection');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1003','Schedule Reads Report','dataIntegrityController/scheduleReadsReport.do','Data Collection');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1004','Miss Data Tracing','dataIntegrityController/list.do','Data Collection');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1005','Collection Schedule Management','assetScheduleSchemeDetailController/list.do','Data Collection');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1006','Meter Group Management','assetMeterGroupController/meterGroupMgmt.do','Provisioning');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1007','Meter Configuration','meterConfigurationController/toMeterConfiguration.do','Provisioning');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1008','DCU Configuration - 376','dcuConfigurationController/toDcuConfiguration.do','Provisioning');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1009','Firmware Upgrade','dataFwuPlanController/list.do','Tools');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1011','On Demand Reads','assetMeterController/onDemandReads.do','Tools');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1012','Connect / Disconnect','meterConnectOrDisconnectController/toConnectOrDisconnect.do','Tools');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1013','Asset Management','assetMeterController/assetManagementList.do','Provisioning');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1014','Deployment Management','sysServerController/list.do','System');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1015','Log Explorer','sysLogController/list.do','System');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1017','Permission','sysUserController/sysUserAndRoleList.do','System');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1018','Data Export Management','sysDataitemExportController/list.do','System Integration');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1019','Reports','dictReportController/list.do','Data Management');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1020','Meter Group Upgrade','dataParameterPlanController/meterGroupUpgradeList.do','Tools');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1021','On Demand Reads - 376','assetMeterController/onDemandReads376.do','Tools');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1022','Calculation Object Define','assetCalationObjectController/assetCalculationObject.do','Data Management');

delete from DICT_OPERATION;
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1005002','Add/Edit scheme','1005','/assetScheduleSchemeController/assetScheduleScheme.do',0,'Add/Edit scheme');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006005','Add TOU group','1006','/assetMeterGroupController/assetMeterGroup.do?type=2',0,'Add TOU group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006006','Add Measurement group','1006','/assetMeterGroupController/toMeasurementGroup.do?type=1',0,'Add Measurement group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006007','Add Limiter group','1006','/assetMeterGroupController/assetMeterGroup.do?type=3',0,'Add Limiter group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1014002','Add/Edit server/service','1014','/sysServerController/toAddSysServer.do',0,'Add/Edit server or service');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1014003','Delete server/service','1014','/sysServerController/delServer.do',0,'Delete server or service');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006008','Delete TOU group','1006','deleteTouGroup',0,'Delete TOU group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006009','Delete Limiter group','1006','deleteLimiterGroup',0,'Delete Limiter group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006010','Delete Measurement group','1006','deleteMeasurementGroup',0,'Delete Measurement group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1009001','Add Plan','1009','/dataFwuPlanController/saveFWUPlan.do',0,'Add plan in Plan Tab');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1018002','Save Data '||'&'||' Event','1018','/sysDataitemExportController/saveMeterDataEventExport.do',0,'Save meter data and events to be exported');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017005','Add/Edit user','1017','/sysUserController/toAddSysUser.do',0,'Add user; Edit user; Reset password');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1005003','Delete scheme','1005','/assetScheduleSchemeController/del.do',0,'Delete scheme');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1005004','Add/Edit task','1005','/assetScheduleSchemeDetailController/assetScheduleSchemeDetail.do',0,'Add/Edit task');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1005005','Delete task','1005','/assetScheduleSchemeDetailController/del.do',0,'Delete task');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013002','Edit '||'&'||' Save Meter','1013','/assetMeterController/save.do',0,'Edit '||'&'||' Save Meter');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013003','Delete Meter','1013','/assetMeterController/del.do',0,'Delete Meter');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013004','Add Meter','1013','/assetMeterController/addAssetDevice.do',0,'Add Meter');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1007004','Excute Task in Set Page','1007','/meterConfigurationController/excuteTask.do',0,'Add '||'&'||' execute task in Set Tab');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1007005','Read data in Get Tab','1007','/meterConfigurationController/getTouGroupData.do',0,'Read data in Get Tab');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017007','Add/Edit role','1017','/sysRoleController/toAddSysRole.do',0,'Add/Edit role');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017008','Delete role','1017','/sysRoleController/deleteRole.do',0,'Delete role');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017006','Delete user','1017','/sysUserController/del.do',0,'Delete user');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017009','Add/Edit/Remove organization','1017','/sysOrgController/toAddSysOrg.do',0,'Add, edit or remove organization');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017010','Delete organization','1017','/sysOrgController/del.do',0,'Delete organization');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1009002','Cancel Plan/Job','1009','/dataFwuPlanController/cancel.do',0,'Cancel plan/job in Plan Report Tab; Cancel job in Job Report Tab');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1019001','Add Plan','1020','/dataParameterPlanController/saveMeterGroupUpgradePlan.do',0,'Add plan in Plan/Plan Report Tab');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1019002','Cancel Plan','1020','/dataParameterPlanController/cancelPlan.do',0,'Cancel plan in Plan Report Tab');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006011','Delete Step Tariff group','1006','deleteStepTariffGroup',0,'Delete Step Tariff group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006012','Add Step Tariff group','1006','/assetMeterGroupController/addStepTariffGroup.do?type=4',0,'Add Step Tariff group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013017','Add Calculation Object to Line','1013','AddCalculationObjectToLine',0,'Add Calculation Object to Line');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013018','Add Calculation Object to Transformer','1013','AddCalculationObjectToTransformer',0,'Add Calculation Object to Transformer');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013020','Add Calculation Object to Organization','1013','AddCalculationObjectToOrg',0,'Add Calculation Object to Organization');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1022001','Add Calculation Object','1022','AddCalculationObject',0,'Add Calculation Object');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1022002','Edit Calculation Object','1022','EditCalculationObject',0,'Edit Calculation Object');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1022003','Delete Calculation Object','1022','DeleteCalculationObject',0,'Delete Calculation Object');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1022004','Add Data Channnel in Calculation Object','1022','AddDataChannelInCalcObj',0,'Add Data Channnel in Calculation Object');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1022005','Edit Data Channnel in Calculation Object','1022','EditDataChannelInCalcObj',0,'Edit Data Channnel in Calculation Object');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1022006','Delete Data Channnel in Calculation Object','1022','DelDataChannelInCalcObj',0,'Delete Data Channnel in Calculation Object');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013021','Edit Calculation Object to Line','1013','EditCalculationObjectToLine',0,'Edit Calculation Object to Line');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013022','Edit Calculation Object to Transformer','1013','EditCalculationObjectToTransformer',0,'Edit Calculation Object to Transformer');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013023','Edit Calculation Object to Organization','1013','EditCalculationObjectToOrg',0,'Edit Calculation Object to Organization');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013024','Delete Calculation Object to Line','1013','DeleteCalculationObjectToLine',0,'Delete Calculation Object to Line');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013025','Delete Calculation Object to Transformer','1013','DeletetCalculationObjectToTransformer',0,'Delete Calculation Object to Transformer');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013026','Delete Calculation Object to Organization','1013','DeleteCalculationObjectToOrg',0,'Delete Calculation Object to Organization');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013101','Meter Tab Page','1013','ViewMeterManagementPage',0,'View Meter Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013102','Communicator Tab Page','1013','ViewCommunicatorManagementPage',0,'View Communicator Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013103','Line Tab Page','1013','ViewLineManagementPage',0,'View Line Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013104','Transformer Tab Page','1013','ViewTransformerManagementPage',0,'View Transformer Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013105','Organization Tab Page','1013','ViewOrganizationManagementPage',0,'View Organization Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013005','Edit '||'&'||' Save Commnicator','1013','EditAndSaveCommunicator',0,'Edit '||'&'||' Save Commnicator');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013006','Delete Commnicator','1013','DeleteCommunicator',0,'Delete Commnicator');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013007','Add Commnicator','1013','AddCommunicator',0,'Add Commnicator');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013008','Edit '||'&'||' Save Line','1013','EditAndSaveLine',0,'Edit '||'&'||' Save Line');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013009','Delete Line','1013','DeleteLine',0,'Delete Line');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013010','Add Line','1013','AddLine',0,'Add Line');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013011','Edit '||'&'||' Save Transformer','1013','EditAndSaveTransformer',0,'Edit '||'&'||' Save Transformer');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013012','Delete Transformer','1013','DeleteTransformer',0,'Delete Transformer');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013013','Add Transformer','1013','AddTransformer',0,'Add Transformer');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013014','Edit '||'&'||' Save Organization','1013','EditAndSaveOrganization',0,'Edit '||'&'||' Save Organization');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006001','TOU Tab Page','1006','ViewTouManagementPage',0,'View TOU Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006002','Measurement Tab Page','1006','ViewMeasurementManagementPage',0,'View Measurement Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006003','Limiter Tab Page','1006','ViewLimiterManagementPage',0,'View Limiter Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006004','Step Tariff Tab Page','1006','ViewStepTariffManagementPage',0,'View Step Tariff Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006013','Edit '||'&'||' Save TOU group','1006','EditAndSaveTouGroup',0,'Edit '||'&'||' Save TOU group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006014','Edit '||'&'||' Save Measurement group','1006','EditAndSaveMeasurementGroup',0,'Edit '||'&'||' Save Measurement group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006015','Edit '||'&'||' Save Limiter group','1006','EditAndSaveLimiterGroup',0,'Edit '||'&'||' Save Limiter group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006016','Edit '||'&'||' Save Step Tariff group','1006','EditSaveStepTariffGroup',0,'Edit '||'&'||' Save Step Tariff group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1007001','View Set Tab Page','1007','ViewSetTabPage',0,'View Set Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1007002','View Get Tab Page','1007','ViewGetTabPage',0,'View Get Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1009101','View Plan Tab Page','1009','FirmViewPlanTabPage',0,'View Plan Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1009102','View Plan Report Tab Page','1009','FirmViewPlanReportTabPage',0,'View Plan Report Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1009103','View Job Report Tab Page','1009','FirmViewJobReportTabPage',0,'View Job Report Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1020101','View Plan Tab Page','1020','ViewPlanTabPage',0,'View Plan Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1020102','View Plan Report Tab Page','1020','ViewPlanReportTabPage',0,'View Plan Report Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1020103','View Job Report Tab Page','1020','ViewJobReportTabPage',0,'View Job Report Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1012001','Connect','1012','ExecuteConnectCommandToMeter',0,'Execute a Connect Command to Meter');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1012002','Disconnect','1012','ExecuteDisConnectCommandToMeter',0,'Execute a Disconnect Command to Meter');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017001','View User tab page','1017','ViewUserTabPage',0,'View User tab page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017002','View Role tab page','1017','ViewRoleTabPage',0,'View Role tab page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017003','View Organization tab page','1017','ViewOrganizationTabPage',0,'View Organization tab page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1015001','View Metering Tracing Log tab page','1015','ViewMeteringTracingLogTabPage',0,'View Metering Tracing Log tab page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1015002','View Time Synchronization Log tab page','1015','ViewTimeSynchronizationLogTabPage',0,'View Time Synchronization Log tab page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1015003','View System Integration Log tab page','1015','ViewSystemIntegrationLogTabPage',0,'View System Integration Log tab page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1015004','View User Log tab page','1015','ViewUserLogTabPage',0,'View User Log tab page');