package com.clou.esp.hes.app.web.service.impl.interfaces;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.util.StringUtils;

import ch.iec.tc57._2011.meterdefineconfig.FaultMessage;
import ch.iec.tc57._2011.meterdefineconfig.ReplyMeterDefineConfigPort;
import ch.iec.tc57._2011.meterdefineconfig_.Meter;
import ch.iec.tc57._2011.meterdefineconfig_.MeterDefineConfig;
import ch.iec.tc57._2011.meterdefineconfig_.MeterDefineConfig.ReadingType;
import ch.iec.tc57._2011.meterdefineconfig_.Structures;
import ch.iec.tc57._2011.meterdefineconfig_.Values;
import ch.iec.tc57._2011.meterdefineconfigmessage.MeterDefineConfigPayloadType;
import ch.iec.tc57._2011.meterdefineconfigmessage.MeterDefineConfigResponseMessageType;
import ch.iec.tc57._2011.schema.message.HeaderType;
import ch.iec.tc57._2011.schema.message.ReplyType;
import ch.iec.tc57._2011.schema.message.UserType;
import clouesp.hes.core.uci.soap.custom.webservice.WebserviceBuilder;
import clouesp.hes.core.uci.soap.custom.webservice.common.DcuDlmsIdEnum;

import com.clou.esp.hes.app.web.core.pushlet.PushletData;
import com.clou.esp.hes.app.web.core.util.DateTimeFormatterUtil;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.core.util.XMLUtil;
import com.clou.esp.hes.app.web.model.asset.DataChannel;
import com.clou.esp.hes.app.web.model.asset.FriendlyPeriod;
import com.clou.esp.hes.app.web.model.asset.FriendlySpecialDay;
import com.clou.esp.hes.app.web.model.asset.FriendlyWeekDay;
import com.clou.esp.hes.app.web.model.asset.MeterConfiguration;
import com.clou.esp.hes.app.web.model.asset.ProfileInterval;
import com.clou.esp.hes.app.web.model.asset.StepTariff;
import com.clou.esp.hes.app.web.model.dict.DictProfile;
import com.clou.esp.hes.app.web.service.dict.DictProfileService;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.redis.JedisUtils;

public class ReplyMeterDefineConfigPort_port implements
		ReplyMeterDefineConfigPort {
	final static String TIME_FLAG =ResourceUtil.getSessionattachmenttitle("local.time.flag");
	 
	private static Logger logger = Logger.getLogger(ReplyMeterDefineConfigPort_port.class);
	
	
	@Resource
    private DictProfileService dictProfileService;
	
	
	@SuppressWarnings("unchecked")
	@Override
	public void createdMeterDefineConfig(MeterDefineConfigResponseMessageType createdMeterDefineConfigMessage)throws FaultMessage {
		//XMLUtil.convertToXml(createdMeterDefineConfigMessage,"C:\\Users\\<USER>\\Desktop\\白建云\\"+createdMeterDefineConfigMessage.getPayload().getMeterDefineConfig().getReadingType().getRef()+".xml");
	try{
		// 协议类型区分处理  2018-07-23 baijun
		HeaderType headerTemp = createdMeterDefineConfigMessage.getHeader();
		//todo edison获取dataitemid去 判断选择哪个方法
		MeterDefineConfigPayloadType payload = createdMeterDefineConfigMessage.getPayload();
			
		if(null != headerTemp.getRevision() && !"".equals(headerTemp.getRevision())) {
			if("200".equalsIgnoreCase(headerTemp.getRevision()) || "300".equalsIgnoreCase(headerTemp.getRevision())) {// 376.1 协议
				//WebserviceBuilder.buildMeterDefineConfigReadingWritingResponse(createdMeterDefineConfigMessage);
				WebserviceBuilder.buildMeterDefineConfigReadingWritingMoreResponse(createdMeterDefineConfigMessage);
				return;
			}
		}else if(StringUtils.isEmpty(headerTemp.getRevision()) || "100".equalsIgnoreCase(headerTemp.getRevision())) {// dlms
			//WebserviceBuilder.buildMeterDefineConfigReadingWritingResponse(createdMeterDefineConfigMessage);
			 if(null != payload) {
				String dataItemId = payload.getMeterDefineConfig().getReadingType().getRef();
				logger.warn("createdMeterDefineConfigId:"+ dataItemId);
				if(DcuDlmsIdEnum.parse(dataItemId) != null){
					logger.warn("createdMeterDefineConfigName:"+ DcuDlmsIdEnum.parse(dataItemId).getName());
					WebserviceBuilder.buildMeterDefineConfigReadingWritingMoreResponse(createdMeterDefineConfigMessage);
					return;
				}
		     }

		}
		
		AjaxJson json = new AjaxJson();
		HeaderType header = createdMeterDefineConfigMessage.getHeader();
		ReplyType reply = createdMeterDefineConfigMessage.getReply();
		if(header==null || payload==null || header.getUser()==null || reply==null){
			return;
		}
		UserType user = header.getUser();			//用户信息，存放userId和orgId
		String messageId = header.getMessageID();	//存放task list的messageId
		MeterDefineConfig meterConfig = payload.getMeterDefineConfig();	//获取前置机返回的数据信息
		ReadingType readingType = meterConfig.getReadingType();			//获取readingType的信息，即ParamterItemId
		if(readingType == null){
			return;
		}
		//表示为GET操作
		if(header.getVerb().equals("reply")){
			//type=1表示抄读
			json.put("type", "1");
			MeterDefineConfig meterDefineConfig = payload.getMeterDefineConfig();
			if(meterDefineConfig==null||meterDefineConfig.getReadingType()==null){
				return ;
			}
			json.put("rateId", meterDefineConfig.getReadingType().getRef());
			/*
			 * 拦截失败的返回值，当result ！= OK时
			 */
			if(meterDefineConfig.getReadingType().getRef().indexOf("38.0.0.") < 0){
				if(reply == null || !reply.getResult().toUpperCase().equals("OK")){
					json.setErrorMsg(reply.getResult());
					
					List<String> HYList = new ArrayList<>();
					// LCD配置下发
					HYList.add("43.0.0.1");
					HYList.add("43.0.0.2");
					HYList.add("43.0.0.3");
					HYList.add("43.0.0.4");
					
					if (HYList.contains(meterDefineConfig.getReadingType().getRef())) {
						String errorMsg = reply.getResult();
						json.put("msg", errorMsg);
						json.setMsg(errorMsg);
						PushletData.pushlet("meterConfigurationHY", json, user.getUserID());
					} else {
						PushletData.pushlet("meterConfiguration", json, user.getUserID());
					}
					return ;
				}
			}
			
			json.put("touGroupName", "");
			json.put("limiterGroupName", "");
			json.put("messageId", messageId);
			switch (meterDefineConfig.getReadingType().getRef()) {
				case "37.0.1.5":
					List<Structures> structures1= meterDefineConfig.getArrays().get(0).getStructures();
					List<Map<String,Object>> dayList1=new ArrayList<Map<String,Object>>();
					for(Structures d:structures1){
						Map<String,Object> m=new HashMap<String,Object>();
						m.put("id", d.getValues().get(0).getValue());
						m.put("dayName","Day "+ d.getValues().get(0).getValue().replace("d", ""));
						m.put("startTime", "");
						m.put("rate", "");
						m.put("isLeaf", "false");
						m.put("expanded", "true");
						m.put("loaded", "true");
						m.put("parent", "");
						dayList1.add(m);
						List<Structures> intervals=d.getArrays().get(0).getStructures();
						int i=0;
						for(;i<intervals.size();i++){
							List<Values> values= intervals.get(i).getValues();
							Map<String,Object> im=new HashMap<String,Object>();
							im.put("id", "d"+d.getValues().get(0).getValue()+"i"+(i+1));
							im.put("dayName","Interval "+ (i+1));
							im.put("parent", d.getValues().get(0).getValue());
							im.put("startTime", values.get(0).getValue());
							im.put("rate", values.get(2).getValue());
							im.put("isLeaf", "true");
							im.put("expanded", "false");
							im.put("loaded", "false");
							dayList1.add(im);
						}
					}
					json.put("dayList", dayList1);
					break;
				case "37.0.1.4":
					List<Structures> wstructures1= meterDefineConfig.getArrays().get(0).getStructures();
					List<Map<String,Object>> weekList1=new ArrayList<Map<String,Object>>();
					for(Structures d:wstructures1){
						List<Values> values= d.getValues();
						String wId="";
						Map<String,Object> m=new HashMap<String,Object>();
						wId=values.get(0).getValue();
						m.put("id", wId);
						m.put("weekName","Week "+ wId.replace("w", ""));
						m.put("dayProfile", "");
						m.put("isLeaf", "false");
						m.put("expanded", "true");
						m.put("loaded", "true");
						m.put("parent", "");
						weekList1.add(m);
						for(int i=0;i<values.size();i++){
							Map<String,Object> m1=new HashMap<String,Object>();
							if(i==0){
								continue;
							}
							m1.put("id", wId+"d"+i);
							switch (""+i) {
							case "1":
								m1.put("weekName","Sunday");
								break;
							case "2":
								m1.put("weekName","Monday");
								break;
							case "3":
								m1.put("weekName","Tuesday");
								break;
							case "4":
								m1.put("weekName","Wednesday");
								break;
							case "5":
								m1.put("weekName","Thursday");
								break;
							case "6":
								m1.put("weekName","Friday");
								break;
							case "7":
								m1.put("weekName","Saturday");
								break;
							}
							m1.put("dayProfile", values.get(i).getValue());
							m1.put("isLeaf", "true");
							m1.put("expanded", "false");
							m1.put("loaded", "false");
							m1.put("parent", wId);
							weekList1.add(m1);
						}
					}
					json.put("weekList", weekList1);
					break;
				case "37.0.1.3":
					List<Structures> sstructures1= meterDefineConfig.getArrays().get(0).getStructures();
					List<Map<String,Object>> seasonList1=new ArrayList<Map<String,Object>>();
					for(Structures d:sstructures1){
						List<Values> values= d.getValues();		
						Map<String,Object> m=new HashMap<String,Object>();
						m.put("id", values.get(0).getValue());
						m.put("seasonName","Season "+ values.get(0).getValue().replace("s", ""));
						//修改season日期格式
//						String[] timeArr = values.get(1).getValue().split(" ");
//						String[] dateArr = timeArr[0].split("-");
//						m.put("startTime", dateArr[1] + "/" + dateArr[2] + "/" + dateArr[0] + " " + timeArr[1]);
						m.put("startTime",DateTimeFormatterUtil.getSimpleDateFormat_HHmmssStr(values.get(1).getValue(),TIME_FLAG));
						
						m.put("weekProfile", values.get(2).getValue());
						seasonList1.add(m);
					}
					json.put("seasonList", seasonList1);
					break;
				case "37.0.1.9":
					List<Structures> structures= meterDefineConfig.getArrays().get(0).getStructures();
					List<Map<String,Object>> dayList=new ArrayList<Map<String,Object>>();
					for(Structures d:structures){
						Map<String,Object> m=new HashMap<String,Object>();
						m.put("id", d.getValues().get(0).getValue());
						m.put("dayName","Day "+ d.getValues().get(0).getValue().replace("d", ""));
						m.put("startTime", "");
						m.put("rate", "");
						m.put("isLeaf", "false");
						m.put("expanded", "true");
						m.put("loaded", "true");
						m.put("parent", "");
						dayList.add(m);
						List<Structures> intervals=d.getArrays().get(0).getStructures();
						int i=0;
						for(;i<intervals.size();i++){
							List<Values> values= intervals.get(i).getValues();
							Map<String,Object> im=new HashMap<String,Object>();
							im.put("id", "d"+d.getValues().get(0).getValue()+"i"+(i+1));
							im.put("dayName","Interval "+ (i+1));
							im.put("parent", d.getValues().get(0).getValue());
							im.put("startTime", values.get(0).getValue());
							im.put("rate", values.get(2).getValue());
							im.put("isLeaf", "true");
							im.put("expanded", "false");
							im.put("loaded", "false");
							dayList.add(im);
						}
					}
					json.put("dayList", dayList);
					break;
				case "37.0.1.8":
					List<Structures> wstructures= meterDefineConfig.getArrays().get(0).getStructures();
					List<Map<String,Object>> weekList=new ArrayList<Map<String,Object>>();
					for(Structures d:wstructures){
						List<Values> values= d.getValues();
						String wId="";
						Map<String,Object> m=new HashMap<String,Object>();
						wId=values.get(0).getValue();
						m.put("id", wId);
						m.put("weekName","Week "+ wId.replace("w", ""));
						m.put("dayProfile", "");
						m.put("isLeaf", "false");
						m.put("expanded", "true");
						m.put("loaded", "true");
						m.put("parent", "");
						weekList.add(m);
						for(int i=0;i<values.size();i++){
							Map<String,Object> m1=new HashMap<String,Object>();
							if(i==0){
								continue;
							}
							m1.put("id", wId+"d"+i);
							switch (""+i) {
							case "1":
								m1.put("weekName","Sunday");
								break;
							case "2":
								m1.put("weekName","Monday");
								break;
							case "3":
								m1.put("weekName","Tuesday");
								break;
							case "4":
								m1.put("weekName","Wednesday");
								break;
							case "5":
								m1.put("weekName","Thursday");
								break;
							case "6":
								m1.put("weekName","Friday");
								break;
							case "7":
								m1.put("weekName","Saturday");
								break;
							}
							m1.put("dayProfile", values.get(i).getValue());
							m1.put("isLeaf", "true");
							m1.put("expanded", "false");
							m1.put("loaded", "false");
							m1.put("parent", wId);
							weekList.add(m1);
						}
					}
					json.put("weekList", weekList);
					break;
				case "37.0.1.7":
					List<Structures> sstructures= meterDefineConfig.getArrays().get(0).getStructures();
					List<Map<String,Object>> seasonList=new ArrayList<Map<String,Object>>();
					for(Structures d:sstructures){
						List<Values> values= d.getValues();		
						Map<String,Object> m=new HashMap<String,Object>();
						m.put("id", values.get(0).getValue());
						m.put("seasonName","Season "+ values.get(0).getValue().replace("s", ""));
						//修改season日期格式
//						String[] timeArr = values.get(1).getValue().split(" ");
//						String[] dateArr = timeArr[0].split("-");
//						m.put("startTime", dateArr[1] + "/" + dateArr[2] + "/" + dateArr[0] + " " + timeArr[1]);
						m.put("startTime",DateTimeFormatterUtil.getSimpleDateFormat_HHmmssStr(values.get(1).getValue(),TIME_FLAG));
						
						m.put("weekProfile", values.get(2).getValue());
						seasonList.add(m);
					}
					json.put("seasonList", seasonList);
					break;
				case "37.2.1.1":
					List<Structures> sdstructures = meterDefineConfig.getArrays().get(0).getStructures();
					List<Map<String,Object>> specialDayList=new ArrayList<Map<String,Object>>();
					for(Structures d:sdstructures){
						List<Values> values= d.getValues();		
						Map<String,Object> m=new HashMap<String,Object>();
						m.put("id", values.get(0).getValue());
						m.put("specialDayName","Secial Day "+ values.get(0).getValue());
						//修改special day日期
//						if(values.get(1).getValue().indexOf(",") > 0){
//							String[] dateArr = values.get(1).getValue().split(",");
//							String[] day = dateArr[0].split("-");
//							m.put("date", day[1] + "/" + day[2] + "/" + day[0] + "," + dateArr[1]);
//						}else{
//							String[] day = values.get(1).getValue().split("-");
//							m.put("date", day[1] + "/" + day[2] + "" + day[0]);
						
//						}
						m.put("date",DateTimeFormatterUtil.getSimpleDateFormatStr(values.get(1).getValue(),TIME_FLAG));
						
						
//							m.put("date", values.get(1).getValue());
						m.put("dayProfile", values.get(2).getValue());
						specialDayList.add(m);
					}
					json.put("specialDayList", specialDayList);
					break;
				case "40.0.0.18":
					System.out.println("40.0.0.18");
					List<FriendlyPeriod> friendlyPeriodList = new ArrayList<>();
				
					List<Structures> sfstructures = meterDefineConfig.getArrays().get(0).getStructures();
					int i = 0;
					for(Structures d:sfstructures){
						i++;
						FriendlyPeriod friendlyPeriod = new FriendlyPeriod();
						List<Values> values= d.getValues();		
						for(Values values1 : values){
							if("start_time".equals(values1.getType())){
								friendlyPeriod.setStart(values1.getValue());
							}else if("end_time".equals(values1.getType())){
								friendlyPeriod.setEnd(values1.getValue());
							}
						}
						friendlyPeriod.setId("Period "+i);
						friendlyPeriod.setPeriodName("Period "+i);
						friendlyPeriodList.add(friendlyPeriod);
					}
					
					json.put("friendlyPeriodList", friendlyPeriodList);
					break;
				case "40.0.0.19":
					System.out.println("40.0.0.19");
					
					List<FriendlyWeekDay> friendlyWeekList = new ArrayList<>();
					List<Values>list = meterDefineConfig.getArrays().get(0).getValues();
					if(list != null && list.size() > 0){
						for(Values values1 : list){
							
							String weekDayStr = values1.getValue();
                            if(weekDayStr.length() == 7){
                            	for(int k = 0; k < weekDayStr.length(); k++ ){
                            		FriendlyWeekDay friendlyWeekDay = new FriendlyWeekDay();
                            		if("1".equals(String.valueOf(weekDayStr.charAt(k)))){
                            			friendlyWeekDay.setStatus("Yes");
                            		}else if("0".equals(String.valueOf(weekDayStr.charAt(k)))){
                            			friendlyWeekDay.setStatus("No");
                            		}
                            		if(k == 0){
                            			friendlyWeekDay.setWeekDay("Monday");
                            		}else if(k == 1){
                            			friendlyWeekDay.setWeekDay("Tuesday");
                            		}else if(k == 2){
                            			friendlyWeekDay.setWeekDay("Wednesday");
                            		}else if(k == 3){
                            			friendlyWeekDay.setWeekDay("Thursday");
                            		}else if(k == 4){
                            			friendlyWeekDay.setWeekDay("Friday");
                            		}else if(k == 5){
                            			friendlyWeekDay.setWeekDay("Saturday");
                            		}else if(k == 6){
                            			friendlyWeekDay.setWeekDay("Sunday");
                            		}
                            		
                            		friendlyWeekDay.setId(friendlyWeekDay.getWeekDay());
                            		friendlyWeekList.add(friendlyWeekDay);
                            	}
                            
                            }
						}
					}
					json.put("friendlyWeekList", friendlyWeekList);
					break;
				case "40.0.0.20":
					
					List<FriendlySpecialDay> friendlyDayList = new ArrayList<>();
					System.out.println("40.0.0.20");
					
					List<Structures> ststructures = meterDefineConfig.getArrays().get(0).getStructures();
				
					for(Structures d:ststructures){
					
						List<Values> values= d.getValues();		
						if(values != null && values.size() > 0){
							int j =0;
							for(int k = 0 ; k < values.size(); k++){
								j++;
								if (k == 1){
									FriendlySpecialDay friendlySpecialDay = new FriendlySpecialDay();
									friendlySpecialDay.setId("Special Day "+j);
									friendlySpecialDay.setSpecialDayName("Special Day "+j);
									
									friendlySpecialDay.setDate(values.get(k).getValue());
									friendlyDayList.add(friendlySpecialDay);
								}
							}
						}
						
					}
					
					json.put("friendlyDayList", friendlyDayList);
					break;
					// 自动轮显list
				case "43.0.0.1":
					// 手动轮显list
				case "43.0.0.2": 
					List<Structures> test1 = meterDefineConfig.getArrays().get(0).getStructures();
					
					List<String> channelList = new ArrayList<>();
					
					for (Structures s : test1) {
						String v = s.getValues().get(0).getValue();
						// 数据项的ID
						channelList.add(v);
					}
					json.put("success", true);
					json.put("read", true);
					json.put("subType", "hy_list");
					json.put("channelList", channelList);
					json.put("msg", "read successfully");		
					json.setMsg("read successfully");
					PushletData.pushlet("meterConfigurationHY", json, user.getUserID());
					return;
					 // 自动轮显period
				case "43.0.0.3":
					 // 手动轮显period
				case "43.0.0.4":
					String value = meterDefineConfig.getArrays().get(0).getValues().get(0).getValue();
					json.put("success", true);
					json.put("read", true);
					json.put("subType", "hy_period");
					json.put("period", value);
					json.put("msg", "read successfully");
					json.setMsg("read successfully");
					PushletData.pushlet("meterConfigurationHY", json, user.getUserID());
					return;
				default:
					break;
			}
			if(meterDefineConfig.getReadingType().getRef().indexOf("38.0.0.") >= 0){
				List<Map<String,Object>> limiterList = new ArrayList<Map<String,Object>>();
					Map<String,Object> m = new HashMap<String, Object>();
					m.put("id", meterDefineConfig.getReadingType().getRef());
					m.put("name", meterDefineConfig.getReadingType().getRef());
					if(reply == null || !reply.getResult().toUpperCase().equals("OK")){
						m.put("value",reply.getResult());
						m.put("status", "0");
					}else{
						if(meterDefineConfig.getArrays() != null && meterDefineConfig.getArrays().size() > 0 && 
								meterDefineConfig.getArrays().get(0).getValues() != null && meterDefineConfig.getArrays().get(0).getValues().size() > 0){
							Values value = meterDefineConfig.getArrays().get(0).getValues().get(0);
							m.put("value", value.getValue());
						}else{
							m.put("value", "0");
						}
						m.put("status", "1");
					}
					limiterList.add(m);
					json.put("limiterList", limiterList);
			}
					
			if(meterDefineConfig.getReadingType().getRef().indexOf("0.0.0.0") >= 0){
					List<DataChannel> dataChannelList = new ArrayList<>();
				
					List<Values> dataChannelArrays = new ArrayList<>();
					int k = 0;
					for(Structures structures : meterDefineConfig.getArrays().get(0).getStructures()){
						dataChannelArrays = structures.getValues();
						StringBuffer sb = new StringBuffer();
						for (int i = 0; i < dataChannelArrays.size()-1; i++) {
							if(i == 2){
								sb.append(dataChannelArrays.get(i).getValue());
							}else{
								sb.append(dataChannelArrays.get(i).getValue()+"#");
							}
						}
						
						DataChannel dataChannel = new DataChannel();
						dataChannel.setId(k+++"");
						dataChannel.setProtocolCode(sb.toString());
						DictProfile dictProfile = new DictProfile();
						dictProfile.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
						dictProfile.setProtocolCode(sb.toString());
						List<DictProfile> dictProfileList = dictProfileService.getList(dictProfile);
						if(dictProfileList != null && dictProfileList.size() > 0){
							dataChannel.setDataItemName(dictProfileList.get(0).getName());
						}else{
							dataChannel.setDataItemName("");
						}
						
						dataChannelList.add(dataChannel);
							
					}

					json.put("dataChannelList", dataChannelList);
			 }
						
					
			if(meterDefineConfig.getReadingType().getRef().indexOf("0.0.0.1") >= 0){
				
				List<ProfileInterval> profileIntervalList = new ArrayList<>();
				
				List<Values> profileIntervalArrays = new ArrayList<>();
				profileIntervalArrays = meterDefineConfig.getArrays().get(0).getValues();
				for (int i = 0; i < profileIntervalArrays.size(); i++) {
					ProfileInterval profileInterval = new ProfileInterval();
					profileInterval.setId(i+"");
					profileInterval.setProfileInterval(profileIntervalArrays.get(i).getValue());
					profileIntervalList.add(profileInterval);
				}
				
				json.put("profileIntervalList", profileIntervalList);
		
			}
			
			
			/**
			 *	40.0.3.1 Step tariff 阶梯汇率
			 *	40.0.3.2 Step tariff activation time 阶梯汇率激活时间
			 *	40.0.2.5 Unit charge passive 备份电价
			 *	40.0.2.6 Unit charge activation time 备份电价激活时间
			 *  与tou tariff使用同一类ID  此处添加特殊判断
			 *  40.0.0.0.0.1 step 40.0.0.0.0.2 tou todoedison 返回解析展示
			 */
			if(meterDefineConfig.getReadingType().getRef().indexOf("40.0") >= 0){
				System.out.println(XMLUtil.convertToXml(createdMeterDefineConfigMessage));
				List<StepTariff> stepTariffList = new ArrayList<>();
				List<Values> stepArrays = new ArrayList<>();
				List<Structures> priceArrays = new ArrayList<>();
				if("40.0.3.1".equals(meterDefineConfig.getReadingType().getRef())){		//阶梯汇率
					stepArrays = meterDefineConfig.getArrays().get(0).getStructures().get(0).getValues();
					for (int i = 0; i < stepArrays.size(); i++) {
						StepTariff entity = new StepTariff();
						entity.setId("Step " + (i+1));
						entity.setStepName("Step " + (i+1));
						BigDecimal bd2 = new BigDecimal("100");//meter里面放的是10w
						//初始值
						if(i == 0){
							entity.setStartQuantity("0");
						}else{
							BigDecimal startQuantity = new BigDecimal(stepArrays.get(i-1).getValue());
							entity.setStartQuantity(String.valueOf(startQuantity.divide(bd2).setScale(3, BigDecimal.ROUND_DOWN)));
						}
						BigDecimal endQuantity = new BigDecimal(stepArrays.get(i).getValue());
						entity.setEndQuantity(String.valueOf(endQuantity.divide(bd2).setScale(3, BigDecimal.ROUND_DOWN)));
						stepTariffList.add(entity);
					}
					json.put("stepTariffList", stepTariffList);
				}else if("40.0.3.2".equals(meterDefineConfig.getReadingType().getRef())){
					
				}else if("40.0.2.5".equals(meterDefineConfig.getReadingType().getRef())){	//备份电价
					priceArrays = meterDefineConfig.getArrays().get(0).getStructures().get(2).getArrays().get(0).getStructures();
					for (int i = 0; i < priceArrays.size(); i++) {
						//如果当前电价 == 上一个电价值，则跳出循环（最后一个电价后面的为废数据）
/*						if(i > 0){
							if(priceArrays.get(i).getValues().get(1).getValue().equals(priceArrays.get(i-1).getValues().get(1).getValue())){
								break;
							}
						}*/
						StepTariff entity = new StepTariff();
						entity.setId("Step " + (i+1));
						BigDecimal bd2 = new BigDecimal("10000");
						BigDecimal price = new BigDecimal(priceArrays.get(i).getValues().get(1).getValue());
						entity.setPrice(String.valueOf(price.divide(bd2).setScale(4, BigDecimal.ROUND_DOWN)));
						stepTariffList.add(entity);
					}
					json.put("stepTariffList", stepTariffList);
				}else if("40.0.2.6".equals(meterDefineConfig.getReadingType().getRef())){
					
				}else if("40.0.2.4".equals(meterDefineConfig.getReadingType().getRef())){	//备份电价
					priceArrays = meterDefineConfig.getArrays().get(0).getStructures().get(2).getArrays().get(0).getStructures();
					for (int i = 0; i < priceArrays.size(); i++) {
						//如果当前电价 == 上一个电价值，则跳出循环（最后一个电价后面的为废数据）
/*						if(i > 0){
							if(priceArrays.get(i).getValues().get(1).getValue().equals(priceArrays.get(i-1).getValues().get(1).getValue())){
								break;
							}
						}*/
						StepTariff entity = new StepTariff();
						entity.setId("Step " + (i+1));
						BigDecimal bd2 = new BigDecimal("10000");
						BigDecimal price = new BigDecimal(priceArrays.get(i).getValues().get(1).getValue());
						entity.setPrice(String.valueOf(price.divide(bd2).setScale(4, BigDecimal.ROUND_DOWN)));
						stepTariffList.add(entity);
					}
					json.put("stepTariffList", stepTariffList);
				}else if("40.0.3.0".equals(meterDefineConfig.getReadingType().getRef())){	//备份电价
					stepArrays = meterDefineConfig.getArrays().get(0).getStructures().get(0).getValues();
					for (int i = 0; i < stepArrays.size(); i++) {
						StepTariff entity = new StepTariff();
						entity.setId("Step " + (i+1));
						entity.setStepName("Step " + (i+1));
						BigDecimal bd2 = new BigDecimal("100");//meter里面放的是10w
						//初始值
						if(i == 0){
							entity.setStartQuantity("0");
						}else{
							BigDecimal startQuantity = new BigDecimal(stepArrays.get(i-1).getValue());
							entity.setStartQuantity(String.valueOf(startQuantity.divide(bd2).setScale(3, BigDecimal.ROUND_DOWN)));
						}
						BigDecimal endQuantity = new BigDecimal(stepArrays.get(i).getValue());
						entity.setEndQuantity(String.valueOf(endQuantity.divide(bd2).setScale(3, BigDecimal.ROUND_DOWN)));
						stepTariffList.add(entity);
					}
					json.put("stepTariffList", stepTariffList);
				}
			}else if(meterDefineConfig.getReadingType().getRef().indexOf("41.0") >= 0){
				System.out.println(XMLUtil.convertToXml(createdMeterDefineConfigMessage));
				List<StepTariff> stepTariffList = new ArrayList<>();
				List<Structures> priceArrays = new ArrayList<>();
				if("41.0.3.1".equals(meterDefineConfig.getReadingType().getRef())){		//阶梯汇率
				
				}else if("41.0.3.2".equals(meterDefineConfig.getReadingType().getRef())){
					
				}else if("41.0.2.5".equals(meterDefineConfig.getReadingType().getRef())){	//备份电价 还没有调试完成的代码TODO
					priceArrays = meterDefineConfig.getArrays().get(0).getStructures().get(2).getArrays().get(0).getStructures();
					for (int i = 0; i < priceArrays.size(); i++) {
						//如果当前电价 == 上一个电价值，则跳出循环（最后一个电价后面的为废数据）
/*						if(i > 0){
							if(priceArrays.get(i).getValues().get(1).getValue().equals(priceArrays.get(i-1).getValues().get(1).getValue())){
								break;
							}
						}*/
						if(i == 0){
							StepTariff entity = new StepTariff();
							entity.setId("TOU 1");
							entity.setStepName("TOU 1");
							BigDecimal bd2 = new BigDecimal("10000");
							BigDecimal price = new BigDecimal(priceArrays.get(i).getValues().get(1).getValue());
							entity.setPrice(String.valueOf(price.divide(bd2).setScale(4, BigDecimal.ROUND_DOWN)));
							stepTariffList.add(entity);
						}else if(i == 10){
							StepTariff entity = new StepTariff();
							entity.setId("TOU 2");
							entity.setStepName("TOU 2");
							BigDecimal bd2 = new BigDecimal("10000");
							BigDecimal price = new BigDecimal(priceArrays.get(i).getValues().get(1).getValue());
							entity.setPrice(String.valueOf(price.divide(bd2).setScale(4, BigDecimal.ROUND_DOWN)));
							stepTariffList.add(entity);
						}else if(i == 20){
							StepTariff entity = new StepTariff();
							entity.setId("TOU 3");
							entity.setStepName("TOU 3");
							BigDecimal bd2 = new BigDecimal("10000");
							BigDecimal price = new BigDecimal(priceArrays.get(i).getValues().get(1).getValue());
							entity.setPrice(String.valueOf(price.divide(bd2).setScale(4, BigDecimal.ROUND_DOWN)));
							stepTariffList.add(entity);
						}else if(i == 30){
							StepTariff entity = new StepTariff();
							entity.setId("TOU 4 ");
							entity.setStepName("TOU 4");
							BigDecimal bd2 = new BigDecimal("10000");
							BigDecimal price = new BigDecimal(priceArrays.get(i).getValues().get(1).getValue());
							entity.setPrice(String.valueOf(price.divide(bd2).setScale(4, BigDecimal.ROUND_DOWN)));
							stepTariffList.add(entity);
						}
						
					}
					json.put("touTariffList", stepTariffList);
				}else if("41.0.2.6".equals(meterDefineConfig.getReadingType().getRef())){
					
				}else if("41.0.2.4".equals(meterDefineConfig.getReadingType().getRef())){	//备份电价 还没有调试完成的代码TODO
					priceArrays = meterDefineConfig.getArrays().get(0).getStructures().get(2).getArrays().get(0).getStructures();
					for (int i = 0; i < priceArrays.size(); i++) {
						//如果当前电价 == 上一个电价值，则跳出循环（最后一个电价后面的为废数据）
/*						if(i > 0){
							if(priceArrays.get(i).getValues().get(1).getValue().equals(priceArrays.get(i-1).getValues().get(1).getValue())){
								break;
							}
						}*/
						if(i == 0){
							StepTariff entity = new StepTariff();
							entity.setId("TOU 1");
							entity.setStepName("TOU 1");
							BigDecimal bd2 = new BigDecimal("10000");
							BigDecimal price = new BigDecimal(priceArrays.get(i).getValues().get(1).getValue());
							entity.setPrice(String.valueOf(price.divide(bd2).setScale(4, BigDecimal.ROUND_DOWN)));
							stepTariffList.add(entity);
						}else if(i == 10){
							StepTariff entity = new StepTariff();
							entity.setId("TOU 2");
							entity.setStepName("TOU 2");
							BigDecimal bd2 = new BigDecimal("10000");
							BigDecimal price = new BigDecimal(priceArrays.get(i).getValues().get(1).getValue());
							entity.setPrice(String.valueOf(price.divide(bd2).setScale(4, BigDecimal.ROUND_DOWN)));
							stepTariffList.add(entity);
						}else if(i == 20){
							StepTariff entity = new StepTariff();
							entity.setId("TOU 3");
							entity.setStepName("TOU 3");
							BigDecimal bd2 = new BigDecimal("10000");
							BigDecimal price = new BigDecimal(priceArrays.get(i).getValues().get(1).getValue());
							entity.setPrice(String.valueOf(price.divide(bd2).setScale(4, BigDecimal.ROUND_DOWN)));
							stepTariffList.add(entity);
						}else if(i == 30){
							StepTariff entity = new StepTariff();
							entity.setId("TOU 4 ");
							entity.setStepName("TOU 4");
							BigDecimal bd2 = new BigDecimal("10000");
							BigDecimal price = new BigDecimal(priceArrays.get(i).getValues().get(1).getValue());
							entity.setPrice(String.valueOf(price.divide(bd2).setScale(4, BigDecimal.ROUND_DOWN)));
							stepTariffList.add(entity);
						}
						
					}
					json.put("touTariffList", stepTariffList);
				}
			}
			PushletData.pushlet("meterConfiguration", json, user.getUserID());
		}else{
			//type=2表示下发
			json.put("type", "2");
			Meter meter = meterConfig.getMeters().get(0);	//获取电表的参数信息，sn
			
			/**
			 * 获取接口返回的参数，更改界面task的状态信息
			 */
			json.put("channelId", readingType.getRef());
			json.put("timeStamp", DateUtils.xmlDate2Date(header.getTimestamp()));
			/**
			 * 获取redis里面的数据
			 * 区分两种情况：Passive Calendar为一种情况（下属三条参数全部回调成功，界面显示成功），
			 * 				Passive Calendar数据的redis获取ID为---参数根据电表SN号+参数编号；
			 * 				另一种情况是根据messageId获取map数据。
			 */
			List<String> strArr = new ArrayList<>();
			strArr.add("37.0.1.7");
			strArr.add("37.0.1.8");
			strArr.add("37.0.1.9");
			
			List<String> stepAndTouTariffList = new ArrayList<>();
			stepAndTouTariffList.add("40.0.3.1");
			stepAndTouTariffList.add("40.0.3.2");
			stepAndTouTariffList.add("40.0.2.5");
			stepAndTouTariffList.add("40.0.2.6");
			
			List<String> touTariffList = new ArrayList<>();
			touTariffList.add("41.0.3.1");
			touTariffList.add("41.0.3.2");
			touTariffList.add("41.0.2.5");
			touTariffList.add("41.0.2.6");
			
			List<String> HYList = new ArrayList<>();
			// LCD配置下发
//			HYList.add("43.0.0.1");
			HYList.add("43.0.0.1");
			HYList.add("43.0.0.2");
			HYList.add("43.0.0.3");
			HYList.add("43.0.0.4");
			// 手动对时
			HYList.add("39.0.0.5");
			// 手动对时
			HYList.add("39.0.0.7");
			// Limit Monitor Value
			HYList.add("38.0.0.61");
			
			// 杭研郁金香项目特殊处理
			if (meter != null) {
				if(HYList.contains(readingType.getRef())){
					if(reply != null && !reply.getResult().toUpperCase().equals("OK")){
						String returnCode = reply.getError().get(0).getCode();
						if(StringUtil.isNotEmpty(returnCode) && !returnCode.equals("0.0")){
							String errorMsg = reply.getResult();
							json.put("msg", errorMsg);
							json.setMsg(errorMsg);
							json.put("success", false)
							;
						}else{	//如果不存在返回码
							json.put("msg", "unknown mistake");
							json.setMsg("unknown mistake");
							json.put("success", false);
						}
						PushletData.pushlet("meterConfigurationHY", json, user.getUserID());
					} else {
						json.put("success", true);
						json.put("msg", "send successfully");
						json.setMsg("send successfully");
						PushletData.pushlet("meterConfigurationHY", json, user.getUserID());
					}
					return;
				}
			}
			
			if(meter != null){
				MeterConfiguration redisObj = new MeterConfiguration();
				if(strArr.contains(readingType.getRef())){
					redisObj = (MeterConfiguration) JedisUtils.getObject("37.0.1.6.0.0.0" + meter.getMRID());
				}else if(stepAndTouTariffList.contains(readingType.getRef())){
					redisObj = (MeterConfiguration) JedisUtils.getObject("40.0.0.0.0" + meter.getMRID());
				}else if(touTariffList.contains(readingType.getRef())){
					redisObj = (MeterConfiguration) JedisUtils.getObject("41.0.0.0.0" + meter.getMRID());
				}else{
					redisObj = (MeterConfiguration) JedisUtils.getObject(readingType.getRef() + meter.getMRID());
				}
				if(redisObj != null){
					/**
					 * 如果返回失败信息：
					 * 		返回码不是0.0，则修改状态为3 failure
					 * 		否则，修改返回码为2，success
					 */
					if(reply != null && !reply.getResult().toUpperCase().equals("OK")){
						String returnCode = reply.getError().get(0).getCode();
						if(StringUtil.isNotEmpty(returnCode) && !returnCode.equals("0.0")){
							String errorMsg = reply.getResult();
							//当返回失败时，且属于非活动日历的参数
							if(strArr.contains(readingType.getRef())){
								json.put("channelId", "37.0.1.6.0.0.0");
							}else if(stepAndTouTariffList.contains(readingType.getRef())){
								json.put("channelId", "40.0.0.0.0");
							}else if(touTariffList.contains(readingType.getRef())){
								json.put("channelId", "41.0.0.0.0");
							}
							System.out.println("error:" + readingType.getRef());
							json.put("reason", errorMsg);
							json.put("status", "3");
							redisObj.setStatus("3");
						}else{	//如果不存在返回码
							json.put("reason", "unknown mistake");
							json.put("status", "3");
							redisObj.setStatus("3");
						}
					}else{
						/**
						 * 判断是否Passive Calendar参数：
						 * 		设置成功的参数到redis中，每次调用取出redis中的数组进行比对，如果长度小于三，则还没有成功三条数据
						 * 		YES:终止执行，pushlet数据到前端显示
						 */
						if(strArr.contains(readingType.getRef())){
							//获取excute方法中缓存入redis中的数组
							List<String> strArrList = (List<String>) JedisUtils.getObject(meter.getMRID() + "37.0.1.6.0.0.0strArrList");
							if(!strArrList.contains(readingType.getRef())){
								strArrList.add(readingType.getRef());
								System.out.println(readingType.getRef());
								JedisUtils.setObject(meter.getMRID() + "37.0.1.6.0.0.0strArrList", strArrList, 0);
							}
							List<String> strArrList_redis = (List<String>) JedisUtils.getObject(meter.getMRID() + "37.0.1.6.0.0.0strArrList");
							if(strArrList_redis.size() < 3){
								return;
							}else{
								json.put("status", "2");
								json.put("channelId", "37.0.1.6.0.0.0");
								redisObj.setStatus("2");
								redisObj.setReponseTime(DateUtils.xmlDate2Date(header.getTimestamp()));
							}
						}else if(stepAndTouTariffList.contains(readingType.getRef())){
							//获取excute方法中缓存入redis中的数组
							List<String> strArrList = (List<String>) JedisUtils.getObject(meter.getMRID() + "40.0.0.0.0strArrList");
							if(!strArrList.contains(readingType.getRef())){
								strArrList.add(readingType.getRef());
								System.out.println(readingType.getRef());
								JedisUtils.setObject(meter.getMRID() + "40.0.0.0.0strArrList", strArrList, 0);
							}
							List<String> strArrList_redis = (List<String>) JedisUtils.getObject(meter.getMRID() + "40.0.0.0.0strArrList");
							if(strArrList_redis.size() < 4){
								return;
							}else{
								json.put("status", "2");
								json.put("channelId", "40.0.0.0.0");
								redisObj.setStatus("2");
								redisObj.setReponseTime(DateUtils.xmlDate2Date(header.getTimestamp()));
							}
						}else if(touTariffList.contains(readingType.getRef())){
							//获取excute方法中缓存入redis中的数组
							List<String> strArrList = (List<String>) JedisUtils.getObject(meter.getMRID() + "41.0.0.0.0strArrList");
							if(!strArrList.contains(readingType.getRef())){
								strArrList.add(readingType.getRef());
								System.out.println(readingType.getRef());
								JedisUtils.setObject(meter.getMRID() + "41.0.0.0.0strArrList", strArrList, 0);
							}
							List<String> strArrList_redis = (List<String>) JedisUtils.getObject(meter.getMRID() + "41.0.0.0.0strArrList");
							if(strArrList_redis.size() < 2){
								return;
							}else{
								json.put("status", "2");
								json.put("channelId", "41.0.0.0.0");
								redisObj.setStatus("2");
								redisObj.setReponseTime(DateUtils.xmlDate2Date(header.getTimestamp()));
							}
						}else{
							json.put("status", "2");
							redisObj.setStatus("2");
							redisObj.setReponseTime(DateUtils.xmlDate2Date(header.getTimestamp()));
						}
					}
					json.put("sn", meter.getMRID());
//					json.setAttributes(data);
				}
			}
			PushletData.pushlet("meterConfiguration", json, user.getUserID());
		}
	}catch (Exception e) {
    	e.printStackTrace();
    }
 }

}
