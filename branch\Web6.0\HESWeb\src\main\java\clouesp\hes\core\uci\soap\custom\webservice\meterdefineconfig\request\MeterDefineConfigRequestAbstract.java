package clouesp.hes.core.uci.soap.custom.webservice.meterdefineconfig.request;

import java.io.UnsupportedEncodingException;
import java.util.Date;

import org.apache.commons.lang.StringUtils;

import com.clou.esp.hes.app.web.core.pushlet.PushletData;
import com.clou.esp.hes.app.web.core.util.XMLUtil;
import com.clou.esp.hes.app.web.enums.system.TaskState;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.redis.JedisUtils;

import ch.iec.tc57._2011.meterdefineconfig.MeterDefineConfigPort;
import ch.iec.tc57._2011.meterdefineconfigmessage.MeterDefineConfigPayloadType;
import ch.iec.tc57._2011.meterdefineconfigmessage.MeterDefineConfigRequestMessageType;
import ch.iec.tc57._2011.meterdefineconfigmessage.MeterDefineConfigResponseMessageType;
import clouesp.hes.core.uci.soap.custom.webservice.abstractt.SoapSendAbstract;
import clouesp.hes.core.uci.soap.custom.webservice.common.ReplyError;


/**
 * @ClassName: MeterDefineConfigRequestAbstract
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年7月18日 上午11:54:55
 *
 */
public abstract class MeterDefineConfigRequestAbstract extends SoapSendAbstract {
	protected MeterDefineConfigPort port;

	@Override
	public void send() {
		// TODO Auto-generated method stub
		MeterDefineConfigRequestMessageType requestMessage = new MeterDefineConfigRequestMessageType();
		requestMessage.setHeader(createHeader());
		requestMessage.setPayload((MeterDefineConfigPayloadType) createPayload());
		System.out.println(XMLUtil.convertToXml(requestMessage));
		
		AjaxJson json = new AjaxJson();
		try {
			MeterDefineConfigResponseMessageType responseMessageType = port.meterDefineConfig(requestMessage);
			System.out.println(XMLUtil.convertToXml(responseMessageType));
			if(!ReplyError.ACK.getCode().equals(responseMessageType.getReply().getError().get(0).getCode())){
				json.setMsg(responseMessageType.getReply().getResult());
				json.setSuccess(false);
				json.put("status", TaskState.Failed.getState());
			} else {
				json.setMsg(TaskState.Processing.getState());
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			json.setMsg(ReplyError.INVALID_WEBSERVICE.getText());
			json.setSuccess(false);
			json.put("status", TaskState.Failed.getState());
		}
		
		pushlet(json);
	}
	
	@Override
	public void pushlet(AjaxJson json) {
		// TODO Auto-generated method stub
		if(json.isSuccess() && !"200.200.200.200".equals(dataItemId)) // 正常状况无需推送 pushlet
			return;
		json.put("messageId", messageId);
		json.put("responseTime", DateUtils.formatDate(new Date(), "dd-MM-yyyy HH:mm:ss"));
		json.put("webservTask", JedisUtils.getObject(messageId));
		//todo edison可以分dataItemId 或者 规约号去区分 不同的pushlet
		json.put("dataItemId", dataItemId);
		JedisUtils.delObject(messageId);
		try {
			if(StringUtils.isNotEmpty(dataItemId) && "200.200.200.200".equals(dataItemId)){
				if(json.isSuccess()){
					json.setMsg(TaskState.Success.getState());
					
					json.put("status", TaskState.Success.getState());
				}
				PushletData.pushlet("deviceDebugPushletChannel", json, userId);
			}else if(StringUtils.isNotEmpty(dataItemId) && ("40.0.7.61".equals(dataItemId)
					|| "40.0.7.62".equals(dataItemId)
					|| "40.0.7.63".equals(dataItemId)
					|| "40.0.7.64".equals(dataItemId)
					|| "40.0.7.65".equals(dataItemId)
					|| "40.0.7.66".equals(dataItemId)
					|| "40.0.7.67".equals(dataItemId)
					|| "40.0.7.68".equals(dataItemId)
					|| "40.0.7.69".equals(dataItemId))){
		
				PushletData.pushlet("moduleConfigPushletChannel", json, userId);
			}else{
				PushletData.pushlet("dcuConfigPushletChannel", json, userId);
			}
		} catch (UnsupportedEncodingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
}
