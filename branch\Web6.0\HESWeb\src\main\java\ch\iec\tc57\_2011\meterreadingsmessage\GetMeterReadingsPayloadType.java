
package ch.iec.tc57._2011.meterreadingsmessage;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import ch.iec.tc57._2011.getmeterreadings_.GetMeterReadings;
import ch.iec.tc57._2011.schema.message.OperationSet;


/**
 * <p>GetMeterReadingsPayloadType complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="GetMeterReadingsPayloadType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element ref="{http://iec.ch/TC57/2011/GetMeterReadings#}GetMeterReadings"/&gt;
 *         &lt;element name="OperationSet" type="{http://www.iec.ch/TC57/2011/schema/message}OperationSet" minOccurs="0"/&gt;
 *         &lt;element name="Compressed" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="Format" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "GetMeterReadingsPayloadType", propOrder = {
    "getMeterReadings",
    "operationSet",
    "compressed",
    "format"
})
public class GetMeterReadingsPayloadType {

    @XmlElement(name = "GetMeterReadings", namespace = "http://iec.ch/TC57/2011/GetMeterReadings#", required = true)
    protected GetMeterReadings getMeterReadings;
    @XmlElement(name = "OperationSet")
    protected OperationSet operationSet;
    @XmlElement(name = "Compressed")
    protected String compressed;
    @XmlElement(name = "Format")
    protected String format;

    /**
     * 获取getMeterReadings属性的值。
     * 
     * @return
     *     possible object is
     *     {@link GetMeterReadings }
     *     
     */
    public GetMeterReadings getGetMeterReadings() {
        return getMeterReadings;
    }

    /**
     * 设置getMeterReadings属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link GetMeterReadings }
     *     
     */
    public void setGetMeterReadings(GetMeterReadings value) {
        this.getMeterReadings = value;
    }

    /**
     * 获取operationSet属性的值。
     * 
     * @return
     *     possible object is
     *     {@link OperationSet }
     *     
     */
    public OperationSet getOperationSet() {
        return operationSet;
    }

    /**
     * 设置operationSet属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link OperationSet }
     *     
     */
    public void setOperationSet(OperationSet value) {
        this.operationSet = value;
    }

    /**
     * 获取compressed属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCompressed() {
        return compressed;
    }

    /**
     * 设置compressed属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCompressed(String value) {
        this.compressed = value;
    }

    /**
     * 获取format属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFormat() {
        return format;
    }

    /**
     * 设置format属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFormat(String value) {
        this.format = value;
    }

}
