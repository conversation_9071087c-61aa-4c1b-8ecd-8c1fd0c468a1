/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysUser{ } 
 * 
 * 摘    要： 用户表
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-10-27 08:42:10
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.system;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.json.ValidForm;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.DateTimeFormatterUtil;
import com.clou.esp.hes.app.web.core.util.IpUtil;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.PwdUtil;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.enums.system.EnumDeleteFlag;
import com.clou.esp.hes.app.web.enums.system.EnumUserType;
import com.clou.esp.hes.app.web.model.system.ResetPassword;
import com.clou.esp.hes.app.web.model.system.SysOrg;
import com.clou.esp.hes.app.web.model.system.SysRole;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.model.system.SysUtility;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.system.SysOrgService;
import com.clou.esp.hes.app.web.service.system.SysRoleService;
import com.clou.esp.hes.app.web.service.system.SysUserService;
import com.clou.esp.hes.app.web.service.system.SysUtilityService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.oConvertUtils;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.encrypt.PasswordUtil;
import com.power7000g.core.util.format.FormatUtil;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;

/**
 * <AUTHOR>
 * @时间：2017-10-27 08:42:10
 * @描述：用户表类
 */
@Controller
@RequestMapping("/sysUserController")
public class SysUserController extends BaseController{

	final static String TIME_FLAG =ResourceUtil.getSessionattachmenttitle("local.time.flag");
	
 	@Resource
    private SysUserService sysUserService;
 	@Resource
    private SysRoleService sysRoleService;
 	@Resource
    private SysOrgService sysOrgService;
 	
	@Resource
    private SysUtilityService sysUtilityService;
 	
 	@Resource
    private DataUserLogService dataUserLogService;

	/**
	 * 跳转到用户表列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "sysUserAndRoleList")
    public ModelAndView list(HttpServletRequest request, Model model) {
		//查询角色列表和组织机构列表
		List<SysRole> roleList = sysRoleService.getAllList();
		String roleReplace = RoletoJson.listToReplaceStr(roleList, "id", "name", ",");	//jqgrid自带的参数，分号分隔
		request.setAttribute("roleReplace", roleReplace);
		List<SysOrg> organizationList = sysOrgService.getAllList();
		for (int i = 0; i < organizationList.size(); i++) {
			if(organizationList.get(i).getName().indexOf(";") > 0){
				organizationList.get(i).getName().replace(";", ",");
			}
		}
		String organizationReplace = RoletoJson.listToReplaceStr(organizationList, "id", "name", ",");
		request.setAttribute("organizationReplace", organizationReplace);
        return new ModelAndView("/system/sysUserAndRoleList");
    }

	/**
	 * 跳转到用户表新增界面
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "toAddSysUser")
	public ModelAndView sysUser(SysUser sysUser, HttpServletRequest request, Model model) {
		//查询角色列表和组织机构列表
		List<SysRole> roleList = sysRoleService.getAllList();
		String roleReplace = RoletoJson.listToReplaceStr(roleList, "id", "name");
		request.setAttribute("roleReplace", roleReplace);
		return new ModelAndView("/system/sysUser");
	}
	
	/**
	 * 跳转到主页修改操作员信息界面
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "toEditMyProfile")
	public ModelAndView toEditMyProfile(HttpServletRequest request, Model model) {
		SysUser su = TokenManager.getToken();
		SysUser sysUser = sysUserService.getEntity(su.getId());
		request.setAttribute("sysUser", sysUser);
		return new ModelAndView("/system/editMyProfile");
	}
	

	/**
	 * 跳转到主页修改license
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "toEditMyLicense")
	public ModelAndView toEditMyLicense(HttpServletRequest request, Model model) {
		SysUtility sysUtility = sysUtilityService.getAllList().get(0);
	
		sysUtility.setStartDateStr(DateUtils.date2Str(
				sysUtility.getStartDate(),
				DateTimeFormatterUtil.getSimpleDateFormat_HHmmss(TIME_FLAG)));
		
		sysUtility.setEndDateStr(DateUtils.date2Str(
				sysUtility.getEndDate(),
				DateTimeFormatterUtil.getSimpleDateFormat_HHmmss(TIME_FLAG)));
		
		request.setAttribute("sysUtility", sysUtility);
		return new ModelAndView("/system/editMyLicense");
	}
	
	
	/**
	 * 跳转到主页修改操作员密码界面
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "toEditMyPassword")
	public ModelAndView toEditMyPassword(HttpServletRequest request, Model model) {
		SysUser su = TokenManager.getToken();
		SysUser sysUser = sysUserService.getEntity(su.getId());
		request.setAttribute("sysUser", sysUser);
		return new ModelAndView("/system/editMyPassword");
	}
	
	/**
	 * 跳转到主页修改操作员密码界面Login
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "toEditMyPasswordLogin")
	public ModelAndView toEditMyPasswordLogin(HttpServletRequest request, Model model) {
		String userName = request.getParameter("userName");

		model.addAttribute("userName", userName);
		return new ModelAndView("/system/editMyPasswordLogin");
	}
	
	
	/**
     * 查询角色列表
     * @param id
     * @return
     */
    @RequestMapping(value = "getRoleList")
    @ResponseBody
    public AjaxJson getRoleList(HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        try {
        	List<SysRole> roleList = sysRoleService.getAllList();
        	if(roleList.size() > 0){
        		j.setObj(RoletoJson.listToReplaceStr(roleList, "id", "name", ";"));
        	}
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg("Abnormal operation");
        }
        return j;
    }
    /**
     * 组织机构列表
     * @param id
     * @return
     */
    @RequestMapping(value = "getOrgList")
    @ResponseBody
    public AjaxJson getOrgList(HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        try {
        	List<SysOrg> organizationList = sysOrgService.getAllList();
        	if(organizationList.size() > 0){
        		j.setObj(RoletoJson.listToReplaceStr(organizationList, "id", "name", ";"));
        	}
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg("Abnormal operation");
        }
        return j;
    }
	
	/**
	 * 跳转到用户重置密码界面
	 * <AUTHOR>
	 * @return
	 */
	@RequestMapping(value  = "toResetPassword")
	public ModelAndView toResetPassword(String userId, HttpServletRequest request, Model model) {
		SysUser user = new SysUser();
		if(StringUtil.isNotEmpty(userId)){
			try {
				user = sysUserService.getEntity(userId);
            }
            catch (Exception e) {
                e.printStackTrace();
            }
		}
		model.addAttribute("sysUser", user);
		return new ModelAndView("/system/resetPassword");
	}
	
	
	@RequestMapping(value = "checkOriginPassword")
    @ResponseBody
	 public ValidForm checkOriginPassword(HttpServletRequest request){
	  ValidForm v = new ValidForm();
	  String originPassword = oConvertUtils.getString(request.getParameter("param"));
	  String userId = request.getParameter("userId");
	  String userName = request.getParameter("userName");
	  if (StringUtil.isNotEmpty(userId)) {
		    SysUser su = TokenManager.getToken();
	
			String pswd = su.getPassword();
		
			if (!originPassword.equals(PasswordUtil.decrypt(pswd, su.getUsername(),
					su.getSignature()))) {// 密码错误要做业务处理
				v.setInfo(MutiLangUtil.doMutiLang("sysUserList.originPasswordError"));
				v.setStatus("n");
			}
	  }else    if (StringUtil.isNotEmpty(userName)) {
		  	SysUser user = new SysUser();
			user.setUsername(userName);
			user.setDeleteFlag(Integer.parseInt(EnumDeleteFlag.NOT_DELETE.getIndex()));
			SysUser su = sysUserService.get(user);
		
			if (!originPassword.equals(PasswordUtil.decrypt(su.getPassword(), su.getUsername(),
					su.getSignature()))) {// 密码错误要做业务处理
				v.setInfo(MutiLangUtil.doMutiLang("sysUserList.originPasswordError"));
				v.setStatus("n");
			}
	  }
	  
	  return v;
	 }
	
	

	@RequestMapping(value = "checkAccount")
    @ResponseBody
	 public ValidForm checkAccount(HttpServletRequest request){
	  ValidForm v = new ValidForm();
	  String userName = oConvertUtils.getString(request.getParameter("param"));
	
	  if (StringUtil.isNotEmpty(userName)) {
			SysUser user = new SysUser();
			user.setUsername(userName);
			user.setDeleteFlag(Integer.parseInt(EnumDeleteFlag.NOT_DELETE.getIndex()));
			SysUser su = sysUserService.get(user);
		
			if (su == null) {// 密码错误要做业务处理
				v.setInfo(MutiLangUtil.doMutiLang("sysUserList.accountNotExist"));
				v.setStatus("n");
			}
	  } 
	  
	  return v;
	 }
	
	

	@RequestMapping(value = "checkPassword")
    @ResponseBody
	 public ValidForm checkPassword(HttpServletRequest request){
	  ValidForm v = new ValidForm();
	  String password = oConvertUtils.getString(request.getParameter("param"));
	
	  if (StringUtil.isNotEmpty(password)) {

		//判断密码复杂度
          if(!PwdUtil.checkPassword(password)){

              v.setInfo(MutiLangUtil.doMutiLang("sysUserList.validatePwd"));
			  v.setStatus("n");
          }
		 
	  } 
	  
	  return v;
	 }
	
	/**
     * 重置密码
     * @param id
     * @return
     */
    @RequestMapping(value = "resetPassword")
    @ResponseBody
    public AjaxJson resetPassword(ResetPassword resetPassword, HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        j.setSuccess(false);
        SysUser su = TokenManager.getToken();
        try {
            SysUser sysUser = sysUserService.getEntity(resetPassword.getId());
            //当旧密码不为空时，判断旧密码是不是正确
            if(StringUtil.isNotEmpty(resetPassword.getOriginalPassword())){
            	if(!sysUser.getPassword()
                		.equals(PasswordUtil.encrypt(resetPassword.getOriginalPassword(), sysUser.getUsername(), sysUser.getSignature()))){
                     j.setMsg(MutiLangUtil.doMutiLang("sysUserList.oldPasswordError"));
                     return j;
                }
            }
            
            if(resetPassword.getNewPassword().length() >= 15){
            	j.setMsg(MutiLangUtil.doMutiLang("sysUserList.passwordTooLong"));
                return j;
            }
            
            //判断密码复杂度
            if(!PwdUtil.checkPassword(resetPassword.getNewPassword())){
            	j.setMsg(MutiLangUtil.doMutiLang("sysUserList.validatePwd"));
                return j;
            }
            
            //判断重新输入的两次密码是不是相同
            if(resetPassword.getNewPassword().equals(resetPassword.getOriginalPassword())){
                 j.setMsg(MutiLangUtil.doMutiLang("sysUserList.originConfirmRepeat"));
                 return j;
            }
            
            //判断重新输入的两次密码是不是相同
            if(!resetPassword.getNewPassword().equals(resetPassword.getConfirmPassword())){
                 j.setMsg(MutiLangUtil.doMutiLang("sysUserList.newPasswordError"));
                 return j;
            }
            if(sysUser.getId() != null){
            	//设置新密码
            	sysUser.setPassword(PasswordUtil.encrypt(resetPassword.getNewPassword(), sysUser.getUsername(), sysUser.getSignature()));
//				if(sysUser.getLastLoginTime() == null){
//					Date date = new Date();
//					Timestamp time = new Timestamp(date.getTime());
//					sysUser.setLastLoginTime(time);
//				}
				sysUser.setLastLoginTime(null);
            	sysUserService.update(sysUser);
            	j.setObj(su.getId());
            	j.setSuccess(true);
                j.setMsg(MutiLangUtil.doMutiLang("sysUserList.resetPassSucc"));
                dataUserLogService.insertDataUserLog(su.getId(), "UserResetPassword", "UserResetPassword", "ResetPassword in to HES (IP Address="+ IpUtil.getIpAddr(request)+")");
                
            }else{
                j.setMsg(MutiLangUtil.doMutiLang("sysUserList.oldPasswordError"));
            }
            
           
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
    
    /**
     * 重置密码
     * @param id
     * @return
     */
    @RequestMapping(value = "resetPasswordLogin")
    @ResponseBody
    public AjaxJson resetPasswordLogin(ResetPassword resetPassword, HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        j.setSuccess(false);
        try {
        	SysUser sysUserEntity = new SysUser();
        	sysUserEntity.setUsername(resetPassword.getAccount());
            SysUser sysUser = sysUserService.get(sysUserEntity);
            
            if(sysUser == null){
            	j.setMsg(MutiLangUtil.doMutiLang("sysUserList.accountNotExist"));
                return j;
            }
            
            //当旧密码不为空时，判断旧密码是不是正确
            if(StringUtil.isNotEmpty(resetPassword.getOriginalPassword())){
            	if(!sysUser.getPassword()
                		.equals(PasswordUtil.encrypt(resetPassword.getOriginalPassword(), sysUser.getUsername(), sysUser.getSignature()))){
                     j.setMsg(MutiLangUtil.doMutiLang("sysUserList.oldPasswordError"));
                     return j;
                }
            }
            
            //判断密码复杂度
            if(!PwdUtil.checkPassword(resetPassword.getNewPassword())){
            	j.setMsg(MutiLangUtil.doMutiLang("sysUserList.validatePwd"));
                return j;
            }
            
            
            //判断重新输入的两次密码是不是相同
            if(resetPassword.getNewPassword().equals(resetPassword.getOriginalPassword())){
                 j.setMsg(MutiLangUtil.doMutiLang("sysUserList.originConfirmRepeat"));
                 return j;
            }
            
            //判断重新输入的两次密码是不是相同
            if(!resetPassword.getNewPassword().equals(resetPassword.getConfirmPassword())){
                 j.setMsg(MutiLangUtil.doMutiLang("sysUserList.newPasswordError"));
                 return j;
            }
            if(sysUser.getId() != null){
            	//设置新密码
            	sysUser.setPassword(PasswordUtil.encrypt(resetPassword.getNewPassword(), sysUser.getUsername(), sysUser.getSignature()));
            	sysUser.setLastLoginTime(new Date());
            	sysUserService.update(sysUser);
            	j.setObj(sysUser.getId());
            	j.setSuccess(true);
                j.setMsg(MutiLangUtil.doMutiLang("sysUserList.resetPassSucc"));
                dataUserLogService.insertDataUserLog(sysUser.getId(), "UserResetPassword", "UserResetPassword", "ResetPassword in to HES (IP Address="+ IpUtil.getIpAddr(request)+")");
            }else{
                j.setMsg(MutiLangUtil.doMutiLang("sysUserList.oldPasswordError"));
            }
            

           
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }


	/**
	 * 用户表查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
		
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        
        jqGridSearchTo.getMap().put("roleId", request.getParameter("roleId"));
		jqGridSearchTo.getMap().put("orgId", request.getParameter("orgId"));
		
        JqGridResponseTo j=null;
        SysUser su = TokenManager.getToken();
        if(su.getUserType()!=EnumUserType.SUPER_USER.getIndex()){
			SysOrg sysOrg=new SysOrg();
			sysOrg.setId(su.getOrgId());
			SysOrg org = sysOrgService.get(sysOrg);
			if(org!=null){
				List<SysOrg> orgList = sysOrgService.getListByOrgCode(org);
				List<String> orgIdList=new ArrayList<String>();
				for(SysOrg o:orgList){
					orgIdList.add(o.getId());
				}
				jqGridSearchTo.put("orgIdList", orgIdList);
			}
		}
        try {
        	j=sysUserService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除用户表信息
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(SysUser sysUser,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            SysUser entity = sysUserService.getEntity(sysUser.getId());
            if(sysUserService.deleteById(sysUser.getId())>0){
            	//添加操作日志
	            dataUserLogService.insertDataUserLog(su.getId(), 
	            		"Permission", "Delete User", "Delete User (Account="+ entity.getUsername() +")");
                j.setMsg(MutiLangUtil.doMutiLang("system.delSuccess"));
            }else{
                j.setSuccess(false);
                j.setMsg(MutiLangUtil.doMutiLang("system.delFail"));
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
    
    /**
     * 保存用户表信息
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })SysUser sysUser,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        SysUser t = new  SysUser();
        try {
        	SysUser su = TokenManager.getToken();
	        if(StringUtil.isNotEmpty(sysUser.getId())){
	        	//修改用户信息
	        	if(sysUser.getName() == null || "".equals(sysUser.getName())){
	        		j.setMsg(MutiLangUtil.doMutiLang("login.usernameNull"));
	        		j.setSuccess(false);
	        		return j;
	        	}
	        	t = sysUserService.getEntity(sysUser.getId());
				MyBeanUtils.copyBeanNotNull2Bean(sysUser, t);
				sysUserService.update(t);
				//添加操作日志
	            dataUserLogService.insertDataUserLog(su.getId(), 
	            		"Permission", "Edit User", "Edit User (Account=" + t.getUsername() +")");
				j.setMsg(MutiLangUtil.doMutiLang("system.updateSucc"));
			}else{
				if(!StringUtil.isNotEmpty(sysUser.getOrgId())){
					j.setMsg(MutiLangUtil.doMutiLang("sysOrganizationList.selectOrg"));
	        		j.setSuccess(false);
	        		return j;
				}
				if(sysUser.getName() == null || "".equals(sysUser.getName())){
					j.setMsg(MutiLangUtil.doMutiLang("login.usernameNull"));
					j.setSuccess(false);
					return j;
				}
				if(sysUser.getShowPassword() == null || "".equals(sysUser.getShowPassword())){
					j.setMsg(MutiLangUtil.doMutiLang("login.pswdNull"));
					j.setSuccess(false);
					return j;
				}
				if(sysUser.getShowUsername() == null || "".equals(sysUser.getShowUsername())){
					j.setMsg(MutiLangUtil.doMutiLang("login.AccountNull"));
					j.setSuccess(false);
					return j;
				}
				if(sysUser.getEmail() == null || "".equals(sysUser.getEmail())){
					j.setMsg(MutiLangUtil.doMutiLang("login.MailNull"));
					j.setSuccess(false);
					return j;
				}
				if(sysUser.getMobilePhone() == null || "".equals(sysUser.getMobilePhone())){
					j.setMsg(MutiLangUtil.doMutiLang("login.TelephoneNull"));
					j.setSuccess(false);
					return j;
				}
				if(sysUser.getOrgId() == null || "".equals(sysUser.getOrgId())){
					j.setMsg(MutiLangUtil.doMutiLang("login.OrgIdNull"));
					j.setSuccess(false);
					return j;
				}
				if(sysUser.getRoleId() == null || "".equals(sysUser.getRoleId())){
					j.setMsg(MutiLangUtil.doMutiLang("login.RoleNull"));
					j.setSuccess(false);
					return j;
				}

				//新增用户
				sysUser.setUsername(sysUser.getShowUsername());
				sysUser.setPassword(sysUser.getShowPassword());
				sysUser.setUtilityId("29018328bd4011e79bb968f728c516f9");
				sysUser.setDeleteFlag(0);
				sysUser.setUserState(1);
				sysUser.setUserType(2);		//1=超级用户；2=系统用户；
				sysUser.setSignature(PasswordUtil.getSalt());
				//加密密码字符串
				sysUser.setPassword(PasswordUtil.encrypt(sysUser.getPassword(), sysUser.getUsername(), sysUser.getSignature()));
	            sysUserService.save(sysUser);
	            //添加操作日志
	            dataUserLogService.insertDataUserLog(su.getId(), 
	            		"Permission", "Add User", "Add User (Account="+ sysUser.getUsername() +")");
	            j.setMsg(MutiLangUtil.doMutiLang("system.addSucc"));
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
    /**
     * 校验user account唯一性
     * @param id5
     * @return
     */
    @RequestMapping(value = "checkUserAccount")
    @ResponseBody
    public ValidForm checkUserAccount(HttpServletRequest request) {
    	ValidForm v = new ValidForm();
		String userAccount = oConvertUtils.getString(request.getParameter("param"));
		List<SysUser> list = sysUserService.vaildAccount(userAccount);
		if (list.size() > 0) {
			v.setInfo("The account already exists!");
			v.setStatus("n");
		}
		return v;
    }
	
    /**
	 * 用户状态修改 Disable / Enable
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "userDisableOrEnable")
    @ResponseBody
    public AjaxJson userDisableOrEnable(SysUser sysUser, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		SysUser t = new  SysUser();
        try {
        	if(sysUser.getId() != null && sysUser.getUserState() != null){
        		t = sysUserService.getEntity(sysUser.getId());
        		MyBeanUtils.copyBeanNotNull2Bean(sysUser, t);
        		sysUserService.update(t);
        		j.setObj(t);
        		j.setMsg(MutiLangUtil.doMutiLang("system.updateSucc"));
        	}else{
        		j.setSuccess(false);
                j.setMsg(MutiLangUtil.doMutiLang("system.pseleData"));
        	}
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
}