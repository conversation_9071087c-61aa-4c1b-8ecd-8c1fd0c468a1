/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataFwuJob{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-12-29 08:37:48
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.data;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.data.DataFwuJobDao;
import com.clou.esp.hes.app.web.model.data.DataFwuJob;
import com.clou.esp.hes.app.web.service.data.DataFwuJobService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@Component
@Service("dataFwuJobService")
public class DataFwuJobServiceImpl extends CommonServiceImpl<DataFwuJob>
		implements DataFwuJobService {

	@Resource
	private DataFwuJobDao dataFwuJobDao;

	@Autowired
	public void setCommonService() {
		super.setCommonService(dataFwuJobDao);
	}

	public DataFwuJobServiceImpl() {
	}

	@Override
	public List<DataFwuJob> getListGroupByField(DataFwuJob dfj) {
		return dataFwuJobDao.getListGroupByField(dfj);
	}

	@Override
	public JqGridResponseTo getForJqGrids(JqGridSearchTo jqGridSearchTo) {
		PageHelper
				.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		List<DataFwuJob> list = dataFwuJobDao.getForJqGrids(jqGridSearchTo);
		PageInfo<DataFwuJob> pageInfo = new PageInfo<DataFwuJob>(list);
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}

	@Override
	public List<DataFwuJob> getListNoPage(JqGridSearchTo j) {
		return dataFwuJobDao.getForJqGrid(j);
	}

	@Override
	public void cancelByPlanId(String planId) {
		this.dataFwuJobDao.cancelByPlanId(planId);
		
	}

	@Override
	public List<DataFwuJob> getListByJq(JqGridSearchTo jqGridSearchTo) {
		List<DataFwuJob> list = dataFwuJobDao.getListByJq(jqGridSearchTo);
		return list;
	}
}