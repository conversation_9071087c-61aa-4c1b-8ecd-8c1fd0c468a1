drop table if exists ppm_sys_data_log;
CREATE TABLE ppm_sys_data_log (
  SYS_DATA_LOG_ID varchar(36) NOT NULL,
  SYS_ORG_ID varchar(50) DEFAULT '' COMMENT '所属机构',
  TABLE_NAME varchar(32) DEFAULT '' COMMENT '表名',
  DATA_ID varchar(32) DEFAULT NULL COMMENT '数据ID',
  DATA_CONTENT text COMMENT '数据内容',
  VERSION_NUMBER int(11) DEFAULT NULL COMMENT '版本号',
  OP_LOG_ID varchar(32) DEFAULT '操作日志ID',
  OP_TIME datetime NOT NULL COMMENT '操作时间',
  OP_ID varchar(32) DEFAULT NULL COMMENT '操作员ID',
  OP_NAME varchar(100) DEFAULT NULL COMMENT '操作员名称',
  REMARK text COMMENT '备注',
  PRIMARY KEY (SYS_DATA_LOG_ID)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据日志';

-- 中间部分的CMS接口脚本已挪到/zhangqian/mysql目录下，因为属于特定项目的脚本，注意升级时的文件MD5码报错

alter table asset_meter add   METER_STATUS smallint(6) DEFAULT '3' COMMENT '电表运行状态(字典表 5:  1=待安装    2=待投运   3=运行   4 =停运  5=故障  6=新装未核对  7=变更未核对 8=换表 9=拆除)';

INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README) VALUES ('200', '0', 'ManufacturerId', '101');
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README) VALUES ('200', '1', 'ComTypeId', '100');
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README) VALUES ('200', '2', 'MeasureId', '2');
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README) VALUES ('200', '3', 'ChannelId', '20010001');
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README) VALUES ('200', '4', 'ScheduleId', '20010002');
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README) VALUES ('201', '0', 'ML074', '101001');
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README) VALUES ('201', '1', 'ML075', '101001');
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README) VALUES ('201', '2', 'ML078', '101001');
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README) VALUES ('201', '3', 'ML079', '101001');
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README) VALUES ('202', '1', 'orgId', '89344c8b02004e5692baa58a7bfabab7');
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README) VALUES ('203', '1', 'cmsResidentialFeeId', '7402e69f6462461c819552a4b3ea4805');
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README) VALUES ('203', '2', 'cmsNonResidentialFeeId', '6e7eb70595a14d11b33145ebb5241e67');
