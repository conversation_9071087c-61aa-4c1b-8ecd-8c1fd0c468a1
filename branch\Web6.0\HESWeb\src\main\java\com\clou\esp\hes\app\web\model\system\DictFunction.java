/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictFunction{ } 
 * 
 * 摘    要： 功能菜单字典
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-01 09:42:07
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.system;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DictFunction  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DictFunction() {
	}

	/**菜单名称*/
	private java.lang.String functionname;
	/**菜单地址*/
	private java.lang.String functionurl;
	/**菜单简介*/
	private java.lang.String functionIntro;

	/**
	 * 菜单名称
	 * @return the value of DICT_FUNCTION.FUNCTIONNAME
	 * @mbggenerated 2017-11-01 09:42:07
	 */
	public java.lang.String getFunctionname() {
		return functionname;
	}

	/**
	 * 菜单名称
	 * @param functionname the value for DICT_FUNCTION.FUNCTIONNAME
	 * @mbggenerated 2017-11-01 09:42:07
	 */
    	public void setFunctionname(java.lang.String functionname) {
		this.functionname = functionname;
	}
	/**
	 * 菜单地址
	 * @return the value of DICT_FUNCTION.FUNCTIONURL
	 * @mbggenerated 2017-11-01 09:42:07
	 */
	public java.lang.String getFunctionurl() {
		return functionurl;
	}

	/**
	 * 菜单地址
	 * @param functionurl the value for DICT_FUNCTION.FUNCTIONURL
	 * @mbggenerated 2017-11-01 09:42:07
	 */
    	public void setFunctionurl(java.lang.String functionurl) {
		this.functionurl = functionurl;
	}
	/**
	 * 菜单简介
	 * @return the value of DICT_FUNCTION.FUNCTION_INTRO
	 * @mbggenerated 2017-11-01 09:42:07
	 */
	public java.lang.String getFunctionIntro() {
		return functionIntro;
	}

	/**
	 * 菜单简介
	 * @param functionIntro the value for DICT_FUNCTION.FUNCTION_INTRO
	 * @mbggenerated 2017-11-01 09:42:07
	 */
    	public void setFunctionIntro(java.lang.String functionIntro) {
		this.functionIntro = functionIntro;
	}

	public DictFunction(java.lang.String functionname 
	,java.lang.String functionurl 
	,java.lang.String functionIntro ) {
		super();
		this.functionname = functionname;
		this.functionurl = functionurl;
		this.functionIntro = functionIntro;
	}

}