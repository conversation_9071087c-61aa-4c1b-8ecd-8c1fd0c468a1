/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictCommunicationType{ } 
 * 
 * 摘    要： 通讯方式
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-18 06:06:26
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.dict;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.model.dict.DictCommunicationType;
import com.clou.esp.hes.app.web.service.dict.DictCommunicationTypeService;

/**
 * <AUTHOR>
 * @时间：2017-11-18 06:06:26
 * @描述：通讯方式类
 */
@Controller
@RequestMapping("/dictCommunicationTypeController")
public class DictCommunicationTypeController extends BaseController{

 	@Resource
    private DictCommunicationTypeService dictCommunicationTypeService;

	/**
	 * 跳转到通讯方式列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/dict/dictCommunicationTypeList");
    }

	/**
	 * 跳转到通讯方式新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dictCommunicationType")
	public ModelAndView dictCommunicationType(DictCommunicationType dictCommunicationType,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dictCommunicationType.getId())){
			try {
                dictCommunicationType=dictCommunicationTypeService.getEntity(dictCommunicationType.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dictCommunicationType", dictCommunicationType);
		}
		return new ModelAndView("/dict/dictCommunicationType");
	}


	/**
	 * 通讯方式查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=dictCommunicationTypeService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除通讯方式信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DictCommunicationType dictCommunicationType,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dictCommunicationTypeService.deleteById(dictCommunicationType.getId())>0){
                j.setMsg("删除成功");
            }else{
                j.setSuccess(false);
                j.setMsg("删除失败");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
    
    /**
     * 返回全部通信方式
     * @param dictCommunicationType
     * @param request
     * @return
     */
    @RequestMapping(value = "getComTypeList")
    @ResponseBody
    public AjaxJson getComTypeList(DictCommunicationType dictCommunicationType,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
        	List<DictCommunicationType> allList = dictCommunicationTypeService.getAllList();
        	j.setObj(allList);
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
    
    /**
     * 保存通讯方式信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DictCommunicationType dictCommunicationType,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DictCommunicationType t=new  DictCommunicationType();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dictCommunicationType.getId())){
        	t=dictCommunicationTypeService.getEntity(dictCommunicationType.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dictCommunicationType, t);
				dictCommunicationTypeService.update(t);
				j.setMsg("修改成功");
				
			}else{
	            dictCommunicationTypeService.save(dictCommunicationType);
	            j.setMsg("创建成功");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
	
	
	
}