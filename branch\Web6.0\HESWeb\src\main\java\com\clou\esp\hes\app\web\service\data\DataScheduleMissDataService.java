/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataScheduleMissData{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-12-01 06:03:45
 * 最后修改时间：
 * 
*********************************************************************************************************/

package com.clou.esp.hes.app.web.service.data;

import java.util.List;

import com.clou.esp.hes.app.web.service.common.CommonService;
import com.clou.esp.hes.app.web.service.system.SysServiceAttributeService;
import com.clou.esp.hes.app.web.model.data.DataParameterPlan;
import com.clou.esp.hes.app.web.model.data.DataScheduleMissData;
import com.clou.esp.hes.app.web.model.report.MissDataReport;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;

public interface DataScheduleMissDataService extends CommonService<DataScheduleMissData>{
	public List<DataScheduleMissData> getListMissDataTv(DataScheduleMissData dataScheduleMissData);
	JqGridResponseTo getForJqGrid1(JqGridSearchTo jqGridSearchTo);
	public List<DataScheduleMissData> getList1(DataScheduleMissData dataScheduleMissData);
	
	public List<DataScheduleMissData> getMissDataList(DataScheduleMissData dataScheduleMissData);
	
}