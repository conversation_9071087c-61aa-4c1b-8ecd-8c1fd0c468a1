/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysService{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-01-29 08:53:20
 * 最后修改时间：
 * 
*********************************************************************************************************/

package com.clou.esp.hes.app.web.service.system;

import java.util.List;

import com.clou.esp.hes.app.web.service.common.CommonService;
import com.clou.esp.hes.app.web.model.system.SysService;

public interface SysServiceService extends CommonService<SysService>{
	
	public List<SysService> getListByEntity(SysService service);
	/**
	 * 校验service名字
	 * @Description 
	 * @param name
	 * @return List<SysService>
	 * <AUTHOR> 
	 * @Time 2018年4月4日 上午9:02:06
	 */
	public List<SysService> vaildServiceName(String name);
}