package com.clou.esp.hes.app.web.model.demo.req;

import java.io.Serializable;

import javax.jws.soap.SOAPBinding;
import javax.jws.soap.SOAPBinding.Style;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * 孟加拉接口项目请求数据头数据对象
 * 
 * <AUTHOR>
 * 
 */
@SOAPBinding(style = Style.RPC)
@XmlRootElement(name = "Names")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = { "name" })
public class NameType implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public NameType(String name) {
		this.name = name;
	}

	public NameType() {
		super();
	}

	public String name;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Override
	public String toString() {
		return "NameType [name=" + name + "]";
	}

}
