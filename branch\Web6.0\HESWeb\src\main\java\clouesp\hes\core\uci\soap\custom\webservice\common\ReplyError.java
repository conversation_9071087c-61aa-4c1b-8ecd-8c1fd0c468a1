package clouesp.hes.core.uci.soap.custom.webservice.common;

public enum ReplyError {
	SUCCESS("OK", "0.0"),
	ACK("OK", "0.3"),
	PARTIAL_SUCCESS_MORE("Partial result", "0.1"),
	PARTIAL_SUCCESS_LESS("Partial result", "0.2"),
	INVALID_METER("Invalid meter", "2.4"),
	INVALID_READINGTYPE("Invalid reading type", "2.6"),
	INVALID_NOUN("Invalid noun", "2.5"),
	INVALID_VERB("Invalid verb", "2.9"),
	INVALID_SOURCE("Invalid source", "2.14"),
	INVALID_WEBSERVICE("Communication Abnormal","2.2"),
	TOO_MANY_PENDING_REQUEST("Too many pending request", "3.2"),
	REQUEST_TIMEOUT("Request time out", "4.1"),
	SERVICE_NOT_AVAILABLE("Request time out", "4.2"),
	LOCAL_ERROR("Local error in processing", "4.3"),
	PACKET_RESPONSE_ERROR("Meter unidentified responds", "4.4"),
	CHANNEL_BUSY("Channel is busy", "4.5"),
	DEVICE_OFFLINE("Device offline", "4.6"),
	COSEM_ASSOCIATE_FAILED("Cosem associate failed", "4.7"),
	PACKET_DECRYPT_FAILED("Packet decrypt failed", "4.8"),
	RESPONSE_INVALID_DATA("Response invalid data","4.9"),
	FIELD_MISSING("Mandatory filed(s) missing", "5.8"),
	REQUEST_CANCEL("Request canceled by user", "6.5");
	
	private String text;
	private String code;
	private ReplyError(String text, String code) {
		this.text = text;
		this.code = code;
	}
	
	public String getText() {
		return text;
	}
	public void setText(String text) {
		this.text = text;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
}
