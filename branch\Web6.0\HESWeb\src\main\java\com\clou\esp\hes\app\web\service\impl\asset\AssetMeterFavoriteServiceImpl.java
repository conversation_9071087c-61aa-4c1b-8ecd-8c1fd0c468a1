/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeterFavorite{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-03-09 07:39:35
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.asset;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.asset.AssetMeterFavoriteDao;
import com.clou.esp.hes.app.web.model.asset.AssetMeterFavorite;
import com.clou.esp.hes.app.web.service.asset.AssetMeterFavoriteService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("assetMeterFavoriteService")
public class AssetMeterFavoriteServiceImpl  extends CommonServiceImpl<AssetMeterFavorite>  implements AssetMeterFavoriteService {

	@Resource
	private AssetMeterFavoriteDao assetMeterFavoriteDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(assetMeterFavoriteDao);
    }
	@SuppressWarnings("rawtypes")
	public AssetMeterFavoriteServiceImpl() {}
	
	
}