/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class onDemandRead{ } 
 * 
 * 摘    要： onDemandRead
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-1-2 09:41:41
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.asset;

import java.util.Date;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.excel.Excel;

public class OnDemandReadSchedulerJob extends  BaseEntity implements Comparable<OnDemandReadSchedulerJob> {

	private static final long serialVersionUID = 1L;
	
	/** sn */
	@Excel(name = "Serial Number", width = 30, groups = ValidGroup1.class)
	private java.lang.String sn;
	/** model */
	@Excel(name = "Model", width = 30, groups = ValidGroup1.class)
	private java.lang.String model;
	/** communicator */
	@Excel(name = "Communicator SN", width = 30, groups = ValidGroup1.class)
	private java.lang.String communicator;
	/** communicator */
	@Excel(name = "Communication", width = 30, groups = ValidGroup1.class)
	private java.lang.String communication;
	/** profile */
	@Excel(name = "Profile", width = 30, groups = ValidGroup1.class)
	private java.lang.String profile;
	/** profile */
	@Excel(name = "ProfileName", width = 30, groups = ValidGroup1.class)
	private java.lang.String profileName;
	/** requestTime */
	@Excel(name = "Start Time", width = 30, groups = ValidGroup1.class)
	private String startTime;
	/** reponseTime */
	@Excel(name = "End Time", width = 30, groups = ValidGroup1.class)
	private String endTime;
	/** dataChannel */
	@Excel(name = "Data Channel", width = 30, groups = ValidGroup1.class)
	private java.lang.String dataChannel;
	/** value */
	@Excel(name = "Value", width = 30, groups = ValidGroup1.class)
	private String value;
	/** status */
	@Excel(name = "Status", width = 30, groups = ValidGroup1.class)
	private java.lang.String status;
	@Excel(name = "Reason", width = 30, groups = ValidGroup1.class)
	private java.lang.String reason;
	/** requestTime */
	@Excel(name = "Request Time", width = 30, groups = ValidGroup1.class)
	private java.util.Date requestTime;
	/** reponseTime */
	@Excel(name = "Reponse Time", width = 30, groups = ValidGroup1.class)
	private java.util.Date reponseTime;
	
	
	public OnDemandReadSchedulerJob(String sn, String communicator, String communication, String model,
			String profile, String startTime, String endTime) {
		super();
		this.sn = sn;
		this.communicator = communicator;
		this.communication = communication;
		this.model = model;
		this.profile = profile;
		this.startTime = startTime;
		this.endTime = endTime;
	}
	
	public OnDemandReadSchedulerJob() {
		
	}

	public java.lang.String getSn() {
		return sn;
	}


	public void setSn(java.lang.String sn) {
		this.sn = sn;
	}


	public java.lang.String getCommunicator() {
		return communicator;
	}


	public void setCommunicator(java.lang.String communicator) {
		this.communicator = communicator;
	}


	public String getStartTime() {
		return startTime;
	}


	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}


	public String getEndTime() {
		return endTime;
	}


	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}


	public java.lang.String getProfile() {
		return profile;
	}


	public void setProfile(java.lang.String profile) {
		this.profile = profile;
	}

	public java.lang.String getModel() {
		return model;
	}


	public void setModel(java.lang.String model) {
		this.model = model;
	}

	public java.lang.String getCommunication() {
		return communication;
	}


	public void setCommunication(java.lang.String communication) {
		this.communication = communication;
	}
	
	public java.lang.String getProfileName() {
		return profileName;
	}

	public void setProfileName(java.lang.String profileName) {
		this.profileName = profileName;
	}

	public java.util.Date getRequestTime() {
		return requestTime;
	}


	public void setRequestTime(java.util.Date requestTime) {
		this.requestTime = requestTime;
	}


	public java.lang.String getStatus() {
		return status;
	}


	public void setStatus(java.lang.String status) {
		this.status = status;
	}



	public java.util.Date getReponseTime() {
		return reponseTime;
	}


	public void setReponseTime(java.util.Date reponseTime) {
		this.reponseTime = reponseTime;
	}


	public java.lang.String getDataChannel() {
		return dataChannel;
	}


	public void setDataChannel(java.lang.String dataChannel) {
		this.dataChannel = dataChannel;
	}


	public String getValue() {
		return value;
	}


	public void setValue(String value) {
		this.value = value;
	}
	
	
	
	public java.lang.String getReason() {
		return reason;
	}

	public void setReason(java.lang.String reason) {
		this.reason = reason;
	}

	@Override
	public int compareTo(OnDemandReadSchedulerJob o) {
		// TODO Auto-generated method stub
		return this.getSn().compareTo(o.getSn());
	}


	
}
