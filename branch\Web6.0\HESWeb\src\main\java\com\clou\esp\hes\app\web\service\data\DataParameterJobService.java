/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataParameterJob{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-04-27 09:13:12
 * 最后修改时间：
 * 
*********************************************************************************************************/

package com.clou.esp.hes.app.web.service.data;

import java.util.List;

import com.clou.esp.hes.app.web.service.common.CommonService;
import com.clou.esp.hes.app.web.model.data.DataParameterJob;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

public interface DataParameterJobService extends CommonService<DataParameterJob>{
	
	/**
	 * 分页,(meter group upgrade 模块中tab3分页)
	 * @param JqGridSearchTo
	 * @return String
	 */
	JqGridResponseTo getForJqGrid_ForJobList(JqGridSearchTo jqGridSearchTo);
	
	/**
	 * 导出meter group upgrade 的tab2的job list数据，筛选条件是 planId
	 * @Description 
	 * @param job
	 * @return List<DataParameterJob>
	 * <AUTHOR> 
	 * @Time 2018年5月9日 下午1:46:15
	 */
	List<DataParameterJob> exportOrPrintPlanReportJobList(DataParameterJob job);
	/**
	 * 导出meter group upgrade 的tab3的job list数据，筛选条件是 界面查询条件
	 * @Description 
	 * @param job
	 * @return List<DataParameterJob>
	 * <AUTHOR> 
	 * @Time 2018年5月9日 下午1:46:15
	 */
	List<DataParameterJob> exportOrPrintJobReportJobList(DataParameterJob job);
	
	public void cancelByPlanId(String planId);
}