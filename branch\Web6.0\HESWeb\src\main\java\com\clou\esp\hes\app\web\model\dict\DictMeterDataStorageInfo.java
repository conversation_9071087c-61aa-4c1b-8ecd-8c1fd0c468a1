/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictMeterDataStorageInfo{ } 
 * 
 * 摘    要： dictMeterDataStorageInfo
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.dict;

import java.math.BigDecimal;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DictMeterDataStorageInfo  extends BaseEntity {

	private static final long serialVersionUID = -3063043211979293531L;

	public DictMeterDataStorageInfo() {	}

	private String 		tableId;
	private Integer 	fieldIndex;
	
	//item表的属性开始
	private String 		name;
	private String 		protocolId;
	private String 		opType;
	private String 		unit;
	private String 		protocolCode;
	private String 		dataitemType;
	//item表的属性结束
	
	private BigDecimal 	showUnit;

	
	public String getTableId() {
		return tableId;
	}

	public void setTableId(String tableId) {
		this.tableId = tableId;
	}

	public Integer getFieldIndex() {
		return fieldIndex;
	}

	public void setFieldIndex(Integer fieldIndex) {
		this.fieldIndex = fieldIndex;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getProtocolId() {
		return protocolId;
	}

	public void setProtocolId(String protocolId) {
		this.protocolId = protocolId;
	}

	public String getOpType() {
		return opType;
	}

	public void setOpType(String opType) {
		this.opType = opType;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public String getProtocolCode() {
		return protocolCode;
	}

	public void setProtocolCode(String protocolCode) {
		this.protocolCode = protocolCode;
	}

	public String getDataitemType() {
		return dataitemType;
	}

	public void setDataitemType(String dataitemType) {
		this.dataitemType = dataitemType;
	}

	public BigDecimal getShowUnit() {
		return showUnit;
	}

	public void setShowUnit(BigDecimal showUnit) {
		this.showUnit = showUnit;
	}

}