/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictProfile{ } 
 * 
 * 摘    要： dictProfile
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-02-26 09:36:52
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.dict;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.dict.DictProfile;
import com.clou.esp.hes.app.web.model.dict.DictProtocol;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.dict.DictProfileService;
import com.clou.esp.hes.app.web.service.dict.DictProtocolService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;

/**
 * <AUTHOR>
 * @时间：2018-02-26 09:36:52
 * @描述：dictProfile类
 */
@Controller
@RequestMapping("/dictProfileController")
public class DictProfileController extends BaseController{

 	@Resource
    private DictProfileService dictProfileService;
 	@Resource
 	private DictProtocolService dictProtocolService;
 	
	/**
	 * 跳转到dictProfile列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/dict/dictProfileList");
    }

	/**
	 * 跳转到dictProfile新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dictProfile")
	public ModelAndView dictProfile(DictProfile dictProfile,HttpServletRequest request, Model model) {
		 if(StringUtil.isNotEmpty(dictProfile.getId())&&StringUtil.isNotEmpty(dictProfile.getProtocolId())){
            dictProfile=dictProfileService.get(dictProfile);
            model.addAttribute("dictProfile", dictProfile);
		}
		
		List<DictProtocol> procols=dictProtocolService.getAllList();
		String proReplace=RoletoJson.listToReplaceStr(procols, "id", "name", ",");
		model.addAttribute("protocolReplace",proReplace);
		return new ModelAndView("/dict/dictProfile");
	}


	/**
	 * dictProfile查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request
    		,String id,String protocolId,String name,String profileType,Boolean select) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        if(select==null||!select) {
        	return j;
        }
        try {
        	jqGridSearchTo.put("id", id);
        	jqGridSearchTo.put("name", name);
    		jqGridSearchTo.put("profileType", profileType);
    		jqGridSearchTo.put("protocolId", protocolId);
            j=dictProfileService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除dictProfile信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DictProfile dictProfile,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dictProfileService.deleteById(dictProfile.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存dictProfile信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DictProfile dictProfile,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DictProfile t=new  DictProfile();
        try {
        	if(StringUtil.isNotEmpty(dictProfile.getOldId())){
        		t.setId(dictProfile.getOldId());
	        	t=dictProfileService.get(t);
				MyBeanUtils.copyBeanNotNull2Bean(dictProfile, t);
				dictProfileService.update(t);
				j.setMsg(MutiLangUtil.doMutiLang("system.updateSucc"));
			}else{
	            dictProfileService.save(dictProfile);
	            j.setMsg(MutiLangUtil.doMutiLang("system.addSucc"));
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
	
    /**
     * 查询所有数据
     * @param id
     * @return
     */
    @RequestMapping(value = "getList")
    @ResponseBody
    public AjaxJson getList(HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        try {
//            SysUser su = TokenManager.getToken();
            List<DictProfile> list = dictProfileService.getAllList();
            if(list.size() > 0){
            	j.setObj(list);
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg("Abnormal operation");
        }
        return j;
    }
	
    /**
  	 * id
  	 */
  	@RequestMapping(value = "isExist")
  	@ResponseBody
  	public AjaxJson isExist(DictProfile item,HttpServletRequest request) {
  		StringBuffer sb = new StringBuffer();
  		AjaxJson j = new AjaxJson();
  		String oldId = item.getOldId();
  		if(!item.getId().equals(item.getOldId())) {
  			DictProfile tmp = this.dictProfileService.get(item);
  			if(tmp!=null) {
  				sb.append(MutiLangUtil.doMutiLang("dictProfile.idExist")+"<br/>");
  			}
  		}
  		//判断name
  		DictProfile entity = new DictProfile();
  		entity.setName(item.getName());
  		List<DictProfile> list = this.dictProfileService.getList(entity);
  		if(list!=null&&list.size()>0) {
  			if(StringUtils.isNotEmpty(oldId)) {//编辑的时候存在,看是不是同一条
	  			entity = list.get(0);
	  			if(list.size()>1||!oldId.equals(entity.getId())) {
	  				sb.append(MutiLangUtil.doMutiLang("dictProfile.nameExist"));
	  			}
  			}else {
  				sb.append(MutiLangUtil.doMutiLang("dictProfile.nameExist"));
  			}
  		}
  		if(sb.length()>0) {  
  			j.setSuccess(false);
  			j.setMsg(sb.toString());
  		}
  		return j;
  	}
}