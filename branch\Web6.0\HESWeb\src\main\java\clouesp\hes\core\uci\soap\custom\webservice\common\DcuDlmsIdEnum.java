package clouesp.hes.core.uci.soap.custom.webservice.common;

import clouesp.hes.core.uci.soap.custom.webservice.common.DcuDlmsIdEnum;


/**
 * @ClassName: DcuDlmsIdEnum
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年7月23日 下午2:01:19
 *
 */
public enum DcuDlmsIdEnum {
	
	
	AddNewMeterList("**********","New Add Meter list W"),
	Add<PERSON><PERSON><PERSON><PERSON>("********","Add Meter list W"),
	Del<PERSON><PERSON><PERSON><PERSON>("********","Delete Meter list W"),
	ReadMeterList("********","Read Meter List R"),
	KeyMeterParam("********","Key Meter parameter RW"),
	KeyMeterParamDelete("********","Key meter parameter delete A"),
	KeyMeterParamDeleteAll("********","Key meter parameter delete all A"),
	HesIpAddreAndPort("********","HES ip address and port RW"),
	GprsNetworkParam("********","GRPS network parameter RW"),
	Clock("********","Clock RW"),
	ConcentratorVersionInfo("********0","Concentrator version information R"),
	HardwareReset("********1","Hardware reset W"),
	DataBaseInit("********2","Data base initialization W"),
	ParamResetExceptComm("********3","Parameter reset (except communication) W"),
	ParamResetAll("********4","Parameter reset (all) W"),
	EncryptionMode("********5","Encryption mode RW"),
	BroadcastKey("********6","Broadcast key RW"),
	HeartbeatCycle("********7","Heartbeat Cycle RW"),
	EnergyProfileReadingCycle("********8","Energy Profile Reading Cycle RW"),
	PLCChannelSendTimes("********9","PLC Channel Send Times RW"),
	PLCChannelTimeout("********0","PLC Channel Timeout RW"),
	ConcentratorAddress("********1","Concentrator Address R"),
	ManagementLogicalDeviceName("********2","Management LogicalDevice Name R"),
	DormancyTime("********3","Dormancy Time RW"),
	ConcentratorIPAddress("********4","Concentrator IP Address RW"),
	EventConfiguration("********5","Event Configuration RW"),
	BroadcastTimeOrderEnable("********6","Broadcast Time Order Enable RW"),
	BroadcastTimeParameter("********7","Broadcast Time Parameter RW"),
	BroadcastTimeParameter2("********8","Broadcast Time Parameter RW2"),
	GPRSSignalStrength("********8","GPRS Signal Strength R"),
	GPRSIMEISerialNumber("********9","GPRS IMEI Serial Number R"),
	GPRSNetworkStandard("********0","GPRS Network Standard R"),
	WANDHCPEnable("********1","WAN DHCP Enable W"),
	CSMode("********3","CS Mode RW"),
	ServerPort("********4","Server Port RW"),
	GPRSModemVersion("********5","GPRS Modem Version R"),
	PLCModemVersion("********6","PLC Modem Version R"),
	RFModemVersion("********7","RF Modem Version R"),
	VPNDial("********0","VPN Dail RW"),
	VPNNet("********1","VPN Net R"),
	TopoInfo("********2","Topo R"),
	SearchMeterSwitch("********3","Search Meter Switch RW"),
	BlacklistOfMeter("********8","Blacklist of meter RW"),
	PacketDebug("***************","Packet Debug"),
	
	apnModule("********1","apn"),
	userAndPasswordModule("********2","user and password"),
	pinCodeModule("********3","PIN Code"),
	hesIpAndPortModule("********4","HES IP and Port"),
	ftpIpAndPortModule("********5","FTP IP and Port"),
	autoConnectModeModule("********6","auto connect mode"),
	autoAnswerModule("********7","auto answer mode"),
	inactivityTimeoutModule("********8","inactivity Timeout"),
	noNetworkCommunicationTimeoutModule("********9","no network communication timeout"),
	
	dayCurves("********2","Number Of Reading Meter Day Curves RW"),
	monthCurves("********3","Number Of Reading Meter Month Curves RW"),
	loadCurves("********4","Number Of Reading Meter Load Curves RW"),
	eventLogCurves("********1","Event Log RW"),
	timeZone("*********","Time Zone");
	private String dataItemId;
	private String name;
	
	private DcuDlmsIdEnum(String dataItemId,String name) {
		this.dataItemId = dataItemId;
		this.name = name;
	}
	
	public static DcuDlmsIdEnum parse(String dataItemId) {
		for (DcuDlmsIdEnum resetType : values()) {
			if(dataItemId.equals(resetType.getDataItemId())) {
				return resetType;
			}
		}
		return null;
	}

	
	
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDataItemId() {
		return dataItemId;
	}

	public void setDataItemId(String dataItemId) {
		this.dataItemId = dataItemId;
	}
}
