function taskStatusCompleted () {
	var completed = parseInt($("#completed_span_id").html());
	$("#completed_span_id").html(completed + 1);
	var processing = parseInt($("#processing_span_id").html());
	$("#processing_span_id").html(processing - 1);	
}

function taskStatusSuccess () {
	var success = Number($("#success_span_id").text());
	$("#success_span_id").html(success+1);	
}

function taskStatusFailed () {
	var failed = Number($("#failed_span_id").text());
	$("#failed_span_id").html(failed+1);	
}

function getHesIpAndPort(values) {
		// ip 

	
		var ip = values[0].value;
		$("#txtIpAddress").val(ip);
	
		// port
		var port = values[1].value;
		$("#txtPort").val(port);	
		
		if(values.length == 4){
		  
			// backup ip
			var bakIp = values[2].value;
			$("#txtIpAddressBackup").val(bakIp);
			
			// backup port
			var bakPort = values[3].value;
			$("#txtPortBackup").val(bakPort);
		}


}


function getFtpIpAndPort(values) {
	// ip 

	var ip = values[0].value;
	$("#txtIpAddress2").val(ip);
	
	// port
	var port = values[1].value;
	$("#txtPort2").val(port);
	
	
	if(values.length == 4){
	
	 // backup ip
		var bakIp = values[2].value;
		$("#txtUserName1").val(bakIp);
	  
		// backup port
		var bakPort = values[3].value;
		$("#txtPassword1").val(bakPort);
	}
	

}


function getUserAndPassword(values) {
	// user 

	var user = values[0].value;
	$("#txtUserName").val(user);
	
	// password
	var password = values[1].value;
	$("#txtPassword").val(password);

}



function getAutoConnectMode(values) {
	var selConnectMode = values[0].value;
	$("#selConnectMode").val(selConnectMode);
}

function getAutoAnswerMode(values) {
	var selAnswerMode = values[0].value;
	$("#selAnswerMode").val(selAnswerMode);
}

function getApn(values) {
	var txtApn = values[0].value;
	$("#txtApn").val(txtApn);
}


function getPinCode(values) {
	var txtPinCode = values[0].value;
	$("#txtPinCode").val(txtPinCode);
}

function getInactivityTimeout(values) {
	var txtInactivityTimeout = values[0].value;
	$("#txtInactivityTimeout").val(txtInactivityTimeout);
}


function getNoNetworkCommunicationTimeoutResetTime(values) {
	var txtNoNetworkCommunicationTimeoutReset = values[0].value;
	$("#txtNoNetworkCommunicationTimeoutReset").val(txtNoNetworkCommunicationTimeoutReset);
}



function setSerialNumber(rowData,searchType){
	//
	var val = $("ul#dcu_config_ul li.active").attr("name");
	if(searchType=="Meter"){  //Meter  //Commnuicator
	 	if(val == "2") {
			$("#txtMeterSn1").val(rowData.sn);
			$("#txtMeterId1").val(rowData.id);
		}
	
	}else if (searchType=="Commnuicator"){
		
	}
}



function veriffMeterSn(){
	//
	var re = /\S/;
	if(!re.test($("#txtMeterSn1").val())){
		$("#snErr1").show();
		$("#snSpan1").text(i18n.t("dcuModuleConfiguration.pleaseChooseMeterSn"));
		return 1;
	}
	
	$("#snErr1").hide();
	$("#snSpan1").text("");
	
	return 0;
}

function veriffIp(flag){
	//
	flag = flag == undefined ? true : false;
	var re = /((25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))/;
	var ipval = $("#txtIpAddress").val();
	if(flag){//get
		if(null == ipval || "" == ipval){
			$("#ipErr1").hide();
			$("#ipSpan1").text("");
			return 0;
		}
	} else {
		if(null == ipval || "" == ipval){
			$("#ipErr1").show();
			$("#ipSpan1").text(i18n.t("dcuConfiguration.pleaseFillIn"));
			return 1;
		}
	}
	
	if(!re.test(ipval)){
		$("#ipErr1").show();
		$("#ipSpan1").text(i18n.t("dcuConfiguration.pleaseFillInCorrect"));
		return 1;
	}
	
	$("#ipErr1").hide();
	$("#ipSpan1").text("");
	
	return 0;
}



function veriffIp2(flag){
	//
	flag = flag == undefined ? true : false;
	var re = /((25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))/;
	var ipval = $("#txtIpAddress2").val();
	if(flag){//get
		if(null == ipval || "" == ipval){
			$("#ipErr2").hide();
			$("#ipSpan2").text("");
			return 0;
		}
	} else {
		if(null == ipval || "" == ipval){
			$("#ipErr2").show();
			$("#ipSpan2").text(i18n.t("dcuConfiguration.pleaseFillIn"));
			return 1;
		}
	}
	
	if(!re.test(ipval)){
		$("#ipErr2").show();
		$("#ipSpan2").text(i18n.t("dcuConfiguration.pleaseFillInCorrect"));
		return 1;
	}
	
	$("#ipErr2").hide();
	$("#ipSpan2").text("");
	
	return 0;
}


function veriffPort(flag){
	//
	flag = flag == undefined ? true : false;
	var re = /^(\d|[1-9]\d{1,3}|[1-5]\d{4}|6([0-4]\d{3}|5([0-4]\d{2}|5([0-2]\d|3[0-4]))))|65535$/;
	var ipval = $("#txtPort").val();
	if(flag){//get
		if(null == ipval || "" == ipval){
			$("#portErr1").hide();
			$("#portSpan1").text("");
			return 0;
		}
	}else{
		if(null == ipval || "" == ipval){
			$("#portErr1").show();
			$("#portSpan1").text(i18n.t("dcuConfiguration.pleaseFillIn"));
			return 1;
		}
	}
	
	if(!re.test(ipval)){
		$("#portErr1").show();
		$("#portSpan1").text(i18n.t("dcuConfiguration.pleaseFillInCorrect"));
		return 1;
	}
	
	$("#portErr1").hide();
	$("#portSpan1").text("");
	
	return 0;
}



function veriffPort2(flag){
	//
	flag = flag == undefined ? true : false;
	var re = /^(\d|[1-9]\d{1,3}|[1-5]\d{4}|6([0-4]\d{3}|5([0-4]\d{2}|5([0-2]\d|3[0-4]))))|65535$/;
	var ipval = $("#txtPort2").val();
	if(flag){//get
		if(null == ipval || "" == ipval){
			$("#portErr2").hide();
			$("#portSpan2").text("");
			return 0;
		}
	}else{
		if(null == ipval || "" == ipval){
			$("#portErr2").show();
			$("#portSpan2").text(i18n.t("dcuConfiguration.pleaseFillIn"));
			return 1;
		}
	}
	
	if(!re.test(ipval)){
		$("#portErr2").show();
		$("#portSpan2").text(i18n.t("dcuConfiguration.pleaseFillInCorrect"));
		return 1;
	}
	
	$("#portErr2").hide();
	$("#portSpan2").text("");
	
	return 0;
}


function veriffIpBak(flag){
	//
	flag = flag == undefined ? true : false;
	var re = /((25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))/;
	var ipval = $("#txtIpAddressBackup").val();
	if(flag){//get
		if(null == ipval || "" == ipval){
			$("#ipErrbak").hide();
			$("#ipSpanbak").text("");
			return 0;
		}
	} else {
		if(null == ipval || "" == ipval){
			$("#ipErrbak").show();
			$("#ipSpanbak").text(i18n.t("dcuConfiguration.pleaseFillIn"));
			return 1;
		}
	}
	
	if(!re.test(ipval)){
		$("#ipErrbak").show();
		$("#ipSpanbak").text(i18n.t("dcuConfiguration.pleaseFillInCorrect"));
		return 1;
	}
	
	$("#ipErrbak").hide();
	$("#ipSpanbak").text("");
	
	return 0;
}

function veriffIpBak2(flag){
	//
	flag = flag == undefined ? true : false;
	var re = /((25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))/;
	var ipval = $("#txtIpAddressBackup2").val();
	if(flag){//get
		if(null == ipval || "" == ipval){
			$("#ipErrbak2").hide();
			$("#ipSpanbak2").text("");
			return 0;
		}
	} else {
		if(null == ipval || "" == ipval){
			$("#ipErrbak2").show();
			$("#ipSpanbak2").text(i18n.t("dcuConfiguration.pleaseFillIn"));
			return 1;
		}
	}
	
	if(!re.test(ipval)){
		$("#ipErrbak2").show();
		$("#ipSpanbak2").text(i18n.t("dcuConfiguration.pleaseFillInCorrect"));
		return 1;
	}
	
	$("#ipErrbak2").hide();
	$("#ipSpanbak2").text("");
	
	return 0;
}

function veriffPortBak(flag){
	//
	flag = flag == undefined ? true : false;
	var re = /^(\d|[1-9]\d{1,3}|[1-5]\d{4}|6([0-4]\d{3}|5([0-4]\d{2}|5([0-2]\d|3[0-4]))))|65535$/;
	var ipval = $("#txtPortBackup").val();
	
	if(flag){//get
		if(null == ipval || "" == ipval){
			$("#portErrbak").hide();
			$("#portSpanbak").text("");
			return 0;
		}
	} else {
		if(null == ipval || "" == ipval){
			$("#portErrbak").show();
			$("#portSpanbak").text(i18n.t("dcuConfiguration.pleaseFillIn"));
			return 1;
		}
	}
	
	if(!re.test(ipval)){
		$("#portErrbak").show();
		$("#portSpanbak").text(i18n.t("dcuConfiguration.pleaseFillInCorrect"));
		return 1;
	}
	
	$("#portErrbak").hide();
	$("#portSpanbak").text("");
	
	return 0;
}



function veriffPortBak2(flag){
	//
	flag = flag == undefined ? true : false;
	var re = /^(\d|[1-9]\d{1,3}|[1-5]\d{4}|6([0-4]\d{3}|5([0-4]\d{2}|5([0-2]\d|3[0-4]))))|65535$/;
	var ipval = $("#txtPortBackup2").val();
	
	if(flag){//get
		if(null == ipval || "" == ipval){
			$("#portErrbak2").hide();
			$("#portSpanbak2").text("");
			return 0;
		}
	} else {
		if(null == ipval || "" == ipval){
			$("#portErrbak2").show();
			$("#portSpanbak2").text(i18n.t("dcuConfiguration.pleaseFillIn"));
			return 1;
		}
	}
	
	if(!re.test(ipval)){
		$("#portErrbak2").show();
		$("#portSpanbak2").text(i18n.t("dcuConfiguration.pleaseFillInCorrect"));
		return 1;
	}
	
	$("#portErrbak2").hide();
	$("#portSpanbak2").text("");
	
	return 0;
}


function veriffUserName(flag){
	flag = flag == undefined ? true : false;
	var re = /^[^ ]+$/;
	var txtUserName = $("#txtUserName").val();
	
	if(flag){//get
		if(null == txtUserName || "" == txtUserName){
			$("#userNameErr").hide();
			$("#userNameSpan").text("");
			return 0;
		}
	} else {
		if(null == txtUserName || "" == txtUserName){
			$("#userNameErr").show();
			$("#userNameSpan").text(i18n.t("dcuConfiguration.pleaseFillIn"));
			return 1;
		}
	}
	
	if(!re.test(txtUserName)){
		$("#userNameErr").show();
		$("#userNameSpan").text(i18n.t("dcuConfiguration.pleaseFillInCorrect"));
		return 1;
	}
	
	$("#userNameErr").hide();
	$("#userNameSpan").text("");
	
	return 0;
}


function veriffPassword(flag){
	flag = flag == undefined ? true : false;
	var re = /^[^ ]+$/;
	var txtPassword = $("#txtPassword").val();
	
	if(flag){//get
		if(null == txtPassword || "" == txtPassword){
			$("#passwordErr").hide();
			$("#passwordSpan").text("");
			return 0;
		}
	} else {
		if(null == txtPassword || "" == txtPassword){
			$("#passwordErr").show();
			$("#passwordSpan").text(i18n.t("dcuConfiguration.pleaseFillIn"));
			return 1;
		}
	}
	
	if(!re.test(txtPassword)){
		$("#passwordErr").show();
		$("#passwordSpan").text(i18n.t("dcuConfiguration.pleaseFillInCorrect"));
		return 1;
	}
	
	$("#passwordErr").hide();
	$("#passwordSpan").text("");
	
	return 0;
}



function veriffUserName1(flag){
	flag = flag == undefined ? true : false;
	var re = /^[^ ]+$/;
	var txtUserName1 = $("#txtUserName1").val();
	
	if(flag){//get
		if(null == txtUserName1 || "" == txtUserName1){
			$("#userName1Err").hide();
			$("#userName1Span").text("");
			return 0;
		}
	} else {
		if(null == txtUserName1 || "" == txtUserName1){
			$("#userName1Err").show();
			$("#userName1Span").text(i18n.t("dcuConfiguration.pleaseFillIn"));
			return 1;
		}
	}
	
	if(!re.test(txtUserName1)){
		$("#userName1Err").show();
		$("#userName1Span").text(i18n.t("dcuConfiguration.pleaseFillInCorrect"));
		return 1;
	}
	
	$("#userName1Err").hide();
	$("#userName1Span").text("");
	
	return 0;
}


function veriffPassword1(flag){
	flag = flag == undefined ? true : false;
	var re = /^[^ ]+$/;
	var txtPassword1 = $("#txtPassword1").val();
	
	if(flag){//get
		if(null == txtPassword1 || "" == txtPassword1){
			$("#password1Err").hide();
			$("#password1Span").text("");
			return 0;
		}
	} else {
		if(null == txtPassword1 || "" == txtPassword1){
			$("#password1Err").show();
			$("#password1Span").text(i18n.t("dcuConfiguration.pleaseFillIn"));
			return 1;
		}
	}
	
	if(!re.test(txtPassword1)){
		$("#password1Err").show();
		$("#password1Span").text(i18n.t("dcuConfiguration.pleaseFillInCorrect"));
		return 1;
	}
	
	$("#password1Err").hide();
	$("#password1Span").text("");
	
	return 0;
}




function veriffPinCode(flag){
	flag = flag == undefined ? true : false;
	var re = /^[^ ]+$/;
	var txtPinCode = $("#txtPinCode").val();
	
	if(flag){//get
		if(null == txtPinCode || "" == txtPinCode){
			$("#pinCodeErr").hide();
			$("#pinCodeSpan").text("");
			return 0;
		}
	} else {
		if(null == txtPinCode || "" == txtPinCode){
			$("#pinCodeErr").show();
			$("#pinCodeSpan").text(i18n.t("dcuConfiguration.pleaseFillIn"));
			return 1;
		}
	}
	
	if(!re.test(txtPinCode)){
		$("#pinCodeErr").show();
		$("#pinCodeSpan").text(i18n.t("dcuConfiguration.pleaseFillInCorrect"));
		return 1;
	}
	
	$("#pinCodeErr").hide();
	$("#pinCodeSpan").text("");
	
	return 0;
}


function veriffApn(flag){
	flag = flag == undefined ? true : false;
	var re = /^[^ ]+$/;
	var txtApn = $("#txtApn").val();
	
	if(flag){//get
		if(null == txtApn || "" == txtApn){
			$("#apnErr").hide();
			$("#apnSpan").text("");
			return 0;
		}
	} else {
		if(null == txtApn || "" == txtApn){
			$("#apnErr").show();
			$("#apnSpan").text(i18n.t("dcuConfiguration.pleaseFillIn"));
			return 1;
		}
	}
	
	if(!re.test(txtApn)){
		$("#apnErr").show();
		$("#apnSpan").text(i18n.t("dcuConfiguration.pleaseFillInCorrect"));
		return 1;
	}
	
	$("#apnErr").hide();
	$("#apnSpan").text("");
	
	return 0;
}





/**
 * 通过UCI接口获取终端参数数据
 * @returns
 */
function getFunc(){

	otherStatusLineInit();
	if($("#autoCommunication1").val() == 1){
		var index = veriffMeterSn();
		if(index ==0){
			handleOtherParameterJqGrid("Get ",false ,1);
			ajaxGetOtherParameters();
		}
	}else if($("#autoCommunication2").val() == 1){
		var index = veriffMeterSn();
		if(index ==0){
			handleOtherParameterJqGrid("Get ",false ,2);
			ajaxGetOtherParameters();
		}
	}else if($("#autoCommunication3").val() == 1){
		var index = veriffMeterSn();
		if(index ==0){
			handleOtherParameterJqGrid("Get ",false ,3);
			ajaxGetOtherParameters();
		}
	}else if($("#autoCommunication4").val() == 1){
		var index = veriffMeterSn();
		if(index ==0){
			handleOtherParameterJqGrid("Get ",false ,4);
			ajaxGetOtherParameters();
		}
	}else if($("#autoCommunication5").val() == 1){
		var index = veriffMeterSn();
		if(index ==0){
			handleOtherParameterJqGrid("Get ",false ,5);
			ajaxGetOtherParameters();
		}
	}else if($("#autoCommunication6").val() == 1){
		var index = veriffMeterSn();
		if(index ==0){
			handleOtherParameterJqGrid("Get ",false ,6);
			ajaxGetOtherParameters();
		}
	}else if($("#autoCommunication7").val() == 1){
		var index = veriffMeterSn();
		if(index ==0){
			handleOtherParameterJqGrid("Get ",false ,7);
			ajaxGetOtherParameters();
		}
	}else if($("#autoCommunication8").val() == 1){
		var index = veriffMeterSn();
		if(index ==0){
			handleOtherParameterJqGrid("Get ",false ,8);
			ajaxGetOtherParameters();
		}
	}else if($("#autoCommunication9").val() == 1){
		var index = veriffMeterSn();
		if(index ==0){
			handleOtherParameterJqGrid("Get ",false ,9);
			ajaxGetOtherParameters();
		}
	}
	
}

function ajaxGetOtherParameters(){
	var meterSn = $("#txtMeterSn1").val();
	var meterId = $("#txtMeterId1").val();
	
	if(null==meterSn||""==meterSn){
		middleTipMsg(i18n.t("dcuConfiguration.selectCommunicators"));
		return;
	}
	
	var sendType = 0;
	
	 if($("#autoCommunication1").val() == 1){
			sendType = 1;
	 }else if($("#autoCommunication2").val() == 1){
			sendType = 2;
	 }else if($("#autoCommunication3").val() == 1){
			sendType = 3;
	 }else if($("#autoCommunication4").val() == 1){
			sendType = 4;
	 }else if($("#autoCommunication5").val() == 1){
			sendType = 5;
	 }else if($("#autoCommunication6").val() == 1){
			sendType = 6;
	 }else if($("#autoCommunication7").val() == 1){
			sendType = 7;
	 }else if($("#autoCommunication8").val() == 1){
			sendType = 8;
	 }else if($("#autoCommunication9").val() == 1){
			sendType = 9;
	 }
		
	layer.load();
	$.ajax({
		type : 'POST',
		url : getRootPathWeb() + '/dcuConfigurationController/ajaxGetOtherParametersModule.do',
		data : {
			meterSn : meterSn,
			meterId : meterId,
			sendType :sendType
		},
		dataType : 'json',
		success : function(data) {
			if(!data.success){
				middleTipMsg(data.msg,data.success);
				layer.closeAll('loading');
				return;
			}
			
			layer.closeAll('loading');
		},
		error : function(msg) {
			window.parent.layer.msg(
					'<t:mutiLang lanKey="system.requestError" />', {
						icon : 2
					});
			layer.closeAll('loading');
		}
	});
}

function setFunc(){
	
	if($("#autoCommunication1").val() == 1){

		var index = veriffMeterSn();
		index += veriffIp(false);
		index += veriffPort(false);
		index += veriffIpBak(false);
		index += veriffPortBak(false);

		if(index ==0){
			handleOtherParameterJqGrid("Set ",true ,1);
			ajaxSetOtherParameters();
		}
		
	}else if($("#autoCommunication2").val() == 1){
		var index = veriffMeterSn();
		index += veriffIp2(false);
		index += veriffPort2(false);
		index += veriffUserName1(false);
		index += veriffPassword1(false);

		if(index ==0){
			handleOtherParameterJqGrid("Set ",true ,2);
			ajaxSetOtherParameters();
		}
	}else if($("#autoCommunication3").val() == 1){
		handleOtherParameterJqGrid("Set ",true ,3);
		ajaxSetOtherParameters();
	}else if($("#autoCommunication4").val() == 1){
		handleOtherParameterJqGrid("Set ",true ,4);
		ajaxSetOtherParameters();
	}else if($("#autoCommunication5").val() == 1){
		handleOtherParameterJqGrid("Set ",true ,5);
		ajaxSetOtherParameters();
	}else if($("#autoCommunication6").val() == 1){
		handleOtherParameterJqGrid("Set ",true ,6);
		ajaxSetOtherParameters();
	}else if($("#autoCommunication7").val() == 1){
		handleOtherParameterJqGrid("Set ",true ,7);
		ajaxSetOtherParameters();
	}else if($("#autoCommunication8").val() == 1){
		handleOtherParameterJqGrid("Set ",true ,8);
		ajaxSetOtherParameters();
	}else if($("#autoCommunication9").val() == 1){
		handleOtherParameterJqGrid("Set ",true ,9);
		ajaxSetOtherParameters();
	}
	
}

function ajaxSetOtherParameters(){

	var meterSn = $("#txtMeterSn1").val();
	var meterId = $("#txtMeterId1").val();
	
	var ip = $("#txtIpAddress").val();
	var port = $("#txtPort").val();
	var ipBak = $("#txtIpAddressBackup").val();
	var portBak = $("#txtPortBackup").val();
	
	var ip2 = $("#txtIpAddress2").val();
	var port2 = $("#txtPort2").val();
	var userName1 = $("#txtUserName1").val();
	var password1 = $("#txtPassword1").val();
	
	var txtApn = $("#txtApn").val();
	var txtUserName = $("#txtUserName").val();
	var txtPassword = $("#txtPassword").val();
	
	var connectMode =  $('#selConnectMode option:selected').val();
	var answerMode =  $('#selAnswerMode option:selected').val();
	
	var txtPinCode = $("#txtPinCode").val();
	
	var txtInactivityTimeout = $("#txtInactivityTimeout").val();
	var txtNoNetworkCommunicationTimeoutReset = $("#txtNoNetworkCommunicationTimeoutReset").val();
	
	var connectMode = 0;
	var answerMode = 0;
	
	//不同的发送类型
	var sendType = 0;
	
	var params = new Array();

	
	if(null==ip||""==ip){
		ip = 0;
	}
	if(null==port||""==port){
		port = 0;
	}
	if(null==ipBak||""==ipBak){
		ipBak = 0;
	}
	if(null==portBak||""==portBak){
		portBak = 0;
	}
	
	if(null==ip2||""==ip2){
		ip2 = 0;
	}
	if(null==port2||""==port2){
		port2 = 0;
	}
	
	if(null==txtInactivityTimeout||""==txtInactivityTimeout){
		txtInactivityTimeout = 0;
	}
	if(null==txtNoNetworkCommunicationTimeoutReset||""==txtNoNetworkCommunicationTimeoutReset){
		txtNoNetworkCommunicationTimeoutReset = 0;
	}
	
	
	if($("#autoCommunication1").val() == 1){
	    sendType = 1; // HES ip and port
	}else if($("#autoCommunication2").val() == 1){
		sendType = 2; //  FTP IP and Port
	}else if($("#autoCommunication3").val() == 1){
		sendType = 3; //	APN
	}else if($("#autoCommunication4").val() == 1){
		sendType = 4; //  User And Password
	}else if($("#autoCommunication5").val() == 1){
		//Auto Connect Mode
		connectMode = $('#selConnectMode option:selected').val();
		if(connectMode == -1){
			otherStatusLineInit();	
			middleTipMsg(i18n.t("dcuModuleConfiguration.selectConnectType"));	
			return
		}
		sendType = 5;
		
	}else if($("#autoCommunication6").val() == 1){
	//Auto Answer Mode
		answerMode = $('#selAnswerMode option:selected').val();
		if(answerMode == -1){
			otherStatusLineInit();	
			middleTipMsg(i18n.t("dcuModuleConfiguration.selectModeType"));	
			return
		}
		sendType = 6;
		
		
	}else if($("#autoCommunication7").val() == 1){
	//PIN Code
		sendType = 7;
	}else if($("#autoCommunication8").val() == 1){
	//Inactivity Timeout
		sendType = 8;
	}else if($("#autoCommunication9").val() == 1){
	//		No Network Communication Timeout Reset
		sendType = 9;
	}
	
	layer.load();
	$.ajax({
		type : 'POST',
		url : getRootPathWeb() + '/dcuConfigurationController/ajaxSetOtherParametersModule.do',
		data : {
			meterSn : meterSn,
			meterId : meterId,
			ip : ip,
			port : port,
			ipBak : ipBak,
			portBak : portBak,
			ip2 : ip2,
			port2 : port,
			userName1 : userName1,
			password1 : password1,
			apn : txtApn,
			userName : txtUserName,
			password : txtPassword,
			connectMode : connectMode,
			answerMode : answerMode,
			pinCode : txtPinCode,
			inactivityTimeout : txtInactivityTimeout,
			noNetworkCommunicationTimeoutReset : txtNoNetworkCommunicationTimeoutReset,
			sendType : sendType
		},
		dataType : 'json',
		success : function(data) {
			if(!data.success){
				middleTipMsg(data.msg,data.success);
				layer.closeAll('loading');
				return;
			}
			
			layer.closeAll('loading');
		},
		error : function(msg) {
			window.parent.layer.msg(
					'<t:mutiLang lanKey="system.requestError" />', {
						icon : 2
					});
			layer.closeAll('loading');
		}
	});
}

//todo other 设置状态栏
function handleOtherParameterJqGrid(title,flag,tabFlag){

	var commParameterTitle = "Module Parameter";
	//false get
	
	if(!flag){
		if(tabFlag == 1){
			commParameterTitle =title+" HES ip and port";
		}else if(tabFlag == 2){
			commParameterTitle =title+" FTP IP and Port";
		}else if(tabFlag == 3){
			commParameterTitle =title+"APN";
		}else if(tabFlag == 4){
			commParameterTitle =title+"User And Password";
		}else if(tabFlag == 5){
			commParameterTitle =title+"Auto Connect Mode";
		}else if(tabFlag == 6){
			commParameterTitle =title+"Auto Answer Mode";
		}else if(tabFlag == 7){
			commParameterTitle =title+"PIN Code";
		}else if(tabFlag == 8){
			commParameterTitle =title+"Inactivity Timeout";
		}else if(tabFlag == 9){
			commParameterTitle =title+"No Network Communication Timeout Reset";
		}
	}else{
		if(tabFlag == 1){
			commParameterTitle =title+" HES ip and port";
		}else if(tabFlag == 2){
			commParameterTitle =title+" FTP IP and Port";
		}else if(tabFlag == 3){
			commParameterTitle =title+"APN";
		}else if(tabFlag == 4){
			commParameterTitle =title+"User And Password";
		}else if(tabFlag == 5){
			commParameterTitle =title+"Auto Connect Mode";
		}else if(tabFlag == 6){
			commParameterTitle =title+"Auto Answer Mode";
		}else if(tabFlag == 7){
			commParameterTitle =title+"PIN Code";
		}else if(tabFlag == 8){
			commParameterTitle =title+"Inactivity Timeout";
		}else if(tabFlag == 9){
			commParameterTitle =title+"No Network Communication Timeout Reset";
		}
	}

	var meterId = $("#txtMeterId1").val();
	var meterSn = $("#txtMeterSn1").val();

	var myDate = new Date();
	
	var  requestTime = myDate.Format($("#dcuJsDateAndTimeFormatter").val());
	
	$.ajax({
				async : false,
	            cache : false,
	            traditional: true,
				type: 'POST',
				url: getRootPathWeb()+'/systemController/currentTime.do',
				dataType: 'json',
				success: function(data) {
					if(data.success) {
						requestTime = data.msg;
					} 
				},
				error: function(msg) {
					window.parent.layer.msg(i18n.t("system.requestError"), {icon: 2});
					layer.closeAll('loading');
				}
			});

	$("#otherCommand").html(commParameterTitle);
	
	var proccesingI18n = i18n.t("limiterList.processing");
	var proccessingStatusHtml = '<a style=\'color:#0000C6\' onclick="openLogExplorer(\''
	+ meterId + '\',\'' + meterSn + '\',\''+requestTime+'\');">'+proccesingI18n+'</a>';
	
	$("#otherStatus").html(proccessingStatusHtml);
	$("#otherRequestTime").html(requestTime);
	
	
	$("#dcuRequestTimeOther").val(requestTime);
}



function otherStatusLineInit(){
	$("#otherResponseTime").html('');
	$("#otherRequestTime").html('');
	$("#otherCommand").html('');
	$("#otherStatus").html('');
	$("#otherReason").html('');

}

	
