/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetReportRoleMap{ } 
 * 
 * 摘    要： assetReportRoleMap
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2020-06-23 04:02:06
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.report;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class AssetReportRoleMap extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public AssetReportRoleMap() {
	}
	/**reportId*/
	private String reportId;
	/**roleId*/
	private String roleId;

	
	
	public String getReportId() {
		return reportId;
	}

	public void setReportId(String reportId) {
		this.reportId = reportId;
	}

	/**
	 * roleId
	 * @return the value of ASSET_REPORT_ROLE_MAP.ROLE_ID
	 * @mbggenerated 2020-06-23 04:02:06
	 */
	public String getRoleId() {
		return roleId;
	}

	/**
	 * roleId
	 * @param roleId the value for ASSET_REPORT_ROLE_MAP.ROLE_ID
	 * @mbggenerated 2020-06-23 04:02:06
	 */
    	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public AssetReportRoleMap(String roleId ) {
		super();
		this.roleId = roleId;
	}

}