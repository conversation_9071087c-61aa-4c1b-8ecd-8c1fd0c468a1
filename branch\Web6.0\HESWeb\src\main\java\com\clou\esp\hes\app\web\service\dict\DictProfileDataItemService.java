/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictProfileDataItem{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-02-26 09:36:52
 * 最后修改时间：
 * 
*********************************************************************************************************/

package com.clou.esp.hes.app.web.service.dict;

import java.util.List;

import com.clou.esp.hes.app.web.model.dict.DictProfileDataItem;
import com.clou.esp.hes.app.web.service.common.CommonService;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

public interface DictProfileDataItemService extends CommonService<DictProfileDataItem>{
	
	List<DictProfileDataItem> getProfileDataitemList(DictProfileDataItem entity);
	
	public JqGridResponseTo unBindForJqGrid(JqGridSearchTo jqGridSearchTo);
	
}