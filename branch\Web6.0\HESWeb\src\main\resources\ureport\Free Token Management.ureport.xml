<?xml version="1.0" encoding="UTF-8"?><ureport><cell expand="None" name="A1" row="1" col="1"><cell-style font-size="10" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[customer No]]></simple-value></cell><cell expand="None" name="B1" row="1" col="2"><cell-style font-size="10" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Customer Name]]></simple-value></cell><cell expand="None" name="C1" row="1" col="3"><cell-style font-size="10" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Serial Number]]></simple-value></cell><cell expand="None" name="D1" row="1" col="4"><cell-style font-size="10" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Charge Amount]]></simple-value></cell><cell expand="None" name="E1" row="1" col="5"><cell-style font-size="10" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Free token Number]]></simple-value></cell><cell expand="None" name="F1" row="1" col="6"><cell-style font-size="10" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[remark]]></simple-value></cell><cell expand="None" name="G1" row="1" col="7"><cell-style font-size="10" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[operator Name]]></simple-value></cell><cell expand="None" name="H1" row="1" col="8"><cell-style font-size="10" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[vending station]]></simple-value></cell><cell expand="None" name="I1" row="1" col="9"><cell-style font-size="10" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[create date]]></simple-value></cell><cell expand="Down" name="A2" row="2" col="1"><cell-style font-size="9" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="customerNo" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="B2" row="2" col="2"><cell-style font-size="9" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="customerName" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="C2" row="2" col="3"><cell-style font-size="9" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="serialNumber" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="D2" row="2" col="4"><cell-style font-size="9" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="chargeAmount" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="E2" row="2" col="5"><cell-style font-size="9" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="freeTokenNumber" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="F2" row="2" col="6"><cell-style font-size="9" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="remark" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="G2" row="2" col="7"><cell-style font-size="9" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="operatorName" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="H2" row="2" col="8"><cell-style font-size="9" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="vendingStation" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="I2" row="2" col="9"><cell-style font-size="9" font-family="Times New Roman" format="dd-MM-yyyy" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="createDate" order="none" mapping-type="simple"></dataset-value></cell><row row-number="1" height="18" band="title"/><row row-number="2" height="18"/><column col-number="1" width="80"/><column col-number="2" width="80"/><column col-number="3" width="80"/><column col-number="4" width="80"/><column col-number="5" width="74"/><column col-number="6" width="150"/><column col-number="7" width="74"/><column col-number="8" width="74"/><column col-number="9" width="74"/><datasource name="freeTokenManageDataSource" type="spring" bean="freeTokenManageDataSource"><dataset name="reportData" type="bean" method="loadReportData" clazz="com.clou.esp.ppm.app.web.ureport.dataset.FreeTokenManageDataSet"><field name="chargeAmount"/><field name="createDate"/><field name="customerName"/><field name="customerNo"/><field name="freeTokenNumber"/><field name="operatorName"/><field name="remark"/><field name="serialNumber"/><field name="vendingStation"/></dataset></datasource><paper type="A4" left-margin="90" right-margin="90"
    top-margin="72" bottom-margin="72" paging-mode="fitpage" fixrows="0"
    width="595" height="842" orientation="portrait" html-report-align="left" bg-image="" html-interval-refresh-value="0" column-enabled="false"></paper></ureport>