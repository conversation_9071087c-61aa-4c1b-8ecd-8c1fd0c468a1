
/**
 * Please modify this class to meet your needs
 * This class is not complete
 */

package ch.iec.tc57._2011.meterreadschedule;

import java.util.logging.Logger;
import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.bind.annotation.XmlSeeAlso;

/**
 * This class was generated by Apache CXF 3.1.1
 * 2018-02-01T09:54:34.736+08:00
 * Generated source version: 3.1.1
 * 
 */

@javax.jws.WebService(
                      serviceName = "MeterReadSchedule",
                      portName = "MeterReadSchedule_Port",
                      targetNamespace = "http://iec.ch/TC57/2011/MeterReadSchedule",
                      wsdlLocation = "file:/F:/workspace/hesdesign/IEC61968/wsdl/MeterReadSchedule.wsdl",
                      endpointInterface = "ch.iec.tc57._2011.meterreadschedule.MeterReadSchedulePort")
                      
public class MeterReadSchedule_PortImpl implements MeterReadSchedulePort {

    private static final Logger LOG = Logger.getLogger(MeterReadSchedule_PortImpl.class.getName());

    /* (non-Javadoc)
     * @see ch.iec.tc57._2011.meterreadschedule.MeterReadSchedulePort#getMeterReadSchedule(ch.iec.tc57._2011.meterreadschedulemessage.MeterReadScheduleRequestMessageType  getMeterReadSchedule )*
     */
    public ch.iec.tc57._2011.meterreadschedulemessage.MeterReadScheduleResponseMessageType getMeterReadSchedule(ch.iec.tc57._2011.meterreadschedulemessage.MeterReadScheduleRequestMessageType getMeterReadSchedule) throws FaultMessage    { 
        LOG.info("Executing operation getMeterReadSchedule");
        System.out.println(getMeterReadSchedule);
        try {
            ch.iec.tc57._2011.meterreadschedulemessage.MeterReadScheduleResponseMessageType _return = null;
            return _return;
        } catch (java.lang.Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException(ex);
        }
        //throw new FaultMessage("FaultMessage...");
    }

}
