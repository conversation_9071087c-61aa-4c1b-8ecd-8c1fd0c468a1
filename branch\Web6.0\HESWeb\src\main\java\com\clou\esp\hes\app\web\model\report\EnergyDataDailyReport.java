/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictReport{ } 
 * 
 * 摘    要： dictReport
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-05-22 04:14:25
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.report;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class EnergyDataDailyReport  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public EnergyDataDailyReport() {
	}


	private Integer index;
	private java.lang.String meterSn;
	private Integer pt;
	private Integer ct;
	private java.lang.String dataName;
	private java.lang.String startCode;
	private java.lang.String endCode;
	private java.lang.String electricQuantity;

	
	
	public Integer getPt() {
		return pt;
	}
	public void setPt(Integer pt) {
		this.pt = pt;
	}
	public Integer getCt() {
		return ct;
	}
	public void setCt(Integer ct) {
		this.ct = ct;
	}
    
	public Integer getIndex() {
		return index;
	}
	public void setIndex(Integer index) {
		this.index = index;
	}
	public java.lang.String getMeterSn() {
		return meterSn;
	}
	public void setMeterSn(java.lang.String meterSn) {
		this.meterSn = meterSn;
	}
	
	public java.lang.String getDataName() {
		return dataName;
	}
	public void setDataName(java.lang.String dataName) {
		this.dataName = dataName;
	}
	public java.lang.String getStartCode() {
		return startCode;
	}
	public void setStartCode(java.lang.String startCode) {
		this.startCode = startCode;
	}
	public java.lang.String getEndCode() {
		return endCode;
	}
	public void setEndCode(java.lang.String endCode) {
		this.endCode = endCode;
	}
	public java.lang.String getElectricQuantity() {
		return electricQuantity;
	}
	public void setElectricQuantity(java.lang.String electricQuantity) {
		this.electricQuantity = electricQuantity;
	}
	
	
}