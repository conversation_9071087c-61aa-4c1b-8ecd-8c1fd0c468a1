
package ch.iec.tc57._2011.meterreadschedule;

import javax.xml.ws.WebFault;


/**
 * This class was generated by Apache CXF 3.1.1
 * 2018-02-01T09:54:34.712+08:00
 * Generated source version: 3.1.1
 */

@WebFault(name = "MeterReadScheduleFaultMessage", targetNamespace = "http://www.iec.ch/TC57/2011/MeterReadScheduleMessage")
public class FaultMessage extends Exception {
    
    private ch.iec.tc57._2011.meterreadschedulemessage.MeterReadScheduleFaultMessageType meterReadScheduleFaultMessage;

    public FaultMessage() {
        super();
    }
    
    public FaultMessage(String message) {
        super(message);
    }
    
    public FaultMessage(String message, Throwable cause) {
        super(message, cause);
    }

    public FaultMessage(String message, ch.iec.tc57._2011.meterreadschedulemessage.MeterReadScheduleFaultMessageType meterReadScheduleFaultMessage) {
        super(message);
        this.meterReadScheduleFaultMessage = meterReadScheduleFaultMessage;
    }

    public FaultMessage(String message, ch.iec.tc57._2011.meterreadschedulemessage.MeterReadScheduleFaultMessageType meterReadScheduleFaultMessage, Throwable cause) {
        super(message, cause);
        this.meterReadScheduleFaultMessage = meterReadScheduleFaultMessage;
    }

    public ch.iec.tc57._2011.meterreadschedulemessage.MeterReadScheduleFaultMessageType getFaultInfo() {
        return this.meterReadScheduleFaultMessage;
    }
}
