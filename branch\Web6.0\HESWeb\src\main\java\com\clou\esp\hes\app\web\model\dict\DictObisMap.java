/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictObisMap{ } 
 * 
 * 摘    要： OBIS映射表
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.dict;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DictObisMap  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DictObisMap() {
	}

	/**mapObis*/
	private java.lang.String mapObis;

	/**
	 * mapObis
	 * @return the value of DICT_OBIS_MAP.MAP_OBIS
	 * @mbggenerated 2017-11-22 03:30:03
	 */
	public java.lang.String getMapObis() {
		return mapObis;
	}

	/**
	 * mapObis
	 * @param mapObis the value for DICT_OBIS_MAP.MAP_OBIS
	 * @mbggenerated 2017-11-22 03:30:03
	 */
    	public void setMapObis(java.lang.String mapObis) {
		this.mapObis = mapObis;
	}

	public DictObisMap(java.lang.String mapObis ) {
		super();
		this.mapObis = mapObis;
	}

}