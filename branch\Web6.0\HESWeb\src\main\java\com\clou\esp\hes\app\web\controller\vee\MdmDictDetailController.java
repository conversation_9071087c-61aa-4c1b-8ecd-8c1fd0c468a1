/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class MdmDictDetail{ } 
 * 
 * 摘    要： MDM字典表
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2020-04-21 07:32:26
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.vee;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.model.vee.MdmDictDetail;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.vee.MdmDictDetailService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @时间：2020-04-21 07:32:26
 * @描述：MDM字典表类
 */
@Controller
@RequestMapping("/mdmDictDetailController")
public class MdmDictDetailController extends BaseController {

 	@Resource
    private MdmDictDetailService mdmDictDetailService;
 	
 	@Resource
 	private DataUserLogService dataUserLogService;

	/**
	 * 跳转到MDM字典表列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/vee/mdmDictDetailList");
    }

	/**
	 * 跳转到MDM字典表新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "mdmDictDetail")
	public ModelAndView mdmDictDetail(MdmDictDetail mdmDictDetail, HttpServletRequest request, Model model) {
		if(mdmDictDetail.getInnerValue()!=null){
			try {
                mdmDictDetail=mdmDictDetailService.get(mdmDictDetail);
            }catch (Exception e) {
                e.printStackTrace();
            }
		}
		model.addAttribute("mdmDictDetail", mdmDictDetail);
		return new ModelAndView("/vee/mdmDictDetail");
	}


	/**
	 * MDM字典表查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request,String dictId,Integer innerValue,String guiDisplayName,boolean select) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        if(!select) {
        	return j;
        }
        try {
        	jqGridSearchTo.put("guiDisplayName", guiDisplayName);
        	jqGridSearchTo.put("dictId", dictId);
        	jqGridSearchTo.put("innerValue", innerValue);
            j=mdmDictDetailService.getForJqGrid(jqGridSearchTo);
        }catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除MDM字典表信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(MdmDictDetail mdmDictDetail, HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su= TokenManager.getToken();
            if(mdmDictDetailService.deleteById(mdmDictDetail.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
    
    /**
     * 保存MDM字典表信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class }) MdmDictDetail mdmDictDetail, BindingResult bindingResult, HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        MdmDictDetail t=new MdmDictDetail();
        SysUser su = TokenManager.getToken();
        
        try {
        	t=mdmDictDetailService.get(mdmDictDetail);
        	if(t!=null) {
        		MyBeanUtils.copyBeanNotNull2Bean(mdmDictDetail, t);
				mdmDictDetailService.update(t);
				j.setMsg(MutiLangUtil.doMutiLang("system.updateSucc"));
				
				// 记录更新
				dataUserLogService.insertDataUserLog(su.getId(), 
						"Dictionary Management", "Add Dictionary Detail",
						"[Update Dictionary:"+ "Dict Id:"+ mdmDictDetail.getDictId() +",Dict Detail Id:"+mdmDictDetail.getInnerValue() +",Dict Detail Name:"+mdmDictDetail.getGuiDisplayName() +",Dict Detail Description"+ mdmDictDetail.getReadme() +"]");
			}else{
	            mdmDictDetailService.save(mdmDictDetail);
	            j.setMsg(MutiLangUtil.doMutiLang("system.addSucc"));
	            // 记录插入
	            dataUserLogService.insertDataUserLog(su.getId(), 
						"Dictionary Management", "Edit Dictionary Detail",
						"[Add Dictionary:"+ "Dict Id:"+ mdmDictDetail.getDictId() +",Dict Detail Id:"+mdmDictDetail.getInnerValue() +",Dict Detail Name:"+mdmDictDetail.getGuiDisplayName() +",Dict Detail Description"+ mdmDictDetail.getReadme() +"]");
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
	
    @RequestMapping(value = "isExist")
	@ResponseBody
	public AjaxJson isExist(MdmDictDetail type , HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		type=this.mdmDictDetailService.get(type);
		if(type!=null) {
			j.setSuccess(false);
		}
		return j;
	}
	
}