/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeterGroup{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-01-16 01:58:30
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.asset;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import ch.iec.tc57._2011.meterdefineconfig_.Arrays;
import ch.iec.tc57._2011.meterdefineconfig_.MeterDefineConfig;
import ch.iec.tc57._2011.meterdefineconfig_.Values;
import ch.iec.tc57._2011.meterdefineconfigmessage.MeterDefineConfigPayloadType;
import ch.iec.tc57._2011.meterdefineconfigmessage.MeterDefineConfigRequestMessageType;

import com.clou.esp.hes.app.web.core.util.DateTimeFormatterUtil;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.core.util.XMLUtil;
import com.clou.esp.hes.app.web.dao.asset.AssetMeterGroupDao;
import com.clou.esp.hes.app.web.dao.asset.AssetMeterGroupValueDao;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroup;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroupValue;
import com.clou.esp.hes.app.web.service.asset.AssetMeterGroupService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@Component
@Service("assetMeterGroupService")
public class AssetMeterGroupServiceImpl  extends CommonServiceImpl<AssetMeterGroup>  implements AssetMeterGroupService {

	final static String TIME_FLAG = ResourceUtil.getSessionattachmenttitle("local.time.flag");
	@Resource
	private AssetMeterGroupDao assetMeterGroupDao;
	@Resource
	private AssetMeterGroupValueDao assetMeterGroupValueDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(assetMeterGroupDao);
    }
	public AssetMeterGroupServiceImpl() {}
	
	@Override
	public AssetMeterGroup selectGroupNameByMeterSN(String sn, String channelId) {
		return assetMeterGroupDao.selectGroupNameByMeterSN(sn, channelId);
	}
	
	@Override
	public JqGridResponseTo datagridStepTariff(JqGridSearchTo jqGridSearchTo) {
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		List<AssetMeterGroup> list = assetMeterGroupDao.datagridStepTariff(jqGridSearchTo);
		//获取阶梯汇率时间
		for (AssetMeterGroup assetMeterGroup : list) {
			AssetMeterGroupValue value = new AssetMeterGroupValue();
			value.setGroupId(assetMeterGroup.getId());
			value.setDataitemId("40.0.3.2");
			AssetMeterGroupValue timeEntity = assetMeterGroupValueDao.get(value);
			if(StringUtil.isNotEmpty(timeEntity)){
				MeterDefineConfigRequestMessageType requestMessageTime = (MeterDefineConfigRequestMessageType) 
						XMLUtil.convertXmlStrToObject(MeterDefineConfigRequestMessageType.class, timeEntity.getXmlValue());
				MeterDefineConfigPayloadType payloadType = requestMessageTime.getPayload();
				MeterDefineConfig config = payloadType.getMeterDefineConfig();
    	        List<Arrays> arraysList = config.getArrays();
    	        Arrays arrays = arraysList.get(0);
    	        List<Values> valuesList = arrays.getValues();
    	        Values values = valuesList.get(0);
    	        assetMeterGroup.setStepTariffActivationTime(DateUtils.dateformat(values.getValue(), "yyyy-MM-dd HH:mm:ss", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
//    	        assetMeterGroup.setStepTariffActivationTime(DateUtils.dateformat(values.getValue(), 
//        				new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"), DateTimeFormatterUtil.getSimpleDateFormat_HHmmss(TIME_FLAG)));
			}
		}
		PageInfo<AssetMeterGroup> pageInfo = new PageInfo<AssetMeterGroup>(list);
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}
	
	
}