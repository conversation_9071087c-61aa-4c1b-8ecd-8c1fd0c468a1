/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysUser{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-10-27 08:02:17
 * 最后修改时间：
 * 
*********************************************************************************************************/

package com.clou.esp.hes.app.web.service.system;

import java.util.List;

import com.clou.esp.hes.app.web.service.common.CommonService;
import com.clou.esp.hes.app.web.model.system.SysUser;

public interface SysUserService extends CommonService<SysUser>{
	
	/**
	 * 校验用户账户的唯一性，根据用户账户查询
	 * @Description 
	 * @param userAccount
	 * @return List<SysUser>
	 * <AUTHOR> 
	 * @Time 2018年3月15日 下午3:26:49
	 */
	List<SysUser> vaildAccount(String userAccount);
}