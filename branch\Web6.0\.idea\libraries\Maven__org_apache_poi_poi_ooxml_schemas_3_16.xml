<component name="libraryTable">
  <library name="Maven: org.apache.poi:poi-ooxml-schemas:3.16">
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/poi/poi-ooxml-schemas/3.16/poi-ooxml-schemas-3.16.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/poi/poi-ooxml-schemas/3.16/poi-ooxml-schemas-3.16-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/poi/poi-ooxml-schemas/3.16/poi-ooxml-schemas-3.16-sources.jar!/" />
    </SOURCES>
  </library>
</component>