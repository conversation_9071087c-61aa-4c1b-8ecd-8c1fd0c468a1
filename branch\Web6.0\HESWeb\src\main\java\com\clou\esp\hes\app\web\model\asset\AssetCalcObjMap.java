package com.clou.esp.hes.app.web.model.asset;

import org.apache.commons.lang.StringUtils;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.common.BaseEntity;


/**
 * @ClassName: AssetCalcObjMap
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年9月5日 上午11:13:44
 *
 */
public class AssetCalcObjMap extends BaseEntity {

	private static final long serialVersionUID = 1L;
	
	/**
	 * 1:供入 2:供出
	 */
	private int type;
	
	private String meteringId;
	private String meterSn;
	private String meterName;
	
	/**
	 * 1:METER; 2:CALC_OJB
	 */
	private int meteringType;
	
	private String dataitemId;
	private String dataItemName;
	
	private String isExist;//页面用
	
	public String getCalcObjId() {
		return id;
	}
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}
	public String getMeteringId() {
		return meteringId;
	}
	public void setMeteringId(String meteringId) {
		this.meteringId = meteringId;
	}
	public int getMeteringType() {
		return meteringType;
	}
	public void setMeteringType(int meteringType) {
		this.meteringType = meteringType;
	}
	
	public String getDataitemId() {
		return dataitemId;
	}
	public void setDataitemId(String dataitemId) {
		this.dataitemId = dataitemId;
	}
	
	public String getDataItemName() {
		return MutiLangUtil.assemblyI18nData("dictDataItem", dataItemName);
	}
	
	public void setDataItemName(String dataItemName) {
		this.dataItemName = dataItemName;
	}
	public String getMeterSn() {
		return meterSn;
	}
	public void setMeterSn(String meterSn) {
		this.meterSn = meterSn;
	}
	public String getMeterName() {
		return meterName;
	}
	public void setMeterName(String meterName) {
		this.meterName = meterName;
	}
	public String getIsExist() {
		return isExist;
	}
	public void setIsExist(String isExist) {
		this.isExist = isExist;
	}
	
	
}
