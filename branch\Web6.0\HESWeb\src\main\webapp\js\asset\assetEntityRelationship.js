var select = false;
var calcObjId;

var transformerSelect = false;
var transformerQuerySelect = false;

function editLinkedMeterByLineRowData() {
	
}

function editLinkedMeterByTransformerRowData() {
	
}

function queryLinkedMeterByLineFun(){
	var parentId = $("#calcLineId").val();
	var data = {type:1,parentId:parentId,select:select};
	return data;
}

function queryLinkedMeterByTransformerFun(){
	var parentId = $("#relTransformerId").val();
	var data = {type:1,parentId:parentId,select:transformerSelect};
	return data;
}

function querylinkedTransformerFun() {
	var parentId = $("#calcLineId").val();
	var data = {type:4,parentId:parentId,select:transformerQuerySelect};
	return data;
}

function queryLinkedTransformerByLineOnclick() {
	transformerQuerySelect = true;
	lineLinkdedTransformersearchOnEnterFn();
	calcObjId = null; // 复位
}

function queryLinkedMeterByLineOnclick() {
	select = true;
	lineLinkedMeterByLinesearchOnEnterFn();
	calcObjId = null; // 复位
}

function queryLinkedMeterByTransformerOnclick() {
	transformerSelect = true;
	lineLinkedMeterByTransformersearchOnEnterFn();
	calcObjId = null; // 复位
}

function deleteLineMeterRelation(id){
	var rowData = $("#lineLinkedMeterByLine").jqGrid("getRowData", id);
	var parentId = $("#calcLineId").val();
	var close_link = window.parent.layer.confirm(i18n.t("system.aydeltherdate"), 
		{
			btn: [i18n.t("system.determine"), i18n.t("system.cancel")],
			title: i18n.t("system.delete")
		}, function() {
			window.parent.layer.close(close_link);
			layer.load();
			$.ajax({
				async : true,
	            cache : false,
	            traditional: true,
				type: 'POST',
				url: getRootPathWeb()+'/assetEntityRelationController/delMeterRelationship.do?&id=' + rowData.id +"&parentType="+ rowData.parentType
					+"&parentId=" + parentId,
				dataType: 'json',
				success: function(data) {
					if(data.success) {
						$("#lineLinkedMeterByLine").trigger('reloadGrid');	//刷新界面
						middleTipMsg(data.msg,true);
					} else {
						window.parent.layer.msg(data.msg, {icon: 2});
					}
					layer.closeAll('loading');
				},
				error: function(msg) {
					window.parent.layer.msg(i18n.t("system.requestError"), {icon: 2});
					layer.closeAll('loading');
				}
			});
		});
}

function deleteTransformerMeterRelation(id){
	var rowData = $("#lineLinkedMeterByTransformer").jqGrid("getRowData", id);
	var parentId = $("#relTransformerId").val();
	var close_link = window.parent.layer.confirm(i18n.t("system.aydeltherdate"), 
		{
			btn: [i18n.t("system.determine"), i18n.t("system.cancel")],
			title: i18n.t("system.delete")
		}, function() {
			window.parent.layer.close(close_link);
			layer.load();
			$.ajax({
				async : true,
	            cache : false,
	            traditional: true,
				type: 'POST',
				url: getRootPathWeb()+'/assetEntityRelationController/delMeterRelationship.do?&id=' + rowData.id +"&parentType="+ rowData.parentType
					+"&parentId=" + parentId,
				dataType: 'json',
				success: function(data) {
					if(data.success) {
						$("#lineLinkedMeterByTransformer").trigger('reloadGrid');	//刷新界面
						middleTipMsg(data.msg,true);
					} else {
						window.parent.layer.msg(data.msg, {icon: 2});
					}
					layer.closeAll('loading');
				},
				error: function(msg) {
					window.parent.layer.msg(i18n.t("system.requestError"), {icon: 2});
					layer.closeAll('loading');
				}
			});
		});
}

function deleteLineTransformerRelation(id){
	var rowData = $("#lineLinkdedTransformer").jqGrid("getRowData", id);
	var parentId = $("#calcLineId").val();
	var close_link = window.parent.layer.confirm(i18n.t("system.aydeltherdate"), 
		{
			btn: [i18n.t("system.determine"), i18n.t("system.cancel")],
			title: i18n.t("system.delete")
		}, function() {
			window.parent.layer.close(close_link);
			layer.load();
			$.ajax({
				async : true,
	            cache : false,
	            traditional: true,
				type: 'POST',
				url: getRootPathWeb()+'/assetEntityRelationController/delTransformerRelationship.do?&id=' + rowData.id +"&parentType="+ rowData.parentType
					+"&parentId=" + parentId,
				dataType: 'json',
				success: function(data) {
					if(data.success) {
						$("#lineLinkdedTransformer").trigger('reloadGrid');	//刷新界面
						middleTipMsg(data.msg,true);
					} else {
						window.parent.layer.msg(data.msg, {icon: 2});
					}
					layer.closeAll('loading');
				},
				error: function(msg) {
					window.parent.layer.msg(i18n.t("system.requestError"), {icon: 2});
					layer.closeAll('loading');
				}
			});
		});
}
