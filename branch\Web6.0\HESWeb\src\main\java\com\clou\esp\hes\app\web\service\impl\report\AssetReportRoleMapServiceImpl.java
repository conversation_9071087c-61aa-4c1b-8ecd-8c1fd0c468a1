/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetReportRoleMap{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2020-06-23 04:02:06
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.report;

import com.clou.esp.hes.app.web.dao.report.AssetReportRoleMapDao;
import com.clou.esp.hes.app.web.model.report.AssetReportRoleMap;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.clou.esp.hes.app.web.service.report.AssetReportRoleMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Component
@Service("assetReportRoleMapService")
public class AssetReportRoleMapServiceImpl  extends CommonServiceImpl<AssetReportRoleMap> implements AssetReportRoleMapService {

	@Resource
	private AssetReportRoleMapDao assetReportRoleMapDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(assetReportRoleMapDao);
    }
	@SuppressWarnings("rawtypes")
	public AssetReportRoleMapServiceImpl() {}
	@Override
	public int batchInsert(List<AssetReportRoleMap> cList) {
		// TODO Auto-generated method stub
		return assetReportRoleMapDao.batchInsert(cList);
	}
	
	
}