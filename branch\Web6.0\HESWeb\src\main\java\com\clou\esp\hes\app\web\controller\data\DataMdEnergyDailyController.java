/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataMeterDataEnergyDaily{ } 
 * 
 * 摘    要： dataMeterDataEnergyDaily
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-24 03:20:08
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.data;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;

import com.clou.esp.hes.app.web.model.data.DataMdEnergyDaily;
import com.clou.esp.hes.app.web.service.data.DataMdEnergyDailyService;

/**
 * <AUTHOR>
 * @时间：2017-11-24 03:20:08
 * @描述：dataMeterDataEnergyDaily类
 */
@Controller
@RequestMapping("/dataMdEnergyDailyController")
public class DataMdEnergyDailyController extends BaseController{

 	@Resource
    private DataMdEnergyDailyService dataMeterDataEnergyDailyService;

	/**
	 * 跳转到dataMeterDataEnergyDaily列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/data/dataMdEnergyDailyList");
    }

	/**
	 * 跳转到dataMeterDataEnergyDaily新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dataMeterDataEnergyDaily")
	public ModelAndView dataMeterDataEnergyDaily(DataMdEnergyDaily dataMeterDataEnergyDaily,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dataMeterDataEnergyDaily.getId())){
			try {
                dataMeterDataEnergyDaily=dataMeterDataEnergyDailyService.getEntity(dataMeterDataEnergyDaily.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dataMeterDataEnergyDaily", dataMeterDataEnergyDaily);
		}
		return new ModelAndView("/data/dataMeterDataEnergyDaily");
	}


	/**
	 * dataMeterDataEnergyDaily查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=dataMeterDataEnergyDailyService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除dataMeterDataEnergyDaily信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DataMdEnergyDaily dataMeterDataEnergyDaily,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dataMeterDataEnergyDailyService.deleteById(dataMeterDataEnergyDaily.getId())>0){
                j.setMsg("删除成功");
            }else{
                j.setSuccess(false);
                j.setMsg("删除失败");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
    
    /**
     * 保存dataMeterDataEnergyDaily信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DataMdEnergyDaily dataMeterDataEnergyDaily,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DataMdEnergyDaily t=new  DataMdEnergyDaily();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dataMeterDataEnergyDaily.getId())){
        	t=dataMeterDataEnergyDailyService.getEntity(dataMeterDataEnergyDaily.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dataMeterDataEnergyDaily, t);
				dataMeterDataEnergyDailyService.update(t);
				j.setMsg("修改成功");
				
			}else{
	            dataMeterDataEnergyDailyService.save(dataMeterDataEnergyDaily);
	            j.setMsg("创建成功");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
	
	
	
}