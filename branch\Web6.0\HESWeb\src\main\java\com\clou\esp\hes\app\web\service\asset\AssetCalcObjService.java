package com.clou.esp.hes.app.web.service.asset;

import java.util.List;

import com.clou.esp.hes.app.web.model.asset.AssetCalcObj;
import com.clou.esp.hes.app.web.model.asset.AssetCalcObjMap;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;
import com.clou.esp.hes.app.web.service.common.CommonService;


public interface AssetCalcObjService extends CommonService<AssetCalcObj> {
	List<AssetCalcObjMap> getCalObjMapList(AssetCalcObjMap map);
	
	//根据曲线id,查出DictDataitem列表
	List<DictDataitem> getDictDataitems(String measureId,String dateType);
}
