package com.clou.esp.hes.app.web.model.demo.req;

import java.io.Serializable;
import java.util.List;

import javax.jws.soap.SOAPBinding;
import javax.jws.soap.SOAPBinding.Style;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * 孟加拉接口项目请求数据头数据对象
 * 
 * <AUTHOR>
 * 
 */
@SOAPBinding(style = Style.RPC)
@XmlRootElement(name = "MeterData")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = { "mRID", "names" })
public class MeterData implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	public String mRID;
	
	public List<Names> names;

	public MeterData() {
		super();
	}

	public MeterData(String mRID, List<Names> names) {
		this.mRID = mRID;
		this.names = names;
	}

	public String getmRID() {
		return mRID;
	}

	public void setmRID(String mRID) {
		this.mRID = mRID;
	}

	public List<Names> getNames() {
		return names;
	}

	public void setNames(List<Names> names) {
		this.names = names;
	}

	@Override
	public String toString() {
		return "MeterData [mRID=" + mRID + ", names=" + names + "]";
	}

}
