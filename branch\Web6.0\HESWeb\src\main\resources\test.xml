<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
	<soap:Body>
		<ns2:MeterReadingsRequestMessage
			xmlns="http://www.iec.ch/TC57/2011/schema/message" xmlns:ns2="http://www.iec.ch/TC57/2011/MeterReadingsMessage"
			xmlns:ns3="http://iec.ch/TC57/2011/GetMeterReadings#" xmlns:ns4="http://iec.ch/TC57/2011/MeterReadings#">
			<ns2:Header>
				<Verb>get</Verb>
				<Noun>MeterReadings</Noun>
				<Timestamp>2017-12-29T11:46:23</Timestamp>
				<Source>Web</Source>
				<AsyncReplyFlag>true</AsyncReplyFlag>
				<ReplyAddress>http://localhost:8081/HESWeb/interfaces/RetornDemandReadsService?wsdl
				</ReplyAddress>
				<AckRequired>true</AckRequired>
				<User>
					<UserID>d6d0c31acd9a11e785b568f728c516f9</UserID>
					<Organization>yanlang</Organization>
				</User>
				<MessageID>sn1602408388566715d53-0f74-4870-a862-06d3a87aa9a0
				</MessageID>
			</ns2:Header>
			<ns2:Payload>
				<ns3:GetMeterReadings>
					<ns3:EndDevice>
						<ns3:mRID>sn16024083885</ns3:mRID>
					</ns3:EndDevice>
					<ns3:ReadingType>
						<ns3:Names>
							<ns3:name>********.********.0.0.0.0.*********.72.0</ns3:name>
							<ns3:NameType>
								<ns3:name>ReadingType</ns3:name>
							</ns3:NameType>
						</ns3:Names>
						<ns3:Names>
							<ns3:name>********.********.0.0.0.0.*********.73.0</ns3:name>
							<ns3:NameType>
								<ns3:name>ReadingType</ns3:name>
							</ns3:NameType>
						</ns3:Names>
						<ns3:Names>
							<ns3:name>********.*********.0.0.0.0.*********.72.0</ns3:name>
							<ns3:NameType>
								<ns3:name>ReadingType</ns3:name>
							</ns3:NameType>
						</ns3:Names>
						<ns3:Names>
							<ns3:name>********.13.1.12.0.0.0.0.0.*********.73.0</ns3:name>
							<ns3:NameType>
								<ns3:name>ReadingType</ns3:name>
							</ns3:NameType>
						</ns3:Names>
					</ns3:ReadingType>
				</ns3:GetMeterReadings>
			</ns2:Payload>
		</ns2:MeterReadingsRequestMessage>
	</soap:Body>
</soap:Envelope>