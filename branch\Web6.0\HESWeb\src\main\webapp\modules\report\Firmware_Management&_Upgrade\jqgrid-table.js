$(document).ready(function() {
	$.jgrid.defaults.styleUI = "Bootstrap";
	var mydata = [{
		id: "1",
		invdate: "sn16024083881",
		name: "<PERSON><PERSON>",
		note: "12/18/2017 00:00:00",
		amount: "CLK22",
		tax: "1.01",
		total: "1.02",
		totals: "12/26/2017 00:00:00",
	}, {
		id: "2",
		invdate: "sn16024083882",
		name: "<PERSON><PERSON>",
		note: "12/18/2017 00:00:00",
		amount: "CLK22",
		tax: "1.01",
		total: "1.02",
		totals: "12/26/2017 00:00:00",
	}, {
		id: "3",
		invdate: "sn16024083883",
		name: "<PERSON><PERSON>",
		note: "12/18/2017 00:00:00",
		amount: "CLK22",
		tax: "1.01",
		total: "1.02",
		totals: "12/26/2017 00:00:00",
	}, {
		id: "4",
		invdate: "sn16024083884",
		name: "<PERSON><PERSON>",
		note: "12/18/2017 00:00:00",
		amount: "CLK22",
		tax: "1.01",
		total: "1.02",
		totals: "12/26/2017 00:00:00",
	}, {
		id: "5",
		invdate: "sn16024083885",
		name: "Clou",
		note: "12/18/2017 00:00:00",
		amount: "CLK22",
		tax: "1.01",
		total: "1.02",
		totals: "12/26/2017 00:00:00",
	}, {
		id: "6",
		invdate: "sn16024083886",
		name: "Clou",
		note: "12/18/2017 00:00:00",
		amount: "CLK22",
		tax: "1.01",
		total: "1.02",
		totals: "12/26/2017 00:00:00",
	}, {
		id: "7",
		invdate: "sn16024083887",
		name: "Clou",
		note: "12/18/2017 00:00:00",
		amount: "CLK22",
		tax: "1.01",
		total: "1.02",
		totals: "12/26/2017 00:00:00",
	}, {
		id: "8",
		invdate: "sn16024083888",
		name: "Clou",
		note: "12/18/2017 00:00:00",
		amount: "CLK22",
		tax: "1.01",
		total: "1.02",
		totals: "12/26/2017 00:00:00",
	}, {
		id: "9",
		invdate: "sn16024083889",
		name: "Clou",
		note: "12/18/2017 00:00:00",
		amount: "CLK22",
		tax: "1.01",
		total: "1.02",
		totals: "12/26/2017 00:00:00",
	}, {
		id: "10",
		invdate: "sn16024083890",
		name: "Clou",
		note: "12/18/2017 00:00:00",
		amount: "CLK22",
		tax: "1.01",
		total: "1.02",
		totals: "12/26/2017 00:00:00",
	}, {
		id: "10",
		invdate: "sn16024083892",
		name: "Clou",
		note: "12/18/2017 00:00:00",
		amount: "CLK22",
		tax: "1.01",
		total: "1.02",
		totals: "12/26/2017 00:00:00",
	}, {
		id: "10",
		invdate: "sn16024083893",
		name: "Clou",
		note: "12/18/2017 00:00:00",
		amount: "CLK22",
		tax: "1.01",
		total: "1.02",
		totals: "12/26/2017 00:00:00",
	}, {
		id: "10",
		invdate: "sn16024083894",
		name: "Clou",
		note: "12/18/2017 00:00:00",
		amount: "CLK22",
		tax: "1.01",
		total: "1.02",
		totals: "12/26/2017 00:00:00",
	}, {
		id: "10",
		invdate: "sn16024083895",
		name: "Clou",
		note: "12/18/2017 00:00:00",
		amount: "CLK22",
		tax: "1.01",
		total: "1.02",
		totals: "12/26/2017 00:00:00",
	}, {
		id: "10",
		invdate: "sn16024083896",
		name: "Clou",
		note: "12/18/2017 00:00:00",
		amount: "CLK22",
		tax: "1.01",
		total: "1.02",
		totals: "12/26/2017 00:00:00",
	}, {
		id: "10",
		invdate: "sn16024083897",
		name: "Clou",
		note: "12/18/2017 00:00:00",
		amount: "CLK22",
		tax: "1.01",
		total: "1.02",
		totals: "12/26/2017 00:00:00",
	}, {
		id: "10",
		invdate: "sn16024083898",
		name: "Clou",
		note: "12/18/2017 00:00:00",
		amount: "CLK22",
		tax: "1.01",
		total: "1.02",
		totals: "12/26/2017 00:00:00",
	}, {
		id: "10",
		invdate: "sn16024083899",
		name: "Clou",
		note: "12/18/2017 00:00:00",
		amount: "CLK22",
		tax: "1.01",
		total: "1.02",
		totals: "12/26/2017 00:00:00",
	}, {
		id: "10",
		invdate: "sn16024083900",
		name: "Clou",
		note: "12/18/2017 00:00:00",
		amount: "CLK22",
		tax: "1.01",
		total: "1.02",
		totals: "12/26/2017 00:00:00",
	}, ];
	$("#table_list1").jqGrid({
		data: mydata,
		datatype: "local",
		autowidth: true,
		gridview: true,
		height: "334px",
		rowNum: 40,
		/* direction:"rtl",*/
		rownumbers: true,
		altRows: true, //单双行样式不同  
		altclass: 'differ',
		rowList: [10, 20, 40],
		colNames: ["", "Serial Number", "Manufacturer", "Model", "Current Version", "New Version", "Start Time ", "Expiry Time"],
		colModel: [{
				name: "id",
				index: "id",
				editable: true,
				width: 60,
				frozen: true,
				search: false,

				hidden: true,
				sorttype: "int"

			}, {
				name: "invdate",
				index: "invdate",
				editable: true,
				width: 120,

			}, {
				name: "name",
				index: "name",
				editable: true,
				width: 120,

			}, {
				name: "amount",
				index: "amount",
				editable: true,
				width: 120,

			}, {
				name: "tax",
				index: "tax",
				editable: true,
				width: 120,
				sorttype: "float"
			}, {
				name: "total",
				index: "total",
				editable: true,
				width: 120,
				sorttype: "float",
				stype: "select"
			}, {
				name: "note",
				index: "note",
				editable: true,

				width: 120,
				sorttype: "float",
			},
			{
				name: "totals",
				index: "totals",
				editable: false,
				search: false,
				width: 140,

				sorttype: "float",
			}
		],
		pager: "#pager_list1",
		viewrecords: true,
		caption: ' Results',
		multiselect: false,
		/*editurl: "/RowEditing",*/
		shrinkToFit: true,
		autoScroll: true
	});

	$("#table_list1").jqGrid("navGrid", "#pager_list1", {
			edit: false,
			add: false,
			del: false,
			search: false
		},

		{
			height: 200,
			reloadAfterSubmit: true
		});

	$(window).bind("resize", function() {
		var width = $(".jqGrid_wrapper").width();

		$("#table_list1").setGridWidth(width)
	});
	$(".ui-jqgrid-titlebar").append(" <div class=' titleBtnItem ' > <div class='btn_wrapper' title='Print'><div class= 'ui-title-btn'><span class='glyphicon glyphicon-print'></span></div></div> <div class='btn_wrapper' title='Export'><div class= 'ui-title-btn'><span class='glyphicon glyphicon-export'></span></div></div></div>");
});

