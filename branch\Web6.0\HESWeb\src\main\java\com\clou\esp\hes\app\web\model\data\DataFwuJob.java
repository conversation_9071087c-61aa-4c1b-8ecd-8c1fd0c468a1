/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataFwuJob{ } 
 * 
 * 摘    要： dataFwuJob
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-12-29 08:37:48
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.data;

import java.util.Date;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.clou.esp.hes.app.web.validation.ValidGroup2;
import com.clou.esp.hes.app.web.validation.ValidGroup3;
import com.power7000g.core.excel.Excel;

public class DataFwuJob extends BaseEntity {

	/**
	 * 意义，目的和功能，以及被用到的地方<br>
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DataFwuJob() {
	}

	/** deviceId */
	private java.lang.String deviceId;
	/**
	 * 1: meter 2: communicator
	 */
	@Excel(name = "Plan Type", width = 30, groups = { ValidGroup1.class,ValidGroup2.class,			ValidGroup3.class })
	private java.lang.String deviceType;
	/** 字符串 */
	private java.lang.String fwuStep;
	/** blockSize */
	private java.lang.Integer blockSize;
	/** blockCount */
	private java.lang.Integer blockCount;
	/** fwuProgress */
	private java.lang.Integer fwuProgress;
	private String planId;

	@Excel(name = "Serial Number", width = 30, groups = { ValidGroup1.class,ValidGroup2.class,			ValidGroup3.class })
	private String sn;
	@Excel(name = "Manufacturer", width = 30, groups = { ValidGroup1.class,			ValidGroup2.class, ValidGroup3.class })
	private String manufacturerName;
	@Excel(name = "Model", width = 30, groups = { ValidGroup1.class,			ValidGroup2.class, ValidGroup3.class })
	private String modelName;
	/** currentVesion */
	@Excel(name = "Current Version", width = 30, groups = { ValidGroup1.class,			ValidGroup2.class })
	private java.lang.String currentVesion;
	@Excel(name = "Old Version", width = 30, groups = ValidGroup3.class)	private java.lang.String oldVersion;
	/** newVersion */
	@Excel(name = "New Version", width = 30, groups = { ValidGroup1.class,			ValidGroup2.class, ValidGroup3.class })
	private java.lang.String newVersion;
	@Excel(name = "Start Time", width = 30, groups = { ValidGroup1.class,			ValidGroup3.class })
	private Date startTime;
	@Excel(name = "Expiry Time", width = 30, groups = { ValidGroup1.class,			ValidGroup3.class })
	private Date expiryTime;
	/** lastExecTime */
	@Excel(name = "Last Execute Time", width = 30, groups = {
			ValidGroup2.class, ValidGroup3.class })
	private java.util.Date lastExecTime;
	/**
	 * 1: Running - uci 2: Done -uci 3: Cancel - apps 4: Waiting - apps 5:
	 * Expired -uci
	 */
	@Excel(name = "Status", width = 30, groups = { ValidGroup2.class,
			ValidGroup3.class })
	private java.lang.String state;
	/** failedReason */
	@Excel(name = "Reason", width = 30, groups = { ValidGroup2.class,
			ValidGroup3.class })
	private java.lang.String failedReason;
	private String manufacturerId;
	private String model;
	
	/** 电表ID * */
	private String meterId;

	/**
	 * deviceId
	 * 
	 * @return the value of DATA_FWU_JOB.DEVICE_ID
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.lang.String getDeviceId() {
		return deviceId;
	}

	/**
	 * deviceId
	 * 
	 * @param deviceId
	 *            the value for DATA_FWU_JOB.DEVICE_ID
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setDeviceId(java.lang.String deviceId) {
		this.deviceId = deviceId;
	}

	/**
	 * 1: meter 2: communicator
	 * 
	 * @return the value of DATA_FWU_JOB.DEVICE_TYPE
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.lang.String getDeviceType() {
		return deviceType;
	}

	/**
	 * 1: meter 2: communicator
	 * 
	 * @param deviceType
	 *            the value for DATA_FWU_JOB.DEVICE_TYPE
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setDeviceType(java.lang.String deviceType) {
		this.deviceType = deviceType;
	}

	/**
	 * currentVesion
	 * 
	 * @return the value of DATA_FWU_JOB.CURRENT_VESION
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.lang.String getCurrentVesion() {
		return currentVesion;
	}

	/**
	 * currentVesion
	 * 
	 * @param currentVesion
	 *            the value for DATA_FWU_JOB.CURRENT_VESION
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setCurrentVesion(java.lang.String currentVesion) {
		this.currentVesion = currentVesion;
	}

	/**
	 * newVersion
	 * 
	 * @return the value of DATA_FWU_JOB.NEW_VERSION
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.lang.String getNewVersion() {
		return newVersion;
	}

	/**
	 * newVersion
	 * 
	 * @param newVersion
	 *            the value for DATA_FWU_JOB.NEW_VERSION
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setNewVersion(java.lang.String newVersion) {
		this.newVersion = newVersion;
	}

	/**
	 * 1: Running - uci 2: Done -uci 3: Cancel - apps 4: Waiting - apps 5:
	 * Expired -uci
	 * 
	 * @return the value of DATA_FWU_JOB.STATE
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.lang.String getState() {
		return state;
	}

	/**
	 * 1: Running - uci 2: Done -uci 3: Cancel - apps 4: Waiting - apps 5:
	 * Expired -uci
	 * 
	 * @param state
	 *            the value for DATA_FWU_JOB.STATE
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setState(java.lang.String state) {
		this.state = state;
	}

	/**
	 * lastExecTime
	 * 
	 * @return the value of DATA_FWU_JOB.LAST_EXEC_TIME
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.util.Date getLastExecTime() {
		return lastExecTime;
	}

	/**
	 * lastExecTime
	 * 
	 * @param lastExecTime
	 *            the value for DATA_FWU_JOB.LAST_EXEC_TIME
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setLastExecTime(java.util.Date lastExecTime) {
		this.lastExecTime = lastExecTime;
	}

	/**
	 * failedReason
	 * 
	 * @return the value of DATA_FWU_JOB.FAILED_REASON
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.lang.String getFailedReason() {
		return failedReason;
	}

	/**
	 * failedReason
	 * 
	 * @param failedReason
	 *            the value for DATA_FWU_JOB.FAILED_REASON
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setFailedReason(java.lang.String failedReason) {
		this.failedReason = failedReason;
	}

	/**
	 * 字符串
	 * 
	 * @return the value of DATA_FWU_JOB.FWU_STEP
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.lang.String getFwuStep() {
		return fwuStep;
	}

	/**
	 * 字符串
	 * 
	 * @param fwuStep
	 *            the value for DATA_FWU_JOB.FWU_STEP
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setFwuStep(java.lang.String fwuStep) {
		this.fwuStep = fwuStep;
	}

	/**
	 * blockSize
	 * 
	 * @return the value of DATA_FWU_JOB.BLOCK_SIZE
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.lang.Integer getBlockSize() {
		return blockSize;
	}

	/**
	 * blockSize
	 * 
	 * @param blockSize
	 *            the value for DATA_FWU_JOB.BLOCK_SIZE
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setBlockSize(java.lang.Integer blockSize) {
		this.blockSize = blockSize;
	}

	/**
	 * blockCount
	 * 
	 * @return the value of DATA_FWU_JOB.BLOCK_COUNT
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.lang.Integer getBlockCount() {
		return blockCount;
	}

	/**
	 * blockCount
	 * 
	 * @param blockCount
	 *            the value for DATA_FWU_JOB.BLOCK_COUNT
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setBlockCount(java.lang.Integer blockCount) {
		this.blockCount = blockCount;
	}

	/**
	 * fwuProgress
	 * 
	 * @return the value of DATA_FWU_JOB.FWU_PROGRESS
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public java.lang.Integer getFwuProgress() {
		return fwuProgress;
	}

	/**
	 * fwuProgress
	 * 
	 * @param fwuProgress
	 *            the value for DATA_FWU_JOB.FWU_PROGRESS
	 * @mbggenerated 2017-12-29 08:37:48
	 */
	public void setFwuProgress(java.lang.Integer fwuProgress) {
		this.fwuProgress = fwuProgress;
	}

	public String getPlanId() {
		return planId;
	}

	public void setPlanId(String planId) {
		this.planId = planId;
	}


	public DataFwuJob(String deviceId, String deviceType, String fwuStep, Integer blockSize, Integer blockCount,
			Integer fwuProgress, String planId, String sn, String manufacturerName, String modelName,
			String currentVesion, String oldVersion, String newVersion, Date startTime, Date expiryTime,
			Date lastExecTime, String state, String failedReason, String manufacturerId, String model, String meterId) {
		super();
		this.deviceId = deviceId;
		this.deviceType = deviceType;
		this.fwuStep = fwuStep;
		this.blockSize = blockSize;
		this.blockCount = blockCount;
		this.fwuProgress = fwuProgress;
		this.planId = planId;
		this.sn = sn;
		this.manufacturerName = manufacturerName;
		this.modelName = modelName;
		this.currentVesion = currentVesion;
		this.oldVersion = oldVersion;
		this.newVersion = newVersion;
		this.startTime = startTime;
		this.expiryTime = expiryTime;
		this.lastExecTime = lastExecTime;
		this.state = state;
		this.failedReason = failedReason;
		this.manufacturerId = manufacturerId;
		this.model = model;
		this.meterId = meterId;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getExpiryTime() {
		return expiryTime;
	}

	public void setExpiryTime(Date expiryTime) {
		this.expiryTime = expiryTime;
	}

	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}

	public String getManufacturerName() {
		return manufacturerName;
	}

	public void setManufacturerName(String manufacturerName) {
		this.manufacturerName = manufacturerName;
	}

	public String getModelName() {
		return modelName;
	}

	public void setModelName(String modelName) {
		this.modelName = modelName;
	}

	public java.lang.String getOldVersion() {
		return oldVersion;
	}

	public void setOldVersion(java.lang.String oldVersion) {
		this.oldVersion = oldVersion;
	}

	public String getManufacturerId() {
		return manufacturerId;
	}

	public void setManufacturerId(String manufacturerId) {
		this.manufacturerId = manufacturerId;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getMeterId() {
		return meterId;
	}

	public void setMeterId(String meterId) {
		this.meterId = meterId;
	}

}