package com.clou.esp.hes.app.web.model.data;

import java.util.Date;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.power7000g.core.excel.Excel;

/**
 * 
 * <AUTHOR>
 *
 */
public class DataIntegrationRequest extends BaseEntity {

	private static final long serialVersionUID = 1L;
	
	public DataIntegrationRequest() {
	}
	
	/**
	 * 序列号
	 */
	@Excel(name = "Serial Number", width = 30)
	private String sn;
	
	/**
	 * meterId
	 */
	private String id;
	
	/**
	 * 来源
	 */
	@Excel(name = "System", width = 30)
	private String fromId;
	
	/**
	 * 时标
	 */
	@Excel(name = "Request Time", width = 30)
	private Date tv;
	
	/**
	 * 请求类型
	 */
	@Excel(name = "Request Type", width = 30)
	private String requestType;
	
	/**
	 * 请求类型的数组，
	 * 用于传参查询
	 */
	private String[] requestTypeArrays;
	
	/**
	 * 开始时间
	 * 用于传参查询
	 */
	private Date startDate;
	
	/**
	 * 结束时间
	 * 用于传参查询
	 */
	private Date endDate;
	
	/**
	 * 请求子类型
	 */
	@Excel(name = "Request Sub Type", width = 30)
	private String requestSubType;
	
	/**
	 * 请求明细
	 */
//	@Excel(name = "Request Details", width = 60)
	private String requestDetail;
	
	/**
	 * 响应明细
	 */
//	@Excel(name = "Response Details", width = 60)
	private String responseDetail;
	
	@Excel(name = "Response Result", width = 60)
	private String responseResult;

	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Date getTv() {
		return tv;
	}

	public void setTv(Date tv) {
		this.tv = tv;
	}

	public String getFromId() {
		return fromId;
	}

	public void setFromId(String fromId) {
		this.fromId = fromId;
	}

	public String getRequestType() {
		return requestType;
	}

	public void setRequestType(String requestType) {
		this.requestType = requestType;
	}

	public String getRequestSubType() {
		return requestSubType;
	}

	public void setRequestSubType(String requestSubType) {
		this.requestSubType = requestSubType;
	}

	public String getRequestDetail() {
		return requestDetail;
	}

	public void setRequestDetail(String requestDetail) {
		this.requestDetail = requestDetail;
	}

	public String getResponseDetail() {
		return responseDetail;
	}

	public void setResponseDetail(String responseDetail) {
		this.responseDetail = responseDetail;
	}

	public String[] getRequestTypeArrays() {
		return requestTypeArrays;
	}

	public void setRequestTypeArrays(String[] requestTypeArrays) {
		this.requestTypeArrays = requestTypeArrays;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public String getResponseResult() {
		return responseResult;
	}

	public void setResponseResult(String responseResult) {
		this.responseResult = responseResult;
	}
}
