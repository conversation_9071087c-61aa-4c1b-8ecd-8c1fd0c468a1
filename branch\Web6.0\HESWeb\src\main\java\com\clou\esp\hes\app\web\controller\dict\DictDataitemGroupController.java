/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictDataitemGroup{ } 
 * 
 * 摘    要： 数据项分组
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.dict;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.dict.DictDataitemGroup;
import com.clou.esp.hes.app.web.model.dict.DictDataitemGroupMap;
import com.clou.esp.hes.app.web.model.dict.DictProtocol;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.dict.DictDataitemGroupMapService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemGroupService;
import com.clou.esp.hes.app.web.service.dict.DictProtocolService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;

/**
 * <AUTHOR>
 * @时间：2017-11-22 03:30:03
 * @描述：数据项分组类
 */
@Controller
@RequestMapping("/dictDataitemGroupController")
public class DictDataitemGroupController extends BaseController{

 	@Resource
    private DictDataitemGroupService 	dictDataitemGroupService;
 	@Resource
 	private DictDataitemGroupMapService dictDataitemGroupMapService;
 	@Resource
 	private DictProtocolService		 	dictProtocolService;

	/**
	 * 跳转到数据项分组列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/dict/dictDataitemGroupList");
    }

	/**
	 * 跳转到数据项分组新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dictDataitemGroup")
	public ModelAndView dictDataitemGroup(DictDataitemGroup dictDataitemGroup,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dictDataitemGroup.getId())){
            dictDataitemGroup=dictDataitemGroupService.getEntity(dictDataitemGroup.getId());
			
		}
		List<DictProtocol> procols=dictProtocolService.getAllList();
		String proReplace=RoletoJson.listToReplaceStr(procols, "id", "name", ",");
		model.addAttribute("protocolReplace",proReplace);
		model.addAttribute("dictDataitemGroup", dictDataitemGroup);
		return new ModelAndView("/dict/dictDataitemGroup");
	}


	/**
	 * 数据项分组查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request
    		,String id,String protocolId,String name,String appType,Boolean select) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        if(select==null||!select) {
        	return j;
        }
        try {
        	jqGridSearchTo.put("id", id);
        	jqGridSearchTo.put("name", name);
    		jqGridSearchTo.put("appType", appType);
    		jqGridSearchTo.put("protocolId", protocolId);
            j=dictDataitemGroupService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除数据项分组信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DictDataitemGroup dictDataitemGroup,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(StringUtil.isNotEmpty(dictDataitemGroup.getId())){
            	//如果有子项，不能删除
            	DictDataitemGroupMap map = new DictDataitemGroupMap();
            	map.setGroupId(dictDataitemGroup.getId());
            	List<DictDataitemGroupMap>  maps = this.dictDataitemGroupMapService.getList(map);
            	if(maps!=null&&maps.size()>0) {
            		j.setSuccess(false);
    	            j.setMsg(MutiLangUtil.doMutiLang("dictDataitemGroup.deleteItem"));
            	}else {
	            	this.dictDataitemGroupService.delete(dictDataitemGroup);
	            	j.setSuccess(true);
	            	j.setMsg(MutiLangUtil.doMutiLang("system.delSuccess"));
            	}
			}else{
				j.setSuccess(false);
	            j.setMsg(MutiLangUtil.doMutiLang("system.delFail"));
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
    
    /**
     * 保存数据项分组信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DictDataitemGroup dictDataitemGroup,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DictDataitemGroup t=new  DictDataitemGroup();
        try {
        	SysUser su=TokenManager.getToken();
			if(StringUtil.isNotEmpty(dictDataitemGroup.getOldId())){
				t.setId(dictDataitemGroup.getOldId());
	        	t=this.dictDataitemGroupService.get(t);
				MyBeanUtils.copyBeanNotNull2Bean(dictDataitemGroup, t);
				dictDataitemGroupService.update(t);
				j.setMsg(MutiLangUtil.doMutiLang("system.updateSucc"));
			}else{
				dictDataitemGroupService.save(dictDataitemGroup);
	            j.setMsg(MutiLangUtil.doMutiLang("system.addSucc"));
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
    
	
    /**
  	 * id、必须唯一
  	 */
  	@RequestMapping(value = "isExist")
  	@ResponseBody
  	public AjaxJson isExist(String id,String oldId,String name,HttpServletRequest request) {
  		AjaxJson j = new AjaxJson();
  		StringBuffer sb = new StringBuffer();
  		if(!id.equals(oldId)) {
  			DictDataitemGroup tmp = this.dictDataitemGroupService.getEntity(id);
  			if(tmp!=null) {
  				sb.append(MutiLangUtil.doMutiLang("dictDataitemGroup.idExist")+"<br/>");
  			}
  		}
  		//判断name
  		DictDataitemGroup entity = new DictDataitemGroup();
  		entity.setName(name);
  		List<DictDataitemGroup> list = this.dictDataitemGroupService.getList(entity);
  		if(list!=null&&list.size()>0) {
  			if(StringUtils.isNotEmpty(oldId)) {//编辑的时候存在,看是不是同一条
	  			entity = list.get(0);
	  			if(list.size()>1||!oldId.equals(entity.getId())) {
	  				sb.append(MutiLangUtil.doMutiLang("dictDataitemGroup.nameExist"));
	  			}
  			}else {
  				sb.append(MutiLangUtil.doMutiLang("dictDataitemGroup.nameExist"));
  			}
  		}
  		if(sb.length()>0) {  
  			j.setSuccess(false);
  			j.setMsg(sb.toString());
  		}
  		return j;
  	}
  	
}