package com.clou.esp.hes.app.web.model.asset;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class ProfileInterval extends BaseEntity{
	
	/**
	    * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	public ProfileInterval() {}
	
	private String profileInterval;      //时间间隔

	public String getProfileInterval() {
		return profileInterval;
	}

	public void setProfileInterval(String profileInterval) {
		this.profileInterval = profileInterval;
	}

	
	
}
