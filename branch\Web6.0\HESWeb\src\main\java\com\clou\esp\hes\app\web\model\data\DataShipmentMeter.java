/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataShipmentMeter{ } 
 * 
 * 摘    要： dataShipmentMeter
 * 版    本：1.0
 * 作    者：赵久霆
 * 创建于：2018-11-18 07:02:58
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.data;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DataShipmentMeter  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DataShipmentMeter() {
	}

	/**ldn*/
	private java.lang.String sn;
	/**ldn*/
	private java.lang.String ldn;
	/**stsSn*/
	private java.lang.String stsSn;
	/**password*/
	private java.lang.String password;
	/**hlsAk*/
	private java.lang.String hlsAk;
	/**hlsEk*/
	private java.lang.String hlsEk;
	/**isEncrypt*/
	private java.lang.String isEncrypt;
	/**authType*/
	private java.lang.String authType;

	public java.lang.String getSn() {
		return sn;
	}

	public void setSn(java.lang.String sn) {
		this.sn = sn;
	}

	/**
	 * ldn
	 * @return the value of DATA_SHIPMENT_METER.LDN
	 * @mbggenerated 2018-11-18 07:02:58
	 */
	public java.lang.String getLdn() {
		return ldn;
	}

	/**
	 * ldn
	 * @param ldn the value for DATA_SHIPMENT_METER.LDN
	 * @mbggenerated 2018-11-18 07:02:58
	 */
    	public void setLdn(java.lang.String ldn) {
		this.ldn = ldn;
	}
	/**
	 * stsSn
	 * @return the value of DATA_SHIPMENT_METER.STS_SN
	 * @mbggenerated 2018-11-18 07:02:58
	 */
	public java.lang.String getStsSn() {
		return stsSn;
	}

	/**
	 * stsSn
	 * @param stsSn the value for DATA_SHIPMENT_METER.STS_SN
	 * @mbggenerated 2018-11-18 07:02:58
	 */
    	public void setStsSn(java.lang.String stsSn) {
		this.stsSn = stsSn;
	}
	/**
	 * password
	 * @return the value of DATA_SHIPMENT_METER.PASSWORD
	 * @mbggenerated 2018-11-18 07:02:58
	 */
	public java.lang.String getPassword() {
		return password;
	}

	/**
	 * password
	 * @param password the value for DATA_SHIPMENT_METER.PASSWORD
	 * @mbggenerated 2018-11-18 07:02:58
	 */
    	public void setPassword(java.lang.String password) {
		this.password = password;
	}
	/**
	 * hlsAk
	 * @return the value of DATA_SHIPMENT_METER.HLS_AK
	 * @mbggenerated 2018-11-18 07:02:58
	 */
	public java.lang.String getHlsAk() {
		return hlsAk;
	}

	/**
	 * hlsAk
	 * @param hlsAk the value for DATA_SHIPMENT_METER.HLS_AK
	 * @mbggenerated 2018-11-18 07:02:58
	 */
    	public void setHlsAk(java.lang.String hlsAk) {
		this.hlsAk = hlsAk;
	}
	/**
	 * hlsEk
	 * @return the value of DATA_SHIPMENT_METER.HLS_EK
	 * @mbggenerated 2018-11-18 07:02:58
	 */
	public java.lang.String getHlsEk() {
		return hlsEk;
	}

	/**
	 * hlsEk
	 * @param hlsEk the value for DATA_SHIPMENT_METER.HLS_EK
	 * @mbggenerated 2018-11-18 07:02:58
	 */
    	public void setHlsEk(java.lang.String hlsEk) {
		this.hlsEk = hlsEk;
	}
	/**
	 * isEncrypt
	 * @return the value of DATA_SHIPMENT_METER.IS_ENCRYPT
	 * @mbggenerated 2018-11-18 07:02:58
	 */
	public java.lang.String getIsEncrypt() {
		return isEncrypt;
	}

	/**
	 * isEncrypt
	 * @param isEncrypt the value for DATA_SHIPMENT_METER.IS_ENCRYPT
	 * @mbggenerated 2018-11-18 07:02:58
	 */
    	public void setIsEncrypt(java.lang.String isEncrypt) {
		this.isEncrypt = isEncrypt;
	}
	/**
	 * authType
	 * @return the value of DATA_SHIPMENT_METER.AUTH_TYPE
	 * @mbggenerated 2018-11-18 07:02:58
	 */
	public java.lang.String getAuthType() {
		return authType;
	}

	/**
	 * authType
	 * @param authType the value for DATA_SHIPMENT_METER.AUTH_TYPE
	 * @mbggenerated 2018-11-18 07:02:58
	 */
    	public void setAuthType(java.lang.String authType) {
		this.authType = authType;
	}

	public DataShipmentMeter(java.lang.String ldn 
	,java.lang.String stsSn 
	,java.lang.String password 
	,java.lang.String hlsAk 
	,java.lang.String hlsEk 
	,java.lang.String isEncrypt 
	,java.lang.String authType ) {
		super();
		this.ldn = ldn;
		this.stsSn = stsSn;
		this.password = password;
		this.hlsAk = hlsAk;
		this.hlsEk = hlsEk;
		this.isEncrypt = isEncrypt;
		this.authType = authType;
	}

}