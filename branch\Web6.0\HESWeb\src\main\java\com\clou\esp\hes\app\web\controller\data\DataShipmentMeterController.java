/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataShipmentMeter{ } 
 * 
 * 摘    要： dataShipmentMeter
 * 版    本：1.0
 * 作    者：赵久霆
 * 创建于：2018-11-18 07:02:58
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.data;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.redis.JedisUtils;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.asset.ImportAssetGprsMeter;
import com.clou.esp.hes.app.web.model.data.DataShipmentMeter;
import com.clou.esp.hes.app.web.service.data.DataShipmentMeterService;

/**
 * <AUTHOR>
 * @时间：2018-11-18 07:02:58
 * @描述：dataShipmentMeter类
 */
@Controller
@RequestMapping("/dataShipmentMeterController")
public class DataShipmentMeterController extends BaseController{

 	@Resource
    private DataShipmentMeterService dataShipmentMeterService;

	/**
	 * 跳转到dataShipmentMeter列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/data/dataShipmentMeterList");
    }

	/**
	 * 跳转到dataShipmentMeter新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dataShipmentMeter")
	public ModelAndView dataShipmentMeter(DataShipmentMeter dataShipmentMeter,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dataShipmentMeter.getId())){
			try {
                dataShipmentMeter=dataShipmentMeterService.getEntity(dataShipmentMeter.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dataShipmentMeter", dataShipmentMeter);
		}
		return new ModelAndView("/data/dataShipmentMeter");
	}


	/**
	 * dataShipmentMeter查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=dataShipmentMeterService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除dataShipmentMeter信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DataShipmentMeter dataShipmentMeter,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dataShipmentMeterService.deleteById(dataShipmentMeter.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存dataShipmentMeter信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DataShipmentMeter dataShipmentMeter,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DataShipmentMeter t=new  DataShipmentMeter();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dataShipmentMeter.getId())){
        	t=dataShipmentMeterService.getEntity(dataShipmentMeter.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dataShipmentMeter, t);
				dataShipmentMeterService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            dataShipmentMeterService.save(dataShipmentMeter);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
	
	@Transactional
	@RequestMapping(value = "batchImportMeterData")
	@ResponseBody
	@SuppressWarnings("unchecked")
	public AjaxJson batchImportMeterData(String uuid, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			SysUser su = TokenManager.getToken();
			//批量导入
			List<ImportAssetGprsMeter> meterList = (List<ImportAssetGprsMeter>) JedisUtils.getObject(uuid);
			JedisUtils.set(su.getId() + "importProgress", "0", 3600);//先清空原来的进度
			if(meterList!=null&&meterList.size()>0) {
			 //为了防止SQL语句超出长度出错，分成几次插入
				int num=0;
				 if(meterList.size()<=2000){
				    dataShipmentMeterService.batchInsert(meterList);
				    JedisUtils.set(su.getId() + "importProgress", "100", 3600);
				 }else{
				     int times = (int)Math.ceil(meterList.size()/2000.0 );
				     for(int i=0; i<times; i++ ){
				         List<ImportAssetGprsMeter> tmpList= meterList.subList(i*2000, Math.min((i+1)*2000, meterList.size()));
				         dataShipmentMeterService.batchInsert(tmpList);
				         num+=tmpList.size();
				         JedisUtils.set(su.getId() + "importProgress", String.valueOf(num*100/(meterList.size())), 3600);
				      }
				 }
			}
		} catch (Exception e) {
			e.printStackTrace();
			j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
			return j;
		}
		j.setMsg(MutiLangUtil.doMutiLang("system.operSucce"));
		return j;
	}
	
}