CREATE TABLE ASSET_ENTITY_RELATIONSHIP
(	
  ID VARCHAR(32) NOT NULL, 
  TYPE DECIMAL(4, 0) NOT NULL, 
  PARENT_ID VARCHAR(32) NOT NULL, 
  PARENT_TYPE DECIMAL(4, 0) NOT NULL,
  primary key (ID,TYPE,PARENT_ID,PARENT_TYPE)
);

drop table DICT_VEE_EVENT;
CREATE TABLE DICT_VEE_EVENT
(	
	ID VARCHAR(32) NOT NULL, 
	NAME VARCHAR(128) NOT NULL, 
	USER_DEFINE DECIMAL(4,0) NOT NULL,
	METHOD VARCHAR(32) NOT NULL,
	DATAITEM_ID VARCHAR(64), 
	DESCR VARCHAR(256), 
	SORT_ID DECIMAL(4,0),
	primary key (ID)
);

CREATE TABLE DICT_VEE_EVENT_DATAITEM 
(	
	EVENT_ID VARCHAR(32) NOT NULL,  
	DATAITEM_ID VARCHAR(64) NOT NULL,  
	DAT<PERSON>ITEM_KEY VARCHAR(32) NOT NULL,  
	<PERSON><PERSON><PERSON> VARCHAR(256), 
	CYCLE_COUNT DECIMAL(4, 0), 
	CYCLE_TYPE VARCHAR(32),
	primary key (EVENT_ID,DATAITEM_ID,DATAITEM_KEY)
);

drop table DICT_VEE_EVENT_PARAM;
CREATE TABLE DICT_VEE_EVENT_PARAM 
(
	EVENT_ID VARCHAR(32) NOT NULL,   
	PARAM_KEY VARCHAR(64) NOT NULL,  
	PARAM_DESC VARCHAR(256), 
	DEFAUT_VALUE DECIMAL(4, 0),
	primary key (EVENT_ID,PARAM_KEY)
);

drop table DICT_VEE_METHOD;
CREATE TABLE DICT_VEE_METHOD 
(	
	ID VARCHAR(32) NOT NULL,    
	NAME VARCHAR(64), 
	TYPE DECIMAL(4, 0), 
	PACKAGE_ID VARCHAR(256),
	primary key (ID)
);

drop table ASSET_VEE_RULE;
CREATE TABLE ASSET_VEE_RULE 
(	
	ID VARCHAR(32) NOT NULL, 
	NAME VARCHAR(128) NOT NULL, 
	MG_ID VARCHAR(32) NOT NULL, 
	CLASS_ID DECIMAL(4, 0), 
	RULE_DETAIL VARCHAR(256), 
	EVENT_ID VARCHAR(32), 
	RULE_TYPE DECIMAL(4, 0) DEFAULT NULL, 
	RULE_STATUS DECIMAL(4, 0),
	primary key (ID,MG_ID)
);
 
 CREATE TABLE ASSET_VEE_RULE_DATAITEM 
(	
	RULE_ID VARCHAR(32) NOT NULL, 
	DATAITEM_KEY VARCHAR(32) NOT NULL,
	CYCLE_COUNT DECIMAL(4, 0) NOT NULL, 
	CYCLE_TYPE VARCHAR(32),
	primary key (RULE_ID,DATAITEM_KEY)
);

CREATE TABLE ASSET_VEE_RULE_PARAM 
(	
	RULE_ID VARCHAR(32) NOT NULL, 
	PARAM_KEY VARCHAR(32) NOT NULL, 
	PARAM_VALUE VARCHAR(32),
	primary key (RULE_ID,PARAM_KEY)
);   
   
truncate table DICT_FUNCTION;
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1026','DCU Configuration - CSG','dcuConfigurationController/toDcuConfiguration300.do','Provisioning');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1001','Meter Data Report','meterDataReportController/list.do','Data Collection');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1002','Meter Event Report','dataMeterEventController/list.do','Data Collection');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1003','Schedule Reads Report','dataIntegrityController/scheduleReadsReport.do','Data Collection');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1004','Miss Data Tracing','dataIntegrityController/list.do','Data Collection');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1005','Collection Schedule Management','assetScheduleSchemeDetailController/list.do','Data Collection');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1006','Meter Group Management','assetMeterGroupController/meterGroupMgmt.do','Provisioning');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1007','Meter Configuration','meterConfigurationController/toMeterConfiguration.do','Provisioning');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1008','DCU Configuration - SG376','dcuConfigurationController/toDcuConfiguration.do','Provisioning');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1009','Firmware Upgrade','dataFwuPlanController/list.do','Tools');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1011','On Demand Reads','assetMeterController/onDemandReads.do','Tools');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1012','Connect / Disconnect','meterConnectOrDisconnectController/toConnectOrDisconnect.do','Tools');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1013','Asset Management','assetMeterController/assetManagementList.do','Provisioning');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1014','Deployment Management','sysServerController/list.do','System');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1015','Log Explorer','sysLogController/list.do','System');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1017','Permission','sysUserController/sysUserAndRoleList.do','System');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1018','Data Export Management','sysDataitemExportController/list.do','System Integration');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1019','Reports','dictReportController/list.do','Data Management');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1020','Meter Group Upgrade','dataParameterPlanController/meterGroupUpgradeList.do','Tools');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1022','Calculation Object Define','assetCalationObjectController/assetCalculationObject.do','Data Management');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1024','DCU Configuration','dcuConfigurationController/toDcuDlmsConfiguration.do','Provisioning');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1000','Home','systemController/home.do','Home');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1025','VEE Management','dictReportController/veeReport.do','VEE Management');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1023','Communication Status Report','dataComminicationStatusController/list.do','Data Collection');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1027','Line Loss Management',null,'Grid Loss Management');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('2000','Search',null,'Search');

truncate table DICT_OPERATION;
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1005002','Add/Edit scheme','1005','/assetScheduleSchemeController/assetScheduleScheme.do',1,'Add/Edit scheme');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006005','Add TOU group','1006','/assetMeterGroupController/assetMeterGroup.do?type=2',6,'Add TOU group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006006','Add Measurement group','1006','/assetMeterGroupController/toMeasurementGroup.do?type=1',2,'Add Measurement group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006007','Add Limiter group','1006','/assetMeterGroupController/assetMeterGroup.do?type=3',10,'Add Limiter group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1014002','Add/Edit server/service','1014','/sysServerController/toAddSysServer.do',1,'Add/Edit server or service');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1014003','Delete server/service','1014','/sysServerController/delServer.do',2,'Delete server or service');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006008','Delete TOU group','1006','deleteTouGroup',7,'Delete TOU group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006009','Delete Limiter group','1006','deleteLimiterGroup',11,'Delete Limiter group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006010','Delete Measurement group','1006','deleteMeasurementGroup',3,'Delete Measurement group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1009001','Add Plan','1009','/dataFwuPlanController/saveFWUPlan.do',2,'Add plan in Plan Tab');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1018002','Save Data & Event','1018','/sysDataitemExportController/saveMeterDataEventExport.do',1,'Save meter data and events to be exported');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017005','Add/Edit user','1017','/sysUserController/toAddSysUser.do',2,'Add user; Edit user; Reset password');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1005003','Delete scheme','1005','/assetScheduleSchemeController/del.do',2,'Delete scheme');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1005004','Add/Edit task','1005','/assetScheduleSchemeDetailController/assetScheduleSchemeDetail.do',3,'Add/Edit task');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1005005','Delete task','1005','/assetScheduleSchemeDetailController/del.do',4,'Delete task');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013202','Edit & Save Meter','1013','/assetMeterController/save.do',4,'Edit & Save Meter');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013203','Delete Meter','1013','/assetMeterController/del.do',3,'Delete Meter');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013204','Add Meter','1013','/assetMeterController/addAssetDevice.do',2,'Add Meter');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1007004','Excute Task in Set Page','1007','/meterConfigurationController/excuteTask.do',4,'Add & execute task in Set Tab');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1007005','Read data in Get Tab','1007','/meterConfigurationController/getTouGroupData.do',2,'Read data in Get Tab');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017007','Add/Edit role','1017','/sysRoleController/toAddSysRole.do',5,'Add/Edit role');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017008','Delete role','1017','/sysRoleController/deleteRole.do',6,'Delete role');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017006','Delete user','1017','/sysUserController/del.do',3,'Delete user');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017009','Add/Edit/Remove organization','1017','/sysOrgController/toAddSysOrg.do',8,'Add, edit or remove organization');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017010','Delete organization','1017','/sysOrgController/del.do',9,'Delete organization');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1009002','Cancel Plan/Job','1009','/dataFwuPlanController/cancel.do',4,'Cancel plan/job in Plan Report Tab; Cancel job in Job Report Tab');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1019001','Add Plan','1020','/dataParameterPlanController/saveMeterGroupUpgradePlan.do',2,'Add plan in Plan/Plan Report Tab');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1019002','Cancel Plan','1020','/dataParameterPlanController/cancelPlan.do',4,'Cancel plan in Plan Report Tab');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006011','Delete Step Tariff group','1006','deleteStepTariffGroup',15,'Delete Step Tariff group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006012','Add Step Tariff group','1006','/assetMeterGroupController/addStepTariffGroup.do?type=4',14,'Add Step Tariff group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013235','Add Calculation Object to Line','1013','AddCalculationObjectToLine',13,'Add Calculation Object to Line');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013255','Add Calculation Object to Transformer','1013','AddCalculationObjectToTransformer',18,'Add Calculation Object to Transformer');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013243','Add Calculation Object to Organization','1013','AddCalculationObjectToOrg',21,'Add Calculation Object to Organization');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1022001','Add Calculation Object','1022','AddCalculationObject',1,'Add Calculation Object');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1022002','Edit Calculation Object','1022','EditCalculationObject',3,'Edit Calculation Object');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1022003','Delete Calculation Object','1022','DeleteCalculationObject',2,'Delete Calculation Object');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1022004','Add Data Channnel in Calculation Object','1022','AddDataChannelInCalcObj',4,'Add Data Channnel in Calculation Object');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1022005','Edit Data Channnel in Calculation Object','1022','EditDataChannelInCalcObj',6,'Edit Data Channnel in Calculation Object');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1022006','Delete Data Channnel in Calculation Object','1022','DelDataChannelInCalcObj',5,'Delete Data Channnel in Calculation Object');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1000001','View Data Collection Home Page','1000','ViewDataCollectionHomePage',1,'View Data Collection Home Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1000002','View Supply & Sales Home Page','1000','ViewSupplySalesHomePage',2,'View Supply & Sales Home Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013201','Meter Tab Page','1013','ViewMeterManagementPage',1,'View Meter Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013211','Communicator Tab Page','1013','ViewCommunicatorManagementPage',5,'View Communicator Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013231','Line Tab Page','1013','ViewLineManagementPage',9,'View Line Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013251','Transformer Tab Page','1013','ViewTransformerManagementPage',14,'View Transformer Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013241','Organization Tab Page','1013','ViewOrganizationManagementPage',19,'View Organization Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013212','Edit & Save Commnicator','1013','EditAndSaveCommunicator',8,'Edit & Save Commnicator');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013213','Delete Commnicator','1013','DeleteCommunicator',7,'Delete Commnicator');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013214','Add Commnicator','1013','AddCommunicator',6,'Add Commnicator');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013232','Edit & Save Line','1013','EditAndSaveLine',12,'Edit & Save Line');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013233','Delete Line','1013','DeleteLine',11,'Delete Line');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013234','Add Line','1013','AddLine',10,'Add Line');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013252','Edit & Save Transformer','1013','EditAndSaveTransformer',17,'Edit & Save Transformer');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013253','Delete Transformer','1013','DeleteTransformer',16,'Delete Transformer');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013254','Add Transformer','1013','AddTransformer',15,'Add Transformer');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013242','Edit & Save Organization','1013','EditAndSaveOrganization',20,'Edit & Save Organization');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006001','TOU Tab Page','1006','ViewTouManagementPage',5,'View TOU Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006002','Measurement Tab Page','1006','ViewMeasurementManagementPage',1,'View Measurement Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006003','Limiter Tab Page','1006','ViewLimiterManagementPage',13,'View Limiter Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006004','Step Tariff Tab Page','1006','ViewStepTariffManagementPage',4,'View Step Tariff Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006013','Edit & Save TOU group','1006','EditAndSaveTouGroup',8,'Edit & Save TOU group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006014','Edit & Save Measurement group','1006','EditAndSaveMeasurementGroup',4,'Edit & Save Measurement group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006015','Edit & Save Limiter group','1006','EditAndSaveLimiterGroup',12,'Edit & Save Limiter group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1006016','Edit & Save Step Tariff group','1006','EditSaveStepTariffGroup',16,'Edit & Save Step Tariff group');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1007001','View Set Tab Page','1007','ViewSetTabPage',3,'View Set Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1007002','View Get Tab Page','1007','ViewGetTabPage',1,'View Get Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1009101','View Plan Tab Page','1009','FirmViewPlanTabPage',1,'View Plan Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1009102','View Plan Report Tab Page','1009','FirmViewPlanReportTabPage',3,'View Plan Report Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1009103','View Job Report Tab Page','1009','FirmViewJobReportTabPage',5,'View Job Report Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1020101','View Plan Tab Page','1020','ViewPlanTabPage',1,'View Plan Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1020102','View Plan Report Tab Page','1020','ViewPlanReportTabPage',3,'View Plan Report Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1020103','View Job Report Tab Page','1020','ViewJobReportTabPage',5,'View Job Report Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1012001','Connect','1012','ExecuteConnectCommandToMeter',1,'Execute a Connect Command to Meter');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1012002','Disconnect','1012','ExecuteDisConnectCommandToMeter',2,'Execute a Disconnect Command to Meter');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017001','View User tab page','1017','ViewUserTabPage',1,'View User tab page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017002','View Role tab page','1017','ViewRoleTabPage',4,'View Role tab page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1017003','View Organization tab page','1017','ViewOrganizationTabPage',7,'View Organization tab page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1015001','View Metering Tracing Log tab page','1015','ViewMeteringTracingLogTabPage',1,'View Metering Tracing Log tab page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1015002','View Time Synchronization Log tab page','1015','ViewTimeSynchronizationLogTabPage',2,'View Time Synchronization Log tab page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1015003','View System Integration Log tab page','1015','ViewSystemIntegrationLogTabPage',3,'View System Integration Log tab page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1015004','View User Log tab page','1015','ViewUserLogTabPage',4,'View User Log tab page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013221','Customer Tab Page','1013','ViewCustomerTabPage',22,'View Customer Management Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013222','Edit & Save Customer','1013','EditSaveCustomer',25,'Edit & Save Customer');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013223','Delete Customer','1013','DeleteCustomer',24,'Delete Customer');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1013224','Add Customer','1013','AddCustomer',23,'Add Customer');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1003001','Overview Tab Page','1003','OverviewTabPage',1,'Overview Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1003002','Manufacturer Tab Page','1003','ManufacturerTabPage',2,'Manufacturer Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1003003','Model Tab Page','1003','ModelTabPage',3,'Model Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1003004','Communication Tab Page','1003','CommunicationTabPage',4,'Communication Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1003005','Organization Tab Page','1003','OrganizationTabPage',5,'Organization Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1008001','Meter List Parameter Tab Page','1008','MeterListParameterTabPage',1,'Meter List Parameter Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1008003','Set Meter List Parameter','1008','SetMeterListParameter',2,'Set Meter List Parameter');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1008002','Other Tab Page','1008','OtherTabPage',3,'Other Tab Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1008004','Set Other Parameter','1008','SettherParameter',4,'Set Other Parameter');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('2000001','View Calculation Object Tree','2000','xxxxxx',1,'View Calculation Object Tree');

