<?xml version="1.0" encoding="UTF-8"?><ureport><cell expand="None" name="A1" row="1" col="1"><cell-style font-size="10" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Tariff Bracket]]></simple-value></cell><cell expand="None" name="B1" row="1" col="2"><cell-style font-size="10" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[0-50]]></simple-value></cell><cell expand="None" name="C1" row="1" col="3"><cell-style font-size="10" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[51-150]]></simple-value></cell><cell expand="None" name="D1" row="1" col="4"><cell-style font-size="10" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[151-300]]></simple-value></cell><cell expand="None" name="E1" row="1" col="5"><cell-style font-size="10" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[301-600]]></simple-value></cell><cell expand="None" name="F1" row="1" col="6"><cell-style font-size="10" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[600 and above]]></simple-value></cell><cell expand="None" name="G1" row="1" col="7"><cell-style font-size="10" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Total]]></simple-value></cell><cell expand="None" name="H1" row="1" col="8"><cell-style font-size="10" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Total No. of customer]]></simple-value></cell><cell expand="Down" name="A2" row="2" col="1"><cell-style font-size="9" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="group" property="traiffBracket" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="B2" row="2" col="2"><cell-style font-size="9" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="group" property="level1" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="C2" row="2" col="3"><cell-style font-size="9" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="group" property="level2" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="D2" row="2" col="4"><cell-style font-size="9" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="group" property="level3" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="E2" row="2" col="5"><cell-style font-size="9" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="group" property="level4" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="F2" row="2" col="6"><cell-style font-size="9" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="group" property="level5" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="G2" row="2" col="7"><cell-style font-size="9" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="group" property="total" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="H2" row="2" col="8"><cell-style font-size="9" font-family="Times New Roman" format="#" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="group" property="customerTotal" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="A3" row="3" col="1"><cell-style font-size="9" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Total]]></simple-value></cell><cell expand="None" name="B3" row="3" col="2" top-cell="B1"><cell-style font-size="9" font-family="Times New Roman" format="#.##" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><expression-value><![CDATA[sum(B2)]]></expression-value></cell><cell expand="None" name="C3" row="3" col="3" top-cell="C1"><cell-style font-size="9" font-family="Times New Roman" format="#.##" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><expression-value><![CDATA[sum(C2)]]></expression-value></cell><cell expand="None" name="D3" row="3" col="4" top-cell="D1"><cell-style font-size="9" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><expression-value><![CDATA[sum(D2)]]></expression-value></cell><cell expand="None" name="E3" row="3" col="5" top-cell="E1"><cell-style font-size="9" font-family="Times New Roman" format="#.##" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><expression-value><![CDATA[sum(E2)]]></expression-value></cell><cell expand="None" name="F3" row="3" col="6" top-cell="F1"><cell-style font-size="9" font-family="Times New Roman" format="#.##" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><expression-value><![CDATA[sum(F2)]]></expression-value></cell><cell expand="None" name="G3" row="3" col="7" top-cell="G1"><cell-style font-size="9" font-family="Times New Roman" format="#.##" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><expression-value><![CDATA[sum(G2)]]></expression-value></cell><cell expand="None" name="H3" row="3" col="8" top-cell="H1"><cell-style font-size="9" font-family="Times New Roman" format="#" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><expression-value><![CDATA[sum(H2)]]></expression-value></cell><cell expand="None" name="A4" row="4" col="1"><cell-style font-size="9" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[No. of Accts]]></simple-value></cell><cell expand="None" name="B4" row="4" col="2" top-cell="B1"><cell-style font-size="9" font-family="Times New Roman" format="#" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="sum" property="level1CustCount" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="C4" row="4" col="3" top-cell="C1"><cell-style font-size="9" font-family="Times New Roman" format="#" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="sum" property="level2CustCount" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="D4" row="4" col="4" top-cell="D1"><cell-style font-size="9" font-family="Times New Roman" format="#" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="sum" property="level2CustCount" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="E4" row="4" col="5" top-cell="E1"><cell-style font-size="9" font-family="Times New Roman" format="#" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="sum" property="level4CustCount" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="F4" row="4" col="6" top-cell="F1"><cell-style font-size="9" font-family="Times New Roman" format="#" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="sum" property="level5CustCount" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="G4" row="4" col="7" top-cell="G1"><cell-style font-size="9" font-family="Times New Roman" format="#" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="sum" property="customerTotal" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="H4" row="4" col="8"><cell-style font-size="9" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[]]></simple-value></cell><row row-number="1" height="18" band="title"/><row row-number="2" height="18"/><row row-number="3" height="18"/><row row-number="4" height="19"/><column col-number="1" width="80"/><column col-number="2" width="80"/><column col-number="3" width="80"/><column col-number="4" width="74"/><column col-number="5" width="80"/><column col-number="6" width="74"/><column col-number="7" width="74"/><column col-number="8" width="74"/><datasource name="monthlySalesSummaryDataSource" type="spring" bean="monthlySalesSummaryDataSource"><dataset name="report" type="bean" method="loadUnitReportData" clazz="com.clou.esp.ppm.app.web.ureport.dataset.MonthlySalesSummaryDataSet"><field name="customerTotal"/><field name="level1"/><field name="level1CustCount"/><field name="level2"/><field name="level2CustCount"/><field name="level3"/><field name="level3CustCount"/><field name="level4"/><field name="level4CustCount"/><field name="level5"/><field name="level5CustCount"/><field name="total"/><field name="traiffBracket"/></dataset></datasource><paper type="A4" left-margin="90" right-margin="90"
    top-margin="72" bottom-margin="72" paging-mode="fitpage" fixrows="0"
    width="595" height="842" orientation="portrait" html-report-align="left" bg-image="" html-interval-refresh-value="0" column-enabled="false"></paper></ureport>