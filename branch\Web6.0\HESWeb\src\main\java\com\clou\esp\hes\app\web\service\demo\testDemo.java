package com.clou.esp.hes.app.web.service.demo;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.power7000g.core.util.base.DateUtils;

public class testDemo {
   public static void main(String[] args) throws ParseException{
	   
	 String uciUrl = ResourceUtil.getSessionattachmenttitle("local.time.formatter");
	 System.out.println(uciUrl);
	 System.out.println(DateUtils.date2Str(new Date(), new SimpleDateFormat("dd-MM-yyyy HH:mm:ss")))  ;
	 
	 System.out.println(DateUtils.formatAddDate(DateUtils.formatDate(new Date(), "MM/dd/yyyy"), "MM/dd/yyyy", 1));
	 System.out.println(DateUtils.parseDate(DateUtils.formatAddDate(DateUtils.formatDate(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd", 1)+" 00:00:00"
			 , "yyyy-MM-dd HH:mm:ss"));
	 
	 System.out.println(DateUtils.date2Str(DateUtils.parseDate(DateUtils.formatAddDate(DateUtils.formatDate(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd", 1)+" 00:00:00"
			 , "MM-dd-yyyy HH:mm:ss"), new SimpleDateFormat("MM-dd-yyyy HH:mm:ss")));
	 
	 
	 System.out.println((DateUtils.parseDate("1923/12/23 12:10:59", "yyyy/MM/dd HH:mm:ss")))   ;
	 System.out.println(DateUtils.date2Str((DateUtils.parseDate("1923/12/23 12:10:59", "yyyy/MM/dd HH:mm:ss")),new SimpleDateFormat("dd-MM-yyyy HH:mm:ss")))   ;
		String[]strp ="26-01-2022 00:00:00,3".split("[- :,]");
	 String[] str = new String[strp.length];
	 str =strp;
	str[0] =strp[0];
	str[2] =strp[1];
	str[1] =strp[2];
	str[3] =strp[3];
	str[4] =strp[4];
	str[5] =strp[5];

	
	System.out.println(str);
   }
}
