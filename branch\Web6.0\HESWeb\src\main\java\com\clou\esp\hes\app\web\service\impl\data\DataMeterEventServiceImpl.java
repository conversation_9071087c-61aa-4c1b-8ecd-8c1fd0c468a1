/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataMeterEvent{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-24 01:56:05
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.data;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.data.DataMeterEventDao;
import com.clou.esp.hes.app.web.model.data.DataMeterEvent;
import com.clou.esp.hes.app.web.service.data.DataMeterEventService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@Component
@Service("dataMeterEventService")
public class DataMeterEventServiceImpl extends
		CommonServiceImpl<DataMeterEvent> implements DataMeterEventService {

	@Resource
	private DataMeterEventDao dataMeterEventDao;

	@Autowired
	public void setCommonService() {
		super.setCommonService(dataMeterEventDao);
	}

	public DataMeterEventServiceImpl() {
	}

	@Override
	public int batchSaves(String sql, String osql) {
		return dataMeterEventDao.batchInsert(sql, osql);
	}

	@Override
	public int batchInsert(List<DataMeterEvent> eList) {
		return dataMeterEventDao.batchSave(eList);
	}

	@Override
	public JqGridResponseTo getForJqGrid(JqGridSearchTo jqGridSearchTo) {
	PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
	List<DataMeterEvent> list = dataMeterEventDao.getForJqGrid(jqGridSearchTo);
	PageInfo<DataMeterEvent> pageInfo = new PageInfo<DataMeterEvent>(list);
	pageInfo.setList(pageInfo.getList());
	return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}

	@Override
	public Long getCountByOrgIds(Map<String, Object> params) {
		return dataMeterEventDao.getCountByOrgIds(params);
	}

	@Override
	public List<DataMeterEvent> getTodayEventByDeviceId(String deviceId, String protocolId) {
		return this.dataMeterEventDao.getTodayEventByDeviceId(deviceId, protocolId);
	}

	@Override
	public List<String> getTodayEventByDeviceIds(List<String> deviceIds) {
		return this.dataMeterEventDao.getTodayEventByDeviceIds(deviceIds);
	}
}