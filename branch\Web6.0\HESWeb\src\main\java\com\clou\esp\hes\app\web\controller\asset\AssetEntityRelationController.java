package com.clou.esp.hes.app.web.controller.asset;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.asset.AssetEntityRelationship;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.asset.AssetEntityRelationshipService;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.google.common.collect.Lists;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;

@Controller
@RequestMapping("/assetEntityRelationController")
public class AssetEntityRelationController {

	@Resource
	private AssetEntityRelationshipService assetEntityRelationshipService;
	
	@Resource
	private DataUserLogService dataUserLogService;
	
	@RequestMapping(value = "findEntityRelationForJqGrid")
    @ResponseBody
	public JqGridResponseTo findEntityRelationForJqGrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request,String parentId,Integer type
			,boolean select) {
		JqGridResponseTo response = null;
		if(!select) {
			response = new JqGridResponseTo();
			return response;
		}
		
		if(null == parentId || "".equals(parentId)) {
			response = new JqGridResponseTo();
			return response;
		}
		
		if(null == type || "".equals(type)) {
			response = new JqGridResponseTo();
			return response;
		}
		
		jqGridSearchTo.put("parentId", "".equals(parentId) ? null : parentId);
		jqGridSearchTo.put("type", "".equals(type) ? null : type);
		
		response = assetEntityRelationshipService.findLinkedEntityMeterList(jqGridSearchTo);
		
		return response;
	}
	
	@RequestMapping(value = "findTransformerRelationForJqGrid")
    @ResponseBody
	public JqGridResponseTo findTransformerRelationForJqGrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request,String parentId,Integer type
			,boolean select) {
		JqGridResponseTo response = null;
		if(!select) {
			response = new JqGridResponseTo();
			return response;
		}
		
		if(null == parentId || "".equals(parentId)) {
			response = new JqGridResponseTo();
			return response;
		}
		
		if(null == type || "".equals(type)) {
			response = new JqGridResponseTo();
			return response;
		}
		
		jqGridSearchTo.put("parentId", "".equals(parentId) ? null : parentId);
		jqGridSearchTo.put("type", "".equals(type) ? null : type);
		
		response = assetEntityRelationshipService.findLinkedEntityTransformerList(jqGridSearchTo);
		
		return response;
	}
	
	@RequestMapping(value = "findUnLinkedMeterListForJqGrid")
    @ResponseBody
	public JqGridResponseTo findUnLinkedMeterListForJqGrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request,
			String communicatorSN,String communicatorName,String sn,String transformerName,Integer parentType,String orgId,
			String parentId,boolean select) {
		JqGridResponseTo response = null;
		if(!select) {
			response = new JqGridResponseTo();
			return response;
		}
		
		if(null == parentType) {
			response = new JqGridResponseTo();
			return response;
		}
		
		jqGridSearchTo.put("communicatorSN", "".equals(communicatorSN) ? null : communicatorSN);
		jqGridSearchTo.put("communicatorName", "".equals(communicatorName) ? null : communicatorName);
		jqGridSearchTo.put("sn", "".equals(sn) ? null : sn);
		jqGridSearchTo.put("transformerName", "".equals(transformerName) ? null : transformerName);
		jqGridSearchTo.put("parentType", parentType);
		jqGridSearchTo.put("parentId", parentId);
		List<String> orgIds = Lists.newArrayList();
		orgIds.add(orgId);
		jqGridSearchTo.put("orgIds", orgIds);
		response = assetEntityRelationshipService.findUnLinkedMeterList(jqGridSearchTo);
		
		return response;
	}
	
	@RequestMapping(value = "findUnLinkedTransformerListForJqGrid")
    @ResponseBody
	public JqGridResponseTo findUnLinkedTransformerListForJqGrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request,
			String sn,String transformerName,Integer parentType,String orgId
			,boolean select,String parentId) {
		JqGridResponseTo response = null;
		if(!select) {
			response = new JqGridResponseTo();
			return response;
		}
		if(null == parentType) {
			response = new JqGridResponseTo();
			return response;
		}
		
		jqGridSearchTo.put("sn", "".equals(sn) ? null : sn);
		jqGridSearchTo.put("transformerName", "".equals(transformerName) ? null : transformerName);
		jqGridSearchTo.put("parentType", parentType);
		jqGridSearchTo.put("parentId", parentId);
		List<String> orgIds = Lists.newArrayList();
		orgIds.add(orgId);
		jqGridSearchTo.put("orgIds", orgIds);
		
		response = assetEntityRelationshipService.findUnLinkedTransformerList(jqGridSearchTo);
		
		return response;
		
	}
	
	
	/**
	 * 新增关系
	 * @param request
	 * @param relation
	 * @param ids
	 * @return
	 */
	@Transactional
	@RequestMapping(value = "saveEntityRelationship",method=RequestMethod.POST)
	@ResponseBody
	public AjaxJson saveEntityRelationship( HttpServletRequest request,AssetEntityRelationship relation,String ids) {
		AjaxJson j = new AjaxJson();
		SysUser su=TokenManager.getToken();
		
		try {
			String parentId = relation.getParentId();
			Integer parentType = relation.getParentType();
			Integer type = relation.getType();
			
			// 批量操作，获取用户勾选的子实体
			if (null == ids || "".equals(ids.trim())) {
				j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
			}
			
			if (null == parentId || "".equals(parentId.trim())) {
				j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
			}
			
			if (null == parentType) {
				j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
			}
			
			if (null == type ) {
				j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
			}
			// 构造需要插入的记录实体
			List<AssetEntityRelationship> relationships = new ArrayList<AssetEntityRelationship>();
			String[] idList = ids.split(",");
			for (int i = 0; i<idList.length; i++) {
				AssetEntityRelationship entity = new AssetEntityRelationship();
				entity.setId(idList[i]);
				entity.setType(type);
				entity.setParentId(parentId);
				entity.setParentType(parentType);
				relationships.add(entity);
				dataUserLogService.insertDataUserLog(su.getId(), "Asset Manage", "Add Linked Meter for Line","Add Linked Meter for Line(ID="+entity.getId()+",EntityType="+ entity.getType() + ",ParentId=" + 
						entity.getParentId() +",ParentType" +  entity.getParentType()+")");
			}
			assetEntityRelationshipService.batchInsert(relationships);
			j.setMsg(MutiLangUtil.doMutiLang("system.addSucc"));
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
			return j;
		}
		return j;
	}
	
	/**
	 * 跳转到新增实体关系弹出窗口
	 * @param request
	 * @param model
	 * @param id
	 * @return
	 */
	@RequestMapping(value  = "assetEntityRelationship")
    public ModelAndView assetEntityRelationship(HttpServletRequest request, Model model,Integer parentType,String orgId,String parentId) {
		model.addAttribute("parentId", parentId);
		model.addAttribute("parentType", parentType);
		model.addAttribute("orgId", orgId);
		if (3 == parentType) {
			model.addAttribute("isLineToMeter",true);
		}
		return new ModelAndView("/asset/assetEntityRelationship");
	}
	
	/**
	 * 跳转到新增变压器关系弹出窗口
	 * @param request
	 * @param model
	 * @param id
	 * @return
	 */
	@RequestMapping(value  = "assetTransformerRelationship")
    public ModelAndView assetTransformerRelationship(HttpServletRequest request, Model model,Integer parentType,String orgId,String parentId) {
		model.addAttribute("parentType", parentType);
		model.addAttribute("parentId", parentId);
		model.addAttribute("orgId", orgId);
		return new ModelAndView("/asset/assetTransformerRelationship");
	}
	
	@Transactional
	@RequestMapping(value = "delMeterRelationship")
    @ResponseBody
    public AjaxJson delMeterRelationship(AssetEntityRelationship relationship, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		SysUser su=TokenManager.getToken();
		
        try {
        	// 电表
        	relationship.setType(1);
        	
        	assetEntityRelationshipService.deleteRelationByEntity(relationship);
        	// 操作日志
 			dataUserLogService.insertDataUserLog(su.getId(), "Asset Manage", "Delete Linked Meter for Line","Delete Linked Meter for Line(ID="+relationship.getId()+",EntityType=1,EntityName=Meter)");
        } catch (Exception e) {
        	e.printStackTrace();
        	j.setSuccess(false);
        	j.setMsg(MutiLangUtil.doMutiLang("system.delFail"));
        }
        j.setMsg(MutiLangUtil.doMutiLang("system.delSuccess"));
        return j;
	}
	
	@Transactional
	@RequestMapping(value = "delTransformerRelationship")
    @ResponseBody
    public AjaxJson delTransformerRelationship(AssetEntityRelationship relationship, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		SysUser su=TokenManager.getToken();
		
        try {
        	// 变压器
        	relationship.setType(4);
        	
        	assetEntityRelationshipService.deleteRelationByEntity(relationship);
        	// 操作日志
 			dataUserLogService.insertDataUserLog(su.getId(), "Asset Manage", "Delete Linked Transformer for Line","Delete Linked Transformer for Line(ID="+relationship.getId()+",EntityType=4,EntityName=Transformer)");
        } catch (Exception e) {
        	e.printStackTrace();
        	j.setSuccess(false);
        	j.setMsg(MutiLangUtil.doMutiLang("system.delFail"));
        }
        j.setMsg(MutiLangUtil.doMutiLang("system.delSuccess"));
        return j;
	}
}
