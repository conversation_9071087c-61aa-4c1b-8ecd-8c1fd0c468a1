/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysServer{ } 
 * 
 * 摘    要： sysServer
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-03-29 03:12:41
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.system;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.json.ValidForm;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.tag.vo.JqTreeUtil;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.system.SysServer;
import com.clou.esp.hes.app.web.model.system.SysService;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.dict.DictServiceAttributeService;
import com.clou.esp.hes.app.web.service.system.SysServerService;
import com.clou.esp.hes.app.web.service.system.SysServiceService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.util.oConvertUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;

/**
 * <AUTHOR>
 * @时间：2018-03-29 03:12:41
 * @描述：sysServer类
 */
@Controller
@RequestMapping("/sysServerController")
public class SysServerController extends BaseController{

 	@Resource
    private SysServerService sysServerService;
 	@Resource
    private SysServiceService sysServiceService;
 	@Resource
    private DictServiceAttributeService dictServiceAttributeService;
 	
 	@Resource
    private DataUserLogService dataUserLogService;

	/**
	 * 跳转到sysServer列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
		List<SysServer> list = sysServerService.getAllList();
		String sysServerList = RoletoJson.listToReplaceStr(list, "id", "introduction", ";");
		model.addAttribute("sysServerList", sysServerList);
        return new ModelAndView("/system/deploymentAndCluster/sysDeploymentAndCluster");
    }

	/**
	 * 跳转到sysServer新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "toAddSysServer")
	public ModelAndView toAddSysServer(SysServer sysServer, HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(sysServer.getId())){
			try {
                sysServer = sysServerService.getEntity(sysServer.getId());
            }
            catch (Exception e) {
                e.printStackTrace();
            }
			model.addAttribute("sysServer", sysServer);
		}
		return new ModelAndView("/system/deploymentAndCluster/addServer");
	}


	/**
	 * sysServer查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo, HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j = null;
        SysServer sysServer = new SysServer();
        SysService sysService = new SysService();
        try {
        	List<SysServer> sysServerlist = sysServerService.getList(sysServer);
        	List<Map<String, Object>> list = new ArrayList<>();
        	//父级server
    		for (int i = 0; i < sysServerlist.size(); i++) {
    			Map<String, Object> mapParent = new HashMap<String, Object>();
    			mapParent.put("id", sysServerlist.get(i).getId());
    			mapParent.put("introduction", sysServerlist.get(i).getIntroduction());
    			mapParent.put("ip", sysServerlist.get(i).getIp());
    			mapParent.put("serviceType", "0");		//0：server
    			mapParent.put("serviceTypeTemp", "0");
    			mapParent.put("isOnline", sysServerlist.get(i).getIsOnline());
    			mapParent.put("parent", "");
    			mapParent.put("isLeaf", false);
    			mapParent.put("expanded", true);
    			//子级service
    			List<Map<String, Object>> listk = new ArrayList<>();
    			sysService.setServerId(sysServerlist.get(i).getId());
    			List<SysService> sysServicelist = sysServiceService.getListByEntity(sysService);
    			if(sysServicelist.size() > 0){
    				for (int jk = 0; jk < sysServicelist.size(); jk++) {
    					Map<String, Object> mapChild = new HashMap<String, Object>();
    					mapChild.put("id", sysServicelist.get(jk).getId());
    					mapChild.put("introduction", sysServicelist.get(jk).getIntroduction());
    					mapChild.put("ip", sysServicelist.get(jk).getHostId());
    					mapChild.put("serviceType", sysServicelist.get(jk).getServiceType());
    					mapChild.put("serviceTypeTemp", sysServicelist.get(jk).getServiceType());
    					mapChild.put("isOnline", sysServicelist.get(jk).getIsOnline());
    					mapChild.put("parent", sysServicelist.get(jk).getServerId());
    					mapChild.put("isLeaf", true);
    					mapChild.put("expanded", false);
    					listk.add(mapChild);
    				}
    			}
    			mapParent.put("list", listk);
    			list.add(mapParent);
    		}
    		PageInfo<Map<String, Object>> pageInfo = new PageInfo<Map<String, Object>>();
    		pageInfo.setList(list);
             j= JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
    		JqTreeUtil jtu = new JqTreeUtil();
    		jtu.setFields(jqGridSearchTo.getField());
    		jtu.setIdFieldName("id");
    		jtu.setParentFieldName("parent");
    		jtu.setSubsetFieldName("list");
    		jtu.setExpandedFieldName("expanded");
    		j.setRows(jtu.getTreeGridData(list));
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除sysServer信息
     * @param id
     * @return
     */
    @RequestMapping(value = "delServer")
    @ResponseBody
    public AjaxJson delServer(SysServer sysServer, HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        try {
            SysUser su = TokenManager.getToken();
            SysService service = new SysService();
            service.setServerId(sysServer.getId());
            List<SysService> serviceList = sysServiceService.getList(service);
            if(serviceList.size() > 0){
            	j.setSuccess(false);
                j.setMsg(MutiLangUtil.doMutiLang("deploymentAndClusterManagement.unableDel_server"));
                return j;
            }
            SysServer entity = sysServerService.getEntity(sysServer.getId());
            if(sysServerService.deleteById(sysServer.getId()) > 0){
            	//添加操作日志
	            dataUserLogService.insertDataUserLog(su.getId(), 
	            		"Deployment & Cluster Mgmt", "Delete Server", "Delete Server (Name="+ entity.getIntroduction() +")");
                j.setMsg(MutiLangUtil.doMutiLang("system.delSuccess"));
            }else{
                j.setSuccess(false);
                j.setMsg(MutiLangUtil.doMutiLang("system.delFail"));
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
    
    /**
     * 保存sysServer信息
     * @param id
     * @return
     */
    @RequestMapping(value = "saveSysServer")
    @ResponseBody
    public AjaxJson saveSysServer(@Validated(value = { ValidGroup1.class })SysServer sysServer, BindingResult bindingResult, HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        SysServer t = new  SysServer();
        try {
        	SysUser su = TokenManager.getToken();
	        if(StringUtil.isNotEmpty(sysServer.getId())){
	        	//修改service数据
	        	SysService sevice = sysServiceService.getEntity(sysServer.getId());
	        	if(StringUtil.isNotEmpty(sevice)){
	        		SysService seviceReplace = new SysService();
	        		seviceReplace.setHostId(sysServer.getIp());
	        		seviceReplace.setIntroduction(sysServer.getIntroduction());
	        		MyBeanUtils.copyBeanNotNull2Bean(seviceReplace, sevice);
	        		sysServiceService.update(sevice);
	        		//添加操作日志
		            dataUserLogService.insertDataUserLog(su.getId(), 
		            		"Deployment & Cluster Mgmt", "Edit Service", "Edit Service (Name="+ sevice.getIntroduction() +")");
	        	}else{	//修改server数据
	        		t = sysServerService.getEntity(sysServer.getId());
	        		MyBeanUtils.copyBeanNotNull2Bean(sysServer, t);
	        		sysServerService.update(t);
	        		//添加操作日志
		            dataUserLogService.insertDataUserLog(su.getId(), 
		            		"Deployment & Cluster Mgmt", "Edit Server", "Edit Server (Name="+ t.getIntroduction() +")");
	        	}
				j.setMsg(MutiLangUtil.doMutiLang("system.updateSucc"));
			}else{
				/**
	        	 * 新增校验：IP地址不能重复
	        	 */
	        	SysServer temp = new SysServer();
	    		temp.setIp(sysServer.getIp());
	    		List<SysServer> listTemp = sysServerService.getList(temp);
	    		if (listTemp.size() > 0) {
	    			j.setSuccess(false);
	                j.setMsg(MutiLangUtil.doMutiLang("deploymentAndClusterManagement.ipExistSystem"));
	                return j;
				}
				sysServer.setHaState("1");
				sysServer.setIsOnline("1");
	            sysServerService.save(sysServer);
	            //添加操作日志
	            dataUserLogService.insertDataUserLog(su.getId(), 
	            		"Deployment & Cluster Mgmt", "Add Server", "Add Server (Name="+ sysServer.getIntroduction() +")");
	            j.setMsg(MutiLangUtil.doMutiLang("system.addSucc"));
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
	
    /**
     * 校验server/service name唯一性
     * @param id5
     * @return
     */
    @RequestMapping(value = "checServerOrServiceAccount")
    @ResponseBody
    public ValidForm checkUserAccount(HttpServletRequest request) {
    	ValidForm v = new ValidForm();
		String name = oConvertUtils.getString(request.getParameter("param"));
		List<SysServer> serverList = sysServerService.vaildServerName(name);
		List<SysService> serviceList = sysServiceService.vaildServiceName(name);
		if (serverList.size() > 0 || serviceList.size() >0) {
			v.setInfo(MutiLangUtil.doMutiLang("deploymentAndClusterManagement.nameExist"));
			v.setStatus("n");
		}
		return v;
    }
	
}