package clouesp.hes.core.uci.soap.custom.asset_refresh;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

/**
 * This class was generated by Apache CXF 3.1.14
 * 2018-04-08T09:52:33.897+08:00
 * Generated source version: 3.1.14
 * 
 */
@WebService(targetNamespace = "http://asset_refresh.custom.soap.uci.core.hes.clouesp/", name = "AssetRefreshPort")
@XmlSeeAlso({ObjectFactory.class})
public interface AssetRefreshPort {

    @WebResult(name = "return", targetNamespace = "")
    @RequestWrapper(localName = "refresh", targetNamespace = "http://asset_refresh.custom.soap.uci.core.hes.clouesp/", className = "clouesp.hes.core.uci.soap.custom.asset_refresh.Refresh")
    @WebMethod
    @ResponseWrapper(localName = "refreshResponse", targetNamespace = "http://asset_refresh.custom.soap.uci.core.hes.clouesp/", className = "clouesp.hes.core.uci.soap.custom.asset_refresh.RefreshResponse")
    public boolean refresh(
        @WebParam(name = "msgs", targetNamespace = "")
        java.util.List<clouesp.hes.core.uci.soap.custom.asset_refresh.RefreshMessage> msgs
    );
}
