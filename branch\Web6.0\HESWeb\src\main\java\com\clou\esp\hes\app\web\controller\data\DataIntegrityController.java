/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataIntegrity{ } 
 * 
 * 摘    要： dataIntegrity
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-12-01 06:03:45
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.data;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.clou.esp.hes.app.web.model.system.SysOrg;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.CreatePdf;
import com.clou.esp.hes.app.web.core.util.DateTimeFormatterUtil;
import com.clou.esp.hes.app.web.core.util.ExcelUtils;
import com.clou.esp.hes.app.web.core.util.Globals;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.OrganizationUtils;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.core.util.UciInterfaceUtil;
import com.clou.esp.hes.app.web.model.asset.AssetCommunicator;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.data.DataIntegrity;
import com.clou.esp.hes.app.web.model.dict.DictCommunicationType;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;
import com.clou.esp.hes.app.web.model.dict.DictDataitemGroup;
import com.clou.esp.hes.app.web.model.dict.DictDeviceModel;
import com.clou.esp.hes.app.web.model.dict.DictManufacturer;
import com.clou.esp.hes.app.web.model.dict.DictProfile;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.asset.AssetCommunicatorService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.data.DataIntegrityService;
import com.clou.esp.hes.app.web.service.dict.DictCommunicationTypeService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemGroupService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemService;
import com.clou.esp.hes.app.web.service.dict.DictDeviceModelService;
import com.clou.esp.hes.app.web.service.dict.DictManufacturerService;
import com.clou.esp.hes.app.web.service.dict.DictProfileService;
import com.clou.esp.hes.app.web.service.impl.system.SysServiceAttributeServiceImpl;
import com.clou.esp.hes.app.web.service.system.SysOrgService;
import com.clou.esp.hes.app.web.service.system.SysServiceAttributeService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.google.common.collect.Lists;
import com.power7000g.core.excel.ExcelDataFormatter;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.format.FormatUtil;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;

import ch.iec.tc57._2011.meterreadings.GetMeterReadingsPort;
import clouesp.hes.core.uci.soap.custom.manualCalculation.DispatchMessage;
import clouesp.hes.core.uci.soap.custom.manualCalculation.DispatchTaskPort;

/**
 * <AUTHOR>
 * @时间：2017-12-01 06:03:45
 * @描述：dataIntegrity类
 */
@Controller
@RequestMapping("/dataIntegrityController")
public class DataIntegrityController extends BaseController {
	
	final static String TIME_FLAG =ResourceUtil.getSessionattachmenttitle("local.time.flag");
	
	@Resource
	private DataIntegrityService dataIntegrityService;
	@Resource
	private DictManufacturerService dictManufacturerService;
	@Resource
	private DictDeviceModelService dictDeviceModelService;
	@Resource
	private DictCommunicationTypeService dictCommunicationTypeService;
	
	@Resource
	private AssetMeterService assetMeterService;
	@Resource
	private SysOrgService sysOrgService;
 	@Resource
    private DictProfileService dictProfileService;
 	@Resource
    private DictDataitemGroupService dictDataitemGroupService;
 	@Resource
    private DictDataitemService dictDataitemService;
	@Resource
	private AssetCommunicatorService assetCommunicatorService;
	@Resource
	private SysServiceAttributeService sysServiceAttributeService;
	
	public  static final String   profileId=ResourceUtil.getSessionattachmenttitle("profileId");

	/**
	 * 
	 * @param tv
	 * @param startTv
	 * @param endTv
	 * @param type
	 * @param name
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "getDailyNameIntegrity")
	@ResponseBody
	public AjaxJson getDailyNameIntegrity(String tv, String startTv,
										  String endTv, String type, String name, String chartType, String defaultOrgId, String profileId) {
		AjaxJson j = new AjaxJson();
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("profileId", profileId);
		if (type != null && type.equals("month")) {
			SimpleDateFormat sdf = new SimpleDateFormat(" MMMM, yyyy",
					Locale.ENGLISH);
			params.put("tvType", 2);// day//month
			if (StringUtil.isNotEmpty(tv)) {
				params.put("tv", DateUtils.date2Str(
						DateUtils.str2Date(tv, sdf), DateUtils.month_sdf));
			}
			if (StringUtil.isNotEmpty(startTv) && StringUtil.isNotEmpty(endTv)) {
				params.put("startTv", DateUtils.date2Str(
						DateUtils.str2Date(startTv, sdf), DateUtils.month_sdf));
				params.put("endTv", DateUtils.date2Str(
						DateUtils.str2Date(endTv, sdf), DateUtils.month_sdf));


				int compareTo = DateUtils.str2Date(startTv, sdf).compareTo(DateUtils.str2Date(endTv, sdf));
				if (compareTo >= 0) {
					j.setSuccess(false);
					j.setMsg(MutiLangUtil.doMutiLang("stepTariffList.startLaterEnd"));
					return j;
				}
			}
		} else {
			params.put("tvType", 1);// day//month
			if (StringUtil.isNotEmpty(tv)) {
				params.put("tv", DateUtils.date2Str(
						DateUtils.str2Date(tv, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)),
						DateUtils.date_sdf));
			}
			if (StringUtil.isNotEmpty(startTv) && StringUtil.isNotEmpty(endTv)) {
				params.put("startTv", DateUtils.date2Str(
						DateUtils.str2Date(startTv, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)),
						DateUtils.date_sdf));
				params.put("endTv", DateUtils.date2Str(
						DateUtils.str2Date(endTv, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)),
						DateUtils.date_sdf));

				int compareTo = DateUtils.str2Date(startTv, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)).compareTo(DateUtils.str2Date(endTv, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
				if (compareTo >= 0) {
					j.setSuccess(false);
					j.setMsg(MutiLangUtil.doMutiLang("stepTariffList.startLaterEnd"));
					return j;
				}
			}
		}
		// name=manufacturer;model;commType;organization
		if (StringUtil.isNotEmpty(name)) {
			if (name.equals("manufacturer")) {
				params.put("idType", "2");
			} else if (name.equals("model")) {
				params.put("idType", "3");
			} else if (name.equals("commType")) {
				params.put("idType", "4");
			} else if (name.equals("organization")) {
				params.put("idType", "5");
				params.put("idTypeOrg", 21);
			}
		}
		if (StringUtil.isNotEmpty(chartType)) {
			params.put("chartType", chartType);
		}

		SysUser su = TokenManager.getToken();
		if (StringUtils.isEmpty(defaultOrgId)) {
			defaultOrgId = su.getOrgId().split(",")[0];
		}

		if (name.equals("organization")) {
			List<String> orgIdList = OrganizationUtils.assembleOrgIds(defaultOrgId, sysOrgService, true, 1);
			params.put("orgIdList", orgIdList);
		} else {
			List<String> orgIdDataIntegrityList = new ArrayList<String>();
			orgIdDataIntegrityList.add(defaultOrgId);
			params.put("orgIdList", orgIdDataIntegrityList);
		}

		List<Map<String, Object>> diList = dataIntegrityService
				.getDataIntegrityByMaps(params);
		List<String> idList = new ArrayList<String>();
		List<String> nameList = new ArrayList<String>();
		List<Double> integrityList = new ArrayList<Double>();
		List<String> tvList = new ArrayList<String>();
		List<Map<String, Object>> attrList = new ArrayList<Map<String, Object>>();

		Map<String, Object> attrs = new HashMap<String, Object>();
		SimpleDateFormat MMyyyy = new SimpleDateFormat("MM-yyyy");
		//	SimpleDateFormat MMdd = new SimpleDateFormat("MM/dd");
		if (diList != null && diList.size() > 0) {
			for (Map<String, Object> di : diList) {
				BigDecimal integrity = (BigDecimal) di.get("INTEGRITY");
				integrityList.add(integrity.setScale(2, BigDecimal.ROUND_DOWN)
						.doubleValue());
				String id = (String) di.get("ID");
				idList.add(id);
				String n = (String) di.get("NAME");
				nameList.add(n);
				String time = "";
				if (type != null && type.equals("month")) {
					time = DateUtils.date2Str((Date) di.get("TV"), MMyyyy);
				} else {
					time = DateUtils.date2Str((Date) di.get("TV"), DateTimeFormatterUtil.getSimpleDateFormat_DDMM(TIME_FLAG));

				}
				if (!attrs.containsKey(time)) {
					tvList.add(time);
				}
				attrs.put(time, time);
				if (attrs.containsKey(n)) {
					Map<String, Object> attr = (Map<String, Object>) attrs
							.get(n);
//					List<Double> iList = (List<Double>) attr.get("iList");
//					iList.add(integrity.setScale(2, BigDecimal.ROUND_DOWN)
//							.doubleValue());

					List<Map<String, Double>> mList = (List<Map<String, Double>>) attr.get("mList");
					Map<String, Double> mListMap = new HashMap<>();
					mListMap.put(time, integrity.setScale(2, BigDecimal.ROUND_DOWN)
							.doubleValue());
					mList.add(mListMap);

				} else {
					Map<String, Object> attr = new HashMap<String, Object>();
//					List<Double> iList = new ArrayList<Double>();
//					iList.add(integrity.setScale(2, BigDecimal.ROUND_DOWN)
//							.doubleValue());

					List<Map<String, Double>> mList = new ArrayList<>();
					Map<String, Double> mListMap = new HashMap();
					mListMap.put(time, integrity.setScale(2, BigDecimal.ROUND_DOWN)
							.doubleValue());
					mList.add(mListMap);

					attr.put("name", n);
					//	attr.put("iList", iList);
					attr.put("mList", mList);
					attr.put("id", id);
					attrs.put(n, attr);
					attrList.add(attr);
				}
			}
		}
		//多机构不缺计算 begin
		for (Map<String, Object> mapFlag : attrList) {
			List<Map<String, Double>> mList = (List) mapFlag.get("mList");
			List<Double> iList = new ArrayList<Double>();
			Map<String, Double> outMap = new HashMap<String, Double>();
			for (Map<String, Double> mMap : mList) {
				for (String tvFlag : tvList) {
					if (mMap.containsKey(tvFlag)) {
						outMap.put(tvFlag, mMap.get(tvFlag));
					} else {
						if (outMap.size() > 0 && outMap.get(tvFlag) != null && outMap.get(tvFlag) != 0d) {
						} else {
							outMap.put(tvFlag, 0d);
						}
					}
				}
			}

			for (String tvFlag : tvList) {
				iList.add(outMap.get(tvFlag));
			}
			mapFlag.put("iList", iList);
		}
		//end

		Map<String, Object> attributes = new HashMap<String, Object>();
		attributes.put("idList", idList);
		attributes.put("nameList", nameList);
		attributes.put("integrityList", integrityList);
		attributes.put("tvList", tvList);
		attributes.put("attrs", attrList);
		j.setAttributes(attributes);

		return j;
	}

	/**
	 * 获取区间范围内日抄读率
	 * 
	 * @param startDay
	 * @param endDay
	 * @return
	 */
	@RequestMapping(value = "getDailyMeterReadsIntegrity")
	@ResponseBody
	public AjaxJson getDailyMeterReadsIntegrity(String startTv, String endTv,
												String type, String defaultOrgId, String profileId) {
		AjaxJson j = new AjaxJson();
		Map<String, Object> params = new HashMap<String, Object>();

		params.put("profileId", profileId);

		if (type != null && type.equals("month")) {
			SimpleDateFormat sdf = new SimpleDateFormat(" MMMM, yyyy",
					Locale.ENGLISH);
			params.put("tvType", 2);// day//month
			params.put("startTv", DateUtils.date2Str(
					DateUtils.str2Date(startTv, sdf), DateUtils.month_sdf));
			params.put("endTv", DateUtils.date2Str(
					DateUtils.str2Date(endTv, sdf), DateUtils.month_sdf));

			int compareTo = DateUtils.str2Date(startTv, sdf).compareTo(DateUtils.str2Date(endTv, sdf));
			if (compareTo >= 0) {
				j.setSuccess(false);
				j.setMsg(MutiLangUtil.doMutiLang("stepTariffList.startLaterEnd"));
				return j;
			}
		} else {
			params.put("tvType", 1);// day//month
			params.put("startTv", DateUtils.date2Str(
					DateUtils.str2Date(startTv, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)),
					DateUtils.date_sdf));
			params.put("endTv", DateUtils.date2Str(
					DateUtils.str2Date(endTv, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)),
					DateUtils.date_sdf));

			int compareTo = DateUtils.str2Date(startTv, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)).compareTo(DateUtils.str2Date(endTv, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
			if (compareTo >= 0) {
				j.setSuccess(false);
				j.setMsg(MutiLangUtil.doMutiLang("stepTariffList.startLaterEnd"));
				return j;
			}
		}
		params.put("idType", "5");

		SysUser su = TokenManager.getToken();

		if ((StringUtils.isEmpty(defaultOrgId))) {
			defaultOrgId = su.getOrgId().split(",")[0];
		}

		List<String> orgIdDataIntegrityList = new ArrayList<String>();
		orgIdDataIntegrityList.add(defaultOrgId);
		params.put("orgIdList", orgIdDataIntegrityList);

		List<Map<String, Object>> diList = dataIntegrityService
				.getDataIntegrityByMaps(params);
		//1
		List<String> tvList = new ArrayList<String>();
		List<Double> integrityList = new ArrayList<Double>();
		SimpleDateFormat MMyyyy = new SimpleDateFormat("MM-yyyy");
//		SimpleDateFormat MMdd = new SimpleDateFormat("MM/dd");
		if (diList != null && diList.size() > 0) {
			for (Map<String, Object> di : diList) {
				BigDecimal integrity = new BigDecimal(0);
				Date tv = (Date) di.get("TV");

				integrity = (BigDecimal) di.get("INTEGRITY");


				integrityList.add(integrity.setScale(2, BigDecimal.ROUND_DOWN)
						.doubleValue());
				if (type != null && type.equals("month")) {
					tvList.add(DateUtils.date2Str(tv, MMyyyy));
				} else {
					tvList.add(DateUtils.date2Str(tv, DateTimeFormatterUtil.getSimpleDateFormat_DDMM(TIME_FLAG)));
				}
			}
		}

		Map<String, Object> attributes = new HashMap<String, Object>();
		attributes.put("tvList", tvList);
		attributes.put("integrityList", integrityList);
		j.setAttributes(attributes);
		return j;
	}

	/**
	 * 抄读进度报表(首页展示数据报表)
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "scheduleReadsReport")
	public ModelAndView scheduleReadsReport(HttpServletRequest request,
											Model model, String profileId) {

		String profileCycleTypeListStr = ResourceUtil.getSessionattachmenttitle("profile.cycle.type.list");

		List<String> profileCycleListDispaly = null;
		if(profileCycleTypeListStr == null || "".equals(profileCycleTypeListStr)) {
			profileCycleListDispaly = new ArrayList<>();
			profileCycleListDispaly.add("Total");
			profileCycleListDispaly.add("Minutely");
			profileCycleListDispaly.add("Daily");
			profileCycleListDispaly.add("Monthly");
		} else {
			String[] splits = profileCycleTypeListStr.split(",");
			profileCycleListDispaly = new ArrayList<>(Arrays.asList(splits));
		}

		if(profileId == null || "".equals(profileId)) {
			profileId = profileCycleListDispaly.get(0);
		}
		request.setAttribute("profileCycleTypeDefault", profileId);
		request.setAttribute("profileCycleListDispaly", profileCycleListDispaly);


		//获取昨日日期字符串
		Date d = new Date();
		d = FormatUtil.addDaysToDate(d, -1);
		String tv = DateUtils.date2Str(d, DateUtils.date_sdf);
		Map<String, Object> params = new HashMap<String, Object>();
		Map<String, Object> result = new HashMap<String, Object>();
		params.put("idTypes", "'5','6','7','8','9','10','11'");
		params.put("profileId", profileId);
		params.put("tv", tv);
		params.put("tvType", 1);

		SysUser su = TokenManager.getToken();

		String defaultOrgId = (String) request.getParameter("defaultOrgId");
		if ((StringUtils.isEmpty(defaultOrgId))) {
			defaultOrgId = su.getOrgId().split(",")[0];
		}

		List<String> orgIdDataIntegrityList = new ArrayList<String>();
		orgIdDataIntegrityList.add(defaultOrgId);
		params.put("orgIdList", orgIdDataIntegrityList);

		List<Map<String, Object>> diList = dataIntegrityService
				.getDataIntegrityByMap(params);
		if (diList != null && diList.size() > 0) {
			for (Map<String, Object> di : diList) {
				String idType = (String) di.get("ID_TYPE");
				BigDecimal integrity = (BigDecimal) di.get("INTEGRITY");
				if (integrity == null) {
					integrity = new BigDecimal(0);
				}
				if (StringUtil.isEmpty(idType)) {
					continue;
				}
				if (idType.equals("5")) {
					result.put("dayIntegrity",
							integrity.setScale(2, BigDecimal.ROUND_DOWN)
									.toString());
				} else if (idType.equals("6")) {
					result.put("hundredPercent",
							integrity.setScale(2, BigDecimal.ROUND_DOWN)
									.toString());
				} else if (idType.equals("7")) {
					result.put("middlePercent",
							integrity.setScale(2, BigDecimal.ROUND_DOWN)
									.toString());
				} else if (idType.equals("8")) {
					result.put("zeroPercent",
							integrity.setScale(2, BigDecimal.ROUND_DOWN)
									.toString());
				} else if (idType.equals("9")) {
					result.put("hundredTotal", integrity.intValue());
				} else if (idType.equals("10")) {
					result.put("middleTotal", integrity.intValue());
				} else if (idType.equals("11")) {
					result.put("zeroTotal", integrity.intValue());
				}
			}
		}
		if (result.get("dayIntegrity") == null) {
			result.put("dayIntegrity",
					new BigDecimal("0").setScale(2, BigDecimal.ROUND_DOWN)
							.toString());
		}
		if (result.get("hundredPercent") == null) {
			result.put("hundredPercent",
					new BigDecimal("0").setScale(2, BigDecimal.ROUND_DOWN)
							.toString());
		}
		if (result.get("middlePercent") == null) {
			result.put("middlePercent",
					new BigDecimal("0").setScale(2, BigDecimal.ROUND_DOWN)
							.toString());
		}
		if (result.get("zeroPercent") == null) {
			result.put("zeroPercent",
					new BigDecimal("0").setScale(2, BigDecimal.ROUND_DOWN)
							.toString());
		}
		if (result.get("hundredTotal") == null) {
			result.put("hundredTotal", 0);
		}
		if (result.get("middleTotal") == null) {
			result.put("middleTotal", 0);
		}
		if (result.get("zeroTotal") == null) {
			result.put("zeroTotal", 0);
		}
		request.setAttribute("dayIntegrity", result.get("dayIntegrity"));        //昨日抄读数据百分比
		request.setAttribute("hundredPercent", result.get("hundredPercent"));    //已抄读到的数据量
		request.setAttribute("middlePercent", result.get("middlePercent"));        //已抄读数据百分比
		request.setAttribute("zeroPercent", result.get("zeroPercent"));            //丢失数据量
		request.setAttribute("hundredTotal", result.get("hundredTotal"));        //丢失数据百分比
		request.setAttribute("middleTotal", result.get("middleTotal"));            //抄读失败数据量
		request.setAttribute("zeroTotal", result.get("zeroTotal"));                //抄读失败数据百分比
		//日数据和月数据报表的默认时间
		request.setAttribute("endDay",
				DateUtils.date2Str(d, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
		request.setAttribute("startDay",
				DateUtils.date2Str(FormatUtil.addDaysToDate(d, -9), DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
		SimpleDateFormat sdf = new SimpleDateFormat(" MMMM, yyyy", Locale.ENGLISH);
		request.setAttribute("endMonth",
				DateUtils.date2Str(d, sdf));
		request.setAttribute("startMonth",
				DateUtils.date2Str(FormatUtil.addMonthsToDate(d, -11), sdf));


		List<SysOrg> sysOrgList = sysOrgService.getAllList();
		List<SysOrg> sysOrgListDisplay = new ArrayList<>();
		String[] orgIds = su.getOrgId().split(",");
		boolean isAdmin = false;

		if (orgIds.length > 0) {
			for (String orgId : orgIds) {
				for (SysOrg sysOrg : sysOrgList) {
					if (sysOrg.getParentOrgTid().equals("0") && sysOrg.getId().equals(orgId)) {
						isAdmin = true;
						break;
					}
				}
			}
		}

		if (!isAdmin) {
			if (orgIds.length > 0) {
				for (String orgId : orgIds) {
					for (SysOrg sysOrg : sysOrgList) {

						if (sysOrg.getId().equals(orgId)) {
							sysOrgListDisplay.add(sysOrg);
							break;
						}
					}
				}

			}
		} else {
			sysOrgListDisplay = sysOrgList;
		}
		request.setAttribute("sysOrgListDisplay", sysOrgListDisplay);
		request.setAttribute("orgIdDefault", defaultOrgId);


		return new ModelAndView("/report/scheduleReadsReport");
	}

	/**
	 * 跳转到dataIntegrity列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "list")
	public ModelAndView list(HttpServletRequest request, Model model,
							 String startIntegrity, String endIntegrity, String startDelay, String endDelay) {
		request.setAttribute("startIntegrity", startIntegrity);
		request.setAttribute("endIntegrity", endIntegrity);
		request.setAttribute("startDelay", startDelay);
		request.setAttribute("endDelay", endDelay);

		String profileCycleTypeListStr = ResourceUtil.getSessionattachmenttitle("profile.cycle.type.list");
		String cycleTypeReplace = "";
		if(profileCycleTypeListStr == null || "".equals(profileCycleTypeListStr)) {
			cycleTypeReplace = "Total:Total,Minutely:Minutely,Daily:Daily,Monthly:Monthly";
		} else {
			String[] splits = profileCycleTypeListStr.split(",");
			for(String split : splits) {
				cycleTypeReplace += (split + ":" + split);
				cycleTypeReplace += ",";
			}
			cycleTypeReplace = cycleTypeReplace.substring(0, cycleTypeReplace.length() - 1);
		}
		model.addAttribute("cycleTypeReplace", cycleTypeReplace);


		List<DictManufacturer> list = dictManufacturerService.getAllList();
		String ftrs = RoletoJson.listToReplaceStr(list, "id", "name");
		model.addAttribute("ftrReplace", ftrs);
		List<DictCommunicationType> dctList = dictCommunicationTypeService
				.getAllList();
		String comms = RoletoJson.listToReplaceStr(dctList, "id", "name");
		model.addAttribute("commReplace", comms);

		DictProfile dictProfile = new DictProfile();
		dictProfile.setProfileType("1");
		List<DictProfile> list1 = dictProfileService.getList(dictProfile);
		String ftrs1 = RoletoJson.listToReplaceStr(list1, "id", "name");
		model.addAttribute("dictProfileReplace", ftrs1);

		String channelIds = "";
		String channelNames = "";
		DictDataitemGroup entity = new DictDataitemGroup();
		entity.setAppType("1");
		List<DictDataitemGroup> ddgList = dictDataitemGroupService.getList(entity);
		if (ddgList.size() > 0) {
			DictDataitem dd = new DictDataitem();
			Map<String, Object> qm = new HashMap<String, Object>();
			qm.put("groupId", ddgList.get(0).getId());
			model.addAttribute("groupId", ddgList.get(0).getId());
			qm.put("appType", "1");
			dd.setExtData(qm);

			channelIds += ddgList.get(0).getId();
			List<DictDataitem> ddList = dictDataitemService.getList(dd);
			for (DictDataitem d : ddList) {
				channelIds += "," + d.getId();
				if (StringUtil.isNotEmpty(channelNames)) {
					channelNames += "&" + d.getName();
				} else {
					channelNames += d.getName();
				}
			}
		}

		model.addAttribute("channelNames", channelNames);


		return new ModelAndView("/data/dataIntegrityList");
	}


	/**
	 * 获取模块列表
	 * 
	 * @param dataIntegrity
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getModelList")
	@ResponseBody
	public AjaxJson getModelList(String mfrId, DataIntegrity dataIntegrity,
			HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		DictDeviceModel entity = new DictDeviceModel();
		entity.setManufacturerId(mfrId);
		List<DictDeviceModel> list = dictDeviceModelService.getDictDeviceList(entity);
		j.setObj(list);
		return j;
	}

	/**
	 * 导出
	 * 
	 * @param dataIntegrity
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "getExcelIntegrity")
	@ResponseBody
	public void getExcelIntegrity(Integer integrity_start, Integer integrity_end,
			String tvDate, DataIntegrity dataIntegrity,String delayDayStart,String delayDayEnd,String profileCycleType,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			Map<String, Object> extData = new HashMap<String, Object>();
			extData.put("integrity_start", integrity_start);
			extData.put("integrity_end", integrity_end);
			SysUser su = TokenManager.getToken();
			List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
			extData.put("orgIdList", orgIdList);

			String profileCycleTypeListStr = ResourceUtil.getSessionattachmenttitle("profile.cycle.type.listvalue");
			List<String> profileList = new ArrayList<>();
			if(profileCycleTypeListStr.length() > 0) {
				String[] splits = profileCycleTypeListStr.split(";");
				for(String split : splits) {
					String[] types = split.split(":");
					if(types.length == 2 && types[0].equals(profileCycleType))
					{
						//get profilecycle type list
						String[] lists = types[1].split(",");
						for(String profile : lists) {
							profileList.add(profile);
						}
						break;
					}
				}
			}
			extData.put("profileList", profileList);
			
			dataIntegrity.setExtData(extData);
			if (StringUtil.isNotEmpty(tvDate)) {
				dataIntegrity.setTv(DateUtils.parseDate(tvDate, ResourceUtil.getSessionattachmenttitle("local.date.formatter")));
			}
			
			//获取采集停留时间段  start
			String pattern="yyyy-MM-dd";
			String delayDate="";
			if(StringUtils.isNotEmpty(tvDate)) {
				delayDate=DateUtils.date2Str(DateUtils.parseDate(tvDate, ResourceUtil.getSessionattachmenttitle("local.date.formatter")),DateUtils.date_sdf);
			}
			if(StringUtils.isEmpty(delayDate)) {
				delayDate=new SimpleDateFormat(pattern).format(new Date());
			}
			try {
				if(StringUtils.isNotEmpty(delayDayStart)&&Integer.parseInt(delayDayStart)>0) {
					extData.put("delayEndDate",DateUtils.formatAddDate(delayDate, pattern, -Integer.parseInt(delayDayStart)));
				}
				if(StringUtils.isNotEmpty(delayDayEnd)&&Integer.parseInt(delayDayEnd)>0) {
					extData.put("delayStartDate",DateUtils.formatAddDate(delayDate, pattern, -Integer.parseInt(delayDayEnd)));
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			//获取采集停留时间段  end
			
			List<DataIntegrity> list = dataIntegrityService
					.getList(dataIntegrity);
			if (list.size() <= 0) {
				DataIntegrity d = new DataIntegrity();
				list.add(d);
			}
			ExcelDataFormatter edf = new ExcelDataFormatter();
			List<DictManufacturer> dmfList = dictManufacturerService
					.getAllList();
			Map<String, String> dmf = new HashMap<String, String>();
			for (DictManufacturer d : dmfList) {
				dmf.put(d.getId(), d.getName());
			}
			edf.set("mfrId", dmf);
			List<DictCommunicationType> dctList = dictCommunicationTypeService
					.getAllList();
			Map<String, String> dct = new HashMap<String, String>();
			for (DictCommunicationType d : dctList) {
				dct.put(d.getId(), d.getName());
			}
			edf.set("commId", dct);
			Map<String, String> trt = new HashMap<String, String>();
			trt.put("1", "Success");
			trt.put("0", "Failed");
			edf.set("taskResult", trt);
			// 要日期格式化使用以下方式
			Map<String, String> tvs = new HashMap<String, String>();
			tvs.put("tv", ResourceUtil.getSessionattachmenttitle("local.date.formatter"));
			edf.set("tv", tvs);
			Map<String, String> progress = new HashMap<String, String>();
			progress.put("progress", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
			edf.set("progress", progress);
			Map<String, String> lastTask = new HashMap<String, String>();
			lastTask.put("lastTask", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
			edf.set("lastTask", lastTask);
			Map<String, String> updateTv = new HashMap<String, String>();
			lastTask.put("updateTv", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
			edf.set("updateTv", updateTv);
			Map<String, String> comStatus = new HashMap<String, String>();
			comStatus.put("1", "Online");
			comStatus.put("0", "Offline");
			edf.set("comStatus", comStatus);
			ExcelUtils.writeToFile(list, edf, "integrityRateReport.xlsx", response,
					ValidGroup1.class);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	/**
	 * 打印
	 * 
	 * @param testUser
	 * @param response
	 */
	@RequestMapping(value = "print")
	@ResponseBody
	public void print(Integer integrity_start, Integer integrity_end,
			String tvDate, DataIntegrity dataIntegrity,String delayDayStart,String delayDayEnd,String profileCycleType,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			Map<String, Object> extData = new HashMap<String, Object>();
			extData.put("integrity_start", integrity_start);
			extData.put("integrity_end", integrity_end);
			String profileCycleTypeListStr = ResourceUtil.getSessionattachmenttitle("profile.cycle.type.listvalue");
			List<String> profileList = new ArrayList<>();
			if(profileCycleTypeListStr.length() > 0) {
				String[] splits = profileCycleTypeListStr.split(";");
				for(String split : splits) {
					String[] types = split.split(":");
					if(types.length == 2 && types[0].equals(profileCycleType))
					{
						//get profilecycle type list
						String[] lists = types[1].split(",");
						for(String profile : lists) {
							profileList.add(profile);
						}
						break;
					}
				}
			}
			extData.put("profileList", profileList);
			dataIntegrity.setExtData(extData);
			if (StringUtil.isNotEmpty(tvDate)) {
				dataIntegrity.setTv(DateUtils.parseDate(tvDate, ResourceUtil.getSessionattachmenttitle("local.date.formatter")));
			}
			
			//获取采集停留时间段  start
			String pattern="yyyy-MM-dd";
			String delayDate="";
			if(StringUtils.isNotEmpty(tvDate)) {
				delayDate=DateUtils.date2Str(DateUtils.parseDate(tvDate, ResourceUtil.getSessionattachmenttitle("local.date.formatter")),DateUtils.date_sdf);
			}
			
			if(StringUtils.isEmpty(delayDate)) {
				delayDate=new SimpleDateFormat(pattern).format(new Date());
			}
			try {
				if(StringUtils.isNotEmpty(delayDayStart)&&Integer.parseInt(delayDayStart)>0) {
					extData.put("delayEndDate",DateUtils.formatAddDate(delayDate, pattern, -Integer.parseInt(delayDayStart)));
				}
				if(StringUtils.isNotEmpty(delayDayEnd)&&Integer.parseInt(delayDayEnd)>0) {
					extData.put("delayStartDate",DateUtils.formatAddDate(delayDate, pattern, -Integer.parseInt(delayDayEnd)));
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			//获取采集停留时间段  end
			
			List<DataIntegrity> list = dataIntegrityService
					.getList(dataIntegrity);
			if (list.size() <= 0) {
				DataIntegrity d = new DataIntegrity();
				list.add(d);
			}
			ExcelDataFormatter edf = new ExcelDataFormatter();
			List<DictManufacturer> dmfList = dictManufacturerService
					.getAllList();
			Map<String, String> dmf = new HashMap<String, String>();
			for (DictManufacturer d : dmfList) {
				dmf.put(d.getId(), d.getName());
			}
			edf.set("mfrId", dmf);
			List<DictCommunicationType> dctList = dictCommunicationTypeService
					.getAllList();
			Map<String, String> dct = new HashMap<String, String>();
			for (DictCommunicationType d : dctList) {
				dct.put(d.getId(), d.getName());
			}
			edf.set("commId", dct);
			Map<String, String> trt = new HashMap<String, String>();
			trt.put("1", "Success");
			trt.put("0", "Failed");
			edf.set("taskResult", trt);
			// 要日期格式化使用以下方式
			Map<String, String> tvs = new HashMap<String, String>();
			tvs.put("tv", ResourceUtil.getSessionattachmenttitle("local.date.formatter"));
			edf.set("tv", tvs);
			Map<String, String> progress = new HashMap<String, String>();
			progress.put("progress",ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
			edf.set("progress", progress);
			Map<String, String> lastTask = new HashMap<String, String>();
			lastTask.put("lastTask", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
			edf.set("lastTask", lastTask);
			Map<String, String> updateTv = new HashMap<String, String>();
			lastTask.put("updateTv", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
			edf.set("updateTv", updateTv);
			Map<String, String> comStatus = new HashMap<String, String>();
			comStatus.put("1", "Online");
			comStatus.put("0", "Offline");
			edf.set("comStatus", comStatus);
			// list生成pdf打印；
			CreatePdf.printPdf(list, edf, ValidGroup1.class, request, response);

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 跳转到dataIntegrity新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "dataIntegrity")
	public ModelAndView dataIntegrity(DataIntegrity dataIntegrity,
			HttpServletRequest request, Model model) {
		if (StringUtil.isNotEmpty(dataIntegrity.getId())) {
			try {
				dataIntegrity = dataIntegrityService.getEntity(dataIntegrity
						.getId());
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			model.addAttribute("dataIntegrity", dataIntegrity);
		}
		return new ModelAndView("/data/dataIntegrity");
	}

	/**
	 * dataIntegrity查询分页
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
	@ResponseBody
	public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,
									 HttpServletRequest request) {
		TagUtil.setFieldValue(jqGridSearchTo, request); // 取传递的参数到JqGridSearchTo
		JqGridResponseTo j = null;
		Map<String, Object> m = jqGridSearchTo.getMap();
		String tv = (String) m.get("tvData");
		String sn = (String) m.get("serizlNoData");
		String profileCycleType = (String) m.get("profileCycleType");
		if(profileCycleType == null || "".equals(profileCycleType)) {
			String profileCycleTypeListStr = ResourceUtil.getSessionattachmenttitle("profile.cycle.type.list");
			if(profileCycleTypeListStr == null || "".equals(profileCycleTypeListStr)) {
				profileCycleType = "Total";
			} else {
				String[] splits = profileCycleTypeListStr.split(",");
				profileCycleType = splits[0];
			}
		}

		SysUser su = TokenManager.getToken();
		String defaultOrgId = (String) request.getParameter("defaultOrgId");

		try {
			if (StringUtil.isNotEmpty(tv))
				m.put("tv", DateUtils.date2Str(DateUtils.parseDate(tv, ResourceUtil.getSessionattachmenttitle("local.date.formatter")), DateUtils.date_sdf));
			m.put("idType", "1");
		} catch (ParseException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		//获取采集停留时间段  start
		String delayDayStart = (String) m.get("delayDay_start");
		String delayDayEnd = (String) m.get("delayDay_end");
		String pattern = "yyyy-MM-dd";
		String delayDate = (String) m.get("tv");
		if (StringUtils.isEmpty(delayDate)) {
			delayDate = new SimpleDateFormat(pattern).format(new Date());
		}
		try {
			if (StringUtils.isNotEmpty(delayDayStart) && Integer.parseInt(delayDayStart) > 0) {
				m.put("delayEndDate", DateUtils.formatAddDate(delayDate, pattern, -Integer.parseInt(delayDayStart)));
			}
			if (StringUtils.isNotEmpty(delayDayEnd) && Integer.parseInt(delayDayEnd) > 0) {
				m.put("delayStartDate", DateUtils.formatAddDate(delayDate, pattern, -Integer.parseInt(delayDayEnd)));
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		//获取采集停留时间段  end

		try {
			/**
			 * 区分电表和集中器
			 */
			AssetCommunicator commTemp = new AssetCommunicator();
			commTemp.setSn(sn);
			AssetCommunicator newComm = assetCommunicatorService.get(commTemp);
			if (StringUtil.isNotEmpty(newComm)) {
				m.put("commSn", newComm.getSn());
			}

//			List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);

			List<String> orgIdList = new ArrayList<>();
			if (StringUtils.isEmpty(defaultOrgId)) {
				orgIdList = OrganizationUtils.assembleOrgIds(su.getOrgId().split(","), sysOrgService, false, 0);
			} else {
				orgIdList = OrganizationUtils.assembleOrgIds(defaultOrgId, sysOrgService, false, 0);
			}
			m.put("orgIdList", orgIdList);

			jqGridSearchTo.put("profileId", profileCycleType);

			j = dataIntegrityService.getForMeterJqGrid(jqGridSearchTo);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return j;
	}

	/**
	 * 删除dataIntegrity信息
	 * 
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "del")
	@ResponseBody
	public AjaxJson del(DataIntegrity dataIntegrity, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			SysUser su = TokenManager.getToken();
			if (dataIntegrityService.deleteById(dataIntegrity.getId()) > 0) {
				j.setMsg("删除成功");
			} else {
				j.setSuccess(false);
				j.setMsg("删除失败");
			}
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg("系统异常");
		}
		return j;
	}

	/**
	 * 保存dataIntegrity信息
	 * 
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "save")
	@ResponseBody
	public AjaxJson save(
			@Validated(value = { ValidGroup1.class }) DataIntegrity dataIntegrity,
			BindingResult bindingResult, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		DataIntegrity t = new DataIntegrity();
		try {
			SysUser su = TokenManager.getToken();
			if (StringUtil.isNotEmpty(dataIntegrity.getId())) {
				t = dataIntegrityService.getEntity(dataIntegrity.getId());
				MyBeanUtils.copyBeanNotNull2Bean(dataIntegrity, t);
				dataIntegrityService.update(t);
				j.setMsg("修改成功");

			} else {
				dataIntegrityService.save(dataIntegrity);
				j.setMsg("创建成功");

			}
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg("系统异常");
		}
		return j;
	}

	
	//手动调用完整率计算
	@Transactional
	@RequestMapping(value = "exeManual")
	@ResponseBody
	public AjaxJson exeManual( HttpServletRequest request,final String startTime,final String endTime) {
			AjaxJson j = new AjaxJson();
			final SimpleDateFormat sdf = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.formatter"));
			final String profileId=ResourceUtil.getSessionattachmenttitle("profileId");
			final List<String> listIds = Lists.newArrayList();
			listIds.add(profileId);
			try {
				//异步调用uci
				new Thread() {
					public void run() {
						try{
							DispatchTaskPort port = (DispatchTaskPort) UciInterfaceUtil.getInterface("DispatchTaskPort", DispatchTaskPort.class, sysServiceAttributeService);
							DispatchMessage mes = new DispatchMessage();
							mes.setType("CALCULATION");
							mes.setOpType("INTEGRITY");
							mes.setOpStartDate(sdf.parse(startTime).getTime());
							mes.setOpEndDate(sdf.parse(endTime).getTime());
							mes.setIds(listIds);
							List<DispatchMessage> messList=Lists.newArrayList();
							messList.add(mes);
							port.dispatch(messList);
						}catch(Exception ex) {
							ex.printStackTrace();
						}
						
					};
				}.start();
			} catch (Exception e) {
				e.printStackTrace();
				j.setSuccess(false);
				j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
			}
		return j;
	}
}