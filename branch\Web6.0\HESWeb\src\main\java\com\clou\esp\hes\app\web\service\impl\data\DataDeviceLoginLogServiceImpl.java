/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataDeviceLoginLog{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：刘义柯
 * 创建于：2020-10-30 07:36:03
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.data;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.data.DataDeviceLoginLogDao;
import com.clou.esp.hes.app.web.model.data.DataDeviceLoginLog;
import com.clou.esp.hes.app.web.service.data.DataDeviceLoginLogService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dataDeviceLoginLogService")
public class DataDeviceLoginLogServiceImpl  extends CommonServiceImpl<DataDeviceLoginLog>  implements DataDeviceLoginLogService {

	@Resource
	private DataDeviceLoginLogDao dataDeviceLoginLogDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dataDeviceLoginLogDao);
    }
	@SuppressWarnings("rawtypes")
	public DataDeviceLoginLogServiceImpl() {}
	
	
}