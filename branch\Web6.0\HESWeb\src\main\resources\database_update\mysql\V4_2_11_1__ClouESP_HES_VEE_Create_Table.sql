CREATE TABLE ASSET_VEE_RULE 
(
  ID VARCHAR(32) NOT NULL, 
  NAME VARCHAR(128) NOT NULL, 
  MG_ID VARCHAR(32) NOT NULL, 
  CLASS_ID DECIMAL(4, 0), 
  RULE_DETAIL VARCHAR(256), 
  EVENT_ID VARCHAR(32), 
  RULE_TYPE DECIMAL(4, 0) NOT NULL, 
  CONSTRAINT ASSET_VEE_RULE_PK PRIMARY KEY (ID,MG_ID)
);


CREATE TABLE DATA_VEE_EVENT 
(
  DEVICE_ID NVARCHAR(32) NOT NULL, 
  TV datetime(6) NOT NULL, 
  EVENT_ID NVARCHAR(32) NOT NULL, 
  EVENT_DETAIL VARCHAR(256), 
  CONSTRAINT DATA_VEE_EVENT_PK PRIMARY KEY (DEVICE_ID, TV, EVENT_ID)
);

CREATE TABLE DICT_VEE_EVENT 
(
  ID VARCHAR(32) NOT NULL, 
  NAME VARCHAR(128) NOT NULL, 
  USER_DEFINE DECIMAL(4, 0) NOT NULL, 
  METHOD VARCHAR(32) NOT NULL, 
  DATAITEM_ID VARCHAR(64), 
  DESCR VARCHAR(256), 
  SORT_ID DECIMAL(4, 0), 
  CONSTRAINT DICT_VEE_EVENT_PK PRIMARY KEY (ID)
);

CREATE TABLE DICT_VEE_EVENT_PARAM 
(
  EVENT_ID VARCHAR(32) NOT NULL, 
  DATAITEM_ID VARCHAR(64) NOT NULL, 
  PRE_CYCLE DECIMAL(4, 0) NOT NULL, 
  CYCLE_TYPE VARCHAR(20), 
  CONSTRAINT DICT_VEE_EVENT_PARAM_PARAM_PK PRIMARY KEY(EVENT_ID, DATAITEM_ID, PRE_CYCLE)
); 

CREATE TABLE DICT_VEE_METHOD 
(
  ID VARCHAR(32) NOT NULL, 
  NAME VARCHAR(64), 
  SORT_ID DECIMAL(4, 0), 
  TYPE DECIMAL(4, 0), 
  PACKAGE_ID VARCHAR(256), 
  METHOD_NAME VARCHAR(256), 
  DESCR VARCHAR(1024), 
  CONSTRAINT DICT_VEE_METHOD_TYPE_PK PRIMARY KEY (ID)
);

