/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataParameterPlan{ } 
 * 
 * 摘    要： dataParameterPlan
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-04-27 09:13:12
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.data;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.clou.esp.hes.app.web.validation.ValidGroup2;
import com.power7000g.core.excel.Excel;

public class DataParameterPlan  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DataParameterPlan() {
	}

	/**introduction*/
	@Excel(name = "Plan Description", width = 30, groups = {ValidGroup1.class,ValidGroup2.class})
	private java.lang.String introduction;
	/**startTime*/
	private java.util.Date startTime;
	@Excel(name = "Plan Start Time", width = 30, groups = {ValidGroup1.class,ValidGroup2.class})
	private java.lang.String startTimeStr;
	/**endTime*/
	private java.util.Date endTime;
	/**taskStartTime*/
	private java.util.Date taskStartTime;
	@Excel(name = "Task Start Time", width = 30, groups = {ValidGroup1.class,ValidGroup2.class})
	private java.lang.String taskStartTimeStr;
	/**taskEndTime*/
	private java.util.Date taskEndTime;
	@Excel(name = "Task End Time", width = 30, groups = {ValidGroup1.class,ValidGroup2.class})
	private java.lang.String taskEndTimeStr;
	/**groupType*/
	@Excel(name = "Group Type", width = 30, groups = ValidGroup1.class)
	private java.lang.String groupType;
	/**groupId*/
	private java.lang.String groupId;
	/**operatorId*/
	private java.lang.String operatorId;
	/**taskCycle*/
	@Excel(name = "Task Cycle", width = 30, groups = {ValidGroup1.class,ValidGroup2.class})
	private java.lang.String taskCycle;
	/**state 0:正常；1:取消  */
	private java.lang.String state;
	/**expiryTime*/
	private java.util.Date expiryTime;
	@Excel(name = "Plan Expiry Time", width = 30, groups = {ValidGroup1.class,ValidGroup2.class})
	private java.lang.String expiryTimeStr;
	private java.lang.String sn;
	
	@Excel(name = "Done", width = 30, groups = {ValidGroup1.class,ValidGroup2.class})
	private java.lang.String done;
	@Excel(name = "Expired", width = 30, groups = {ValidGroup1.class,ValidGroup2.class})
	private java.lang.String expired;
	@Excel(name = "Running", width = 30, groups = {ValidGroup1.class,ValidGroup2.class})
	private java.lang.String running;
	@Excel(name = "Waiting", width = 30, groups = {ValidGroup1.class,ValidGroup2.class})
	private java.lang.String waiting;
	@Excel(name = "Group Name", width = 30, groups = ValidGroup1.class)
	private java.lang.String groupName;
	
	private String jobState;
	
	private String deviceType;
	
	/**
	 * introduction
	 * @return the value of DATA_PARAMETER_PLAN.INTRODUCTION
	 * @mbggenerated 2018-04-27 09:13:12
	 */
	public java.lang.String getIntroduction() {
		return introduction;
	}

	/**
	 * introduction
	 * @param introduction the value for DATA_PARAMETER_PLAN.INTRODUCTION
	 * @mbggenerated 2018-04-27 09:13:12
	 */
    public void setIntroduction(java.lang.String introduction) {
		this.introduction = introduction;
	}
	/**
	 * startTime
	 * @return the value of DATA_PARAMETER_PLAN.START_TIME
	 * @mbggenerated 2018-04-27 09:13:12
	 */
	public java.util.Date getStartTime() {
		return startTime;
	}

	/**
	 * startTime
	 * @param startTime the value for DATA_PARAMETER_PLAN.START_TIME
	 * @mbggenerated 2018-04-27 09:13:12
	 */
    public void setStartTime(java.util.Date startTime) {
		this.startTime = startTime;
	}
	/**
	 * endTime
	 * @return the value of DATA_PARAMETER_PLAN.END_TIME
	 * @mbggenerated 2018-04-27 09:13:12
	 */
	public java.util.Date getEndTime() {
		return endTime;
	}

	/**
	 * endTime
	 * @param endTime the value for DATA_PARAMETER_PLAN.END_TIME
	 * @mbggenerated 2018-04-27 09:13:12
	 */
    public void setEndTime(java.util.Date endTime) {
		this.endTime = endTime;
	}
	/**
	 * taskStartTime
	 * @return the value of DATA_PARAMETER_PLAN.TASK_START_TIME
	 * @mbggenerated 2018-04-27 09:13:12
	 */
	public java.util.Date getTaskStartTime() {
		return taskStartTime;
	}

	/**
	 * taskStartTime
	 * @param taskStartTime the value for DATA_PARAMETER_PLAN.TASK_START_TIME
	 * @mbggenerated 2018-04-27 09:13:12
	 */
    public void setTaskStartTime(java.util.Date taskStartTime) {
		this.taskStartTime = taskStartTime;
	}
	/**
	 * taskEndTime
	 * @return the value of DATA_PARAMETER_PLAN.TASK_END_TIME
	 * @mbggenerated 2018-04-27 09:13:12
	 */
	public java.util.Date getTaskEndTime() {
		return taskEndTime;
	}

	/**
	 * taskEndTime
	 * @param taskEndTime the value for DATA_PARAMETER_PLAN.TASK_END_TIME
	 * @mbggenerated 2018-04-27 09:13:12
	 */
    public void setTaskEndTime(java.util.Date taskEndTime) {
		this.taskEndTime = taskEndTime;
	}
	/**
	 * groupType
	 * @return the value of DATA_PARAMETER_PLAN.GROUP_TYPE
	 * @mbggenerated 2018-04-27 09:13:12
	 */
	public java.lang.String getGroupType() {
		return groupType;
	}

	/**
	 * groupType
	 * @param groupType the value for DATA_PARAMETER_PLAN.GROUP_TYPE
	 * @mbggenerated 2018-04-27 09:13:12
	 */
    public void setGroupType(java.lang.String groupType) {
		this.groupType = groupType;
	}
	/**
	 * groupId
	 * @return the value of DATA_PARAMETER_PLAN.GROUP_ID
	 * @mbggenerated 2018-04-27 09:13:12
	 */
	public java.lang.String getGroupId() {
		return groupId;
	}

	/**
	 * groupId
	 * @param groupId the value for DATA_PARAMETER_PLAN.GROUP_ID
	 * @mbggenerated 2018-04-27 09:13:12
	 */
    public void setGroupId(java.lang.String groupId) {
		this.groupId = groupId;
	}
	/**
	 * operatorId
	 * @return the value of DATA_PARAMETER_PLAN.OPERATOR_ID
	 * @mbggenerated 2018-04-27 09:13:12
	 */
	public java.lang.String getOperatorId() {
		return operatorId;
	}

	/**
	 * operatorId
	 * @param operatorId the value for DATA_PARAMETER_PLAN.OPERATOR_ID
	 * @mbggenerated 2018-04-27 09:13:12
	 */
    public void setOperatorId(java.lang.String operatorId) {
		this.operatorId = operatorId;
	}
	/**
	 * taskCycle
	 * @return the value of DATA_PARAMETER_PLAN.TASK_CYCLE
	 * @mbggenerated 2018-04-27 09:13:12
	 */
	public java.lang.String getTaskCycle() {
		return taskCycle;
	}

	/**
	 * taskCycle
	 * @param taskCycle the value for DATA_PARAMETER_PLAN.TASK_CYCLE
	 * @mbggenerated 2018-04-27 09:13:12
	 */
    public void setTaskCycle(java.lang.String taskCycle) {
		this.taskCycle = taskCycle;
	}
	/**
	 * state
	 * @return the value of DATA_PARAMETER_PLAN.STATE
	 * @mbggenerated 2018-04-27 09:13:12
	 */
	public java.lang.String getState() {
		return state;
	}

	/**
	 * state
	 * @param state the value for DATA_PARAMETER_PLAN.STATE
	 * @mbggenerated 2018-04-27 09:13:12
	 */
    public void setState(java.lang.String state) {
		this.state = state;
	}
	/**
	 * expiryTime
	 * @return the value of DATA_PARAMETER_PLAN.EXPIRY_TIME
	 * @mbggenerated 2018-04-27 09:13:12
	 */
	public java.util.Date getExpiryTime() {
		return expiryTime;
	}

	/**
	 * expiryTime
	 * @param expiryTime the value for DATA_PARAMETER_PLAN.EXPIRY_TIME
	 * @mbggenerated 2018-04-27 09:13:12
	 */
    	public void setExpiryTime(java.util.Date expiryTime) {
		this.expiryTime = expiryTime;
	}

	public DataParameterPlan(java.lang.String introduction 
	,java.util.Date startTime 
	,java.util.Date endTime 
	,java.util.Date taskStartTime 
	,java.util.Date taskEndTime 
	,java.lang.String groupType 
	,java.lang.String groupId 
	,java.lang.String operatorId 
	,java.lang.String taskCycle 
	,java.lang.String state 
	,java.util.Date expiryTime ) {
		super();
		this.introduction = introduction;
		this.startTime = startTime;
		this.endTime = endTime;
		this.taskStartTime = taskStartTime;
		this.taskEndTime = taskEndTime;
		this.groupType = groupType;
		this.groupId = groupId;
		this.operatorId = operatorId;
		this.taskCycle = taskCycle;
		this.state = state;
		this.expiryTime = expiryTime;
	}

	public java.lang.String getSn() {
		return sn;
	}

	public void setSn(java.lang.String sn) {
		this.sn = sn;
	}

	public java.lang.String getTaskStartTimeStr() {
		return taskStartTimeStr;
	}

	public void setTaskStartTimeStr(java.lang.String taskStartTimeStr) {
		this.taskStartTimeStr = taskStartTimeStr;
	}

	public java.lang.String getTaskEndTimeStr() {
		return taskEndTimeStr;
	}

	public void setTaskEndTimeStr(java.lang.String taskEndTimeStr) {
		this.taskEndTimeStr = taskEndTimeStr;
	}

	public java.lang.String getDone() {
		return done;
	}

	public void setDone(java.lang.String done) {
		this.done = done;
	}

	public java.lang.String getExpired() {
		return expired;
	}

	public void setExpired(java.lang.String expired) {
		this.expired = expired;
	}

	public java.lang.String getRunning() {
		return running;
	}

	public void setRunning(java.lang.String running) {
		this.running = running;
	}

	public java.lang.String getWaiting() {
		return waiting;
	}

	public void setWaiting(java.lang.String waiting) {
		this.waiting = waiting;
	}

	public java.lang.String getGroupName() {
		return groupName;
	}

	public void setGroupName(java.lang.String groupName) {
		this.groupName = groupName;
	}

	public java.lang.String getStartTimeStr() {
		return startTimeStr;
	}

	public void setStartTimeStr(java.lang.String startTimeStr) {
		this.startTimeStr = startTimeStr;
	}

	public java.lang.String getExpiryTimeStr() {
		return expiryTimeStr;
	}

	public void setExpiryTimeStr(java.lang.String expiryTimeStr) {
		this.expiryTimeStr = expiryTimeStr;
	}

	public String getJobState() {
		return jobState;
	}

	public void setJobState(String jobState) {
		this.jobState = jobState;
	}

	public String getDeviceType() {
		return deviceType;
	}

	public void setDeviceType(String deviceType) {
		this.deviceType = deviceType;
	}

}