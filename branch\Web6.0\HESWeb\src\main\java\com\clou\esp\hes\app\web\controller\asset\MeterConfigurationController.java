/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetCommunicator{ } 
 * 
 * 摘    要： GPRS Module
DCU
Gateway
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-06 08:04:54
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.asset;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import ch.iec.tc57._2011.meterdefineconfig.MeterDefineConfigPort;
import ch.iec.tc57._2011.meterdefineconfig_.Arrays;
import ch.iec.tc57._2011.meterdefineconfig_.Meter;
import ch.iec.tc57._2011.meterdefineconfig_.MeterDefineConfig;
import ch.iec.tc57._2011.meterdefineconfig_.Structures;
import ch.iec.tc57._2011.meterdefineconfig_.Values;
import ch.iec.tc57._2011.meterdefineconfig_.MeterDefineConfig.ReadingType;
import ch.iec.tc57._2011.meterdefineconfigmessage.MeterDefineConfigPayloadType;
import ch.iec.tc57._2011.meterdefineconfigmessage.MeterDefineConfigRequestMessageType;
import ch.iec.tc57._2011.meterdefineconfigmessage.MeterDefineConfigResponseMessageType;
import ch.iec.tc57._2011.schema.message.HeaderType;
import ch.iec.tc57._2011.schema.message.UserType;
import clouesp.hes.core.uci.soap.custom.asset_refresh.AssetRefreshPort;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.CreatePdf;
import com.clou.esp.hes.app.web.core.util.DateTimeFormatterUtil;
import com.clou.esp.hes.app.web.core.util.ExcelUtils;
import com.clou.esp.hes.app.web.core.util.Globals;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.core.util.SpringContextUtil;
import com.clou.esp.hes.app.web.core.util.UciInterfaceUtil;
import com.clou.esp.hes.app.web.core.util.XMLUtil;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.asset.AssetMeterDto;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroup;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroupMap;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroupValue;
import com.clou.esp.hes.app.web.model.asset.MeterConfiguration;
import com.clou.esp.hes.app.web.model.asset.StepTariff;
import com.clou.esp.hes.app.web.model.dict.DictCommunicationType;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;
import com.clou.esp.hes.app.web.model.dict.DictDataitemGroup;
import com.clou.esp.hes.app.web.model.dict.DictDataitemGroupMap;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.asset.AssetCommunicatorService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterGroupMapService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterGroupService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterGroupValueService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.dict.DictCommunicationTypeService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemGroupMapService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemGroupService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemService;
import com.clou.esp.hes.app.web.service.system.SysServiceAttributeService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.excel.ExcelDataFormatter;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;
import com.power7000g.core.util.redis.JedisUtils;

/**
 * <AUTHOR>
 * @时间：2018-01-19 11:31:54
 * @描述：Meter Configuration
DCU
Gateway类
 */
@Controller
@RequestMapping("/meterConfigurationController")
public class MeterConfigurationController extends BaseController{

 	@Resource
    private AssetCommunicatorService assetCommunicatorService;
 	@Resource
    private AssetMeterService assetMeterService;
	@Resource
	private DictDataitemGroupService dictDataitemGroupService;
	@Resource
	private DictDataitemService dictDataitemService;
	@Resource
    private DictCommunicationTypeService dictCommunicationTypeService;
	@Resource
	private DictDataitemGroupMapService dictDataitemGroupMapService;
	@Resource
	private AssetMeterGroupService assetMeterGroupService;
	@Resource
    private AssetMeterGroupValueService assetMeterGroupValueService;
	@Resource
    private AssetMeterGroupMapService assetMeterGroupMapService;
	@Resource
    private SysServiceAttributeService sysServiceAttributeService;
	@Resource
    private DataUserLogService dataUserLogService;

	/**
	 * 跳转到Meter Configuration界面
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "toMeterConfiguration")
    public ModelAndView toMeterConfiguration(HttpServletRequest request, Model model) {
		String channelIds = "";
		String channelNames = "";
		DictDataitemGroup entity = new DictDataitemGroup();
		entity.setAppType("4");
		entity.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
		List<DictDataitemGroup> ddgList = dictDataitemGroupService.getList(entity);
		if(ddgList.size() > 0){
			DictDataitem dd = new DictDataitem();
			Map<String, Object> qm = new HashMap<String, Object>();
			qm.put("groupId", ddgList.get(0).getId());
			qm.put("appType", "4");
			dd.setExtData(qm);
			//分组已区分 规约 故删掉
		//	dd.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
			channelIds += ddgList.get(0).getId();
			List<DictDataitem> ddList = dictDataitemService.getList(dd);
			for(DictDataitem d : ddList){
				channelIds += "," + d.getId();
				if(StringUtil.isNotEmpty(channelNames)){
					channelNames += "," + d.getName();
				}else{
					channelNames += d.getName();
				}
			}
		}
		model.addAttribute("channelIds", channelIds);
		model.addAttribute("channelNames", channelNames);
		List<DictCommunicationType> dctl = dictCommunicationTypeService.getAllList();
		String comms = RoletoJson.listToReplaceStr(dctl, "id", "name");
		model.addAttribute("commReplace", comms);
		//添加假费率数据
		List<DictDataitem> ddList=new ArrayList<DictDataitem>();
		for(int i=1;i<=4;i++){
			DictDataitem dd=new DictDataitem();
			dd.setId(""+i);
			dd.setName("Rate"+i);
			ddList.add(dd);
		}
		String rateReplace = RoletoJson.listToReplaceStr(ddList, "id", "name",";");
		model.addAttribute("rateReplace", rateReplace);
		DictDataitem dd=new DictDataitem();
		dd.put("groupId", "1004002");
		List<DictDataitem> ddtList=dictDataitemService.getList(dd);
		String itemReplace = RoletoJson.listToReplaceStr(ddtList, "id", "name");
		model.addAttribute("itemReplace", itemReplace);
		model.addAttribute("itemList",ddtList);
        return new ModelAndView("/asset/meterConfiguration");
    }
	
	/**
	 * 获取设备信息和数据项信息，新增下发任务列表
	 * assetMeter查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "addTask")
    @ResponseBody
    public JqGridResponseTo addTask(JqGridSearchTo jqGridSearchTo, HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j = null;
        JSONObject o = new JSONObject();
        String[] snsl = request.getParameterValues("snsl[]");
        String[] channelIdl = request.getParameterValues("channelIdl[]");
        String[] channelNames = request.getParameterValues("channelNames[]");
        List<MeterConfiguration> list = new ArrayList<MeterConfiguration>();
        if(snsl == null || channelIdl == null){
        	PageInfo<MeterConfiguration> pageInfo = new PageInfo<MeterConfiguration>(list);
            j = JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
        	return j;
        }
        try {
        	//获取当前用户token
        	SysUser sysUser = TokenManager.getToken();
        	//拼接ID，作为列表数据入redis中的主键ID
        	String messageId = DateUtils.formatDate("yyyyMMdd") + sysUser.getId();
        	Map<String,Object> odrData = new LinkedHashMap<String, Object>();
        	Date date = new Date();
            for(int i=0; i<snsl.length; i++){
	           for(int k=0; k<channelIdl.length; k++){
	        	   MeterConfiguration mc = new MeterConfiguration();
	        	   //获取参数类型名字或者ID等
	        	   DictDataitemGroupMap groupMap = dictDataitemGroupMapService.selectParamterTypeNameByItemId(channelIdl[k]);
	        	   //获取电表所属组信息
	        	   //TODO EDISON 20200714
	        	   AssetMeterGroup assetMeterGroup = assetMeterGroupService.selectGroupNameByMeterSN(snsl[i], channelIdl[k]);
	        	   if(!StringUtil.isNotEmpty(assetMeterGroup)){
	        		   PageInfo<MeterConfiguration> pageInfo = new PageInfo<MeterConfiguration>(list);
	                   j = JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	                   return j;
	        	   }
	        	   mc.setId(channelIdl[k] + snsl[i]);
	        	   mc.setSn(snsl[i]);
	        	   mc.setParameterItemId(channelIdl[k]);	//参数类型ID
	        	   mc.setParameterItem(channelNames[k]);	//参数类型名称
	        	   //获取父属性ID和名称
	        	   mc.setParameterItemParentId(groupMap.getId());
	        	   mc.setParameterType(groupMap.getParamterTypeName());
	        	   mc.setMeterGroupId(assetMeterGroup.getId());	//电表所属组ID
	        	   mc.setMeterGroup(assetMeterGroup.getName());	//电表所属组名
	        	   mc.setRequestTime(date);
	        	   mc.setStatus("0");	//0:Upcoming,1:Processing,2:Success,3:Failed,4:Timeout,5:Cancel
	        	   list.add(mc);
	        	   odrData.put(mc.getId(), mc);
	           }
            }
            //拼接当前日期和用户ID为主键，把列表中的数据存储入Redis中
            JedisUtils.setObject(messageId, odrData, 0);
            
            PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
    		PageInfo<MeterConfiguration> pageInfo = new PageInfo<MeterConfiguration>();
    		pageInfo.setList(list);
            j = JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
            o.put("messageId", messageId);
            o.put("status", true);
            j.setJson(o);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
	
	/**
	 * @Description 获取task任务数据，调用接口，下发执行
	 * @param request  jqGridSearchTo
	 * @return JqGridResponseTo
	 * <AUTHOR> 
	 * @Time 2018年1月30日 下午3:05:58
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "excuteTask")
    @ResponseBody
    public AjaxJson excuteTask(HttpServletRequest request) {
		AjaxJson json = new AjaxJson();
        Map<String,Object> odrData = new LinkedHashMap<String, Object>();
        Date currentDate = new Date();	//初始化当前时间
        List<MeterConfiguration> list = new ArrayList<MeterConfiguration>();
        List<String> meterList = new ArrayList<>();
        try {
        	//获取当前用户token
        	SysUser sysUser = TokenManager.getToken();
        	//拼接ID，作为列表数据入redis中的主键ID
        	String messageId = DateUtils.formatDate("yyyyMMdd") + sysUser.getId();
        	//根据ID获取缓存到redis的task列表, 如果不存在数据，则返回界面提示
        	odrData = (Map<String, Object>) JedisUtils.getObject(messageId);
        	if(odrData == null || odrData.size() == 0){
        		json.setSuccess(false);
        		json.setObj(list);
            	return json;
            }
        	/**
        	 * 下面是调用接口的完整过程
        	 * todoedison
        	 * //	String basePath ="http://linan1607.f3322.net:20000/HESWeb";
        	 */
			String basePath = ResourceUtil.getUciBasePath(request);
			//	String basePath ="http://linan1607.f3322.net:20000/HESWeb";
        	/**
        	 * 遍历map，查询对应的XML数据转换成pojo对象，循环调用接口下发
        	 */
        	for(Map.Entry<String,Object> entry : odrData.entrySet()){
        		MeterConfiguration meterConfig = (MeterConfiguration) entry.getValue();
        		//遍历的每条task缓存入数据库，ID为channelId+sn
        		JedisUtils.setObject(meterConfig.getParameterItemId() + meterConfig.getSn(), meterConfig, 0);
        		//获取xml的值
        		List<AssetMeterGroupValue> listAmgv = new ArrayList<>();
        		//判断是否passive calendar, 如果是，需要查询season、week、day三条数据
        		if("37.0.1.6.0.0.0".equals(meterConfig.getParameterItemId())){
        			//设置一个空的list<string>到redis中，方便回调方法的判断，KEY：sn+37.0.1.6.0.0.0
        			List<String> strArrList = new ArrayList<String>();
        			JedisUtils.setObject(meterConfig.getSn() + "37.0.1.6.0.0.0strArrList", strArrList, 0);
        			
        			// 2018-09-22 baijun 添加一个TOU激活时间
//        			String[] strArr = {"37.0.1.7","37.0.1.8","37.0.1.9"};
        			String[] strArr = {"37.0.1.7","37.0.1.8","37.0.1.9","37.0.1.10"};
        			
        			for (int i = 0; i < strArr.length; i++) {
        				AssetMeterGroupValue obj = new AssetMeterGroupValue();
        				obj = assetMeterGroupValueService.selectOneDate(meterConfig.getMeterGroupId(), strArr[i]);
        				if(obj == null || obj.getXmlValue() == null){
        					 json.setSuccess(false);
 				            json.setErrorMsg("The Fee rate setting is incomplete and can't be sent down!");
 				    		json.setObj(list);
 				        	return json;
        				}
						listAmgv.add(obj);
					}
        		}else if("40.0.0.0.0".equals(meterConfig.getParameterItemId())){
        			//设置一个空的list<string>到redis中，方便回调方法的判断，KEY：sn+40.0.0.0.0
        			List<String> strArrList = new ArrayList<String>();
        			JedisUtils.setObject(meterConfig.getSn() + "40.0.0.0.0strArrList", strArrList, 0);
        			
        			//阶梯费率的ID 同时是tou tariff的id
        			String[] strArr = {"40.0.3.1", "40.0.3.2", "40.0.2.5", "40.0.2.6"};
        			
        			for (int i = 0; i < strArr.length; i++) {
        				AssetMeterGroupValue obj = new AssetMeterGroupValue();
        				obj = assetMeterGroupValueService.selectOneDate(meterConfig.getMeterGroupId(), strArr[i]);
        				if(obj == null || obj.getXmlValue() == null){
        					json.setSuccess(false);
 				            json.setErrorMsg("The Fee rate setting is incomplete and can't be sent down!");
 				    		json.setObj(list);
 				        	return json;
        				}
						listAmgv.add(obj);
					}
        		}else if("41.0.0.0.0".equals(meterConfig.getParameterItemId())){
        			//设置一个空的list<string>到redis中，方便回调方法的判断，KEY：sn+41.0.0.0.0
        			List<String> strArrList = new ArrayList<String>();
        			JedisUtils.setObject(meterConfig.getSn() + "41.0.0.0.0strArrList", strArrList, 0);
        			
        			//阶梯费率的ID 同时是tou tariff的id
        			String[] strArr = {"41.0.2.5", "41.0.2.6"};
        			
        			for (int i = 0; i < strArr.length; i++) {
        				AssetMeterGroupValue obj = new AssetMeterGroupValue();
        				obj = assetMeterGroupValueService.selectOneDate(meterConfig.getMeterGroupId(), strArr[i]);
        				if(obj == null || obj.getXmlValue() == null){
        					json.setSuccess(false);
 				            json.setErrorMsg("The Fee rate setting is incomplete and can't be sent down!");
 				    		json.setObj(list);
 				        	return json;
        				}
						listAmgv.add(obj);
					}
        		}else{
        			
        			//TODO EDISON
        			if(!StringUtils.isEmpty(meterConfig.getParameterItemId())){
        				AssetMeterGroupValue obj =  new AssetMeterGroupValue();
        				if("0.0.0.0".equals(meterConfig.getParameterItemId()) || "0.0.0.1".equals(meterConfig.getParameterItemId())){
        					obj.setGroupId(meterConfig.getMeterGroupId());
        					obj.setDataitemId(meterConfig.getParameterItemId());
        					//组成XML
    						SysUser su=TokenManager.getToken();   
    					    MeterDefineConfigRequestMessageType mdcrmt=new MeterDefineConfigRequestMessageType();
    				   	 	HeaderType ht=new HeaderType();
    				        Date date=new Date();
    				        ht.setVerb("create");
    				        ht.setNoun("MeterConfig");
    				        ht.setTimestamp(DateUtils.dateToXmlDate(date));
    				        ht.setSource("ClouESP HES");
    				        ht.setMessageID(DateUtils.formatDate("yyyyMMddHHmmss")+su.getId());
    				        ht.setAsyncReplyFlag(true);
    				        ht.setReplyAddress(basePath+"/interfaces/ReplyMeterDefineConfigPort?wsdl");
    				        ht.setAckRequired(true);
    				        UserType u =new UserType();
    				        u.setOrganization(su.getOrgId());
    				        u.setUserID(su.getId());
    				        ht.setUser(u);
    				        mdcrmt.setHeader(ht);
    				        MeterDefineConfigPayloadType mdfpt=new MeterDefineConfigPayloadType();
    				        MeterDefineConfig mdc=new MeterDefineConfig();
    				        Meter meter=new Meter();
    				        meter.setMRID(meterConfig.getSn());
    				        mdc.getMeters().add(meter);
    				        
    				        ReadingType rt=new ReadingType();
    			        	rt.setRef(meterConfig.getParameterItemId());
    			        	mdc.setReadingType(rt);
    			        	mdfpt.setMeterDefineConfig(mdc);
    			        	mdcrmt.setPayload(mdfpt);
    			        	
    			        	
    			        	
    			        	
    			        	Arrays arrays = new Arrays();
    			    		
		    				Values values = new Values();
		    				values.setValue("test");
		    				values.setType("index1");
		    				
		    				Structures structures = new Structures();
		    				structures.getValues().add(values);
		    			
		    				structures.setType("index2");
		    				arrays.getStructures().add(structures);
		    				
		    				arrays.setType("index3");
		    				mdc.getArrays().add(arrays);
    			        	
    			        	obj.setXmlValue(XMLUtil.convertToXml(mdcrmt));
        					System.out.println(obj.getXmlValue());
        				}else{
        					 obj = assetMeterGroupValueService
        	        					.selectOneDate(meterConfig.getMeterGroupId(), meterConfig.getParameterItemId());
        	        			if("37.2.1.1".equals(meterConfig.getParameterItemId())){
        	        				if(obj == null || obj.getXmlValue() == null){
        	        					json.setSuccess(false);
        					            json.setErrorMsg("The Fee rate setting is incomplete and can't be sent down!");
        					    		json.setObj(list);
        					        	return json;
        	        				}
        	        			}
        				}

            			if(StringUtil.isNotEmpty(obj)){
            				listAmgv.add(obj);
            			}
            		}
        		}
        			
        		for(int i=0; i<listAmgv.size(); i++){
        			/**
            		 * XML字符串反转成对象
            		 * 请求参数类： 包含Header Request Payload
            		 * //对象转化为xml存储
            		 *	XMLUtil.convertToXml(mdcrmt);
            		 */
            		MeterDefineConfigRequestMessageType requestMessageType = (MeterDefineConfigRequestMessageType) 
            				XMLUtil.convertXmlStrToObject(MeterDefineConfigRequestMessageType.class, listAmgv.get(i).getXmlValue());
            		//HeaderType
            		HeaderType headerType = requestMessageType.getHeader();
            		headerType.setVerb("create");
            		headerType.setSource("ClouESP HES");
            		headerType.setTimestamp(DateUtils.dateToXmlDate(currentDate));
            		headerType.setReplyAddress(basePath+"/interfaces/ReplyMeterDefineConfigPort?wsdl");	//回调方法地址
            		UserType userType = headerType.getUser();			//Header中的UserType(userId用户ID,orgId组织机构ID)
                    userType.setOrganization(sysUser.getOrgId());
                    userType.setUserID(sysUser.getId());
                    headerType.setMessageID(meterConfig.getSn() + UUID.randomUUID());
                    //Payload
                    MeterDefineConfigPayloadType payloadType = requestMessageType.getPayload();
                    MeterDefineConfig defineConfig = payloadType.getMeterDefineConfig();
                    Meter meter = new Meter();
                    meter.setMRID(meterConfig.getSn());
                    defineConfig.getMeters().clear();		//清除旧数据
                    defineConfig.getMeters().add(meter);	//new一个电表，设置电表号到xml对象中
                    /**
                     * 修改xml文件中数据项的时间格式
                     * season_profile	yyyy-MM-dd HH:mm:ss,FF
                     * special_day_table	yyyy-MM-dd,FF
                     */
                    if("37.0.1.7".equals(defineConfig.getReadingType().getRef()) || "37.2.1.1".equals(defineConfig.getReadingType().getRef())){
                    	List<Arrays> dataArraysList = defineConfig.getArrays();
                    	if(dataArraysList.size() > 0){
                    		for (int j = 0; j < dataArraysList.size(); j++) {
                    			Arrays arraysObj = dataArraysList.get(j);
                    			List<Structures> structuresList = arraysObj.getStructures();
                    			for (int k = 0; k < structuresList.size(); k++) {
                    				Structures structuresObj = structuresList.get(k);
                    				List<Values> valuesList = (List<Values>) structuresObj.getValues();
                    				for (int l = 0; l < valuesList.size(); l++) {
										Values value = valuesList.get(l);
										//修改日期格式，把界面显示的  月/日/年  时:分:秒 转化成 年-月-日  时:分:秒
										if("season_start".equals(value.getType())){
//											String[] timeArr = value.getValue().split(" ");
//											String[] dateArr = timeArr[0].split("/");
											value.setValue(value.getValue());
										}
										if("specialday_date".equals(value.getType())){					
//												String[] dateArr = value.getValue().split(",");
//												String[] day = dateArr[0].split("/");
											value.setValue(value.getValue());						
										}
									}
                    			}
                    		}
                    	}else{
                    		json.setSuccess(false);
                    		json.setErrorMsg("No Passive Calendar Data!");
                    		return json;
                    	}
                    }
                    System.out.println("下发的数据转换成的XML===" + XMLUtil.convertToXml(requestMessageType));
                    //调用接口，先实例化对象
                    MeterDefineConfigPort port = (MeterDefineConfigPort)UciInterfaceUtil
                    		.getInterface("MeterDefineConfigPort", MeterDefineConfigPort.class,sysServiceAttributeService);
                   /* XMLUtil.convertToXml(requestMessageType, "D:\\WarFiles\\" 
                    		+ defineConfig.getReadingType().getRef() + listAmgv.get(i).getDataitemId() + ".xml");*/
                    MeterDefineConfigResponseMessageType responseMessageType = port.meterDefineConfig(requestMessageType);
                    
                    System.out.println("Write Meter Information....." + XMLUtil.convertToXml(responseMessageType));
                    
                    //如果list中不存在电表号，则新增
                    if(!(meterList.contains(meter.getMRID()))){
                    	meterList.add(meter.getMRID().toString());
                    }
                    //添加操作日志
                    try {
                    	  AssetMeter tmp = assetMeterService.getEntityBySN(meterConfig.getSn());
                  	  	  AssetMeterDto dto = this.assetMeterService.getMeterDtoInfo(tmp.getId());
                          dataUserLogService.insertDataUserLog(sysUser.getId(), "Meter Configuration", "Set Parameter", dto.getLog("Set Parameter")+";[Data Channel="+listAmgv.get(i).getDataitemId()+"]");
                          System.out.println("Isuued Data :[" + meterConfig.getSn() + "] " + "[" + listAmgv.get(i).getDataitemId() +"]" + responseMessageType.getReply().getResult());
                    }catch (Exception e) {
						e.printStackTrace();
					}
                }
                //修改task list的状态为正在运行
        		meterConfig.setStatus("1");		//1:Processing
        		list.add(meterConfig);
        	}
        	//修改task状态信息，并且展示到页面
        	json.setObj(list);
        }
        catch (Exception e) {	//异常处理
            e.printStackTrace();
            json.setSuccess(false);
            json.setErrorMsg("System Exception!");
    		json.setObj(list);
        	return json;
        }
        return json;
    }
	
	/**
     * 删除新增的task数据
     * @param id
     * @return
     */
    @SuppressWarnings("unchecked")
	@ResponseBody
    @RequestMapping(value = "delTaskRow")
    public AjaxJson delTaskRow(String id, HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        Map<String,Object> odrData = new HashMap<String, Object>();
        Map<String, Object> attributes = new HashMap<>();
        try {
        	SysUser sysUser = TokenManager.getToken();
        	//拼接ID，作为列表数据入redis中的主键ID
        	String messageId = DateUtils.formatDate("yyyyMMdd") + sysUser.getId();
        	odrData = (Map<String, Object>) JedisUtils.getObject(messageId);
        	if(!(odrData.isEmpty() || odrData.size() == 0)){
        		odrData.remove(id);		//移除界面删除的数据
        		JedisUtils.setObject(messageId, odrData, 0);	//存储新的数据到redis中，替换旧的
        		//重新获取数据总长度
        		odrData = (Map<String, Object>) JedisUtils.getObject(messageId);
        		attributes.put("dataSize", odrData.size());
        		System.out.println("删除之后的长度：" + odrData.size());
        		j.setAttributes(attributes);
        		j.setMsg("Delete success!");
        	}else{
                j.setSuccess(false);
                j.setMsg("Delete failure!");
            }
        }
        catch (Exception e) {
        	e.printStackTrace();
            j.setSuccess(false);
            j.setMsg("System Exception!");
        }
        return j;
    }
	
	/**
	 * GET页面   根据ID查询TOU、Limiter、step tariff
	 * @param meterId
	 * @param groupType
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getTouGroupData")
    @ResponseBody
    public AjaxJson getTouGroupData(String meterId,String searchType,String type,String profileId,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        String[] limiterItems=request.getParameterValues("limiterItem");
        String getType=request.getParameter("getType");
        if(StringUtil.isEmpty(meterId)){
        	j.setErrorMsg("The Meter No is empty!");
        	return j;
        }
        if(StringUtil.isEmpty(searchType)){
        	j.setErrorMsg("The Search Type is empty!");
        	return j;
        }
        if(StringUtil.isEmpty(type)){
        	j.setErrorMsg("The Type is empty!");
        	return j;
        }
        
        if(!StringUtil.isEmpty(getType)){
	        if("3".equals(getType)){
	        	   if(StringUtil.isEmpty(profileId)){
	               	j.setErrorMsg("The ProfileId is empty!");
	               	return j;
	               }
	        }
        }
        
        try {
        	j.put("touGroupName", "");
        	j.put("limiterGroupName", "");
        	if(searchType.equals("1")){
        		AssetMeterGroupMap entity=new AssetMeterGroupMap();
        		entity.setId(meterId);
        		entity.setType("2");	//查询TOU组
        		AssetMeterGroupMap amgm=assetMeterGroupMapService.get(entity);
        		if(amgm!=null&&StringUtil.isNotEmpty(amgm.getGroupId())){
        			AssetMeterGroup touAmg=assetMeterGroupService.getEntity(amgm.getGroupId());
            		if(touAmg!=null){
            			j.put("touGroupName", touAmg.getName());
            			j.put("touGroupId", touAmg.getId());
            		}
        		}
        		entity.setType("3");	//查询Limiter组
        		AssetMeterGroupMap amgm1=assetMeterGroupMapService.get(entity);
        		if(amgm1!=null&&StringUtil.isNotEmpty(amgm1.getGroupId())){
        			AssetMeterGroup limiterAmg=assetMeterGroupService.getEntity(amgm1.getGroupId());
            		if(limiterAmg!=null){
            			j.put("limiterGroupName", limiterAmg.getName());
            			j.put("limiterGroupId", limiterAmg.getId());
            		}
        		}
        		entity.setType("4");	//查询阶梯汇率组
        		AssetMeterGroupMap stepTariffGroupMap = assetMeterGroupMapService.get(entity);
        		if(stepTariffGroupMap != null && StringUtil.isNotEmpty(stepTariffGroupMap.getGroupId())){
        			AssetMeterGroup stepTariffGroup = assetMeterGroupService.getEntity(stepTariffGroupMap.getGroupId());
            		if(stepTariffGroup != null){
            			j.put("stepTariffGroupName", stepTariffGroup.getName());
            			j.put("stepTariffGroupId", stepTariffGroup.getId());
            		}
        		}
        		entity.setType("1");	//查询Measurement组
        		AssetMeterGroupMap measurementGroupMap = assetMeterGroupMapService.get(entity);
        		if(measurementGroupMap != null && StringUtil.isNotEmpty(measurementGroupMap.getGroupId())){
        			AssetMeterGroup measurementGroup = assetMeterGroupService.getEntity(measurementGroupMap.getGroupId());
            		if(measurementGroup != null){
            			j.put("measurementGroupName", measurementGroup.getName());
            			j.put("measurementGroupId", measurementGroup.getId());
            		}
        		}
        	}else{
        		/*
        		 * 查询TOU、Limiter、step tariff组数据（UCI接口）
        		 */
        		AssetMeter entity = assetMeterService.getEntity(meterId);
        		SysUser su=TokenManager.getToken();   
    	        //todoedison String basePath ="http://linan1607.f3322.net:20000/HESWeb";
    	        String basePath = ResourceUtil.getUciBasePath(request);
    	        //   String basePath ="http://2481h646b9.wicp.vip:21273/HESWeb";
    	        MeterDefineConfigPort port = (MeterDefineConfigPort)UciInterfaceUtil
    	        		.getInterface("MeterDefineConfigPort", MeterDefineConfigPort.class,sysServiceAttributeService);
        		MeterDefineConfigRequestMessageType mdcrmt = new MeterDefineConfigRequestMessageType();
        		HeaderType ht = new HeaderType();
    	        Date date = new Date();
    	        ht.setVerb("get");
    	        ht.setNoun("MeterConfig");
    	        ht.setTimestamp(DateUtils.dateToXmlDate(date));
    	        ht.setSource("ClouESP HES");
    	        ht.setMessageID(DateUtils.formatDate("yyyyMMddHHmmss")+su.getId());
    	        ht.setAsyncReplyFlag(true);
    	        ht.setReplyAddress(basePath+"/interfaces/ReplyMeterDefineConfigPort?wsdl");
    	        ht.setAckRequired(true);
    	        UserType u = new UserType();
    	        u.setOrganization(su.getOrgId());
    	        u.setUserID(su.getId());
    	        ht.setUser(u);
    	        mdcrmt.setHeader(ht);
    	        ch.iec.tc57._2011.meterdefineconfig_.MeterDefineConfig mdc = new ch.iec.tc57._2011.meterdefineconfig_.MeterDefineConfig();
    	        Meter meter = new Meter();
    	        meter.setMRID(entity.getSn());
    	        mdc.getMeters().add(meter);
    	        ch.iec.tc57._2011.meterdefineconfig_.MeterDefineConfig.ReadingType rt = new ch.iec.tc57._2011.meterdefineconfig_.MeterDefineConfig.ReadingType();
    	        MeterDefineConfigPayloadType mdcpy = new MeterDefineConfigPayloadType();
    	        Map<String,Object> msgIds = new HashMap<String, Object>();
    	        if(StringUtil.isEmpty(getType)){
    	        	j.setErrorMsg("The Get Type is empty!");
    	        	return j;
    	        }
    	        if(getType.equals("0")){
    	        	if(type.equals("1")){ //Active
    	        		for(int i=9;i>=7;i--){
    	        			rt.setRef("37.0.1."+i);
    	        			mdc.setReadingType(rt);
    	        			mdcpy.setMeterDefineConfig(mdc);
    	        			mdcrmt.setPayload(mdcpy);
    	        			mdcrmt.getHeader().setMessageID(DateUtils.formatDate("yyyyMMddHHmmss")+rt.getRef()+su.getId());
    	        			msgIds.put(mdcrmt.getHeader().getMessageID(), mdcrmt.getHeader().getMessageID());
    	        			MeterDefineConfigResponseMessageType s = port.meterDefineConfig(mdcrmt);
    	        			if(!s.getReply().getResult().toUpperCase().equals("OK")){
    	        				Map<String,Object> m=new HashMap<String, Object>();
    	        				m.put("rateId", rt.getRef());
    	        				m.put("msg", s.getReply().getResult());
    	        				j.put(mdcrmt.getHeader().getMessageID(), m);
    	        			}
    	        		}
    	        	}
    	        	if(type.equals("2")){ //Passive
    	        		for(int i=5;i>=3;i--){
    	        			rt.setRef("37.0.1."+i);
    	        			mdc.setReadingType(rt);
    	        			mdcpy.setMeterDefineConfig(mdc);
    	        			mdcrmt.setPayload(mdcpy);
    	        			mdcrmt.getHeader().setMessageID(DateUtils.formatDate("yyyyMMddHHmmss")+rt.getRef()+su.getId());
    	        			msgIds.put(mdcrmt.getHeader().getMessageID(), mdcrmt.getHeader().getMessageID());
    	        			MeterDefineConfigResponseMessageType s = port.meterDefineConfig(mdcrmt);
    	        			if(!s.getReply().getResult().toUpperCase().equals("OK")){
    	        				Map<String,Object> m=new HashMap<String, Object>();
    	        				m.put("rateId", rt.getRef());
    	        				m.put("msg", s.getReply().getResult());
    	        				j.put(mdcrmt.getHeader().getMessageID(), m);
    	        			}
    	        		}
    	        	}
    	        }else if(getType.equals("1")){	//spcial day
    	        	rt.setRef("37.2.1.1");
    	        	mdc.setReadingType(rt);
    	        	mdcpy.setMeterDefineConfig(mdc);
    	        	mdcrmt.setPayload(mdcpy);
    	        	mdcrmt.getHeader().setMessageID(DateUtils.formatDate("yyyyMMddHHmmss")+rt.getRef()+su.getId());
    	        	msgIds.put(mdcrmt.getHeader().getMessageID(), mdcrmt.getHeader().getMessageID());
    	        	MeterDefineConfigResponseMessageType s = port.meterDefineConfig(mdcrmt);
    	        	if(!s.getReply().getResult().toUpperCase().equals("OK")){
    	        		Map<String,Object> m=new HashMap<String, Object>();
    	        		m.put("rateId", rt.getRef());
    	        		m.put("msg", s.getReply().getResult());
    	        		j.put(mdcrmt.getHeader().getMessageID(), m);
    	        	}
    	        }else if(getType.equals("2")){	//Limiter
    	        	if(limiterItems==null||limiterItems.length<=0){
    	        		j.setErrorMsg("The Limiter Item is empty!");
        	        	return j;
    	        	}
    	        	for(int i=0;i<limiterItems.length;i++){
    	        		rt.setRef(limiterItems[i]);
    	        		mdc.setReadingType(rt);
    	        		mdcpy.setMeterDefineConfig(mdc);
    	        		mdcrmt.setPayload(mdcpy);
    	        		mdcrmt.getHeader().setMessageID(DateUtils.formatDate("yyyyMMddHHmmss")+rt.getRef()+su.getId());
    	        		msgIds.put(mdcrmt.getHeader().getMessageID(), mdcrmt.getHeader().getMessageID());
    	        		MeterDefineConfigResponseMessageType s = port.meterDefineConfig(mdcrmt);
	        			if(!s.getReply().getResult().toUpperCase().equals("OK")){
	        				Map<String,Object> m=new HashMap<String, Object>();
	        				m.put("rateId", rt.getRef());
	        				m.put("msg", s.getReply().getResult());
	        				j.put(mdcrmt.getHeader().getMessageID(), m);
	        			}
    	        	}
    	        }else if(getType.equals("4")){	//step tariff todoedison
    	        	if(type.equals("1")){ 
    	        		String[] strArr = {"40.0.3.1", "40.0.3.2", "40.0.2.5", "40.0.2.6"};
        	        	for (int i = 0; i < strArr.length; i++) {
        	        		rt.setRef(strArr[i]);
        	        		mdc.setReadingType(rt);
        	        		mdcpy.setMeterDefineConfig(mdc);
        	        		mdcrmt.setPayload(mdcpy);
        	        		mdcrmt.getHeader().setMessageID(DateUtils.formatDate("yyyyMMddHHmmss")+rt.getRef()+su.getId());
        	        		msgIds.put(mdcrmt.getHeader().getMessageID(), mdcrmt.getHeader().getMessageID());
        	        		MeterDefineConfigResponseMessageType responseMessageType = port.meterDefineConfig(mdcrmt);
        	        		System.out.println("GET Passive step tariff xml data "+ strArr[i] +" is " + XMLUtil.convertToXml(responseMessageType));
        	        		/*
        	        		 * 解析UCI接口返回的参数
        	        		 * 如果下发失败，则把失败数据存入json中
        	        		 */
        	        		if(!responseMessageType.getReply().getResult().toUpperCase().equals("OK")){
    	        				Map<String,Object> m = new HashMap<String, Object>();
    	        				m.put("rateId", rt.getRef());
    	        				m.put("msg", responseMessageType.getReply().getResult());
    	        				j.put(mdcrmt.getHeader().getMessageID(), m);
    	        			}
    					}
        	        	j.put("stepTariffDataitems", strArr);
    	        	}else if(type.equals("2")){ //active
    	        		String[] strArr = {"40.0.3.0","40.0.2.4"};
        	        	for (int i = 0; i < strArr.length; i++) {
        	        		rt.setRef(strArr[i]);
        	        		mdc.setReadingType(rt);
        	        		mdcpy.setMeterDefineConfig(mdc);
        	        		mdcrmt.setPayload(mdcpy);
        	        		mdcrmt.getHeader().setMessageID(DateUtils.formatDate("yyyyMMddHHmmss")+rt.getRef()+su.getId());
        	        		msgIds.put(mdcrmt.getHeader().getMessageID(), mdcrmt.getHeader().getMessageID());
        	        		MeterDefineConfigResponseMessageType responseMessageType = port.meterDefineConfig(mdcrmt);
        	        		System.out.println("GET active step tariff xml data "+ strArr[i] +" is " + XMLUtil.convertToXml(responseMessageType));
        	        		/*
        	        		 * 解析CUI接口返回的参数
        	        		 * 如果下发失败，则把失败数据存入json中
        	        		 */
        	        		if(!responseMessageType.getReply().getResult().toUpperCase().equals("OK")){
    	        				Map<String,Object> m = new HashMap<String, Object>();
    	        				m.put("rateId", rt.getRef());
    	        				m.put("msg", responseMessageType.getReply().getResult());
    	        				j.put(mdcrmt.getHeader().getMessageID(), m);
    	        			}
    					}
        	        	j.put("stepTariffDataitems", strArr);
    	        	}
    	        
    	        }else if(getType.equals("3")){	//TODO Measurement group todoedison
    	
    	        		String[] strArr = {"0.0.0.0", "0.0.0.1"};
        	        	for (int i = 0; i < strArr.length; i++) {
        	        		rt.setRef(strArr[i]+"_"+profileId);
        	        		mdc.setReadingType(rt);
        	        		mdcpy.setMeterDefineConfig(mdc);
        	        		mdcrmt.setPayload(mdcpy);
        	        		mdcrmt.getHeader().setMessageID(DateUtils.formatDate("yyyyMMddHHmmss")+rt.getRef()+su.getId());
        	        		msgIds.put(mdcrmt.getHeader().getMessageID(), mdcrmt.getHeader().getMessageID());
        	        		MeterDefineConfigResponseMessageType responseMessageType = port.meterDefineConfig(mdcrmt);
        	        		System.out.println("GET Measurement xml data "+ strArr[i] +" is " + XMLUtil.convertToXml(responseMessageType));
        	        		/*
        	        		 * 解析UCI接口返回的参数
        	        		 * 如果下发失败，则把失败数据存入json中
        	        		 */
        	        		if(!responseMessageType.getReply().getResult().toUpperCase().equals("OK")){
    	        				Map<String,Object> m = new HashMap<String, Object>();
    	        				m.put("rateId", rt.getRef());
    	        				m.put("msg", responseMessageType.getReply().getResult());
    	        				j.put(mdcrmt.getHeader().getMessageID(), m);
    	        			}
    					}
    	        	
    	        }
    	        else if(getType.equals("6")){	//tou tariff todoedison
    	        	
    	        	if(type.equals("1")){ 
    	        		String[] strArr = {"41.0.2.5", "41.0.2.6"};
        	        	for (int i = 0; i < strArr.length; i++) {
        	        		rt.setRef(strArr[i]);
        	        		mdc.setReadingType(rt);
        	        		mdcpy.setMeterDefineConfig(mdc);
        	        		mdcrmt.setPayload(mdcpy);
        	        		mdcrmt.getHeader().setMessageID(DateUtils.formatDate("yyyyMMddHHmmss")+rt.getRef()+su.getId());
        	        		msgIds.put(mdcrmt.getHeader().getMessageID(), mdcrmt.getHeader().getMessageID());
        	        		MeterDefineConfigResponseMessageType responseMessageType = port.meterDefineConfig(mdcrmt);
        	        		System.out.println("GET Passive tou tariff xml data "+ strArr[i] +" is " + XMLUtil.convertToXml(responseMessageType));
        	        		/*
        	        		 * 解析CUI接口返回的参数
        	        		 * 如果下发失败，则把失败数据存入json中
        	        		 */
        	        		if(!responseMessageType.getReply().getResult().toUpperCase().equals("OK")){
    	        				Map<String,Object> m = new HashMap<String, Object>();
    	        				m.put("rateId", rt.getRef());
    	        				m.put("msg", responseMessageType.getReply().getResult());
    	        				j.put(mdcrmt.getHeader().getMessageID(), m);
    	        			}
    					}
        	        	j.put("touTariffDataitems", strArr);
    	        	}else if(type.equals("2")){ //active
    	        		String[] strArr = {"41.0.2.4"};
        	        	for (int i = 0; i < strArr.length; i++) {
        	        		rt.setRef(strArr[i]);
        	        		mdc.setReadingType(rt);
        	        		mdcpy.setMeterDefineConfig(mdc);
        	        		mdcrmt.setPayload(mdcpy);
        	        		mdcrmt.getHeader().setMessageID(DateUtils.formatDate("yyyyMMddHHmmss")+rt.getRef()+su.getId());
        	        		msgIds.put(mdcrmt.getHeader().getMessageID(), mdcrmt.getHeader().getMessageID());
        	        		MeterDefineConfigResponseMessageType responseMessageType = port.meterDefineConfig(mdcrmt);
        	        		System.out.println("GET Active tou tariff xml data "+ strArr[i] +" is " + XMLUtil.convertToXml(responseMessageType));
        	        		/*
        	        		 * 解析CUI接口返回的参数
        	        		 * 如果下发失败，则把失败数据存入json中
        	        		 */
        	        		if(!responseMessageType.getReply().getResult().toUpperCase().equals("OK")){
    	        				Map<String,Object> m = new HashMap<String, Object>();
    	        				m.put("rateId", rt.getRef());
    	        				m.put("msg", responseMessageType.getReply().getResult());
    	        				j.put(mdcrmt.getHeader().getMessageID(), m);
    	        			}
    					}
        	        	j.put("touTariffDataitems", strArr);
    	        	}
    	        
    	        }else if(getType.equals("5")){
    	        	String[] strArr = {"40.0.0.18", "40.0.0.19", "40.0.0.20"};
    	        	for (int i = 0; i < strArr.length; i++) {
    	        		rt.setRef(strArr[i]);
    	        		mdc.setReadingType(rt);
    	        		mdcpy.setMeterDefineConfig(mdc);
    	        		mdcrmt.setPayload(mdcpy);
    	        		mdcrmt.getHeader().setMessageID(DateUtils.formatDate("yyyyMMddHHmmss")+rt.getRef()+su.getId());
    	        		msgIds.put(mdcrmt.getHeader().getMessageID(), mdcrmt.getHeader().getMessageID());
    	        		MeterDefineConfigResponseMessageType responseMessageType = port.meterDefineConfig(mdcrmt);
    	        		System.out.println("GET friendly xml data "+ strArr[i] +" is " + XMLUtil.convertToXml(responseMessageType));
    	        		/*
    	        		 * 解析CUI接口返回的参数
    	        		 * 如果下发失败，则把失败数据存入json中
    	        		 */
    	        		if(!responseMessageType.getReply().getResult().toUpperCase().equals("OK")){
	        				Map<String,Object> m = new HashMap<String, Object>();
	        				m.put("rateId", rt.getRef());
	        				m.put("msg", responseMessageType.getReply().getResult());
	        				j.put(mdcrmt.getHeader().getMessageID(), m);
	        			}
					}
    	        	j.put("friendlyDataitems", strArr);
    	        }
        	j.put("messageId", msgIds);
        	}
        }catch (Exception e) {
        	e.printStackTrace();
        	j.setSuccess(false);
        	j.setMsg("Abnormal operation!");
        }
        return j;
    }
    
    /**
	  * 导出excel文件
	  * @param dataIntegrity
	  * @param request
	  * @param response
	  */
	 @SuppressWarnings("unchecked")
	 @RequestMapping(value = "getExcelResultData")
	 @ResponseBody
	 public void getExcelResultData(String messageId, HttpServletRequest request, HttpServletResponse response) {
		if(StringUtil.isEmpty(messageId)){
			return ;
		}
		try {
			//根据messageId获取redis中的数据信息
			Map<String,Object> map = (Map<String, Object>) JedisUtils.getObject(messageId);
			List<MeterConfiguration> mcs = new ArrayList<MeterConfiguration>();
			if(map == null){
				MeterConfiguration mc = new MeterConfiguration();
	        	mcs.add(mc);
			}else{
				for (Entry<String, Object> entry : map.entrySet()) {  
					MeterConfiguration mc = (MeterConfiguration) entry.getValue();
					mcs.add(mc);
				}
			}
	   		if(mcs.size() <= 0){
	   			MeterConfiguration mc = new MeterConfiguration();
	        	mcs.add(mc);
	        }
	        ExcelDataFormatter edf = new ExcelDataFormatter();
	        Map<String,String> requestTime = new HashMap<String, String>();
	        requestTime.put("requestTime", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
	        edf.set("requestTime", requestTime);
	        Map<String,String> reponseTime = new HashMap<String, String>();
	        reponseTime.put("reponseTime", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
	        edf.set("reponseTime", reponseTime);
	        Map<String,String> status = new HashMap<String, String>();
	        status.put("0", "Upcoming");
	        status.put("1", "Processing");
	        status.put("2", "Success");
	        status.put("3", "Failed");
	        status.put("4", "Timeout");
	        edf.set("status", status);
	        ExcelUtils.writeToFile(mcs, edf, "excelResultDataList.xlsx", response, ValidGroup1.class);
		} catch (Exception e) {
			e.printStackTrace();
		}
	 }
	/**
	 * 默认limiter项
	 * @param jqGridSearchTo
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "limiterDatagrid")
    @ResponseBody
    public JqGridResponseTo limiterDatagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
        	DictDataitem dd=new DictDataitem();
    		dd.put("groupId", "1004002");
    		List<DictDataitem> ddtList=dictDataitemService.getList(dd);
        	PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
    		PageInfo<DictDataitem> pageInfo = new PageInfo<DictDataitem>();
    		pageInfo.setList(ddtList);
            j= JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
	 
	 /**
	  * 打印
	  * @param dataIntegrity
	  * @param request
	  * @param response
	  */
	 @SuppressWarnings({ "unchecked" })
	 @RequestMapping(value = "getPrintResultData")
	 @ResponseBody
	 public void getPrintResultData(String messageId,HttpServletRequest request, HttpServletResponse response) {
		 if(StringUtil.isEmpty(messageId)){
			 return ;
		 }
		try {
			Map<String,Object> map = (Map<String, Object>) JedisUtils.getObject(messageId);
			List<MeterConfiguration> mcs = new ArrayList<MeterConfiguration>();
			if(map == null){
				MeterConfiguration mc = new MeterConfiguration();
	        	mcs.add(mc);
			}else{
				for (Entry<String, Object> entry : map.entrySet()) {  
					MeterConfiguration mc = (MeterConfiguration) entry.getValue();
					mcs.add(mc);
				}
			}
	   		if(mcs.size() <= 0){
	   			MeterConfiguration mc = new MeterConfiguration();
	        	mcs.add(mc);
	        }
		   	Collections.sort(mcs);
			ExcelDataFormatter edf = new ExcelDataFormatter();
			Map<String,String> requestTime = new HashMap<String, String>();
			requestTime.put("requestTime", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
			edf.set("requestTime", requestTime);
			Map<String,String> reponseTime = new HashMap<String, String>();
			reponseTime.put("reponseTime", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
			edf.set("reponseTime", reponseTime);
			Map<String,String> status = new HashMap<String, String>();
			status.put("0", "Upcoming");
			status.put("1", "Processing");
			status.put("2", "Success");
			status.put("3", "Failed");
			status.put("4", "Timeout");
			edf.set("status", status);
		    CreatePdf.printPdf(mcs, edf, ValidGroup1.class, request, response);
		} catch (Exception e) {
			e.printStackTrace();
		}
	 }
	 
	 /**
	  * 日期增加分钟
	  * @Description 
	  * @param num
	  * @return Date
	  * <AUTHOR> 
	  * @Time 2018年2月8日 下午2:35:43
	  */
	 public Date dateAddMinute(int num){
		 Calendar afterTime = Calendar.getInstance();
		 afterTime.add(Calendar.MINUTE, num);
		 Date afterDate = (Date) afterTime.getTime();
		 return afterDate;
	 }
	
}