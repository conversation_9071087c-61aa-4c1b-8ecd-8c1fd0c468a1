package com.clou.esp.hes.app.web.model.dict;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

/**
 * 
 * <AUTHOR>
 *
 */
public class DictUserLog extends BaseEntity {
	
	private static final long serialVersionUID = 1L;
	
	public DictUserLog(){
	}
	
	private String logType;
	private String logSubType;
	private int sortId;
	private boolean flag = false;

	public String getLogType() {
		return logType;
	}
	public void setLogType(String logType) {
		this.logType = logType;
	}
	public String getLogSubType() {
		return logSubType;
	}
	public void setLogSubType(String logSubType) {
		this.logSubType = logSubType;
	}
	public int getSortId() {
		return sortId;
	}
	public void setSortId(int sortId) {
		this.sortId = sortId;
	}
	public boolean isFlag() {
		return flag;
	}
	public void setFlag(boolean flag) {
		this.flag = flag;
	}
	
	
}
