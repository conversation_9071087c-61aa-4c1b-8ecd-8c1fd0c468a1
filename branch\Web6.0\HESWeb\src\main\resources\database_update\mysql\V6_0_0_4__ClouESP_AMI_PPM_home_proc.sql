DELIMITER $$
DROP PROCEDURE IF EXISTS Total_Month_Sales_Amount_All$$
CREATE PROCEDURE Total_Month_Sales_Amount_All()
BEGIN
	    DECLARE done INT DEFAULT 0;
	
	    -- 声明一个变量，用来存放从游标中提取的数据
		-- 特别注意这里的名字不能与由游标中使用的列明相同，否则得到的数据都是NULL
		DECLARE	iRecordNumber INT ;                         /* 记录个数 */
		DECLARE s_ORG_ID VARCHAR (100) DEFAULT NULL;       /* 管理机构ID */
	    DECLARE s_Utility_ID VARCHAR (100) DEFAULT NULL;       /* 租户ID */
	 
	    /* 根据传入的管理机构ID 进行统计 */
	    -- 声明游标对应的 SQL 语句
	    DECLARE cur_1 CURSOR FOR
	          SELECT  ID,Utility_ID  FROM sys_org  ORDER BY ID asc ;
	   -- 在游标循环到最后会将 done 设置为 1
		DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;
	    truncate table ppm_total_month_sales_amount;
	
	    /* 打开游标 */
	    open cur_1;
		REPEAT
			FETCH cur_1 INTO  s_ORG_ID,s_Utility_ID ;
			 if not done then
			   -- select "1", s_ORG_ID; /* 调试输出语句 */
	            call Total_Month_Sales_Amount_One(s_ORG_ID,s_Utility_ID);
			  end if;
	 	UNTIL done END REPEAT;
	   -- 关闭游标
	   CLOSE cur_1;
	   -- SELECT ' 所有管理机构售电金额 统计完毕！！'; /* 调试输出语句 */
END$$


DROP PROCEDURE IF EXISTS Total_Month_Sales_Amount_One$$

CREATE PROCEDURE Total_Month_Sales_Amount_One()
BEGIN
	/**
	        功能说明：根据传入的管理机构，计算最近12个月的售电量，保存到 数据库表中
	        作者：孙学峰 创建日期：2018年06月12日
			调用方法：call `Total_Month_Sales_Amount_One`(Input_Org_ID);
		***/
	
	    /* 需要定义接收游标数据的变量  */
		DECLARE i INT ;                           /* 循环次数 */
		DECLARE s_SQL VARCHAR(100);
	    DECLARE done INT DEFAULT 0;
		DECLARE ID VARCHAR(32);
		DECLARE	s_DATE VARCHAR (30) DEFAULT NULL;            /* 时间 */
		DECLARE	s_Value VARCHAR (30) DEFAULT NULL;           /* 统计金额 */
	    
	    /* 根据传入的org_ID 进行统计 */
	    -- 声明游标对应的 SQL 语句
	    DECLARE cur_1 CURSOR FOR
	    
	        SELECT DATE_FORMAT(vhi.SALES_DATE, '%Y-%m') AS StatMonth,
			   SUM(CASE WHEN recharge_type = 0 THEN vhi.Customer_Payment_Amount
				   WHEN recharge_type = 1 THEN - 1 * vhi.Uninstall_Amount
				   ELSE 0
				   END) AS SaleAmount
			FROM
				ppm_vend_historical_info vhi
			WHERE
				vhi.SALES_DATE BETWEEN DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 YEAR), '%Y-%m-01') AND NOW()
				 	AND vhi.ORG_ID LIKE    CONCAT(Input_Org_ID ,'%')
	                AND VHI.Utility_ID like CONCAT(Input_Utility_ID ,'%')
					-- AND vhi.Recharge_Type IN ('0' , '1')
	                AND vhi.Recharge_Type IN ('0')
	                AND vhi.Receipt_State IN ('0')
			GROUP BY DATE_FORMAT(vhi.SALES_DATE, '%Y-%m');
	
	    -- 在游标循环到最后会将 done 设置为 1
		DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;
	     -- 注意这里，一定要重置done的值为 0
		set done = 0;
	    /*循环次数--赋初始值*/
	    SET i = 0 ; 
	 /* 打开游标 */
	    open cur_1;
		REPEAT 
		    FETCH cur_1 INTO s_DATE,s_Value;
	   		if not done then
	        
	            -- 获取ID
	            set ID = (SELECT REPLACE(UUID(),'-',''));
				-- 插入新记录
				INSERT INTO ppm_total_month_sales_amount(Utility_ID,ID,ORG_ID,Month,Month_Amount) 
				VALUES(Input_Utility_ID,ID,Input_Org_ID,s_DATE,s_Value);
			END IF;
	        
	        SET i = i + 1 ; /* 记录数 计数，在更新过程中判断使用 */
	 	UNTIL done END REPEAT;
		CLOSE cur_1;
	-- SELECT '管理机构：' + Input_Org_ID + ' 统计完毕！！'; /* 调试输出语句 */
END$$

DELIMITER ;