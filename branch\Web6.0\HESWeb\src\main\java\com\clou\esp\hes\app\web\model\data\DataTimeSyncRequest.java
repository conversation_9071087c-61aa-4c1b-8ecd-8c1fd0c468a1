package com.clou.esp.hes.app.web.model.data;

import java.util.Date;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.power7000g.core.excel.Excel;

/**
 * 
 * <AUTHOR>
 *
 */

public class DataTimeSyncRequest  extends BaseEntity {

    private static final long serialVersionUID = 1L;
  
    public DataTimeSyncRequest() {
    }    
    
    /**
     * 序列号
     */
    @Excel(name = "Serial Number", width = 30)
    private String sn;
    
    /**
     * meterId
     */
    private String id;
    
    /**
     * 时标
     */
    @Excel(name = "Request Time", width = 30)
    private Date tv; 
    
    /**
     * 电表时间
     */
    @Excel(name = "Meter Time", width = 30)
    private Date meterTv;  
    
    /**
     * 系统时间
     */
    @Excel(name = "System Time", width = 30)
    private Date systemTv; 
    
    /**
     * 同步时间
     */
    @Excel(name = "Sync Time", width = 30)
    private Date synTv;  
    
 
    private int synResult;
    
    /**
     * 同步结果
     */
    @Excel(name = "Sync Result", width = 30)    
    private String strSynResult ;   
    
    /**
     * 请求类型的数组，
     * 用于传参查询
     */
    private int[] synResultArrays;
    
    /**
     * 开始时间
     * 用于传参查询
     */
    private Date startDate;
    
    /**
     * 结束时间
     * 用于传参查询
     */
    private Date endDate;    
    
    /**
     * 失败原因
     */
    @Excel(name = "Failed Reason", width = 30)
    private String failedReason; 
    
    public String getSn() {
        return this.sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getTv() {
        return tv;
    }

    public void setTv(Date tv) {
        this.tv = tv;
    }
    
    public Date getMeterTv() {
        return this.meterTv;
    }

    public void setMeterTv(Date meterTv) {
        this.meterTv = meterTv;
    } 
    
    public Date getSystemTv() {
        return this.systemTv;
    }

    public void setSystemTv(Date systemTv) {
        this.systemTv = systemTv;
    }
    public Date getSynTv() {
        return this.synTv;
    }

    public void setSynTv(Date synTv) {
        this.synTv = synTv;
    }
    
    public int getSynResult() {
        return this.synResult;
    }

    public void setSynResult(int synResult) {
        this.synResult = synResult;
        if(this.synResult == 0)
            this.strSynResult = "Failed";
        else if(this.synResult == 1)
            this.strSynResult = "Success";
        else if(this.synResult == 2)
            this.strSynResult = "Normal";
    }
    
    
    public String getStrSynResult() {
        return this.strSynResult;
    }

    public void setStrSynResult(String strSynResult) {
        this.strSynResult = strSynResult;
    }     
    
    public int[] getSynResultArrays() {
        return synResultArrays;
    }

    public void setSynResultArrays(int[] synResultArrays) {
        this.synResultArrays = synResultArrays;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }    
    
    public String getFailedReason() {
        return this.failedReason;
    }

    public void setFailedReason(String failedReason) {
        this.failedReason = failedReason;
    }  
  
}
