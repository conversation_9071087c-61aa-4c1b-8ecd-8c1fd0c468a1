/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetCommunicator{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-06 08:04:54
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.asset;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.asset.AssetCommunicatorDao;
import com.clou.esp.hes.app.web.model.asset.AssetCommunicator;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.service.asset.AssetCommunicatorService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@Component
@Service("assetCommunicatorService")
public class AssetCommunicatorServiceImpl extends
		CommonServiceImpl<AssetCommunicator> implements
		AssetCommunicatorService {

	@Resource
	private AssetCommunicatorDao assetCommunicatorDao;

	@Autowired
	public void setCommonService() {
		super.setCommonService(assetCommunicatorDao);
	}

	public AssetCommunicatorServiceImpl() {
	}

	@Override
	public int batchInsert(List<AssetCommunicator> cList) {
		return assetCommunicatorDao.batchInsert(cList);
	}

	@Override
	public List<AssetCommunicator> getListGroupByFwVersion() {
		return assetCommunicatorDao.getListGroupByFwVersion();
	}

	
	@Override
	public List<AssetCommunicator> getBySns(List<String> sns) {
		return assetCommunicatorDao.getBySns(sns);
	}
	
	
	@Override
	public List<AssetCommunicator> getFwVersionGroupByModel(String modelTypeId) {
		return assetCommunicatorDao.getFwVersionGroupByModel(modelTypeId);
	}

	@Override
	public List<AssetCommunicator> getListNoGprs() {
		return assetCommunicatorDao.getListNoGprs();
	}

	@Override
	public List<Map<String, Object>> getSnNameByMaps(Map<String, Object> params) {
		// TODO Auto-generated method stub
		return assetCommunicatorDao.getSnNameByMaps(params);
	}

	@Override
	public List<AssetCommunicator> getListLimitTwenty(Map<String, Object> p) {
		// TODO Auto-generated method stub
		return assetCommunicatorDao.getListLimitTwenty(p);
	}

	@Override
	public JqGridResponseTo getForJqGridAdvanced(JqGridSearchTo jqGridSearchTo) {
		// TODO Auto-generated method stub
		
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		PageInfo<AssetCommunicator> pageInfo = new PageInfo<AssetCommunicator>(assetCommunicatorDao.getForJqGridAdvanced(jqGridSearchTo));
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
		
	}
	@Override
	public void relatePreMeterToDCU(AssetCommunicator dcu) {
		this.assetCommunicatorDao.relatePreMeterToDCU(dcu);
		
	}

	@Override
	public void updateRelateDCU(AssetCommunicator dcu) {
		this.assetCommunicatorDao.updateRelateDCU(dcu);
	}
}