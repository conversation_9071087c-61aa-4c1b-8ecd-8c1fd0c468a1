function dataFormatter(obj) {
    var temp;
    var max = 0;
    var min = 0;
    for (var year in obj) {
        temp = obj[year];
        for (var i = 0, l = temp.length; i < l; i++) {
            max = Math.max((temp[i] == '-' ?0:temp[i]), max);
            min = Math.min((temp[i] == '-' ?0:temp[i]), min);
            obj[year][i] = {
                name : pList[i],
                value : temp[i]
            }
        }
        obj[year+'max'] = Math.ceil(max / 100) * 100;
        obj[year+'min'] = Math.floor(min / 100) * 100;
    }
    return obj;
}
var pList = [
         '北京','天津','河北','山西','内蒙古','辽宁','吉林','黑龙江',
         '上海','江苏','浙江','安徽','福建','江西','山东','河南','湖北',
         '湖南','广东','广西','海南','重庆','四川','贵州','云南','西藏',
         '陕西','甘肃','青海','新疆','宁夏'
    ];
var dataPeople = dataFormatter({
    1987:[54.385,-3.7215,14.2049,67.2978,12.8015,0.0364,0.0017,60.0185,0.0013,-0.0088,-0.0021,0.3783,74.4804,73.358,68.5216,214.5266,62.1894,0.0035,-515.4337,-0.3961,615.08,0.00,-2845.1717,21.1903,21.0012,1E-04,-0.1941,12.3201,10.3655,971.1747,-971.1667 ],
    1988:[57.1491,-4.2217,3.1205,75.8062,10.8571,-0.0394,-0.0036,64.4743,0.0013,0.0027,0.0041,-0.0644,83.7506,50.4412,51.8223,237.1979,40.6486,-0.0079,138.5266,-0.0722,-0.6382,0.00,-2873.3272,-16.6705,11.3674,0.004,-0.0343,12.4604,12.661,981.8884,-981.8935],
    1989:[50.29,-4.5993,-7.3556,78.5695,9.6678,-0.0442,0.0102,67.6278,0.0002,-0.0188,0.005,-0.2444,94.9497,51.0692,-20.8574,259.8603,34.9442,-0.001,179.9817,0.0809,0.4062,0.00,-2897.0608,-13.0481,5.7817,-0.0027,0.0143,14.0836,12.9175,999.3519,-999.3504],
    1990:[50.2859,-4.2069,42.2014,53.7355,13.1682,-0.0483,0.0288,54.1539,-0.002,95.171,3.0887,0.2922,37.1807,49.218,69.4372,84.5741,65.7441,0.3187,100.8657,0.3798,11.5363,0.00,-2920.8799,30.5574,36.0878,3.418,40.981,24.7647,12.8175,1063.4826,-1033.0436],
    1991:[51.0951,-3.9216,36.8404,58.5051,19.1136,-0.0199,0.0079,64.2971,-0.0048,109.8266,8.1255,0.0124,39.9098,62.7092,36.0235,76.013,65.5016,-0.6381,178.063,29.5051,12.6283,0.00,-2938.9688,43.2298,47.4105,3.2455,53.0692,26.9077,14.9817,1080.6909,-1054.1512],
    1992:[53.2555,-3.7515,25.6707,60.2343,28.0514,0.0466,-0.0149,81.8314,0.0014,143.7108,18.4853,-0.4827,49.1473,86.0625,30.2203,49.5142,66.2012,-1.4657,243.2811,20.6362,15.0762,0.00,-2950.7421,59.9873,64.4504,3.2634,64.7113,26.0655,17.9637,1098.3603,-1071.8704],
    1993:[55.0697,-3.739,24.3862,57.0988,34.3876,0.0199,0.0248,101.1494,-0.0046,166.5805,21.5027,-0.0093,50.832,108.8473,21.5901,31.357,63.0162,-2.4158,355.0892,29.2036,19.2196,0.00,-2964.971,76.3891,83.0642,3.3432,73.2626,26.6638,20.3808,1114.4018,-1086.2439],
    1994:[56.8153,-3.9691,22.0478,54.2695,43.1379,-0.0421,-0.0135,114.363,0.0041,189.2624,22.5009,0.0879,56.1322,121.6964,18.3728,21.9762,62.0394,-3.3118,518.1241,37.9459,19.9997,0.00,-2985.6499,77.8287,102.1114,4.1551,79.435,34.8047,22.9523,1128.8318,-1101.4292],
    1995:[174.02,43.4236,16.5327,51.336,46.7893,58.0059,41.1343,124.1584,113.6261,197.5796,-50.6263,13.4337,72.374,124.4112,3.8379,-8.8492,44.8748,35.2568,598.7517,40.8899,21.5759,0.00,162.0639,88.5427,116.5043,4.446,82.0673,49.6152,24.751,1148.6155,-1124.2688],
    1996:[175.7699,45.5737,22.9662,49.7874,43.9981,59.216,30.8628,122.8962,114.5701,201.867,-57.0909,16.0165,50.3901,123.9676,-9.0477,-31.0568,48.6335,24.148,673.0064,43.4959,19.942,0.00,191.7666,95.8653,132.6151,4.7001,85.3145,39.1666,25.3501,1167.7901,-1154.5646],
    1997:[147.721,47.8868,16.8914,49.7127,37.4781,60.8626,27.8545,122.469,151.5376,199.6431,12.7158,17.783,44.8654,124.0067,175.2907,-49.7248,34.1717,20.8802,765.9638,44.5077,18.4711,-0.9164,165.3035,110.3146,149.4077,5.2643,87.3234,37.3613,29.5188,1189.0599,-1175.8071],
    1998:[148.2413,46.2594,13.6937,58.666,34.8051,66.5906,40.7652,130.9915,157.4155,198.9067,9.1442,31.8303,38.1807,120.3532,-33.5112,-58.6583,16.4163,19.8222,874.385,52.8062,19.691,0.3138,177.316,121.0629,160.6607,6.6131,94.9251,35.3631,32.6563,1210.4334,-1195.5965],
    1999:[150.8017,42.8335,11.8421,58.9192,32.4636,67.7656,41.9,131.1575,160.8796,203.914,7.5393,31.4845,32.4022,113.9748,-38.6648,-59.3428,-4.4899,11.3728,919.0251,55.3521,18.7876,2.6601,191.4441,128.0141,173.6112,8.2805,98.8471,35.5543,36.758,1230.7109,-1220.3656],
    2000:[243.4737,82.2894,3.0707,51.8206,71.0536,48.6559,54.7429,108.8937,319.3676,257.7226,94.7841,7.6462,105.3683,-15.4693,22.5377,-38.5066,24.0103,46.5345,1151.4896,26.39,28.0612,0.9097,194.5295,79.0863,164.3817,6.7659,71.8319,23.3492,36.584,1294.6786,-1237.5459],
    2001:[255.107,80.2525,-3.4876,51.7132,57.7746,46.9634,53.8989,95.6021,286.865,257.9968,93.1603,2.7738,119.0279,-25.9819,16.9878,-48.1747,18.3236,56.1682,1167.8538,30.0477,26.5011,-0.9137,203.3784,88.3083,180.3253,9.2999,69.4945,24.3236,39.5324,1310.0462,-1260.8746],
    2002:[280.1698,80.0133,-6.7155,48.5457,44.0791,47.569,49.6115,80.9504,290.7745,253.6751,111.0193,-30.9508,133.645,-40.9685,12.6844,-69.7571,9.7805,64.8038,1192.7873,30.6353,24.1105,-6.8236,198.5424,89.6025,192.0175,11.5577,63.3516,25.1551,41.8997,1332.4624,-1288.4158],
    2003:[301.9367,78.8371,-12.8295,45.858,30.4388,48.4016,45.3478,91.4371,369.231,242.0732,128.4163,-0.245,138.335,-48.3326,16.5379,-101.2709,1.4659,63.7985,1239.2665,26.9465,20.7418,-0.1032,170.6068,82.8159,200.1145,10.7887,47.524,21.6772,42.0317,1353.8088,-1308.5279],
    2004:[325.2408,85.5271,-13.0036,41.2184,24.1717,44.1738,47.0526,56.4998,389.6103,226.9513,142.7754,0.2319,143.9713,-79.3536,16.6211,-171.1906,14.6873,55.9323,1305.9078,5.5032,12.1208,-22.2309,129.7069,72.5083,185.2617,11.1719,30.5615,25.8139,40.4512,1372.6245,-1337.5804],
    2005:[353.8577,100.0051,-13.6599,60.5706,34.0898,31.8171,46.6316,51.7798,417.7405,222.1221,295.8905,-395.5056,150.0509,-72.5491,35.5714,-630.0384,-274.0631,-347.7883,1294.3612,-234.1746,8.9652,-371.1561,-430.1445,-137.7339,179.6842,9.4498,15.7137,-6.0848,39.0935,1421.47,-1366.3197],
    2006:[381.0356,122.7207,-41.1195,35.1167,14.681,60.6467,43.4877,31.1924,446.9173,232.2766,350.5707,-483.4505,148.8509,-118.0876,26.651,-787.1037,-345.3824,-459.3371,1255.2894,-255.2291,2.5642,-390.8692,-553.5224,-164.7302,167.2511,12.4194,-3.5759,-7.2473,37.6536,1449.2329,-1393.0334],
    2007:[416.7532,150.8626,-88.5592,0.6556,-8.7474,66.295,33.9426,4.2385,479.1384,270.919,400.6602,-557.7129,140.3431,-160.6615,21.4692,-1003.0009,-385.9108,-525.1436,1292.9476,-293.3316,-4.2566,-419.3229,-688.1705,-222.6786,143.1931,10.4133,-34.7142,-32.2398,30.1865,1482.1035,-1432.437],
    2008:[462.72,201.7258,-149.0351,-18.0563,-22.3084,68.5628,23.4355,-5.2806,497.4173,288.6671,432.1518,-605.8459,126.8603,-182.2407,24.5232,-1086.3572,-399.7908,-565.3489,1276.9115,-324.1705,-10.7252,-418.047,-769.7872,-244.0225,125.3654,5.6621,-51.9167,-49.4974,22.5488,1506.2831,-1464.2877],
    2009:[507.4753,243.3067,-182.5441,-36.09,5.0799,85.0265,20.5115,-18.8419,809.302,390.7682,559.821,-663.5286,167.3208,-201.3425,20.5492,-1176.4344,-421.8808,-601.3323,1764.0185,-347.643,-15.564,-416.6056,-799.6874,-553.7842,101.2231,7.1755,-125.8156,-147.2858,13.9844,1524.9359,-1496.0878],
    2010:[700.2997,309.4373,-104.0499,100.3752,18.8034,123.3245,23.1861,-9.7859,890.6798,402.4096,699.0459,-868.102,163.3099,-231.5451,51.811,-1394.6345,-420.9501,-499.0368,1919.4508,-721.4293,-27.0873,-418.4498,-956.2697,-709.9952,73.7803,7.0461,-138.8725,-152.1215,13.0282,1542.3941,-1531.4377],
    2011:[737.6843,354.6014,-104.2525,93.4996,15.7775,128.0161,22.8711,-0.315,928.1013,384.5499,681.6868,-918.5812,168.2389,-264.1216,46.0023,-1534.4461,-406.5616,-539.2863,1867.6563,-723.6353,-30.4827,-410.814,-1008.3928,-769.7188,68.5125,1.0264,-166.0765,-164.6892,10.585,1557.079,-1563.213]
});
//console.log(dataPeople);

var option = {
    //color : ["rgb(255,0,0)", "rgb(255,63,0)", "rgb(255,127,0)", "rgb(255,191,0)", "rgb(255,255,0)"],
    title : {
        text: '中国人口流动图示 (2011)',
        subtext: '1987-2011（单位：万人）',
        x:'center'
    },
    tooltip : {
        trigger: 'item'
    },
    legend: {
        data:[],
        //orient: 'vertical',
        x:'right',
        y: 55,
        //data:['1000以上','0 ~ 1000','-1000 ~ 0','-2000 ~ -1000','-2000以下'],
        selectedMode : false
    },
    dataRange: {
        //min: dataPeople['2011min'],
        //max: dataPeople['2011max'],
        color : ['red', 'yellow'],
        text:['高','低'],           // 文本，默认为数值文本
        calculable : true,
        textStyle: {
        }
    },
    toolbox: {
        show : true,
        orient : 'vertical',
        x: 'right',
        y: 'center',
        feature : {
            mark : {show: true},
            saveAsImage : {show: true}
        }
    },
    grid: {
        x:700,
        y:100
    },
     xAxis : [
        {
            type : 'category',
            boundaryGap : false,
            data : function (){
                var list = [];
                for (var i = 1987; i <= 2011; i++) {
                    list.push(i);
                }
                return list;
            }()
        }
    ],
    yAxis : [
        {
            type : 'value',
            name : '人口流动情况',
            splitArea : {show : true}
        }
    ],
    series : [
        {
            name: '全国人口变化',
            type: 'map',
            mapType: 'china',
            mapLocation: {
                x: 'left'
            },
            selectedMode : 'multiple',
            itemStyle:{
                normal:{label:{show:true}},
                emphasis:{
                    label:{show:true},
                    areaStyle: {color: '#eee'}
                }
            },
            data:dataPeople[2011]
        }
        /*,
        {name:'1000以上',type:'kener'},
        {name:'0 ~ 1000',type:'kener'},
        {name:'-1000 ~ 0',type:'kener'},
        {name:'-2000 ~ -1000',type:'kener'},
        {name:'-2000以下',type:'kener'}
        */
        // 图例示意占位
        
    ],
    animation: true
};


var _bdhmProtocol = (("https:" == document.location.protocol) ? " https://" : " http://");
document.write(unescape("%3Cscript src='" + _bdhmProtocol + "hm.baidu.com/h.js%3Fb78830c9a5dad062d08b90b2bc0cf5da' type='text/javascript'%3E%3C/script%3E"));
