<?xml version="1.0" encoding="UTF-8"?><ureport><cell expand="None" name="A1" row="1" col="1" col-span="10"><cell-style font-size="10" font-family="Times New Roman" bold="false" align="center" valign="middle"></cell-style><simple-value><![CDATA[None Purchase Report By Station Brekum]]></simple-value></cell><cell expand="None" name="A2" row="2" col="1"><cell-style font-size="10" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Station Name]]></simple-value></cell><cell expand="None" name="B2" row="2" col="2"><cell-style font-size="10" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Customer Name]]></simple-value></cell><cell expand="None" name="C2" row="2" col="3"><cell-style font-size="10" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Customer ID]]></simple-value></cell><cell expand="None" name="D2" row="2" col="4"><cell-style font-size="10" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Customer Address]]></simple-value></cell><cell expand="None" name="E2" row="2" col="5"><cell-style font-size="10" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Date　Installed]]></simple-value></cell><cell expand="None" name="F2" row="2" col="6"><cell-style font-size="10" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Meter NO.]]></simple-value></cell><cell expand="None" name="G2" row="2" col="7"><cell-style font-size="10" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Last Transaction Date]]></simple-value></cell><cell expand="None" name="H2" row="2" col="8"><cell-style font-size="10" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Last Transaction No.]]></simple-value></cell><cell expand="None" name="I2" row="2" col="9"><cell-style font-size="10" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Last amount Purchased GHS]]></simple-value></cell><cell expand="None" name="J2" row="2" col="10"><cell-style font-size="10" font-family="Times New Roman" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Days None Purchased]]></simple-value></cell><cell expand="Down" name="A3" row="3" col="1"><cell-style font-size="9" forecolor="0,0,0" font-family="Times New Roman" bold="false" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="select" property="orgName" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="B3" row="3" col="2"><cell-style font-size="9" forecolor="0,0,0" font-family="Times New Roman" bold="false" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="group" property="customerName" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="C3" row="3" col="3"><cell-style font-size="9" forecolor="0,0,0" font-family="Times New Roman" bold="false" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="group" property="customerId" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="D3" row="3" col="4"><cell-style font-size="9" forecolor="0,0,0" font-family="Times New Roman" bold="false" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="group" property="customerAddr" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="E3" row="3" col="5"><cell-style font-size="9" forecolor="0,0,0" font-family="Times New Roman" bold="false" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="group" property="dateInstalled" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="F3" row="3" col="6"><cell-style font-size="9" forecolor="0,0,0" font-family="Times New Roman" bold="false" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="group" property="meterNo" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="G3" row="3" col="7"><cell-style font-size="9" forecolor="0,0,0" font-family="Times New Roman" bold="false" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="group" property="lastTransDate" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="H3" row="3" col="8"><cell-style font-size="9" forecolor="0,0,0" font-family="Times New Roman" bold="false" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="group" property="lastTransNo" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="I3" row="3" col="9"><cell-style font-size="9" forecolor="0,0,0" font-family="Times New Roman" bold="false" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="group" property="lastGHS" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="J3" row="3" col="10" link-url="order(J,false)"><cell-style font-size="9" forecolor="0,0,0" font-family="Times New Roman" bold="false" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="report" aggregate="select" property="days" order="desc" mapping-type="simple"></dataset-value></cell><row row-number="1" height="18"/><row row-number="2" height="18" band="title"/><row row-number="3" height="18"/><column col-number="1" width="80"/><column col-number="2" width="80"/><column col-number="3" width="80"/><column col-number="4" width="86"/><column col-number="5" width="74"/><column col-number="6" width="74"/><column col-number="7" width="74"/><column col-number="8" width="74"/><column col-number="9" width="74"/><column col-number="10" width="80"/><datasource name="reportBean" type="spring" bean="nonePurchaseDataSource"><dataset name="report" type="bean" method="loadReportData" clazz="com.clou.esp.ppm.app.web.ureport.dataset.NonePurchaseDataSet"><field name="customerAddr"/><field name="customerId"/><field name="customerName"/><field name="dateInstalled"/><field name="days"/><field name="lastGHS"/><field name="lastTransDate"/><field name="lastTransNo"/><field name="meterNo"/><field name="orgName"/></dataset></datasource><paper type="A4" left-margin="90" right-margin="90"
    top-margin="72" bottom-margin="72" paging-mode="fitpage" fixrows="0"
    width="595" height="842" orientation="portrait" html-report-align="left" bg-image="" html-interval-refresh-value="0" column-enabled="false"></paper></ureport>