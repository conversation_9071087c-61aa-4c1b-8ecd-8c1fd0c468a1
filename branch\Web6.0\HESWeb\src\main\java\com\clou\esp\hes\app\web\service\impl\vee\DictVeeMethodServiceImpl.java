/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictVeeMethod{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2019-03-04 09:36:59
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.vee;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.vee.DictVeeMethodDao;
import com.clou.esp.hes.app.web.model.vee.DictVeeMethod;
import com.clou.esp.hes.app.web.service.vee.DictVeeMethodService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dictVeeMethodService")
public class DictVeeMethodServiceImpl  extends CommonServiceImpl<DictVeeMethod>  implements DictVeeMethodService {

	@Resource
	private DictVeeMethodDao dictVeeMethodDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dictVeeMethodDao);
    }
	@SuppressWarnings("rawtypes")
	public DictVeeMethodServiceImpl() {}
	
	
}