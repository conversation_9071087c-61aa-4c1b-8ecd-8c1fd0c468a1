package clouesp.hes.core.uci.soap.custom.webservice.common;


/**
 * @ClassName: BaudType
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年8月7日 上午10:39:41
 *
 */
public enum ComTypeEnum {

	COMTYPE_1(1,"RS485-1"),
	COMTYPE_2(2,"RS485-2"),
	COMTYPE_30(30,"RF"),	
	COMTYPE_31(31,"PLC");
	
	private ComTypeEnum(int index,String comType) {
		this.index = index;
		this.comType = comType;
	}
	
	public static ComTypeEnum parseComType(String comType) {
		for (ComTypeEnum type : values()) {
			if(type.comType.equals(comType))
				return type;
		}
		return null;
	}
	
	private int index;
	private String comType;
	
	public int getIndex() {
		return index;
	}
	public String getComType() {
		return comType;
	}
	
	
}
