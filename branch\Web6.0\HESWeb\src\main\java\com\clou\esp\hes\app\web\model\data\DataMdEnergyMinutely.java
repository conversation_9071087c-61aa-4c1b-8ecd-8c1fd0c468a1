/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataMeterDataEnergyMinutely{ } 
 * 
 * 摘    要： dataMeterDataEnergyMinutely
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-24 03:20:08
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.data;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DataMdEnergyMinutely  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DataMdEnergyMinutely() {
	}

	/**tv*/
	private java.util.Date tv;
	/**value1*/
	private java.lang.String value1;
	/**value2*/
	private java.lang.String value2;
	/**value3*/
	private java.lang.String value3;
	/**value4*/
	private java.lang.String value4;

	/**
	 * tv
	 * @return the value of DATA_METER_DATA_ENERGY_MINUTELY.TV
	 * @mbggenerated 2017-11-24 03:20:08
	 */
	public java.util.Date getTv() {
		return tv;
	}

	/**
	 * tv
	 * @param tv the value for DATA_METER_DATA_ENERGY_MINUTELY.TV
	 * @mbggenerated 2017-11-24 03:20:08
	 */
    	public void setTv(java.util.Date tv) {
		this.tv = tv;
	}
	/**
	 * value1
	 * @return the value of DATA_METER_DATA_ENERGY_MINUTELY.VALUE1
	 * @mbggenerated 2017-11-24 03:20:08
	 */
	public java.lang.String getValue1() {
		return value1;
	}

	/**
	 * value1
	 * @param value1 the value for DATA_METER_DATA_ENERGY_MINUTELY.VALUE1
	 * @mbggenerated 2017-11-24 03:20:08
	 */
    	public void setValue1(java.lang.String value1) {
		this.value1 = value1;
	}
	/**
	 * value2
	 * @return the value of DATA_METER_DATA_ENERGY_MINUTELY.VALUE2
	 * @mbggenerated 2017-11-24 03:20:08
	 */
	public java.lang.String getValue2() {
		return value2;
	}

	/**
	 * value2
	 * @param value2 the value for DATA_METER_DATA_ENERGY_MINUTELY.VALUE2
	 * @mbggenerated 2017-11-24 03:20:08
	 */
    	public void setValue2(java.lang.String value2) {
		this.value2 = value2;
	}
	/**
	 * value3
	 * @return the value of DATA_METER_DATA_ENERGY_MINUTELY.VALUE3
	 * @mbggenerated 2017-11-24 03:20:08
	 */
	public java.lang.String getValue3() {
		return value3;
	}

	/**
	 * value3
	 * @param value3 the value for DATA_METER_DATA_ENERGY_MINUTELY.VALUE3
	 * @mbggenerated 2017-11-24 03:20:08
	 */
    	public void setValue3(java.lang.String value3) {
		this.value3 = value3;
	}
	/**
	 * value4
	 * @return the value of DATA_METER_DATA_ENERGY_MINUTELY.VALUE4
	 * @mbggenerated 2017-11-24 03:20:08
	 */
	public java.lang.String getValue4() {
		return value4;
	}

	/**
	 * value4
	 * @param value4 the value for DATA_METER_DATA_ENERGY_MINUTELY.VALUE4
	 * @mbggenerated 2017-11-24 03:20:08
	 */
    	public void setValue4(java.lang.String value4) {
		this.value4 = value4;
	}

	public DataMdEnergyMinutely(java.util.Date tv 
	,java.lang.String value1 
	,java.lang.String value2 
	,java.lang.String value3 
	,java.lang.String value4 ) {
		super();
		this.tv = tv;
		this.value1 = value1;
		this.value2 = value2;
		this.value3 = value3;
		this.value4 = value4;
	}

}