/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class PpmAssetTariffGroup{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2019-09-25 03:56:32
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.tariff;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.tariff.PpmAssetTariffGroupDao;
import com.clou.esp.hes.app.web.model.tariff.PpmAssetTariffGroup;
import com.clou.esp.hes.app.web.service.tariff.PpmAssetTariffGroupService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("ppmAssetTariffGroupService")
public class PpmAssetTariffGroupServiceImpl  extends CommonServiceImpl<PpmAssetTariffGroup>  implements PpmAssetTariffGroupService {

	@Resource
	private PpmAssetTariffGroupDao ppmAssetTariffGroupDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(ppmAssetTariffGroupDao);
    }
	@SuppressWarnings("rawtypes")
	public PpmAssetTariffGroupServiceImpl() {}
	
	
}