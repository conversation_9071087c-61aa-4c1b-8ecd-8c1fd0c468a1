/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeterGroup{ } 
 * 
 * 摘    要： assetMeterGroup
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-01-16 01:58:30
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.asset;

import org.hibernate.validator.constraints.Length;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.clou.esp.hes.app.web.validation.ValidGroup1;

public class AssetMeterGroup  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public AssetMeterGroup() {
	}
	/**name*/
	@Length(min = 1, max = 64, message = "{AssetMeterGroup.name.length.error}", groups = ValidGroup1.class)
	private java.lang.String name;
	/**protocolId*/
//	@NotEmpty(message = "{AssetMeterGroup.protocolId.empty.error}", groups = ValidGroup1.class)
	private java.lang.String protocolId;
	/**Measurement: 1
            TOU: 2
            Limiter: 3*/
	private java.lang.String type;
	private java.lang.String typeName;
	/****/
	private Long meterNumber;
	/**introduction*/
	@Length(min = 0, max = 256, message = "{AssetMeterGroup.introduction.length.error}", groups = ValidGroup1.class)
	private java.lang.String introduction;
	/**
	 * 电表sn号
	 */
	private String sn;
	
	private String referenceGroupId;
	/**阶梯汇率激活时间*/
	private String stepTariffActivationTime;
	/**TOU激活时间*/
	private String touActivationTime;

	/**
	 * name
	 * @return the value of ASSET_METER_GROUP.NAME
	 * @mbggenerated 2018-01-16 01:58:30
	 */
	public java.lang.String getName() {
		return name;
	}

	/**
	 * name
	 * @param name the value for ASSET_METER_GROUP.NAME
	 * @mbggenerated 2018-01-16 01:58:30
	 */
    	public void setName(java.lang.String name) {
		this.name = name;
	}
	/**
	 * protocolId
	 * @return the value of ASSET_METER_GROUP.PROTOCOL_ID
	 * @mbggenerated 2018-01-16 01:58:30
	 */
	public java.lang.String getProtocolId() {
		return protocolId;
	}

	/**
	 * protocolId
	 * @param protocolId the value for ASSET_METER_GROUP.PROTOCOL_ID
	 * @mbggenerated 2018-01-16 01:58:30
	 */
    	public void setProtocolId(java.lang.String protocolId) {
		this.protocolId = protocolId;
	}
	/**
	 * Measurement: 1
            TOU: 2
            Limiter: 3
	 * @return the value of ASSET_METER_GROUP.TYPE
	 * @mbggenerated 2018-01-16 01:58:30
	 */
	public java.lang.String getType() {
		return type;
	}

	/**
	 * Measurement: 1
            TOU: 2
            Limiter: 3
	 * @param type the value for ASSET_METER_GROUP.TYPE
	 * @mbggenerated 2018-01-16 01:58:30
	 */
    	public void setType(java.lang.String type) {
		this.type = type;
	}
	/**
	 * introduction
	 * @return the value of ASSET_METER_GROUP.INTRODUCTION
	 * @mbggenerated 2018-01-16 01:58:30
	 */
	public java.lang.String getIntroduction() {
		return introduction;
	}

	/**
	 * introduction
	 * @param introduction the value for ASSET_METER_GROUP.INTRODUCTION
	 * @mbggenerated 2018-01-16 01:58:30
	 */
    	public void setIntroduction(java.lang.String introduction) {
		this.introduction = introduction;
	}
    	
    	
    	

	public Long getMeterNumber() {
		return meterNumber;
	}

	public void setMeterNumber(Long meterNumber) {
		this.meterNumber = meterNumber;
	}

	public AssetMeterGroup(String name, String protocolId, String type,
			Long meterNumber, String introduction) {
		super();
		this.name = name;
		this.protocolId = protocolId;
		this.type = type;
		this.meterNumber = meterNumber;
		this.introduction = introduction;
	}

	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}

	public String getReferenceGroupId() {
		return referenceGroupId;
	}

	public void setReferenceGroupId(String referenceGroupId) {
		this.referenceGroupId = referenceGroupId;
	}

	public String getStepTariffActivationTime() {
		return stepTariffActivationTime;
	}

	public void setStepTariffActivationTime(String stepTariffActivationTime) {
		this.stepTariffActivationTime = stepTariffActivationTime;
	}

	public String getTouActivationTime() {
		return touActivationTime;
	}

	public void setTouActivationTime(String touActivationTime) {
		this.touActivationTime = touActivationTime;
	}

	public java.lang.String getTypeName() {
		return typeName;
	}

	public void setTypeName(java.lang.String typeName) {
		this.typeName = typeName;
	}

	

}