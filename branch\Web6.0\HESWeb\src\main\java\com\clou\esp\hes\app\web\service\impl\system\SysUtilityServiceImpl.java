/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysUtility{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-10-27 09:17:08
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.system;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.system.SysUtilityDao;
import com.clou.esp.hes.app.web.model.system.SysUtility;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.clou.esp.hes.app.web.service.system.SysUtilityService;

@Component
@Service("sysUtilityService")
public class SysUtilityServiceImpl  extends CommonServiceImpl<SysUtility>  implements SysUtilityService {

	@Resource
	private SysUtilityDao sysUtilityDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(sysUtilityDao);
    }
	@SuppressWarnings("rawtypes")
	public SysUtilityServiceImpl() {}
	
	
}