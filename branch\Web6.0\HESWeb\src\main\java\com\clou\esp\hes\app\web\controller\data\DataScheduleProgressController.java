/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataScheduleProgress{ } 
 * 
 * 摘    要： dataScheduleProgress
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-12-01 06:03:46
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.data;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.CreatePdf;
import com.clou.esp.hes.app.web.core.util.DateTimeFormatterUtil;
import com.clou.esp.hes.app.web.core.util.ExcelUtils;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.OrganizationUtils;
import com.clou.esp.hes.app.web.core.util.ProgressMissDataUtil;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.excel.Excel;
import com.power7000g.core.excel.ExcelDataFormatter;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;
import com.clou.esp.hes.app.web.model.report.MeterDataReport;
import com.clou.esp.hes.app.web.model.report.ProgressDelayDataReport;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.model.vee.AssetVeeRule;
import com.clou.esp.hes.app.web.model.asset.AssetCommunicator;
import com.clou.esp.hes.app.web.model.asset.AssetMeasurementProfile;
import com.clou.esp.hes.app.web.model.asset.AssetMeasurementProfileDi;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroupMap;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroupValue;
import com.clou.esp.hes.app.web.model.data.DataIntegrity;
import com.clou.esp.hes.app.web.model.data.DataScheduleProgress;
import com.clou.esp.hes.app.web.model.data.DataScheduleProgress1;
import com.clou.esp.hes.app.web.model.dict.DictCommunicationType;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;
import com.clou.esp.hes.app.web.model.dict.DictManufacturer;
import com.clou.esp.hes.app.web.model.dict.DictProfile;
import com.clou.esp.hes.app.web.service.asset.AssetCommunicatorService;
import com.clou.esp.hes.app.web.service.asset.AssetMeasurementProfileDiService;
import com.clou.esp.hes.app.web.service.asset.AssetMeasurementProfileService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterGroupMapService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.data.DataScheduleProgressService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemService;
import com.clou.esp.hes.app.web.service.dict.DictManufacturerService;
import com.clou.esp.hes.app.web.service.dict.DictProfileService;
import com.clou.esp.hes.app.web.service.system.SysOrgService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

/**
 * <AUTHOR>
 * @时间：2017-12-01 06:03:46
 * @描述：dataScheduleProgress类
 */
@Controller
@RequestMapping("/dataScheduleProgressController")
public class DataScheduleProgressController extends BaseController{

 	@Resource
    private DataScheduleProgressService dataScheduleProgressService;
	@Resource
    private AssetCommunicatorService assetCommunicatorService;
	@Resource
    private AssetMeterService assetMeterService;
	@Resource
    private SysOrgService sysOrgService;
	@Resource
    private DictProfileService dictProfileService;
	@Resource
    private AssetMeterGroupMapService assetMeterGroupMapService;
	@Resource
    private AssetMeasurementProfileService assetMeasurementProfileService;
	@Resource
    private AssetMeasurementProfileDiService assetMeasurementProfileDiService;
	@Resource
    private DictDataitemService dictDataitemService;
	
	@Resource
    private DictManufacturerService dictManufacturerService;
	

	/**
	 * 跳转到dataScheduleProgress列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/data/dataScheduleProgressList");
    }

	/**
	 * 跳转到dataScheduleProgress新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dataScheduleProgress")
	public ModelAndView dataScheduleProgress(DataScheduleProgress dataScheduleProgress,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dataScheduleProgress.getId())){
			try {
                dataScheduleProgress=dataScheduleProgressService.getEntity(dataScheduleProgress.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dataScheduleProgress", dataScheduleProgress);
		}
		return new ModelAndView("/data/dataScheduleProgress");
	}


	/**
	 * dataScheduleProgress查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
        	 
        	 jqGridSearchTo.getMap().put("tv", DateUtils.date2Str(
						DateUtils.parseDate(jqGridSearchTo.getMap().get("tv").toString(), ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
						new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")));
        	 
        	 if(StringUtils.isEmpty(jqGridSearchTo.getMap().get("serizlNo"))){
        		 jqGridSearchTo.getMap().put("searchType", "");
        	 }
        	 
        	 List<AssetMeter> assetMeterLists = null;
        	 List<String> meterSns = null;
        	 if(!StringUtils.isEmpty(jqGridSearchTo.getMap().get("searchType"))
        			 && "Meter".equals(jqGridSearchTo.getMap().get("searchType"))
        			 && !StringUtils.isEmpty(jqGridSearchTo.getMap().get("serizlNo"))){
        		
        		 
        			AssetMeter entity = new AssetMeter();
     				entity.setSn(jqGridSearchTo.getMap().get("serizlNo").toString());
     				AssetMeter entity1 = assetMeterService.get(entity);
        		 if(entity1 != null){
        			 meterSns = new ArrayList();
        			 meterSns.add(entity1.getId());
        		 }
        		 jqGridSearchTo.getMap().put("snLists", meterSns);
        	 }else if(!StringUtils.isEmpty(jqGridSearchTo.getMap().get("searchType"))
        			 && "Commnuicator".equals(jqGridSearchTo.getMap().get("searchType"))
        			 && !StringUtils.isEmpty(jqGridSearchTo.getMap().get("serizlNo"))){
        		 
        	
     			AssetCommunicator commTemp = new AssetCommunicator();
     			commTemp.setSn(jqGridSearchTo.getMap().get("serizlNo").toString());
     			AssetCommunicator newComm = assetCommunicatorService.get(commTemp);
     			if(StringUtil.isNotEmpty(newComm)){
     				AssetMeter entity = new AssetMeter();
     				entity.setCommunicatorId(newComm.getId());
     				assetMeterLists = assetMeterService.getList(entity);
     			}
     		
     			if(assetMeterLists != null && assetMeterLists.size() > 0 ){
     				 meterSns = new ArrayList();
     				for(AssetMeter assetMeter : assetMeterLists){
     					
     					meterSns.add(assetMeter.getId());
     				}
     				jqGridSearchTo.getMap().put("snLists", meterSns);
     			}else{
     				
     				jqGridSearchTo.getMap().put("snLists", meterSns);
     			}
        	 }
        	 
        	SysUser su = TokenManager.getToken();
 			List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
 			jqGridSearchTo.getMap().put("orgIdList", orgIdList);
 			 if(StringUtils.isEmpty(jqGridSearchTo.getSidx()) || "null".equals(jqGridSearchTo.getSidx())){
        		 jqGridSearchTo.setSidx("progress");
        	 }
 			 
 			String formatter=ResourceUtil.getSessionattachmenttitle("local.date.formatter");
			String startTime = (String)jqGridSearchTo.getMap().get("deviceCreateTime_start");
			if(!StringUtils.isEmpty(startTime)) {
				jqGridSearchTo.getMap().put("deviceCreateTime_start", DateUtils.parseDate(startTime, formatter));
			}
			String endTime = (String)jqGridSearchTo.getMap().get("deviceCreateTime_end");
			if(!StringUtils.isEmpty(endTime)) {
				jqGridSearchTo.getMap().put("deviceCreateTime_end", org.apache.commons.lang.time.DateUtils.addDays(DateUtils.parseDate(endTime, formatter),1));
			}
 			//progressDelay
            j=dataScheduleProgressService.getForJqGrid(jqGridSearchTo);
            if(j.getRows() != null && jqGridSearchTo.getMap().get("progressDelay") == null){
            	List<Object> list = (List<Object>) j.getRows();
     			for (int i = 0; i < list.size(); i++) {
     				Map<Object, Object> entity = (Map<Object, Object>) list.get(i);
     				//System.out.println(entity.get("progress").toString());	
     				if(entity.get("progress") != null){
     					if(DateTimeFormatterUtil.compare_date(jqGridSearchTo.getMap().get("tv").toString(), entity.get("progress").toString()) == 1){
     						entity.put("progressDelay","1");
     					}else{
     						entity.put("progressDelay","0");
     					}
     				}
     			
     			}
             }else if(j.getRows() != null && "1".equals(jqGridSearchTo.getMap().get("progressDelay"))){
            	List<Object> list = (List<Object>) j.getRows();
      			for (int i = 0; i < list.size(); i++) {
      				Map<Object, Object> entity = (Map<Object, Object>) list.get(i);
      				entity.put("progressDelay","1");
      				//System.out.println(entity.get("progress").toString());
      			}
             }else if(j.getRows() != null && "0".equals(jqGridSearchTo.getMap().get("progressDelay"))){
            	 List<Object> list = (List<Object>) j.getRows();
       			for (int i = 0; i < list.size(); i++) {
       				Map<Object, Object> entity = (Map<Object, Object>) list.get(i);
       				entity.put("progressDelay","0");
       				//System.out.println(entity.get("progress").toString());
       			}
             }
            
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
                       
	private boolean  isNotMonthLyMeasurement(Map<Object, Object> entity) {
		

		AssetMeter assetMeter = new AssetMeter();
		assetMeter.setSn(entity.get("meterSn").toString());
		assetMeter = assetMeterService.get(assetMeter);
		
		AssetMeterGroupMap group = new AssetMeterGroupMap();
		group.setId(assetMeter.getId());
		group.setType("1");
		group=	assetMeterGroupMapService.get(group);
        if(group == null){
        	return false;
        }
		
		AssetMeasurementProfile assetMeasurementProfile = new AssetMeasurementProfile();
		assetMeasurementProfile.setMgId(group.getGroupId());
		assetMeasurementProfile.setProfileId(entity.get("profileId").toString());
		List<AssetMeasurementProfile> dis = assetMeasurementProfileService
				.getList(assetMeasurementProfile);
		if(dis !=null && dis.size() > 0){
			if(("Monthly").equals(dis.get(0).getProfileCycleType())){
				return true;
			}
		}
		return false;
	}
	
	
	private boolean  isNotMonthLyMeasurement(DataScheduleProgress entity) {
		

		AssetMeterGroupMap group = new AssetMeterGroupMap();
		group.setId(entity.getDeviceId());
		group.setType("1");
		group=	assetMeterGroupMapService.get(group);
        if(group == null){
        	return false;
        }
		
		AssetMeasurementProfile assetMeasurementProfile = new AssetMeasurementProfile();
		assetMeasurementProfile.setMgId(group.getGroupId());
		assetMeasurementProfile.setProfileId(entity.getProfileId());
		List<AssetMeasurementProfile> dis = assetMeasurementProfileService
				.getList(assetMeasurementProfile);
		if(dis !=null && dis.size() > 0){
			if(("Monthly").equals(dis.get(0).getProfileCycleType())){
				return true;
			}
		}
		return false;
	}
	
	public String initDateByMonth(String tv){
		  SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		  
		Calendar calendar = Calendar.getInstance();
		try {
			calendar.setTime(sdf.parse(tv));
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		
		return sdf.format(calendar.getTime());
	}

    
    /**
     * 删除dataScheduleProgress信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DataScheduleProgress dataScheduleProgress,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dataScheduleProgressService.deleteById(dataScheduleProgress.getId())>0){
                j.setMsg("删除成功");
            }else{
                j.setSuccess(false);
                j.setMsg("删除失败");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
    
    /**
     * 保存dataScheduleProgress信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DataScheduleProgress dataScheduleProgress,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DataScheduleProgress t=new  DataScheduleProgress();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dataScheduleProgress.getId())){
        	t=dataScheduleProgressService.getEntity(dataScheduleProgress.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dataScheduleProgress, t);
				dataScheduleProgressService.update(t);
				j.setMsg("修改成功");
				
			}else{
	            dataScheduleProgressService.save(dataScheduleProgress);
	            j.setMsg("创建成功");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
    
    
    /**
	 * 导出
	 * 
	 * @param delay data
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "getExcelDelayData")
	@ResponseBody
	public void getExcelDelayData(String tv, String profileId,
			String searchType, String progressDelay, String serizlNo,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			Map<String, Object> extData = new HashMap<String, Object>();
			extData.put("tv",  DateUtils.date2Str(
					DateUtils.parseDate(tv, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
					new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")));
			extData.put("monthTv",initDateByMonth(extData.get("tv").toString()));
			
			extData.put("profileId" , profileId);
			if("0".equals(progressDelay)){
				progressDelay = "2";
			}
			extData.put("progressDelay" , progressDelay);
			extData.put("searchType" , searchType);
			extData.put("serizlNo" , serizlNo);
            // deviceIds 获取
			 List<AssetMeter> assetMeterLists = null;
        	 List<String> meterSns = null;
        	 if(!StringUtils.isEmpty(searchType)
        			 && "Meter".equals(searchType)
        			 && !StringUtils.isEmpty(serizlNo)){
        		
        		 
        			AssetMeter entity = new AssetMeter();
     				entity.setSn(serizlNo);
     				AssetMeter entity1 = assetMeterService.get(entity);
        		 if(entity1 != null){
        			 meterSns = new ArrayList();
        			 meterSns.add(entity1.getId());
        		 }
        		 extData.put("snLists", meterSns);
        	 }else if(!StringUtils.isEmpty(searchType)
        			 && "Commnuicator".equals(searchType)
        			 && !StringUtils.isEmpty(serizlNo)){
        		 
        	
     			AssetCommunicator commTemp = new AssetCommunicator();
     			commTemp.setSn(serizlNo);
     			AssetCommunicator newComm = assetCommunicatorService.get(commTemp);
     			if(StringUtil.isNotEmpty(newComm)){
     				AssetMeter entity = new AssetMeter();
     				entity.setCommunicatorId(newComm.getId());
     				assetMeterLists = assetMeterService.getList(entity);
     			}
     		
     			if(assetMeterLists != null && assetMeterLists.size() > 0 ){
     				for(AssetMeter assetMeter : assetMeterLists){
     					 meterSns = new ArrayList();
     					meterSns.add(assetMeter.getId());
     				}
     				 extData.put("snLists", meterSns);
     			}else{
     				
     				 extData.put("snLists", meterSns);
     			}
        	 }
        	 
        	SysUser su = TokenManager.getToken();
 			List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
 			extData.put("orgIdList", orgIdList);
 			DataScheduleProgress dataScheduleProgress = new DataScheduleProgress();
 			dataScheduleProgress.setExtData(extData);
 			List<DataScheduleProgress> list = dataScheduleProgressService.getList(dataScheduleProgress);
 			 if(StringUtils.isEmpty(progressDelay)){
	                if(list != null && list.size() > 0){
	                	for (DataScheduleProgress dataScheduleProgress1 : list) {
	 	     			
	 	     				if(dataScheduleProgress1.getProgress() != null){
	 	     					if(DateTimeFormatterUtil.compare_date(DateUtils.date2Str(
	 	     									DateUtils.parseDate(tv, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
	 	     									new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")), 
	 	     									DateUtils.date2Str(
	 	     											dataScheduleProgress1.getProgress(),
	 	 	 	     									new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"))
	 	     									
	 	     									
	 	     							) == 1){
	 	     						dataScheduleProgress1.setProgressDelay("Yes");
	 	     					}else{
	 	     						dataScheduleProgress1.setProgressDelay("No");
	 	     					}
	 	     				}
	 	     			
	 	     			}
	                }
	     			
	             }else if(!StringUtils.isEmpty(progressDelay) && "1".equals(progressDelay)){
	            	if(list != null && list.size() > 0){
	                	for (DataScheduleProgress dataScheduleProgress1 : list) {
	                		dataScheduleProgress1.setProgressDelay("Yes");
	                	}
	            	}
	             }else if(!StringUtils.isEmpty(progressDelay) && "2".equals(progressDelay)){
	            	if(list != null && list.size() > 0){
	                	for (DataScheduleProgress dataScheduleProgress1 : list) {
	                		dataScheduleProgress1.setProgressDelay("No");
	                	}
	            	}
	             }
 		
			if (list.size() <= 0) {
				DataScheduleProgress d = new DataScheduleProgress();
				list.add(d);
			}
			ExcelDataFormatter edf = new ExcelDataFormatter();
			
			Map<String, String> progress = new HashMap<String, String>();
			progress.put("progress", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
			edf.set("progress", progress);
			
			DictProfile dictProfile = new DictProfile();
			dictProfile.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
			List<DictProfile> dmfList = dictProfileService.getList(dictProfile);

			Map<String, String> dmf = new HashMap<String, String>();
			for (DictProfile d : dmfList) {
				dmf.put(d.getId(), d.getName());
			}
			edf.set("profileId", dmf);
		
			ExcelUtils.writeToFile(list, edf, "progressDelay.xlsx", response,
					ValidGroup1.class);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	
	  /**
		 * 导出
		 * 
		 * @param delay data
		 * @param request
		 * @param response
		 */
		@RequestMapping(value = "getExcelDelayData1")
		@ResponseBody
		public void getExcelDelayData1(String tv, String profileId,
				String searchType, String progressDelay, String serizlNo,String deviceCreateTime_start,String deviceCreateTime_end,
				HttpServletRequest request, HttpServletResponse response) {
			try {
				Map<String, Object> extData = new HashMap<String, Object>();
				extData.put("tv",  DateUtils.date2Str(
						DateUtils.parseDate(tv, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
						new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")));
				extData.put("monthTv",initDateByMonth(extData.get("tv").toString()));
				
				extData.put("profileId" , profileId);
				if("0".equals(progressDelay)){
					progressDelay = "2";
				}
				extData.put("progressDelay" , progressDelay);
				extData.put("searchType" , searchType);
				extData.put("serizlNo" , serizlNo);
	            // deviceIds 获取
				 List<AssetMeter> assetMeterLists = null;
	        	 List<String> meterSns = null;
	        	 if(!StringUtils.isEmpty(searchType)
	        			 && "Meter".equals(searchType)
	        			 && !StringUtils.isEmpty(serizlNo)){
	        		
	        		 
	        			AssetMeter entity = new AssetMeter();
	     				entity.setSn(serizlNo);
	     				AssetMeter entity1 = assetMeterService.get(entity);
	        		 if(entity1 != null){
	        			 meterSns = new ArrayList();
	        			 meterSns.add(entity1.getId());
	        		 }
	        		 extData.put("snLists", meterSns);
	        	 }else if(!StringUtils.isEmpty(searchType)
	        			 && "Commnuicator".equals(searchType)
	        			 && !StringUtils.isEmpty(serizlNo)){
	        		 
	        	
	     			AssetCommunicator commTemp = new AssetCommunicator();
	     			commTemp.setSn(serizlNo);
	     			AssetCommunicator newComm = assetCommunicatorService.get(commTemp);
	     			if(StringUtil.isNotEmpty(newComm)){
	     				AssetMeter entity = new AssetMeter();
	     				entity.setCommunicatorId(newComm.getId());
	     				assetMeterLists = assetMeterService.getList(entity);
	     			}
	     		
	     			if(assetMeterLists != null && assetMeterLists.size() > 0 ){
	     				meterSns = Lists.newArrayList();
	     				for(AssetMeter assetMeter : assetMeterLists){
	     					meterSns.add(assetMeter.getId());
	     				}
	     				 extData.put("snLists", meterSns);
	     			}else{
	     				
	     				 extData.put("snLists", meterSns);
	     			}
	        	 }
	        	 
	        	String formatter=ResourceUtil.getSessionattachmenttitle("local.date.formatter");
	 			if(!StringUtils.isEmpty(deviceCreateTime_start)) {
	 				extData.put("deviceCreateTime_start", DateUtils.parseDate(deviceCreateTime_start, formatter));
	 			}
	 			if(!StringUtils.isEmpty(deviceCreateTime_end)) {
	 				extData.put("deviceCreateTime_end", org.apache.commons.lang.time.DateUtils.addDays(DateUtils.parseDate(deviceCreateTime_end, formatter),1));
	 			}
		 			
	        	SysUser su = TokenManager.getToken();
	 			List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
	 			extData.put("orgIdList", orgIdList);
	 			DataScheduleProgress dataScheduleProgress = new DataScheduleProgress();
	 			dataScheduleProgress.setExtData(extData);
	 			List<DataScheduleProgress1> list = dataScheduleProgressService.getList1(dataScheduleProgress);
	 			
	 			
 			    if(StringUtils.isEmpty(progressDelay)){
 	                if(list != null && list.size() > 0){
 	                	for (DataScheduleProgress1 dataScheduleProgress1 : list) {
 	 	     			
 	 	     				if(dataScheduleProgress1.getProgress() != null){
 	 	     					if(DateTimeFormatterUtil.compare_date(DateUtils.date2Str(
 	 	     									DateUtils.parseDate(tv, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
 	 	     									new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")), 
 	 	     									DateUtils.date2Str(
 	 	     											dataScheduleProgress1.getProgress(),
 	 	 	 	     									new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"))
 	 	     									
 	 	     									
 	 	     							) == 1){
 	 	     						dataScheduleProgress1.setProgressDelay("Yes");
 	 	     					}else{
 	 	     						dataScheduleProgress1.setProgressDelay("No");
 	 	     					}
 	 	     				}
 	 	     			
 	 	     			}
 	                }
 	     			
 	             }else if(!StringUtils.isEmpty(progressDelay) && "1".equals(progressDelay)){
 	            	if(list != null && list.size() > 0){
 	                	for (DataScheduleProgress1 dataScheduleProgress1 : list) {
 	                		dataScheduleProgress1.setProgressDelay("Yes");
 	                	}
 	            	}
 	             }else if(!StringUtils.isEmpty(progressDelay) && "2".equals(progressDelay)){
 	            	if(list != null && list.size() > 0){
 	                	for (DataScheduleProgress1 dataScheduleProgress1 : list) {
 	                		dataScheduleProgress1.setProgressDelay("No");
 	                	}
 	            	}
 	             }
	 			
	 		
				if (list.size() <= 0) {
					DataScheduleProgress1 d = new DataScheduleProgress1();
					list.add(d);
				}
				ExcelDataFormatter edf = new ExcelDataFormatter();
				
				Map<String, String> progress = new HashMap<String, String>();
				progress.put("progress", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
				edf.set("progress", progress);
				
				DictProfile dictProfile = new DictProfile();
				dictProfile.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
				List<DictProfile> dmfList = dictProfileService.getList(dictProfile);

				Map<String, String> dmf = new HashMap<String, String>();
				for (DictProfile d : dmfList) {
					dmf.put(d.getId(), d.getName());
				}
				edf.set("profileId", dmf);
				
			
				List<DictManufacturer> dmfList1 = dictManufacturerService
						.getAllList();
				Map<String, String> dmf1 = new HashMap<String, String>();
				for (DictManufacturer d : dmfList1) {
					dmf1.put(d.getId(), d.getName());
				}
				edf.set("mfrId", dmf1);

				Map<String, String> trt = new HashMap<String, String>();
				trt.put("1", "Success");
				trt.put("0", "Failed");
				edf.set("taskState", trt);

				Map<String, String> lastTask = new HashMap<String, String>();
				lastTask.put("lastTaskTv", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
				edf.set("lastTaskTv", lastTask);
				Map<String, String> updateTv = new HashMap<String, String>();
				updateTv.put("updateTv", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
				edf.set("updateTv", updateTv);
				
				Map<String, String> deviceCreateTime = new HashMap<String, String>();
				deviceCreateTime.put("deviceCreateTime", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
				edf.set("deviceCreateTime", deviceCreateTime);
				
				Map<String, String> comStatus = new HashMap<String, String>();
				comStatus.put("1", "Online");
				comStatus.put("0", "Offline");
				edf.set("comStatus", comStatus);
						
				ExcelUtils.writeToFile(list, edf, "progressDelay.xlsx", response,
						ValidGroup1.class);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		
		/**
		 * 打印
		 * 
		 * @param testUser
		 * @param response
		 */
		@RequestMapping(value = "printDelayData1")
		@ResponseBody
		public void printDelayData1(String tv, String profileId,
				String searchType, String progressDelay, String serizlNo,String deviceCreateTime_start,String deviceCreateTime_end,
				HttpServletRequest request, HttpServletResponse response) {
			try {
				Map<String, Object> extData = new HashMap<String, Object>();
				extData.put("tv",  DateUtils.date2Str(
						DateUtils.parseDate(tv, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
						new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")));
				extData.put("monthTv",initDateByMonth(extData.get("tv").toString()));
				extData.put("profileId" , profileId);
				if("0".equals(progressDelay)){
					progressDelay = "2";
				}
				extData.put("progressDelay" , progressDelay);
				extData.put("searchType" , searchType);
				extData.put("serizlNo" , serizlNo);
				//获取采集停留时间段  end
				
				
				 List<AssetMeter> assetMeterLists = null;
	        	 List<String> meterSns = null;
	        	 if(!StringUtils.isEmpty(searchType)
	        			 && "Meter".equals(searchType)
	        			 && !StringUtils.isEmpty(serizlNo)){
	        		
	        		 
	        			AssetMeter entity = new AssetMeter();
	     				entity.setSn(serizlNo);
	     				AssetMeter entity1 = assetMeterService.get(entity);
	        		 if(entity1 != null){
	        			 meterSns = new ArrayList();
	        			 meterSns.add(entity1.getId());
	        		 }
	        		 extData.put("snLists", meterSns);
	        	 }else if(!StringUtils.isEmpty(searchType)
	        			 && "Commnuicator".equals(searchType)
	        			 && !StringUtils.isEmpty(serizlNo)){
	        		 
	        	
	     			AssetCommunicator commTemp = new AssetCommunicator();
	     			commTemp.setSn(serizlNo);
	     			AssetCommunicator newComm = assetCommunicatorService.get(commTemp);
	     			if(StringUtil.isNotEmpty(newComm)){
	     				AssetMeter entity = new AssetMeter();
	     				entity.setCommunicatorId(newComm.getId());
	     				assetMeterLists = assetMeterService.getList(entity);
	     			}
	     		
	     			if(assetMeterLists != null && assetMeterLists.size() > 0 ){
	     				for(AssetMeter assetMeter : assetMeterLists){
	     					 meterSns = new ArrayList();
	     					meterSns.add(assetMeter.getId());
	     				}
	     				 extData.put("snLists", meterSns);
	     			}else{
	     				
	     				 extData.put("snLists", meterSns);
	     			}
	        	 }
	        	 
	        	String formatter=ResourceUtil.getSessionattachmenttitle("local.date.formatter");
	 			if(!StringUtils.isEmpty(deviceCreateTime_start)) {
	 				extData.put("deviceCreateTime_start", DateUtils.parseDate(deviceCreateTime_start, formatter));
	 			}
	 			if(!StringUtils.isEmpty(deviceCreateTime_end)) {
	 				extData.put("deviceCreateTime_end", org.apache.commons.lang.time.DateUtils.addDays(DateUtils.parseDate(deviceCreateTime_end, formatter),1));
	 			}
	 			
	        	SysUser su = TokenManager.getToken();
	 			List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
	 			extData.put("orgIdList", orgIdList);
	 			DataScheduleProgress dataScheduleProgress = new DataScheduleProgress();
	 			dataScheduleProgress.setExtData(extData);
	 			List<DataScheduleProgress1> list = dataScheduleProgressService.getList1(dataScheduleProgress);

	 			 if(StringUtils.isEmpty(progressDelay)){
	 	                if(list != null && list.size() > 0){
	 	                	for (DataScheduleProgress1 dataScheduleProgress1 : list) {
	 	 	     			
	 	 	     				if(dataScheduleProgress1.getProgress() != null){
	 	 	     					if(DateTimeFormatterUtil.compare_date(DateUtils.date2Str(
	 	 	     									DateUtils.parseDate(tv, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
	 	 	     									new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")), 
	 	 	     									DateUtils.date2Str(
	 	 	     											dataScheduleProgress1.getProgress(),
	 	 	 	 	     									new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"))
	 	 	     									
	 	 	     									
	 	 	     							) == 1){
	 	 	     						dataScheduleProgress1.setProgressDelay("Yes");
	 	 	     					}else{
	 	 	     						dataScheduleProgress1.setProgressDelay("No");
	 	 	     					}
	 	 	     				}
	 	 	     			
	 	 	     			}
	 	                }
	 	     			
	 	             }else if(!StringUtils.isEmpty(progressDelay) && "1".equals(progressDelay)){
	 	            	if(list != null && list.size() > 0){
	 	                	for (DataScheduleProgress1 dataScheduleProgress1 : list) {
	 	                		dataScheduleProgress1.setProgressDelay("Yes");
	 	                	}
	 	            	}
	 	             }else if(!StringUtils.isEmpty(progressDelay) && "2".equals(progressDelay)){
	 	            	if(list != null && list.size() > 0){
	 	                	for (DataScheduleProgress1 dataScheduleProgress1 : list) {
	 	                		dataScheduleProgress1.setProgressDelay("No");
	 	                	}
	 	            	}
	 	             }
	 			
				if (list.size() <= 0) {
					DataScheduleProgress1 d = new DataScheduleProgress1();
					list.add(d);
				}
				ExcelDataFormatter edf = new ExcelDataFormatter();
				
				Map<String, String> progress = new HashMap<String, String>();
				progress.put("progress", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
				edf.set("progress", progress);
				
				
				
				DictProfile dictProfile = new DictProfile();
				dictProfile.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
				List<DictProfile> dmfList = dictProfileService.getList(dictProfile);

				Map<String, String> dmf = new HashMap<String, String>();
				for (DictProfile d : dmfList) {
					dmf.put(d.getId(), d.getName());
				}
				edf.set("profileId", dmf);
				
			
				List<DictManufacturer> dmfList1 = dictManufacturerService
						.getAllList();
				Map<String, String> dmf1 = new HashMap<String, String>();
				for (DictManufacturer d : dmfList1) {
					dmf1.put(d.getId(), d.getName());
				}
				edf.set("mfrId", dmf1);

	Map<String, String> trt = new HashMap<String, String>();
				trt.put("1", "Success");
				trt.put("0", "Failed");
				edf.set("taskState", trt);

	Map<String, String> lastTask = new HashMap<String, String>();
				lastTask.put("lastTaskTv", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
				edf.set("lastTaskTv", lastTask);
				Map<String, String> updateTv = new HashMap<String, String>();
				updateTv.put("updateTv", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
				edf.set("updateTv", updateTv);
				
				Map<String, String> deviceCreateTime = new HashMap<String, String>();
				deviceCreateTime.put("deviceCreateTime", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
				edf.set("deviceCreateTime", deviceCreateTime);
				
				Map<String, String> comStatus = new HashMap<String, String>();
				comStatus.put("1", "Online");
				comStatus.put("0", "Offline");
				edf.set("comStatus", comStatus);
				
				
				// list生成pdf打印；
				CreatePdf.printPdf(list, edf, ValidGroup1.class, request, response);

			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		

	/**
	 * 打印
	 * 
	 * @param testUser
	 * @param response
	 */
	@RequestMapping(value = "printDelayData")
	@ResponseBody
	public void printDelayData(String tv, String profileId,
			String searchType, String progressDelay, String serizlNo,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			Map<String, Object> extData = new HashMap<String, Object>();
			extData.put("tv",  DateUtils.date2Str(
					DateUtils.parseDate(tv, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
					new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")));
			extData.put("monthTv",initDateByMonth(extData.get("tv").toString()));
			extData.put("profileId" , profileId);
			if("0".equals(progressDelay)){
				progressDelay = "2";
			}
			extData.put("progressDelay" , progressDelay);
			extData.put("searchType" , searchType);
			extData.put("serizlNo" , serizlNo);
			//获取采集停留时间段  end
			
			
			 List<AssetMeter> assetMeterLists = null;
        	 List<String> meterSns = null;
        	 if(!StringUtils.isEmpty(searchType)
        			 && "Meter".equals(searchType)
        			 && !StringUtils.isEmpty(serizlNo)){
        		
        		 
        			AssetMeter entity = new AssetMeter();
     				entity.setSn(serizlNo);
     				AssetMeter entity1 = assetMeterService.get(entity);
        		 if(entity1 != null){
        			 meterSns = new ArrayList();
        			 meterSns.add(entity1.getId());
        		 }
        		 extData.put("snLists", meterSns);
        	 }else if(!StringUtils.isEmpty(searchType)
        			 && "Commnuicator".equals(searchType)
        			 && !StringUtils.isEmpty(serizlNo)){
        		 
        	
     			AssetCommunicator commTemp = new AssetCommunicator();
     			commTemp.setSn(serizlNo);
     			AssetCommunicator newComm = assetCommunicatorService.get(commTemp);
     			if(StringUtil.isNotEmpty(newComm)){
     				AssetMeter entity = new AssetMeter();
     				entity.setCommunicatorId(newComm.getId());
     				assetMeterLists = assetMeterService.getList(entity);
     			}
     		
     			if(assetMeterLists != null && assetMeterLists.size() > 0 ){
     				for(AssetMeter assetMeter : assetMeterLists){
     					 meterSns = new ArrayList();
     					meterSns.add(assetMeter.getId());
     				}
     				 extData.put("snLists", meterSns);
     			}else{
     				
     				 extData.put("snLists", meterSns);
     			}
        	 }
        	 
        	SysUser su = TokenManager.getToken();
 			List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
 			extData.put("orgIdList", orgIdList);
 			DataScheduleProgress dataScheduleProgress = new DataScheduleProgress();
 			dataScheduleProgress.setExtData(extData);
 			List<DataScheduleProgress> list = dataScheduleProgressService.getList(dataScheduleProgress);

 			 if(StringUtils.isEmpty(progressDelay)){
	                if(list != null && list.size() > 0){
	                	for (DataScheduleProgress dataScheduleProgress1 : list) {
	 	     			
	 	     				if(dataScheduleProgress1.getProgress() != null){
	 	     					if(DateTimeFormatterUtil.compare_date(DateUtils.date2Str(
	 	     									DateUtils.parseDate(tv, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")),
	 	     									new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")), 
	 	     									DateUtils.date2Str(
	 	     											dataScheduleProgress1.getProgress(),
	 	 	 	     									new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"))
	 	     									
	 	     									
	 	     							) == 1){
	 	     						dataScheduleProgress1.setProgressDelay("Yes");
	 	     					}else{
	 	     						dataScheduleProgress1.setProgressDelay("No");
	 	     					}
	 	     				}
	 	     			
	 	     			}
	                }
	     			
	             }else if(!StringUtils.isEmpty(progressDelay) && "1".equals(progressDelay)){
	            	if(list != null && list.size() > 0){
	                	for (DataScheduleProgress dataScheduleProgress1 : list) {
	                		dataScheduleProgress1.setProgressDelay("Yes");
	                	}
	            	}
	             }else if(!StringUtils.isEmpty(progressDelay) && "2".equals(progressDelay)){
	            	if(list != null && list.size() > 0){
	                	for (DataScheduleProgress dataScheduleProgress1 : list) {
	                		dataScheduleProgress1.setProgressDelay("No");
	                	}
	            	}
	             }
 			
			if (list.size() <= 0) {
				DataScheduleProgress d = new DataScheduleProgress();
				list.add(d);
			}
			ExcelDataFormatter edf = new ExcelDataFormatter();
			
			Map<String, String> progress = new HashMap<String, String>();
			progress.put("progress", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
			edf.set("progress", progress);
			// list生成pdf打印；
			CreatePdf.printPdf(list, edf, ValidGroup1.class, request, response);

		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	
	  /**
		 * 导出
		 * 
		 * @param delay data
		 * @param request
		 * @param response
		 */
		@RequestMapping(value = "getExcelDelayDataDetail")
		@ResponseBody
		public void getExcelDelayDataDetail(String progressTime, String deadline,
				String meterSn, String profileId, String progressDelay,
				HttpServletRequest request, HttpServletResponse response) {
			try {

				//Meter  //Commnuicator
				Map<String,Object> m=new HashMap();
			
				AssetMeter assetMeter = new AssetMeter();
	         	assetMeter.setSn(meterSn);
	         	assetMeter = assetMeterService.get(assetMeter);
	         	String meterId=assetMeter.getId();
	         	m.put("deviceId", meterId);
				
	        	AssetMeterGroupMap group = new AssetMeterGroupMap();
	    		group.setId(meterId);
	    		group.setType("1");
	    		group=	assetMeterGroupMapService.get(group);

	    		
	    		AssetMeasurementProfileDi assetMeasurementProfileDi = new AssetMeasurementProfileDi();
				assetMeasurementProfileDi.setMgId(group.getGroupId());
				assetMeasurementProfileDi.setProfileId(profileId);
				List<AssetMeasurementProfileDi> dis = assetMeasurementProfileDiService
						.getList(assetMeasurementProfileDi);

				List<String> ids = new ArrayList();

				for (AssetMeasurementProfileDi assetMeasurementProfileDi1 : dis) {
					ids.add(assetMeasurementProfileDi1.getDataitemId());
				}

				List<DictDataitem> dictDataItems = new ArrayList();
				if (ids != null && ids.size() > 0) {
					dictDataItems = dictDataitemService.getListByIds(ids);
				}
				
				
				Map<String,Excel> excels=new HashMap<String, Excel>();
		        Excel times=ExcelUtils.createExcel("Time", 22);
		        excels.put("times", times);
		        Excel serizlName=ExcelUtils.createExcel("Serial Number", 15);
		        excels.put("serizlName", serizlName);
		        for(int i=0;i<dictDataItems.size();i++){
			        	Excel value = ExcelUtils.createExcel(dictDataItems.get(i).getName(), dictDataItems.get(i).getName().length());
			        	excels.put("delay_value"+(i+1), value);    
		        }
				
	    		
	    		AssetMeasurementProfile assetMeasurementProfile = new AssetMeasurementProfile();
	    		assetMeasurementProfile.setMgId(group.getGroupId());
	    		assetMeasurementProfile.setProfileId(profileId);
	    		AssetMeasurementProfile assetMeasurementProfile1 = assetMeasurementProfileService.get(assetMeasurementProfile);
	    	
	    		if(StringUtil.isNotEmpty(progressTime)){
	    			m.put("times_start", DateUtils.parseDate(progressTime, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
	    		}
				
				if(StringUtil.isNotEmpty(deadline)){
					m.put("times_end", DateUtils.parseDate(deadline, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
	        	}
	    		
	    		
	    		List<Date> timeList = Lists.newArrayList();
	    		 
	    		List<Date> timeListNew =ProgressMissDataUtil.regularTime((Date) m.get("times_start"),(Date) m.get("times_end"), assetMeasurementProfile1, timeList);
	    		 if(timeListNew != null && timeListNew.size() > 0){
	             	timeListNew.remove(m.get("times_start"));	
	             }
	        	Map<Date,ProgressDelayDataReport> map = Maps.newHashMap();

	            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	     	//	List<Map<String,String>>	tmplist=Lists.newArrayList();
	            List<ProgressDelayDataReport>	tmplist=Lists.newArrayList();
	     		for(Date date:timeListNew) {
	     			ProgressDelayDataReport report =map.get(date);	
	     			
	     			if(report==null) {    
	     				Date tmpDate=null;
	     				try {
	     					tmpDate = sdf.parse(sdf.format(date));
	     				} catch (ParseException e) {
	     					e.printStackTrace();
	     				}
	     				report = new ProgressDelayDataReport(meterSn,tmpDate);
	     			}
	     			tmplist.add(report);
	     		}
			
	     		 ExcelDataFormatter edf = new ExcelDataFormatter();
	  	        Map<String,String> tvs=new HashMap<String, String>();
	  	        tvs.put("times",  ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
	  	        edf.set("times", tvs);
	  	        
	  	      if(tmplist.size()<=0){
	  	    	  ProgressDelayDataReport mdr=new ProgressDelayDataReport();
	  	    	  tmplist.add(mdr);
		        }
	  	        
	  	        ExcelUtils.writeToFile(tmplist, edf,"excelDelayDataDetailList.xlsx",response, excels);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		
		
		/**
		 * 打印
		 * 
		 * @param testUser
		 * @param response
		 */
		@RequestMapping(value = "printDelayDataDetail")
		@ResponseBody
		public void printDelayDataDetail(String progressTime, String deadline,
				String meterSn, String profileId, String progressDelay,
				HttpServletRequest request, HttpServletResponse response) {
			try {
				//Meter  //Commnuicator
				Map<String,Object> m=new HashMap();
			
				AssetMeter assetMeter = new AssetMeter();
	         	assetMeter.setSn(meterSn);
	         	assetMeter = assetMeterService.get(assetMeter);
	         	String meterId=assetMeter.getId();
	         	m.put("deviceId", meterId);
				
	        	AssetMeterGroupMap group = new AssetMeterGroupMap();
	    		group.setId(meterId);
	    		group.setType("1");
	    		group=	assetMeterGroupMapService.get(group);

	    		
	    		AssetMeasurementProfileDi assetMeasurementProfileDi = new AssetMeasurementProfileDi();
				assetMeasurementProfileDi.setMgId(group.getGroupId());
				assetMeasurementProfileDi.setProfileId(profileId);
				List<AssetMeasurementProfileDi> dis = assetMeasurementProfileDiService
						.getList(assetMeasurementProfileDi);

				List<String> ids = new ArrayList();

				for (AssetMeasurementProfileDi assetMeasurementProfileDi1 : dis) {
					ids.add(assetMeasurementProfileDi1.getDataitemId());
				}

				List<DictDataitem> dictDataItems = new ArrayList();
				if (ids != null && ids.size() > 0) {
					dictDataItems = dictDataitemService.getListByIds(ids);
				}
				
				
				Map<String,Excel> excels=new HashMap<String, Excel>();
		        Excel times=ExcelUtils.createExcel("Time", 22);
		        excels.put("times", times);
		        Excel serizlName=ExcelUtils.createExcel("Serial Number", 15);
		        excels.put("serizlName", serizlName);
		        for(int i=0;i<dictDataItems.size();i++){
			        	Excel value = ExcelUtils.createExcel(dictDataItems.get(i).getName(), dictDataItems.get(i).getName().length());
			        	excels.put("delay_value"+(i+1), value);    
		        }
				
	    		
	    		AssetMeasurementProfile assetMeasurementProfile = new AssetMeasurementProfile();
	    		assetMeasurementProfile.setMgId(group.getGroupId());
	    		assetMeasurementProfile.setProfileId(profileId);
	    		AssetMeasurementProfile assetMeasurementProfile1 = assetMeasurementProfileService.get(assetMeasurementProfile);
	    	
	    		if(StringUtil.isNotEmpty(progressTime)){
	    			m.put("times_start", DateUtils.parseDate(progressTime, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
	    		}
				
				if(StringUtil.isNotEmpty(deadline)){
					m.put("times_end", DateUtils.parseDate(deadline, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
	        	}
	    		
	    		
	    		List<Date> timeList = Lists.newArrayList();
	    		 
	    		List<Date> timeListNew =ProgressMissDataUtil.regularTime((Date) m.get("times_start"),(Date) m.get("times_end"), assetMeasurementProfile1, timeList);
	    		 if(timeListNew != null && timeListNew.size() > 0){
		             	timeListNew.remove(m.get("times_start"));	
		             }
	        	Map<Date,ProgressDelayDataReport> map = Maps.newHashMap();

	            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	     	//	List<Map<String,String>>	tmplist=Lists.newArrayList();
	            List<ProgressDelayDataReport>	tmplist=Lists.newArrayList();
	     		for(Date date:timeListNew) {
	     			ProgressDelayDataReport report =map.get(date);	
	     			
	     			if(report==null) {    
	     				Date tmpDate=null;
	     				try {
	     					tmpDate = sdf.parse(sdf.format(date));
	     				} catch (ParseException e) {
	     					e.printStackTrace();
	     				}
	     				report = new ProgressDelayDataReport(meterSn,tmpDate);
	     			}
	     			tmplist.add(report);
	     		}
			
	     		ExcelDataFormatter edf = new ExcelDataFormatter();
	  	        Map<String,String> tvs=new HashMap<String, String>();
	  	        tvs.put("times",  ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
	  	        edf.set("times", tvs);
	  	        
	  	      if(tmplist.size()<=0){
	  	    	  ProgressDelayDataReport mdr=new ProgressDelayDataReport();
	  	    	  tmplist.add(mdr);
		        }
				// list生成pdf打印；
	  	      	CreatePdf.printPdf(tmplist, edf,excels, request, response);

			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	
	
	/**
	 * dataItem Title
	 * @param sn
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getDataItemIdTitle")
	@ResponseBody
	public AjaxJson getDataItemIdTitle(String meterSn , String profileId , HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			//Meter  //Commnuicator
			Map<String,Object> m=new HashMap();
			MeterDataReport meterDataReport = new MeterDataReport();

			AssetMeter assetMeter = new AssetMeter();
         	assetMeter.setSn(meterSn);
         	assetMeter = assetMeterService.get(assetMeter);
         	String meterId=assetMeter.getId();
         	m.put("deviceId", meterId);
			
        	AssetMeterGroupMap group = new AssetMeterGroupMap();
    		group.setId(meterId);
    		group.setType("1");
    		group=	assetMeterGroupMapService.get(group);
    		
    		if(group == null){
    			j.setSuccess(false);
    			j.setMsg(MutiLangUtil.doMutiLang("dataIntegrityDetails.noMeasurementGroup"));
    		}
    		
    		AssetMeasurementProfileDi assetMeasurementProfileDi = new AssetMeasurementProfileDi();
    		assetMeasurementProfileDi.setMgId(group.getGroupId());
    		assetMeasurementProfileDi.setProfileId(profileId);
    		List<AssetMeasurementProfileDi> dis = assetMeasurementProfileDiService.getList(assetMeasurementProfileDi);
    		List<DictDataitem> dictDataItems = new ArrayList();
//    		List<String> ids = new ArrayList();
    		
    		for(AssetMeasurementProfileDi assetMeasurementProfileDi1 : dis){
//    			ids.add(assetMeasurementProfileDi1.getDataitemId());
    			dictDataItems.add(new DictDataitem(assetMeasurementProfileDi1.getDataitemId(),assetMeasurementProfileDi1.getDataitemName()));
    		}
     		
//    		List<DictDataitem> dictDataItems = new ArrayList();
//    		if(ids != null && ids.size() > 0){
//    			dictDataItems = dictDataitemService.getListByIds(ids);
//    		}

			j.put("dictDataItemsList", dictDataItems);
			
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg("Abnormal operation!");
		}
		return j;
	}
	
	
	
	/**
	 * dataItem Title
	 * @param sn
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getDelayDataDetail")
	@ResponseBody
	public AjaxJson getDelayDataDetail(String meterSn , String profileId , String progressTime, String deadline ,HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			//Meter  //Commnuicator
			Map<String,Object> m=new HashMap();
		
			AssetMeter assetMeter = new AssetMeter();
         	assetMeter.setSn(meterSn);
         	assetMeter = assetMeterService.get(assetMeter);
         	String meterId=assetMeter.getId();
         	m.put("deviceId", meterId);
			
        	AssetMeterGroupMap group = new AssetMeterGroupMap();
    		group.setId(meterId);
    		group.setType("1");
    		group=	assetMeterGroupMapService.get(group);
    		if(group == null){
    			j.setSuccess(false);
    			j.setMsg(MutiLangUtil.doMutiLang("dataIntegrityDetails.noMeasurementGroup"));
    		}

    		AssetMeasurementProfile assetMeasurementProfile = new AssetMeasurementProfile();
    		assetMeasurementProfile.setMgId(group.getGroupId());
    		assetMeasurementProfile.setProfileId(profileId);
    		AssetMeasurementProfile assetMeasurementProfile1 = assetMeasurementProfileService.get(assetMeasurementProfile);
    	    if(assetMeasurementProfile1 == null){
    	    	System.out.println("measurement profile is null;MgId ="+group.getGroupId()+"ProfileId ="+profileId);
    	    	return j;
    	    }
    		if(StringUtil.isNotEmpty(progressTime)){
    			m.put("times_start", DateUtils.parseDate(progressTime, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
    		}
			
			if(StringUtil.isNotEmpty(deadline)){
				m.put("times_end", DateUtils.parseDate(deadline, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
        	}
    		
    		
    		List<Date> timeList = Lists.newArrayList();
    		 
    		List<Date> timeListNew =ProgressMissDataUtil.regularTime((Date) m.get("times_start"),(Date) m.get("times_end"), assetMeasurementProfile1, timeList);
            if(timeListNew != null && timeListNew.size() > 0){
            	timeListNew.remove(m.get("times_start"));	
            }
        	Map<Date,ProgressDelayDataReport> map = Maps.newHashMap();

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
     	//	List<Map<String,String>>	tmplist=Lists.newArrayList();
            List<ProgressDelayDataReport>	tmplist=Lists.newArrayList();
     		for(Date date:timeListNew) {
     			ProgressDelayDataReport report =map.get(date);	
     			
     			if(report==null) {    
     				Date tmpDate=null;
     				try {
     					tmpDate = sdf.parse(sdf.format(date));
     				} catch (ParseException e) {
     					e.printStackTrace();
     				}
     				report = new ProgressDelayDataReport(meterSn,tmpDate);
     			}
     			tmplist.add(report);
     		}

			j.put("progressDelayList", tmplist);
			
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg("Abnormal operation!");
		}
		return j;
	}
	
	
	
	
}