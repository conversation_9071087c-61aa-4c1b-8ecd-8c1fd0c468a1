package com.clou.esp.hes.app.web.controller.asset;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.OrganizationUtils;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.core.util.UciInterfaceUtil;
import com.clou.esp.hes.app.web.dao.asset.AssetLineManagementDao;
import com.clou.esp.hes.app.web.dao.asset.AssetTransformerDao;
import com.clou.esp.hes.app.web.model.asset.AssetCalcObj;
import com.clou.esp.hes.app.web.model.asset.AssetCalcObjMap;
import com.clou.esp.hes.app.web.model.asset.AssetEntityRelationship;
import com.clou.esp.hes.app.web.model.asset.AssetLine;
import com.clou.esp.hes.app.web.model.asset.AssetMeasurementProfile;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroup;
import com.clou.esp.hes.app.web.model.asset.AssetTransformer;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;
import com.clou.esp.hes.app.web.model.report.LineLossReport;
import com.clou.esp.hes.app.web.model.system.SysOrg;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.asset.AssetCalcObjService;
import com.clou.esp.hes.app.web.service.asset.AssetEntityRelationshipService;
import com.clou.esp.hes.app.web.service.asset.AssetLineManagementService;
import com.clou.esp.hes.app.web.service.asset.AssetMeasurementProfileService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterGroupService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.asset.AssetTransformerService;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemService;
import com.clou.esp.hes.app.web.service.system.SysOrgService;
import com.clou.esp.hes.app.web.service.system.SysServiceAttributeService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gson.JsonArray;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;

import ch.iec.tc57._2011.meterreadings.GetMeterReadingsPort;
import clouesp.hes.core.uci.soap.custom.manualCalculation.DispatchMessage;
import clouesp.hes.core.uci.soap.custom.manualCalculation.DispatchTaskPort;


/**
 * @ClassName: AssetCalationObjectController
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年10月27日 上午9:50:43
 *
 */
@Controller
@RequestMapping("/assetCalationObjectController")
public class AssetCalationObjectController extends BaseController {
	@Resource
	private AssetLineManagementService 	assetLineManagementService;
	@Resource
	private AssetTransformerService 	assetTransformerService;
	@Resource
	private SysOrgService 				sysOrgService;
	@Resource
	private AssetLineManagementDao 		assetLineManagementDao;
	@Resource
	private AssetTransformerDao 		assetTransformerDao;
	@Resource
	private DataUserLogService 			dataUserLogService;
	@Resource
	private DictDataitemService  		dictDataItemService;
	@Resource
	private AssetCalcObjService  		assetCalcObjService;
	@Resource
	private AssetMeterService  			assetMeterService;
	@Resource
	private AssetEntityRelationshipService assetEntityRelationshipService;
	@Resource
	private SysServiceAttributeService sysServiceAttributeService;
	@Resource
	private AssetMeasurementProfileService profileService;
	@Resource
	private AssetMeterGroupService         groupService;
	
	
	@RequestMapping(value  = "assetCalculationObject")
    public ModelAndView assetCalculationObject(HttpServletRequest request, Model model) {
		return new ModelAndView("/asset/assetCalculationObject");
	}
	
	@RequestMapping(value  = "gridLossManager")
    public ModelAndView gridLossManager(HttpServletRequest request, Model model) {
		SimpleDateFormat sdf = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.formatter"));
		Calendar lastDate = Calendar.getInstance();
	    lastDate.add(Calendar.DATE, -7);	//昨天
	    model.addAttribute("lineLossStartTime", sdf.format(lastDate.getTime()));
	    lastDate.add(Calendar.DATE, 7);
	    model.addAttribute("lineLossExpiryTime", sdf.format(lastDate.getTime()));
		return new ModelAndView("/asset/gridLossManager");
	}
	
	@RequestMapping(value  = "addCalcMap")
    public ModelAndView addCalcMap(HttpServletRequest request, Model model,String objId,String mapType) {
		AssetCalcObj  obj= assetCalcObjService.getEntity(objId);
		AssetMeterGroup group = new AssetMeterGroup();
		group.setType("1");
		List<AssetMeterGroup> groupList = this.groupService.getList(group);
		
		String groupReplace = RoletoJson.listToReplaceStr(groupList, "id", "name");
		model.addAttribute("groupReplace", groupReplace);
		
		model.addAttribute("objId", objId);
		model.addAttribute("tvType", obj.getTvType());
		if("1".equals(mapType)) {
			return new ModelAndView("/asset/assetCalcMapAddBySearch");
		}else {
			return new ModelAndView("/asset/assetCalcMapAddByRelationship");
		}
		
	}

	
	@RequestMapping(value = "getChannels")
	@ResponseBody
	public AjaxJson getChannels(HttpServletRequest request,String mgId,int tvType){
			AjaxJson result = new AjaxJson();
			String dateType="Monthly";
			if(tvType==1) {//日
				dateType="Daily";
			}
			List<DictDataitem> dataItems = this.assetCalcObjService.getDictDataitems(mgId, dateType);
			String channelReplace = RoletoJson.listToReplaceStr(dataItems, "id", "name",";");
			result.setObj(channelReplace);
			result.setSuccess(true);
		return result;
	}
	@RequestMapping(value = "getCalcObjById")
	@ResponseBody
	public AjaxJson getCalcObjById(HttpServletRequest request,String objId){
		AjaxJson result = new AjaxJson();
		if(StringUtils.isEmpty(objId)) {
			result.setSuccess(false);
			return result;
		}
		AssetCalcObj assetCalcObj = this.assetCalcObjService.getEntity(objId);
		if(assetCalcObj!=null) {
			result.setObj(assetCalcObj.getType());
			result.setSuccess(true);
		}else {
			result.setSuccess(false);
		}
		return result;
	}
	
	@RequestMapping(value = "getCalcMapDataGrid")
    @ResponseBody
    public JqGridResponseTo getCalcMapDataGrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request,
    		String meterSn,String commId,String objId,String type,String channelId,String groupId,String channelName) {
		SysUser su = TokenManager.getToken();
		List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
		//根据objId查出原来绑定的sn
		AssetCalcObjMap mapTmp = new AssetCalcObjMap();
		if(StringUtils.isEmpty(objId)) {
			return null;
		}
		mapTmp.setId(objId);
		mapTmp.setMeteringType(1);
		
		List<AssetCalcObjMap> mapList = this.assetCalcObjService.getCalObjMapList(mapTmp);
		Set<String>  keys = Sets.newHashSet();
		for(AssetCalcObjMap map:mapList) {
			String key = map.getMeteringId()+"_"+map.getType()+"_"+map.getDataitemId();
			keys.add(key);
		}
		
		List<AssetCalcObjMap> resultList=Lists.newArrayList();
		if("-1".equals(commId)) {
			commId="";
		}
		//根据meterSn和commId查出表
		if(StringUtils.isNotEmpty(meterSn)||StringUtils.isNotEmpty(commId)) {
			AssetMeter  meterTmp = new AssetMeter();
			meterTmp.setCommunicatorId(commId);
			meterTmp.setSn(meterSn);
			meterTmp.setOrgIdList(orgIdList);
			meterTmp.setMeasurementGroupId(groupId);
			List<AssetMeter>   meters = this.assetMeterService.getList(meterTmp);
			if(meters!=null) {
				for(AssetMeter tmp:meters) {
					String id=tmp.getId();
					String key =id+"_"+type+"_"+channelId;
					AssetCalcObjMap map = new AssetCalcObjMap();
					if(keys.contains(key)) {
						map.setIsExist("1");
					}else {
						map.setIsExist("0");
					}
					map.setId(UUID.randomUUID().toString());
					map.setMeteringId(id);
					map.setMeterSn(tmp.getSn());
					map.setMeteringType(1);
					map.setDataitemId(channelId);
					map.setDataItemName(channelName);
					map.setType(Integer.parseInt(type));
					resultList.add(map);
				}
			}
		}
		
		JqGridResponseTo j= JqGridHandler.GetJqGridPageJsonData(new PageInfo<AssetCalcObjMap>(),jqGridSearchTo);
		j.setRows(resultList);
		return j;
	}
	
	@RequestMapping(value = "getCalcDataGrid")
	@ResponseBody
	public JqGridResponseTo getCalcDataGrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request,
			String entityName,String entityType) {
//		SysUser su = TokenManager.getToken();
//		List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
//		List<AssetCalcObj> mapList = this.assetCalcObjService.getList();
//		
//		
		JqGridResponseTo j= JqGridHandler.GetJqGridPageJsonData(new PageInfo<AssetCalcObjMap>(),jqGridSearchTo);
//		j.setRows(resultList);
		return j;
	}
	
	
	@RequestMapping(value = "getCalcMapDataGridByShip")
	@ResponseBody
	public JqGridResponseTo getCalcMapDataGridByShip(JqGridSearchTo jqGridSearchTo,HttpServletRequest request,
			String objId,String type,String channelId,String groupId,String channelName) {
		
		if(StringUtils.isEmpty(objId)) {
			return null;
		}
		AssetCalcObj  calcObj= assetCalcObjService.getEntity(objId);
		int  entityType =calcObj.getEntityType(); 
		String entityId =calcObj.getEntityId();
//		AssetEntityRelationship  ship = new AssetEntityRelationship();
//		ship.setParentId(entityId);
//		ship.setParentType(entityType);
//		ship.setType(1);
		
		jqGridSearchTo.put("parentId", entityId);
		jqGridSearchTo.put("parentType", entityType);
		jqGridSearchTo.put("type", 1);
		jqGridSearchTo.put("groupId", groupId);
		
//		List<AssetEntityRelationship> ships= assetEntityRelationshipService.getList(ship);
//		
//		List<String> meterIds = Lists.newArrayList();
//		if(ships!=null) {
//			for(AssetEntityRelationship shipTmp:ships) {
//				meterIds.add(shipTmp.getId());
//			}
//		}
		
		//根据objId查出原来绑定的sn
		AssetCalcObjMap mapTmp = new AssetCalcObjMap();
		mapTmp.setId(objId);
		mapTmp.setMeteringType(1);
		List<AssetCalcObjMap> mapList = this.assetCalcObjService.getCalObjMapList(mapTmp);
		Set<String>  keys = Sets.newHashSet();
		for(AssetCalcObjMap map:mapList) {
			//meterId+type+channel
			String key = map.getMeteringId()+"_"+map.getType()+"_"+map.getDataitemId();
			keys.add(key);
		}
		
		List<AssetCalcObjMap> resultList=Lists.newArrayList();
//		if(meterIds.size()>0) {
			List<AssetMeter>   meters = this.assetEntityRelationshipService.findLinkedMeterList(jqGridSearchTo);
			if(meters!=null) {
				for(AssetMeter tmp:meters) {
					String id=tmp.getId();
					String key =id+"_"+type+"_"+channelId;
					AssetCalcObjMap map = new AssetCalcObjMap();
					if(keys.contains(key)) {
						map.setIsExist("1");
					}else {
						map.setIsExist("0");
					}
					map.setId(UUID.randomUUID().toString());
					map.setMeteringId(id);
					map.setMeterSn(tmp.getSn());
					map.setMeteringType(1);
					map.setDataitemId(channelId);
					map.setDataItemName(channelName);
					map.setType(Integer.parseInt(type));
					resultList.add(map);
				}
//			}
			
		}
		
		JqGridResponseTo j= JqGridHandler.GetJqGridPageJsonData(new PageInfo<AssetCalcObjMap>(),jqGridSearchTo);
		j.setRows(resultList);
		return j;
	}
	
	
	
	
	
	@RequestMapping(value = "queryCalcObject")
	@ResponseBody
	public AjaxJson queryCalcObject(AssetCalcObj assetCalcObj,HttpServletRequest request){
		AjaxJson result = new AjaxJson();
		JqGridSearchTo requestParam = new JqGridSearchTo();
	
		SysUser su = TokenManager.getToken();
		List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
		requestParam.put("orgIds", orgIdList);
			
		switch(assetCalcObj.getEntityType()) {
		case 3://line
			List<AssetLine> lines = assetLineManagementDao.findLinesForJqGrid(requestParam);
			result.setObj(lines);
			break;
		case 4://transformer
			List<AssetTransformer> transformers = assetTransformerDao.getForJqGrid(requestParam);
			result.setObj(transformers);
			break;
		case 5:// orgs
			SysOrg entity = new SysOrg();
			List<SysOrg> sysOrglist = sysOrgService.getList(entity);
			List<SysOrg> sysOrglistNew = new ArrayList();
			for(SysOrg sysOrg : sysOrglist){
				boolean isExist = false;
				for(String orgId : orgIdList){
					if(orgId.equals(sysOrg.getId())){
						isExist = true;	
					}	
				}
				
				if(isExist){
					sysOrglistNew.add(sysOrg);
				}
	
			}
			
			result.setObj(sysOrglistNew);
			break;
		default:break;
		}
		
//		List<AssetCalcObj> assetCalcObjList =assetTransformerService.getListByCalObj(assetCalcObj);
//		result.setObj(assetCalcObjList);
		
		return result;
	}
	
	@RequestMapping(value = "findCalObjForJqGrid")
    @ResponseBody
    public JqGridResponseTo findCalObjForJqGrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request,
    		String name,String type,String tvType,
    		String entityType,String entityName,boolean select) {
		JqGridResponseTo response = null;
		if(!select) {
			response = new JqGridResponseTo();
			return response;
		}
		
		entityType = "0".equalsIgnoreCase(entityType) ? "" : entityType;
		type = "0".equalsIgnoreCase(type) ? "" : type;
		tvType = "0".equalsIgnoreCase(tvType) ? "" : tvType;
		
		
//		jqGridSearchTo.put("entityId", entityId);
		jqGridSearchTo.put("entityName", entityName);
		jqGridSearchTo.put("entityType", "".equals(entityType) ? null : Integer.parseInt(entityType));
		jqGridSearchTo.put("name", name);
		jqGridSearchTo.put("type", "".equals(type) ? null : Integer.parseInt(type));
		jqGridSearchTo.put("tvType", "".equals(tvType) ? null : Integer.parseInt(tvType));

		SysUser su = TokenManager.getToken();
		List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
		jqGridSearchTo.put("orgIds", orgIdList);
		
		response = assetTransformerService.getListByCalObjJqGrid(jqGridSearchTo);
		
		return response;
	}

	@RequestMapping(value  = "assetCalcManagement")
    public ModelAndView assetCalcManagement(HttpServletRequest request, Model model,AssetCalcObj obj) {
		JqGridSearchTo requestParam = new JqGridSearchTo();
		
		
		if(StringUtil.isNotEmpty(obj.getId())) {
			obj = assetLineManagementService.getCalcObj(obj);
			switch(obj.getEntityType()) {
			case 3://line
			default:
				List<AssetLine> lines = assetLineManagementDao.findLinesForJqGrid(requestParam);
				model.addAttribute("entityNameReplace",RoletoJson.listToReplaceStr(lines, "id", "name", ","));
				break;
			case 4://transformer
				List<AssetTransformer> transformers = assetTransformerDao.getForJqGrid(requestParam);
				model.addAttribute("entityNameReplace",RoletoJson.listToReplaceStr(transformers, "id", "name", ","));
				break;
			case 5:// orgs
				SysOrg entity = new SysOrg();
				List<SysOrg> sysOrglist = sysOrgService.getList(entity);
				model.addAttribute("entityNameReplace",RoletoJson.listToReplaceStr(sysOrglist, "id", "name", ","));
				break;
			}
		}else if(obj.getEntityId() != null) {
			switch(obj.getEntityType()) {
			case 3://line
			default:
				List<AssetLine> lines = assetLineManagementDao.findLinesForJqGrid(requestParam);
				model.addAttribute("entityNameReplace",RoletoJson.listToReplaceStr(lines, "id", "name", ","));
				break;
			case 4://transformer
				List<AssetTransformer> transformers = assetTransformerDao.getForJqGrid(requestParam);
				model.addAttribute("entityNameReplace",RoletoJson.listToReplaceStr(transformers, "id", "name", ","));
				break;
			case 5:// orgs
				SysOrg entity = new SysOrg();
				List<SysOrg> sysOrglist = sysOrgService.getList(entity);
				model.addAttribute("entityNameReplace",RoletoJson.listToReplaceStr(sysOrglist, "id", "name", ","));
				break;
			}
		}
		model.addAttribute("assetCalcObj", obj);
		
		return new ModelAndView("/asset/assetCalcManagementEditOther");
	}
	
	@Transactional
	@RequestMapping(value = "saveCalcObj")
	@ResponseBody
	public AjaxJson saveCalcObj( HttpServletRequest request,AssetCalcObj obj) {
		AjaxJson j = new AjaxJson();
		SysUser su=TokenManager.getToken();
		try {
			String entityType="";
			String entityName="";
			switch(obj.getEntityType()) {
			case 3://line
			default:
				AssetLine line = assetLineManagementDao.getEntity(obj.getEntityId());
				entityType=" Line ";
				if(line!=null&&StringUtils.isNotEmpty(line.getName())) {
					entityName=line.getName();
				}
				break;
			case 4://transformer
				AssetTransformer transformer = assetTransformerDao.getEntity(obj.getEntityId());
				entityType=" Transformer ";
				if(transformer!=null&&StringUtils.isNotEmpty(transformer.getName())) {
					entityName=transformer.getName();
				}
				break;
			case 5:// orgs
				entityType=" Organization ";
				SysOrg sysOrg = sysOrgService.getEntity(obj.getEntityId());
				if(sysOrg!=null&&StringUtils.isNotEmpty(sysOrg.getName())) {
					entityName=sysOrg.getName();
				}
				break;
			}
			if(StringUtil.isEmpty(obj.getId())) {// 添加操作
				String uuid=UUID.randomUUID().toString().replace("-", "");
				obj.setId(uuid);
				assetLineManagementService.saveCalcObj(obj);
				
				//屏蔽自动生成（因为数据项是动态取的）
//				//添加的时候根据refId，生成map
//				String referenceId = obj.getReferenceId();
//				if(StringUtils.isNotEmpty(referenceId)) {
//					//根据objId查出原来绑定的sn
//					AssetCalcObjMap mapTmp = new AssetCalcObjMap();
//					mapTmp.setId(referenceId);
//					mapTmp.setMeteringType(1);
//					List<AssetCalcObjMap> list=this.assetCalcObjService.getCalObjMapList(mapTmp);
//					if(list!=null) {
//						for(AssetCalcObjMap calcMap:list) {
//							calcMap.setId(uuid);
//							String dataitemId =calcMap.getDataitemId();
//							String dataitemIdTmp="";
//							if("11.0.2.4.1.1.12.0.0.0.0.0.0.0.224.3.72.0".equals(dataitemId)) {
//								dataitemIdTmp="13.0.2.4.1.1.12.0.0.0.0.0.0.0.224.3.72.0";
//							}else if("11.0.2.4.19.1.12.0.0.0.0.0.0.0.224.3.72.0".equals(dataitemId)) {
//								dataitemIdTmp="13.0.2.4.19.1.12.0.0.0.0.0.0.0.224.3.72.0";
//							}else if("13.0.2.4.1.1.12.0.0.0.0.0.0.0.224.3.72.0".equals(dataitemId)) {
//								dataitemIdTmp="11.0.2.4.1.1.12.0.0.0.0.0.0.0.224.3.72.0";
//							}else if("13.0.2.4.19.1.12.0.0.0.0.0.0.0.224.3.72.0".equals(dataitemId)) {
//								dataitemIdTmp="11.0.2.4.1.1.12.0.0.0.0.0.0.0.224.3.72.0";
//							}
//							calcMap.setDataitemId(dataitemIdTmp);
//							if(StringUtils.isNotEmpty(dataitemIdTmp)) {
//								assetLineManagementService.saveCalcObjMap(calcMap);
//							}
//						}
//					}
//				}
//				
				
	        	dataUserLogService.insertDataUserLog(su.getId(), "Calculation Object Define", "Add Calculation Object","Add Calculation Object(Name="+obj.getName()+",EntityType="+entityType+",EntityName="+entityName+")");
				j.setMsg(MutiLangUtil.doMutiLang("system.addSucc"));
			} else { // 更新操作
				assetLineManagementService.updateCalcObj(obj);
	        	dataUserLogService.insertDataUserLog(su.getId(), "Calculation Object Define", "Edit Calculation Object","Edit Calculation Object(Name="+obj.getName()+",EntityType="+entityType+",EntityName="+entityName+")");
				j.setMsg(MutiLangUtil.doMutiLang("system.updateSucc"));
			}
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			j.setSuccess(false);
			j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
			return j;
		}
		
		return j;
	}

	
	
	
	
	@Transactional
	@RequestMapping(value = "saveCalcObjMapBatch")
	@ResponseBody
	public AjaxJson saveCalcObjMapBatch( HttpServletRequest request,String objId,String calcMaps) {
		AjaxJson j = new AjaxJson();
		try {
			JSONArray curArray = null;
			SysUser su=TokenManager.getToken();
			if(StringUtils.isNotEmpty(calcMaps)) {
				curArray= JSON.parseArray(calcMaps);
				if(curArray!=null) {
					AssetCalcObj calcObj = this.assetCalcObjService.getEntity(objId);
					for(int i=0;i<curArray.size();i++) {
						JSONObject jsonRestTemp = curArray.getJSONObject(i);
						String meterId = jsonRestTemp.getString("meteringId");
						String channelId =jsonRestTemp.getString("dataitemId");
						String type =jsonRestTemp.getString("type");
						//String isExist =jsonRestTemp.getString("isExist");
						AssetCalcObjMap obj = new AssetCalcObjMap();
						obj.setMeteringId(meterId);
						obj.setMeteringType(1); // 暂时只有电表类型
						obj.setDataitemId(channelId);
						obj.setType(Integer.parseInt(type));
						obj.setId(objId);
						//不存在、增加
						List<AssetCalcObjMap> list=this.assetCalcObjService.getCalObjMapList(obj);
						if(list==null||list.size()==0) {
							assetLineManagementService.saveCalcObjMap(obj);
						}
					}
					
					j.setMsg(MutiLangUtil.doMutiLang("system.addSucc"));
		        	dataUserLogService.insertDataUserLog(su.getId(), "Calculation Object Define", "Edit Calculation Object","Edit Calculation Object(Name="+calcObj.getName()+")");
				}
			}
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			j.setSuccess(false);
			j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
			return j;
		}
		
		return j;
	}
	
	@RequestMapping(value = "isExist")
	@ResponseBody
	public AjaxJson isExist(AssetCalcObjMap map,HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			List<AssetCalcObjMap> list=this.assetCalcObjService.getCalObjMapList(map);
			if(list!=null&&list.size()>0) {
				j.setObj(1);
			}else {
				j.setObj(0);
			}
			
		}catch(Exception ex) {
			ex.printStackTrace();
			j.setSuccess(false);
		}
		return j;
	}
	
	//输入手动计算时间页面
	@RequestMapping(value  = "showManual")
    public ModelAndView showManual(HttpServletRequest request, Model model) {
		SimpleDateFormat sdf = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.formatter"));
		Calendar lastDate = Calendar.getInstance();
	    lastDate.add(Calendar.DATE, -2);
	    model.addAttribute("startTime", sdf.format(lastDate.getTime()));
	    lastDate.add(Calendar.DATE, 1);
	    model.addAttribute("endTime", sdf.format(lastDate.getTime()));
		return new ModelAndView("/asset/manualLineLoss");
	}
	
	
	//手动执行线损计算
	@Transactional
	@RequestMapping(value = "saveManual")
	@ResponseBody
	public AjaxJson saveManual( HttpServletRequest request,String calcs) {
			AjaxJson j = new AjaxJson();
			final SimpleDateFormat sdf = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.formatter"));
			try {
				JSONArray array = JSON.parseArray(calcs);
				for(int i=0;i<array.size();i++) {
					JSONObject jsonRestTemp = array.getJSONObject(i);
					final String calcId = jsonRestTemp.getString("calcObjId");
					final String date   = jsonRestTemp.getString("calDate");
					//异步调用uci
					new Thread() {
						public void run() {
							try{
								List<String> list = Lists.newArrayList();
								list.add(calcId);
								DispatchTaskPort port = (DispatchTaskPort) UciInterfaceUtil.getInterface("DispatchTaskPort", DispatchTaskPort.class, sysServiceAttributeService);
								DispatchMessage mes = new DispatchMessage();
								mes.setType("CALCULATION");
								mes.setOpType("CALCOBJ");
								mes.setOpStartDate(sdf.parse(date).getTime());
								mes.setOpEndDate(sdf.parse(date).getTime());
								mes.setIds(list);
								List<DispatchMessage> messList=Lists.newArrayList();
								messList.add(mes);
								port.dispatch(messList);
								}catch(Exception ex) {
									ex.printStackTrace();
								}
						};
					}.start();
				}
			} catch (Exception e) {
				e.printStackTrace();
				j.setSuccess(false);
				j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
			}
		return j;
	}
	
	
	//查出相同实体下的不同频率的计算对象
	@RequestMapping(value = "getRefCalc")
	@ResponseBody
	public AjaxJson getRefCalc(HttpServletRequest request,String entityId,int tvType) {
		AjaxJson j = new AjaxJson();
		try {
			int tvTypeTmp=0;
			if(1==tvType) {
				tvTypeTmp=2;
			}else if(2==tvType) {
				tvTypeTmp=1;
			}
			AssetCalcObj obj= new AssetCalcObj();
			obj.setEntityId(entityId);
			obj.setTvType(tvTypeTmp);
			List<AssetCalcObj> list=this.assetCalcObjService.getList(obj);
			j.setObj(list);
		}catch(Exception ex) {
			ex.printStackTrace();
			j.setSuccess(false);
		}
		return j;
	}
}
