
package ch.iec.tc57._2011.schema.message;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAnyElement;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import org.w3c.dom.Element;


/**
 * Each operation set is a collection of operations that may require operational-integrity and/or sequence control.
 * 
 * <p>OperationSet complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="OperationSet"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="enforceMsgSequence" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="enforceTransactionalIntegrity" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="Operation" maxOccurs="unbounded" minOccurs="0"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="operationId" type="{http://www.w3.org/2001/XMLSchema}integer"/&gt;
 *                   &lt;element name="noun" minOccurs="0"&gt;
 *                     &lt;simpleType&gt;
 *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *                         &lt;enumeration value="AuxiliaryAgreementConfig"/&gt;
 *                         &lt;enumeration value="ComModuleConfig"/&gt;
 *                         &lt;enumeration value="CustomerAccountConfig"/&gt;
 *                         &lt;enumeration value="CustomerAgreementConfig"/&gt;
 *                         &lt;enumeration value="CustomerConfig"/&gt;
 *                         &lt;enumeration value="CustomerMeterDataSet"/&gt;
 *                         &lt;enumeration value="EndDeviceConfig"/&gt;
 *                         &lt;enumeration value="EndDeviceControls"/&gt;
 *                         &lt;enumeration value="EndDeviceEvents"/&gt;
 *                         &lt;enumeration value="EndDeviceFirmware"/&gt;
 *                         &lt;enumeration value="EndDeviceGroups"/&gt;
 *                         &lt;enumeration value="MasterDataLinkageConfig"/&gt;
 *                         &lt;enumeration value="MeterConfig"/&gt;
 *                         &lt;enumeration value="MeterReadings"/&gt;
 *                         &lt;enumeration value="MeterReadSchedule"/&gt;
 *                         &lt;enumeration value="MeterServiceRequest"/&gt;
 *                         &lt;enumeration value="MeterServiceRequests"/&gt;
 *                         &lt;enumeration value="PricingStructureConfig"/&gt;
 *                         &lt;enumeration value="ReceiptRecord"/&gt;
 *                         &lt;enumeration value="ServiceCategoryConfig"/&gt;
 *                         &lt;enumeration value="ServiceLocationConfig"/&gt;
 *                         &lt;enumeration value="ServiceSupplierConfig"/&gt;
 *                         &lt;enumeration value="TransactionRecord"/&gt;
 *                         &lt;enumeration value="UsagePointConfig"/&gt;
 *                         &lt;enumeration value="UsagePointGroups"/&gt;
 *                         &lt;enumeration value="UsagePointLocationConfig"/&gt;
 *                       &lt;/restriction&gt;
 *                     &lt;/simpleType&gt;
 *                   &lt;/element&gt;
 *                   &lt;element name="verb" minOccurs="0"&gt;
 *                     &lt;simpleType&gt;
 *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *                         &lt;enumeration value="cancel"/&gt;
 *                         &lt;enumeration value="canceled"/&gt;
 *                         &lt;enumeration value="change"/&gt;
 *                         &lt;enumeration value="changed"/&gt;
 *                         &lt;enumeration value="create"/&gt;
 *                         &lt;enumeration value="created"/&gt;
 *                         &lt;enumeration value="close"/&gt;
 *                         &lt;enumeration value="closed"/&gt;
 *                         &lt;enumeration value="delete"/&gt;
 *                         &lt;enumeration value="deleted"/&gt;
 *                         &lt;enumeration value="get"/&gt;
 *                         &lt;enumeration value="show"/&gt;
 *                         &lt;enumeration value="reply"/&gt;
 *                         &lt;enumeration value="subscribe"/&gt;
 *                         &lt;enumeration value="unsubscribe"/&gt;
 *                         &lt;enumeration value="execute"/&gt;
 *                         &lt;enumeration value="report"/&gt;
 *                         &lt;enumeration value="stop"/&gt;
 *                         &lt;enumeration value="terminate"/&gt;
 *                       &lt;/restriction&gt;
 *                     &lt;/simpleType&gt;
 *                   &lt;/element&gt;
 *                   &lt;element name="elementOperation" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *                   &lt;any processContents='skip' namespace='##other' minOccurs="0"/&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OperationSet", propOrder = {
    "enforceMsgSequence",
    "enforceTransactionalIntegrity",
    "operation"
})
public class OperationSet {

    protected Boolean enforceMsgSequence;
    protected Boolean enforceTransactionalIntegrity;
    @XmlElement(name = "Operation")
    protected List<OperationSet.Operation> operation;

    /**
     * 获取enforceMsgSequence属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isEnforceMsgSequence() {
        return enforceMsgSequence;
    }

    /**
     * 设置enforceMsgSequence属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setEnforceMsgSequence(Boolean value) {
        this.enforceMsgSequence = value;
    }

    /**
     * 获取enforceTransactionalIntegrity属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isEnforceTransactionalIntegrity() {
        return enforceTransactionalIntegrity;
    }

    /**
     * 设置enforceTransactionalIntegrity属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setEnforceTransactionalIntegrity(Boolean value) {
        this.enforceTransactionalIntegrity = value;
    }

    /**
     * Gets the value of the operation property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the operation property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getOperation().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link OperationSet.Operation }
     * 
     * 
     */
    public List<OperationSet.Operation> getOperation() {
        if (operation == null) {
            operation = new ArrayList<OperationSet.Operation>();
        }
        return this.operation;
    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType&gt;
     *   &lt;complexContent&gt;
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
     *       &lt;sequence&gt;
     *         &lt;element name="operationId" type="{http://www.w3.org/2001/XMLSchema}integer"/&gt;
     *         &lt;element name="noun" minOccurs="0"&gt;
     *           &lt;simpleType&gt;
     *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
     *               &lt;enumeration value="AuxiliaryAgreementConfig"/&gt;
     *               &lt;enumeration value="ComModuleConfig"/&gt;
     *               &lt;enumeration value="CustomerAccountConfig"/&gt;
     *               &lt;enumeration value="CustomerAgreementConfig"/&gt;
     *               &lt;enumeration value="CustomerConfig"/&gt;
     *               &lt;enumeration value="CustomerMeterDataSet"/&gt;
     *               &lt;enumeration value="EndDeviceConfig"/&gt;
     *               &lt;enumeration value="EndDeviceControls"/&gt;
     *               &lt;enumeration value="EndDeviceEvents"/&gt;
     *               &lt;enumeration value="EndDeviceFirmware"/&gt;
     *               &lt;enumeration value="EndDeviceGroups"/&gt;
     *               &lt;enumeration value="MasterDataLinkageConfig"/&gt;
     *               &lt;enumeration value="MeterConfig"/&gt;
     *               &lt;enumeration value="MeterReadings"/&gt;
     *               &lt;enumeration value="MeterReadSchedule"/&gt;
     *               &lt;enumeration value="MeterServiceRequest"/&gt;
     *               &lt;enumeration value="MeterServiceRequests"/&gt;
     *               &lt;enumeration value="PricingStructureConfig"/&gt;
     *               &lt;enumeration value="ReceiptRecord"/&gt;
     *               &lt;enumeration value="ServiceCategoryConfig"/&gt;
     *               &lt;enumeration value="ServiceLocationConfig"/&gt;
     *               &lt;enumeration value="ServiceSupplierConfig"/&gt;
     *               &lt;enumeration value="TransactionRecord"/&gt;
     *               &lt;enumeration value="UsagePointConfig"/&gt;
     *               &lt;enumeration value="UsagePointGroups"/&gt;
     *               &lt;enumeration value="UsagePointLocationConfig"/&gt;
     *             &lt;/restriction&gt;
     *           &lt;/simpleType&gt;
     *         &lt;/element&gt;
     *         &lt;element name="verb" minOccurs="0"&gt;
     *           &lt;simpleType&gt;
     *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
     *               &lt;enumeration value="cancel"/&gt;
     *               &lt;enumeration value="canceled"/&gt;
     *               &lt;enumeration value="change"/&gt;
     *               &lt;enumeration value="changed"/&gt;
     *               &lt;enumeration value="create"/&gt;
     *               &lt;enumeration value="created"/&gt;
     *               &lt;enumeration value="close"/&gt;
     *               &lt;enumeration value="closed"/&gt;
     *               &lt;enumeration value="delete"/&gt;
     *               &lt;enumeration value="deleted"/&gt;
     *               &lt;enumeration value="get"/&gt;
     *               &lt;enumeration value="show"/&gt;
     *               &lt;enumeration value="reply"/&gt;
     *               &lt;enumeration value="subscribe"/&gt;
     *               &lt;enumeration value="unsubscribe"/&gt;
     *               &lt;enumeration value="execute"/&gt;
     *               &lt;enumeration value="report"/&gt;
     *               &lt;enumeration value="stop"/&gt;
     *               &lt;enumeration value="terminate"/&gt;
     *             &lt;/restriction&gt;
     *           &lt;/simpleType&gt;
     *         &lt;/element&gt;
     *         &lt;element name="elementOperation" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
     *         &lt;any processContents='skip' namespace='##other' minOccurs="0"/&gt;
     *       &lt;/sequence&gt;
     *     &lt;/restriction&gt;
     *   &lt;/complexContent&gt;
     * &lt;/complexType&gt;
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "operationId",
        "noun",
        "verb",
        "elementOperation",
        "any"
    })
    public static class Operation {

        @XmlElement(required = true)
        protected BigInteger operationId;
        protected String noun;
        protected String verb;
        @XmlElement(defaultValue = "false")
        protected Boolean elementOperation;
        @XmlAnyElement
        protected Element any;

        /**
         * 获取operationId属性的值。
         * 
         * @return
         *     possible object is
         *     {@link BigInteger }
         *     
         */
        public BigInteger getOperationId() {
            return operationId;
        }

        /**
         * 设置operationId属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link BigInteger }
         *     
         */
        public void setOperationId(BigInteger value) {
            this.operationId = value;
        }

        /**
         * 获取noun属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getNoun() {
            return noun;
        }

        /**
         * 设置noun属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setNoun(String value) {
            this.noun = value;
        }

        /**
         * 获取verb属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getVerb() {
            return verb;
        }

        /**
         * 设置verb属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setVerb(String value) {
            this.verb = value;
        }

        /**
         * 获取elementOperation属性的值。
         * 
         * @return
         *     possible object is
         *     {@link Boolean }
         *     
         */
        public Boolean isElementOperation() {
            return elementOperation;
        }

        /**
         * 设置elementOperation属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link Boolean }
         *     
         */
        public void setElementOperation(Boolean value) {
            this.elementOperation = value;
        }

        /**
         * 获取any属性的值。
         * 
         * @return
         *     possible object is
         *     {@link Element }
         *     
         */
        public Element getAny() {
            return any;
        }

        /**
         * 设置any属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link Element }
         *     
         */
        public void setAny(Element value) {
            this.any = value;
        }

    }

}
