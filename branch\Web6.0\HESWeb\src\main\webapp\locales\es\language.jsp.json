{"login": {"username": "Nombre usuario", "pswd": "Contraseña", "usernameNull": "Nombre usuario no puede estar vacío", "pswdNull": "La contraseña no puede estar vacío", "remeberMe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "login": "Acceso", "loginOnOtherPlace": "Has iniciado sesión en otra parte, accede otra vez", "accountDisabled": "Cuenta deshabilitada, estas obligado cerrar sesión", "world": "Mundo", "America": "América", "Nigeria": "Nigeria", "Australia": "Australia", "ACP": "American Cloud Platform", "notExistUser": "No existe el usuario", "LockedUser": "¡Usuario bloqueado!", "errorPwd": "Contrase<PERSON>", "disabledUser": "<PERSON><PERSON><PERSON> deshabili<PERSON>o", "errorUser": "Excepción del usuario", "loginFirst": "Acceder primero", "logoutFailed": "Cierre de sesión fallida, inténtelo de nuevo", "logout": "<PERSON><PERSON><PERSON>", "tenantIsStop": "El ocupante ha sido deshabilitado", "accLgedElsewhere": "Esta cuenta de usuario se registra en otro lugar", "userDontExist": "La cuenta de usuario no existe", "toRevisePwd": "Tu contraseña ha expirado. Por favor cámbialo.", "firstLoginRevisePwd": "Cambie su contraseña para el primer inicio de sesión.", "firstLoginResetPassword": "Primer inicio de sesión para restablecer la contraseña", "incorrectPcCode": "Código de PC incorrecto. Si tiene alguna pregunta, póngase en contacto con el administrador del sistema", "startTimeNotEffect": "La hora de inicio aún no ha entrado en vigencia. Si tiene alguna pregunta, comuníquese con el administrador del sistema", "pcCodeOverdue": "El código de la PC estaba vencido. Si tiene alguna pregunta, comuníquese con el administrador del sistema", "pcCodeUnavailable": "El código de PC no está disponible. Si tiene alguna pregunta, comuníquese con el administrador del sistema", "meterNumUnavailable": "El número de medidor no está disponible. Si tiene alguna pregunta, comuníquese con el administrador del sistema"}, "system": {"systemName": "ClouESP", "systemVersion": "V6.0.46", "copyRight": "Copyright @ 2020 SHENZHEN CLOU. All rights reserved.", "determine": "OK", "cancel": "<PERSON><PERSON><PERSON>", "notifications": "Notificaciones", "information": "Información", "message": "Men<PERSON><PERSON>", "aydeltherdate": "¿Confirmar para eliminar este registro?", "submit": "OK", "pseleData": "¡Por favor, selecciona un dato!", "pleaseInterNum": "¡Por favor, introduzca un número!", "reqexc": "¡Petición anormal!", "delete": "Eliminar", "add": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "change": "Cambiar RF a GPRS ", "inquiryTitle": "Notificaciones", "delSuccess": "¡Eliminar exitoso!", "delFail": "¡Eliminar fallo!", "abnoOpera": "¡Operación anormal!", "addSucc": "¡Éxito de añadido!", "updateSucc": "¡Éxito de actualización!", "saveSucc": "¡Guardado exitoso!", "saveFail": "¡Guardado fallo!", "cancelSucc": "!Cancelar exitoso!", "cancelFail": "¡Cancelar fallo!", "requestError": "¡Solicitud de error!", "operSucce": "¡Operación exitosa!", "nameExist": "¡La cuenta ya existe! ", "systemException": "Sistema de excepción!", "UCIException": "UCI Excepción!", "selectNoData": "¡No se encuentra el dato!", "operation": "Operación", "export": "Exportar", "print": "Imprimir", "start": "Empezar", "end": "Final", "validSucc": "Verificación aprobada ", "refreshTree": "Actualizar nodo de árbol .", "checkLicense": "Verifique que la licencia sea correcta "}, "home": {"devicesStatistic": "Estadísticas de dispositivos", "meters": "Medidores", "concentrators": "Concentradores", "communicators": "Comunicadores", "selectiveCategory": "Selección de categoría", "manufacturer": "Fabricante", "model": "<PERSON><PERSON>", "communication": "Comunicación", "name": "Nombre", "number": "Número", "rate": "Ra<PERSON>", "date": "<PERSON><PERSON>", "collectionIntegrity": "Estadísticas de recolección de datos", "completeRate": "<PERSON><PERSON> completado", "completedNumber": "Número completado", "partialNumber": "Número parcial", "failedNumber": "Número fallo", "eventsStatistic": "Estadísticas de eventos", "eventsTotal": "Total de eventos", "classifiedStatistcOfEvents": "Estadística de eventos", "overallDaily": "Historial gráfico global de recolección de datos(%)", "classifiedStatistcOfMeter": "Gráfica de dispositivos", "supplyStaticOfSubOrgan": "Suministro Estadístico de Sub Organización", "supplyStatistic": "Suministro estadísticos", "salesStatisticOfCustomer": "Estadística de ventas del cliente", "organization": "Organización", "type": "Tipo", "powerSupply": "Suministro(kWh)", "customerNumber": "Número", "supplyStatisticDaily": "Estadística de suministro(kWh) de los últimos 7 días", "supplyStatistic15Daily": "Estadísticas de los últimos 15 días de suministro (kWh)", "supplyStatistic30Daily": "Estadísticas de los últimos 30 días de suministro (kWh)", "supplyStatistic6Month": "Estadísticas de los últimos 6 meses de suministro (kWh)", "supplyStatistic12Month": "Estadísticas de los últimos 12 meses de suministro (kWh)", "Last7days": "7", "Last15days": "15", "Last30days": "30", "supplyCount": "Estadística de Suministro de Sub Organización", "onlineMeter": "Medidores conectados", "onlineCommunicators": "Comunicadores conectados", "onlineConcentrators": "Concentradores conectados", "yesterdayPowerSupply": "Suministro de energía de ayer(kWh)", "other": "<PERSON><PERSON>", "noData": "Sin datos", "Last6months": "6", "Last12months": "12"}, "error403": {"title": "¡Página no encontrada!", "content": "La página no está autorizada"}, "error404": {"title": "¡Página no encontrada!", "content": "Lo siento, la página parece ir a Marte ~"}, "error500": {"title": "¡Error servidor interno!", "content": "El servidor parece estar equivocado ..."}, "noPermissions": {"code": "10002", "title": "Sin acceso!", "content": "Si necesita acceso, póngase en contacto con el administrador ..."}, "index": {"home": "Portada", "logout": "<PERSON><PERSON><PERSON>", "search": "Buscar", "meter": "Medidores", "commnuicator": "Comunicadores", "serialNumber": "Número de serie", "name": "Nombre", "mac": "LDN", "model": "<PERSON><PERSON>", "searchFor": "Buscar", "favorites": "<PERSON><PERSON><PERSON><PERSON>", "myProfile": "Mi perfil", "passwordSetting": "Contraseña", "licenseSetting": "Licencias", "confCanFavorite": "¿Confirmar para cancelar el favorito?", "advancedSearch": "Búsqueda avanzada", "frimwareVersion": "Versión de Firmware", "measurementGroup": "Grupo de medida", "collectionSchemeGroup": "Grupo plan de recopilación", "line": "Lín<PERSON>", "transformer": "Transformador", "manufacturer": "Fabricante", "commuicationType": "Tipo comunicación", "more": "más", "selected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pleaseChoose": "Por favor selecciona", "communicatorSn": "Nº serie Comunicador", "organization": "Organización", "headAlarmTitle": "Informe eventos de alarma medidor", "supplyStatisticsReport": "Informe de estadísticas de suministro", "noAlarm": "Sin <PERSON>arma", "customer": "Clientes", "telephoneNum": "Núm. Teléfono", "tel": "TEL", "voltageLevel": "<PERSON><PERSON>", "refreshOrgLineTransformer": "Actualizar el árbol organizativo ...", "searchOrgLineTransformer": "<PERSON><PERSON>do nodo ...", "loadTreeNode": "Cargando nodo de árbol ...", "dailyReport": "Reporte diario", "monthlyReport": "Reporte mensual", "refreshReportTree": "Actualizando el árbol de informes ..."}, "connectOrDisconnect": {"function": "Función", "system": "Sistema", "url": "URL", "connectDisconnect": "Conectar/ Desconectar", "power7000": "Power7000"}, "scheduleReadsReport": {"title": "Informe de lecturas programadas", "overview": "Visión general", "manufacturer": "Fabricante", "model": "<PERSON><PERSON>", "communication": "Comunicación", "ydayInteRate": "Tasa integridad de lectura datos de ayer", "ydayCompRate": "Ratio lectura completa de ayer", "completed": "Completado", "partial": "Parcial", "failed": "fallo", "details": "Detalles", "dMeterInteRate": "Rango integral de lecturas diarias", "start": "Empezar", "end": "Final", "mmeterInteRate": "Rango integral de lecturas mensuales", "dInteRateManu": "Rango integral diario por fabricantes", "mInteRateManu": "Rango integral mensual por fabricantes", "dInteRateSMmanu": "Rango integral diario indiv o multiple por fabricante(s)", "mInteRateSMmanu": "Rango integral mensual indiv o multiple por fabricante(s)", "dInteRateModels": "Rango integral diario por modelos", "mInteRateModels": "Rango integral mensual por modelos", "dInteRateSMModel": "Rango integral diario indiv o multiple por modelos(s)", "mInteRateSMModel": "Rango integral mensual indiv o multiple por modelos(s)", "dInteRateCommun": "Rango integral diario por Comunicaciones", "mInteRateCommun": "Rango integral mensual por Comunicaciones", "dInteRateSMCommun": "Rango integral diario indiv o multiple por Comunicaciones(s)", "mInteRateSMCommun": "Rango integral mensual indiv o multiple por Comunicaciones", "dInteRateOrg": "Rango integral diario por Organización", "mInteRateOrg": "Rango integral mensual por Organización", "dInteRateSMOrg": "Rango integral diario indiv o multiple por Organizacione(s)", "mInteRateSMOrg": "Rango integral mensual indiv o multiple por Organizacione(s)", "dailyInteRate": "<PERSON>ngo de integridad diario", "monthlyInteRate": "<PERSON><PERSON> de integridad mensual", "dmeterInteRadeio": "Rango de integridad diario de lecturas", "mmeterInteRadeio": "Rango de integridad mensual de lecturas", "dInteRatioManu": "Rango de integridad diario de fabricantes", "calcIntegrityRate": "Calcular la tasa de integridad", "calcLineLoss": "Calcular pérdida de línea", "holdManual": "Fondo recalculado, espere"}, "assetScheduleSchemeDetailList": {"title": "Lista de tarea", "taskId": "Id tarea", "id": "Id", "taskType": "<PERSON><PERSON><PERSON> de tareas", "profileId": "Nombre de perfil", "taskCycleType": "Tipo de ciclo de tarea", "taskCycle": "C<PERSON>lo de tarea", "startTime": "Hora de inicio", "endTime": "Hora de finalización", "pseleData": "¡Este plan es referencial y no se puede eliminar!", "confirmDel": "¿Confirma borrar esta tarea?", "addReadsTask": "Tarea de lectura de datos", "addEvenTask": "Tarea de lectura de eventos", "addTimeTask": "Tarea sincronización del tiempo", "addOnline": "Tarea de lecturas de estado en línea", "collSchemeMage": "Administración plan de recopilación", "daily": "Diario", "hourly": "<PERSON><PERSON> hora", "minutely": "Cada minuto", "monthly": "Cada mes", "pleaseChoose": "---<PERSON><PERSON><PERSON><PERSON>r---", "successfullyModified": "Modificación éxitoso", "addedSuccessfully": "<PERSON><PERSON><PERSON><PERSON>", "dcuSyncTime": "dcu Sync Time"}, "assetScheduleSchemeList": {"title": "Lista de plan", "id": "Id", "name": "Nombre de plan", "meterStandard": "<PERSON><PERSON><PERSON>", "protocolId": "Id <PERSON>", "meterNumber": "Contador de medidores", "referenceScheme": "Plan referencial", "description": "Descripción", "pseleData": "¡Seleccione los datos de la lista plan!", "confirmDel": "¿Confirma eliminar el plan?", "canBeDeleted": "¡El plan está asociado con medidores que no pueden ser borrados!"}, "dataIntegrityList": {"headTitle": "Seguimiento de datos perdidos", "evenExportTitle": "Exportar datos del medidor y eventos", "id": "Id", "serizlNo": "Nº serie Medidor", "tv": "<PERSON><PERSON>", "mfrId": "Fabricante", "modelId": "<PERSON><PERSON>", "communicator": "Nº serie comunicador", "commId": "Comunicación", "progress": "Progreso", "lastTask": "Última llamada", "taskResult": "Resultado de tarea", "failureCause": "C<PERSON>a de fallo", "integrity": "<PERSON><PERSON>(%)", "integrityRate": "Integridad de Rango (%)", "analysis": "<PERSON><PERSON><PERSON><PERSON> referencial", "export": "Exportar", "print": "Imprimir", "taskResultFailed": "Fallo", "taskResultSuccess": "Éxito", "all": "Todo", "delayDay": "Retraso del progreso (Días)", "progressDelayReport": "Reporte progreso retraso", "missDataReport": "Reporte de datos perdidos", "integrityRateReport": "Reporte rango de integridad", "updateTv": "Tiempo de actualización de estado", "comStatus": "Estatus de com."}, "dataIntegrityDetails": {"id": "Id", "serizlNo": "Nº serie", "tv": "<PERSON><PERSON>", "manufacturer": "Fabricante", "model": "<PERSON><PERSON>", "communication": "Comunicaciones", "profile": "Perfil", "readStatus": "Estatus de lectura", "noMeasurementGroup": "No se agregó ningún grupo de medición"}, "dataMeterEventList": {"headTitle": "Informe de eventos medidor", "headAlarmTitle": "Informe eventos alarma del medidor", "deviceId": "Nº Dispositivo", "eventId": "Nº evento", "sn": "Nº serie", "export": "Exportar", "print": "Imprimir", "tv": "<PERSON><PERSON>", "eventType": "Tipo evento", "event": "Evento", "eventDetail": "Detalles evento"}, "dayList": {"id": "Nº", "dayName": "Día", "startTime": "Hora de inicio", "rate": "Ra<PERSON>", "add": "<PERSON><PERSON><PERSON>", "addDay": "<PERSON><PERSON><PERSON>", "hour": "<PERSON><PERSON>", "minute": "Min<PERSON>", "second": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "title": "Lista de Días", "pleaseSelectData": "Seleccione un día de la lista", "pleSetRateDayList": "Configure la tarifa de la lista", "lastOneTimePeriod": "Datos del día al menos un período de tiempo", "areBeAtMost": "Se permite crear 255 días como máximo", "inteAllAtMost": "¡Se permiten 8 intervalos como máximo en un día!"}, "device_list": {"headTitle": "Reporte de eventos", "pageTitle": "Configuración del medidor", "title": "Lista medidor", "id": "id", "sn": "Nº serie Medidor", "modelName": "<PERSON><PERSON>", "communicator": "Nº serie comunicador", "comType": "Comunicación", "set": "Escribir", "get": "<PERSON><PERSON>", "delete": "Eliminar", "parameterType": "Tipos parámetros", "operation": "Operación", "profiles": "<PERSON><PERSON><PERSON>", "channels": "Canales", "seleLeastOne": "¡Selecciona al menos un dato de la tabla！", "seleCommunicator": "Por favor seleccione un comunicador", "leastOneChoose": "¡Elija al menos una copia de la lectura！", "communicatorList": "Lista de comunicador", "donotRepeat": "No repita la operación"}, "device_read_parameter_list": {"title": "Resul<PERSON><PERSON>", "id": "id", "sn": "Nº serie medidor", "parameterType": "Tipos parámetros", "parameterItem": "<PERSON><PERSON>", "meterGroup": "Grupo medidor", "requestTime": "Tiempo requerido", "responseTime": "Tiempo de respuesta", "status": "<PERSON><PERSON><PERSON>", "reason": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "export": "Exportar", "print": "Imprimir", "total": "Total", "completed": "Completado", "success": "Éxito", "failed": "Fallo", "processing": "Procesando", "tariffType": "Tipo tarifa", "active": "Activo", "passive": "Pasivo", "touGroup": "Grupo TOU", "limiterGroup": "Grupo de Limitador ", "measurementGroup": "Grupo de medición", "pleAddTask": "¡Favor añadir tarea!", "areToExcTask": "¿Estás seguro de ejecutar esta lista de tareas?", "stepTariffGroup": "Grupo de tarifas escalonadas", "prepay": "<PERSON><PERSON> por adelantado", "friend": "Amigo", "touTariff": "<PERSON><PERSON><PERSON>"}, "linkedEntityRelationship": {"meterSN": "Nº serie medidor", "logicalName": "Nombre lógico", "customerType": "Tipo de cliente", "organization": "Organización", "linkedMeter": "Medidor vinculado", "linkedMeterList": "Lista de medidores vinculados", "linkedTransformer": "Transformador vinculado", "linkedTransformerList": "Lista de transformadores vinculados", "communicatorSN": "Comunicador SN", "communicatorName": "Nombre del comunicador", "transformerName": "Nombre del transformador", "transformerSN": "Transformer SN", "selectLeastLine": "Seleccione primero una línea", "selectLeastTransformer": "Por favor, seleccione un transformador de línea"}, "importTempGprsMeter": {"downFileImport": "Descargar archivo de plantilla", "tempFileImport": "Importar archivo de plantilla", "tempFile": "Archivo de plantilla", "meterType": "Tipo de medidor", "DCU": "DCU", "importStatus": "Estado de importación", "importLog": "Importar registro", "commAddress": "Dirección común", "logicalAddr": "Dirección lógica", "import": "Importar", "finish": "Cerca"}, "deviceList": {"abnormalRequestFromUCI": "Problema de solicitud de la UCI", "title": "Administración activos", "basicTitle": "Estado básica", "communication": "Estado COM", "groupPro": "Estado Grupo", "other": "<PERSON><PERSON>", "addGprsMeter": "Añadir medidor GPRS", "addMeterCommun": "Añadir comunicador", "relateMeterCommun": "Relacionar el medidor con el comunicador", "addCommunicatior": "Añadir comunicador", "id": "id", "deviceSN": "Nº serie medidor", "deviceType": "Tipo de dispositivo", "name": "Nombre", "mac": "Nombre lógico", "model": "<PERSON><PERSON>", "communicationType": "Tipo de Comunicación", "manufacturer": "Fabricante", "limiter": "Limitador", "tou": "TOU", "stepTariff": "Ta<PERSON>fas escalona<PERSON>", "schemeGroup": "Grupo plan de recopilación", "meaGroup": "Grupo de medida", "encrypt": "Encriptación", "ek": "EK", "ak": "AK", "llsPwd": "Contraseña LLS", "authType": "Tipo de autentificación", "port": "Puerto", "ip": "IP", "organation": "Organización", "channel": "Canal", "schedule": "Plan", "firmVer": "Versión de Firmware", "pleaseChoose": "---Selecciona---", "meterNullMsg": "<PERSON><PERSON><PERSON>", "commnuicatorNullMsg": "Comunicador nulo", "pleSelDevice": "¡Selecciona dispositivo de la lista!", "ipValidErrorMsg": "Formato incorrecto de dirección Ip", "noDeviceFound": "Dispositivo no encotrado", "deviceNumExists": "El número de dispositivo ya existe", "pleSeleCOmmun": "Selecciona comunicador", "pleSaveOrDel": "¡guardar o borrar datos!", "tCommunHChild": "La comunicación tiene un child data！", "pleSelOrganation": "Selecciona Organización!", "addr": "Dirección", "simNum": "Num. SIM", "meterTitle": "Medidor", "commTitle": "Comunicador", "meterInfo": "Medidor", "commInfo": "Comunicador", "commSN": "Nº Comunicador", "communicator": "Comunicador", "ct": "CT", "pt": "PT", "indexDcu": "Indizar DCU", "comPort": "Puerto COM", "keyMeter": "Medidor clave", "archivedMeter": "Medidor archivado", "changeSuccess": "¡Cambia el éxito!", "changeTitle": "Cambio de tipo de comunicación del medidor", "longitude": "<PERSON><PERSON><PERSON>", "latitude": "Latitud", "setLongLat": "Establecer latitud y longitud", "server": "<PERSON><PERSON><PERSON>", "pleaseSelectedProfile": "Por favor, seleccione Perfil", "over100": "¡Error! La cantidad del medidor en HES ahora alcanza el 100% por ciento de la cantidad del medidor en su licencia.", "over90": "¡Advertencia! La cantidad del medidor en HES ahora alcanza el 90% por ciento de la cantidad del medidor en su licencia.", "createTime": "Hora de registro"}, "divFMUPlanMeterList": {"pleSelDevType": "¡Selecciona el tipo de dispositivo!", "pleSelManufac": "!Selecciona el fabricante!", "pleEntPlanDesc": "!Selecciona el plan de descrición!", "pleSelPlanTime": "¡Seleccione la hora de inicio del plan!", "pleSelPlanExpTime": "¡Selecciona la hora expiración del plan!", "planExpPlanTime": "¡La hora vencimiento plan debe ser posterior a la hora inicio plan!", "pleEntNewVer": "¡Por favor, ingrese Nueva Versión!", "pleEntImgeIden": "Ingrese el identificador de imagen", "pleSelFirFile": "!Seleccione el archivo firmware!", "pleSelTaskStartTime": "Seleccione la hora de inicio de la tarea", "pleSelTaskEndTime": "Seleccione la hora de finalización de la tarea!", "taskStartLaterSTime": "La hora inicio tarea debe ser igual o posterior a la hora finalización tarea.", "pleSelTaskCyc": "Seleccione Ciclo de tareas!", "confCreatePlan": "¿Confirma la creación de este plan?", "pleSelModel": "¡Selecciona modelo!", "plan": "Plan", "planReport": "Informe del plan", "jobReport": "Informe trabajo", "deviceSearch": "Búsqueda de dispositivo", "manufacturer": "Fabricante", "model": "<PERSON><PERSON>", "deviceSN": "Nº serie Medidor", "planCreation": "Plan Creación", "planDesc": "Plan Descripción", "planStartTime": "Hora plan de comienzo", "planExpTime": "Tiempo de vencimiento del plan", "newVersion": "Nueva Versión", "imageIdentifier": "Identificador imagen", "firmwareFile": "Archivo Firmware", "taskStartTime": "Hora de inicio tarea", "taskEndTime": "Hora final tarea", "taskCycle": "C<PERSON>lo de tarea", "hour1": "1 hora", "hour2": "2 horas", "hour3": "3 horas", "hour4": "4 horas", "hour5": "5 horas", "hour6": "6 horas", "hour7": "7 horas", "hour8": "8 horas", "hour9": "9 horas", "hour10": "10 horas", "hour11": "11 horas", "hour12": "12 horas", "hour24": "24 hours", "deviceType": "Tipo de dispositivo", "versionType": "Tipo de versión", "currentVersion": "Versión actual", "expTimeAfterStaTime": "¡Tiempo de vencimiento plan debe ser posterior a la tiempo inicio plan!", "curTimeAfterExpTime": "¡El tiempo de vencimiento del plan debe ser mayor que el tiempo actual!", "pleaEntNewVer": "¡Ingrese Nueva Versión!", "pleaEntImaIdent": "Ingrese el identificador de imagen!", "pleaSelFirmFile": "¡Selecciona archivo Firmware!", "pleaSelStartTime": "¡Selecciona tiempo comienzo de tarea!", "pleaSelEndTime": "Selecciona tiempo finalización de tarea!", "startTimeLaterThanEndTime": "¡Tiempo inicio tarea debe ser igual o posterior a la tiempo finalización tarea!", "pleaSelTaskCycle": "¡Seleccione Ciclo de tareas!", "pleaSelVersionType": "¡Seleccione el tipo de versión!", "midFV": "Versión MID", "appFV": "Version de aplicacion", "confCreaPlan": "¿Confirmar para crear este plan?", "information": "Información", "commMeter": "Módulo de medidor GPRS", "commCommunicatior": "Módulo de comunicación de Communicatior", "broadcastMeter": "Medidor (de difusión)", "broadcastComm": "(Broadcast) Communicatior", "broadcastCommMeter": "Módulo de comunicación (Broadcast) del medidor", "broadcastCommCommunicatior": "Módulo PLC de DCU", "broadcastPlcComm": "<PERSON><PERSON><PERSON><PERSON> (Broadcast) de Communicatior", "broadcastHplcComm": "<PERSON><PERSON><PERSON><PERSON> (Broadcast) de medidor"}, "addAssetGPRSMeter": {"sn": "Nº serie", "comSn": "Nº serie Comunicador", "refCommun": "Comunicador de referencia", "referenceMeter": "Rereferencia medidor", "refSnIsEm": "¡Nº referencia medidor está vacío!", "successMatch": "<PERSON><PERSON><PERSON>", "devTypeMis": "Tipo dispositivo parejo fallida", "pleMatchData": "¡Haga coincidir los datos primero!"}, "FMUPlanMeterList": {"title": "Lista de dispositivo", "sn": "Nº medidor", "manufacturerName": "Fabricante", "modelName": "<PERSON><PERSON>", "export": "Exportar", "print": "Imprimir", "fwVersion": "Versión actual"}, "FMUPlanMeterList0": {"title": "Lista de dispositivo", "sn": "Nº medidor", "manufacturerName": "Fabricante", "modelName": "<PERSON><PERSON>", "currentVesion": "Versión actual", "newVersion": "Nueva versión", "startTime": "Tiempo de comienzo", "export": "Exportar", "print": "Imprimir", "expiryTime": "Tiempo de expiración"}, "jobReportJobList": {"title": "Trabajos", "opt": "Acción", "id": "ID", "meterIdDfj": "Nº medidor", "deviceTypeDfj": "Tipo de dispositivo", "snDfj": "Nº medidor", "manufacturerIdDfj": "Fabricante", "modelNameDfj": "<PERSON><PERSON>", "currentVesionDfj": "Antigua versión", "newVersionDfj": "Nueva versión", "startTimeDfj": "Tiempo de comienzo", "expiryTimeDfj": "Tiempo de expiración", "lastExecTimeDfj": "Última hora de ejecución", "stateDfj": "<PERSON><PERSON><PERSON>", "blockSizeDfj": "Tamaño de bloque(Byte)", "blockCountDfj": "Nº bloques transferidos", "fwuProgressDfj": "Progreso", "export": "Exportar", "print": "Imprimir", "confToCancJob": "¿Confirmar cancelar este trabajo?", "cancelled": "Cancelado", "failedReasonDfj": "Razón", "runing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "done": "Listo", "cancel": "Cancelado", "waiting": "Espera", "expired": "<PERSON><PERSON><PERSON>", "jobNoExcute": "¡La tarea no ha empezado a ejecutar!"}, "limiterGroupList": {"id": "Nº", "type": "Tipo", "name": "Nombre grupo", "protocolId": "<PERSON><PERSON><PERSON>", "meterNumber": "Nº del medidor", "introduction": "Descripción", "meterCount": "Contador de medidor", "title": "Lista de grupos limitadores", "titleDetails": "Detalles del grupo limitador", "add": "Agregar grupo limitador", "delete": "Eliminar", "pleaseSelectData": "¡Seleccione un grupo en la lista de grupos limitadores!", "dateError": "Ingrese el formato de fecha correcto"}, "limiterList": {"title": "Configuración del limitador", "id": "Nº", "name": "<PERSON><PERSON>", "value": "Valor", "meterCount": "Contador del medidor", "getProgress": "OBTENER PROGRESOS", "day": "Día", "week": "Se<PERSON>", "season": "Sesión", "upcoming": "Próximo", "processing": "Procedimiento", "success": "Éxito", "failed": "fallo", "timeout": "Fuera de tiempo", "cancelled": "Cancelado", "meterCanBeEm": "¡El medidor no puede estar vacio!", "choLeastCopRead": "¡Elija al menos una copia de la lectura！", "seleLeastOneData": "¡Seleccione al menos una tabla de datos！", "pleLimitCofVal": "¡Configure el valor de configuración del limitador!", "pleLimitCofValNo": "¡Establezca el valor numérico de la configuración del limitador!", "calGetPtogress": "Progreso calendario", "speDayGetPtogress": "Progreso festivos", "stepTariffGetPtogress": "Progreso por etapa de tarifas"}, "measurementGroupList": {"title": "Grupo de lista de medición", "titleDetails": "Detalles del grupo de medición", "add": "Agregar grupo de medidas", "delete": "Eliminar", "id": "Nº", "type": "Tipo", "name": "Nombre del grupo", "protocolId": "<PERSON><PERSON><PERSON>", "referenceGroup": "Referencia grupo", "meterNumber": "Nº de medidores", "introduction": "Descripción", "meterCount": "Contador medidor", "editProfile": "<PERSON><PERSON> perfil", "pleaseSelectData": "Seleccione un grupo en la Lista de Grupos de Medición!", "confirmToDel": "¿Confirma para eliminar este grupo?", "addProfile": "<PERSON><PERSON><PERSON>"}, "meterDataReportList": {"frequency": "Frecuencia", "headTitle": "Reporte datos del medidor", "intervalEnergy": "Informe de calidad de energía", "times": "Tiempo", "groupId": "Grupo", "channel": "Canal de datos", "serizlName": "Nº", "export": "Exportar", "print": "Imprimir", "graphVisualiz": "Visualización gráfica", "noDataChannel": "Selecciona canal No Datos", "tableView": "Vista de tabla", "lineChart": "Gráfico de lineal", "columnChart": "Gráfico de columnas", "reduction": "<PERSON><PERSON><PERSON><PERSON>", "saveAsPicture": "Guardar como imagen", "mustSingTable": "Deber ser una tabla estadísticas individual", "pleSelColumn": "Selecciona una columna", "searchFor": "Buscar...", "selectSn": "Seleccionar número serie", "resultOver": "¡Más de 200 conjuntos de resultados, vuelva a seleccionar los criterios!", "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "allSelected": "Todos seleccionados"}, "planReportJobList": {"title": "Trabajos", "opt": "Acción", "id": "ID", "meterId": "ID Medidor", "sn": "Nº serie medidor", "manufacturerName": "Fabricante", "modelName": "<PERSON><PERSON>", "currentVesion": "Versión actual", "newVersion": "Nueva Versión", "lastExecTime": "Última hora de ejecución", "state": "<PERSON><PERSON><PERSON>", "blockSize": "Tamaño del bloque (Byte)", "blockCount": "Número de bloques transferidos", "fwuProgress": "Progreso", "failedReason": "Razón", "export": "Exportar", "print": "Imprimir", "cancel": "<PERSON><PERSON><PERSON>", "confiCanCelJob": "¿Confirmar para cancelar este trabajo?"}, "planReportList": {"title": "Planes", "opt": "Acción", "id": "id", "deviceType": "Tipo de dispositivo", "introduction": "Plan Descripción", "manufacturerId": "Fabricante", "modelName": "<PERSON><PERSON>", "currentVesion": "Versión actual", "newVersion": "Nueva versión", "taskCycle": "C<PERSON>lo de tarea", "filePath": "Nombre de archivo", "startTime": "Tiempo de comienzo", "expiryTime": "Tiempo de expiración", "taskStartTimeStr": "Comienzo <PERSON>", "taskEndTimeStr": "Finalización de tarea", "state": "<PERSON><PERSON><PERSON>", "done": "<PERSON><PERSON>", "expired": "<PERSON><PERSON><PERSON>", "running": "En funcionamiento", "export": "Exportar", "print": "Imprimir", "cancelled": "Cancelado", "cancel": "<PERSON><PERSON><PERSON>", "conToCanPlan": "¿Confirma cancelar este plan?", "waiting": "<PERSON><PERSON><PERSON><PERSON>", "meter": "Medidor", "communicator": "Comunicador", "valid": "<PERSON><PERSON><PERSON><PERSON>"}, "profileList": {"title": "Lista perfil", "add": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "id": "ID", "mgId": "Id Grupo de Medición", "profileId": "ID perfil", "profileType": "Tipo de perfil", "profileType_view": "Tipo de perfil", "profileName": "Nombre de perfil", "profileCycleType": "Tipo de perfil intervalo", "profileCycle": "Perfil de intervalo", "protocolCode": "Código", "pleaseSelectData": "¡Seleccione un perfil de la lista", "pleSelAntOne": "¡Este perfil ha sido agregado, por favor seleccione otro!", "confirmToDel": "¿Confirma para eliminar este perfil?"}, "seasonList": {"id": "Id", "seasonName": "<PERSON><PERSON> <PERSON><PERSON>", "startTime": "Comienzo de tiempo", "weekProfile": "Perfil de semana", "title": "Lista de sesión", "add": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "month": "<PERSON><PERSON>", "dayofMonth": "Día del mes", "year": "<PERSON><PERSON>", "dayofWeek": "Día de semana", "hour": "<PERSON><PERSON>", "minute": "Min<PERSON>", "second": "<PERSON><PERSON><PERSON>", "pleaseSelectData": "¡Seleccione una temporada de la Lista!", "pleSetSeasonList": ",¡Configure el perfil de la semana en la lista de temporadas!", "seasonAreBeAtMost": "¡Se permite crear 255 temporadas como máximo!", "seleLeastOneData": "¡Selecciona al menos un dato de la tabla!", "noMeterGroup": "¡Compruebe que las propiedades del Grupo Medidores han sido configuradas! "}, "selectedDataChannelList": {"title": "Canales de datos seleccionados", "up": "Subida", "down": "Bajada", "delete": "Eliminar", "id": "", "mgId": "Id grupo de medición", "profileId": "Id perfil", "dataitemId": "Id datos del item", "dataitemName": "Canal de datos", "sortId": "Id corta"}, "specialDayList": {"title": "Lista de Días especiales", "id": "ID", "specialDayName": "ID", "date": "<PERSON><PERSON>", "dayProfile": "Perfil del día", "month": "<PERSON><PERSON>", "dayofMonth": "Días del Mes", "year": "<PERSON><PERSON>", "dayofWeek": "Día de la semana", "pleaseSelectData": "¡Seleccione un día en la lista de días especiales!", "pleSpeDayDprofile": "¡Establezca el Perfil de Día de la Lista de Días Especiales!!", "daysBeAtMost": "¡Se permiten 255 días para ser creados como máximo!", "dayAssCanBeDel": "¡El día está asociado con la Lista especial de días que no se puede eliminar!"}, "sysIntegrationLogList": {"title": "Registro de integración del sistema", "id": "ID", "sn": "Nº serie", "fromId": "Sistema", "tv": "Tiempo requerido", "requestType": "<PERSON><PERSON><PERSON> de solicitud", "requestSubType": "Subtipo de solicitud", "responseResult": "Resultado de respuesta", "export": "Exportar", "print": "Imprimir"}, "timeSynchronizationLog": {"title": "Registro de sincronización de tiempo", "id": "ID", "sn": "Nºserie", "tv": "Tiempo de tarea", "strSynResult": "Resultado de la sincronización", "meterTv": "Hora del medidor", "systemTv": "Hora del sistema", "synTv": "Sincronización de la hora", "failedReason": "<PERSON><PERSON>a fallida", "export": "Exportar", "print": "Imprimir"}, "touGroupList": {"id": "id", "title": "Grupos de medición", "touTitle": "Lista de Grupo TOU", "touTitle_": "Detalles grupo TOU", "measurement": "Medición", "tou": "TOU", "limiter": "Limitador", "addTouGroup": "Añadir grupo TOU Group", "calendar": "Calendario", "specialDay": "Día especial", "delete": "Eliminar", "type": "Tipo", "name": "Nombre del grupo", "protocolId": "<PERSON><PERSON><PERSON>", "meterNumber": "Número de medidores", "introduction": "Descripción", "meterCount": "Contador de medidores", "referenceGroup": "Grupo de referencia", "pleaseSelectData": "¡Seleccione un grupo TOU en lista de grupos!", "canNotBeDel": "¡No puede ser eliminado!", "confirmToDel": "¿Confirma eliminar este grupo?"}, "tracingLogList": {"headTitle": "Administración de grupo de medidores", "meterTracingLog": "Registro de seguimiento de medición", "timeSynLog": "Registro de sincronización de tiempo", "sysInteLog": "Registro de integración del sistema", "userLog": "Registro de usuario", "deviceSn": "Nº dispositivo", "service": "<PERSON><PERSON><PERSON>", "logLevel": "Registro de nivel", "startTime": "Hora de inicio", "endTime": "Hora de finalización", "reqType": "<PERSON><PERSON><PERSON> de solicitud", "user": "Usuario", "logType": "Tipo de registro", "logSubType": "Subtipo de registro", "clikcSearchBut": "Haz clic en el icono para leer", "sychResult": "Resultado de sincronización", "title": "Registro de seguimiento", "export": "Exportar", "print": "Imprimir", "id": "ID", "date": "Tiempo de petición", "serviceId": "<PERSON><PERSON><PERSON>", "type": "Tipo de registro", "level": "<PERSON><PERSON><PERSON> de nivel", "content": "Contenido", "success": "Éxito", "failed": "fallo", "normal": "Normal"}, "userLogList": {"title": "Registro de usuario", "userId": "Id usuario", "tv": "Tiempo de operación", "name": "Usuario", "logType": "Tipo de registro", "logSubType": "Subtipo de registro", "detail": "Contenido", "startTimeCanNot": "¡La hora de inicio o la hora de finalización no pueden estar vacías!", "logTimeBeWDay": "¡El tiempo de consulta de registro será dentro de un día!"}, "unSelectedDataChannelList": {"title": "Canal de datos opcional", "title1": "Leer canales de datos", "profileInterval": "Intervalo de perfil", "add": "<PERSON><PERSON><PERSON>", "id": "id", "mgId": "Id grupo de medición", "profileId": "Id perfil", "dataitemId": "Id datos item", "dataitemName": "Canal datos", "sortId": "Id corta", "pleaseSelectData": "¡Seleccione un canal de datos en esta lista!", "hasMovedToTheTop": "¡Ha subido a la cima!", "hasMovedToTheFoot": "¡Se ha movido al pie!"}, "weekList": {"id": "id", "weekName": "ID Semana", "dayProfile": "Perfil del día", "add": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "title": "Lista de la semana", "pleaseSelectData": "¡Selecciona una semana de la lista!", "pleSetWeekList": "¡Configure el perfil del día en la lista de la semana!", "youCanSingleDay": "¡No puedes borrar un solo día!", "seasonListCanBeDel": "La semana está asociada con la Lista de temporadas que no se puede eliminar", "weeksToBeAtMost": "¡Se permite crear 255 semanas como máximo!", "weekCanBeDel": "¡El día está asociado con la Lista de semanas que no se puede eliminar!"}, "sysUserList": {"headTitle": "Rol del usuario", "title": "Lista de usuario", "orgPassword": "Contraseña actual", "newPassword": "Nueva contraseña", "confirm": "Confirmar", "enterNewPasswordAgain": "Inserta otra vez la nueva contraseña", "askYouForgotPwd": "Solicite ayuda al administrador del sistema si olvidó la contraseña", "id": "id", "username": "C<PERSON><PERSON>", "name": "Nombre", "email": "<PERSON><PERSON><PERSON>", "mobilePhone": "No. de teléfono", "userState": "<PERSON><PERSON><PERSON>", "userType": "Tipo de usuario", "roleId": "Rol", "orgId": "Organización", "lastLoginTime": "Hora del último inicio de sesión", "opt": "Acción", "user": "Usuario", "role": "Rol", "organization": "Organización", "eisable": "Deshabilitar", "enable": "Habilitar", "addUser": "<PERSON><PERSON><PERSON>", "delUser": "Eliminar usuario", "resPwd": "Resetear contraseña", "oldPasswordError": "¡Error, contraseña anterior!", "newPasswordError": "Nueva contraseña y Confirmar contraseña inconsistente!", "resetPassSucc": "Resetear contraseña con éxito", "confirmDisable": "¿Confirmar para deshabilitar esta cuenta de usuario?", "confirmEnable": "¿Confirmar para habilitar esta cuenta de usuario?", "selectUser": "Selecciona al usuario", "myProfile": "Mi perfil", "passSet": "Configuración de contraseña", "licenseSet": "Configuración de licencia", "administrator": "Admininistrador", "normal": "Normal", "password": "Contraseña", "pleaEnterPass": "Inserta tu contraseña", "roleName": "Rol", "validatePwd": "La longitud de la contraseña es de 8 a 15 bits, debe contener letras y números, letras que distinguen entre mayúsculas y minúsculas", "originConfirmRepeat": "¡La contraseña original no es la misma que la nueva contraseña!", "passwordTooLong": "La contraseña es demasiado larga, ingrese menos de 15 dígitos", "originPasswordError": "Error de contraseña de origen", "accountNotExist": "La cuenta no existe"}, "sysRoleList": {"title": "Lista Rol", "id": "id", "name": "Nombre", "userCount": "Cuenta de usuario", "description": "Descripción", "delete": "Eliminar", "addRole": "<PERSON><PERSON><PERSON> rol", "rolenameNull": "El nombre no puede estar vacío", "selectRole": "¡Selecciona el rol¡", "deleteRole": "¿Confirmar para eliminar este rol?", "queryResultNull": "El resultado de la consulta es nulo!", "unableDel_user": "El rol tiene subusuarios que no pueden ser eliminados!", "operaConfig": "Configuración de operación ", "roleNameExist": "¡El nombre del rol ya existe!"}, "meterConfigurationList": {"systemFunction": "Función del sistema", "title": "Configurar operación", "id": "id", "isSelect": "Es selecto", "operationname": "Item de operación", "description": "Descripción"}, "organizationList": {"title": "Lista de Organización", "addOrganization": "Añadir Organización", "delete": "Delete", "name": "Nombre", "id": "id", "userCount": "Contador de usuario", "meterCount": "Contador de medidor", "mobile": "Nº teléfono", "address": "Dirección", "description": "Descripción", "contactMan": "Persona de contacto"}, "sysOrganizationList": {"id": "id", "parentOrg": "Organización principal", "name": "Nombre", "mobile": "No. teléfono", "address": "Dirección", "description": "Descripción", "unableDel_org": "¡La Organización tiene sub organizaciones que no pueden ser eliminadas!", "unableDel_comm": "¡La Organización tiene sub comunicadores que no pueden ser eliminados!", "unableDel_meter": "¡La Organización tiene medidores que no pueden ser eliminados!", "unableDel_user": "¡The organization has sub users that cannot be deleted!", "moveOrg": "Mover Organización", "moveOrgError": "¡No se puede seleccionar la propia Organización!", "selectOrg": "¡Selecciona Organización!", "organization": "Organización", "addOrganization": "Añadir Organización", "moveSuccess": "Movimiento éxitoso", "admitFourLevelOrg": "¡Se permiten 4 niveles de organizaciones como máximo!", "deleteOrg": "¿Confirma eliminar esta Organización?", "noMoveToChildOrg": "¡A la Organización no se le permite moverse a su Organización subordinada!"}, "device_read_result_list": {"id": "id", "sn": "Nº de serie", "profileId": "Id perfil", "profileName": "Perfil", "communication": "Comunicación", "requestTime": "Tiempo de petición", "dataChannel": "Canal de datos", "value": "Valor", "reponseTime": "Tiempo de datos", "startTime": "Hora de inicio", "endTime": "Hora de finalización", "status": "<PERSON><PERSON><PERSON>", "statusView": "<PERSON><PERSON><PERSON>", "export": "Exportar", "print": "Imprimir"}, "device_read_schedule_list": {"id": "id", "sn": "Nº de serie", "model": "<PERSON><PERSON>", "communication": "Comunicación", "profileId": "Id perfil", "profile": "Perfil", "startTime": "Hora de inicio", "endTime": "Hora de finalización"}, "device_read_channel_list": {"title": "Resul<PERSON><PERSON>", "checkReadTitle": "Haz clic en el icono para leer", "id": "id", "sn": "Nº Medidor", "communicator": "Nº Comunicador", "commmunication": "Comunicación", "dataChannel": "Canal de datos", "value": "Valor", "status": "<PERSON><PERSON><PERSON>", "statusView": "<PERSON><PERSON><PERSON>", "requestTime": "Tiempo de petición", "responseTime": "Tiempo de respuesta", "export": "Exportar", "print": "Imprimir", "total": "Total", "completed": "Completado", "success": "Éxito", "failed": "Fallo", "processing": "Procesando", "read": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "cancelFailure": "Cancelar el fallo"}, "deploymentAndClusterManagement": {"title": "Implementación y gestión de medición concentrada", "serviceConfig": "Configuración de servidor y servicio", "channelService": "Detalles de configuración", "name": "Nombre", "ip": "IP", "hostId": "Host ID", "server": "<PERSON><PERSON><PERSON>", "type": "Tipo", "ipOrHostId": "IP/Host ID", "status": "<PERSON><PERSON><PERSON>", "properties": "Propiedades", "value": "Valor", "description": "Descripción", "addServer": "<PERSON><PERSON><PERSON>", "addService": "<PERSON><PERSON><PERSON> servic<PERSON>", "deleteServer": "Delete Servidor", "deleteService": "Delete Servicio", "action": "Acción", "save": "Guardar", "editChannel": "Editar canal", "unableDel_server": "¡El servidor tiene sub servicios que no pueden ser eliminados!", "unableDel_service": "¡El servicio tiene sub propiedades que no pueden ser eliminadas!", "serviceExistSystem": "¡Este servicio ya existe en el sistema!", "serviceExistServer": "¡Este servicio ya existe en el servidor!", "ipExistSystem": "¡Esta ip ya existe en el sistema!", "hostIdExistServer": "¡Este ID de host ya existe en el servidor!", "pleaseSelectService": "¡Seleccione un servicio!", "pleaseAddChannel": "¡Agregue lista de servicio de canal!", "nameExist": "¡Este nombre ya existe en el sistema!", "stop": "<PERSON><PERSON>", "running": "En funcionamiento", "channel": "Canal", "messageBus": "Mensaje Bus", "schedule": "Planificación", "UCI": "UCI", "calculation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "application": "Aplicación"}, "serviceConfig": {"title": "Configuración de servicio", "id": "id", "parent": "ID Principal", "serviceTypeTemp": "Tipo de servicio", "introduction": "Nombre", "serviceType": "Tipo", "ip": "IP/Host ID", "isOnline": "<PERSON><PERSON><PERSON>", "delete": "Eliminar"}, "meterDataEventExport": {"title": "Medidor de datos y exportación de eventos", "dataChannel": "Canal de datos", "exportProgress": "Progreso de exportación", "exportTime": "Tiempo de exportación", "result": "<PERSON><PERSON><PERSON><PERSON>", "unableDel_server": "¡El servidor tiene sub servicios que no pueden ser eliminados!", "unableDel_service": "El servicio tiene sub propiedades que no pueden ser eliminadas!", "serviceExistSystem": "¡Este servicio ya existe en el sistema!", "serviceExistServer": "¡Este servicio ya existe en el servidor!", "ipExistSystem": "¡Esta ip ya existe en el sistema!", "hostIdExistServer": "¡Este ID de host ya existe en el servidor!", "pleaseSelectService": "¡Seleccione un servicio!", "pleaseAddChannel": "¡Añadir el canal en la lista de servicio!", "nameExist": "¡Este nombre ya existe en el sistema!"}, "stepTariffList": {"title": "Lista de grupos de tarifas escalonadas", "titleDetails": "Detalles del grupo de tarifas escalonadas ", "id": "id", "stepTariff": "Ta<PERSON>fa es<PERSON>", "description": "Descripción", "groupName": "Nombre del grupo", "stepName": "Nombre escalonado", "meterStandard": "<PERSON><PERSON><PERSON>", "startQuantity": "Cantidad de inicio (kWh)", "meterNumber": "Nº medidor", "endQuantity": "Cantidad final (kWh)", "activateTime": "Activar tiempo", "price": "Precio ($)", "referenceGroup": "Referencia de grupo", "addGroup": "Agregar grupo de tarifas escalonadas", "addStep": "<PERSON><PERSON><PERSON> escal<PERSON>", "pleaseSelectData": "¡Tarifa escalonada en esta lista!", "nameExist": "¡El nombre de grupo de tarifa escalonada ya existe!", "stepNameExist": "¡El nombre de tarifa escalonada ya existe!", "selectStepTariff": "¡Seleccione un grupo de tarifas escalonadas!", "deleteStepTariff": "¿Confirmar para eliminar este grupo de tarifas escalonadas?", "selectStep": "¡Selecciona un escalonado!", "delStep": "¿Confirmar para eliminar este escalonado?", "endGreaterThanStart": "¡La cantidad final debe ser mayor que la cantidad inicial!", "pleaseAddStep": "¡Agregue la lista de escalonada!", "pleaseCorrectFormat": "¡Introduzca los datos del formato correcto!", "reselectStepName": "¡Seleccione el nombre del escalonado de nuevo!", "startQuantityError": "¡Será conºsistente con la cantidad final del escalonado anterior!", "delete": "Eliminar", "save": "Guardar", "stepTariffGetPtogress": "Progreso de tarifa escalonada", "friendly": "Simpático", "prepay": "<PERSON><PERSON> por adelantado", "touTariff": "Tarifa TOU", "friendlyPeriod": "<PERSON><PERSON><PERSON>", "friendlyWeekDay": "Día amistoso de la semana", "friendlySpecialDay": "Día especial amistoso", "friendlyTou": "TOU amigable", "periodId": "ID de período", "start": "<PERSON><PERSON><PERSON><PERSON>", "end": "Fin", "weekDayId": "ID de día de la semana", "weekDay": "Día laborable", "specialDayId": "ID de día especial", "enable": "Habilitar", "touId": "ID de TOU", "prepayDetail": "Detalles del prepago", "touAtMost": "Se permite crear una tarifa de 4 TOU como máximo", "weekAtMost": "Se permite crear 7 días como máximo", "periodAtMost": "Se permite crear 8 períodos como máximo", "startLaterEnd": "¡La hora de inicio debe ser posterior a la hora de finalización!", "touExist": "ha existido", "periodRepeat": "Los períodos amistosos no deben superponerse", "stepExist": "El precio de paso existe no se puede agregar el precio TOU", "cannotOp": "No se puede operar más que el tiempo de activación"}, "meterConfiguration": {"title": "Configuración del medidor", "canNotBeDelete": "¡La tarea en el estado de procesamiento no se puede eliminar!", "pleaseSelectData": "¡Seleccione una tarea en esta lista!", "confirmDeleteTask": "¿Estás seguro de eliminar esta tarea?", "stepNameExist": "¡Este nombre escalonado ha sido agregado, seleccione otro!"}, "meterGroupUpgrade": {"plan": "Plan", "planReport": "Reporte plan", "jobReport": "Reporte de trabajp", "deviceSearch": "Buscar dispositivo", "groupType": "Tipo de grupo", "group": "Grupo", "measurement": "Medición", "TOU": "TOU", "limiter": "Limitador", "stepTariff": "Tarifa escalonada", "friendly": "Friendly", "search": "Búsqueda", "deviceSN": "Nº Serial medidor", "pleSelGroupType": "¡Selecciona tipo de grupo!", "pleSelGroup": "¡Selecciona grupo!", "pleEntPlanDesc": "Confirma plan de descripción!", "noDeviceFound": "¡No se encontraron dispositivo!", "startEqualExpiry": "¡Plan de Tiempo de caducidad debe ser posterior plan de Hora de inicio!", "taskStartEqualEnd": "¡Hora de inicio de tarea debe ser igual o posterior a la hora de finalización de la tarea!", "ifExpired": "Si expira", "valid": "Valido", "expired": "<PERSON><PERSON><PERSON>", "startTime": "Hora de inicio", "endTime": "Hora de finalización", "noFoundPlan": "¡No se encontraron planes!", "pleaseSelectAplan": "¡Selecciona un plan!", "pleaseSelectState": "Seleccione un plan caducado y un trabajo en espera o caducado", "createPlanSucc": "Creado con éxito. El nuevo plan puede ser consultado en la pestaña del Informe", "exceptionMsg": "¡El servicio de trabajo transfiere datos inusuales, verifica y crea un plan otra vez!", "jobUpgradeSucc": "¡Todos los trabajos de este plan se han actualizado con éxito!", "all": "Todos", "pleaseSelModel": "¡Selecciona modelo!", "hasMeter": "Este grupo tiene medidores que no se pueden eliminar"}, "divMGU_PlanMeterList": {"sn": "Nº serie", "manufacturer": "Fabricante", "model": "<PERSON><PERSON>", "groupType": "Tipo de grupo", "group": "Grupo", "startTime": "Plan hora de inicio", "expiryTime": "Plan hora de finalización "}, "MGU_planReportList": {"opt": "Acción", "introduction": "Plan Descripción", "groupType": "Tipo de grupo", "groupName": "Nombre de grupo", "model": "<PERSON><PERSON>", "state": "Estado", "taskCycle": "C<PERSON>lo de tarea", "done": "<PERSON><PERSON>", "expired": "<PERSON><PERSON><PERSON>", "running": "<PERSON><PERSON><PERSON><PERSON>", "waiting": "<PERSON><PERSON><PERSON><PERSON>", "startTime": "Plan hora de inicio", "expiryTime": "Plan tiempo de expiración", "taskStartTime": "Hora de inicio de la tarea", "taskEndTime": "Hora de finalización de la tarea"}, "MGU_planReportJobList": {"title": "Trabajos", "id": "ID", "meterId": "ID medidor", "sn": "Nº serie del Medidor", "manufacturer": "Fabricante", "model": "<PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON>", "lastExecTime": "Última hora de ejecución", "reason": "Razón", "running": "<PERSON><PERSON><PERSON><PERSON>", "done": "<PERSON><PERSON>", "cancel": "Cancelado", "waiting": "<PERSON><PERSON><PERSON><PERSON>", "expired": "<PERSON><PERSON><PERSON>", "export": "Exportar", "print": "Imprimir", "failedReason": "Razón", "addPlan": "Añadir <PERSON>"}, "connOrDisconnList": {"title": "Resul<PERSON><PERSON>", "mainTitle": "Conectar / Desconectar", "sn": "Nº Serie Medidor", "commSN": "Nº Serie Comunicador", "communication": "Comunicador", "command": "Comand<PERSON>", "status": "<PERSON><PERSON><PERSON>", "requestTime": "Tiempo de petición", "responseTime": "Tiempo de respuesta", "connect": "Conectar", "disconnect": "Desconectar", "pleSelMeter": "¡Selecciona medidor!", "export": "Exportar", "print": "Imprimir", "readTimeOut": "Lectura fuera de tiempo", "failReason": "Razón", "relayOptional": "Relé Opcional", "internal": "<PERSON><PERSON> principal", "external": "Extensión Relé 1"}, "billingReportList": {"reports": "Reportes", "reportList": "Lista de Reporte", "startTime": "Hora de inicio", "endTime": "Hora de finalización", "organization": "Organización", "lineLossObjectType": "Tipo", "timeType": "Tipo de tiempo", "lineLossObject": "Ob<PERSON><PERSON> de línea", "import": "Importar(KWh)", "export": "Exportar(KWh)", "loss": "Pérdida(KWh)", "rate": "<PERSON><PERSON>(%25)", "date": "<PERSON><PERSON>", "lineLossStartTimeTip": "¡Seleccione el tiempo de inicio de pérdida de línea!", "lineLossEndTimeTip": "¡Seleccione el tiempo de finalización de pérdida de línea!", "timeIssueAlert": "¡La hora de finalización del plan debe ser posterior a la hora de inicio de pérdida de línea!", "transformer": "Transformador", "line": "Lín<PERSON>", "meter": "Medidor", "communicator": "Comunicador", "daily": "Diario", "monthly": "<PERSON><PERSON><PERSON>", "title": "Informe de facturación del cliente", "sn": "Nº serie del medidor", "time": "Tiempo", "energy": "Consumo de energía(KWh)", "consumption": "Cantidad de energía(USD)", "rateOther": "<PERSON><PERSON>(%)", "serach": "Buscar", "reportTime": "Hora del informe", "assetSelectTip": "¡Seleccione el activo primero!"}, "unknowDeviceManageReport": {"unknowDeviceManageStartTimeTip": "¡Seleccione la hora de inicio del dispositivo desconocido!", "unknowDeviceManageEndTimeTip": "Seleccione la hora de finalización del dispositivo desconocido", "timeIssueAlert": "¡La hora de finalización del plan debe ser posterior a la hora de inicio de la pérdida de línea!", "title": "Informe de administración de dispositivos desconocidos"}, "lineLossReportList": {"reportList": "Lista de reporte", "startTime": "Hora de inicio", "endTime": "Hora de finalización", "organization": "Organización", "lineLossObjectType": "Tipo", "timeType": "Tipo de tiempo", "lineLossObject": "Objeto de pérdida de línea", "import": "Importada(KWh)", "export": "Exportada(KWh)", "loss": "Pérdida(KWh)", "rate": "<PERSON><PERSON>(%25)", "date": "<PERSON><PERSON>", "lineLossStartTimeTip": "¡Seleccione el tiempo de inicio de pérdida de línea!", "lineLossEndTimeTip": "¡Seleccione el tiempo de finalización de pérdida de línea!", "timeIssueAlert": "¡La hora de finalización del plan debe ser posterior a la hora de inicio de pérdida de línea!", "transformer": "Transformador", "line": "Lín<PERSON>", "meter": "Medidor", "communicator": "Comunicador", "daily": "Diario", "monthly": "<PERSON><PERSON><PERSON>", "title": "Reporte de línea perdida", "rateOther": "<PERSON><PERSON>(%)", "type": "Tipo", "meterName": "Nombre del medidor", "tv": "Tv", "name": "Canal de datos", "dataValue": "<PERSON><PERSON>", "objectName": "Objeto de pérdida de línea", "entity": "Entidad", "entityName": "Nombre de entidad", "objectComparison": "Comparación de objetos", "yoyComparison": "Comparar año a año", "qoqComparison": "Comparar en tres meses", "cancelComparison": "Cancel Comparison", "yoy": "YoY", "qoq": "QoQ"}, "importAndExportReport": {"organization": "Organización", "timeType": "Tipo de tiempo", "dataType": "Tipo de <PERSON>", "startTime": "Hora de inicio", "endTime": "Hora de finalización", "result": "<PERSON><PERSON><PERSON><PERSON>", "time": "Tiempo", "value": "Valor(kWh)", "meterSn": "Nombre", "import": "Importar", "export": "Exportar", "detail": "Detalles", "noRecords": "Sin registro", "timeNotExist": "¡Esta escala de tiempo no existe!", "title": "Informe de estadísticas de suministro", "curveType": "Tipo de curva"}, "lineManagementList": {"title": "Lín<PERSON>", "lineSn": "Nº serie línea", "lineName": "Nombre de la línea", "lineList": "Lista de línea", "name": "Nombre", "organization": "Organización", "type": "Tipo", "voltageLevel": "Nivel de voltaje", "properties": "Características", "basicInformation": "Información básica", "transformers": "Transformadores", "calculationObject": "Objeto de calculación", "sn": "Nº de serie", "transformerList": "Lista de transformadores", "transformerSn": "Nº serie transformadores", "calObjList": "Lista de objeto de calculación", "calculationObjectName": "Nombre de objeto de calculación", "cycle": "<PERSON><PERSON><PERSON>", "calculationObjectProperties": "Características de objeto de calculación", "dataChannelList": "Lista de canal de datos", "meterSn": "Nº serie medidor", "meterName": "Nombre del medidor", "dataChannel": "Canal de datos", "addDataChannel": "Añadir canal de datos", "addDataChannelFromTransformers": "Añadir canal de datos desde transformadores", "addLine": "<PERSON><PERSON><PERSON>", "opt": "Acción", "busbar": "Busbar", "feeder": "Fuente de alimentación", "unit": "kV", "meterNotExist": "¡No se ha encontrado ningún medidor!", "noCalObjFound": "¡No se ha encontrado ningún objeto de calculación!", "nolineFound": "¡No se ha encontrado ninguna línea!"}, "transformerManagementList": {"title": "Transformador", "transformerSn": "Nº serie Transformador", "transformerName": "Nombre del Transformador", "transformerList": "Lista del transformador", "name": "Nombre", "organization": "Organización", "ratedCapacity": "Rango de capacidad", "properties": "Características", "basicInformation": "Información básica", "calculationObject": "Objeto de calculación", "sn": "Nº serie", "address": "Dirección", "calculationObjectList": "Lista de objeto de calculación", "calculationObjectName": "Nombre de objeto de calculación", "type": "Tipo", "cycle": "<PERSON><PERSON><PERSON>", "calculationObjectProperties": "Características de objeto de calculación", "dataChannelList": "Lista canales de datos", "meterSn": "Nº serie del medidor", "meterName": "Nombre del medidor", "dataChannel": "Canal de datos", "addDataChannel": "Añadir canal de datos", "noTrFound": "¡No se ha encontrado ningún transformador!"}, "calculationObjectList": {"define": "Define objeto de calculación", "title": "Objeto de calculación", "name": "Nombre", "type": "Tipo", "cycle": "<PERSON><PERSON><PERSON>", "entityType": "Tipo de entidad", "entityName": "Nombre de entidad", "calculationObject": "Objeto de calculación", "properties": "Características", "calculationObjectList": "Lista de objetos de calculación", "meterSn": "Nº seríe del medidor", "dataChannel": "Canal de datos", "addDataChannel": "Añadir canal de datos", "lineLoss": "Pérdida de línea", "import": "Suministro", "export": "Ventas", "daily": "Diario", "monthly": "<PERSON><PERSON><PERSON>", "line": "Lín<PERSON>", "transformer": "Transformador", "organization": "Organización", "pleaseChoose": "---<PERSON><PERSON><PERSON><PERSON>r---", "all": "Todos", "addBySearch": "Agregar por b<PERSON>que<PERSON>", "addFromRelationship": "Agregar desde relación", "addMeteringPoint": "Agregar punto de medición", "defaultType": "<PERSON>ipo predeterminado", "defaultDataChannel": "Canal de datos predeterminado", "gridLossReport": "Informe de pérdida de red", "rangeOfLossRate": "<PERSON>ngo de tasa de p<PERSON>rdida (%)", "reference": "Referencia"}, "dcuConfiguration": {"activeCollect": "<PERSON><PERSON><PERSON> leer el medidor", "passiveCollect": "<PERSON><PERSON><PERSON> leer el medidor directamente", "startedTime": "Hora de inicio", "delayTime": "Tiempo retrasado", "switch": "Cambiar", "searchMeterSwitch": "Interruptor de medidor de búsqueda", "rs485LogicalAddress": "Dirección lógica RS485", "title": "Configuración del DCU", "meterListUpload": "Lista de parámetros del medidor", "otherParameter": "<PERSON><PERSON><PERSON>", "communicatorSn": "Nº serie comunicador", "set": "Escribir", "meterTitle": "Lista de medidores", "meterTitleHES": "Lista de medidores (HES)", "meterTitleDCU": "Lista de medidores (Lectura desde DCU)", "meterSn": "Nº serie medidor", "pointNum": "Nº de punto", "logicalName": "Nombre lógico", "baudRate": "<PERSON><PERSON>", "comNum": "Nº COM", "isVip": "Llave de usuario", "protocol": "Protocolo", "communicatorAddress": "Dirección de comunicador", "result": "<PERSON><PERSON><PERSON><PERSON>", "communication": "Comunicación", "status": "<PERSON><PERSON><PERSON>", "requestTime": "Tiempo de solicitud", "responseTime": "Tiempo de respuesta", "reason": "Razón", "get": "Lectura", "hesCommunicationParameter": "Parámetro de comunicación HES", "gprsNetworkParameter": "Parámetro de red GPRS", "clock": "<PERSON><PERSON><PERSON>", "concentratorVersion": "Información de la versión del concentrador", "encryptionMode": "Modo de encriptación", "encryptionType": "Tipo de cifrado", "encryptionKey": "Clave de transmisión", "globalEncryption": "Cifrado global", "dedicatedEncryption": "<PERSON><PERSON><PERSON> de<PERSON>ado", "unencryted": "Sin encriptar", "ipAddress": "IP Dirección", "port": "Puerto", "userName": "Nombre de usuario", "password": "Contraseña", "dialedNumber": "<PERSON>ú<PERSON><PERSON> marca<PERSON>", "ipAddressBackup": "Dirección IP de copia de seguridad", "portBackup": "Puerto - Copia de seguridad", "apn": "APN", "resetCommand": "Comando de reinicio", "resetType": "Tipo de reinicio", "pleaseChooseCommSn": "¡Selecciona nº serie comm!", "pleaseFillIn": " ¡Por favor, complete la información!", "pleaseFillInCorrect": "¡Por favor, complete la información correcta!", "pleaseChoose": "---<PERSON><PERSON><PERSON><PERSON>r---", "hardwareReset": "Reiniciar Hardware", "dataReset": "<PERSON>ini<PERSON><PERSON> datos", "parameterResetExceptComm": "Reset de parámetros (excepto comunicación)", "parameterAndDataReset": "Reiniciar parámetros y datos", "parameterExceptHesReset": "Resetear Parámetros (Exepto Parámetros de comunicación del HES)", "importantUserSettingList": "Configuración de usuario importante", "meterModel": "Modelo del medidor", "importantFlag": "Bandera importante", "concentratorimportantFlag": "Indicador del medidor clave -Concentrador", "command": "Comand<PERSON>", "resetTypeCannotBeRead": "<PERSON>ipo de reinicio, no se puede leer", "itemCannotBeRead": "El artículo no se puede leer", "selectCommunicators": "Selecciona comunicador", "selectImportantUser": "Seleccione el medidor que desea establecer como un usuario importante", "selectResetType": "Selecciona Restablecer tipo", "wiringMethod": "Método de cableado", "comType": "Tipo COM", "versionInfo": "Información de versión", "equipmentNumber": "Número de equipo", "softwareVersion": "Versión del software", "softwareDate": "Fecha del software", "SoftwareItem": "Elemento de software", "communicationProtocol": "Protocolo de comunicación", "hardwareVersion": "Versión del hardware", "hardwareDate": "Fecha de hardware", "versionInformationWrite": "La información de la versión del concentrador no escribe", "importUserPleaseSelect": "Seleccione el medidor que desea establecer como usuario importante", "blackListPleaseSelect": "Seleccione el medidor que desea configurar como medidor negro.", "indexDcu": "<PERSON><PERSON><PERSON>", "heartbeatCycle": "C<PERSON>lo de latidos", "heartbeatCycleUnit": "C<PERSON>lo de latido (unidad: seg)", "energyProfileReadingCycle": "Ciclo de lectura del perfil energético", "energyProfileReadingCycleUnit": "Ciclo de lectura del perfil energético (Unidad: seg)", "plcChannelSendTimes": "Tiempos de envío de canal de PLC", "plcChannelSendTimesUnit": "Tiempos de envío del canal del PLC (Unidad: seg)", "plcChannelTimeout": "Tiempo de espera del canal del PLC", "plcChannelTimeoutUnit": "Tiempo de espera del canal del PLC (unidad: seg)", "concentratorAddress": "Dirección del concentrador", "managementLogicalDeviceName": "Nombre del dispositivo lógico de administración", "dormancyTime": "Tiempo de inactividad", "dormancyTimeUnit": "Tiempo de inactividad (unidad: seg)", "concentratorIpAddress": "Dirección IP del concentrador", "eventConfiguration": "Configuración de eventos", "broadcastTimeOrderEnable": "Habilitar orden de tiempo de transmisión", "broadcastTimeParameter": "Parámetro de tiempo de transmisión", "gprsSignalStrength": "Intensidad de la señal GPRS", "gprsImeiSerialNumber": "Número de serie GPRS IMEI", "gprsNetworkStandard": "Estándar de red GPRS", "wanDhcpEnable": "Activar WAN DHCP", "csMode": "Modo C / S", "serverPort": "Puerto de servicio", "gprsModemVersion": "Versión del módem GPRS", "plcModemVersion": "Versión del módem del PLC", "rfModemVersion": "Versión de módem RF", "blacklistOfMeter": "Lista negra de medidores", "meterModuleSoftwareVersion": "Versión de software del módulo del medidor", "dcuAsClient": "Dcu como cliente", "dcuAsServer": "Dcu como servidor", "terminalIp": "Terminal Ip", "subnetMask": "Máscara de subred", "gatewayMask": "Máscara de puerta de enlace", "ItemDontWrite": "El artículo no escribe", "blackFlag": "Bandera Negra", "pleaseCsMode": "¡Elija el modo C / S!", "broadcastKey": "Clave de transmisión", "firmwareVersionLaterThan": "la versión de firmware es posterior a 1.39", "vpnDial": "Marcación VPN", "vpnNet": "Vpn Net", "connectedState": "Estado conectado", "topoInfo": "Información de topología", "shortAddress": "Dirección corta", "signalIntensity": "Intensidad de la señal", "netStatus": "Estado de la red", "phaseInfo": "Información de fase", "networkAccess": "Acceso a la red", "networkIng": "Entrar en la red", "beaconForwarding": "Reenvío de balizas", "waitingConf": "Esperando configuración", "searchNode": "Nodo de bús<PERSON>da", "delMeter": "Eliminar medidor", "selectLogicName": "Por favor, seleccione un LogicName!", "relayControlMode": "Modo de control de relé", "searchLevel": "Nivel de topología", "lossedInfo": "Información de topología perdida", "total": "Total", "lossed": "<PERSON><PERSON><PERSON>", "level": "<PERSON><PERSON>", "dayCurves": "Número de perfiles diarios del medidor de lectura (perfiles de eventos)", "monthCurves": "Número de perfiles mensuales del medidor de lectura", "loadCurves": "Número de perfiles de carga del medidor de lectura", "eventLogCurves": "Lectura de perfiles de eventos", "writeEvent": "Escribir :", "readEvent": "Leer:", "eventLogName": "Nombre del perfil del evento", "obisCode": "<PERSON><PERSON><PERSON>", "selectEventToWrite": "Seleccione perfiles de eventos para escribir", "enterValueTimes": "Ingrese un valor entre 30 y 120 segundos", "timeZone": "Zona horaria"}, "dcuModuleConfiguration": {"title": "Configuración del módulo", "moduleParameter": "Parámetro del módulo", "pleaseChooseMeterSn": "¡Elija el medidor sn!", "meterSn": "Nº serie medidor", "APN": "APN", "userAndPassword": "Usuario y contraseña", "pinCode": "Código PIN", "hesIpAndPort": "IP y puerto HES", "ftpIpAndPort": "IP y puerto FTP", "autoConnectMode": "Modo de conexión automática", "autoAnswerMode": "Modo de respuesta automática", "inactivityTimeOut": "Tiempo de inactividad", "noNetworkCommunicationTimeoutReset": "Sin reinicio del tiempo de espera de comunicación de red", "user": "Nombre de usuario", "password": "Contraseña", "inactiveConnection": "Conexión inactiva", "activeConnection": "Conexión activa", "activeConnectionNotInWindowTime": "Conexión activa (no se puede activar en tiempo de Windows)", "activeConnectionInWindowTime": "Conexión activa (se puede activar en tiempo de Windows)", "inactiveConnectionRouseHesConnect": "Conexión inactiva (puede activar la conexión HES)", "alwaysConnectedGprs": "Siempre conectado GPRS", "notNeedToAnswer": "Conexión (no hay necesidad de responder a despertar)", "needToAnswer": "Conexión (hay necesidad de responder despertar)", "closedGprs": "GRPS cerrado (no se puede activar)", "selectConnectType": "Seleccione el tipo de conexión", "selectModeType": "Seleccione el tipo de modo"}, "importGprsMeter": {"importFromShipmentFile": "Importar archivo del medidor transportado", "importGprsMeter": "Importar archivo de transporte", "num1": "1", "num2": "2", "num3": "3", "num4": "4", "step1": "Paso uno", "step2": "Paso dos", "step3": "Paso tres", "step4": "Paso cuatro", "assetType": "Tipo de activo", "GPRSMeter": "Medidor GPRS", "assetFile": "Archivo de activo", "manufacturer": "Fabricante", "model": "<PERSON><PERSON>", "firmwareVersion": "Versión Firmware", "measurementGroup": "Grupo de medición", "collectionSchemeGroup": "Grupo de Esquemas de Colección", "meterExist1": "El medidor con número de Nº serie", "meterExist2": "Ya existe", "checkProgress": "Ver progreso", "importProgress": "Importar progreso", "selectFile": "Seleccionar archivo", "checkStatus": "Verificando archivos de Excel,¡no cambiar!", "pleaseSelectFile": "¡Selecciona archivo de excel", "noDataInExcel": "¡No hay datos en el archivo de Excel!", "verificationFailed": "Verificación fallida, verifique y vuelva a importar!", "pleaseSelMeaGroup": "¡Selecciona grupo de medición!", "pleaseSelColSchGroup": "¡Seleccione el Grupo de Esquema de Colección!", "checkCompleted": "¡Chequeo completado!", "importStatus": "¡Importar archivos de Excel, no cambiar!", "deviceTypeIsEmpty": "¡El tipo de dispositivo está vacío!", "pleaseSelCommType": "¡Selecciona el tipo de comunicador!", "shipmentFileType": "Tipo de archivo de transporte", "meterSipmentFile": "Archivo de medidor en transporte", "shipmentFile": "Archivo de transporte", "verifySuccess": "Verificación satisfactoria", "verifyFail": "Verificación fallida"}, "customerList": {"customer": "Cliente", "customerList": "Lista de cliente", "customerSn": "Nº serie del cliente", "customerType": "Tipo de cliente", "customerName": "Nombre  del cliente", "industryType": "Tipo de industria", "onlyOneError": "El Nº serie del medidor o cliente es referenciado por otro cliente"}, "dataComminicationStatus": {"title": "Reporte del estado de comunicación", "statusUpdateTime": "Estado del tiempo de actualización", "networkAddress": "Dirección network", "commComType": "Tipo COM Comunicador", "meterComType": "Tipo COM Medidor", "offlineTimeDays": "Tiempo desconectado(Días)", "offline": "Desconectado", "online": "Conectado"}, "saleStatReport": {"missData": "<PERSON><PERSON> perdidos"}, "veeList": {"validatingReport": "Validando reporte", "exception tatal": "Excepción Total", "class1": "Clase 1", "class2": "Clase 2", "class3": "Clase 3", "class4": "Clase 4", "class5": "Clase 5", "veeGroup": "Grupo VEE", "veeGroupList": "Lista de grupos VEE", "groupName": "Nombre del grupo", "meterCount": "<PERSON><PERSON><PERSON>", "descr": "Descripción", "veeRuleList": "Lista de reglas VEE", "ruleName": "Nombre de la regla", "ruleType": "T<PERSON><PERSON> de regla", "class": "Clase", "dataChannel": "Canal de datos", "event": "Evento", "method": "<PERSON><PERSON><PERSON><PERSON>", "estimationReportProgressDelay": "Informe de estimación - Retraso de progreso", "estimationReportMissData": "Informe de estimación - Datos perdidos", "meterSn": "Nº serie medidor", "profile": "Perfil", "communicatorSn": "Comunicador SN", "communicationType": "Tipo de comunicación", "progress": "Progreso", "progressDelay": "Retraso de progreso", "time": "<PERSON><PERSON>", "missDataListDetail": "Detalle de lista de datos perdidos", "missDataList": "Lista de datos perdidos", "progressDelayList": "Lista de retraso de progreso", "progressDelayListDetail": "Detalle de la lista de retrasos de progreso", "serizlNo": "Dispositivo SN", "missData": "<PERSON><PERSON> perdidos", "deadline": "<PERSON><PERSON> lí<PERSON>", "delayExported": "Solo se pueden exportar datos de retraso", "missExported": "Solo se pueden exportar los datos perdidos", "rowExport": "Seleccione la fila para exportar", "taskTv": "Tiempo de tarea", "ruleStatus": "Estado de la regla", "ruleClass": "Clase de regla", "editVeeRule": "Editar regla en V", "addVeeRule": "Agregar regla en V", "pleSeleVeeGroup": "¡Seleccione Vee Group!", "pleFillName": "¡El nombre no puede estar vacío!", "pleCycleCount": "¡El recuento de ciclos deben ser números!", "pleDefaultValue": "¡El valor del parámetro deben ser números!", "eventRuleDesc": "Descripción de la regla de evento", "dataItemName": "Nombre del canal de datos", "dataItemKey": "Clave de canal de datos", "cycleCount": "Contador de Cíclos", "cycleType": "Tipo de ciclo", "paramKey": "Tecla de parámetro", "defaultValue": "Valor de parámetro", "dataItemList": "Lista de canales de datos", "veeEventParamList": "Lista de parámetros", "timeStamp": "sello de tiempo"}, "dict": {"Reports": "Reportes", "Cusomer Billing Report": "Informe de facturación del cliente", "Line Loss Report": "Reporte de línea perdida", "Supply Statistics Report": "Informe de estadísticas de suministro", "Sales Statistics Report": "Informe de estadísticas de ventas", "Non-Residential": "No residencial", "Residential": "Residencial", "Production": "Producción", "Farming": "Agricultura", "Animal Husbandry": "Ganadería", "Fishery": "Pesca", "Mining": "Minería", "Lodging & Catering": "Alojamiento y restauración", "Minutely": "Cada minuto", "Hourly": "<PERSON><PERSON> hora", "Daily": "Diariamente", "Monthly": "Mensualmente", "Yes": "Si", "No": "No", "Data Profile": "<PERSON><PERSON><PERSON> de <PERSON>", "Event Profile": "Perfil de evento", "Unknow Device Management": "Gestión de dispositivos desconocidos "}, "dcuConfiguration300": {"title": "Administración de parámetros de puntos de medición", "communicatorSn": "Nº serie concentrador", "measurementPointRange": "Rango de punto de medición", "meterTitleHES": "Tablas de punto de medición", "communication": "Comunicación", "meterSn": "Nºserie del medidor", "pointNum": "Nºserie de puntos de medición", "status": "<PERSON><PERSON><PERSON>", "meterProperties": "Característica del medidor", "mac": "Dirección puntos de medición", "meterType": "Tipos de medidores electrónicos", "totalDivType": "Tipos de total de puntos", "isVip": "Atributos del Usuario VIP", "feeRateCount": "Nº de tarifas", "collectorAddress": "Dirección del colector", "comNum": "Nº de puerto", "taChange": "Cambio TA", "tvChange": "Cambio TV", "command": "Comand<PERSON>", "reason": "Razón", "requestTime": "Tiempo de petición", "responseTime": "Tiempo de respuesta", "protocol": "protocolo", "meterTitleDCU": "Tabla de medidores (Lectura desde DCU)", "seleCommunicator": "¡Selecciona un concentrador！", "seleLeastOne": "¡Selecciona al menos un dato del medidor", "pleaseEnterPositiveInt": "Inserta nº enteros positivos", "beginGreaterThanEnd": "Inicio de punto de medición debe ser mayor que el punto medición final", "baudRate": "velocidad en baudios", "stopFlag": "detener bandera"}, "courtsTopology": {"title": "Topología de tribunales"}, "reportDesigner": {"reportDesigner": "Diseñador de informes", "reportManagement": "Gestión de informes", "reportExploer": "Informes", "reportManagementList": "Lista de gestión de informes", "reportType": "Tipo de informe", "reportName": "Reportar nombre", "orgName": "Nombre de la organización", "templateFile": "Archivo de plantilla"}, "dictManagement": {"title": "Gestión de diccionarios", "dictId": "Dict ID", "dictName": "Dict Name", "dictDesp": "Descripción de dictado", "dictGroupList": "Lista de grupos de dictados", "dictDetailList": "Lista detallada de dictados", "dictDetailId": "ID de detalle de Dict", "dictGroupId": "Dict ID de grupo", "dictDetailName": "Dict Detail Name", "dictDetailDesp": "Descripción detallada de Dict", "isDefault": "<PERSON>s predeterminado", "displayOrder": "Orden de visualización", "dictIdOneTips": "¡Dict ID ya existe!", "dictDetailIdOneTips": "¡La ID detallada de Dict ya existe!", "dictGroupFirst": "¡Por favor, seleccione primero el grupo Dict!"}, "dictDataitem": {"name": "Nombre del Canal", "title": "Gestión de canales", "list": "Lista de canales", "id": "Canal ID", "protocolCode": "Código OBIS", "protocolName": "Nombre de protocolo", "unit": "Unidad", "dataitemType": "Tipo de canal", "showUnit": "Mostrar unidad", "opType": "Tipo de operación", "idExist": "¡El ID del canal ya existe!", "nameExist": "¡El ID del canal ya existe!", "add": "Agregar canal", "import": "Importar archivo de canal", "parseType": "Tipo de aná<PERSON>", "parseLen": "Parse Length", "scale": "Escala"}, "dictDataitemGroup": {"title": "Mostrar gestión de grupo", "list": "Mostrar lista de grupos", "name": "Nombre del grupo", "id": "Identificación del grupo", "appType": "Tipo de grupo", "protocolName": "Nombre de protocolo", "idExist": "¡El ID de grupo ya existe!", "nameExist": "¡El nombre del grupo ya existe!", "selectGroup": "¡Por favor, seleccione el grupo primero!", "sortId": "Ordenar ID", "deleteItem": "Elimine primero los canales vinculados"}, "dictProfile": {"id": "Perfil Id", "name": "Nombre de perfil", "type": "Tipo de perfil", "title": "Gestión de plantillas de perfil", "list": "Lista de plantillas de perfil", "idExist": "¡El ID de perfil ya existe!", "nameExist": "¡El nombre del perfil ya existe!", "itemList": "Lista de canales vinculados", "linkChannel": "Enlace de canal", "sortChannel": "Ordenar canal", "selectProfile": "¡Por favor, seleccione el perfil primero!"}, "storeTable": {"title": "Administración de la tienda", "list": "Lista de la tabla de la tienda", "columnCount": "Recuento de columnas", "id": "ID de tabla", "name": "Nombre de la tabla", "columnList": "Lista de detalles de columna", "columnId": "ID de columna", "selectTable": "¡Por favor, seleccione primero la tabla de la tienda!", "idExist": "¡La ID de la tabla ya existe!", "nameExist": "¡El nombre de la tabla ya existe!", "delHasData": "La tabla tiene datos que no se pueden eliminar", "setProfile": "El nombre del perfil está vacío ¡No se puede vincular el canal!"}, "sysUtility": {"licenseInfo": "Información de licencia", "newLicense": "Nueva licencia", "companyName": "Nombre de Organización", "licenseStartDate": "Fecha de inicio de la licencia actual", "licenseEndDate": "Fecha de finalización de la licencia actual", "meterNum": "Número de medidor de soporte actual"}, "menu": {"Data Collection": "Recolección de datos", "Tools": "Herramientas", "System": "Sistema", "Provisioning": "Configurar", "Meter Data Report": "Reporte de datos", "Meter Event Report": "Reporte de eventos ", "Schedule Reads Report": "Reporte de lecturas", "Miss Data Tracing": "Seguimiento de pérdidas de datos", "Collection Scheme Management": "Admistrar de plan de recolección", "Meter Group Management": "Administrar grupos de medidores", "Meter Configuration": "Configurar parámetros del medidor", "Firmware Upgrade": "Actualización del firmware", "Connect / Disconnect": "Conexión/Desconexión", "Asset Management": "Administrar activos", "Deployment Management": "Administración del servidor", "Log Explorer": "Historial de navegación", "Permission": "<PERSON><PERSON><PERSON>", "Data Export Management": "Administración de exportarción de datos", "Meter Group Upgrade": "Actualización por grupos de medidores", "Data Management": "Administración de datos", "DCU Configuration - SG376": "Configurar el concentrador", "On Demand Reads": "Lecturas en demanda", "Calculation Object Define": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reports": "Reportes", "Communication Status Report": "Reporte estado de comunicación", "Home": "Página de inicio", "VEE Management": "Gestión de VEE", "Grid Loss Management": "Gestión de pérdidas de red", "DCU Configuration": "Configuración DCU", "Channel Management": "Gestión de canales", "Module Configuration": "Configuración del módulo", "On Demand Reads -376": "Lecturas a pedido -376", "Packet Debug": "Depuración de paquetes", "DCU Group Upgrade": "Actualización de grupo de DCU"}, "dictDataItemGroup": {"Energy": "Energia", "Demand": "<PERSON><PERSON><PERSON>", "Instantaneous": "Valores instantáneos", "Other": "<PERSON><PERSON><PERSON>", "Prepaid": "Prepago", "Load Profile Minutely": "Perfil de carga cada al minuto", "TOU": "TOU", "Standard Event Log": "Registro de evento estándar", "Relay Control Log": "Registro de control de relés", "Power Quality Log": "Registro de calidad de energía", "Communication Log": "Registro de comunicación", "Fraud Event Log": "Registro de eventos de fraude", "Self Define Log": "Registro de autodefinición", "Long power failure log": "Registro largo de fallas de energía"}, "dictDataItem": {"Active energy import (+A) [Unit: kWh]": "Energía Activa Importada (+A) [Unidad: kWh]", "Active energy import (+A) rate 1 [Unit: kWh]": "Energía Activa Importada (+A) rango 1[Unidad: kWh]", "Active energy import (+A) rate 2 [Unit: kWh]": "Energía Activa Importada (+A) rango 2[Unidad: kWh]", "Active energy import (+A) rate 3 [Unit: kWh]": "Energía Activa Importada (+A) rango 3[Unidad: kWh]", "Active energy import (+A) rate 4 [Unit: kWh]": "Energía Activa Importada (+A) rango 4[Unidad: kWh]", "Active energy export (-A) [Unit: kWh]": "Energía activa exportada(-A) [Unidad: kWh]", "Active energy export (-A) rate 1 [Unit: kWh]": "Energía activa exportada(-A) rango 1[Unidad: kWh]", "Active energy export (-A) rate 2 [Unit: kWh]": "Energía activa exportada(-A) rango 2[Unidad: kWh]", "Active energy export (-A) rate 3 [Unit: kWh]": "Energía activa exportada(-A) rango 3[Unidad: kWh]", "Active energy export (-A) rate 4 [Unit: kWh]": "Energía activa exportada(-A) rango 4[Unidad: kWh]", "Reactive energy import (+R) (QI+QII) [Unit: kVarh]": "Energía reactiva importada(+R)(QI+QII)[Unidad: kVarh]", "Reactive energy import (+R) (QI+QII) rate 1 [Unit: kVarh]": "Energía reactiva importada(+R)(QI+QII)rango 1[Unidad: kVarh]", "Reactive energy import (+R) (QI+QII) rate 2 [Unit: kVarh]": "Energía reactiva importada(+R)(QI+QII)rango 2[Unidad: kVarh]", "Reactive energy import (+R) (QI+QII) rate 3 [Unit: kVarh]": "Energía reactiva importada(+R)(QI+QII)rango 3[Unidad: kVarh]", "Reactive energy import (+R) (QI+QII) rate 4 [Unit: kVarh]": "Energía reactiva importada(+R)(QI+QII)rango 4[Unidad: kVarh]", "Reactive energy export (-R) (QIII+QIV) [Unit: kVarh]": "Energía reactiva exportada(-R)(QIII+QIV)[Unidad: kVarh]", "Reactive energy export (-R) (QIII+QIV) rate 1 [Unit: kVarh]": "Energía reactiva exportada(-R)(QIII+QIV)rango 1[Unidad: kVarh]", "Reactive energy export (-R) (QIII+QIV) rate 2 [Unit: kVarh]": "Energía reactiva exportada(-R)(QIII+QIV)rango 2[Unidad: kVarh]", "Reactive energy export (-R) (QIII+QIV) rate 3 [Unit: kVarh]": "Energía reactiva exportada(-R)(QIII+QIV)rango 3[Unidad: kVarh]", "Reactive energy export (-R) (QIII+QIV) rate 4 [Unit: kVarh]": "Energía reactiva exportada(-R)(QIII+QIV)rango 4[Unidad: kVarh]", "Reactive energy QI (+Rl) [Unit: kVarh]": "Energía reactiva QI(+Rl)[Unidad: kVarh]", "Reactive energy QI (+Rl) rate 1 [Unit: kVarh]": "Energía reactiva QI(+Rl)rango 1[Unidad: kVarh]", "Reactive energy QI (+Rl) rate 2 [Unit: kVarh]": "Energía reactiva QI(+Rl)rango 2[Unidad: kVarh]", "Reactive energy QI (+Rl) rate 3 [Unit: kVarh]": "Energía reactiva QI(+Rl)rango 3[Unidad: kVarh]", "Reactive energy QI (+Rl) rate 4 [Unit: kVarh]": "Energía reactiva QI(+Rl)rango 4[Unidad: kVarh]", "Reactive energy QII (+Rc) [Unit: kVarh]": "Energía reactiva QII(+Rc)[Unidad: kVarh]", "Reactive energy QII (+Rc) rate 1 [Unit: kVarh]": "Energía reactiva QII(+Rc)rango 1[Unidad: kVarh]", "Reactive energy QII (+Rc) rate 2 [Unit: kVarh]": "Energía reactiva QII(+Rc)rango 2[Unidad: kVarh]", "Reactive energy QII (+Rc) rate 3 [Unit: kVarh]": "Energía reactiva QII(+Rc)rango 3[Unidad: kVarh]", "Reactive energy QII (+Rc) rate 4 [Unit: kVarh]": "Energía reactiva QII(+Rc)rango 4[Unidad: kVarh]", "Reactive energy QIII (+Rl) [Unit: kVarh]": "Energía reactiva QIII(+Rl)[Unidad: kVarh]", "Reactive energy QIII (-Rl) rate 1 [Unit: kVarh]": "Energía reactiva QIII(-Rl)rango 1[Unidad: kVarh]", "Reactive energy QIII (-Rl) rate 2 [Unit: kVarh]": "Energía reactiva QIII(-Rl)rango 2[Unidad: kVarh]", "Reactive energy QIII (-Rl) rate 3 [Unit: kVarh]": "Energía reactiva QIII(-Rl)rango 3[Unidad: kVarh]", "Reactive energy QIII (-Rl) rate 4 [Unit: kVarh]": "Energía reactiva QIII(-Rl)rango 4[Unidad: kVarh]", "Reactive energy QIII (+Rl) rate 1 [Unit: kVarh]": "Energía reactiva QIII(+Rl)rango 1[Unidad: kVarh]", "Reactive energy QIII (+Rl) rate 2 [Unit: kVarh]": "Energía reactiva QIII(+Rl)rango 2[Unidad: kVarh]", "Reactive energy QIII (+Rl) rate 3 [Unit: kVarh]": "Energía reactiva QIII(+Rl)rango 3[Unidad: kVarh]", "Reactive energy QIII (+Rl) rate 4 [Unit: kVarh]": "Energía reactiva QIII(+Rl)rango 4[Unidad: kVarh]", "Reactive energy QIV (-Rc) [Unit: kVarh]": "Energía reactiva QIV(-Rc)[Unidad: kVarh]", "Reactive energy QIV (-Rc) rate 1 [Unit: kVarh]": "Energía reactiva QIV(-Rc)rango 1[Unidad: kVarh]", "Reactive energy QIV (-Rc) rate 2 [Unit: kVarh]": "Energía reactiva QIV(-Rc)rango 2[Unidad: kVarh]", "Reactive energy QIV (-Rc) rate 3 [Unit: kVarh]": "Energía reactiva QIV(-Rc)rango 3[Unidad: kVarh]", "Reactive energy QIV (-Rc) rate 4 [Unit: kVarh]": "Energía reactiva QIV(-Rc)rango 4[Unidad: kVarh]", "Apparent energy import (+E) [Unit: kVah]": "Energía aparente importada(+E)[Unidad: kVah]", "Apparent energy export (-E) [Unit: kVah]": "Energía aparente exportada(-E)[Unidad: kVah]", "Import active demand [Unit:kW]": "Demanda activa importada [Unidad:kW]", "Export active demand [Unit: kW]": "Demanda activa exportada [Unidad:kW]", "Import reactive demand [Unit:kVar]": "Demanda reactiva importada [Unidad:kVar]", "Export reactive demand [Unit:kVar]": "Demanda reactiva exportada [Unidad:kVar]", "Instantaneous voltage L1 [Unit: V] ": "Voltaje instantáneo L1 [Unidad: V]", "Instantaneous voltage L2 [Unit: V]": "Voltaje instantáneo L2 [Unidad: V]", "Instantaneous voltage L3 [Unit: V]": "Voltaje instantáneo L3 [Unidad: V]", "Instantaneous current L1 [Unit: A]": "Corriente instantáneo L1 [Unidad: A]", "Instantaneous current L2 [Unit: A]": "Corriente instantáneo L2 [Unidad: A]", "Instantaneous current L3 [Unit: A]": "Corriente instantáneo L3 [Unidad: A]", "Neutral current [Unit: A]": "Corriente neutro [Unidad: A]", "Instantaneous active import power (+A) [Unit: kW]": "Energía activa importada instantánea(+A)[Unidad: kW]", "Instantaneous active export power (-A) [Unit: kW]": "Energía activa exportada instantánea(-A)[Unidad: kW]", "Instantaneous reactive import power (+R) [Unit: kVar]": "Energía reactiva importada instantánea(+R)[Unidad: kVar]", "Instantaneous reactive export power (-R) [Unit: kVar]": "Energía reactiva exportada instantánea(-R)[Unidad: kVar]", "Instantaneous apparent export power [Unit: kVa]": "Energía aparente exportada instantánea[Unidad: kVa]", "Instantaneous Power factor (+A/+VA)": "Factor energía instantánea (+A/+VA)", "Instantaneous power factor (PF) L1": "Factor energía instantánea(PF)L1", "Instantaneous power factor (PF) L2": "Factor energía instantánea(PF)L2", "Instantaneous power factor (PF) L3": "Factor energía instantánea(PF)L3", "Frequency [Unit: Hz]": "Frecuencia [Unidad: Hz]", "Meter Serial Number": "Número de serie del medidor", "Manufactory Identifier": "Identificador del fabricante", "Logic Device Name": "Nombre lógico del dispositivo", "Firmware Version": "Versión Firmware", "Clock": "<PERSON><PERSON><PERSON>", "Relay Control State": "Estado del control de relé", "Relay Control Mode": "Estado modo del relé", "Currently active step": "Etapa activada actual", "Currently active price": "Precio activado actual", "Tariff index": "Í<PERSON>ce de tarifas", "Max credit limit": "Límite del crédito máximo", "Key expiry number": "Número de llave expirada", "Max vend limit": "Límite de máxima venta", "Supply group code": "Código de grupo del proveedor", "Key type": "Tipo de llave", "Key Revision number": "Número de revisión llave", "STS software versopm": "Versión STS software", "Current time purchase credit": "Tiempo actual compra de crédito", "Cumulative purchase credit": "Compra de crédito acumulado", "Residual credit": "<PERSON><PERSON><PERSON><PERSON> restante", "Cumulative consume credit": "Consumo de crédito acumulado", "Current month consume credit": "Consumo de crédito del me<PERSON> actual", "Current day consume credit": "Consumo de crédidto del día actual", "Date of passive step active": "Fecha pasiva de etapa activa", "Watchdog Error": "Error Watchdog", "Export active demand [Unit:kW]": "Demanda activa exportada [Unit:kW]", "Over voltage (voltage FSWELL) L1": "Sobrevoltaje L1 (voltaje SWELL)", "Register successful": "Registro correcto", "Power Up": "Encendido", "Under Limit Threshold of miss Voltage [Unit: V]": "Límite de umbral inferior de caída de tensión [Unidad: V]", "Power Down": "Desconectado", "Clock Adjust (new date/time)": "<PERSON><PERSON><PERSON> (nueva fecha/hora)", "Parameter(s) Changed": "Parámetros camb<PERSON>dos", "Module Cover removed": "Tapa del módulo quitada", "Terminal Cover closed": "Tapa del terminal cerrada", "Terminal Cover opened": "Tapa del terminal abierta", "Remote Connection": "Conexión remota", "Remote Disconnection": "Desconexión remota", "Clock Adjust (old date/time)": "<PERSON><PERSON><PERSON> reloj (anterior fecha/hora)", "Module Cover closed": "Tapa del módulo cerrada", "Replace Battery": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Association authentication failure after n times": "Error de autenticación de asociación después en n veces", "Meter demand clear": "Demanda de medidor clara", "onlineCommunicatorS": "Comunicadores conectados", "onlineConcentrators": "Concentradores conectados", "Meter": "Metro", "Communicator": "Comunicador", "Standard Event Log": "Registro de eventos estándar", "Error Register Cleared": "Registro de errores borrado", "Firmware ready for activation": "Firmware listo para activación", "Firmware activated": "Firmware activado", "Firmware verification failed": "Falló la verificación del firmware", "Security key(s) changed": "Se cambiaron las llaves de seguridad", "Alarm Register Cleared": "Registro de alarma borrado", "NV Memory Error": "Error de memoria NV", "Meter Program Memory Error": "Error de memoria del programa del medidor", "RAM Error": "<PERSON><PERSON>r de RAM", "Battery voltage low": "Voltaje de batería bajo", "Passive TOU programmed": "TOU pasivo programado", "TOU activated": "TOU activado", "Measurement System Error": "Error del sistema de medición", "Missing neutral": "Falta neutral", "Neutral Distrube": "Distribuidor neutral", "Neutral Distrube End": "Extremo neutro del distribuidor", "Clock Invalid": "Re<PERSON>j no válido", "Daylight Saving Time Enabled/Disable": "Horario de verano activado / desactivado", "Load profile cleared": "Perfil de carga despejado", "Event log cleared": "Registro de eventos borrado", "Meter Clear": "Con<PERSON><PERSON> claro", "Phase sequence reversal": "Inversión de secuencia de fase", "External alert detected": "Alerta externa detectada", "Unexpected consumption": "Consumo inesperado", "Relay Control Log": "Registro de control de relés", "Disconnector in": "Seccionador en", "Ready for Reconnection": "Listo para reconexión", "Manual Connection": "Registro de control de relés", "Local Reconnection": "Reconexión local", "Disconnect /Reconnect failure": "Desconectar / reconectar error", "Manual Disconnection": "Desconexión manual", "Local Disconnection": "Desconexión local", "Limiter threshold changed": "El umbral del limitador cambió", "Limiter threshold exceed": "El umbral del limitador excede", "Limiter threshold OK": "Limitador umbral OK", "Fuse supervision L1, threshold exceeded": "Supervisión de fusible L1, umbral excedido", "Fuse supervision L1, threshold OK": "Supervisión de fusibles L1, umbral OK", "Fuse supervision L2, threshold exceeded": "Supervisión de fusible L2, umbral excedido", "Fuse supervision L2, threshold OK": "Supervisión de fusibles L2, umbral OK", "Fuse supervision L3, threshold exceeded": "Supervisión de fusible L3, umbral excedido", "Fuse supervision L3, threshold OK": "Supervisión de fusibles L3, umbral OK ", "Power Quality Log": "Registro de calidad de energía", "Phase Asymmetry": "Asimetría de fase", "Phase Failure L1 started": "Se inició la falla de fase L1", "Phase Failure L1 stopped": "Se finalizó error de fase L1", "Under voltage (voltage SAG) L1": "Bajo voltaje (voltaje SAG) L1", "Over voltage (voltage SWELL) L1": "Sobretensión (tensión SWELL) L1", "Missing Voltage (Voltage Cut) L1": "Falta voltaje (corte de voltaje) L1", "Normal Voltage L1": "Voltaje normal L1", "Bad Voltage Quality L1": "Mala calidad de voltaje L1", "Phase Failure L2 started": "Se inició el error de fase L2", "Phase Failure L2 stopped": "Se finalizó error de fase L2", "Under voltage (voltage SAG) L2": "Bajo voltaje (voltaje SAG) L2", "Overvoltage (voltage SWELL) L2": "Sobretensión (tensión SWELL) L2", "Missing Voltage (Voltage Cut) L2": "Falta voltaje (corte de voltaje) L2", "Normal Voltage L2": "Voltaje normal L2", "Bad Voltage Quality L2": "Mala calidad de voltaje L2", "Phase Failure L3 started": "Se inició la falla de fase L3", "Phase Failure L3 stopped": "Error de fase L3 detenido", "Under voltage (voltage SAG) L3": "Caida de voltaje (voltaje SAG) L3", "Overvoltage (voltage SWELL) L3": "Sobretensión (tensión SWELL) L3", "Missing Voltage (Voltage Cut) L3": "Falta voltaje (corte de voltaje) L3", "Normal Voltage L3": "Voltaje normal L3", "Bad Voltage Quality L3": "Mala calidad de voltaje L3", "L1 Undervoltage End": "Fin de subtensión L1", "L2 Undervoltage End": "Fin de subtensión L2", "L3 Undervoltage End": "Fin de subtensión L3", "L1 Overvoltage End": "Fin de sobretensión L1", "L2 Overvoltage End": "Fin de sobretensión L2", "L3 Overvoltage End": "Fin de sobretensión L3", "L1 Missing Voltage End": "Ausencia de tensión L1 final", "L2 Missing Voltage End": "Ausencia de tensión L2 final", "L3 Missing Voltage End": "Ausencia de tensión L3 final", "Power Overload L1 started": "Se inició la sobrecarga de energía L1", "Power Overload L1 stopped": "Sobrecarga de energía L1 detenida", "Active Power Reverse L1 started": "Energía Activa Inversa L1 iniciado", "Active Power Reverse L1 stopped": "Energía Activa Inversa L1 detenido", "Power Overload L2 started": "Se inició la sobrecarga de energía L2", "Power Overload L2 stopped": "Se detuvo la sobrecarga de energía L2", "Low Power Factor started": "Se inició el factor de potencia bajo", "Low Power Factor stopped": "Se detuvo el factor de potencia bajo", "Active Power Reverse L2 started": "Energía Activa Inversa L2 iniciado", "Active Power Reverse L2 stopped": "Energía Activa Inversa L2 detenido", "Power Overload L3 started": "Sobrecarga de energía L3 inició", "Power Overload L3 stopped": "Sobrecarga de energía L3 detenido", "Active Power Reverse L3 started": "Energía Activa Inversa L3 iniciado", "Active Power Reverse L3 stopped": "Energía Activa Inversa L3 detenido", "Voltage unbalance started": "Se inició el desequilibrio de voltaje", "Voltage unbalance stopped": "Desequilibrio de voltaje detenido", "Over current L1 started": "Sobreintensidad L1 comenzó", "Over current L1 stopped": "Sobreintensidad L1 detenido", "Loss current L1 started": "Pérdida de intensidad L1 iniciada", "Loss current L1 stopped": "Pérdida de intensidad L1 detenida", "Over current L2 started": "Sobreintensidad L2 comenzó", "Over current L2 stopped": "Sobreintensidad L2 detenido", "Loss current L2 started": "Pérdida de intensidad L2 iniciada", "Loss current L2 stopped": "Pérdida de intensidad L2 detenida", "Over current L3 started": "Sobreintensidad L3 comenzó", "Over current L3 stopped": "Sobreintensidad L3 detenido", "Loss current L3 started": "Pérdida intensidad L3 iniciada", "Loss current L3 stopped": "Pérdida de intensidad L3 detenida", "Communication Log": "Registro de comunicación", "Data initialization": "Inicialización de datos", "Soft Version change": "Cambio de versión software", "A Phase Miss": "Una fase perdida", "B Phase Miss": "Ausencia fase B", "C Phase Miss": "Ausencia fase C", "A Phase Lower": " Caida de fase A", "B Phase Lower": "Caida de fase B", "C Phase Lower": "Caida de fase C", "A Phase High": "Subida de fase A", "B Phase High": "Subida de fase B", "C Phase High": "Subida de Fase C", "CPU Temperature High": "Temperatura de CPU alta", "Terminal Top Cover opened": "Cubierta superior del terminal abierta", "Terminal Top Cover closed": "Cubierta superior del terminal cerrada", "Terminal Power Failure": "Error de energía terminal", "Terminal Power on": "Terminal encendido", "GPRS Module Pullout": "Extracción del módulo GPRS", "PLC Module Pullout": "Extracción del módulo PLC", "Terminal time": "Hora terminal", "Modem SW Reset": "Reinicio del software del módem", "Modem HW Reset": "Reinicio de HW del módem", "GSM Outgoing Connection": "Conexión GSM saliente", "GSM Hang-up": "Colgar GSM", "Diagnostic failure": "<PERSON><PERSON><PERSON> di<PERSON>n<PERSON><PERSON>", "GSM Incoming Connection": "Conexión GSM entrante", "No connection timeout": "Sin tiempo de espera de conexión", "Modem Initialization Failure": "Error de inicialización del módem", "User Initialization Failure": "Error de inicialización del usuario", "SIM Card Ok": "Tarjeta SIM Ok", "SIM Card Failure": "error de la tarjeta SIM", "GSM Registration Failure": "Error de registro GSM", "GPRS Registration Failure": "Error de registro de GPRS", "PDP context established": "Se estableció el contexto de PDP", "PDP context destoryed": "Contexto de PDP destruido", "PDP context failure": "Error de contexto de PDP", "Signal Quality Low": "Calidad de señal baja", "Auto answer number of calls exceeded": "Se superó el número de llamadas de respuesta automática", "Local communication attempt": "Intento de comunicación local", "Event Log Cleared": "Registro de eventos borrado", "Fraud Event Log": "Registro de eventos de fraude", "Active Power Reversal": "Potencia activa inversa", "Decryption or Message Authentication failure": "error de descifrado o autenticación de mensajes", "Relay attack": "Ataque al relé", "Strong DC field removed": "Campo de CC fuerte eliminado", "Strong DC field detected": "Se detectó un campo de CC fuerte", "Current reverse end": "Extremo inverso intensidad", "Module cover open": "Tapa del módulo abierta", "Module cover close": "Cerrar la tapa del módulo", "CT Bypass Start": "Inicio de bypass de CT", "CT Bypass End": "Extremo de derivación de CT", "Self Define Log": "Registro de autodefinición", "High Temperature startted": "Inicio alta temperatura", "High Temperature stopped": "Final alta temperatura", "CT bypass startted": "Bypass de CT iniciado", "CT bypass stopped": "Bypass de CT detenido", "Low frequency startted": "Inicio de baja frecuencia", "Low frequency stopped": "Final de baja frecuencia", "High frequency startted": "Inicio alta frecuencia", "High frequency stopped": "Final alta frecuencia", "Long power failure log": "Registro de pérdidas de energía", "Power down duration": "Duración de apagado"}, "profile": {"Interval Profile": "Perfil intervalo", "Energy Billing Profile Jingxiang (Daily)": "Perfil de facturación de energía Jingxiang (Diario)", "Standard Event Log": "Registro de evento estándar", "Time Synchronization": "Tiempo de sincronización"}, "afterAdd": {"addTask": "Agregar tarea ", "execute": "<PERSON><PERSON><PERSON><PERSON> ", "content": "Contenido"}}