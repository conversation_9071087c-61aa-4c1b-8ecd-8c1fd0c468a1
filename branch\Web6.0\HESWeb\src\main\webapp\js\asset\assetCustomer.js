	
	
	function queryCustomerDataFun(){
		var sn = $("#searchCustomerSn").val();
		var name = $("#searchCustomerName").val();
		var telephoneNum = $("#searchCustomerPhone").val();
		var orgId= $("#customer_txt_org_id").val();
		var data = {sn:sn,name:name,orgId:orgId,telephoneNum:telephoneNum};
		return data;
	}
	
	function editCustomerData(id){
		add('','system.edit',getRootPathWeb() +'/assetCustomerController/assetCustomer.do?id='+id,'90%','50%',false);
		
	}
	
	function editCustomerSuccess(data){
		//更新编辑数据
		var customer=data.attributes.customer;
		if(customer){
			$("#customerList").jqGrid('setCell',customer.id,"sn",customer.sn);
			$("#customerList").jqGrid('setCell',customer.id,"name",customer.name);
			$("#customerList").jqGrid('setCell',customer.id,"customerTypeName",customer.customerTypeName);
			$("#customerList").jqGrid('setCell',customer.id,"orgName",customer.orgName);
			$("#customerList").jqGrid('setCell',customer.id,"meterSn",customer.meterSn);
			$("#customerList").jqGrid('setCell',customer.id,"telephoneNum",customer.telephoneNum);
			$("#customerList").jqGrid('setCell',customer.id,"industryTypeName",customer.industryTypeName);
			$("#customerList").jqGrid('setCell',customer.id,"addr",customer.addr);
		}
		setView(customer);
	}
	
	function addCustomerData(){
		add('','system.add',getRootPathWeb() +'/assetCustomerController/assetCustomer.do','90%','50%',false);
		$("#customerList").trigger('reloadGrid');
	}
	
	function queryCustomerDataOnclick() {
		// customerListsearchOnEnterFn();
		 $("#customerList").setGridParam({
                 datatype: 'json',
                 url: getRootPathWeb()+"/assetCustomerController/datagrid.do",
                 postData:queryCustomerDataFun()
		 }).trigger("reloadGrid");
	}
	
	
	 function viewCustomerRowData(rowId){
	 	var rowData = $("#customerList").jqGrid('getRowData',rowId);
		$("#customerSpan").html(rowData.sn +" "+ i18n.t("lineManagementList.properties"));
		setView(rowData);
	 }
	
	function  setView(rowData){
		$("#customerSn").val(rowData.sn);
		$("#customerName").val(rowData.name);
		$("#customerType").val(rowData.customerTypeName);
		$("#customerOrg").val(rowData.orgName);
		$("#customerMeterSn").val(rowData.meterSn);
		$("#customerTelephone").val(rowData.telephoneNum);
		$("#industryType").val(rowData.industryTypeName);
		$("#customerAddress").html(rowData.addr);
	}
	 
	function deleteCustomer(id){
		var rowData = $("#customerList").jqGrid("getRowData", id);
		var close_link = window.parent.layer.confirm(i18n.t("system.aydeltherdate"), 
			{
				btn: [i18n.t("system.determine"), i18n.t("system.cancel")],
				title: i18n.t("system.delete")
			}, function() {
				window.parent.layer.close(close_link);
				layer.load();
				$.ajax({
					async : true,
		            cache : false,
		            traditional: true,
					type: 'POST',
					url: getRootPathWeb()+'/assetCustomerController/del.do?&id=' + rowData.id,
					dataType: 'json',
					success: function(data) {
						if(data.success) {
							$("#customerList").trigger('reloadGrid');	//刷新界面
						} else {
							window.parent.layer.msg(data.msg, {icon: 2});
						}
						layer.closeAll('loading');
					},
					error: function(msg) {
						window.parent.layer.msg(i18n.t("system.requestError"), {icon: 2});
						layer.closeAll('loading');
					}
				});
			});
	}
	
		 