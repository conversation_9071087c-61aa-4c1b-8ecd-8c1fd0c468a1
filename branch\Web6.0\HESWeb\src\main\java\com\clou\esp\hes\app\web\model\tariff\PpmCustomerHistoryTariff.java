package com.clou.esp.hes.app.web.model.tariff;

import com.clou.esp.hes.app.web.model.common.BaseEntity;



/*********************************************************************************************************
 * @see Copyright© 2019 Shenzhen Clou Electronics CO., LTD.  All rights reserved. 
 * @see 
 * @see File Name:public class PpmCustomerHistoryTariff{ } 
 * @see 
 * @see Description： ppmCustomerHistoryTariff
 * @version *******
 * <AUTHOR>
 * @see Create Time：2019-09-17 10:06:13
 * @see Last modification Time：2019-09-17 10:06:13
 * 
*********************************************************************************************************/
public class PpmCustomerHistoryTariff  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public PpmCustomerHistoryTariff() {
	}

	/**type*/
	private java.lang.Integer type;
	/**groupId*/
	private java.lang.String groupId;
	/**activeTv*/
	private java.util.Date activeTv;

	
	
	
	public java.lang.Integer getType() {
		return type;
	}

	public void setType(java.lang.Integer type) {
		this.type = type;
	}

	/**
	 * groupId
	 * @return the value of PPM_CUSTOMER_HISTORY_TARIFF.GROUP_ID
	 * @mbggenerated 2019-09-17 10:06:13
	 */
	public java.lang.String getGroupId() {
		return groupId;
	}

	/**
	 * groupId
	 * @param groupId the value for PPM_CUSTOMER_HISTORY_TARIFF.GROUP_ID
	 * @mbggenerated 2019-09-17 10:06:13
	 */
    	public void setGroupId(java.lang.String groupId) {
		this.groupId = groupId;
	}
	/**
	 * activeTv
	 * @return the value of PPM_CUSTOMER_HISTORY_TARIFF.ACTIVE_TV
	 * @mbggenerated 2019-09-17 10:06:13
	 */
	public java.util.Date getActiveTv() {
		return activeTv;
	}

	/**
	 * activeTv
	 * @param activeTv the value for PPM_CUSTOMER_HISTORY_TARIFF.ACTIVE_TV
	 * @mbggenerated 2019-09-17 10:06:13
	 */
    	public void setActiveTv(java.util.Date activeTv) {
		this.activeTv = activeTv;
	}

	public PpmCustomerHistoryTariff(java.lang.Integer type 
	,java.lang.String groupId 
	,java.util.Date activeTv ) {
		super();
		this.type = type;
		this.groupId = groupId;
		this.activeTv = activeTv;
	}

}