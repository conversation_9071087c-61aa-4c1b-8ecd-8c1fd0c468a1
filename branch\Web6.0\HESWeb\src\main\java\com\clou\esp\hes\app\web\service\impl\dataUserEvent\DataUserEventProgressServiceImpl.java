/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataUserEventProgress{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-11-16 02:57:56
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.dataUserEvent;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.dataUserEvent.DataUserEventProgressDao;
import com.clou.esp.hes.app.web.model.dataUserEvent.DataUserEventProgress;
import com.clou.esp.hes.app.web.service.dataUserEvent.DataUserEventProgressService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dataUserEventProgressService")
public class DataUserEventProgressServiceImpl  extends CommonServiceImpl<DataUserEventProgress>  implements DataUserEventProgressService {

	@Resource
	private DataUserEventProgressDao dataUserEventProgressDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dataUserEventProgressDao);
    }
	@SuppressWarnings("rawtypes")
	public DataUserEventProgressServiceImpl() {}
	
	
}