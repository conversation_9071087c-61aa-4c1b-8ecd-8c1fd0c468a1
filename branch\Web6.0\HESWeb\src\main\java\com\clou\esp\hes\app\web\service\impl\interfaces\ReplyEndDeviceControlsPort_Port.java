package com.clou.esp.hes.app.web.service.impl.interfaces;

import java.text.SimpleDateFormat;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import ch.iec.tc57._2011.enddevicecontrols.FaultMessage;
import ch.iec.tc57._2011.enddevicecontrols.ReplyEndDeviceControlsPort;
import ch.iec.tc57._2011.enddevicecontrolsmessage.EndDeviceControlsPayloadType;
import ch.iec.tc57._2011.enddevicecontrolsmessage.EndDeviceControlsResponseMessageType;
import ch.iec.tc57._2011.schema.message.ErrorType;
import ch.iec.tc57._2011.schema.message.HeaderType;
import ch.iec.tc57._2011.schema.message.IdentifiedObject;
import ch.iec.tc57._2011.schema.message.ReplyType;
import ch.iec.tc57._2011.schema.message.UserType;

import com.clou.esp.hes.app.web.core.pushlet.PushletData;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.core.util.XMLUtil;
import com.clou.esp.hes.app.web.model.asset.ConnOrDisconn;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.redis.JedisUtils;

public class ReplyEndDeviceControlsPort_Port implements ReplyEndDeviceControlsPort{

	@Override
	public EndDeviceControlsResponseMessageType closedEndDeviceControls(
			EndDeviceControlsResponseMessageType closedEndDeviceControlsEventMessage)
			throws FaultMessage {
		return null;
	}

	@Override
	public EndDeviceControlsResponseMessageType canceledEndDeviceControls(
			EndDeviceControlsResponseMessageType canceledEndDeviceControlsEventMessage)
			throws FaultMessage {
		return null;
	}

	@Override
	public EndDeviceControlsResponseMessageType createdEndDeviceControls(
			EndDeviceControlsResponseMessageType createdEndDeviceControlsEventMessage) throws FaultMessage {
		try{
			AjaxJson json = new AjaxJson();
			HeaderType header = createdEndDeviceControlsEventMessage.getHeader();
			ReplyType reply = createdEndDeviceControlsEventMessage.getReply();
			UserType user = header.getUser();				//用户信息，存放userId和orgId
//			EndDeviceControlsPayloadType payload = createdEndDeviceControlsEventMessage.getPayload();
			if(header==null || header.getUser()==null || reply==null){
				return null;
			}
			String messageId = header.getMessageID();		//messageId也是拉合闸记录的ID
			System.out.println("UCI_interface callback return info is === " + XMLUtil.convertToXml(createdEndDeviceControlsEventMessage));
			
			// 判断是否是token下发
			List<ErrorType> errorTypeList = createdEndDeviceControlsEventMessage.getReply().getError();
			if (CollectionUtils.isEmpty(errorTypeList)) {
				return null;
			}
			
			IdentifiedObject object = errorTypeList.get(0).getObject();
			if (object == null) {
				return null;
			}
			
			String objectType = object.getObjectType();
			
			// 下发token售电为 15.13.112.78
			if ("15.13.112.78".equals(objectType)) {
				if("0.0".equals(reply.getError().get(0).getCode()) || "0.3".equals(reply.getError().get(0).getCode())){
//					json.setSuccess(true);
//				}else{
//					json.setSuccess(false);
					json.setMsg(reply.getError().get(0).getObject().getName().get(0).getNameType().getName().toString());
				}else {
					json.setMsg(reply.getResult());
				}
				String meterSn = reply.getError().get(0).getObject().getMRID();
				json.setObj(meterSn);
				PushletData.pushlet("SendTokens", json, user.getUserID());
			} else {
				/**
				 * 获取接口返回的参数，更改界面task的状态信息
				 */
				json.put("timeStamp", DateUtils.date2Str(DateUtils.xmlDate2Date(header.getTimestamp()),
						new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"))));
				json.put("messageId", messageId);
				json.setMsg(reply.getResult());
				if("0.0".equals(reply.getError().get(0).getCode()) || "0.3".equals(reply.getError().get(0).getCode())){
					json.setSuccess(true);
					json.put("status", "2");
				}else{
					json.setSuccess(false);
					json.put("status", "3");
				}
				//缓存数据到Redis
				ConnOrDisconn entity = (ConnOrDisconn) JedisUtils.getObject(messageId);
				entity.setResponseTime(json.getAttributes().get("timeStamp").toString());	//返回时间
				if("2".equals(json.getAttributes().get("status"))){							//返回状态
					entity.setStatus(MutiLangUtil.doMutiLang("limiterList.success"));
				}else if("3".equals(json.getAttributes().get("status"))){
					entity.setStatus(MutiLangUtil.doMutiLang("limiterList.failed"));
				}
				JedisUtils.setObject(messageId, entity, 0);
				PushletData.pushlet("MeterConnectOrDisconnect", json, user.getUserID());
			}
			
		}catch (Exception e) {
	    	e.printStackTrace();
	    }
		return null;
	}

	@Override
	public EndDeviceControlsResponseMessageType changedEndDeviceControls(
			EndDeviceControlsResponseMessageType changedEndDeviceControlsEventMessage)
			throws FaultMessage {
		return null;
	}

	@Override
	public EndDeviceControlsResponseMessageType deletedEndDeviceControls(
			EndDeviceControlsResponseMessageType deletedEndDeviceControlsEventMessage)
			throws FaultMessage {
		return null;
	}

}
