package clouesp.hes.core.uci.soap.custom.webservice.abstractt;

import java.util.List;

import javax.xml.datatype.XMLGregorianCalendar;

import ch.iec.tc57._2011.schema.message.HeaderType;
import ch.iec.tc57._2011.schema.message.UserType;
import clouesp.hes.core.uci.soap.custom.webservice.common.WebservTask;

import com.clou.esp.hes.app.web.enums.system.TaskState;
import com.power7000g.core.util.redis.JedisUtils;


/**
 * @ClassName: SoapSendAbstract
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年7月18日 上午11:41:19
 *
 */
public abstract class SoapSendAbstract implements SoapInterface{

	protected String verb;
	protected String noun;
	protected XMLGregorianCalendar timestamp;
	protected String source;
	protected boolean asyncReplyFlag;
	protected String replyAddress;
	protected boolean ackRequired;
	protected String userId;
	protected String organization;
	protected String messageId;
	protected String correlationId;
	
	protected String dataItemId;
	protected String sn;
	protected String id;
	
	protected String revision;
	protected List<String> pns;
	
	@Override
	public HeaderType createHeader() {
		// TODO Auto-generated method stub
		HeaderType header = new HeaderType();
		header.setVerb(verb);
		header.setNoun(noun);
		header.setRevision("2.0");
		header.setTimestamp(timestamp);
		header.setSource(source);
		header.setAsyncReplyFlag(asyncReplyFlag);
		header.setAckRequired(ackRequired);
		header.setReplyAddress(replyAddress);
		header.setMessageID(messageId);
		header.setRevision(revision);
		
		UserType userType = new UserType();
		userType.setOrganization(organization);
		userType.setUserID(userId);
		header.setUser(userType);
		header.setCorrelationID(correlationId);
		
		return header;
	}
	
	public void saveToRedis(){
		
	}
	
	@Override
	public void saveToRedis(String id,String dateItemId,TaskState state,Object value){
		// TODO Auto-generated method stub
		WebservTask task = new WebservTask(id, dateItemId,state, value);
		JedisUtils.setObject(messageId, task, 0);
	}

	@Override
	public void recv(Object object) {
		// TODO Auto-generated method stub
	}
	
	@Override
	public void parseHeader(){
		// TODO Auto-generated method stub
	}
	
	@Override
	public void parsePayload(){
		// TODO Auto-generated method stub
	}
	
	@Override
	public void parseReply() {
		// TODO Auto-generated method stub
	}

}
