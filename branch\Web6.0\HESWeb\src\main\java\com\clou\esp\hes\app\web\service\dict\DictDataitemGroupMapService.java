/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictDataitemGroupMap{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
*********************************************************************************************************/

package com.clou.esp.hes.app.web.service.dict;

import com.clou.esp.hes.app.web.model.dict.DictDataitemGroupMap;
import com.clou.esp.hes.app.web.service.common.CommonService;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

public interface DictDataitemGroupMapService extends CommonService<DictDataitemGroupMap>{
	
	/**
	 * 根据ID查询groupName
	 * @return
	 * Wangjiale
	 * 2018年1月21日 下午4:42:46
	 */
	public DictDataitemGroupMap selectParamterTypeNameByItemId(String itemId);
	
	public JqGridResponseTo unBindForJqGrid(JqGridSearchTo jqGridSearchTo);

	public DictDataitemGroupMap getItemGroupMapInfo(DictDataitemGroupMap itemInfo);
}