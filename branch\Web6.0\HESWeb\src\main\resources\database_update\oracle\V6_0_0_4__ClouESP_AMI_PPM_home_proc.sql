DROP PROCEDURE IF EXISTS TOTAL_MONTH_SALES_AMOUNT_ALL;
CREATE OR REPLACE PROCEDURE TOTAL_MONTH_SALES_AMOUNT_ALL AS 
  s_ORG_ID VARCHAR (100);
  CURSOR cur_1 IS SELECT  ID  FROM sys_org  ORDER BY ID asc ;
BEGIN
  /**
   功能说明：通过循环调用 SunBoy_Total_Month_Sales_Amount_One_OrgID 统计所有管理机构的月售电量
   实现方法：每次传入一个 ORG_ID 对一个管理机构进行统计
   创建日期：2019年11月12日
   调用方法：call Total_Month_Sales_Amount_All();
  ***/
  -- truncate table ppm_total_month_sales_amount;
  
  /* 打开游标 */
  open cur_1;
  loop 
    fetch cur_1 into s_ORG_ID;
    exit when cur_1%notfound;
    TOTAL_MONTH_SALES_AMOUNT_ONE(s_ORG_ID);
  end loop;
  commit;
  CLOSE cur_1;
  -- SELECT ' 所有管理机构售电金额 统计完毕！！'; /* 调试输出语句 */
END TOTAL_MONTH_SALES_AMOUNT_ALL;

DROP PROCEDURE IF EXISTS TOTAL_MONTH_SALES_AMOUNT_ONE;
CREATE OR REPLACE PROCEDURE TOTAL_MONTH_SALES_AMOUNT_ONE 
(
  INPUT_ORG_ID IN VARCHAR2 DEFAULT 100 
) AS 
  i INT ;                           /* 循环次数 */
  s_SQL VARCHAR(100);
  done INT DEFAULT 0;
  ID VARCHAR(32);
  s_DATE VARCHAR (30) DEFAULT NULL;            /* 时间 */
  s_Value VARCHAR (30) DEFAULT NULL;           /* 统计金额 */
    
    /* 根据传入的org_ID 进行统计 */
  CURSOR cur_1 IS				    
  SELECT to_date(vhi.SALES_DATE, '%Y-%m') AS StatMonth,
    SUM(CASE WHEN recharge_type = 0 THEN vhi.Customer_Payment_Amount
     WHEN recharge_type = 1 THEN - 1 * vhi.Uninstall_Amount
     ELSE 0
     END) AS SaleAmount
    FROM
      ppm_vend_historical_info vhi
    WHERE
        vhi.SALES_DATE > add_months(to_date(sysdate, '%Y-%m-01'), -12) AND vhi.SALES_DATE < sysdate
        AND vhi.ORG_ID LIKE CONCAT(Input_Org_ID ,'%')
        AND vhi.Recharge_Type IN ('0')
        AND vhi.Receipt_State IN ('0')
    GROUP BY to_date(vhi.SALES_DATE, '%Y-%m');
BEGIN
  NULL;
END TOTAL_MONTH_SALES_AMOUNT_ONE;