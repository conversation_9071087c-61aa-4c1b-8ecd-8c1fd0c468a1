/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysUser{ } 
 * 
 * 摘    要： 用户表
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-10-27 08:02:17
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.system;

import java.util.Arrays;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class SysUser  extends BaseEntity{

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public SysUser() {
	}

	/**utilityId*/
	private java.lang.String utilityId;
	/**orgId*/
	private java.lang.String orgId;
	/**roleId*/
	private java.lang.String roleId;
	/**真实姓名*/
	private java.lang.String name;
	/**用户名*/
	private java.lang.String username;
	/**用户密码*/
	private java.lang.String password;
	/**email*/
	private java.lang.String email;
	/**mobilePhone*/
	private java.lang.String mobilePhone;
	/**profileFile*/
	private java.lang.String profileFile;
	/**1=超级用户；2=系统用户；*/
	private java.lang.Integer userType;
	/**签名文件*/
	private byte[] signature;
	/**用户验证唯一标示*/
	private java.lang.String userkey;
	/**删除状态  0:不删除  1：删除*/
	private java.lang.Integer deleteFlag;
	/**,0：禁用；1=启用；2=锁住（密码输入次数）；3=离职；*/
	private java.lang.Integer userState;
	/**lastLoginTime*/
	private java.util.Date lastLoginTime;
	
	/**orgName*/
	private java.lang.String orgName;
	/**roleName*/
	private java.lang.String roleName;
	
	private java.lang.String showUsername;
	private java.lang.String showPassword;

	/**
	 * utilityId
	 * @return the value of SYS_USER.VENTOR_ID
	 * @mbggenerated 2017-10-27 08:02:17
	 */
	public java.lang.String getUtilityId() {
		return utilityId;
	}

	/**
	 * utilityId
	 * @param utilityId the value for SYS_USER.VENTOR_ID
	 * @mbggenerated 2017-10-27 08:02:17
	 */
    	public void setUtilityId(java.lang.String utilityId) {
		this.utilityId = utilityId;
	}
	/**
	 * orgId
	 * @return the value of SYS_USER.ORG_ID
	 * @mbggenerated 2017-10-27 08:02:17
	 */
	public java.lang.String getOrgId() {
		return orgId;
	}

	/**
	 * orgId
	 * @param orgId the value for SYS_USER.ORG_ID
	 * @mbggenerated 2017-10-27 08:02:17
	 */
    	public void setOrgId(java.lang.String orgId) {
		this.orgId = orgId;
	}
	/**
	 * roleId
	 * @return the value of SYS_USER.ROLE_ID
	 * @mbggenerated 2017-10-27 08:02:17
	 */
	public java.lang.String getRoleId() {
		return roleId;
	}

	/**
	 * roleId
	 * @param roleId the value for SYS_USER.ROLE_ID
	 * @mbggenerated 2017-10-27 08:02:17
	 */
    	public void setRoleId(java.lang.String roleId) {
		this.roleId = roleId;
	}
	/**
	 * 真实姓名
	 * @return the value of SYS_USER.NAME
	 * @mbggenerated 2017-10-27 08:02:17
	 */
	public java.lang.String getName() {
		return name;
	}

	/**
	 * 真实姓名
	 * @param name the value for SYS_USER.NAME
	 * @mbggenerated 2017-10-27 08:02:17
	 */
    	public void setName(java.lang.String name) {
		this.name = name;
	}
	/**
	 * 用户名
	 * @return the value of SYS_USER.USERNAME
	 * @mbggenerated 2017-10-27 08:02:17
	 */
	public java.lang.String getUsername() {
		return username;
	}

	/**
	 * 用户名
	 * @param username the value for SYS_USER.USERNAME
	 * @mbggenerated 2017-10-27 08:02:17
	 */
    	public void setUsername(java.lang.String username) {
		this.username = username;
	}
	/**
	 * 用户密码
	 * @return the value of SYS_USER.PASSWORD
	 * @mbggenerated 2017-10-27 08:02:17
	 */
	public java.lang.String getPassword() {
		return password;
	}

	/**
	 * 用户密码
	 * @param password the value for SYS_USER.PASSWORD
	 * @mbggenerated 2017-10-27 08:02:17
	 */
    	public void setPassword(java.lang.String password) {
		this.password = password;
	}
	/**
	 * email
	 * @return the value of SYS_USER.EMAIL
	 * @mbggenerated 2017-10-27 08:02:17
	 */
	public java.lang.String getEmail() {
		return email;
	}

	/**
	 * email
	 * @param email the value for SYS_USER.EMAIL
	 * @mbggenerated 2017-10-27 08:02:17
	 */
    	public void setEmail(java.lang.String email) {
		this.email = email;
	}
	/**
	 * mobilePhone
	 * @return the value of SYS_USER.MOBILE_PHONE
	 * @mbggenerated 2017-10-27 08:02:17
	 */
	public java.lang.String getMobilePhone() {
		return mobilePhone;
	}

	/**
	 * mobilePhone
	 * @param mobilePhone the value for SYS_USER.MOBILE_PHONE
	 * @mbggenerated 2017-10-27 08:02:17
	 */
    	public void setMobilePhone(java.lang.String mobilePhone) {
		this.mobilePhone = mobilePhone;
	}
	/**
	 * profileFile
	 * @return the value of SYS_USER.PROFILE_FILE
	 * @mbggenerated 2017-10-27 08:02:17
	 */
	public java.lang.String getProfileFile() {
		return profileFile;
	}

	/**
	 * profileFile
	 * @param profileFile the value for SYS_USER.PROFILE_FILE
	 * @mbggenerated 2017-10-27 08:02:17
	 */
    	public void setProfileFile(java.lang.String profileFile) {
		this.profileFile = profileFile;
	}
	/**
	 * 1=超级用户；2=系统用户；
	 * @return the value of SYS_USER.USER_TYPE
	 * @mbggenerated 2017-10-27 08:02:17
	 */
	public java.lang.Integer getUserType() {
		return userType;
	}

	/**
	 * 1=超级用户；2=系统用户；
	 * @param userType the value for SYS_USER.USER_TYPE
	 * @mbggenerated 2017-10-27 08:02:17
	 */
    	public void setUserType(java.lang.Integer userType) {
		this.userType = userType;
	}
	/**
	 * 签名文件
	 * @return the value of SYS_USER.SIGNATURE
	 * @mbggenerated 2017-10-27 08:02:17
	 */
	public byte[] getSignature() {
		return signature;
	}

	/**
	 * 签名文件
	 * @param signature the value for SYS_USER.SIGNATURE
	 * @mbggenerated 2017-10-27 08:02:17
	 */
    	public void setSignature(byte[] signature) {
		this.signature = signature;
	}
	/**
	 * 用户验证唯一标示
	 * @return the value of SYS_USER.USERKEY
	 * @mbggenerated 2017-10-27 08:02:17
	 */
	public java.lang.String getUserkey() {
		return userkey;
	}

	/**
	 * 用户验证唯一标示
	 * @param userkey the value for SYS_USER.USERKEY
	 * @mbggenerated 2017-10-27 08:02:17
	 */
    	public void setUserkey(java.lang.String userkey) {
		this.userkey = userkey;
	}
	/**
	 * 删除状态  0:不删除  1：删除
	 * @return the value of SYS_USER.DELETE_FLAG
	 * @mbggenerated 2017-10-27 08:02:17
	 */
	public java.lang.Integer getDeleteFlag() {
		return deleteFlag;
	}

	/**
	 * 删除状态  0:不删除  1：删除
	 * @param deleteFlag the value for SYS_USER.DELETE_FLAG
	 * @mbggenerated 2017-10-27 08:02:17
	 */
    	public void setDeleteFlag(java.lang.Integer deleteFlag) {
		this.deleteFlag = deleteFlag;
	}
	/**
	 * ,0：禁用；1=启用；2=锁住（密码输入次数）；3=离职；
	 * @return the value of SYS_USER.USER_STATE
	 * @mbggenerated 2017-10-27 08:02:17
	 */
	public java.lang.Integer getUserState() {
		return userState;
	}

	/**
	 * ,0：禁用；1=启用；2=锁住（密码输入次数）；3=离职；
	 * @param userState the value for SYS_USER.USER_STATE
	 * @mbggenerated 2017-10-27 08:02:17
	 */
    	public void setUserState(java.lang.Integer userState) {
		this.userState = userState;
	}
	/**
	 * lastLoginTime
	 * @return the value of SYS_USER.LAST_LOGIN_TIME
	 * @mbggenerated 2017-10-27 08:02:17
	 */
	public java.util.Date getLastLoginTime() {
		return lastLoginTime;
	}

	/**
	 * lastLoginTime
	 * @param lastLoginTime the value for SYS_USER.LAST_LOGIN_TIME
	 * @mbggenerated 2017-10-27 08:02:17
	 */
    	public void setLastLoginTime(java.util.Date lastLoginTime) {
		this.lastLoginTime = lastLoginTime;
	}

	public SysUser(java.lang.String utilityId 
	,java.lang.String orgId 
	,java.lang.String roleId 
	,java.lang.String name 
	,java.lang.String username 
	,java.lang.String password 
	,java.lang.String email 
	,java.lang.String mobilePhone 
	,java.lang.String profileFile 
	,java.lang.Integer userType 
	,byte[] signature 
	,java.lang.String userkey 
	,java.lang.Integer deleteFlag 
	,java.lang.Integer userState 
	,java.util.Date lastLoginTime ) {
		super();
		this.utilityId = utilityId;
		this.orgId = orgId;
		this.roleId = roleId;
		this.name = name;
		this.username = username;
		this.password = password;
		this.email = email;
		this.mobilePhone = mobilePhone;
		this.profileFile = profileFile;
		this.userType = userType;
		this.signature = signature;
		this.userkey = userkey;
		this.deleteFlag = deleteFlag;
		this.userState = userState;
		this.lastLoginTime = lastLoginTime;
	}

	public SysUser(SysUser sysUser) {
		super(sysUser);
	}

	@Override
	public String toString() {
		return "SysUser [utilityId=" + utilityId + ", orgId=" + orgId
				+ ", roleId=" + roleId + ", name=" + name + ", username="
				+ username + ", password=" + password + ", email=" + email
				+ ", mobilePhone=" + mobilePhone + ", profileFile="
				+ profileFile + ", userType=" + userType + ", signature="
				+ Arrays.toString(signature) + ", userkey=" + userkey
				+ ", deleteFlag=" + deleteFlag + ", userState=" + userState
				+ ", lastLoginTime=" + lastLoginTime + "]";
	}
	
	public String toStringLess() {
		return "SysUser [utilityId=" + utilityId + ", orgId=" + orgId
				+ ", roleId=" + roleId + ", name=" + name + ", username="
				+ username + ", email=" + email + ", mobilePhone=" + mobilePhone + "]";
	}

	public java.lang.String getOrgName() {
		return orgName;
	}

	public void setOrgName(java.lang.String orgName) {
		this.orgName = orgName;
	}

	public java.lang.String getRoleName() {
		return roleName;
	}

	public void setRoleName(java.lang.String roleName) {
		this.roleName = roleName;
	}

	public java.lang.String getShowUsername() {
		return showUsername;
	}

	public void setShowUsername(java.lang.String showUsername) {
		this.showUsername = showUsername;
	}

	public java.lang.String getShowPassword() {
		return showPassword;
	}

	public void setShowPassword(java.lang.String showPassword) {
		this.showPassword = showPassword;
	}

}