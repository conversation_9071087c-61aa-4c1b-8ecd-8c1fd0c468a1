/*counterUp*/
function startRun() {
	$('.counter').counterUp({
		delay : 10,
		time : 1000
	});
}
startRun();
$(document).ready(function() {
	var $loader;
	var totalKb = dayIntegrity;
	var kb = 0;
	var i = 0;
	var avg = totalKb / 10;
	$loader = $("#loader").percentageLoader({
		width : 140,
		height : 140,
		progress : 0,
		controllable : false,
	});
	var animateFunc = function() {
		var progress = kb / 100;
		$loader.setProgress(progress);
		$loader.setValue(kb.toString() + 'kb');
		i++;
		if (i <= 10) {
			kb += avg;
			setTimeout(animateFunc, 25);
		}
	}
	setTimeout(animateFunc, 25);
});