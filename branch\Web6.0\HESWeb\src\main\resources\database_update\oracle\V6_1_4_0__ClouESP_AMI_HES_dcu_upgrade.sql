insert into dict_dataitem values('40.0.7.71', 100, 'Meter Event Profile Cycle Read Switch-integral control', '1#*********.30.255#3', null, 'RW', 1, '','','special',null,0);
insert into dict_dataitem values('39.1.1.4', 100, 'APP Firmware Version', '1#1.1.0.2.0.255#2', null, 'R', 1,'','','octet-string', 2,0);
UPDATE DICT_DATAITEM SET NAME = 'MID Firmware Version',PARSE_TYPE = 'octet-string',parse_len = 3 WHERE ID = '39.0.0.4';

DELETE FROM dict_dataitem_group_map WHERE GROUP_ID='1003002' and DATAITEM_ID='0.0.73.6.1.1.8.0.0.0.0.0.0.0.224.3.38.0';
insert into dict_dataitem_group_map values ('1003002','0.0.73.6.1.1.8.0.0.0.0.0.0.0.224.3.38.0','1');
DELETE FROM dict_dataitem_group_map WHERE GROUP_ID='1003002' and DATAITEM_ID='0.0.73.6.19.1.8.0.0.0.0.0.0.0.224.3.38.0';
insert into dict_dataitem_group_map values ('1003002','0.0.73.6.19.1.8.0.0.0.0.0.0.0.224.3.38.0','2');
DELETE FROM dict_dataitem_group_map WHERE GROUP_ID='1003002' and DATAITEM_ID='0.0.73.6.1.1.8.0.0.0.0.0.0.0.224.3.63.0';
insert into dict_dataitem_group_map values ('1003002','0.0.73.6.1.1.8.0.0.0.0.0.0.0.224.3.63.0','3');
DELETE FROM dict_dataitem_group_map WHERE GROUP_ID='1003002' and DATAITEM_ID='0.0.73.6.19.1.8.0.0.0.0.0.0.0.224.3.63.0';
insert into dict_dataitem_group_map values ('1003002','0.0.73.6.19.1.8.0.0.0.0.0.0.0.224.3.63.0','4');
DELETE FROM dict_dataitem_group_map WHERE GROUP_ID='1003004' and DATAITEM_ID='39.0.0.2';
insert into dict_dataitem_group_map values ('1003004','39.0.0.2','2');

DELETE FROM dict_dataitem_group_map WHERE GROUP_ID='1004004' and DATAITEM_ID='0.0.0.0';
Insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004004','0.0.0.0',1);
DELETE FROM dict_dataitem_group_map WHERE GROUP_ID='1004004' and DATAITEM_ID='0.0.0.1';
Insert into DICT_DATAITEM_GROUP_MAP (GROUP_ID,DATAITEM_ID,SORT_ID) values ('1004004','0.0.0.1',2);

ALTER TABLE DATA_FWU_PLAN ADD(VERSION_DATAITEM_ID VARCHAR2(32));
update DATA_FWU_PLAN set VERSION_DATAITEM_ID = '39.0.0.4';
ALTER TABLE DATA_PARAMETER_PLAN ADD(DEVICE_TYPE VARCHAR2(10));
update DATA_PARAMETER_PLAN set DEVICE_TYPE = '1';

ALTER TABLE ASSET_METER ADD(CREATE_TIME DATE DEFAULT sysdate);
ALTER TABLE ASSET_COMMUNICATOR ADD(CREATE_TIME DATE DEFAULT sysdate);

UPDATE DICT_DATAITEM SET PARSE_TYPE = 'long-unsigned' WHERE ID = '40.0.7.19';
UPDATE DICT_DATAITEM SET PARSE_TYPE = 'long-unsigned' WHERE ID = '*********';
UPDATE DICT_DATAITEM SET PARSE_TYPE = 'octet-string' WHERE ID = '*********';
Insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT,DATAITEM_TYPE,IS_SHOW,PARSE_TYPE,PARSE_LEN,SCALE) values ('********','100','Clock','8#*******.0.255#2',null,'RW',1,null,null,'date-time',12,null);
Insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT,DATAITEM_TYPE,IS_SHOW,PARSE_TYPE,PARSE_LEN,SCALE) values ('*********','100','Concentrator Address','1#********.0.255#2',null,'R',1,null,null,'octet-string',null,null);
Insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT,DATAITEM_TYPE,IS_SHOW,PARSE_TYPE,PARSE_LEN,SCALE) values ('*********','100','Management LogicalDevice Name','1#********.0.255#2',null,'R',1,null,null,'visible-string',null,null);
Insert into DICT_DATAITEM (ID,PROTOCOL_ID,NAME,PROTOCOL_CODE,UNIT,OP_TYPE,SHOW_UNIT,DATAITEM_TYPE,IS_SHOW,PARSE_TYPE,PARSE_LEN,SCALE) values ('*********','100','DCU controls the overall progress of the upgrade','1#*********.21.255#9',null,'RW',1,null,null,'special',null,null);

Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('6f088b2c33c545b78aac411aa5fd2161','29018328bd4011e79bb968f728c516f9',2,'DCU Group Upgrade',5,'dataParameterPlanController/dcuGroupUpgradeList.do','265ddc0cbeda11e79bb968f728c516f9','1','1031',null);

Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1031','DCU Group Upgrade','dataParameterPlanController/dcuGroupUpgradeList.do','Tools');

insert into dict_dataitem_group_map values ('1003004','39.1.1.4','4');

update dict_dataitem set name='Duration of long power failures in any phase',protocol_code='1#0.0.96.7.19.255#2',parse_type='double-long-unsigned' where id='41.96.11.9';
update dict_profile set name='Long power failure log' where id=10057;
update dict_dataitem_group set name='Long power failure log' where id=1002007;