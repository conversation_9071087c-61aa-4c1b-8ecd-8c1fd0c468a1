/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class MdmDictDetail{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2020-04-21 07:32:26
 * 最后修改时间：
 * 
*********************************************************************************************************/

package com.clou.esp.hes.app.web.service.vee;

import com.clou.esp.hes.app.web.model.vee.MdmDictDetail;
import com.clou.esp.hes.app.web.service.common.CommonService;

import java.util.List;
import java.util.Map;

public interface MdmDictDetailService extends CommonService<MdmDictDetail> {
	public String getDictReplace(String dictId);
	
	public Map<String,String> getDictReplaces(List<String> dictIds);
	
	public Map<String,Map<String,String>> getDictMap(List<String> dictId);
	
	
	
}