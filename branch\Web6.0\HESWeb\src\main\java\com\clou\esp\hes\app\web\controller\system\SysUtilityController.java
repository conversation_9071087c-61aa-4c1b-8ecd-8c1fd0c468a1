/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysUtility{ } 
 * 
 * 摘    要： 租户表
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-10-27 08:42:10
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.system;

import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.Globals;
import com.clou.esp.hes.app.web.core.util.MacAddressUtil;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.service.system.SysUtilityService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.encrypt.PasswordUtil;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.model.system.SysUtility;


/**
 * <AUTHOR>
 * @时间：2017-10-27 08:42:10
 * @描述：租户表类
 */
@Controller
@RequestMapping("/sysUtilityController")
public class SysUtilityController extends BaseController{

 	@Resource
    private SysUtilityService sysUtilityService;

	/**
	 * 跳转到租户表列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/system/sysUtilityList");
    }

	/**
	 * 跳转到租户表新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "sysUtility")
	public ModelAndView sysUtility(SysUtility sysUtility,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(sysUtility.getId())){
			try {
                sysUtility=sysUtilityService.getEntity(sysUtility.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("sysUtility", sysUtility);
		}
		return new ModelAndView("/system/sysUtility");
	}


	/**
	 * 租户表查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=sysUtilityService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除租户表信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(SysUtility sysUtility,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(sysUtilityService.deleteById(sysUtility.getId())>0){
                j.setMsg("删除成功");
            }else{
                j.setSuccess(false);
                j.setMsg("删除失败");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
    
    /**
     * 保存租户表信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })SysUtility sysUtility,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        if(Globals.isDevelop){
        	j.setMsg(MutiLangUtil.doMutiLang("system.updateSucc"));
        	return j;
        }
        SysUtility t=new  SysUtility();
        try {
        if(StringUtil.isNotEmpty(sysUtility.getId())){
        	t=sysUtilityService.getEntity(sysUtility.getId());
			MyBeanUtils.copyBeanNotNull2Bean(sysUtility, t);
			
			String [] licenseArryay = t.getReadme().split("-");
			if(licenseArryay.length != 4){
				  j.setSuccess(false);
		          j.setMsg(MutiLangUtil.doMutiLang("system.checkLicense"));
		          return j;
			}
			
			String mac = PasswordUtil.decrypt(licenseArryay[0], "root",// 解密密钥，root，写死
					PasswordUtil.getStaticSalt());
			String startDateStr = PasswordUtil.decrypt(licenseArryay[1], "root",// 解密密钥，root，写死
					PasswordUtil.getStaticSalt());
			String endDateStr = PasswordUtil.decrypt(licenseArryay[2], "root",// 解密密钥，root，写死
					PasswordUtil.getStaticSalt());
			String meterNum = PasswordUtil.decrypt(licenseArryay[3], "root",// 解密密钥，root，写死
					PasswordUtil.getStaticSalt());
			
			if(!mac.equals(MacAddressUtil.getMacAddress())){
			
				 j.setSuccess(false);
		         j.setMsg(MutiLangUtil.doMutiLang("login.incorrectPcCode"));
		         return j;
			}
			
			if(!isInteger(meterNum) || Integer.valueOf(meterNum) == 0){
				 j.setSuccess(false);
		         j.setMsg(MutiLangUtil.doMutiLang("login.meterNumUnavailable"));
		         return j;
	    	}
			
			Date startDate =  DateUtils.parseDate(startDateStr, "yyyy-MM-dd HH:mm:ss");
			Date endDate = DateUtils.parseDate(endDateStr, "yyyy-MM-dd HH:mm:ss");
			t.setMaxNum(meterNum);
			t.setStartDate(startDate);
			t.setEndDate(endDate);
			
			sysUtilityService.update(t);
			
			Globals.pcCodeMap.clear();
			Globals.pcCodeMap.put("pc-code", t.getReadme());
			j.setMsg(MutiLangUtil.doMutiLang("system.updateSucc"));
				
			}else{
	            sysUtilityService.save(sysUtility);
	            j.setMsg("创建成功");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.checkLicense"));
        }
        return j;
    }
	
    
    public static boolean isInteger(String input){  
    	Matcher mer = Pattern.compile("^[0-9]+$").matcher(input);  
        return mer.find();  
    }  
	
}