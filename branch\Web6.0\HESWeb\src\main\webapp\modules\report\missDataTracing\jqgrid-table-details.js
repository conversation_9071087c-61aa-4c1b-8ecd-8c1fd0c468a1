
	
$(document).ready(function() {
	$.jgrid.defaults.styleUI = "Bootstrap";
	var mydata = [{
		id: "1",
		invdate: "2010-05-24",
		name: "test",
		note: "note",
		amount: "300.00",
		tax: "10.00",
		total: "2111.00"
	}, {
		id: "2",
		invdate: "2010-05-25",
		name: "test2",
		note: "note2",
		amount: "300.00",
		tax: "20.00",
		total: "320.00"
	}, {
		id: "3",
		invdate: "2007-09-01",
		name: "test3",
		note: "note3",
		amount: "300.00",
		tax: "30.00",
		total: "430.00"
	}, {
		id: "4",
		invdate: "2007-10-04",
		name: "test",
		note: "note",
		amount: "300.00",
		tax: "10.00",
		total: "210.00"
	}, {
		id: "5",
		invdate: "2007-10-05",
		name: "test2",
		note: "note2",
		amount: "300.00",
		tax: "20.00",
		total: "320.00"
	}, {
		id: "6",
		invdate: "2007-09-06",
		name: "test3",
		note: "note3",
		amount: "300.00",
		tax: "30.00",
		total: "430.00"
	}, {
		id: "7",
		invdate: "2007-10-04",
		name: "test",
		note: "note",
		amount: "300.00",
		tax: "10.00",
		total: "210.00"
	}, {
		id: "8",
		invdate: "2007-10-03",
		name: "test2",
		note: "note2",
		amount: "300.00",
		tax: "21.00",
		total: "320.00"
	}, {
		id: "9",
		invdate: "2007-09-01",
		name: "test3",
		note: "note3",
		amount: "400.00",
		tax: "30.00",
		total: "430.00"
	}, {
		id: "10",
		invdate: "2007-09-02",
		name: "test3",
		note: "note2",
		amount: "500.00",
		tax: "30.00",
		total: "430.00"
	}];
	$("#table_list_2").jqGrid({
		data: mydata,
		datatype: "local",
		autowidth: true,
		gridview: true,
		height: "auto",
		rowNum: 10,

		/*direction:"rtl",*/
		rownumbers: true,
		altRows: true, //单双行样式不同  
		altclass: 'differ',
		rowList: [10, 20, 30],
		colNames: [ "Serial Number", "Serial No.", "Date", "Manufacturer", "Model", "Communication", "Ratio (%)", "Reference Analysis"],
		colModel: [ {
			name: "id",
			index: "id",
			editable: true,
			width: 60,
			frozen: true,
			search: false,
			hidden: true,
			sorttype: "int"

		}, {
			name: "invdate",
			index: "invdate",
			editable: true,
			width: 180,
			sorttype: "date",
			stype: "date",
			formatter: "date"
		}, {
			name: "name",
			index: "name",
			editable: true,
			width: 180
		}, {
			name: "amount",
			index: "amount",
			editable: true,
			width: 180,
			/*align: "right",*/

			sorttype: "float",
			formatter: "number"
		}, {
			name: "tax",
			index: "tax",
			editable: true,
			width: 180,
			/*align: "right",*/
			sorttype: "float"
		}, {
			name: "total",
			index: "total",
			editable: true,
			width: 170,
			/*align: "right",*/
			sorttype: "float",
			stype: "select"
		}, {
			name: "note",
			index: "note",
			editable: true,
			width: 180,
			search: false,
			sortable: false
		}, {
			name: "act",
			index: "act",
			editable: false,
			search: false,
			width: 140,
			sortable: false
		}],
		pager: "#pager_list_2",
		viewrecords: true,
		caption: ' ',
		multiselect: false,
		/*editurl: "/RowEditing",*/
		shrinkToFit: false,
		autoScroll: true
	});

	$("#table_list").jqGrid({
		data: mydata,
		datatype: "local",
		autowidth: true,
		gridview: true,
		height: "auto",
		rowNum: 10,

		/*direction:"rtl",*/
		rownumbers: true,
		altRows: true, //单双行样式不同  
		altclass: 'differ',
		rowList: [10, 20, 30],
		colNames: [ "Serial Number", "Log Time", "Log Type", "Content"],
		colModel: [ {
			name: "id",
			index: "id",
			editable: true,
			width: 160,
			frozen: true,
			search: false,
			hidden: true,
			sorttype: "int"

		}, {
			name: "invdate",
			index: "invdate",
			editable: true,
			width: 280,
			sorttype: "date",
			stype: "date",
			formatter: "date"
		}, {
			name: "name",
			index: "name",
			editable: true,
			width: 280
		}, {
			name: "amount",
			index: "amount",
			editable: true,
			width: 280,
			/*align: "right",*/

			sorttype: "float",
			formatter: "number"
		}],
		pager: "#pager_list",
		viewrecords: true,
		caption: ' ',
		multiselect: false,
		/*editurl: "/RowEditing",*/
		shrinkToFit: false,
		autoScroll: true
	});


	
	$("#table_list_2").jqGrid("navGrid", "#pager_list_2", {
			edit: false,
			add: false,
			del: false,
			search: false
		},

		{
			height: 200,
			reloadAfterSubmit: true
		});
$("#table_list").jqGrid("navGrid", "#pager_list", {
			edit: false,
			add: false,
			del: false,
			search: false
		},

		{
			height: 200,
			reloadAfterSubmit: true
		});
	$(window).bind("resize", function() {
		var width = $(".jqGrid_wrapper").width();
		
		$("#table_list_2").setGridWidth(width)
	})
	$(window).bind("resize", function() {
		var width = $(".jqGrid_wrapper").width();
		
		$("#table_list").setGridWidth(width)
	})
});