delete from DICT_SERVICE_ATTRIBUTE;
/*-============================================-*/
/*----data init  --*/
/*============================================*/
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (4,'UCI.DittPort','Ditt proxy listen port','Integer','9999',null,null,null,5);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (3,'Schedule.TimeSynchronizationRange','Time synchronization range (Unit: Second)','Integer','180','180','600',null,45);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Export.StartTime','Meter data/event export start time','Date','03:32:00',null,null,'HH:mm:ss',3);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Export.Cycle','Meter data/event export cycle (Unit: Hour)','Integer','4','3','12',null,4);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Data.StorageCycle','Meter data/event storage cycle (Unit: Month)','Integer','12','3','65535',null,6);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (2,'MQBus.IP','Message bus server IP address','String','127.0.0.1','1','1',null,1);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (2,'MQBus.Port','Message bus server listen port','Integer','61616','0','65535',null,2);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (1,'Channel.AssetReloadCycle','Service reload asset from database (Unit: Hour)','Integer','12','6','65535',null,3);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (1,'Channel.DLMS.ListenPort','DLMS meter/DCU listen port','Integer','9800','0','65535',null,1);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (1,'Channel.Hall.ListenPort','Hall service listen port','Integer','0','0','65535',null,2);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (3,'Schedule.AssetReloadCycle','Service reload asset from database (Unit: Hour)','Integer','12','6','65535',null,4);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (3,'Schedule.RequestRetryTimes','The times of resending request after timeout','Integer','0','1','2',null,3);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (3,'Schedule.RequestTimeout','Request timeout (Unit: Second)','Integer','60','30','120',null,2);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (3,'Schedule.Concurrency','The count of concurrency task','Integer','5000','1000','50000',null,1);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (4,'UCI.AssetReloadCycle','Service reload asset from database (Unit: Hour)','Integer','12','6','65535',null,4);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (4,'UCI.RequestRetryTimes','The times of resending request after timeout','Integer','0','1','3',null,3);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (4,'UCI.RequestTimeout','Request timeout (Unit: Second)','Integer','60','30','120',null,2);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (4,'UCI.Concurrency','The count of concurrency task','Integer','1000','1000','50000',null,1);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Task.Export','If execute export task','Enum','Enable',null,null,'Enable;Disable',1);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Task.Integrity','If execute integrity calculation task','Enum','Enable',null,null,'Enable;Disable',5);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Export.FtpUrl','Meter data/event export FTP server URL','String','ftp://127.0.0.1/ExportData',null,null,null,2);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (6,'Application.UCI.Interface.Url','UCI interface service URL','String','http://127.0.0.1:8080/UCI-1.0-SNAPSHOT',null,null,null,1);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (1,'Channel.SGPort','SG376 protocol server listen port','Integer','9999',null,null,null,4);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Integrity.StartTime','Integrity statistic start time','Date','00:05:00',null,null,'HH:mm:ss',7);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Integrity.Cycle','Integrity statistic cycle (Unit: Hour)','Integer','2',null,null,null,8);

delete from DICT_MENU;

Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('1e858f11beda11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',1,'Data Collection',1,null,'0',null,null,null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('265ddc0cbeda11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',1,'Tools',5,null,'0',null,null,null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('2dd2e5a5beda11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',1,'System',6,null,'0',null,null,null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('2e858f11beda11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',1,'Provisioning',3,null,'0',null,null,null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40422339bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Meter Data Report',3,'meterDataReportController/list.do','1e858f11beda11e79bb968f728c516f9','1','1001',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('404227d7bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Meter Event Report',4,'dataMeterEventController/list.do','1e858f11beda11e79bb968f728c516f9','1','1002',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40422a45bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Schedule Reads Report',1,'dataIntegrityController/scheduleReadsReport.do','1e858f11beda11e79bb968f728c516f9','1','1003',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40422c62bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Miss Data Tracing',2,'dataIntegrityController/list.do','1e858f11beda11e79bb968f728c516f9','1','1004',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40422ec4bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Collection Scheme Management',5,'assetScheduleSchemeDetailController/list.do','1e858f11beda11e79bb968f728c516f9','1','1005',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('4057793ebedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Meter Group Management',2,'assetMeterGroupController/meterGroupMgmt.do','2e858f11beda11e79bb968f728c516f9','1','1006',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40577c5abedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Meter Configuration',3,'meterConfigurationController/toMeterConfiguration.do','2e858f11beda11e79bb968f728c516f9','1','1007',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40577ff3bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Firmware Upgrade',2,'dataFwuPlanController/list.do','265ddc0cbeda11e79bb968f728c516f9','1','1009',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40578309bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'On Demand Reads',1,'assetMeterController/onDemandReads.do','265ddc0cbeda11e79bb968f728c516f9','1','1011',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('4057848ebedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Connect / Disconnect',4,'meterConnectOrDisconnectController/toConnectOrDisconnect.do','265ddc0cbeda11e79bb968f728c516f9','1','1012',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('4057860cbedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Asset Management',1,'assetMeterController/assetManagementList.do','2e858f11beda11e79bb968f728c516f9','1','1013',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40578791bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Deployment Management',3,'sysServerController/list.do','2dd2e5a5beda11e79bb968f728c516f9','1','1014',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('4057890cbedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Log Explorer',2,'sysLogController/list.do','2dd2e5a5beda11e79bb968f728c516f9','1','1015',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40578c0dbedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Permission',1,'sysUserController/sysUserAndRoleList.do','2dd2e5a5beda11e79bb968f728c516f9','1','1017',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40578a88bedc11e79bb968f728c51688','29018328bd4011e79bb968f728c516f9',2,'Data Export Management',4,'sysDataitemExportController/list.do','2dd2e5a5beda11e79bb968f728c516f9','1','1018',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('6f088b1d22b545b78aac411aa5fd2161','29018328bd4011e79bb968f728c516f9',2,'Meter Group Upgrade',3,'dataParameterPlanController/meterGroupUpgradeList.do','265ddc0cbeda11e79bb968f728c516f9','1','1019',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('2dd2e5a5beda11e79bb968f728c53333','29018328bd4011e79bb968f728c516f9',1,'Data Management',2,null,'0',null,null,null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('2dd2e5a5beda11e79bb968f723333333','29018328bd4011e79bb968f728c516f9',2,'Calculation Object Define',1,'assetCalationObjectController/assetCalculationObject.do','2dd2e5a5beda11e79bb968f728c53333','1','1019',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('2dd2e5a5beda11e79bb9644723333333','29018328bd4011e79bb968f728c516f9',2,'Reports',22,'dictReportController/list.do','2dd2e5a5beda11e79bb968f728c53333','1','1022',null);

delete from DICT_REPORT;
Insert into DICT_REPORT (ID,REPORTNAME,SORT_ID,FUNCTIONURL,PARENT_ID) values ('100001','Billing Reports',1,'dictReportController/billingReportList.do','100');
Insert into DICT_REPORT (ID,REPORTNAME,SORT_ID,FUNCTIONURL,PARENT_ID) values ('100002','Line Loss Analysis',2,'dictReportController/lineLossSingleObjectList.do','100');
Insert into DICT_REPORT (ID,REPORTNAME,SORT_ID,FUNCTIONURL,PARENT_ID) values ('100','Reports',1,null,'0');
Insert into DICT_REPORT (ID,REPORTNAME,SORT_ID,FUNCTIONURL,PARENT_ID) values ('100003','Import '||'&'||' Export Reports',3,'dictReportController/importAndExportReport.do','100');
