/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictMeterDataStorageTable{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
*********************************************************************************************************/

package com.clou.esp.hes.app.web.service.dict;

import java.util.List;
import java.util.Map;

import com.clou.esp.hes.app.web.model.dict.DictMeterDataStorageTable;
import com.clou.esp.hes.app.web.model.report.MeterDataReport;
import com.clou.esp.hes.app.web.model.report.MissDataReport;
import com.clou.esp.hes.app.web.model.report.MissDataReport1;
import com.clou.esp.hes.app.web.service.common.CommonService;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

public interface DictMeterDataStorageTableService extends CommonService<DictMeterDataStorageTable>{
	/**
	 * 查询动态数据报表
	 * @param jqGridSearchTo
	 * @return
	 */
	public JqGridResponseTo getForJqGridMeter(JqGridSearchTo jqGridSearchTo,String searchTimeType);
	public List<MeterDataReport> getForJqGridMeterExcel(JqGridSearchTo jqGridSearchTo);
	public JqGridResponseTo getForJqGridMeterOrg(JqGridSearchTo jqGridSearchTo,String searchTimeType);
	public JqGridResponseTo getForJqGridMeterOnlyShenyu(JqGridSearchTo jqGridSearchTo);
	public  List<MeterDataReport> getForJqGridMeterOnlyShenyuExcel(JqGridSearchTo jqGridSearchTo);
	public JqGridResponseTo getForJqGridMeterMonthShenyu(JqGridSearchTo jqGridSearchTo);
	public List<MeterDataReport> getForJqGridMeterMonthShenyuExcel(JqGridSearchTo jqGridSearchTo);
	/**
	 * 根据对象查询动态数据项返回数据列表
	 * @param meterDataReport
	 * @return
	 */
	public List<MeterDataReport> getMeterDataList(MeterDataReport meterDataReport);
	public List<MeterDataReport> getMeterDataListOrg(MeterDataReport meterDataReport);
	public List<MeterDataReport> getMeterDataListByCommnuicator(MeterDataReport meterDataReport);
	
	/**
	 * 查询动态数据报表 添加通讯器查询
	 * @param jqGridSearchTo
	 * @return
	 */
	public JqGridResponseTo getForJqGridCommnuicator(JqGridSearchTo jqGridSearchTo, Map<String,String> meterMap);
	public List<MeterDataReport> getForJqGridCommnuicatorExcel(JqGridSearchTo jqGridSearchTo, Map<String,String> meterMap);

	
	public List<MissDataReport> getMissDataList(MissDataReport missDataReport);
	
	public List<MissDataReport1> getMissDataList1(MissDataReport missDataReport);

	public List<Map<String, Object>> getMeterDateList(Map<String, Object> params);
	public List<MeterDataReport> getMeterDateListExcel(Map<String, Object> params);
	public List<Map<String, Object>> getEnergyDataDailyByMaps(Map<String, Object> params);
	
	public  Long   getCountTableName(String tableName);
	
	public  void createTable(String tableName);
	
	public  void deleteTable(String tableName);
	
	public  void addTableColumn(String tableName,String columns);
	
	public  void deleteTableColumn(String tableName,String columns);
	
}