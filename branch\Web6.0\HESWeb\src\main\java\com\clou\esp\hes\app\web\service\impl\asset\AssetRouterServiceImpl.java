/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetRouter{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-04-08 03:09:42
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.asset;


import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.asset.AssetRouterDao;
import com.clou.esp.hes.app.web.model.asset.AssetRouter;
import com.clou.esp.hes.app.web.service.asset.AssetRouterService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("assetRouterService")
public class AssetRouterServiceImpl  extends CommonServiceImpl<AssetRouter>  implements AssetRouterService {

	@Resource
	private AssetRouterDao assetRouterDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(assetRouterDao);
    }
	@SuppressWarnings("rawtypes")
	public AssetRouterServiceImpl() {}
	
	@Override
	public int batchInsert(List<AssetRouter> mList) {
		return this.assetRouterDao.batchInsert(mList);
	}
}