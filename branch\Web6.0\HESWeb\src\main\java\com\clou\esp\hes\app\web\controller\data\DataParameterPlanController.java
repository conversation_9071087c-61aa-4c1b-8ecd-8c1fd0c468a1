/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataParameterPlan{ } 
 * 
 * 摘    要： dataParameterPlan
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-04-27 09:13:12
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.data;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.CreatePdf;
import com.clou.esp.hes.app.web.core.util.DateTimeFormatterUtil;
import com.clou.esp.hes.app.web.core.util.ExcelUtils;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.core.util.UciInterfaceUtil;
import com.clou.esp.hes.app.web.core.util.XMLUtil;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroup;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroupMap;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroupValue;
import com.clou.esp.hes.app.web.model.data.DataParameterJob;
import com.clou.esp.hes.app.web.model.data.DataParameterPlan;
import com.clou.esp.hes.app.web.model.dict.DictDeviceType;
import com.clou.esp.hes.app.web.model.dict.DictProfile;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.asset.AssetMeterGroupMapService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterGroupService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterGroupValueService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.data.DataParameterJobService;
import com.clou.esp.hes.app.web.service.data.DataParameterPlanService;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.dict.DictDeviceTypeService;
import com.clou.esp.hes.app.web.service.dict.DictProfileService;
import com.clou.esp.hes.app.web.service.system.SysServiceAttributeService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.clou.esp.hes.app.web.validation.ValidGroup2;
import com.clou.esp.hes.app.web.validation.ValidGroup3;
import com.google.common.collect.Lists;
import com.power7000g.core.excel.ExcelDataFormatter;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;
import com.power7000g.core.util.redis.JedisUtils;

import ch.iec.tc57._2011.enddevicecontrols.RequestEndDeviceControlsPort;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl.EndDeviceControlType;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl.EndDevices;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl.EndDevices.Names;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl.EndDevices.Names.NameType;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControls;
import ch.iec.tc57._2011.enddevicecontrolsmessage.EndDeviceControlsPayloadType;
import ch.iec.tc57._2011.enddevicecontrolsmessage.EndDeviceControlsRequestMessageType;
import ch.iec.tc57._2011.enddevicecontrolsmessage.EndDeviceControlsResponseMessageType;
import ch.iec.tc57._2011.schema.message.HeaderType;
import ch.iec.tc57._2011.schema.message.UserType;

/**
 * <AUTHOR>
 * @时间：2018-04-27 09:13:12
 * @描述：dataParameterPlan类
 */
@Controller
@RequestMapping("/dataParameterPlanController")
public class DataParameterPlanController extends BaseController{

 	@Resource
    private DataParameterPlanService 	dataParameterPlanService;
 	@Resource
    private DataParameterJobService 	dataParameterJobService;
 	@Resource
    private AssetMeterService 			assetMeterService;
 	@Resource
    private AssetMeterGroupService 		assetMeterGroupService;
 	@Resource
 	private AssetMeterGroupValueService 		assetMeterGroupValueService;
 	@Resource
    private AssetMeterGroupMapService 	assetMeterGroupMapService;
 	@Resource
    private DataUserLogService 			dataUserLogService;
 	@Resource
    private SysServiceAttributeService  sysServiceAttributeService;
 	@Resource
 	private DictDeviceTypeService       dictDeviceTypeService;
 	@Resource
 	private DictProfileService          dictProfileService;

	private static SimpleDateFormat sdf = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.formatter"));

	/**
	 * 跳转到Meter Group Upgrade列表页面
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "meterGroupUpgradeList")
    public ModelAndView meterGroupUpgradeList(HttpServletRequest request, Model model) {
		AssetMeterGroup temp = new AssetMeterGroup();
		temp.setType("1");		//初始页面，默认选取measurement的所有组
		List<AssetMeterGroup> list = assetMeterGroupService.getList(temp);
		model.addAttribute("groupList", list);
	    Calendar lastDate = Calendar.getInstance();
	    model.addAttribute("planStartTime", sdf.format(lastDate.getTime()));
	    lastDate.add(Calendar.DATE, 7);	//日期延后7天
	    model.addAttribute("planExpiryTime", sdf.format(lastDate.getTime()));
	    
        return new ModelAndView("/data/meterGroupUpgradeList");
    }
	/**
	 * 跳转到add plan页面
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "toAddNewPlan")
    public ModelAndView list(DataParameterPlan plan, HttpServletRequest request, Model model,String jobState) {
		DataParameterPlan planTemp = new DataParameterPlan();
		if(StringUtil.isNotEmpty(plan.getId())){
			planTemp = dataParameterPlanService.getEntity(plan.getId());
		}
	    Calendar lastDate = Calendar.getInstance();
	    model.addAttribute("planStartTime", sdf.format(lastDate.getTime()));
	    lastDate.add(Calendar.DATE, 7);	//日期延后7天
	    model.addAttribute("planExpiryTime", sdf.format(lastDate.getTime()));
	    model.addAttribute("plan", planTemp);
	    model.addAttribute("jobState", jobState);
        return new ModelAndView("/data/addNewPlan");
    }

	/**
	 * 跳转到dataParameterPlan新增界面
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dataParameterPlan")
	public ModelAndView dataParameterPlan(DataParameterPlan dataParameterPlan,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dataParameterPlan.getId())){
			try {
                dataParameterPlan=dataParameterPlanService.getEntity(dataParameterPlan.getId());
            }
            catch (Exception e) {
                e.printStackTrace();
            }
			model.addAttribute("dataParameterPlan", dataParameterPlan);
		}
		return new ModelAndView("/data/dataParameterPlan");
	}

	/**
	 * 电表组升级，根据查询条件获取电表List
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "getPlanMeterList_MGU")
    @ResponseBody
    public JqGridResponseTo getPlanMeterList_MGU(JqGridSearchTo jqGridSearchTo, HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request);		//取传递的参数到JqGridSearchTo
        Map<String, Object> map = jqGridSearchTo.getMap();
        JqGridResponseTo json = null;
       
        try {
        	if("meter".equals(map.get("searchType"))){
        		if(!StringUtil.isNotEmpty(map.get("groupType"))){	//判断值是否为空，为空直接返回
    	        	return json;
    	        }
        		json = assetMeterGroupMapService.getPlanMeterList_MGU(jqGridSearchTo);
        	}else if ("job".equals(map.get("searchType"))){
        		if(!StringUtil.isNotEmpty(map.get("groupType"))){	//判断值是否为空，为空直接返回
    	        	return json;
    	        }
        		json = assetMeterGroupMapService.getPlanJobList_MGU(jqGridSearchTo);
        	}else if("comm".equals(map.get("searchType"))){
        		json = assetMeterGroupMapService.getPlanMeterList_MGU(jqGridSearchTo);
        	}else if ("commJob".equals(map.get("searchType"))){
        		json = assetMeterGroupMapService.getPlanJobList_MGU(jqGridSearchTo);
        	}
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return json;
    }
	/**
	 * meter group upgrade根据查询条件获取job list信息
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "getPlanJobList_MGU")
    @ResponseBody
    public JqGridResponseTo getPlanJobList_MGU(JqGridSearchTo jqGridSearchTo, HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request);	//取传递的参数到JqGridSearchTo
        JqGridResponseTo json = null;
        try {
        	json = assetMeterGroupMapService.getPlanJobList_MGU(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return json;
    }
	
	/**
	 * 获取Meter Group Upgrade创建的plan的job list信息
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "getMGU_PlanMeterList")
    @ResponseBody
    public JqGridResponseTo getMGU_PlanMeterList(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request);	//取传递的参数到JqGridSearchTo
        JqGridResponseTo json = null;
        try {
        	json = dataParameterPlanService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return json;
    }
	
	/**
	 * 获取
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request);		//取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        if(jqGridSearchTo.getMap().get("deviceType")==null||"".equals(jqGridSearchTo.getMap().get("deviceType"))) {
        	return j;
        }
        try {
             j = dataParameterPlanService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除dataParameterPlan信息
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DataParameterPlan dataParameterPlan,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dataParameterPlanService.deleteById(dataParameterPlan.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
	 * 保存生成固件升级计划
	 * 
	 * @param dfp
	 * @param request
	 * @return
	 */
    @Transactional
	@RequestMapping(value = "saveMeterGroupUpgradePlan")
	@ResponseBody
	public AjaxJson saveMeterGroupUpgradePlan(DataParameterPlan plan, HttpServletRequest request,
			String searchType,String deviceType_s,String fileIndexId) {
		AjaxJson j = new AjaxJson();
		SysUser su = TokenManager.getToken();
		plan.setOperatorId(su.getId());
		plan.setState("0");// 是否取消；0=未取消；1=已取消；
		plan.put("deviceType", deviceType_s);
		try {
			if("comm".equals(searchType)) {
				plan.setDeviceType("2");//1：电表    2：集中器
				j = dataParameterPlanService.saveMGU_Plan(plan, sysServiceAttributeService);
				//添加文件
				String paramXml =JedisUtils.get(fileIndexId);
				if(StringUtils.isEmpty(paramXml)){
					j.setErrorMsg(MutiLangUtil.doMutiLang("commGroupUpgrade.setFirst"));
				}
				AssetMeterGroupValue value = new AssetMeterGroupValue();
				value.setGroupId(plan.getId());
				value.setDataitemId("1");
				value.setXmlValue(paramXml);
				assetMeterGroupValueService.save(value);
				//清空redis
				JedisUtils.del(fileIndexId);
	            dataUserLogService.insertDataUserLog(su.getId(), 
	            		"DCU Group Upgrade", "Add Plan", "Add Plan ( Plan Description=" + plan.getIntroduction() + ")");
			}else {
				plan.setDeviceType("1");
				AssetMeterGroup group = assetMeterGroupService.getEntity(plan.getGroupId());
				j = dataParameterPlanService.saveMGU_Plan(plan, sysServiceAttributeService);
				//获取电表组的信息（类型，名字）
				//添加操作日志
		        dataUserLogService.insertDataUserLog(su.getId(), 
		        		"Meter Group Upgrade", "Add Plan", "Add Plan (Group Type=" + group.getTypeName() 
		        		+ ", Group="+ group.getName() +", Plan Description=" + plan.getIntroduction() + ")");
				}
		} catch (Exception e) {
			e.printStackTrace();
			j.setErrorMsg(MutiLangUtil.doMutiLang("meterGroupUpgrade.exceptionMsg"));
		}
		return j;
	}
	
	/**
	 * 获取旧升级计划的未完成的job，生成新的plan和job
	 * @param dfp
	 * @param request
	 * @return
	 * @throws ParseException 
	 */
	@RequestMapping(value = "cretePlanAgain")
	@ResponseBody
	public AjaxJson cretePlanAgain(DataParameterPlan plan,String jobState, HttpServletRequest request) throws ParseException {
		AjaxJson j = new AjaxJson();
		SysUser su = TokenManager.getToken();
		//获取旧的升级计划
		DataParameterPlan oldPlan = dataParameterPlanService.getEntity(plan.getId());
		//获取该计划未完成job数量，如果为0，则计划不可创建
		DataParameterJob entity = new DataParameterJob();
		entity.setPlanId(plan.getId());
		//entity.setState("4"); waiting状态
		if(!"4".equals(jobState)&&!"5".equals(jobState)) {
			jobState="8";
		}
		entity.setState(jobState);
		int jobCount = dataParameterJobService.getCount(entity).intValue();
		if(jobCount == 0){
			j.setMsg(MutiLangUtil.doMutiLang("meterGroupUpgrade.jobUpgradeSucc"));
			j.setSuccess(false);
		}else{
			//新的升级计划
			DataParameterPlan newPlan = new DataParameterPlan();
			newPlan.setIntroduction(plan.getIntroduction());
			newPlan.setOperatorId(su.getId());
			newPlan.setState("0");		// 是否取消；0=未取消；1=已取消
			newPlan.setDeviceType(oldPlan.getDeviceType()); //1：电表    2：集中器
			newPlan.setStartTime(sdf.parse(plan.getTaskStartTimeStr()));
			newPlan.setExpiryTime(sdf.parse(plan.getTaskEndTimeStr()));
			newPlan.setEndTime(null);
			newPlan.setTaskStartTime(oldPlan.getTaskStartTime());
			newPlan.setTaskEndTime(oldPlan.getTaskEndTime());
			newPlan.setGroupType(oldPlan.getGroupType());
			newPlan.setGroupId(oldPlan.getGroupId());
			newPlan.setTaskCycle(oldPlan.getTaskCycle());
			newPlan.setJobState(jobState);
			try {
				j = dataParameterPlanService.cretePlanAgain(newPlan, oldPlan.getId(), sysServiceAttributeService);
				//获取电表组的信息（类型，名字）
				if(StringUtils.isNotEmpty(oldPlan.getGroupId())) {
					AssetMeterGroup group = assetMeterGroupService.getEntity(oldPlan.getGroupId());
					//添加操作日志
					if(group!=null) {
		            dataUserLogService.insertDataUserLog(su.getId(), 
		            		"Meter Group Upgrade", "Add Plan", "Add Plan (Group Type=" + group.getTypeName() 
		            		+ ", Group="+ group.getName() +", Plan Description=" + plan.getIntroduction() + ")");
					}
				}else {
					dataUserLogService.insertDataUserLog(su.getId(), 
						"DCU Group Upgrade", "Add Plan", "Add Plan ( Plan Description=" + plan.getIntroduction() + ")");
				}
			} catch (Exception e) {
				e.printStackTrace();
				j.setErrorMsg(MutiLangUtil.doMutiLang("meterGroupUpgrade.exceptionMsg"));
			}
		}
		return j;
	}
    
    /**
     * 根据Group Type 获取group list
     * @param id
     * @return
     */
    @RequestMapping(value = "getGroupList")
    @ResponseBody
    public AjaxJson del(String groupType, HttpServletRequest request) {
        AjaxJson json = new AjaxJson();
        try {
//            SysUser su = TokenManager.getToken();
            if(StringUtil.isNotEmpty(groupType)){
            	AssetMeterGroup entity = new AssetMeterGroup();
            	entity.setType(groupType);
            	List<AssetMeterGroup> groupList = assetMeterGroupService.getList(entity);
            	json.setObj(groupList);
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            json.setSuccess(false);
            json.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return json;
    }
	
    /**
	 * 取消meter group upgrade升级计划，取消DataParameterPlan信息
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "cancelPlan")
	@ResponseBody
	public AjaxJson cancelPlan(String id, HttpServletRequest req) {
		AjaxJson j = new AjaxJson();
		try {
			if (StringUtil.isEmpty(id)) {
				j.setErrorMsg(MutiLangUtil.doMutiLang("meterGroupUpgrade.pleaseSelectAplan"));
				return j;
			}
			DataParameterPlan plan = dataParameterPlanService.getEntity(id);
			if (plan == null) {
				j.setErrorMsg(MutiLangUtil.doMutiLang("meterGroupUpgrade.noFoundPlan"));
				return j;
			}
			Date expiryTime = plan.getExpiryTime();
			Date current = new Date();
			String state = plan.getState();
			j.setErrorMsg(MutiLangUtil.doMutiLang("system.cancelFail"));
			//取消条件：state == 0 /  当前时间在过期时间之前
			if (state.equals("0") && current.before(expiryTime)) {
				/*
				 * 更改plan的状态为已取消，然后再调用接口
				 */
				plan.setState("1");	//取消状态
				int update = dataParameterPlanService.update(plan);
				//取消plan
				this.dataParameterJobService.cancelByPlanId(id);
				System.out.println("Update meter group upgrade plan status is == " + update);
				RequestEndDeviceControlsPort port = (RequestEndDeviceControlsPort)
						UciInterfaceUtil.getInterface("RequestEndDeviceControlsPort", RequestEndDeviceControlsPort.class,sysServiceAttributeService);
				EndDeviceControlsRequestMessageType reqMsg = new EndDeviceControlsRequestMessageType();
				SysUser su = TokenManager.getToken();
				HeaderType header = new HeaderType();
				Date date = new Date();
				header.setVerb("cancel");
				header.setNoun("EndDeviceControls");
				header.setTimestamp(DateUtils.dateToXmlDate(date));
				header.setSource("ClouESP HES");
				header.setAsyncReplyFlag(false);
				header.setReplyAddress("");
				header.setAckRequired(true);
				UserType u = new UserType();
				u.setOrganization(su.getOrgId());
				u.setUserID(su.getId());
				header.setUser(u);
				header.setMessageID(DateUtils.formatDate("yyyyMMdd") + su.getId());
				header.setCorrelationID(DateUtils.formatDate("yyyyMMdd") + su.getId());
				reqMsg.setHeader(header);
				EndDeviceControlsPayloadType payload = new EndDeviceControlsPayloadType();
				EndDeviceControls endDeviceControls = new EndDeviceControls();
				List<EndDeviceControl> endDeviceControl = endDeviceControls.getEndDeviceControl();
				EndDeviceControl edc = new EndDeviceControl();
				edc.setMRID(id);				// 放的电表组升级计划主键ID
				edc.setReason("immediately");	// immediately 立即开始， periodicity
				EndDeviceControlType edct = new EndDeviceControlType();
				edct.setRef("********");		// 取消升级计划dataitem ID
				edc.setEndDeviceControlType(edct);
				List<EndDeviceControl.EndDevices> endDevices = edc.getEndDevices();
				EndDevices ed = new EndDevices();
				List<EndDeviceControl.EndDevices.Names> names = ed.getNames();
				Names name1 = new Names();
				name1.setName(DateUtils.date2Str(plan.getStartTime(), DateUtils.date_sdf));
				NameType nt1 = new NameType();
				nt1.setName("plan start time");
				name1.setNameType(nt1);
				Names name2 = new Names();
				name2.setName(DateUtils.date2Str(plan.getExpiryTime(), DateUtils.date_sdf));
				NameType nt2 = new NameType();
				nt2.setName("plan end time");
				name2.setNameType(nt2);
				Names name3 = new Names();
				name3.setName(DateUtils.date2Str(plan.getTaskStartTime(), DateUtils.HHmmss));
				NameType nt3 = new NameType();
				nt3.setName("job start time");
				name3.setNameType(nt3);
				Names name4 = new Names();
				name4.setName(DateUtils.date2Str(plan.getTaskEndTime(), DateUtils.HHmmss));
				NameType nt4 = new NameType();
				nt4.setName("job end time");
				name4.setNameType(nt4);
				Names name5 = new Names();
				name5.setName(plan.getTaskCycle().toString());
				NameType nt5 = new NameType();
				nt5.setName("task cycle");
				name5.setNameType(nt5);
				names.add(name1);
				names.add(name2);
				names.add(name3);
				names.add(name4);
				names.add(name5);
				endDevices.add(ed);
				endDeviceControl.add(edc);
				payload.setEndDeviceControls(endDeviceControls);
				reqMsg.setPayload(payload);
				System.out.println("XML format == " + XMLUtil.convertToXml(reqMsg));
				EndDeviceControlsResponseMessageType resMsg = port.cancelEndDeviceControls(reqMsg);		// result OK就是成功
				System.out.println("Cancel Meter/DCU group upgrade Plan return result === " + XMLUtil.convertToXml(resMsg));
				String result = resMsg.getReply().getResult();
				if (result != null && result.toUpperCase().equals("OK")) {
					//获取电表组的信息（类型，名字）
					if(StringUtils.isNotEmpty(plan.getGroupId())) {
						AssetMeterGroup group = assetMeterGroupService.getEntity(plan.getGroupId());
						//添加操作日志
			            dataUserLogService.insertDataUserLog(su.getId(), 
			            		"Meter Group Upgrade", "Cancel Plan", "Cancel Plan (Group Type=" + group.getTypeName() 
			            		+ ", Group="+ group.getName() +", Plan Description=" + plan.getIntroduction() + ")");
					}else {
						//添加操作日志
						dataUserLogService.insertDataUserLog(su.getId(), 
							"DCU Group Upgrade", "Cancel Plan", "Plan Description=" + plan.getIntroduction() + ")");
					}
					j.setSuccess(true);
					j.setMsg(MutiLangUtil.doMutiLang("system.cancelSucc"));
				} else {
					j.setErrorMsg(MutiLangUtil.doMutiLang("system.cancelFail"));
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			j.setErrorMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
		}
		return j;
	}
	
	/**
	 * 导出tab1待升级的电表
	 * @param groupType
	 * @param group
	 * @param sn
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "exportMGU_PlanMeterList")
	@ResponseBody
	public void exportFMUPlanMeterList(String groupType, String groupId, String sn, String exportType,
			String planId, HttpServletRequest request, HttpServletResponse response,String searchType,String deviceType) {
		AjaxJson j = new AjaxJson();
		try {
			if("comm".equals(searchType)) {
				
				AssetMeterGroupMap meter = new AssetMeterGroupMap();
				meter.setDeviceType(deviceType);
				meter.put("planId", planId);
				meter.put("deviceType", deviceType);
				meter.put("searchType", "comm");
				meter.setSn(sn);
				if (exportType.equals("1")) {	// Meter list
					List<AssetMeterGroupMap> list = assetMeterGroupMapService.getPlanMeterList_export(meter);
					ExcelDataFormatter edf = new ExcelDataFormatter();
					ExcelUtils.writeToFile(list, edf, System.currentTimeMillis() + ".xlsx", response, ValidGroup3.class);
				} else {						// Job list
					List<AssetMeterGroupMap> list = assetMeterGroupMapService.getPlanJobList_export(meter);
					//转化时间显示格式
					for (int i = 0; i < list.size(); i++) {
						list.get(i).setStartTimeStr(DateUtils.date2Str(list.get(i).getStartTime(), new SimpleDateFormat("MM/dd/yyyy HH:mm:ss")));
						list.get(i).setExpiryTimeStr(DateUtils.date2Str(list.get(i).getExpiryTime(), new SimpleDateFormat("MM/dd/yyyy HH:mm:ss")));
					}
					ExcelDataFormatter edf = new ExcelDataFormatter();
					ExcelUtils.writeToFile(list, edf, System.currentTimeMillis() + ".xlsx", response, ValidGroup3.class);
				}
			}else {
				if (StringUtil.isEmpty(groupType) || StringUtil.isEmpty(groupId)) {
					return;
				}
				AssetMeterGroupMap meter = new AssetMeterGroupMap();
				meter.setGroupType(groupType);
				meter.setGroupId(groupId);
				meter.put("planId", planId);
				if (StringUtil.isNotEmpty(sn)) {
					meter.setSn(sn);
				}
				if (exportType.equals("1")) {	// Meter list
					List<AssetMeterGroupMap> list = assetMeterGroupMapService.getPlanMeterList_export(meter);
					ExcelDataFormatter edf = new ExcelDataFormatter();
					ExcelUtils.writeToFile(list, edf, System.currentTimeMillis() + ".xlsx", response, ValidGroup1.class);
				} else {						// Job list
					List<AssetMeterGroupMap> list = assetMeterGroupMapService.getPlanJobList_export(meter);
					//转化时间显示格式
					for (int i = 0; i < list.size(); i++) {
						list.get(i).setStartTimeStr(DateUtils.date2Str(list.get(i).getStartTime(), new SimpleDateFormat("MM/dd/yyyy HH:mm:ss")));
						list.get(i).setExpiryTimeStr(DateUtils.date2Str(list.get(i).getExpiryTime(), new SimpleDateFormat("MM/dd/yyyy HH:mm:ss")));
					}
					ExcelDataFormatter edf = new ExcelDataFormatter();
					ExcelUtils.writeToFile(list, edf, System.currentTimeMillis() + ".xlsx", response, ValidGroup2.class);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
		}
	}
	
	/**
	 * 打印待升级的电表
	 * @param deviceType
	 * @param sn
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "printMGU_PlanMeterList")
	@ResponseBody
	public void printMGU_PlanMeterList(String groupType, String groupId, String sn, String exportType,
			String planId, HttpServletRequest request, HttpServletResponse response) {
		if (StringUtil.isEmpty(groupType) || StringUtil.isEmpty(groupId)) {
			return;
		}
		AssetMeterGroupMap meter = new AssetMeterGroupMap();
		meter.setGroupType(groupType);
		meter.setGroupId(groupId);
		if (StringUtil.isNotEmpty(sn)) {
			meter.setSn(sn);
		}
		List<AssetMeterGroupMap> meterList = null;
		List<AssetMeterGroupMap> jobList = null;
		if (exportType.equals("1")) {		// Meter
			meterList = assetMeterGroupMapService.getPlanMeterList_export(meter);
			if (meterList != null ||meterList.size() > 0) {
				for (int i = 0; i < meterList.size(); i++) {
					
				}
			}else{
				AssetMeterGroupMap tempMeter = new AssetMeterGroupMap();
				tempMeter.setSn("");
				tempMeter.setManufacturer("");
				tempMeter.setModel("");
				tempMeter.setGroupType("");
				tempMeter.setGroupId("");
				meterList.add(tempMeter);
			}
		} else {							// job
			jobList = assetMeterGroupMapService.getPlanJobList_export(meter);
			if (jobList != null || jobList.size() > 0) {
				for (int i = 0; i < jobList.size(); i++) {
					
				}
			}else{
				AssetMeterGroupMap tempJob = new AssetMeterGroupMap();
				tempJob.setSn("");
				tempJob.setManufacturer("");
				tempJob.setModel("");
				tempJob.setGroupType("");
				tempJob.setGroupId("");
				tempJob.setStartTime(null);
				tempJob.setExpiryTime(null);
				jobList.add(tempJob);
			}
		}
		if (exportType.equals("1")) {
			CreatePdf.printPdf(meterList, null, ValidGroup1.class, request, response);
		} else {
			CreatePdf.printPdf(jobList, null, ValidGroup1.class, request, response);
		}
	}
	
	/**
	 * 导出或者打印Tab2 升级计划
	 * @param type
	 * @param dfp
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@RequestMapping(value = "exportOrPrintPlanReportList")
	@ResponseBody
	public void exportOrPrintPlanReportList(String operaType, String groupType, String groupId, String state,String deviceType,
			HttpServletRequest request, HttpServletResponse response)throws Exception {
		if (StringUtil.isEmpty(operaType)) {
			return;
		}
		DataParameterPlan plan = new DataParameterPlan();
		plan.setGroupType(groupType);
		plan.setGroupId(groupId);
		plan.setState(state);
		plan.setDeviceType(deviceType);
		List<DataParameterPlan> list = dataParameterPlanService.exportPlanReportList(plan);
		if(!(list.size() > 0)){		//当查询的数据为空值时，插入一条空数据，防止导出Excel报错
			DataParameterPlan temp = new DataParameterPlan();
			temp.setIntroduction("");
			temp.setStartTimeStr("");
			temp.setTaskStartTimeStr("");
			temp.setTaskEndTimeStr("");
			temp.setGroupType("");
			temp.setTaskCycle("");
			temp.setExpiryTimeStr("");
			temp.setDone("");
			temp.setExpired("");
			temp.setRunning("");
			temp.setWaiting("");
			temp.setGroupName("");
			list.add(temp);
		}
		ExcelDataFormatter edf = new ExcelDataFormatter();
		if (operaType.equals("export")) {	// export
			if("1".equals(deviceType)) {
				ExcelUtils.writeToFile(list, edf, System.currentTimeMillis() + ".xlsx", response, ValidGroup1.class);
			}else {
				ExcelUtils.writeToFile(list, edf, System.currentTimeMillis() + ".xlsx", response, ValidGroup2.class);
			}
		} else {
			CreatePdf.printPdf(list, edf, ValidGroup1.class, request, response);
		}
	}
	
	/**
	 * 打印meter group upgrade计划下的job任务列表
	 * @param type
	 * @param planId
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@RequestMapping(value = "exportOrPrintPlanReportJobList")
	@ResponseBody
	public void exportOrPrintPlanReportJobList(String type, String planId,String jobState,HttpServletRequest request,
			HttpServletResponse response,String deviceType) throws Exception {
		if (StringUtil.isEmpty(type)) {
			return;
		}
		if (StringUtil.isEmpty(planId)) {
			return;
		}
		DataParameterPlan plan = dataParameterPlanService.getEntity(planId);
		if (plan == null) {
			return;
		}
		DataParameterJob job = new DataParameterJob();
		job.setPlanId(planId);
		job.setState(jobState);
		job.put("deviceType", deviceType);
		List<DataParameterJob> list = dataParameterJobService.exportOrPrintPlanReportJobList(job);
		if(!(list.size() > 0)){		//当查询的数据为空值时，插入一条空数据，防止导出Excel报错
			DataParameterJob temp = new DataParameterJob();
			temp.setState("");
			temp.setLastExecTime(new Date());
			temp.setFailedReason("");
			temp.setSn("");
			temp.setModel("");
			temp.setManufacturer("");
			list.add(temp);
		}
		ExcelDataFormatter edf = new ExcelDataFormatter();
		// 1:Runing,2:Done,3:Cancel,4:Waiting,5:Expired
		Map<String, String> trt = new HashMap<String, String>();
		trt.put("1", "Runing");
		trt.put("2", "Done");
		trt.put("3", "Cancel");
		trt.put("4", "Waiting");
		trt.put("5", "Expired");
		edf.set("state", trt);
		// 要日期格式化使用以下方式
		Map<String, String> tvs = new HashMap<String, String>();
		tvs.put("lastExecTime", "MM/dd/yyyy HH:mm:ss");
		edf.set("lastExecTime", tvs);
		if (type.equals("export")) {// export
			ExcelUtils.writeToFile(list, edf, System.currentTimeMillis() + ".xlsx", response, ValidGroup1.class);
		} else {
			CreatePdf.printPdf(list, edf, ValidGroup2.class, request, response);
		}
	}
	
	
	
	@RequestMapping(value  = "dcuGroupUpgradeList")
    public ModelAndView dcuGroupUpgradeList(HttpServletRequest request, Model model) {
		List<Integer> dictDeviceTypeIds = Lists.newArrayList();
		dictDeviceTypeIds.add(202);
		dictDeviceTypeIds.add(203);
		dictDeviceTypeIds.add(204);
		dictDeviceTypeIds.add(205);
		List<DictDeviceType> dictDeviceTypes = this.dictDeviceTypeService.getListByIds(dictDeviceTypeIds);
		String deviceTypeReplace = RoletoJson.listToReplaceStr(dictDeviceTypes, "id", "name");
		model.addAttribute("deviceTypeReplace", deviceTypeReplace);
		
		
		SimpleDateFormat sdf = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.formatter"));
	    Calendar lastDate = Calendar.getInstance();
	    model.addAttribute("planStartTime", sdf.format(lastDate.getTime()));
	    lastDate.add(Calendar.DATE, 7);	//日期延后7天
	    model.addAttribute("planExpiryTime", sdf.format(lastDate.getTime()));
	    
        return new ModelAndView("/data/dcuGroupUpgradeList");
    }
	
	@RequestMapping(value  = "setDcuXml")
	public ModelAndView setDcuXml(HttpServletRequest request, Model model) {
		String baudRateReplace   = "0:300,";
		baudRateReplace += "1:600,";
		baudRateReplace += "2:1200,";
		baudRateReplace += "3:2400,";
		baudRateReplace += "4:4800,";
		baudRateReplace += "5:9600,";
		baudRateReplace += "6:19200,";
		baudRateReplace += "7:38400,";
		baudRateReplace += "8:57600,";
		baudRateReplace += "9:115200";
		String comTypeReplace   = "1:RS485-1,";
		comTypeReplace += "2:RS485-2,";
		comTypeReplace += "30:RF,";
		comTypeReplace += "31:PLC";
		
		DictProfile dictProfile = new DictProfile();
		//获取所有的profile类型
		dictProfile.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
		dictProfile.setProfileType("5");
		List<DictProfile> dictProfilelist = dictProfileService.getList(dictProfile);
		
		 if(dictProfilelist != null && dictProfilelist.size() > 0){
        	for(DictProfile dictProfile1 : dictProfilelist){
        		if(dictProfile1.getProtocolCode() != null){
        			if(dictProfile1.getProtocolCode().split("#").length == 3){
        				dictProfile1.setProtocolCode(dictProfile1.getProtocolCode().split("#")[1]);
        			}
        		}
        	}
         }
		 model.addAttribute("dictProfilelist",dictProfilelist);
		 model.addAttribute("baudRateReplace",baudRateReplace);
		 model.addAttribute("comTypeReplace",comTypeReplace);
		 model.addAttribute("dmlsDefaultDate",DateUtils.date2Str(new Date(), DateTimeFormatterUtil.getSimpleDateFormat_HHmmss(ResourceUtil.getSessionattachmenttitle("local.time.flag"))));
		return new ModelAndView("/data/dcuGroupUpgradeSetXml");
	}
	
	@RequestMapping(value  = "showDcuXml")
	public ModelAndView showDcuXml(HttpServletRequest request, Model model,String id) {
		String paramXml =JedisUtils.get(id);
		model.addAttribute("paramXml",paramXml);
		return new ModelAndView("/data/dcuGroupUpgradeShowXml");
	}
	
	
	
	
}