/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysMenu{ } 
 * 
 * 摘    要： 系统菜单
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-10-27 08:42:10
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.system;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.tag.vo.JqTreeUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.dict.DictMenu;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.system.SysMenuService;
import com.github.pagehelper.PageInfo;

/**
 * <AUTHOR>
 * @时间：2017-10-27 08:42:10
 * @描述：系统菜单类
 */
@Controller
@RequestMapping("/sysMenuController")
public class SysMenuController extends BaseController{

 	@Resource
    private SysMenuService sysMenuService;

	/**
	 * 跳转到系统菜单列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/system/sysMenuList");
    }

	/**
	 * 跳转到系统菜单新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "sysMenu")
	public ModelAndView sysMenu(DictMenu sysMenu,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(sysMenu.getId())){
			try {
                sysMenu=sysMenuService.getEntity(sysMenu.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("sysMenu", sysMenu);
		}
		return new ModelAndView("/system/sysMenu");
	}


	/**
	 * 系统菜单查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
        	jqGridSearchTo.put("id", "no");
            PageInfo<Map<String, Object>> pageInfo = new PageInfo<Map<String, Object>>();
            DictMenu sysMenu = new DictMenu();
     		sysMenu.setMenulevel(1);
     		List<DictMenu> parentMenuList = sysMenuService.getList(sysMenu);	
     		addMenuChildList(parentMenuList);
            j= JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
     		JqTreeUtil jtu = new JqTreeUtil();
     		jtu.setFields(jqGridSearchTo.getField());
     		jtu.setIdFieldName("id");
     		jtu.setParentFieldName("parentmenuid");
     		jtu.setSubsetFieldName("childList");
     		jtu.setExpandedFieldName("extData.expanded");
     		j.setRows(jtu.getTreeGridData(parentMenuList));
     		System.out.println(j.toJSONString());
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
	/**
	 * 遍历得到子列表
	 * @param menuList
	 */
	public void addMenuChildList(List<DictMenu> menuList){
		for(DictMenu m:menuList){
			 DictMenu sysMenu = new DictMenu();
	     	 sysMenu.setParentmenuid(m.getId());
	     	 List<DictMenu> childList = sysMenuService.getList(sysMenu);
	     	 addMenuChildList(childList);
	     	 if(childList.size()>0){
	     		 m.put("expanded", true);
	     	 }
	     	 m.setChildList(childList);
		}
	}
    
    /**
     * 删除系统菜单信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DictMenu sysMenu,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(sysMenuService.deleteById(sysMenu.getId())>0){
                j.setMsg("删除成功");
            }else{
                j.setSuccess(false);
                j.setMsg("删除失败");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
    
    /**
     * 保存系统菜单信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DictMenu sysMenu,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DictMenu t=new  DictMenu();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(sysMenu.getId())){
        	t=sysMenuService.getEntity(sysMenu.getId());
			MyBeanUtils.copyBeanNotNull2Bean(sysMenu, t);
				sysMenuService.update(t);
				j.setMsg("修改成功");
				
			}else{
	            sysMenuService.save(sysMenu);
	            j.setMsg("创建成功");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
	
}