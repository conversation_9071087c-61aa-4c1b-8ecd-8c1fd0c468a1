alter table DICT_DEVICE_MODEL add column INIT_CREDIT_AMOUNT decimal(10,4) DEFAULT 0.0;
update DICT_DEVICE_MODEL set INIT_CREDIT_AMOUNT = 10.0 where id= '101001';
update DICT_DEVICE_MODEL set INIT_CREDIT_AMOUNT = 15.0 where id= '101002';
update DICT_DEVICE_MODEL set INIT_CREDIT_AMOUNT = 0 where id= '101003';
update DICT_DEVICE_MODEL set INIT_CREDIT_AMOUNT = 10.0 where id= '102001';
update DICT_DEVICE_MODEL set INIT_CREDIT_AMOUNT = 15.0 where id= '102002';
update DICT_DEVICE_MODEL set INIT_CREDIT_AMOUNT = 0 where id= '102003';
update DICT_DEVICE_MODEL set INIT_CREDIT_AMOUNT = 15.0 where id= '103001';
update DICT_<PERSON>VI<PERSON>_MODEL set INIT_CREDIT_AMOUNT = 0 where id= '103002';

INSERT INTO `ppm_sys_menu` (`ID`, `UTILITY_IDS`, `ME<PERSON><PERSON>VEL`, `<PERSON><PERSON><PERSON>ME`, `MENUORDER`, `FUNCTIONURL`, `PARENTMENUID`, `MENU_INTRO`, `FUNCTIONID`, `HIDE_TAB`) VALUES ('1e858f11beda11e79bb968f728cb4019', '1', '2', 'Meter Data Monthly Supplement', '5', 'meterDataMonthlySupplementController/list.do', '1e858f11beda11e79bb968f728c50005', NULL, '2016', NULL);
INSERT INTO `ppm_dict_function` (`ID`, `FUNCTIONNAME`, `FUNCTIONURL`, `FUNCTION_INTRO`) VALUES ('2016', 'Meter Data Monthly Supplement', 'meterDataMonthlySupplementController/list.do', 'System');
INSERT INTO `ppm_dict_operation` (`ID`, `OPERATIONNAME`, `FUNCTION_ID`, `OPERATIONURL`, `ISHIDE`, `DESCRIPTION`) VALUES ('2016001', 'Meter Data Monthly Supplement', '2016', 'addMeterDataMonthlySupplement', '1', 'Meter Data Monthly Supplement');
alter table data_md_energy_monthly add `FLAG` decimal(2,0) NOT NULL DEFAULT '1';
alter table ASSET_CUSTOMER add `GEO_CODE` varchar(30) DEFAULT NULL COMMENT '地理代码';

UPDATE PPM_DICT_DETAIL SET GUI_DISPLAY_NAME = 'Cancellation Token' WHERE DICT_ID='54' AND INNER_VALUE='15';
UPDATE PPM_DICT_DETAIL SET GUI_DISPLAY_NAME = 'Disable' WHERE DICT_ID='84' AND INNER_VALUE='0';
UPDATE PPM_DICT_DETAIL SET GUI_DISPLAY_NAME = 'Enable' WHERE DICT_ID='84' AND INNER_VALUE='1';
UPDATE PPM_DICT_DETAIL SET GUI_DISPLAY_NAME = 'Destroy' WHERE DICT_ID='65' AND INNER_VALUE='3';
UPDATE PPM_DICT_DETAIL SET GUI_DISPLAY_NAME = 'Meter Cover Opened' WHERE DICT_ID='80' AND INNER_VALUE='140';
UPDATE PPM_DICT_DETAIL SET GUI_DISPLAY_NAME = 'Stopped' WHERE DICT_ID='66' AND INNER_VALUE='2';
UPDATE PPM_DICT_DETAIL SET GUI_DISPLAY_NAME = 'Successed' WHERE DICT_ID='85' AND INNER_VALUE='1';
UPDATE PPM_DICT_DETAIL SET GUI_DISPLAY_NAME = 'Failed' WHERE DICT_ID='85' AND INNER_VALUE='2';
UPDATE PPM_DICT_DETAIL SET GUI_DISPLAY_NAME = 'Disconnector in [Ready for Reconnection]', README = 'Disconnector in [Ready for Reconnection]' WHERE DICT_ID='80' AND INNER_VALUE='47';
UPDATE PPM_DICT_DETAIL SET GUI_DISPLAY_NAME = 'Unable to process the request - transaction attempted and failed' WHERE DICT_ID='91' AND INNER_VALUE='42';

-- 文件MD5变更，该位置开始需手动执行
INSERT INTO `ppm_dict_user_log` (`LOG_TYPE`, `LOG_SUB_TYPE`, `SORT_ID`) VALUES ('Step Tariff', 'Delete Step Tariff', '6');
INSERT INTO `ppm_dict_user_log` (`LOG_TYPE`, `LOG_SUB_TYPE`, `SORT_ID`) VALUES ('Step Tariff', 'Add Step Tariff', '7');
INSERT INTO `ppm_dict_user_log` (`LOG_TYPE`, `LOG_SUB_TYPE`, `SORT_ID`) VALUES ('Step Tariff', 'Modify Step Tariff', '8');
INSERT INTO `ppm_dict_user_log` (`LOG_TYPE`, `LOG_SUB_TYPE`, `SORT_ID`) VALUES ('Step Tariff Detail', 'Delete Step Tariff Detail', '9');
INSERT INTO `ppm_dict_user_log` (`LOG_TYPE`, `LOG_SUB_TYPE`, `SORT_ID`) VALUES ('Step Tariff Detail', 'Add Step Tariff Detail', '10');
INSERT INTO `ppm_dict_user_log` (`LOG_TYPE`, `LOG_SUB_TYPE`, `SORT_ID`) VALUES ('Step Tariff Detail', 'Modify Step Tariff Detail', '11');
INSERT INTO `ppm_dict_user_log` (`LOG_TYPE`, `LOG_SUB_TYPE`, `SORT_ID`) VALUES ('Tariff Group Management', 'Set Passvie Tariff Group', '12');
INSERT INTO `ppm_dict_user_log` (`LOG_TYPE`, `LOG_SUB_TYPE`, `SORT_ID`) VALUES ('Tariff Group Management', 'Activate Tariff Group', '13');

DELIMITER $$
DROP PROCEDURE IF EXISTS Total_month_electricity_consumption_One$$
CREATE PROCEDURE Total_month_electricity_consumption_One(
				IN Input_Org_ID VARCHAR (100)  ,Input_Org_Code VARCHAR (100), Input_Date DATETIME  
				)
BEGIN
/**
        功能说明：根据传入的管理机构，计算上一个月的售电量，保存到 数据库表中
        作者：王波  创建日期：2019年11月20日
		调用方法：call `Total_month_electricity_consumption_One`(Input_Org_ID,Input_Org_Code);
	***/
	DECLARE i INT ;                           /* 循环次数 */
	DECLARE s_SQL VARCHAR(100);
	DECLARE done INT DEFAULT 0;
	DECLARE ID VARCHAR(32);
	DECLARE	s_date VARCHAR (30) DEFAULT NULL;            /* 时间 */
	DECLARE	s_value1 decimal(10,4) DEFAULT 0;           /* 正向有功总电量 */
	DECLARE	s_value6 decimal(10,4) DEFAULT 0;           /* 反向有功总电量 */

    -- select    CONCAT('统计--管理机构：' ,Input_Org_ID ,'%') ; /* 调试输出语句 */
    /* 根据传入的org_ID 进行统计 */  
	DECLARE cur_1 CURSOR FOR    
	select IFNULL(sum(dmim.value1),0) as Month_VALUE1,IFNULL(sum(dmim.value6),0) as Month_VALUE6
	from data_md_interval_monthly dmim
	left join asset_meter am on am.id=dmim.DEVICE_ID
	left join sys_org so on am.org_id=so.id
	where dmim.TV = s_DATE and so.org_code like Input_Org_Code;
    -- 在游标循环到最后会将 done 设置为 1
	DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;
	set s_date = DATE_FORMAT(Input_Date, '%Y-%m-01');  
	set done = 0;
	SET i = 0 ; 
	open cur_1;
	REPEAT 
	FETCH cur_1 INTO s_value1,s_value6;
	if not done then	
			set s_date =  DATE_FORMAT(s_date,'%Y-%m');
			set ID = (SELECT REPLACE(UUID(),'-',''));
			DELETE FROM ppm_total_month_electricity_consumption WHERE ORG_ID = Input_Org_ID AND Months = s_date;
			INSERT INTO ppm_total_month_electricity_consumption(ID,ORG_ID,Months,Month_Electricity) 
			VALUES(ID,Input_Org_ID,s_date,s_value1+s_value6);
	END IF;
	
	SET i = i + 1 ; /* 记录数 计数，在更新过程中判断使用 */
	UNTIL done END REPEAT;
	CLOSE cur_1;
	-- SELECT '管理机构：' + s_date + ' 统计完毕！！'; /* 调试输出语句 */
END$$

DROP PROCEDURE IF EXISTS Total_month_electricity_consumption_All$$
CREATE PROCEDURE Total_month_electricity_consumption_All()
BEGIN
	/**
	        功能说明：通过循环调用 Total_Month_Sales_Amount_One_OrgID 统计所有管理机构的月售电量
	        实现方法：每次传入一个 ORG_ID 对一个管理机构进行统计
			创建日期：2018年06月12日
	        作者：孙学峰
			调用方法：call Total_month_electricity_consumption_All();
	***/

    /* 需要定义接收游标数据的变量  */
    DECLARE done INT DEFAULT 0;
    -- 声明一个变量，用来存放从游标中提取的数据
	-- 特别注意这里的名字不能与由游标中使用的列明相同，否则得到的数据都是NULL
	DECLARE	iRecordNumber INT ;                         /* 记录个数 */
	DECLARE s_ORG_ID VARCHAR (100) DEFAULT NULL;       /* 管理机构ID */
  DECLARE s_ORG_CODE VARCHAR (100) DEFAULT NULL;       /* 管理机构code */
  DECLARE s_DATE DATETIME DEFAULT NOW();       		/* 需要统计的月份 */
  DECLARE v_i int unsigned DEFAULT 1;
    /* 根据传入的管理机构ID 进行统计 */
    -- 声明游标对应的 SQL 语句
  DECLARE cur_1 CURSOR FOR
          SELECT  ID,org_code  FROM sys_org  ORDER BY ID asc ;
     
   -- 在游标循环到最后会将 done 设置为 1
	DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;
  
    /* 打开游标 */
    open cur_1;
	REPEAT
		FETCH cur_1 INTO  s_ORG_ID,s_ORG_CODE;
			if not done then
				set s_ORG_CODE = CONCAT(s_ORG_CODE,'%');
		    WHILE v_i < 3 DO
		    	SET s_DATE = DATE_SUB(s_DATE, INTERVAL 1 MONTH);
		        call Total_month_electricity_consumption_One(s_ORG_ID, s_ORG_CODE, s_DATE);
		        SET v_i = v_i+1;
		    END WHILE;
				SET v_i = 1;
				SET s_DATE = NOW();
			end if;
 	UNTIL done END REPEAT;
   -- 关闭游标
   CLOSE cur_1;
   -- select ' 所有管理机构售电金额 统计完毕！！'; /* 调试输出语句 */
END$$

DROP PROCEDURE IF EXISTS Total_Month_Sales_Amount_One$$
CREATE PROCEDURE Total_Month_Sales_Amount_One(
				IN Input_Org_ID VARCHAR (100)  ,Input_Org_Code VARCHAR (100), Input_DATE DATETIME  
				)
BEGIN
	/**
	        功能说明：根据传入的管理机构，计算最近12个月的售电量，保存到 数据库表中
	        作者：孙学峰 创建日期：2018年06月12日
			调用方法：call `Total_Month_Sales_Amount_One`(Input_Org_ID);
		***/
	
	    /* 需要定义接收游标数据的变量  */
	DECLARE i INT ;                           /* 循环次数 */
	DECLARE s_SQL VARCHAR(100);
    DECLARE done INT DEFAULT 0;
	DECLARE ID VARCHAR(32);
	DECLARE	s_DATE VARCHAR (30) DEFAULT NULL;            /* 时间 */
	DECLARE	s_Value VARCHAR (30) DEFAULT NULL;           /* 统计金额 */
    
    /* 根据传入的org_ID 进行统计 */
    -- 声明游标对应的 SQL 语句
    DECLARE cur_1 CURSOR FOR
    
        SELECT DATE_FORMAT(vhi.SALES_DATE, '%Y-%m') AS StatMonth,
		   SUM(CASE WHEN recharge_type = 0 THEN vhi.Customer_Payment_Amount
			   WHEN recharge_type = 1 THEN - 1 * vhi.Uninstall_Amount
			   ELSE 0
			   END) AS SaleAmount
		FROM
			ppm_vend_historical_info vhi
			LEFT JOIN sys_org so on vhi.org_id=so.id
		WHERE
			vhi.SALES_DATE BETWEEN DATE_FORMAT(Input_DATE, '%Y-%m-01') 
			AND DATE_FORMAT(DATE_ADD(Input_DATE, INTERVAL 1 MONTH), '%Y-%m-01')
            AND vhi.Recharge_Type IN ('0')
            AND vhi.Receipt_State IN ('0')
            AND so.org_code like Input_Org_Code
		GROUP BY DATE_FORMAT(vhi.SALES_DATE, '%Y-%m');

    -- 在游标循环到最后会将 done 设置为 1
	DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;
	set s_DATE = DATE_FORMAT(Input_DATE, '%Y-%m-01');  
     -- 注意这里，一定要重置done的值为 0
	set done = 0;
    /*循环次数--赋初始值*/
    SET i = 0 ; 
 	/* 打开游标 */
    open cur_1;
	REPEAT 
	    FETCH cur_1 INTO s_DATE,s_Value;
   		if not done then
			set ID = (SELECT REPLACE(UUID(),'-',''));
			DELETE FROM ppm_total_month_sales_amount WHERE ORG_ID = Input_Org_ID AND Months = s_DATE;
            			-- 插入新记录
			INSERT INTO ppm_total_month_sales_amount(ID,ORG_ID,Months,Month_Amount) 
			VALUES(ID,Input_Org_ID,s_DATE,s_Value);
		END IF;
        
        SET i = i + 1 ; /* 记录数 计数，在更新过程中判断使用 */
 	UNTIL done END REPEAT;
	CLOSE cur_1;
	-- SELECT '管理机构：' + Input_DATE + ' 统计完毕！！'; /* 调试输出语句 */
END$$

DROP PROCEDURE IF EXISTS Total_Month_Sales_Amount_All$$
CREATE PROCEDURE Total_Month_Sales_Amount_All()
BEGIN
	DECLARE done INT DEFAULT 0;
	
    -- 声明一个变量，用来存放从游标中提取的数据
	-- 特别注意这里的名字不能与由游标中使用的列明相同，否则得到的数据都是NULL
	DECLARE	iRecordNumber INT ;                         /* 记录个数 */
	DECLARE s_ORG_ID VARCHAR (100) DEFAULT NULL;       /* 管理机构ID */
    DECLARE s_ORG_CODE VARCHAR (100) DEFAULT NULL;       /* 管理机构Code */
    DECLARE s_DATE DATETIME DEFAULT NOW();       		/* 需要统计的月份 */
   	DECLARE v_i int unsigned DEFAULT 1;
 
    /* 根据传入的管理机构ID 进行统计 */
    -- 声明游标对应的 SQL 语句
    DECLARE cur_1 CURSOR FOR
          SELECT  ID,org_code  FROM sys_org  ORDER BY ID asc ;
   -- 在游标循环到最后会将 done 设置为 1
	DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;

    /* 打开游标 */
    open cur_1;
	REPEAT
		FETCH cur_1 INTO  s_ORG_ID,s_ORG_CODE;
		if not done then
		 	set s_ORG_CODE = CONCAT(s_ORG_CODE,'%');
		    call Total_Month_Sales_Amount_One(s_ORG_ID,s_ORG_CODE, s_DATE);
		 	WHILE v_i < 3 DO
		    	SET s_DATE = DATE_SUB(s_DATE, INTERVAL 1 MONTH);
		        call Total_Month_Sales_Amount_One(s_ORG_ID,s_ORG_CODE, s_DATE);
		        SET v_i = v_i+1;
		    END WHILE;
        	SET v_i = 1;
			SET s_DATE = NOW();
		end if;
 	UNTIL done END REPEAT;
   -- 关闭游标
   CLOSE cur_1;
	   -- SELECT ' 所有管理机构售电金额 统计完毕！！'; /* 调试输出语句 */
END$$

DELIMITER ;

DROP EVENT IF EXISTS calc_month_sales_amount;
CREATE EVENT calc_month_sales_amount ON SCHEDULE EVERY 1 DAY 
STARTS '2019-10-01 06:00:00' 
ON COMPLETION NOT PRESERVE ENABLE 
DO CALL Total_Month_Sales_Amount_All;

delete from dict_dataitem_group_map where dataitem_id like '2.36.26.0.%' and group_id = '1002005';
delete from dict_dataitem where id like '2.36.26.0.%';

insert into dict_dataitem values('2.36.26.0.0', 100, 'Data initialization', '7#0.0.99.98.0.255#2#0', null, 'R', 1, null, null);
insert into dict_dataitem values('2.36.26.0.1', 100, 'Soft Version change', '7#0.0.99.98.0.255#2#1', null, 'R', 1, null, null);

insert into dict_dataitem values('2.36.26.0.4', 100, 'Terminal Power Failure', '7#0.0.99.98.0.255#2#4', null, 'R', 1, null, null);
insert into dict_dataitem values('2.36.26.0.5', 100, 'Terminal Power on', '7#0.0.99.98.0.255#2#5', null, 'R', 1, null, null);

insert into dict_dataitem values('2.36.26.0.6', 100, 'GPRS Module Pullout', '7#0.0.99.98.0.255#2#6', null, 'R', 1, null, null);
insert into dict_dataitem values('2.36.26.0.7', 100, 'PLC Module Pullout', '7#0.0.99.98.0.255#2#7', null, 'R', 1, null, null);

insert into dict_dataitem values('2.36.26.0.9', 100, 'Terminal time', '7#0.0.99.98.0.255#2#9', null, 'R', 1, null, null);

insert into dict_dataitem values('2.36.26.0.16', 100, 'Terminal Cover opened', '7#0.0.99.98.0.255#2#16', null, 'R', 1, null, null);
insert into dict_dataitem values('2.36.26.0.17', 100, 'Terminal Cover closed', '7#0.0.99.98.0.255#2#17', null, 'R', 1, null, null);

insert into dict_dataitem values('2.36.26.0.19', 100, 'A Phase Miss', '7#0.0.99.98.0.255#2#19', null, 'R', 1, null, null);
insert into dict_dataitem values('2.36.26.0.20', 100, 'B Phase Miss', '7#0.0.99.98.0.255#2#20', null, 'R', 1, null, null);
insert into dict_dataitem values('2.36.26.0.21', 100, 'C Phase Miss', '7#0.0.99.98.0.255#2#21', null, 'R', 1, null, null);

insert into dict_dataitem values('2.36.26.0.22', 100, 'A Phase Lower', '7#0.0.99.98.0.255#2#22', null, 'R', 1, null, null);
insert into dict_dataitem values('2.36.26.0.23', 100, 'B Phase Lower', '7#0.0.99.98.0.255#2#23', null, 'R', 1, null, null);
insert into dict_dataitem values('2.36.26.0.24', 100, 'C Phase Lower', '7#0.0.99.98.0.255#2#24', null, 'R', 1, null, null);

insert into dict_dataitem values('2.36.26.0.25', 100, 'A Phase High', '7#0.0.99.98.0.255#2#25', null, 'R', 1, null, null);
insert into dict_dataitem values('2.36.26.0.26', 100, 'B Phase High', '7#0.0.99.98.0.255#2#26', null, 'R', 1, null, null);
insert into dict_dataitem values('2.36.26.0.27', 100, 'C Phase High', '7#0.0.99.98.0.255#2#27', null, 'R', 1, null, null);

insert into dict_dataitem values('2.36.26.0.28', 100, 'CPU Temperature High', '7#0.0.99.98.0.255#2#28', null, 'R', 1, null, null);
insert into dict_dataitem values('2.36.26.0.29', 100, 'Terminal Top Cover opened', '7#0.0.99.98.0.255#2#29', null, 'R', 1, null, null);
insert into dict_dataitem values('2.36.26.0.30', 100, 'Terminal Top Cover closed', '7#0.0.99.98.0.255#2#30', null, 'R', 1, null, null);

insert into dict_dataitem_group_map values('1002005', '2.36.26.0.0', 0);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.1', 1);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.4', 4);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.5', 5);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.6', 6);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.7', 7);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.9', 9);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.16', 16);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.17', 17);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.19', 19);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.20', 20);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.21', 21);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.22', 22);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.23', 23);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.24', 24);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.25', 25);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.26', 26);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.27', 27);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.28', 28);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.29', 29);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.30', 30);

-- 文件MD5变更，该位置开始需手动执行 2019-12-18 19:18
alter table `asset_customer` modify column `NAME` varchar(200) DEFAULT NULL ;
ALTER TABLE `ppm_vending_station` ADD COLUMN `CMS_ID`  varchar(100) NULL AFTER `VERSION_OPTIMISTIC_LOCKING`;

-- 文件MD5变更，该位置开始需手动执行 2019-12-21 15:18
alter table ppm_vend_historical_info add column CENCOBRO_BANK decimal(10,0) default null;
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1002', 'SOCIÉTÉ GÉNÉRALE SOCIAL SECURITY BANK', 'SOCIÉTÉ GÉNÉRALE SOCIAL SECURITY BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1003', 'STANDARD CHARTERED BANK GHANA LIMITED', 'STANDARD CHARTERED BANK GHANA LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1004', 'BARCLAYS BANK (GHANA) LIMITED', 'BARCLAYS BANK (GHANA) LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1005', 'Ghana Commercial Bank', 'Ghana Commercial Bank', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1007', 'PRUDENTIAL BANK LIMITED', 'PRUDENTIAL BANK LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1009', 'ECOBANK GHANA LIMITED', 'ECOBANK GHANA LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1011', 'AGRICULTURAL DEVELOPMENT BANK OF GHANA', 'AGRICULTURAL DEVELOPMENT BANK OF GHANA', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1013', 'CAL BANK LIMITED', 'CAL BANK LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1014', 'STANBIC BANK (GHANA) LIMITED', 'STANBIC BANK (GHANA) LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1017', 'HFC BANK LIMITED', 'HFC BANK LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1018', 'GUARANTY TRUST BANK (GHANA) LIMITED', 'GUARANTY TRUST BANK (GHANA) LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1019', 'ZENITH BANK (GHANA) LIMITED', 'ZENITH BANK (GHANA) LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1021', 'BANK OF AFRICA (GHANA)', 'BANK OF AFRICA (GHANA)', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1022', 'UNIBANK GHANA LIMITED', 'UNIBANK GHANA LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1023', 'UNIVERSAL MERCHANT BANK', 'UNIVERSAL MERCHANT BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1024', 'FIDELITY BANK GHANA LIMITED', 'FIDELITY BANK GHANA LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1025', 'UNITED BANK FOR AFRICA (GHANA) LIMITED', 'UNITED BANK FOR AFRICA (GHANA) LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1026', 'UT BANK LIMITED', 'UT BANK LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1027', 'FIRST ATLANTIC BANK LIMITED', 'FIRST ATLANTIC BANK LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1028', 'ENERGY BANK (GHANA) LIMITED', 'ENERGY BANK (GHANA) LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1029', 'SAHEL SAHARA', 'SAHEL SAHARA', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1034', 'GHANA INTER BANK-HEAD OFFICE, UK', 'GHANA INTER BANK-HEAD OFFICE, UK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1080', 'ACCESS BANK (GHANA) LIMITED', 'ACCESS BANK (GHANA) LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1090', 'FIRST CAPITAL PLUS (FCP) BANK', 'FIRST CAPITAL PLUS (FCP) BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3171', 'ADANSI RURAL BANK', 'ADANSI RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3172', 'AHANTAMAN RURAL BANK-ABURA', 'AHANTAMAN RURAL BANK-ABURA', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3173', 'ASANTE AKIM RURAL BANK JUANSA', 'ASANTE AKIM RURAL BANK JUANSA', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3174', 'ASAWINSO RURAL BANK', 'ASAWINSO RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3175', 'ATWIMA RURAL BANK', 'ATWIMA RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3176', 'DANGBE RURAL BANK', 'DANGBE RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3177', 'JUABENG RURAL BANK', 'JUABENG RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3178', 'KUMAWUMAN RURAL BANK', 'KUMAWUMAN RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3179', 'KWAEBIBIREM RURAL BANK', 'KWAEBIBIREM RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3180', 'KWANWUMA RURAL BANK', 'KWANWUMA RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3181', 'MPONUA RURAL BANK', 'MPONUA RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3182', 'MUMUADU RURAL BANK', 'MUMUADU RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3183', 'NZEMA MANLE RURAL BANK', 'NZEMA MANLE RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3184', 'ODOTOBRI/RURAL BANK', 'ODOTOBRI/RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3185', 'OTUASEKAN RURAL BANK', 'OTUASEKAN RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3186', 'SEKYERE RURAL BANK', 'SEKYERE RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3187', 'UPPER AMENFI RURAL BANK', 'UPPER AMENFI RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3188', 'UPPER MANYA KROBO RURAL BANK', 'UPPER MANYA KROBO RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3189', 'ROYAL BANK', 'ROYAL BANK', NULL);

-- 重建PPM_CMS_PREP_TRANS_EXT, modified at 2020.1.7 15:46
drop table IF EXISTS ppm_cms_prep_trans_ext;
CREATE TABLE ppm_cms_prep_trans_ext (
  TS date NOT NULL COMMENT 'Timestamp of the transaction group 交易组时间戳',
  TRANSACTION_ID varchar(32) NOT NULL COMMENT 'The ID of the transaction 交易ID',
  NUM_APA varchar(32) NOT NULL COMMENT 'Meter serial number 电表序列号',
  CO_MARCA varchar(32) NOT NULL COMMENT 'Meter manufacturer code 电表制造代码',
  CUST_NAME varchar(64) NOT NULL COMMENT 'Name of the customer (Surname, Names) 用户名（姓，名）',
  CUSTOMER_NUMBER varchar(32) NOT NULL COMMENT 'Consumer number用户数',
  SERVICE_POINT_NO varchar(32) NOT NULL COMMENT 'Service Point number 服务点数',
  VENDOR_ID varchar(32) DEFAULT NULL COMMENT 'Vendor id the transaction(s) took place at交易发生所在供应商ID',
  RECPT_NO varchar(64) NOT NULL COMMENT '组交易使用的小票数。若无有效的RECPT_NO, 组合CO_CONCEPTO 及 TRANSACTION_ID',
  TOKEN_NO varchar(64) DEFAULT NULL COMMENT 'Token number Token 数',
  PMETHOD varchar(32) DEFAULT NULL COMMENT 'Payment method 支付方式',
  CO_CONCEPTO varchar(32) NOT NULL COMMENT '概念代码（债务/信用/消耗/分期）',
  CSMO_FACT decimal(12,4) DEFAULT NULL COMMENT '资源交易下发的单元总计(KWH)。不接受负值',
  CYCLE decimal(2,0) DEFAULT NULL COMMENT 'Cycle number for the consumption 消耗循环数',
  CYCLE_DATE date DEFAULT NULL COMMENT 'End date of the cycle 循环结束日期',
  IMP_CONCEPTO decimal(15,4) DEFAULT NULL COMMENT 'Amount of the Concept. 概念金额',
  DEBT_REF_NO varchar(32) DEFAULT NULL COMMENT '债务唯一参考号',
  COD_UNICOM decimal(10,0) DEFAULT NULL COMMENT '供应商商务处',
  OPERATOR_NAME varchar(64) DEFAULT NULL COMMENT '销售token的供应商',
  CO_SISTEMA varchar(10) NOT NULL COMMENT '预付费系统创始代码',
  NUM_CHEQUE varchar(32) DEFAULT NULL COMMENT '支票号',
  COD_CENCOBRO_BANK decimal(10,0) DEFAULT '0' COMMENT '银行商业代码',
  EXPORTED decimal(2,0) NOT NULL DEFAULT '0' COMMENT 'Processed status. [0 – Not processed], [1 –  Processed]处理状态',
  EXPORTED_TS date DEFAULT NULL COMMENT '日期记录已处理',
  IS_STORE varchar(1) NOT NULL DEFAULT '0' COMMENT '是否已经存储 0-为存储  1-已存储',
  KEY index_cms_prep_trans_ext (TRANSACTION_ID)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='CMS接口交易记录表';

-- 文件MD5变更，该位置开始需手动执行 2019-12-24 15:18
ALTER TABLE PPM_VEND_CUSTOMER_DEBT ADD COLUMN `DEBT_SOURCE` varchar(20) comment '债务来源：CMS, PPM';
ALTER TABLE ppm_vend_free_token_manage ADD COLUMN `CREATE_DATE` datetime default CURRENT_TIMESTAMP comment '创建时间';

INSERT INTO ppm_dict_operation (ID, OPERATIONNAME, FUNCTION_ID, OPERATIONURL, ISHIDE, DESCRIPTION) VALUES ('2005001', 'Recharge', '2005', '/vendFreeTokenManageController/saveVendFreeTokenInfo.do', '1', 'Free Token Recharge');
INSERT INTO ppm_dict_operation (ID, OPERATIONNAME, FUNCTION_ID, OPERATIONURL, ISHIDE, DESCRIPTION) VALUES ('2005002', 'Print', '2005', '/vendFreeTokenManageController/print.do', '1', 'Print Tiket Again');
INSERT INTO ppm_dict_operation (ID, OPERATIONNAME, FUNCTION_ID, OPERATIONURL, ISHIDE, DESCRIPTION) VALUES ('2005003', 'Send Token', '2005', 'sendFreeRechargeToken', '1', 'Send Free Recharge Token');
INSERT INTO ppm_dict_operation (ID, OPERATIONNAME, FUNCTION_ID, OPERATIONURL, ISHIDE, DESCRIPTION) VALUES ('2005004', 'Send Token & Print', '2005', 'issuedFreeTokenAndPrint', '1', 'Send Free Token And Print');
INSERT INTO ppm_dict_operation (ID, OPERATIONNAME, FUNCTION_ID, OPERATIONURL, ISHIDE, DESCRIPTION) VALUES ('2005005', 'Print Button', '2005', 'printFreeTokenTicket', '1', 'Print Free Token Ticket');

INSERT INTO ppm_sys_menu (ID, UTILITY_IDS, MENULEVEL, MENUNAME, MENUORDER, FUNCTIONURL, PARENTMENUID, MENU_INTRO, FUNCTIONID, HIDE_TAB) VALUES ('1e858f11beda11e79bb968f728c52005', '1', '2', 'Free Token', '5', 'vendFreeTokenManageController/vendFreeToken.do', '1e858f11beda11e79bb968f728c50002', NULL, '2005', NULL);
INSERT INTO ppm_dict_function (ID, FUNCTIONNAME, FUNCTIONURL, FUNCTION_INTRO) VALUES ('2005', 'Free Token', 'vendFreeTokenManageController/vendFreeToken.do', 'Recharge');

-- 文件MD5变更，该位置开始需手动执行 2019-12-30 15:18
ALTER TABLE ppm_vend_historical_info ADD COLUMN `POWER_TOTAL` decimal(20,4) comment '本次充值总耗电量';
ALTER TABLE ppm_vend_historical_info ADD COLUMN `POWER_LASTMONTH` decimal(20,4) comment '本次充值上月耗电量';

-- 文件MD5变更，该位置开始需手动执行 2019-01-13 17:18
INSERT INTO PPM_SYS_MENU(ID, UTILITY_IDS, MENULEVEL, MENUNAME, MENUORDER, FUNCTIONURL, PARENTMENUID, MENU_INTRO, FUNCTIONID, HIDE_TAB) VALUES ('1e458f11beda11e79bb968f728c50011', '29018328bd4011e79bb968f728c516f9', '2', 'Energy Sales Breakdown', '11', 'uReportController/energySalesBreakdown.do', '1e858f11beda11e79bb968f728c50006', NULL, NULL, NULL);

-- 文件MD5变更，该位置开始需手动执行 2019-01-16 10:18
INSERT INTO ppm_sys_menu (ID, UTILITY_IDS, MENULEVEL, MENUNAME, MENUORDER, FUNCTIONURL, PARENTMENUID, MENU_INTRO, FUNCTIONID, HIDE_TAB) VALUES ('1c458f11beda11e79bb968f728c50a12', '1', '2', 'Vending Correction Report', '12', 'uReportController/vendingCorrectionReport.do', '1e858f11beda11e79bb968f728c50006', NULL, NULL, NULL);

-- 新增报表菜单，售电站充值小票增加银行字段，文件MD5变更，该位置开始需手动执行 2020-02-02 19:18
ALTER TABLE PPM_VEND_STATION_RECHARGE ADD COLUMN CENCOBRO_BANK decimal(10,0);

INSERT INTO ppm_sys_menu (ID, UTILITY_IDS, MENULEVEL, MENUNAME, MENUORDER, FUNCTIONURL, PARENTMENUID, MENU_INTRO, FUNCTIONID, HIDE_TAB) VALUES ('1c458f11beda11e79bb968f728c50010', '1', '2', 'Vending Station Recharge Report', '8', 'uReportController/stationRechargeReport.do', '1e858f11beda11e79bb968f728c50006', NULL, NULL, NULL);
INSERT INTO ppm_sys_menu (ID, UTILITY_IDS, MENULEVEL, MENUNAME, MENUORDER, FUNCTIONURL, PARENTMENUID, MENU_INTRO, FUNCTIONID, HIDE_TAB) VALUES ('1c458f11beda11e79bb968f728c50011', '1', '2', 'Customer Billing Data Report', '9', 'uReportController/customerBillingDataReport.do', '1e858f11beda11e79bb968f728c50006', NULL, NULL, NULL);

-- 新增预收取库表，文件MD5变更，该位置开始需手动执行 2020-02-06 20:16
CREATE TABLE PPM_VEND_HISTORICAL_DETAIL 
(	
	ID VARCHAR(32), 
	CUSTOMER_ID VARCHAR(32),     
	METER_ID VARCHAR(32),
	ESTIMATION_SALES_ID VARCHAR(32),   
	SUPPLYMENT_SALES_ID VARCHAR(32),	
	SALES_DATE DATETIME COMMENT '标注该月份客户是否收取过税费等费用，时间格式化：YYYY-MM-01 00:00:00' NOT NULL,
	DEBT_AMOUNT DECIMAL(10,4) COMMENT '该月份收取的债务费用' DEFAULT '0.0000', 
	TAX_AMOUNT DECIMAL(10,4) COMMENT '该月份收取的税费等费用' DEFAULT '0.0000', 
	OTHER_TOTAL_AMOUNT DECIMAL(10,4) COMMENT '该月份收取的其它费用' DEFAULT '0.0000', 
	SALES_STATUS DECIMAL(2,0) COMMENT '该月份费用收取状态,0:未收取任何费用,1:已预收取部分费用,2:待预收取部分费用,3:已收取全部费用,4:待收取全部费用' DEFAULT NULL,
	UPDATE_DATE DATETIME DEFAULT NULL, 
	SAVE_DB_DATE DATETIME DEFAULT CURRENT_TIMESTAMP,
	PRIMARY KEY (CUSTOMER_ID,METER_ID,SALES_DATE)
);

	ALTER TABLE PPM_DATA_PREPAID_PROGRESS ADD COLUMN LAST_ESTIMATION_DATE DATETIME;
	ALTER TABLE ppm_vend_historical_info ADD COLUMN LAST_ESTIMATION_DATE DATETIME;	
	
INSERT INTO `ppm_sys_menu` (`ID`, `UTILITY_IDS`, `MENULEVEL`, `MENUNAME`, `MENUORDER`, `FUNCTIONURL`, `PARENTMENUID`, `MENU_INTRO`, `FUNCTIONID`, `HIDE_TAB`)
VALUES ('13458f11beda11e79bb968f728c50a13', '1', '2', 'Installed Single Meters Report', '13', 'uReportController/installedSingleMeterReport.do', '1e858f11beda11e79bb968f728c50006', NULL, NULL, NULL);


INSERT INTO `ppm_sys_menu` (`ID`, `UTILITY_IDS`, `MENULEVEL`, `MENUNAME`, `MENUORDER`, `FUNCTIONURL`, `PARENTMENUID`, `MENU_INTRO`, `FUNCTIONID`, `HIDE_TAB`)
VALUES ('14458f11beda11e79bb968f728c50a14', '1', '2', 'Installed Three Meters Report', '14', 'uReportController/installedThreeMeterReport.do', '1e858f11beda11e79bb968f728c50006', NULL, NULL, NULL);

ALTER TABLE DATA_INTEGRITY MODIFY COLUMN integrity DECIMAL(12,2);
ALTER TABLE DATA_INTEGRITY_METER MODIFY COLUMN integrity DECIMAL(12,2);