/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysOrg{ } 
 * 
 * 摘    要： 组织机构表
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-10-27 08:42:10
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.system;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.json.ValidForm;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.tag.vo.JqTreeUtil;
import com.clou.esp.hes.app.web.core.tag.vo.ZtreeNode;
import com.clou.esp.hes.app.web.core.tag.vo.ZtreeUtil;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.enums.system.EnumUserType;
import com.clou.esp.hes.app.web.model.asset.AssetCalcObj;
import com.clou.esp.hes.app.web.model.asset.AssetCommunicator;
import com.clou.esp.hes.app.web.model.asset.AssetLine;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.asset.AssetTransformer;
import com.clou.esp.hes.app.web.model.report.AssetReportTemplate;
import com.clou.esp.hes.app.web.model.report.DictReport;
import com.clou.esp.hes.app.web.model.system.SysOrg;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.asset.AssetCommunicatorService;
import com.clou.esp.hes.app.web.service.asset.AssetLineManagementService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.asset.AssetTransformerService;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.report.AssetReportTemplateService;
import com.clou.esp.hes.app.web.service.system.SysOrgService;
import com.clou.esp.hes.app.web.service.system.SysUserService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.oConvertUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;

/**
 * <AUTHOR>
 * @时间：2017-10-27 08:42:10
 * @描述：组织机构表类
 */
@Controller
@RequestMapping("/sysOrgController")
public class SysOrgController extends BaseController{

 	@Resource
    private SysOrgService sysOrgService;
 	@Resource
    private AssetLineManagementService assetLineManagementService;
 	@Resource
    private AssetTransformerService assetTransformerService;
 
 	@Resource
    private AssetCommunicatorService assetCommunicatorService;
 	@Resource
    private AssetMeterService assetMeterService;
 	@Resource
    private SysUserService sysUserService;
 	
 	@Resource
    private DataUserLogService dataUserLogService;
 	@Resource
    private AssetReportTemplateService assetReportTemplateService;
	/**
	 * 跳转到组织机构表列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/system/sysOrgList");
    }

	/**
	 * 跳转到组织机构表新增界面
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "toAddSysOrg")
	public ModelAndView sysOrg(SysOrg sysOrg,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(sysOrg.getId())){
			try {
                sysOrg = sysOrgService.getEntity(sysOrg.getId());
            }
            catch (Exception e) {
                e.printStackTrace();
            }
			model.addAttribute("sysOrg", sysOrg);
		}
		return new ModelAndView("/system/sysOrg");
	}

	/**
	 * 跳转到组织机构表新增界面
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "sysOrgBase")
	public ModelAndView sysOrgBaseEdit(SysOrg sysOrg,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(sysOrg.getId())){
			try {
                sysOrg = sysOrgService.getEntity(sysOrg.getId());
            }
            catch (Exception e) {
                e.printStackTrace();
            }
			model.addAttribute("orgTypeReplace", getOrgTypeReplace()); 
			model.addAttribute("sysOrg", sysOrg);
			return new ModelAndView("/system/sysOrgBase");
		}else {
			return null;
		}
		
	}

	/**
	 * 组织机构表查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
			j = sysOrgService.getForJqGrid(jqGridSearchTo);
			SysUser su = TokenManager.getToken();
			SysOrg entity = new SysOrg();
			entity.setParentOrgTid("0");
			SysOrg org = sysOrgService.getEntity(su.getOrgId());
			if(su.getUserType()!=EnumUserType.SUPER_USER.getIndex()){
				if(org!=null){
					entity.setOrgCode(org.getOrgCode());
					entity.setParentOrgTid(org.getParentOrgTid());
				}
			}
			List<SysOrg> sysOrglist = sysOrgService.getList(entity);
			if(su.getUserType()!=EnumUserType.SUPER_USER.getIndex()&&org!=null){
				this.getSysOrgTreeData(sysOrglist,org.getOrgCode());
			}else{
				this.getSysOrgTreeData(sysOrglist,null);
			}
			JqTreeUtil jtu = new JqTreeUtil();
			jtu.setFields(jqGridSearchTo.getField());
			jtu.setIdFieldName("id");
			jtu.setParentFieldName("parentOrgTid");
			jtu.setSubsetFieldName("sysOrgList");
			jtu.setExpandedFieldName("expanded");
			j.setRows(jtu.getTreeGridData(sysOrglist));
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
	/**
	 * 递归方法，查询组织机构数据
	 * @Description 
	 * @param list void
	 * <AUTHOR> 
	 * @Time 2018年3月22日 下午7:04:55
	 */
	public void getSysOrgTreeData(List<SysOrg> list,String orgCode){
		for (SysOrg sysOrg : list) {
			SysOrg entity = new SysOrg();
            entity.setParentOrgTid(sysOrg.getId());
            if(StringUtil.isNotEmpty(orgCode)){
            	entity.setOrgCode(orgCode);
            }
            sysOrg.setExpanded(true);
            //查询是否存在子节点
            List<SysOrg> sysOrglist = sysOrgService.getList(entity);
            if(sysOrglist.size() > 0){
            	//设置子节点等级
            	for (int i = 0; i < sysOrglist.size(); i++) {
					sysOrglist.get(i).setLevel(sysOrg.getLevel() + 1);
				}
            	getSysOrgTreeData(sysOrglist,orgCode);
            	sysOrg.setSysOrgList(sysOrglist);
            }
		}
	}
    
    /**
     * 删除组织机构表信息
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(SysOrg sysOrg, HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su = TokenManager.getToken();
            //判断是否还存在下属组织机构，如果存在则不能删除
            SysOrg deleteObj = new SysOrg();
            deleteObj.setParentOrgTid(sysOrg.getId());
            List<SysOrg> orglist = sysOrgService.getList(deleteObj);	//是否存在组织机构子集
            if(orglist.size() > 0){
            	j.setSuccess(false);
                j.setMsg(MutiLangUtil.doMutiLang("sysOrganizationList.unableDel_org"));
                return j;
            }
            //查询集中器是否挂靠组织机构子集
            AssetCommunicator delComm = new AssetCommunicator();
            delComm.setOrgId(sysOrg.getId());
            Long commNum = assetCommunicatorService.getCount(delComm);
            if(commNum > 0){
            	j.setSuccess(false);
                j.setMsg(MutiLangUtil.doMutiLang("sysOrganizationList.unableDel_comm"));
                return j;
            }
            //查询电表是否挂靠组织机构子集
            AssetMeter delMeter = new AssetMeter();
            delMeter.setOrgId(sysOrg.getId());
            Long meterNum = assetMeterService.getCount(delMeter);	
            if(meterNum > 0){
            	j.setSuccess(false);
                j.setMsg(MutiLangUtil.doMutiLang("sysOrganizationList.unableDel_meter"));
                return j;
            }
            //查询用户是否挂靠组织机构子集
            SysUser delUser = new SysUser();
            delUser.setOrgId(sysOrg.getId());
            List<SysUser> userList = sysUserService.getList(delUser);	
            if(userList.size() > 0){
            	j.setSuccess(false);
                j.setMsg(MutiLangUtil.doMutiLang("sysOrganizationList.unableDel_user"));
                return j;
            }
            SysOrg entity = sysOrgService.getEntity(sysOrg.getId());
        	if(sysOrgService.deleteById(sysOrg.getId())>0){
        		//添加操作日志
                dataUserLogService.insertDataUserLog(su.getId(), 
                		"Permission", "Delete Organization", "Delete Organization (Name=" + entity.getName() + ")");
        		j.setMsg(MutiLangUtil.doMutiLang("system.delSuccess"));
        	}else{
        		j.setSuccess(false);
        		j.setMsg(MutiLangUtil.doMutiLang("system.delFail")); 
        	}
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
    
    /**
     * 保存组织机构表信息
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })SysOrg sysOrg,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        SysOrg t = new  SysOrg();
        try {
        	SysUser su = TokenManager.getToken();
	        if(StringUtil.isNotEmpty(sysOrg.getId())){
	        	t = sysOrgService.getEntity(sysOrg.getId());
				MyBeanUtils.copyBeanNotNull2Bean(sysOrg, t);
				sysOrgService.update(t);
				//添加操作日志
                dataUserLogService.insertDataUserLog(su.getId(), 
                		"Permission", "Edit Organization", "Edit Organization (Name=" + t.getName() + ")");
				j.setMsg(MutiLangUtil.doMutiLang("system.updateSucc"));
				j.put("sysOrg", sysOrg);
			}else{
				//新增最高级父级机构
				if(!StringUtil.isNotEmpty(sysOrg.getParentOrgTid())){
					sysOrg.setParentOrgTid("0");
					List<SysOrg> list = sysOrgService.getListByparentOrgTid(sysOrg);
					Integer code = 0;
					if(list.size() > 0){
						code = Integer.parseInt(list.get(0).getOrgCode());
					}
					DecimalFormat decimalFormat = new DecimalFormat("0000");
					sysOrg.setOrgCode(decimalFormat.format(code + 1));
				}else{
					//新增子级组织机构
					SysOrg parentOrg = sysOrgService.getEntity(sysOrg.getParentOrgTid());
					List<SysOrg> list = sysOrgService.getListByparentOrgTid(sysOrg);
					Integer code = 0;
					if(list.size() > 0){
						code = Integer.parseInt(list.get(0).getOrgCode()
								.substring(list.get(0).getOrgCode().length() - 4, list.get(0).getOrgCode().length()));
					}
					DecimalFormat decimalFormat = new DecimalFormat("0000");
					sysOrg.setOrgCode(parentOrg.getOrgCode() + decimalFormat.format(code + 1));
					/*
					 * 只允许添加四级组织机构
					 */
					if(sysOrg.getOrgCode().length() > 16){
						j.setSuccess(false);
			            j.setMsg(MutiLangUtil.doMutiLang("sysOrganizationList.admitFourLevelOrg"));
			            return j;
					}
				}
				sysOrg.setUtilityId("1");
				sysOrgService.save(sysOrg);
				//添加操作日志
                dataUserLogService.insertDataUserLog(su.getId(), 
                		"Permission", "Add Organization", "Add Organization (Name=" + sysOrg.getName() + ")");
				j.setMsg(MutiLangUtil.doMutiLang("system.addSucc"));
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
	
    /**
     * 校验org name唯一性
     * @param id5
     * @return
     */
    @RequestMapping(value = "checkOrgName")
    @ResponseBody
    public ValidForm checkOrgName(HttpServletRequest request,String id) {
    	ValidForm v = new ValidForm();
		String orgName = oConvertUtils.getString(request.getParameter("param"));
		SysOrg sysOrg = new SysOrg();
		sysOrg.setName(orgName);
		List<SysOrg> list = sysOrgService.getList(sysOrg);
		if(StringUtils.isNotEmpty(id)) {//如果有id
			boolean flag=false;
			for(SysOrg org:list) {
				if(!id.equals(org.getId())) {
					flag=true;
				}
			}
			if(flag) {//不同id相同名字的报异常
				if (list.size() > 0) {
					v.setInfo(MutiLangUtil.doMutiLang("system.nameExist"));
					v.setStatus("n");
				}
			}
		}else {
			if (list.size() > 0) {
				v.setInfo(MutiLangUtil.doMutiLang("system.nameExist"));
				v.setStatus("n");
			}
		}
		
		return v;
    }
    /**
	 * 获取organization组织机构的树形结构
	 * @param searchName
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getOrganizationZtree")
	@ResponseBody
	public AjaxJson getOrganizationZtree(String orgSearchInputId, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		SysOrg entity = new SysOrg();
		try {
			entity.setParentOrgTid("0");
			SysUser su = TokenManager.getToken();
			SysOrg org = sysOrgService.getEntity(su.getOrgId());
		//	if(su.getUserType()!=EnumUserType.SUPER_USER.getIndex()){
				if(org!=null){
					entity.setOrgCode(org.getOrgCode());
					entity.setParentOrgTid(org.getParentOrgTid());
				}
			//}
			List<SysOrg> sysOrglist = sysOrgService.getList(entity);
			if(org!=null){
				//if(su.getUserType()!=EnumUserType.SUPER_USER.getIndex()&&org!=null){
				this.getSysOrgZtreeData(sysOrglist,org.getOrgCode());
			}else{
				this.getSysOrgZtreeData(sysOrglist,null);
			}
			ZtreeUtil jtu = new ZtreeUtil();
			jtu.setIdFname("id");
			jtu.setNameFname("name");
			jtu.setpIdFname("parentOrgTid");
			jtu.setSubsetFieldName("sysOrgList");
			jtu.setOpenFname("expanded");
			j.setObj(jtu.getZtreeNodeData(sysOrglist));
			System.out.println(" json tree --> " + j.toString());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return j;
	}
	
	public AjaxJson getChannelZtree(HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		List<Map<String, Object>> list = new ArrayList<>();
		SysOrg entity = new SysOrg();
	
			entity.setParentOrgTid("0");
			SysUser su = TokenManager.getToken();
			SysOrg org = sysOrgService.getEntity(su.getOrgId());
		
			if(org!=null){
				entity.setOrgCode(org.getOrgCode());
				entity.setParentOrgTid(org.getParentOrgTid());
			}
			List<SysOrg> sysOrglist = sysOrgService.getList(entity);
		for(SysOrg g:sysOrglist){
			Map<String, Object> m = new HashMap<String, Object>();
			m.put("id", g.getId());
			m.put("name", g.getName());
			m.put("parent", "");
			m.put("expanded", true);
			SysOrg sysOrg=new SysOrg();
			sysOrg.setParentOrgTid(g.getParentOrgTid());
			List<SysOrg> entityChildrenList = sysOrgService.getList(sysOrg);
			List<Map<String, Object>> listk = new ArrayList<>();
			for(SysOrg d:entityChildrenList){
				Map<String, Object> dm = new HashMap<String, Object>();
				dm.put("id", d.getId());
				dm.put("name", d.getName());
				dm.put("parent", g.getId());
				dm.put("expanded", false);
				listk.add(dm);
			}
			
			if(listk.size()>0){
			m.put("list", listk);
			list.add(m);
			}
			
		}
		ZtreeUtil jtu = new ZtreeUtil();
		jtu.setIdFname("id");
		jtu.setNameFname("name");
		jtu.setpIdFname("parent");
		jtu.setSubsetFieldName("list");
		jtu.setOpenFname("expanded");
		//jtu.seteUrl("functionUrl");
		j.setObj(jtu.getZtreeNodeData(list));
		return j;
	}
	
	
	/**
	 * 获取organization组织机构的树形结构 关联线路，变压器
	 * @param searchName
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getOrgLineTransZtree")
	@ResponseBody
	public AjaxJson getOrgLineTransZtree(String id, String pId, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		List<Map<String, Object>> list = new ArrayList<>();
		
		if(StringUtils.isEmpty(id) || StringUtils.isEmpty(pId) || "null".equals(pId)){
			initOrgTree(request, list , pId);
		}else{
			expandOrgTree(request, list ,id);
		}
		
		ZtreeUtil jtu = new ZtreeUtil();
		jtu.setIdFname("id");
		jtu.setNameFname("name");
		jtu.setpIdFname("parent");
		jtu.setSubsetFieldName("list");
		jtu.setOpenFname("expanded");
		jtu.setIconFname("iconFname");
		jtu.setIsParentFname("isParent");
		jtu.setIsAjaxingFname("isAjaxing");
		//jtu.seteUrl("functionUrl");
		j.setObj(jtu.getZtreeNodeData(list));
		return j;
	}
	
	/**
	 * 获取getReportTemplateZtree
	 * @param searchName
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getReportTemplateZtree")
	@ResponseBody
	public AjaxJson getReportTemplateZtree(String id, String pId, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		List<Map<String, Object>> list = new ArrayList<>();
		
		if(StringUtils.isEmpty(id) || StringUtils.isEmpty(pId) || "null".equals(pId)){
			initReportTemplateTree(request, list , pId);
		}else{
			expandReportTemplateTree(request, list ,id);
		}
	
	ZtreeUtil jtu = new ZtreeUtil();
	jtu.setIdFname("id");
	jtu.setNameFname("name");
	jtu.setpIdFname("parent");
	jtu.setSubsetFieldName("list");
	jtu.setOpenFname("expanded");
	jtu.setIconFname("iconFname");
	jtu.setIsParentFname("isParent");
	jtu.setIsAjaxingFname("isAjaxing");
	//jtu.seteUrl("functionUrl");
	j.setObj(jtu.getZtreeNodeData(list));
	return j;
}
	
	@RequestMapping(value = "searchOrgLineTransZtree")
	@ResponseBody
	public AjaxJson searchOrgLineTransZtree( String searchType, String id,HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		List<Map<String, Object>> list = new ArrayList<>();
		
		searchOrgTree(request,list,searchType,id);
		
		ZtreeUtil jtu = new ZtreeUtil();
		jtu.setIdFname("id");
		jtu.setNameFname("name");
		jtu.setpIdFname("parent");
		jtu.setSubsetFieldName("list");
		jtu.setOpenFname("expanded");
		jtu.setIconFname("iconFname");
		jtu.setIsParentFname("isParent");
		jtu.setIsAjaxingFname("isAjaxing");
		//jtu.seteUrl("functionUrl");
		
		Map<String,Object> obj = new HashMap();
		obj.put("ztreeNode", jtu.getZtreeNodeData(list));
		obj.put("id",id);
		j.setObj(obj);
		return j;
	}
	
	
	private void expandReportTemplateTree(HttpServletRequest request,
			List<Map<String, Object>> list ,String id) {
		if(id.indexOf(":") > 0){
			//变压器
			String[] orgIds = id.split(":");
			if("DailyReport".equals(orgIds[1])){			
				//日报表自定义报表开始
				AssetReportTemplate assetReportTemplate = new AssetReportTemplate();
				assetReportTemplate.setOrgId(orgIds[0]);
				assetReportTemplate.setReportType(new BigDecimal(1));
				List<AssetReportTemplate> assetReportTemplateDailyList = assetReportTemplateService.getList(assetReportTemplate);
			
				
				for(AssetReportTemplate assetReportTemplate1 : assetReportTemplateDailyList){
					Map<String, Object> dm1 = new HashMap<String, Object>();
					dm1.put("id", assetReportTemplate1.getId());
					dm1.put("name", assetReportTemplate1.getReportName());
					dm1.put("parent", id);
					dm1.put("expanded", false);
					dm1.put("functionUrl", "dailyReport");
					dm1.put("iconFname", request.getContextPath() + "/theme/img/forms.png");
					list.add(dm1);
				}
			}else if("MonthlyReport".equals(orgIds[1])){
				
				//月报表自定义报表开始
				AssetReportTemplate assetReportTemplate = new AssetReportTemplate();
				assetReportTemplate.setOrgId(orgIds[0]);
				assetReportTemplate.setReportType(new BigDecimal(2));
				List<AssetReportTemplate> assetReportTemplateMonthlyList = assetReportTemplateService.getList(assetReportTemplate);

				for(AssetReportTemplate assetReportTemplate1 : assetReportTemplateMonthlyList){
					Map<String, Object> dm1 = new HashMap<String, Object>();
					dm1.put("id", assetReportTemplate1.getId());
					dm1.put("name", assetReportTemplate1.getReportName());
					dm1.put("parent", id);
					dm1.put("expanded", false);
					dm1.put("functionUrl", "monthlyReport");
					dm1.put("iconFname", request.getContextPath() + "/theme/img/forms.png");
					list.add(dm1);
				}
			}
			
		}else{
			//日报表自定义报表开始
			AssetReportTemplate assetReportTemplate = new AssetReportTemplate();
			assetReportTemplate.setOrgId(id);
			assetReportTemplate.setReportType(new BigDecimal(1));
			List<AssetReportTemplate> assetReportTemplateDailyList = assetReportTemplateService.getList(assetReportTemplate);
			//月报表自定义报表开始
			AssetReportTemplate assetReportTemplate1 = new AssetReportTemplate();
			assetReportTemplate1.setOrgId(id);
			assetReportTemplate1.setReportType(new BigDecimal(2));
			List<AssetReportTemplate> assetReportTemplateMonthlyList = assetReportTemplateService.getList(assetReportTemplate1);
			boolean isExistDailyReport = false;
			boolean isExistMonthlyReport = false;
			if(assetReportTemplateDailyList != null && assetReportTemplateDailyList.size() > 0){
				isExistDailyReport = true;
			}
			
			if(assetReportTemplateMonthlyList != null && assetReportTemplateMonthlyList.size() > 0){
				isExistMonthlyReport = true;
			}
			
			String dailyReportId = id+":DailyReport";
			String monthlyReportId =  id+":MonthlyReport";
			
			
			if(isExistDailyReport){
				Map<String, Object> dm = new HashMap<String, Object>();
				dm.put("id", dailyReportId);
				dm.put("name", MutiLangUtil.doMutiLang("index.dailyReport"));
				dm.put("parent", id);
				dm.put("expanded", false);
				dm.put("functionUrl", "dailyReportV");
				dm.put("iconFname", request.getContextPath() + "/theme/img/org-transformer.png");
				dm.put("isParent", true);
				list.add(dm);
			}
			
			if(isExistMonthlyReport){
				Map<String, Object> dm = new HashMap<String, Object>();
				dm.put("id", monthlyReportId);
				dm.put("name", MutiLangUtil.doMutiLang("index.monthlyReport"));
				dm.put("parent", id);
				dm.put("expanded", false);
				dm.put("functionUrl", "monthlyReportV");
				dm.put("iconFname", request.getContextPath() + "/theme/img/org-transformer.png");
				dm.put("isParent", true);
				list.add(dm);
			}
		}
	}
	private void expandOrgTree(HttpServletRequest request,
			List<Map<String, Object>> list ,String id) {
		if(id.indexOf(":") > 0){
			//变压器
			String[] orgIds = id.split(":");
			if("Transformer".equals(orgIds[1])){
				AssetTransformer assetTransformer = new AssetTransformer();
				assetTransformer.setOrgId(orgIds[0]);
				List<AssetTransformer> assetTransformerList = assetTransformerService.getList(assetTransformer);
				
				for(AssetTransformer assetTransformer1 : assetTransformerList){
					
					Map<String, Object> dm1 = new HashMap<String, Object>();
					dm1.put("id", assetTransformer1.getId());
					dm1.put("name", assetTransformer1.getName());
					dm1.put("parent", id);
					dm1.put("expanded", false);
					dm1.put("functionUrl", "transformer");
					dm1.put("iconFname",  request.getContextPath() + "/theme/img/transformer1.png");
					
					list.add(dm1);
				}
			}else if("Line".equals(orgIds[1])){
				AssetLine assetLine2 = new AssetLine();
				assetLine2.setOrgId(orgIds[0]);
				List<AssetLine> assetLineList1 = assetLineManagementService.getList(assetLine2);
				
				for(AssetLine assetLine1 : assetLineList1){
					Map<String, Object> dm1 = new HashMap<String, Object>();
					dm1.put("id", assetLine1.getId());
					dm1.put("name", assetLine1.getName());
					dm1.put("parent", id);
					dm1.put("expanded", false);
					dm1.put("functionUrl", "line");
					dm1.put("iconFname", request.getContextPath() + "/theme/img/lines1.png");
					list.add(dm1);
				}
			}
			
		}else{
			//org id 传入 查询line and transformer 确定 虚拟文件夹
			
			AssetLine assetLine2 = new AssetLine();
			assetLine2.setOrgId(id);
			List<AssetLine> assetLineList1 = assetLineManagementService.getList(assetLine2);
			//变压器
			AssetTransformer assetTransformer2 = new AssetTransformer();
			assetTransformer2.setOrgId(id);
			List<AssetTransformer> assetTransformerList1 = assetTransformerService.getList(assetTransformer2);
			
			AssetCalcObj assetCalcObj = new AssetCalcObj();
			assetCalcObj.setEntityId(id);
			assetCalcObj.setEntityType(5);
			List<AssetCalcObj> assetCalcObjList =assetLineManagementService.getListForCalObj(assetCalcObj);
			
			boolean isExistLine = false;
			boolean isExistTransformer = false;
			boolean isExistAssetCalcObj = false;
			
			if(assetLineList1 != null && assetLineList1.size() > 0){
				isExistLine = true;
			}
			
			if(assetTransformerList1 != null && assetTransformerList1.size() > 0){
				isExistTransformer = true;
			}
			
			if(assetCalcObjList != null && assetCalcObjList.size() > 0){
				isExistAssetCalcObj = true;
			}
			
			
			String virtualTransformerId =  id+":Transformer";
			String virtualLineId =  id+":Line";
			
			if(isExistLine){
					Map<String, Object> dm = new HashMap<String, Object>();
					dm.put("id", virtualLineId);
					dm.put("name", MutiLangUtil.doMutiLang("index.line"));
					dm.put("parent", id);
					dm.put("expanded", false);
					dm.put("functionUrl", "lineV");
					dm.put("iconFname", request.getContextPath() + "/theme/img/org-lines.png");
					dm.put("isParent", true);
					list.add(dm);
				
			}
			
			if(isExistTransformer){
				Map<String, Object> dm = new HashMap<String, Object>();
				dm.put("id", virtualTransformerId);
				dm.put("name", MutiLangUtil.doMutiLang("index.transformer"));
				dm.put("parent", id);
				dm.put("expanded", false);
				dm.put("functionUrl", "transformerV");
				dm.put("iconFname", request.getContextPath() + "/theme/img/org-transformer.png");
				dm.put("isParent", true);
				list.add(dm);
			}
			
			if(isExistAssetCalcObj){
				for(AssetCalcObj assetCalcObj1 : assetCalcObjList){
					Map<String, Object> dm = new HashMap<String, Object>();
					dm.put("id", assetCalcObj1.getId());
					dm.put("name", assetCalcObj1.getName());
					dm.put("parent", id);
					dm.put("expanded", false);
					dm.put("functionUrl", "calcObj");
					dm.put("iconFname", request.getContextPath() + "/theme/img/com-Object.png");
					dm.put("isParent", false);
					list.add(dm);
				}
			
			}

		}
	}
	
	private void searchOrgTree(HttpServletRequest request,
			List<Map<String, Object>> list ,String searchType,String id) {
		AssetLine assetLineNode = new AssetLine();
		AssetTransformer assetTransformerNode = new AssetTransformer();
		if(searchType.equals("Line")){
			assetLineNode.setId(id);
			assetLineNode = assetLineManagementService.get(assetLineNode);
		}else if(searchType.equals("Transformer")){
			assetTransformerNode.setId(id);
			assetTransformerNode = assetTransformerService.get(assetTransformerNode);
		}
		
		SysOrg entity = new SysOrg();
		String path = request.getContextPath();
		entity.setParentOrgTid("0");
		SysUser su = TokenManager.getToken();
		SysOrg org = sysOrgService.getEntity(su.getOrgId());
		
		if(org!=null){
			entity.setOrgCode(org.getOrgCode());
			entity.setParentOrgTid(org.getParentOrgTid());
		}
		List<SysOrg> sysOrglist = sysOrgService.getList(entity);
		for(SysOrg g:sysOrglist){
			Map<String, Object> m = new HashMap<String, Object>();
			m.put("id", g.getId());
			m.put("name", g.getName());
			m.put("parent", "");
			m.put("isParent", true);
			m.put("expanded", true);
			m.put("isAjaxing", true);
			m.put("functionUrl", "org");
			
			m.put("iconFname", path + "/theme/img/org.png");
			//组织机构
			SysOrg sysOrg=new SysOrg();
			sysOrg.setParentOrgTid(g.getId());
			List<SysOrg> entityChildrenList = sysOrgService.getList(sysOrg);
			//线路
			
			AssetLine assetLine = new AssetLine();
			assetLine.setOrgId(g.getId());
			List<AssetLine> assetLineList = assetLineManagementService.getList(assetLine);
			//变压器
			AssetTransformer assetTransformer = new AssetTransformer();
			assetTransformer.setOrgId(g.getId());
			List<AssetTransformer> assetTransformerList = assetTransformerService.getList(assetTransformer);
			
			AssetCalcObj assetCalcObj = new AssetCalcObj();
			assetCalcObj.setEntityId(g.getId());
			assetCalcObj.setEntityType(5);
			List<AssetCalcObj> assetCalcObjList =assetLineManagementService.getListForCalObj(assetCalcObj);

			List<Map<String, Object>> listk = new ArrayList<>();
			for(SysOrg d:entityChildrenList){
				Map<String, Object> dm = new HashMap<String, Object>();
				dm.put("id", d.getId());
				dm.put("name", d.getName());
				dm.put("parent", g.getId());
				dm.put("expanded", true);
				dm.put("functionUrl", "org");
				dm.put("iconFname", path + "/theme/img/org.png");
		
				AssetLine assetLine2 = new AssetLine();
				assetLine2.setOrgId(d.getId());
				List<AssetLine> assetLineList1 = assetLineManagementService.getList(assetLine2);
				//变压器
				AssetTransformer assetTransformer2 = new AssetTransformer();
				assetTransformer2.setOrgId(d.getId());
				List<AssetTransformer> assetTransformerList1 = assetTransformerService.getList(assetTransformer2);
				boolean isExistLineAndTransformer = false;
				
				AssetCalcObj assetCalcObj2 = new AssetCalcObj();
				assetCalcObj2.setEntityId(d.getId());
				assetCalcObj2.setEntityType(5);
				List<AssetCalcObj> assetCalcObjList1 =assetLineManagementService.getListForCalObj(assetCalcObj2);
				
				
				if(assetLineList1 != null && assetLineList1.size() > 0){
					isExistLineAndTransformer = true;
				}
				
				if(assetTransformerList1 != null && assetTransformerList1.size() > 0){
					isExistLineAndTransformer = true;
				}
				
				if(assetCalcObjList1 != null && assetCalcObjList1.size() > 0){
					isExistLineAndTransformer = true;
				}
				
				if(isExistLineAndTransformer){
					dm.put("isParent", true);
				}
				
				if(searchType.equals("Line")){
					// 搜索line begin
					if(assetLineNode != null){
						if(assetLineNode.getOrgId().equals(d.getId())){
							List<Map<String, Object>> listLine = new ArrayList<>();
							AssetLine assetLine3 = new AssetLine();
							assetLine3.setOrgId(d.getId());
							List<AssetLine> assetLineList3 = assetLineManagementService.getList(assetLine3);
							String virtualLineId =  d.getId()+":Line";
							if(assetLineList3 != null && assetLineList3.size() > 0){
								dm.put("expanded", true);
								dm.put("isAjaxing", true);
								Map<String, Object> dm1 = new HashMap<String, Object>();
								dm1.put("id", virtualLineId);
								dm1.put("name", MutiLangUtil.doMutiLang("index.line"));
								dm1.put("parent", d.getId());
								dm1.put("expanded", true);
								dm1.put("functionUrl", "lineV");
								dm1.put("iconFname", path + "/theme/img/org-lines.png");
								dm1.put("isParent", true);
								dm1.put("isAjaxing", true);
								
								List<Map<String, Object>> listLine4 = new ArrayList<>();
								for(AssetLine assetLine4 : assetLineList3){
									
										Map<String, Object> dm4 = new HashMap<String, Object>();
										dm4.put("id", assetLine4.getId());
										dm4.put("name", assetLine4.getName());
										dm4.put("parent", virtualLineId);
										dm4.put("expanded", false);
										dm4.put("functionUrl", "line");
										dm4.put("iconFname", request.getContextPath() + "/theme/img/lines1.png");
										listLine4.add(dm4);
								
								}
								if(listLine4.size()>0){
									dm1.put("list", listLine4);
								}
								
								listLine.add(dm1);
								
								if(assetTransformerList1 != null && assetTransformerList1.size() > 0){
										String virtualTransformerId =  d.getId()+":Transformer";
								
										Map<String, Object> dm2 = new HashMap<String, Object>();
										dm2.put("id", virtualTransformerId);
										dm2.put("name", MutiLangUtil.doMutiLang("index.transformer"));
										dm2.put("parent", d.getId());
										dm2.put("expanded", false);
										dm2.put("functionUrl", "transformerV");
										dm2.put("iconFname", path + "/theme/img/org-transformer.png");
										dm2.put("isParent", true);
										listLine.add(dm2);
									
								}
							}
							
							if(listLine.size()>0){
								dm.put("list", listLine);
							}
						}
					}
					// 搜索line end
				}else if(searchType.equals("Transformer")){
					
					if(assetTransformerNode != null){
						if(assetTransformerNode.getOrgId().equals(d.getId())){
					List<Map<String, Object>> listTransformer = new ArrayList<>();
					AssetTransformer assetTransformer3 = new AssetTransformer();
					assetTransformer3.setOrgId(d.getId());
					List<AssetTransformer> assetAssetTransformerList3 = assetTransformerService.getList(assetTransformer3);
					String virtualTransformerId =  d.getId()+":Transformer";
					if(assetAssetTransformerList3 != null && assetAssetTransformerList3.size() > 0){
						dm.put("expanded", true);
						dm.put("isAjaxing", true);
						Map<String, Object> dm1 = new HashMap<String, Object>();
						dm1.put("id", virtualTransformerId);
						dm1.put("name", MutiLangUtil.doMutiLang("index.transformer"));
						dm1.put("parent", d.getId());
						dm1.put("expanded", false);
						dm1.put("functionUrl", "transformerV");
						dm1.put("iconFname", path + "/theme/img/org-transformer.png");
						dm1.put("isParent", true);
						dm1.put("isAjaxing", true);
						
						
						List<Map<String, Object>> listTransformer4 = new ArrayList<>();
						for(AssetTransformer assetTransformer4 : assetAssetTransformerList3){
							
								Map<String, Object> dm4 = new HashMap<String, Object>();
								dm4.put("id", assetTransformer4.getId());
								dm4.put("name", assetTransformer4.getName());
								dm4.put("parent", virtualTransformerId);
								dm4.put("expanded", false);
								dm4.put("functionUrl", "transformer");
								dm4.put("iconFname", request.getContextPath() + "/theme/img/lines1.png");
								listTransformer4.add(dm4);
						
						}
						if(listTransformer4.size()>0){
							dm1.put("list", listTransformer4);
						}
						
						listTransformer.add(dm1);
						
				
							
							if(assetLineList1 != null && assetLineList1.size() > 0){
								String virtualLineId =  d.getId()+":Line";
						
								Map<String, Object> dm2 = new HashMap<String, Object>();
								dm2.put("id", virtualLineId);
								dm2.put("name", MutiLangUtil.doMutiLang("index.line"));
								dm2.put("parent", d.getId());
								dm2.put("expanded", false);
								dm2.put("functionUrl", "lineV");
								dm2.put("iconFname", path + "/theme/img/org-lines.png");
								dm2.put("isParent", true);
								listTransformer.add(dm2);
							
						}
					}
							
							if(listTransformer.size()>0){
								dm.put("list", listTransformer);
							}
						}
					}
				}
				
				listk.add(dm);
			}
			for(AssetCalcObj assetCalcObj1 : assetCalcObjList){
				Map<String, Object> dm = new HashMap<String, Object>();
				dm.put("id", assetCalcObj1.getId());
				dm.put("name", assetCalcObj1.getName());
				dm.put("parent", g.getId());
				dm.put("expanded", false);
				dm.put("functionUrl", "calcObj");
				dm.put("iconFname", path + "/theme/img/com-Object.png");
				listk.add(dm);
			}
			
			String virtualTransformerId =  g.getId()+":Transformer";
			String virtualLineId =  g.getId()+":Line";
			if(assetTransformerList != null && assetTransformerList.size() > 0){
				Map<String, Object> dm = new HashMap<String, Object>();
				dm.put("id", virtualTransformerId);
				dm.put("name", MutiLangUtil.doMutiLang("index.transformer"));
				dm.put("parent", g.getId());
				dm.put("expanded", false);
				dm.put("functionUrl", "transformerV");
				dm.put("iconFname", path + "/theme/img/org-transformer.png");
				dm.put("isParent", true);
				listk.add(dm);
			}
			
			if(assetLineList != null && assetLineList.size() > 0){
				Map<String, Object> dm = new HashMap<String, Object>();
				dm.put("id", virtualLineId);
				dm.put("name", MutiLangUtil.doMutiLang("index.line"));
				dm.put("parent", g.getId());
				dm.put("expanded", false);
				dm.put("functionUrl", "lineV");
				dm.put("iconFname", path + "/theme/img/org-lines.png");
				dm.put("isParent", true);
				listk.add(dm);
			}
			if(listk.size()>0){
				m.put("list", listk);
				list.add(m);
			}
			
		}
	}
	
	private void initReportTemplateTree(HttpServletRequest request,
			List<Map<String, Object>> list ,String pId) {
		SysOrg entity = new SysOrg();
		String path = request.getContextPath();
		
		entity.setParentOrgTid("0");
		SysUser su = TokenManager.getToken();
		SysOrg org = sysOrgService.getEntity(su.getOrgId());
	
		if(org!=null){
			entity.setOrgCode(org.getOrgCode());
			entity.setParentOrgTid(org.getParentOrgTid());
		}
		List<SysOrg> sysOrglist = sysOrgService.getList(entity);
		for(SysOrg g:sysOrglist){
			Map<String, Object> m = new HashMap<String, Object>();
			m.put("id", g.getId());
			m.put("name", g.getName());
			m.put("parent", "");
			m.put("isParent", true);
			m.put("expanded", true);
			m.put("isAjaxing", true);
			m.put("functionUrl", "org");
			
			m.put("iconFname", path + "/theme/img/org.png");
//			//组织机构
			SysOrg sysOrg=new SysOrg();
			sysOrg.setParentOrgTid(g.getId());
			List<SysOrg> entityChildrenList = sysOrgService.getList(sysOrg);
			//日报表自定义报表开始
			AssetReportTemplate assetReportTemplate = new AssetReportTemplate();
			assetReportTemplate.setOrgId(g.getId());
			assetReportTemplate.setReportType(new BigDecimal(1));
			List<AssetReportTemplate> assetReportTemplateDailyList = assetReportTemplateService.getList(assetReportTemplate);
			//月报表自定义报表开始
			AssetReportTemplate assetReportTemplate1 = new AssetReportTemplate();
			assetReportTemplate1.setOrgId(g.getId());
			assetReportTemplate1.setReportType(new BigDecimal(2));
			List<AssetReportTemplate> assetReportTemplateMonthlyList = assetReportTemplateService.getList(assetReportTemplate1);
			
			List<Map<String, Object>> listk = new ArrayList<>();
			for(SysOrg d:entityChildrenList){
				Map<String, Object> dm = new HashMap<String, Object>();
				dm.put("id", d.getId());
				dm.put("name", d.getName());
				dm.put("parent", g.getId());
				dm.put("expanded", false);
				dm.put("functionUrl", "org");
				dm.put("iconFname", path + "/theme/img/org.png");
			//	listk.add(dm);
				
				//日报表自定义报表开始
				AssetReportTemplate assetReportTemplate4 = new AssetReportTemplate();
				assetReportTemplate4.setOrgId(d.getId());
				assetReportTemplate4.setReportType(new BigDecimal(1));
				List<AssetReportTemplate> assetReportTemplateDailyList1 = assetReportTemplateService.getList(assetReportTemplate4);
				//月报表自定义报表开始
				AssetReportTemplate assetReportTemplate5 = new AssetReportTemplate();
				assetReportTemplate5.setOrgId(d.getId());
				assetReportTemplate5.setReportType(new BigDecimal(2));
				List<AssetReportTemplate> assetReportTemplateMonthlyList1 = assetReportTemplateService.getList(assetReportTemplate5);

				boolean isExistLineAndTransformer = false;
				if(assetReportTemplateDailyList1 != null && assetReportTemplateDailyList1.size() > 0){
					isExistLineAndTransformer = true;
				}
				if(assetReportTemplateMonthlyList1 != null && assetReportTemplateMonthlyList1.size() > 0){
					isExistLineAndTransformer = true;
				}
				
				
				
				if(isExistLineAndTransformer){
					dm.put("isParent", true);
				}
			//	dm.put("isParent", true);
				listk.add(dm);
			}
			
			String dailyReportId =  g.getId()+":DailyReport";
			String monthlyReportId =  g.getId()+":MonthlyReport";
			
			if(assetReportTemplateDailyList != null && assetReportTemplateDailyList.size() > 0){
				Map<String, Object> dm = new HashMap<String, Object>();
				dm.put("id", dailyReportId);
				dm.put("name", MutiLangUtil.doMutiLang("index.dailyReport"));
				dm.put("parent", g.getId());
				dm.put("expanded", false);
				dm.put("functionUrl", "dailyReportV");
				dm.put("iconFname", path + "/theme/img/org-lines.png");
				dm.put("isParent", true);
				listk.add(dm);
			}
			
			if(assetReportTemplateMonthlyList !=null && assetReportTemplateMonthlyList.size() > 0){
				Map<String, Object> dm = new HashMap<String, Object>();
				dm.put("id", monthlyReportId);
				dm.put("name", MutiLangUtil.doMutiLang("index.monthlyReport"));
				dm.put("parent", g.getId());
				dm.put("expanded", false);
				dm.put("functionUrl", "monthlyReportV");
				dm.put("iconFname", path + "/theme/img/org-lines.png");
				dm.put("isParent", true);
				listk.add(dm);
			}
			
			if(listk.size()>0){
				m.put("list", listk);
				list.add(m);
			}
			
		}
	}
	
	
	private void initOrgTree(HttpServletRequest request,
			List<Map<String, Object>> list ,String pId) {
		SysOrg entity = new SysOrg();
		String path = request.getContextPath();
		
		entity.setParentOrgTid("0");
		SysUser su = TokenManager.getToken();
		SysOrg org = sysOrgService.getEntity(su.getOrgId());
	
		if(org!=null){
			entity.setOrgCode(org.getOrgCode());
			entity.setParentOrgTid(org.getParentOrgTid());
		}
		List<SysOrg> sysOrglist = sysOrgService.getList(entity);
		for(SysOrg g:sysOrglist){
			Map<String, Object> m = new HashMap<String, Object>();
			m.put("id", g.getId());
			m.put("name", g.getName());
			m.put("parent", "");
			m.put("isParent", true);
			m.put("expanded", true);
			m.put("isAjaxing", true);
			m.put("functionUrl", "org");
			
			m.put("iconFname", path + "/theme/img/org.png");
//			//组织机构
			SysOrg sysOrg=new SysOrg();
			sysOrg.setParentOrgTid(g.getId());
			List<SysOrg> entityChildrenList = sysOrgService.getList(sysOrg);
			//线路
			
			AssetLine assetLine = new AssetLine();
			assetLine.setOrgId(g.getId());
			List<AssetLine> assetLineList = assetLineManagementService.getList(assetLine);
			//变压器
			AssetTransformer assetTransformer = new AssetTransformer();
			assetTransformer.setOrgId(g.getId());
			List<AssetTransformer> assetTransformerList = assetTransformerService.getList(assetTransformer);
			
			AssetCalcObj assetCalcObj = new AssetCalcObj();
			assetCalcObj.setEntityId(g.getId());
			assetCalcObj.setEntityType(5);
			List<AssetCalcObj> assetCalcObjList =assetLineManagementService.getListForCalObj(assetCalcObj);
			
			List<Map<String, Object>> listk = new ArrayList<>();
			for(SysOrg d:entityChildrenList){
				Map<String, Object> dm = new HashMap<String, Object>();
				dm.put("id", d.getId());
				dm.put("name", d.getName());
				dm.put("parent", g.getId());
				dm.put("expanded", false);
				dm.put("functionUrl", "org");
				dm.put("iconFname", path + "/theme/img/org.png");
			//	listk.add(dm);
				
				AssetLine assetLine2 = new AssetLine();
				assetLine2.setOrgId(d.getId());
				List<AssetLine> assetLineList1 = assetLineManagementService.getList(assetLine2);
				//变压器
				AssetTransformer assetTransformer2 = new AssetTransformer();
				assetTransformer2.setOrgId(d.getId());
				List<AssetTransformer> assetTransformerList1 = assetTransformerService.getList(assetTransformer2);
				boolean isExistLineAndTransformer = false;

				AssetCalcObj assetCalcObj2 = new AssetCalcObj();
				assetCalcObj2.setEntityId(d.getId());
				assetCalcObj2.setEntityType(5);
				List<AssetCalcObj> assetCalcObjList1 =assetLineManagementService.getListForCalObj(assetCalcObj2);
				
				
				if(assetLineList1 != null && assetLineList1.size() > 0){
					isExistLineAndTransformer = true;
				}
				
				if(assetTransformerList1 != null && assetTransformerList1.size() > 0){
					isExistLineAndTransformer = true;
				}
				
				if(assetCalcObjList1 != null && assetCalcObjList1.size() > 0){
					isExistLineAndTransformer = true;
				}
				
				
				if(isExistLineAndTransformer){
					dm.put("isParent", true);
				}
			//	dm.put("isParent", true);
				listk.add(dm);
			}
			
			for(AssetCalcObj assetCalcObj1 : assetCalcObjList){
				Map<String, Object> dm = new HashMap<String, Object>();
				dm.put("id", assetCalcObj1.getId());
				dm.put("name", assetCalcObj1.getName());
				dm.put("parent", g.getId());
				dm.put("expanded", false);
				dm.put("functionUrl", "calcObj");
				dm.put("iconFname", path + "/theme/img/com-Object.png");
				listk.add(dm);
			}
			
			String virtualTransformerId =  g.getId()+":Transformer";
			String virtualLineId =  g.getId()+":Line";
			if(assetTransformerList != null && assetTransformerList.size() > 0){
				Map<String, Object> dm = new HashMap<String, Object>();
				dm.put("id", virtualTransformerId);
				dm.put("name", MutiLangUtil.doMutiLang("index.transformer"));
				dm.put("parent", g.getId());
				dm.put("expanded", false);
				dm.put("functionUrl", "transformerV");
				dm.put("iconFname", path + "/theme/img/org-transformer.png");
				dm.put("isParent", true);
				listk.add(dm);
			}
			
			if(assetLineList != null && assetLineList.size() > 0){
				Map<String, Object> dm = new HashMap<String, Object>();
				dm.put("id", virtualLineId);
				dm.put("name", MutiLangUtil.doMutiLang("index.line"));
				dm.put("parent", g.getId());
				dm.put("expanded", false);
				dm.put("functionUrl", "lineV");
				dm.put("iconFname", path + "/theme/img/org-lines.png");
				dm.put("isParent", true);
				listk.add(dm);
			}
			
//			for(AssetTransformer assetTransformer1 : assetTransformerList){
//				Map<String, Object> dm = new HashMap<String, Object>();
//				dm.put("id", assetTransformer1.getId());
//				dm.put("name", assetTransformer1.getName());
//				dm.put("parent", virtualTransformerId);
//				dm.put("expanded", false);
//				dm.put("functionUrl", "transformer");
//				dm.put("iconFname", path + "/theme/img/transformer1.png");
//				
//				listk.add(dm);
//			}
//			
//			for(AssetLine assetLine1 : assetLineList){
//				Map<String, Object> dm = new HashMap<String, Object>();
//				dm.put("id", assetLine1.getId());
//				dm.put("name", assetLine1.getName());
//				dm.put("parent", virtualLineId);
//				dm.put("expanded", false);
//				dm.put("functionUrl", "line");
//				dm.put("iconFname", path + "/theme/img/lines1.png");
//				listk.add(dm);
//			}
			
			if(listk.size()>0){
				m.put("list", listk);
				list.add(m);
			}
			
		}
	}
	
	
	/**
	 * 递归方法，查询组织机构数据,弹出页的ztree
	 * @Description 
	 * @param list void
	 * <AUTHOR> 
	 * @Time 2018年3月22日 下午7:04:55
	 */
	public void getSysOrgZtreeData(List<SysOrg> list,String orgCode){
		for (SysOrg sysOrg : list) {
			sysOrg.setExpanded(true);	//默认false
			SysOrg entity = new SysOrg();
			if(StringUtil.isNotEmpty(orgCode)){
				entity.setOrgCode(orgCode);
			}
            entity.setParentOrgTid(sysOrg.getId());
            List<SysOrg> sysOrglist = sysOrgService.getList(entity);
            if(sysOrglist.size() > 0){
            	getSysOrgZtreeData(sysOrglist,orgCode);
            	sysOrg.setSysOrgList(sysOrglist);
            }
		}
	}
	
	/**
	 * 跳转到组织机构迁移界面
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "toMoveOrgPage")
	public ModelAndView toMoveOrgPage(SysOrg sysOrg, HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(sysOrg.getId())){
			try {
                sysOrg = sysOrgService.getEntity(sysOrg.getId());
            }
            catch (Exception e) {
                e.printStackTrace();
            }
			model.addAttribute("sysOrg", sysOrg);
		}
		return new ModelAndView("/system/sysOrgMove");
	}
	
	/**
     * 迁移组织机构到其他的组织机构下
     * @param id
     * @return
     */
    @RequestMapping(value = "moveOrgnization")
    @ResponseBody
    public AjaxJson moveOrgnization(SysOrg sysOrg, BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        SysOrg temp = new  SysOrg();
        try {
//        	SysUser su = TokenManager.getToken();
        	if(sysOrg.getId().equals(sysOrg.getParentOrgTid())){
        		j.setSuccess(false);
                j.setMsg(MutiLangUtil.doMutiLang("sysOrganizationList.moveOrgError"));
                return j;
        	}
        	SysOrg children = sysOrgService.getEntity(sysOrg.getId());			//获取子组织机构数据
        	if(StringUtil.isNotEmpty(sysOrg.getParentOrgTid())){
        		MyBeanUtils.copyBeanNotNull2Bean(sysOrg, temp);
        		if(StringUtil.isNotEmpty(children)){
        			/*
        			 * 根据parent ID 获取父级数据
        			 */
					SysOrg parentOrg = sysOrgService.getEntity(sysOrg.getParentOrgTid());
					/*
					 * 父级数据不能挂靠在子级数据的下面
					 */
					if(parentOrg.getParentOrgTid().equals(children.getId())){
						j.setSuccess(false);
						j.setMsg(MutiLangUtil.doMutiLang("sysOrganizationList.noMoveToChildOrg"));
						return j;
					}
					/*
					 * 获取父级org下属数据排序，新orgcode = 最大的原有数据orgcode + 1
					 */
					List<SysOrg> parentList = sysOrgService.getListByparentOrgTid(sysOrg);
					Integer code = 0;
					if(parentList.size() > 0){
						code = Integer.parseInt(parentList.get(0).getOrgCode()
								.substring(parentList.get(0).getOrgCode().length() - 4, parentList.get(0).getOrgCode().length()));
					}
					DecimalFormat decimalFormat = new DecimalFormat("0000");
					temp.setOrgCode(parentOrg.getOrgCode() + decimalFormat.format(code + 1));	//修改orgcode
					temp.setParentOrgTid(parentOrg.getId());	//修改父ID
					/*
					 * 判断修改的子级组织机构不超过四级
					 */
					if(temp.getOrgCode().length() > 16){
						j.setSuccess(false);
						j.setMsg(MutiLangUtil.doMutiLang("sysOrganizationList.admitFourLevelOrg"));
						return j;
					}
					/*
					 * 查询子集下属数据， 如果下属数据的组织机构超过四级的长度，则迁移失败
					 */
					List<SysOrg> childrenList = sysOrgService.getListByOrgCode(children);
					for (int i = 0; i < childrenList.size(); i++) {
						SysOrg childrenOrg = childrenList.get(i);
						//过滤子级组织机构，只判断下属的数据
						if(children.getOrgCode().length() != childrenOrg.getOrgCode().length()){
							childrenOrg.setOrgCode(temp.getOrgCode() 
									+ childrenOrg.getOrgCode().substring(children.getOrgCode().length(), childrenOrg.getOrgCode().length()));
							/*
							 * 判断组织机构不超过四级
							 */
							if(childrenOrg.getOrgCode().length() > 16){
								j.setSuccess(false);
								j.setMsg(MutiLangUtil.doMutiLang("sysOrganizationList.admitFourLevelOrg"));
								return j;
							}
						}
					}
					sysOrgService.update(temp);			//修改子级数据
        			/*
        			 * 修改子集下属数据的OrgCode信息
        			 */
					List<SysOrg> updateChildrenList = sysOrgService.getListByOrgCode(children);
					if(updateChildrenList.size() > 0){
						for (int i = 0; i < updateChildrenList.size(); i++) {
							SysOrg childrenOrg = updateChildrenList.get(i);
							childrenOrg.setOrgCode(temp.getOrgCode() 
									+ childrenOrg.getOrgCode().substring(children.getOrgCode().length(), childrenOrg.getOrgCode().length()));
							sysOrgService.update(childrenOrg);
						}
					}
        		}
        		j.setMsg(MutiLangUtil.doMutiLang("sysOrganizationList.moveSuccess"));
        	}else{
        		/*j.setSuccess(false);
        		j.setMsg(MutiLangUtil.doMutiLang("sysOrganizationList.selectOrg"));*/
        		//如果没有选择父级组织机构，则是迁移至第一级
        		//获取第一级所有组织机构，获取org code
        		temp.setOrgCode(children.getOrgCode());
        		SysOrg so = new SysOrg();
        		so.setParentOrgTid("0");
        		List<SysOrg> parentList = sysOrgService.getListByparentOrgTid(so);
				Integer code = 0;
				if(parentList.size() > 0){
					code = Integer.parseInt(parentList.get(0).getOrgCode());
				}
				DecimalFormat decimalFormat = new DecimalFormat("0000");
				children.setOrgCode(decimalFormat.format(code + 1));
				children.setParentOrgTid("0");	//设置父ID为0
				sysOrgService.update(children);
				//修改子集下属数据的OrgCode信息, 查询子集下属数据，跟据OrgCode
				List<SysOrg> childrenList = sysOrgService.getListByOrgCode(temp);
				if(childrenList.size() > 0){
					for (int i = 0; i < childrenList.size(); i++) {
						SysOrg childrenOrg = childrenList.get(i);
						childrenOrg.setOrgCode(temp.getOrgCode() 
								+ childrenOrg.getOrgCode().substring(children.getOrgCode().length(), childrenOrg.getOrgCode().length()));
						sysOrgService.update(childrenOrg);
					}
				}
				j.setMsg(MutiLangUtil.doMutiLang("sysOrganizationList.moveSuccess"));
        	}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
    
    public  static  String getOrgTypeReplace() {
    	return "1:"+getOrgTypeReplaceI18("State Grid Company")+","
    			+ "2:"+getOrgTypeReplaceI18("Provincial Company")+","
				+ "3:"+getOrgTypeReplaceI18("City Company")+","
				+ "4:"+getOrgTypeReplaceI18("Dostroct & County Company")+","
				+ "5:"+getOrgTypeReplaceI18("Subsidairy")+","
				+ "6:"+getOrgTypeReplaceI18("Power Supply Station")+","
				+ "7:"+getOrgTypeReplaceI18("Headquarter")+","
				+ "8:"+getOrgTypeReplaceI18("Branch")+","
				+ "9:"+getOrgTypeReplaceI18("System Software Provider");
    }
    
    public static String getOrgTypeReplaceI18(String dataItem) {
		String res="";
		if(StringUtils.isNotEmpty(dataItem)) {
			String eventKey="sysRoleList."+dataItem;
			String i18dataItem=MutiLangUtil.doMutiLang(eventKey);
			if(StringUtils.isNotEmpty(i18dataItem)&&!eventKey.equals(i18dataItem)) {
				res=i18dataItem;
			}else {
				res=dataItem;
			}
		}
		return res;
	}
}