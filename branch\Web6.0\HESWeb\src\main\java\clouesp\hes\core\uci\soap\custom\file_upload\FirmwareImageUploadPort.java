package clouesp.hes.core.uci.soap.custom.file_upload;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

/**
 * This class was generated by Apache CXF 3.1.14
 * 2018-04-25T17:50:26.602+08:00
 * Generated source version: 3.1.14
 * 
 */
@WebService(targetNamespace = "http://file_upload.custom.soap.uci.core.hes.clouesp/", name = "FirmwareImageUploadPort")
@XmlSeeAlso({ObjectFactory.class})
public interface FirmwareImageUploadPort {

    @WebResult(name = "return", targetNamespace = "")
    @RequestWrapper(localName = "upload", targetNamespace = "http://file_upload.custom.soap.uci.core.hes.clouesp/", className = "clouesp.hes.core.uci.soap.custom.file_upload.Upload")
    @WebMethod
    @ResponseWrapper(localName = "uploadResponse", targetNamespace = "http://file_upload.custom.soap.uci.core.hes.clouesp/", className = "clouesp.hes.core.uci.soap.custom.file_upload.UploadResponse")
    public boolean upload(
        @WebParam(name = "file", targetNamespace = "")
        clouesp.hes.core.uci.soap.custom.file_upload.CxfFileWrapper file
    );
}
