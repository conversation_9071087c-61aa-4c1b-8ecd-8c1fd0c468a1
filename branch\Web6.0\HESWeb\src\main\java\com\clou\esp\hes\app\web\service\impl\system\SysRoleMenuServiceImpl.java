/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysRoleMenu{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-10-27 09:17:08
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.system;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.system.SysRoleMenuDao;
import com.clou.esp.hes.app.web.model.system.SysRole;
import com.clou.esp.hes.app.web.model.system.SysRoleMenu;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.clou.esp.hes.app.web.service.system.SysRoleMenuService;

@Component
@Service("sysRoleMenuService")
public class SysRoleMenuServiceImpl  extends CommonServiceImpl<SysRoleMenu>  implements SysRoleMenuService {

	@Resource
	private SysRoleMenuDao sysRoleMenuDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(sysRoleMenuDao);
    }
	public SysRoleMenuServiceImpl() {}
	
	@Override
	public SysRoleMenu getEntityByRole(SysRoleMenu sysRoleMenu) {
		return sysRoleMenuDao.getEntityByRole(sysRoleMenu);
	}
	
	
}