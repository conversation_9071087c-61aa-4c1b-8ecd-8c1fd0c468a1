/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetCommunicator{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-06 08:04:54
 * 最后修改时间：
 * 
 *********************************************************************************************************/

package com.clou.esp.hes.app.web.service.asset;

import java.util.List;
import java.util.Map;

import com.clou.esp.hes.app.web.model.asset.AssetCommunicator;
import com.clou.esp.hes.app.web.service.common.CommonService;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

public interface AssetCommunicatorService extends
		CommonService<AssetCommunicator> {

	public int batchInsert(List<AssetCommunicator> cList);

	public List<AssetCommunicator> getListGroupByFwVersion();
	
	public List<AssetCommunicator> getListLimitTwenty(Map<String, Object> p);
	/**
	 * 根据model获取当前版本号
	 * @Description 
	 * @return List<AssetCommunicator>
	 * <AUTHOR> 
	 * @Time 2018年2月22日 下午3:34:57
	 */
	public List<AssetCommunicator> getFwVersionGroupByModel(String modelTypeId);

	public List<AssetCommunicator> getListNoGprs();
	
	/**
	 * 线损统计对象数据
	 * 
	 * @param params
	 * @return
	 */
	public List<Map<String, Object>> getSnNameByMaps(Map<String, Object> params);
	
	
	JqGridResponseTo getForJqGridAdvanced(JqGridSearchTo jqGridSearchTo);
	
	public List<AssetCommunicator> getBySns(List<String> sns);
	
	public void relatePreMeterToDCU(AssetCommunicator dcu);
	
	public void updateRelateDCU(AssetCommunicator dcu);	
	
}