package com.clou.esp.hes.app.web.model.common;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import com.clou.esp.hes.app.web.model.system.SysUser;

/**
 * Entity支持类
 * 
 * <AUTHOR>
 * @version 2016-12-09
 */
public abstract class IdEntity implements Serializable {

	protected static final long serialVersionUID = 1L;

	/**
	 * 实体编号（唯一标识）
	 */
	protected String id;
	
	/** 扩展信息 */
	private Map<String, Object> extData = new HashMap<String, Object>();

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public IdEntity() {

	}

	public IdEntity(String id) {
		this();
		this.id = id;
	}

	public IdEntity(SysUser sysUser) {
		this.id = sysUser.getId();
	}


	public Map<String, Object> getExtData() {
		return extData;
	}

	public void put(String key, Object value) {
		extData.put(key, value);
	}

	public void setExtData(Map<String, Object> extData) {
		this.extData = extData;
	}

}
