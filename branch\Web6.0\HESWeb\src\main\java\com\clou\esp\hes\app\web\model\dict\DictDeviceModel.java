/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictDeviceModel{ } 
 * 
 * 摘    要： 设备类型型号
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-18 02:59:36
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.dict;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DictDeviceModel  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DictDeviceModel() {
	}

	/**name*/
	private java.lang.String name;
	/**manufacturerId*/
	private java.lang.String manufacturerId;
	/**protocolId*/
	private java.lang.String protocolId;
	/**introduction*/
	private java.lang.String introduction;
	/**deviceTypeIcon*/
	private java.lang.String deviceTypeIcon;
	
	/**deviceType，100-199的是Meter 200-299是Communicator*/
	private java.lang.String deviceType;

	/**预付费表预置金额*/
	private java.math.BigDecimal initCreditAmount;
	
	/**
	 * name
	 * @return the value of DICT_DEVICE_MODEL.NAME
	 * @mbggenerated 2017-11-18 02:59:36
	 */
	public java.lang.String getName() {
		return name;
	}

	/**
	 * name
	 * @param name the value for DICT_DEVICE_MODEL.NAME
	 * @mbggenerated 2017-11-18 02:59:36
	 */
    	public void setName(java.lang.String name) {
		this.name = name;
	}
	/**
	 * manufacturerId
	 * @return the value of DICT_DEVICE_MODEL.MANUFACTURER_ID
	 * @mbggenerated 2017-11-18 02:59:36
	 */
	public java.lang.String getManufacturerId() {
		return manufacturerId;
	}

	/**
	 * manufacturerId
	 * @param manufacturerId the value for DICT_DEVICE_MODEL.MANUFACTURER_ID
	 * @mbggenerated 2017-11-18 02:59:36
	 */
    	public void setManufacturerId(java.lang.String manufacturerId) {
		this.manufacturerId = manufacturerId;
	}
	/**
	 * protocolId
	 * @return the value of DICT_DEVICE_MODEL.PROTOCOL_ID
	 * @mbggenerated 2017-11-18 02:59:36
	 */
	public java.lang.String getProtocolId() {
		return protocolId;
	}

	/**
	 * protocolId
	 * @param protocolId the value for DICT_DEVICE_MODEL.PROTOCOL_ID
	 * @mbggenerated 2017-11-18 02:59:36
	 */
    	public void setProtocolId(java.lang.String protocolId) {
		this.protocolId = protocolId;
	}
	/**
	 * introduction
	 * @return the value of DICT_DEVICE_MODEL.INTRODUCTION
	 * @mbggenerated 2017-11-18 02:59:36
	 */
	public java.lang.String getIntroduction() {
		return introduction;
	}

	/**
	 * introduction
	 * @param introduction the value for DICT_DEVICE_MODEL.INTRODUCTION
	 * @mbggenerated 2017-11-18 02:59:36
	 */
    	public void setIntroduction(java.lang.String introduction) {
		this.introduction = introduction;
	}
	/**
	 * deviceTypeIcon
	 * @return the value of DICT_DEVICE_MODEL.DEVICE_TYPE_ICON
	 * @mbggenerated 2017-11-18 02:59:36
	 */
	public java.lang.String getDeviceTypeIcon() {
		return deviceTypeIcon;
	}

	/**
	 * deviceTypeIcon
	 * @param deviceTypeIcon the value for DICT_DEVICE_MODEL.DEVICE_TYPE_ICON
	 * @mbggenerated 2017-11-18 02:59:36
	 */
    	public void setDeviceTypeIcon(java.lang.String deviceTypeIcon) {
		this.deviceTypeIcon = deviceTypeIcon;
	}

	public java.math.BigDecimal getInitCreditAmount() {
		return initCreditAmount;
	}

	public void setInitCreditAmount(java.math.BigDecimal initCreditAmount) {
		this.initCreditAmount = initCreditAmount;
	}

	public DictDeviceModel(java.lang.String name 
	,java.lang.String manufacturerId 
	,java.lang.String protocolId 
	,java.lang.String introduction 
	,java.lang.String deviceTypeIcon
	,java.lang.String deviceType) {
		super();
		this.name = name;
		this.manufacturerId = manufacturerId;
		this.protocolId = protocolId;
		this.introduction = introduction;
		this.deviceTypeIcon = deviceTypeIcon;
		this.deviceType = deviceType;
	}

	public java.lang.String getDeviceType() {
		return deviceType;
	}

	public void setDeviceType(java.lang.String deviceType) {
		this.deviceType = deviceType;
	}

}