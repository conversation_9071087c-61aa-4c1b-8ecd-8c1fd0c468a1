package com.clou.esp.hes.app.web.model.asset;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class FriendlySpecialDay extends BaseEntity{
	
	/**
	    * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	public FriendlySpecialDay() {}
	
	private String specialDayName;      //名字
	private String date;			//特殊日期

	public String getSpecialDayName() {
		return specialDayName;
	}
	public void setSpecialDayName(String specialDayName) {
		this.specialDayName = specialDayName;
	}
	public String getDate() {
		return date;
	}
	public void setDate(String date) {
		this.date = date;
	}

	
	
}
