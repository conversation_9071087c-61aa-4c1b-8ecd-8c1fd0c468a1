<?xml version="1.0" encoding="UTF-8"?>  
<beans xmlns="http://www.springframework.org/schema/beans"  
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"  
    xmlns:context="http://www.springframework.org/schema/context"  
    xmlns:mvc="http://www.springframework.org/schema/mvc"  
    xsi:schemaLocation="http://www.springframework.org/schema/beans    
                        http://www.springframework.org/schema/beans/spring-beans-3.1.xsd    
                        http://www.springframework.org/schema/context    
                        http://www.springframework.org/schema/context/spring-context-3.1.xsd    
                        http://www.springframework.org/schema/mvc    
                        http://www.springframework.org/schema/mvc/spring-mvc-4.0.xsd">  
    <!-- 自动扫描 -->  
    <context:component-scan base-package="com.clou.esp.hes.app.web" />  
    <!-- 引入配置文件 -->  
    <bean
		class="com.clou.esp.hes.app.web.core.configurer.InitConfigurer">
		<property name="locations">
			<value>file:#{systemEnvironment['POWER7000G_WEB_CONFIG_HOME']}/init-web.properties</value>
		</property>
	</bean>
	
	<!--Start 多数据库语言配置-这里以  2015-07-16 -->
	<bean id="vendorProperties"
		class="org.springframework.beans.factory.config.PropertiesFactoryBean">
		<property name="properties">
			<props>
				<prop key="Oracle">oracle</prop>
				<prop key="MySQL">mysql</prop>
				<prop key="SQL Server">sqlserver</prop>
				<prop key="DB2">db2</prop>
			</props>
		</property>
	</bean>
	
	<bean id="databaseIdProvider" class="org.apache.ibatis.mapping.VendorDatabaseIdProvider">  
         <property name="properties" ref="vendorProperties"/>  
    </bean>  
  	
   <!--  <bean id="dataSource" class="org.apache.commons.dbcp.BasicDataSource"  
        destroy-method="close">  
        <property name="driverClassName" value="${jdbc.driver}" />  
        <property name="url" value="${jdbc.url}" />  
        <property name="username" value="${jdbc.username}" />  
        <property name="password" value="${jdbc.password}" />  
        初始化连接大小  
        <property name="initialSize" value="${jdbc.initialSize}"></property>  
        连接池最大数量  
        <property name="maxActive" value="${jdbc.maxActive}"></property>  
        连接池最大空闲  
        <property name="maxIdle" value="${jdbc.maxIdle}"></property>  
        连接池最小空闲  
        <property name="minIdle" value="${jdbc.minIdle}"></property>  
        获取连接最大等待时间  
        <property name="maxWait" value="${jdbc.maxWait}"></property>  
        <property name="timeBetweenEvictionRunsMillis" value="3600000" />
        <property name="minEvictableIdleTimeMillis" value="3600000" />
    </bean>  
     -->
    
    <bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource"
		>
		
		<description>HES datasource</description>
        <!-- 基本属性 url、user、password -->
        <property name="driverClassName" value="${jdbc.driver}"/>
		<property name="url" value="${jdbc.url}" />
		<property name="username" value="${jdbc.username}" />
		<property name="password" value="${jdbc.password}" />

		<!-- 配置初始化大小、最小、最大 -->
		<property name="initialSize" value="${jdbc.initialSize}" />
		<property name="minIdle" value="${jdbc.minIdle}" />
		<property name="maxActive" value="${jdbc.maxActive}" />

		<!-- 配置获取连接等待超时的时间 -->
		<property name="maxWait" value="${jdbc.maxWait}" />

		<!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
		<property name="timeBetweenEvictionRunsMillis" value="3600000" />

		<!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
		<property name="minEvictableIdleTimeMillis" value="3600000" />

		<property name="validationQuery" value="SELECT 'x' from dual" />
		<property name="testWhileIdle" value="true" />
		<property name="testOnBorrow" value="false" />
		<property name="testOnReturn" value="false" />

		<!-- 打开PSCache，并且指定每个连接上PSCache的大小 -->
		<property name="poolPreparedStatements" value="true" />
		<property name="maxPoolPreparedStatementPerConnectionSize"
			value="20" />

		<!-- 配置监控统计拦截的filters -->
		<property name="filters" value="stat" />
	</bean>
	<!--
   	<bean id="MigrationSqlite" class="com.clou.esp.hes.app.web.sysMaintain.MigrationMySql"
		init-method="migrate">
		<property name="dataSource" ref="dataSource"></property>
	</bean>
 	-->
    <!-- spring和MyBatis完美整合，不需要mybatis的配置映射文件 -->  
    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">  
        <property name="dataSource" ref="dataSource" />  
    	<!--多数据库支持-->
		<property name="databaseIdProvider" ref="databaseIdProvider"/>  
        <!-- 自动扫描mapping.xml文件 -->  
        <property name="mapperLocations" value="classpath:/mappings/**/*.xml"></property> 
        <property name="configLocation" value="classpath:/mybatis-config.xml"></property> 
        <!-- mybatis 分页插件 -->
		<!-- dialect属性，使用时必须指定该属性，可选值为oracle,mysql,mariadb,sqlite,hsqldb,postgresql,sqlserver,没有默认值，必须指定该属性 -->
		<property name="plugins">
			<array>
				<bean class="com.github.pagehelper.PageHelper">
					<property name="properties">
						<value>		  
							dialect=${jdbc.type}
							reasonable=true
						</value>
					</property>
				</bean>
			</array>
		</property>
    </bean>  
  
    <!-- DAO接口所在包名，Spring会自动查找其下的类 -->  
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">  
        <property name="basePackage" value="com.clou.esp.hes.app.web.dao" />  
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"></property>
		<property name="annotationClass" value="com.clou.esp.hes.app.web.core.annotation.MyBatisDao" />  
    </bean>  
  
    <!-- (事务管理)transaction manager, use JtaTransactionManager for global tx -->  
    <bean id="transactionManager"  
        class="org.springframework.jdbc.datasource.DataSourceTransactionManager">  
        <property name="dataSource" ref="dataSource" />  
    </bean>  
  
</beans>  