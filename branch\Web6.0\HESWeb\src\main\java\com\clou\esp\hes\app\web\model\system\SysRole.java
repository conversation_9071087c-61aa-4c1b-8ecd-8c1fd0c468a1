/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysRole{ } 
 * 
 * 摘    要： 角色表
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-10-27 09:28:59
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.system;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class SysRole  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public SysRole() {
	}

	/**utilityId*/
	private java.lang.String utilityId;
	/**角色名称*/
	private java.lang.String name;
	/**description*/
	private java.lang.String description;
	
	private java.lang.String userCount;
	
	private java.lang.String menuLevel;
	
	private java.lang.String parentMenuId;

	/**
	 * utilityId
	 * @return the value of SYS_ROLE.VENDOR_ID
	 * @mbggenerated 2017-10-27 09:28:59
	 */
	public java.lang.String getUtilityId() {
		return utilityId;
	}

	/**
	 * utilityId
	 * @param utilityId the value for SYS_ROLE.VENDOR_ID
	 * @mbggenerated 2017-10-27 09:28:59
	 */
    	public void setUtilityId(java.lang.String utilityId) {
		this.utilityId = utilityId;
	}
	/**
	 * 角色名称
	 * @return the value of SYS_ROLE.NAME
	 * @mbggenerated 2017-10-27 09:28:59
	 */
	public java.lang.String getName() {
		return name;
	}

	/**
	 * 角色名称
	 * @param name the value for SYS_ROLE.NAME
	 * @mbggenerated 2017-10-27 09:28:59
	 */
    	public void setName(java.lang.String name) {
		this.name = name;
	}
	/**
	 * description
	 * @return the value of SYS_ROLE.DESCRIPTION
	 * @mbggenerated 2017-10-27 09:28:59
	 */
	public java.lang.String getDescription() {
		return description;
	}

	/**
	 * description
	 * @param description the value for SYS_ROLE.DESCRIPTION
	 * @mbggenerated 2017-10-27 09:28:59
	 */
    	public void setDescription(java.lang.String description) {
		this.description = description;
	}

	public SysRole(java.lang.String utilityId 
	,java.lang.String name 
	,java.lang.String description ) {
		super();
		this.utilityId = utilityId;
		this.name = name;
		this.description = description;
	}

	public java.lang.String getUserCount() {
		return userCount;
	}

	public void setUserCount(java.lang.String userCount) {
		this.userCount = userCount;
	}

	public java.lang.String getMenuLevel() {
		return menuLevel;
	}

	public void setMenuLevel(java.lang.String menuLevel) {
		this.menuLevel = menuLevel;
	}

	public java.lang.String getParentMenuId() {
		return parentMenuId;
	}

	public void setParentMenuId(java.lang.String parentMenuId) {
		this.parentMenuId = parentMenuId;
	}

}