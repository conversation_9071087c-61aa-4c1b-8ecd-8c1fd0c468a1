package clouesp.hes.core.uci.soap.custom.webservice.meterdefineconfig.request;

import java.util.Date;
import java.util.List;
import java.util.UUID;

import ch.iec.tc57._2011.meterdefineconfig.MeterDefineConfigPort;
import ch.iec.tc57._2011.meterdefineconfig_.Arrays;
import ch.iec.tc57._2011.meterdefineconfig_.Meter;
import ch.iec.tc57._2011.meterdefineconfig_.MeterDefineConfig;
import ch.iec.tc57._2011.meterdefineconfigmessage.MeterDefineConfigPayloadType;

import com.clou.esp.hes.app.web.enums.system.TaskState;
import com.power7000g.core.util.base.DateUtils;


/**
 * @ClassName: MeterDefineConfigWritingRequest
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年7月18日 上午11:57:43
 *
 */
public class MeterDefineConfigWritingRequest extends
		MeterDefineConfigRequestAbstract {
	Arrays arrays; // 参数
	List<String> ids; // 每一条数据的唯一 id (一次可向 uci 发送多条数据，但是 web 需要分开标识)
	
	public MeterDefineConfigWritingRequest(List<String> ids,String id,Arrays arrays,String sn,String dataItemId,
			String userId,String verb,String noun,String bathPath,MeterDefineConfigPort port,String revision){
		this.ids = ids;
		this.arrays = arrays;
		
		this.sn = sn;
		this.dataItemId = dataItemId;
		this.messageId = UUID.randomUUID().toString().replace("-", "");
		this.verb = verb;
		this.noun = noun;
		this.source = "ClouESP HES";
		this.timestamp = DateUtils.dateToXmlDate(new Date());
		this.replyAddress = bathPath+"/interfaces/ReplyMeterDefineConfigPort?wsdl";
		this.asyncReplyFlag = true;
		this.ackRequired = true;
		
		this.userId = userId;
		this.id = id;
		
		this.port = port;
		
		this.revision = revision;
	}
	
	public MeterDefineConfigWritingRequest(List<String> ids,Arrays arrays,String sn,String dataItemId,
			String userId,String verb,String noun,String bathPath,MeterDefineConfigPort port,String revision){
		
		this(ids,null,arrays,sn,dataItemId,userId,verb,noun,bathPath,port,revision);
		saveToRedis(null, dataItemId,TaskState.Processing, ids);
	}
	
	public MeterDefineConfigWritingRequest(String id,Arrays arrays,String sn,String dataItemId,
			String userId,String verb,String noun,String bathPath,MeterDefineConfigPort port,String revision){
		
		this(null,id,arrays,sn,dataItemId,userId,verb,noun,bathPath,port,revision);
		saveToRedis(id, dataItemId,TaskState.Processing, null);
	}

	@Override
	public Object createPayload() {
		// TODO Auto-generated method stub
		MeterDefineConfig config = new MeterDefineConfig();
		
		Meter meter = new Meter();
		meter.setMRID(sn);
		config.getMeters().add(meter);
		
		MeterDefineConfig.ReadingType type = new MeterDefineConfig.ReadingType();
		type.setRef(dataItemId);
		config.setReadingType(type);
		
		config.getArrays().add(arrays);
		
		MeterDefineConfigPayloadType payLoadType = new MeterDefineConfigPayloadType();
		payLoadType.setMeterDefineConfig(config);
		
		return payLoadType;
	}


}
