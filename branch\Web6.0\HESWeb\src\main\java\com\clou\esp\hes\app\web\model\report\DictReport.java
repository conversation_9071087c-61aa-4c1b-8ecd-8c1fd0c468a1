/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictReport{ } 
 * 
 * 摘    要： dictReport
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-05-22 04:14:25
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.report;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DictReport  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DictReport() {
	}

	/**reportname*/
	private java.lang.String reportname;
	/**sortId*/
	private java.math.BigInteger sortId;
	/**functionurl*/
	private java.lang.String functionurl;
	/**parentId*/
	private java.lang.String parentId;

	/**
	 * reportname
	 * @return the value of DICT_REPORT.REPORTNAME
	 * @mbggenerated 2018-05-22 04:14:25
	 */
	public java.lang.String getReportname() {
		return MutiLangUtil.assemblyI18nData(MutiLangUtil.DICT_I18N, reportname);
	}

	/**
	 * reportname
	 * @param reportname the value for DICT_REPORT.REPORTNAME
	 * @mbggenerated 2018-05-22 04:14:25
	 */
    	public void setReportname(java.lang.String reportname) {
		this.reportname = reportname;
	}
	/**
	 * sortId
	 * @return the value of DICT_REPORT.SORT_ID
	 * @mbggenerated 2018-05-22 04:14:25
	 */
	public java.math.BigInteger getSortId() {
		return sortId;
	}

	/**
	 * sortId
	 * @param sortId the value for DICT_REPORT.SORT_ID
	 * @mbggenerated 2018-05-22 04:14:25
	 */
    	public void setSortId(java.math.BigInteger sortId) {
		this.sortId = sortId;
	}
	/**
	 * functionurl
	 * @return the value of DICT_REPORT.FUNCTIONURL
	 * @mbggenerated 2018-05-22 04:14:25
	 */
	public java.lang.String getFunctionurl() {
		return functionurl;
	}

	/**
	 * functionurl
	 * @param functionurl the value for DICT_REPORT.FUNCTIONURL
	 * @mbggenerated 2018-05-22 04:14:25
	 */
    	public void setFunctionurl(java.lang.String functionurl) {
		this.functionurl = functionurl;
	}
	/**
	 * parentId
	 * @return the value of DICT_REPORT.PARENT_ID
	 * @mbggenerated 2018-05-22 04:14:25
	 */
	public java.lang.String getParentId() {
		return parentId;
	}

	/**
	 * parentId
	 * @param parentId the value for DICT_REPORT.PARENT_ID
	 * @mbggenerated 2018-05-22 04:14:25
	 */
    	public void setParentId(java.lang.String parentId) {
		this.parentId = parentId;
	}

	public DictReport(java.lang.String reportname 
	,java.math.BigInteger sortId 
	,java.lang.String functionurl 
	,java.lang.String parentId ) {
		super();
		this.reportname = reportname;
		this.sortId = sortId;
		this.functionurl = functionurl;
		this.parentId = parentId;
	}

}