/*-============================================-*/
/*----new ORG_ID must be primary key  --*/
/*============================================*/
ALTER TABLE DATA_INTEGRITY drop primary key;
ALTER TABLE DATA_INTEGRITY ADD CONSTRAINT DATA_INTEGRITY_PK PRIMARY KEY ("ID", "ID_TYPE", "PROFILE_ID", "TV", "TV_TYPE", "ORG_ID");

ALTER TABLE DATA_STATISTICS_DEVICE drop primary key;
ALTER TABLE DATA_STATISTICS_DEVICE ADD CONSTRAINT DATA_STATISTICS_DEVICE_PK PRIMARY KEY ("ID", "ID_TYPE", "TV", "TV_TYPE", "ORG_ID");

ALTER TABLE DATA_STATISTICS_EVENT drop primary key;
ALTER TABLE DATA_STATISTICS_EVENT ADD CONSTRAINT DATA_STATISTICS_EVENT_PK PRIMARY KEY ("EVENT_ID", "TV", "TV_TYPE", "ORG_ID");