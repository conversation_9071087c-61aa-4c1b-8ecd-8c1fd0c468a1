<component name="ArtifactManager">
  <artifact type="war" name="HESWeb:war">
    <output-path>$PROJECT_DIR$/HESWeb/target</output-path>
    <properties id="maven-jee-properties">
      <options>
        <exploded>false</exploded>
        <module>HESWeb</module>
        <packaging>war</packaging>
        <unpackNestedArchives>false</unpackNestedArchives>
      </options>
    </properties>
    <root id="archive" name="HESWeb.war">
      <element id="artifact" artifact-name="HESWeb:war exploded" />
    </root>
  </artifact>
</component>