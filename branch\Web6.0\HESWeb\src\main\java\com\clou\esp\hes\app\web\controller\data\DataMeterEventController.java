/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataMeterEvent{ } 
 * 
 * 摘    要： dataMeterEvent
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-24 01:56:05
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.data;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.clou.esp.hes.app.web.model.asset.AssetLine;
import com.clou.esp.hes.app.web.model.system.SysOrg;
import com.clou.esp.hes.app.web.service.asset.AssetLineManagementService;
import com.clou.esp.hes.app.web.service.system.SysUserService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.CreatePdf;
import com.clou.esp.hes.app.web.core.util.DateTimeFormatterUtil;
import com.clou.esp.hes.app.web.core.util.ExcelUtils;
import com.clou.esp.hes.app.web.core.util.Globals;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.OrganizationUtils;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.model.asset.AssetCommunicator;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.data.DataMeterEvent;
import com.clou.esp.hes.app.web.model.dataUserEvent.DataUserEventProgress;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;
import com.clou.esp.hes.app.web.model.dict.DictDataitemGroup;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.asset.AssetCommunicatorService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.data.DataMeterEventService;
import com.clou.esp.hes.app.web.service.dataUserEvent.DataUserEventProgressService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemGroupService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemService;
import com.clou.esp.hes.app.web.service.system.SysOrgService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.excel.ExcelDataFormatter;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;

/**
 * <AUTHOR>
 * @时间：2017-11-24 01:56:05
 * @描述：dataMeterEvent类
 */
@Controller
@RequestMapping("/dataMeterEventController")
public class DataMeterEventController extends BaseController {

	final static String TIME_FLAG =ResourceUtil.getSessionattachmenttitle("local.time.flag");
	
	@Resource
	private DataMeterEventService dataMeterEventService;
	@Resource
	private DataUserEventProgressService dataUserEventProgressService;
	@Resource
	private SysUserService sysUserService;
	@Resource
	AssetLineManagementService assetLineManagementService;
	@Resource
	private DictDataitemGroupService dictDataitemGroupService;
	@Resource
	private DictDataitemService dictDataitemService;
	@Resource
	private SysOrgService sysOrgService;
	@Resource
	private AssetCommunicatorService assetCommunicatorService;
	@Resource
	private AssetMeterService assetMeterService;
	/**
	 * 打印
	 * @param testUser
	 * @param response
	 */
	@RequestMapping(value = "print")
	@ResponseBody
	public void print(DataMeterEvent eataMeterEvent,
			HttpServletRequest request, HttpServletResponse response) {
		AjaxJson j = new AjaxJson();
		try {
			if (eataMeterEvent == null) {
				eataMeterEvent = new DataMeterEvent();
			}
			String tvStart = eataMeterEvent.getTvStart();
			String tvEnd = eataMeterEvent.getTvEnd();
			List<String> meterIdList =  null;
			if (StringUtil.isNotEmpty(tvStart)) {// 日/月/年
				eataMeterEvent.setTvStart(DateUtils.date2Str(
						DateUtils.str2Date(tvStart, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)),
						new SimpleDateFormat("yyyy-MM-dd 00:00:00")));
			}
			if (StringUtil.isNotEmpty(tvEnd)) {// 日/月/年
				eataMeterEvent.setTvEnd(DateUtils.date2Str(
						DateUtils.str2Date(tvEnd, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)),
						new SimpleDateFormat("yyyy-MM-dd 23:59:59")));
			}
			
			
			String indexAlarmFlag = request.getParameter("indexAlarmFlag");
			String userALarmProgress = request.getParameter("userALarmProgress");
			
			String alarmBell = request.getParameter("alarmBell");
			if(StringUtil.isNotEmpty(alarmBell)){
				eataMeterEvent.setDeviceType("3");
			}
			
			if(StringUtil.isNotEmpty(indexAlarmFlag) && "1".equals(indexAlarmFlag)){
				
				eataMeterEvent.setTvStart(userALarmProgress);
				eataMeterEvent.setTvEnd(DateUtils.date2Str(DateUtils.getDate(), DateTimeFormatterUtil.getSimpleDateFormat_HHmmss("5")));

			}
			
			
			String events=eataMeterEvent.getEvent();
			eataMeterEvent.setEvent("");
			if(StringUtil.isNotEmpty(events)){
				String[] eventList=events.split(",");
				eataMeterEvent.put("events", eventList);
			}
			
			if(StringUtils.isEmpty(eataMeterEvent.getSn())){
				eataMeterEvent.setEventSearchType("");
			}else{
				if("Commnuicator".equals(eataMeterEvent.getEventSearchType())){
					AssetCommunicator assetCommunicatorEntity = new AssetCommunicator();
					assetCommunicatorEntity.setSn(eataMeterEvent.getSn());
					AssetCommunicator assetCommunicator =	assetCommunicatorService.get(assetCommunicatorEntity);
					eataMeterEvent.setSn("");
					if("2".equals(eataMeterEvent.getDeviceType())) {
						eataMeterEvent.setCommId(assetCommunicator.getId());
					}else {
						AssetMeter assetMeter = new AssetMeter();
						assetMeter.setCommunicatorId(assetCommunicator.getId());
						meterIdList = new ArrayList();
						meterIdList.add(assetCommunicator.getId());
						List<AssetMeter> assetMeterList = assetMeterService.getList(assetMeter);
						if(assetMeterList != null && assetMeterList.size() >0){
					
							for(AssetMeter assetMeter1 : assetMeterList){
								meterIdList.add(assetMeter1.getId());
							}
						}
						eataMeterEvent.setMeterIdList(meterIdList);
					}
				}
			}
			SysUser su = TokenManager.getToken();

			List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
			eataMeterEvent.setOrgIdList(orgIdList);
			eataMeterEvent.setMeterIdList(meterIdList);
			
			List<DataMeterEvent> list = dataMeterEventService.getList(eataMeterEvent);
			// list生成pdf就可以了；
			String basePath = request.getRealPath("/");
			basePath += "\\upload\\";
			File f = new File(basePath);
			if (!f.exists()) {
				f.mkdirs();
			}
			basePath += System.currentTimeMillis() + new Random().nextInt(10000) + ".pdf";
			// { "Region", "orgName" },
			String[][] head = { { "Serial Number", "sn" }, { "Time", "stv" },
					{ "Event Type", "eventType" }, { "Event", "event" } };
			if (list == null || list.size() == 0) {
				DataMeterEvent dme = new DataMeterEvent();
				dme.setSn("");
				dme.setStv("");
				dme.setOrgName("");
				dme.setEventType("");
				dme.setEvent("");
				list.add(dme);
			}else{
				for(DataMeterEvent dataMeterEvent : list){
                     if(dataMeterEvent.getTv() != null){
                    		dataMeterEvent.setStv(DateUtils.date2Str(
                    				dataMeterEvent.getTv(),
        							new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"))));
                     }
				
				}
			}
			String filePath = new CreatePdf().generatePDFs(basePath, head, list);
			response.setContentType("application/pdf");
			try {
				String strPdfPath = new String(basePath);
				// 判断该路径下的文件是否存在
				File file = new File(strPdfPath);
				if (file.exists()) {
					DataOutputStream temps = new DataOutputStream(response.getOutputStream());
					DataInputStream in = new DataInputStream(new FileInputStream(strPdfPath));
					byte[] b = new byte[2048];
					while ((in.read(b)) != -1) {
						temps.write(b);
						temps.flush();
					}
					in.close();
					temps.close();
				} else {
					
				}
			} catch (Exception e) {
				j.setSuccess(false);
				e.printStackTrace();
			}
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
		}
	}

	/**
	 * 导出
	 * 
	 * @param testUser
	 * @param response
	 */
	@RequestMapping(value = "exportExcel")
	@ResponseBody
	public void exportExcel(DataMeterEvent eataMeterEvent,
			HttpServletRequest request, HttpServletResponse response) {
		AjaxJson j = new AjaxJson();
		try {
			if (eataMeterEvent == null) {
				eataMeterEvent = new DataMeterEvent();
			}
		//	String eventSearchType = request.getParameter("eventSearchType");
			String indexAlarmFlag = request.getParameter("indexAlarmFlag");
			String userALarmProgress = request.getParameter("userALarmProgress");
			
			
			String alarmBell = request.getParameter("alarmBell");
			if(StringUtil.isNotEmpty(alarmBell)){
				eataMeterEvent.setDeviceType("3");
			}
	
			List<String> meterIdList =  null;
			String tvStart = eataMeterEvent.getTvStart();
			String tvEnd = eataMeterEvent.getTvEnd();
			if (StringUtil.isNotEmpty(tvStart)) {// 日/月/年
				eataMeterEvent.setTvStart(DateUtils.date2Str(
						DateUtils.str2Date(tvStart, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)),
						new SimpleDateFormat("yyyy-MM-dd 00:00:00")));
			}
			if (StringUtil.isNotEmpty(tvEnd)) {// 日/月/年
				eataMeterEvent.setTvEnd(DateUtils.date2Str(
						DateUtils.str2Date(tvEnd, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)),
						new SimpleDateFormat("yyyy-MM-dd 23:59:59")));
			}
			
			
			if(StringUtil.isNotEmpty(indexAlarmFlag)  && "1".equals(indexAlarmFlag)){
				
				eataMeterEvent.setTvStart(userALarmProgress);
				eataMeterEvent.setTvEnd(DateUtils.date2Str(DateUtils.getDate(), DateTimeFormatterUtil.getSimpleDateFormat_HHmmss("5")));

			}
			
			String events=eataMeterEvent.getEvent();
			eataMeterEvent.setEvent("");
			if(StringUtil.isNotEmpty(events)){
				String[] eventList=events.split(",");
				eataMeterEvent.put("events", eventList);
			}
			
			if(StringUtils.isEmpty(eataMeterEvent.getSn())){
				eataMeterEvent.setEventSearchType("");
			}else{
				
				if("Commnuicator".equals(eataMeterEvent.getEventSearchType())){
					AssetCommunicator assetCommunicatorEntity = new AssetCommunicator();
					assetCommunicatorEntity.setSn(eataMeterEvent.getSn());
					AssetCommunicator assetCommunicator =	assetCommunicatorService.get(assetCommunicatorEntity);
					eataMeterEvent.setSn("");
					if("2".equals(eataMeterEvent.getDeviceType())) {
						eataMeterEvent.setCommId(assetCommunicator.getId());
					}else {
						AssetMeter assetMeter = new AssetMeter();
						assetMeter.setCommunicatorId(assetCommunicator.getId());
						meterIdList = new ArrayList();
						meterIdList.add(assetCommunicator.getId());
						List<AssetMeter> assetMeterList = assetMeterService.getList(assetMeter);
						if(assetMeterList != null && assetMeterList.size() >0){
					
							for(AssetMeter assetMeter1 : assetMeterList){
								meterIdList.add(assetMeter1.getId());
							}
						}
						eataMeterEvent.setMeterIdList(meterIdList);
					}
					
				}
			}
			SysUser su = TokenManager.getToken();

			List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
			eataMeterEvent.setOrgIdList(orgIdList);
			eataMeterEvent.setMeterIdList(meterIdList);
			List<DataMeterEvent> list = dataMeterEventService.getList(eataMeterEvent);
			if (list.size() <= 0) {
				DataMeterEvent d = new DataMeterEvent();
				list.add(d);
			}
			ExcelDataFormatter edf = new ExcelDataFormatter();
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("tv", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
	        edf.set("tv", tvs);
			ExcelUtils.writeToFile(list, edf, "dataEventReport.xlsx", response,
					ValidGroup1.class);
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
		}
	}

	/**
	 * 根据事件类型获取事件
	 * 
	 * @param groupId
	 * @return
	 */
	@RequestMapping(value = "getEvent")
	@ResponseBody
	public AjaxJson getEvent(String groupId, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		List<DictDataitem> list = dictDataitemService
				.getDictDataitemByGroupId(groupId);
		j.setObj(list);
		return j;
	}

	/**
	 * 跳转到dataMeterEvent列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "list")
	public ModelAndView list(HttpServletRequest request, Model model) {
		SysUser su = TokenManager.getToken();
		SysUser sysUser = sysUserService.getEntity(su.getId());

		SysOrg sysOrg = sysOrgService.getEntity(sysUser.getOrgId());
		model.addAttribute("sysOrg", sysOrg);

		DictDataitemGroup entity=new DictDataitemGroup();
		entity.setAppType("2");
		entity.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
		List<DictDataitemGroup> ddgList = dictDataitemGroupService.getList(entity);
		List<DictDataitem> ddAllList=new ArrayList<DictDataitem>();
		for(int i=0;i<ddgList.size();i++){
			DictDataitem dd=new DictDataitem();
			Map<String, Object> qm = new HashMap<String, Object>();
			qm.put("groupId", ddgList.get(i).getId());
			qm.put("appType", "2");
			dd.setExtData(qm);
			//分组已区分 规约 故删掉
			//		dd.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
			List<DictDataitem> ddList=dictDataitemService.getList(dd);
			ddAllList.addAll(ddList);
		}
		String eventTypeReplace = RoletoJson.listToReplaceStr(ddgList, "id", "name",";");
		model.addAttribute("eventTypeReplace", eventTypeReplace);
		String eventReplace = RoletoJson.listToReplaceStr(ddAllList, "id", "name",";");
		model.addAttribute("eventReplace", eventReplace);

		SimpleDateFormat sdf = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.formatter"));
		Calendar lastDate = Calendar.getInstance();
		lastDate.add(Calendar.DATE, -3);	//昨天
		model.addAttribute("startTime", sdf.format(lastDate.getTime())+" 00:00:00");
		lastDate.add(Calendar.DATE, 3);
		model.addAttribute("endTime", sdf.format(lastDate.getTime())+" 23:59:59");

		return new ModelAndView("/data/dataEventReport");
	}
	
	
	/**
	 * 跳转到dataMeterEvent列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "list1")
	public ModelAndView list1(HttpServletRequest request, Model model) {
		
		DictDataitemGroup entity=new DictDataitemGroup();
		entity.setAppType("2");
		entity.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
		List<DictDataitemGroup> ddgList = dictDataitemGroupService.getList(entity);
		List<DictDataitem> ddAllList=new ArrayList<DictDataitem>();
		for(int i=0;i<ddgList.size();i++){
			DictDataitem dd=new DictDataitem();
			Map<String, Object> qm = new HashMap<String, Object>();
			qm.put("groupId", ddgList.get(i).getId());
			qm.put("appType", "2");
			dd.setExtData(qm);
			//分组已区分 规约 故删掉
			//		dd.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
			List<DictDataitem> ddList=dictDataitemService.getList(dd);
			ddAllList.addAll(ddList);
		}
		String eventTypeReplace = RoletoJson.listToReplaceStr(ddgList, "id", "name",";");
		model.addAttribute("eventTypeReplace", eventTypeReplace);
		String eventReplace = RoletoJson.listToReplaceStr(ddAllList, "id", "name",";");
		model.addAttribute("eventReplace", eventReplace);
		return new ModelAndView("/data/dataEventReport1");
	}
	
	/**
	 * 精确查找设备
	 * @param deviceSn
	 * @param deviceType
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "getEventType")
	@ResponseBody
	public AjaxJson getEventType(String event,
			HttpServletRequest request, Model model) {
		AjaxJson j = new AjaxJson();
		DictDataitemGroup entity=new DictDataitemGroup();
		entity.setAppType("2");
		entity.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
		Map<String, Object> qm = new HashMap<String, Object>();
		qm.put("event", event);

		entity.setExtData(qm);
		String eventType = dictDataitemGroupService.getMeterDataEventType(entity);
	    if(StringUtils.isEmpty(eventType)){
	    	j.setObj("");
	    }else{
	    	j.setObj(eventType);
	    }

		return j;
	}
	
	
	/**
	 * Meter Data & Event Export
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "dataEventExport")
	public ModelAndView dataEventExport(HttpServletRequest request, Model model) {
		return new ModelAndView("/data/dataEventExport");
	}

	/**
	 * 跳转到dataMeterEvent新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "dataMeterEvent")
	public ModelAndView dataMeterEvent(DataMeterEvent dataMeterEvent,
			HttpServletRequest request, Model model) {
		if (StringUtil.isNotEmpty(dataMeterEvent.getId())) {
			try {
				dataMeterEvent = dataMeterEventService.getEntity(dataMeterEvent
						.getId());
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			model.addAttribute("dataMeterEvent", dataMeterEvent);
		}
		return new ModelAndView("/data/dataMeterEvent");
	}

	/**
	 * dataMeterEvent查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
	@ResponseBody
	public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,	HttpServletRequest request,String txt_org_id) {
 		TagUtil.setFieldValue(jqGridSearchTo, request); // 取传递的参数到JqGridSearchTo

		String tvStart = (String) jqGridSearchTo.getMap().get("tv_start");
		String tvEnd = (String) jqGridSearchTo.getMap().get("tv_end");
		String starttime = " 00:00:00";
		String endtime = " 23:59:59";
		if(tvStart.length() > 10){
			  starttime = tvStart.substring(tvStart.substring(0, tvStart.indexOf(" ")).length()+1);
			  endtime = tvEnd.substring(tvEnd.substring(0, tvEnd.indexOf(" ")).length()+1);
		}

		List<String> orgIds=OrganizationUtils.getOrgIds(sysOrgService,txt_org_id);
		jqGridSearchTo.getMap().put("txt_org_id", orgIds);

		String events=(String) jqGridSearchTo.getMap().get("event");
		//String indexAlarmFlag= (String) jqGridSearchTo.getMap().get("indexAlarmFlag");
		String indexAlarmFlag= request.getParameter("indexAlarmFlag");
		String userALarmProgress= request.getParameter("userALarmProgress");

		String alarmBell= request.getParameter("alarmBell");
		
		jqGridSearchTo.getMap().put("alarmBell", alarmBell);
		
		String eventSearchType= request.getParameter("eventSearchType");
		String deviceType= request.getParameter("deviceType");
		System.out.println(eventSearchType);
		SysUser su = TokenManager.getToken();

		jqGridSearchTo.put("event", "");
		if(StringUtil.isNotEmpty(events)){
			String[] eventList = events.split(",");
			jqGridSearchTo.put("events", eventList);
		}
		if (StringUtil.isNotEmpty(tvStart)) {// 日/月/年
			String st = DateUtils.date2Str(
					DateUtils.str2Date(tvStart, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)),
					new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
			jqGridSearchTo.getMap().put("tv_start", st.substring(0, st.indexOf(" ")) + " "+starttime);
		}
		if (StringUtil.isNotEmpty(tvEnd)) {// 日/月/年
			String et =  DateUtils.date2Str(
					DateUtils.str2Date(tvEnd, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)),
					new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
			jqGridSearchTo.getMap().put("tv_end", et.substring(0, et.indexOf(" ")) + " "+ endtime);
		}


		if(StringUtil.isNotEmpty(indexAlarmFlag) && "1".equals(indexAlarmFlag)){
			jqGridSearchTo.getMap().put("indexAlarmFlag","flag");
			DataUserEventProgress DataUserEventProgress1 = new DataUserEventProgress();
			DataUserEventProgress1.setUserId(su.getId());
			DataUserEventProgress dataUserEventProgress =dataUserEventProgressService.get(DataUserEventProgress1);
			if(dataUserEventProgress == null){
				jqGridSearchTo.getMap().put("tv_start", DateUtils.date2Str(DateUtils.getDate(), DateTimeFormatterUtil.getSimpleDateFormat_HHmmss("5")));
			}else{
				jqGridSearchTo.getMap().put("tv_start", userALarmProgress);
			}

			jqGridSearchTo.getMap().put("tv_end", DateUtils.date2Str(DateUtils.getDate(), DateTimeFormatterUtil.getSimpleDateFormat_HHmmss("5")));
		}
		
		JqGridResponseTo j = null;
		try {
			List<String> meterIdList = null; 
			List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
			jqGridSearchTo.getMap().put("orgIdList", orgIdList);
			if(StringUtils.isEmpty(jqGridSearchTo.getMap().get("sn"))){
				jqGridSearchTo.getMap().put("eventSearchType","");
			}else{
				if("Commnuicator".equals(eventSearchType)){
					AssetCommunicator assetCommunicatorEntity = new AssetCommunicator();
					assetCommunicatorEntity.setSn(jqGridSearchTo.getMap().get("sn").toString());
					AssetCommunicator assetCommunicator =	assetCommunicatorService.get(assetCommunicatorEntity);
					jqGridSearchTo.getMap().put("sn", "");
					if("2".equals(deviceType)) {
						jqGridSearchTo.getMap().put("commId", assetCommunicator.getId());
					}else {
						AssetMeter assetMeter = new AssetMeter();
						assetMeter.setCommunicatorId(assetCommunicator.getId());
						meterIdList = new ArrayList();
						meterIdList.add(assetCommunicator.getId());
						List<AssetMeter> assetMeterList = assetMeterService.getList(assetMeter);
						if(assetMeterList != null && assetMeterList.size() >0){
					
							for(AssetMeter assetMeter1 : assetMeterList){
								meterIdList.add(assetMeter1.getId());
							}
						}
						jqGridSearchTo.getMap().put("meterIdList", meterIdList);
					}
				}
				jqGridSearchTo.getMap().put("eventSearchType",eventSearchType);	
			}
			j = dataMeterEventService.getForJqGrid(jqGridSearchTo);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return j;
	}

	/**
	 * 删除dataMeterEvent信息
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "del")
	@ResponseBody
	public AjaxJson del(DataMeterEvent dataMeterEvent,
			HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			SysUser su = TokenManager.getToken();
			if (dataMeterEventService.deleteById(dataMeterEvent.getId()) > 0) {
				j.setMsg("删除成功");
			} else {
				j.setSuccess(false);
				j.setMsg("删除失败");
			}
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg("系统异常");
		}
		return j;
	}

	/**
	 * 保存dataMeterEvent信息
	 * 
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "save")
	@ResponseBody
	public AjaxJson save(
			@Validated(value = { ValidGroup1.class }) DataMeterEvent dataMeterEvent,
			BindingResult bindingResult, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		DataMeterEvent t = new DataMeterEvent();
		try {
			SysUser su = TokenManager.getToken();
			if (StringUtil.isNotEmpty(dataMeterEvent.getId())) {
				t = dataMeterEventService.getEntity(dataMeterEvent.getId());
				MyBeanUtils.copyBeanNotNull2Bean(dataMeterEvent, t);
				dataMeterEventService.update(t);
				j.setMsg("修改成功");

			} else {
				dataMeterEventService.save(dataMeterEvent);
				j.setMsg("创建成功");

			}
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg("系统异常");
		}
		return j;
	}
	
	@RequestMapping(value = "getCurrentEventSum")
	@ResponseBody
	public AjaxJson getCurrentEventSum(HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		Map<String, Object> attributes = new HashMap<String, Object>();
		SysUser su = TokenManager.getToken();
		if(su==null) {
			attributes.put("meterEventTotal", 0);
			j.setAttributes(attributes);
			return j;
		}
		List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
	
		Map<String, Object> params = new HashMap<String, Object>();
		
		//List<Map<String, Object>> diList = assetCommunicatorService.getSnNameByMaps(params);
		//todo增加组织机构过滤
		DataUserEventProgress dataUserEventProgress =dataUserEventProgressService.getEntity(su.getId());
		if(dataUserEventProgress == null){
			DataUserEventProgress dataUserEventProgress1 = new DataUserEventProgress();
			dataUserEventProgress1.setUserId(su.getId());
			dataUserEventProgress1.setProgress(DateUtils.getDate());
			//dataUserEventProgress1.setUpdateTv(DateUtils.getDate());
			dataUserEventProgressService.save(dataUserEventProgress1);
			params.put("tv_start", DateUtils.date2Str(DateUtils.getDate(), DateTimeFormatterUtil.getSimpleDateFormat_HHmmss("5")));
		}else{
			params.put("tv_start", DateUtils.date2Str(dataUserEventProgress.getProgress(), DateTimeFormatterUtil.getSimpleDateFormat_HHmmss("5")));
		}
		//todo 时间没有转格式
		params.put("orgIdList", orgIdList);
		
		params.put("tv_end", DateUtils.date2Str(DateUtils.getDate(), DateTimeFormatterUtil.getSimpleDateFormat_HHmmss("5")));
		Long count =dataMeterEventService.getCountByOrgIds(params);
		attributes.put("meterEventTotal", count);
		j.setAttributes(attributes);
		return j;
	}
	
	@RequestMapping(value = "updateCurrentEventProgress")
	@ResponseBody
	public AjaxJson updateCurrentEventProgress(HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		
		SysUser su = TokenManager.getToken();

		List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);

		Map<String, Object> params = new HashMap<String, Object>();
		
		//todo增加组织机构过滤
		DataUserEventProgress dataUserEventProgress =dataUserEventProgressService.getEntity(su.getId());
		Date date =dataUserEventProgress.getProgress();

		dataUserEventProgress.setUserId(su.getId());
		dataUserEventProgress.setProgress(DateUtils.getDate());
		
		dataUserEventProgressService.update(dataUserEventProgress);
		
		Map<String, Object> attributes = new HashMap<String, Object>();

		attributes.put("userALarmProgress",DateUtils.date2Str(date, DateTimeFormatterUtil.getSimpleDateFormat_HHmmss("5")));
		j.setAttributes(attributes);
		return j;
	}
	
	
	

}