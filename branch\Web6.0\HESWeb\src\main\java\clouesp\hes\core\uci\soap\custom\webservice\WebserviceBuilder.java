package clouesp.hes.core.uci.soap.custom.webservice;

import java.util.List;

import clouesp.hes.core.uci.soap.custom.webservice.meterdefineconfig.request.MeterDefineConfigReadingMoreRequest;
import clouesp.hes.core.uci.soap.custom.webservice.meterdefineconfig.request.MeterDefineConfigReadingRequest;
import clouesp.hes.core.uci.soap.custom.webservice.meterdefineconfig.request.MeterDefineConfigWritingMoreRequest;
import clouesp.hes.core.uci.soap.custom.webservice.meterdefineconfig.request.MeterDefineConfigWritingRequest;
import clouesp.hes.core.uci.soap.custom.webservice.meterdefineconfig.response.MeterDefineConfigReadingWritingMoreResponse;
import clouesp.hes.core.uci.soap.custom.webservice.meterdefineconfig.response.MeterDefineConfigReadingWritingResponse;
import ch.iec.tc57._2011.meterdefineconfig.MeterDefineConfigPort;
import ch.iec.tc57._2011.meterdefineconfig_.Arrays;


/**
 * @ClassName: WebserviceBuilder
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年7月18日 下午4:02:11
 *
 */
public class WebserviceBuilder {

	/**
	 * 
	 * @Title: buildMeterDefineConfigReadingRequest
	 * @Description: 终端参数读取请求
	 * @param sn
	 * @param dataItemId
	 * @param userId
	 * @param verb
	 * @param noun
	 * @param bathPath
	 * @param port
	 * @return void
	 * @throws
	 */
	public static void buildMeterDefineConfigReadingRequest(String id,String sn,String dataItemId,String userId,String verb,String noun,String bathPath,MeterDefineConfigPort port,String revision){
		MeterDefineConfigReadingRequest readingRequest = new MeterDefineConfigReadingRequest(id, sn, dataItemId, userId, verb, noun, bathPath, port,revision);
		readingRequest.send();
	}
	
	/**
	 * 
	 * @Title: buildMeterDefineConfigWritingRequest
	 * @Description: 终端参数设置 (多条)请求
	 * @param ids
	 * @param arrays
	 * @param sn
	 * @param dataItemId
	 * @param userId
	 * @param verb
	 * @param noun
	 * @param bathPath
	 * @param port
	 * @return void
	 * @throws
	 */
	public static void buildMeterDefineConfigWritingRequest(List<String> ids,Arrays arrays,String sn,String dataItemId,
			String userId,String verb,String noun,String bathPath,MeterDefineConfigPort port,String revision){
		MeterDefineConfigWritingRequest writingRequest = new MeterDefineConfigWritingRequest(ids, arrays, sn, dataItemId, userId, verb, noun, bathPath, port,revision);
		writingRequest.send();
	}
	
	/**
	 * 
	 * @Title: buildMeterDefineConfigWritingRequest
	 * @Description: 终端参数设置 (单条)请求
	 * @param id
	 * @param arrays
	 * @param sn
	 * @param dataItemId
	 * @param userId
	 * @param verb
	 * @param noun
	 * @param bathPath
	 * @param port
	 * @return void
	 * @throws
	 */
	public static void buildMeterDefineConfigWritingRequest(String id,Arrays arrays,String sn,String dataItemId,
			String userId,String verb,String noun,String bathPath,MeterDefineConfigPort port,String revision) {
		MeterDefineConfigWritingRequest writingRequest = new MeterDefineConfigWritingRequest(id, arrays, sn, dataItemId, userId, verb, noun, bathPath, port,revision);
		writingRequest.send();
	}
	
	/**
	 * 
	 * @Title: buildMeterDefineConfigReadingWritingResponse
	 * @Description: 终端参数设置、读取响应的处理
	 * @param object
	 * @return void
	 * @throws
	 */
	public static void buildMeterDefineConfigReadingWritingResponse(Object object) {
		MeterDefineConfigReadingWritingResponse rwResponse = new MeterDefineConfigReadingWritingResponse();
		rwResponse.recv(object);
	}
	
	/**
	 * 
	 * @Title: buildMeterDefineConfigWritingMoreRequest
	 * @Description: 处理一条请求，多条响应的参数设置
	 * @param ids
	 * @param meters
	 * @param arrays
	 * @param sn
	 * @param dataItemId
	 * @param userId
	 * @param verb
	 * @param noun
	 * @param bathPath
	 * @param port
	 * @return void
	 * @throws
	 */
	public static void buildMeterDefineConfigWritingMoreRequest(String id, List<String> ids,List<String> meters,Arrays arrays,String sn,String dataItemId,
			String userId,String verb,String noun,String bathPath,MeterDefineConfigPort port,String revision,List<String> pns){
		MeterDefineConfigWritingMoreRequest moreRequest = new MeterDefineConfigWritingMoreRequest(id,ids,meters,arrays,sn,dataItemId,userId,verb,noun,bathPath,port,revision,pns);
		moreRequest.send();
	}
	
	/**
	 * 
	 * @Title: buildMeterDefineConfigReadingWritingMoreResponse
	 * @Description: 处理一条请求，多条响应的参数设置
	 * @param Object
	 * @return void
	 * @throws
	 */
	public static void buildMeterDefineConfigReadingWritingMoreResponse(Object object){
		MeterDefineConfigReadingWritingMoreResponse moreResponse = new MeterDefineConfigReadingWritingMoreResponse();
		moreResponse.recv(object);
	}
	
	public static void buildMeterDefineConfigReadingMoreRequest(String id, List<String> ids,List<String> meters,Arrays arrays,String sn,String dataItemId,
			String userId,String verb,String noun,String bathPath,MeterDefineConfigPort port,String revision,List<String> pns){
		MeterDefineConfigReadingMoreRequest moreRequest = new MeterDefineConfigReadingMoreRequest(id,ids,meters,arrays,sn,dataItemId,userId,verb,noun,bathPath,port,revision,pns);
		moreRequest.send();
	}
}
