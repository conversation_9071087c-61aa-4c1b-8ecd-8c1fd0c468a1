package com.clou.esp.hes.app.web.model.tariff;


import java.util.List;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

/*********************************************************************************************************
 * @see Copyright© 2019 Shenzhen Clou Electronics CO., LTD.  All rights reserved. 
 * @see 
 * @see File Name:public class PpmCustomerTariffMap{ } 
 * @see 
 * @see Description： ppmCustomerTariffMap
 * @version *******
 * <AUTHOR>
 * @see Create Time：2019-09-17 10:06:31
 * @see Last modification Time：2019-09-17 10:06:31
 * 
*********************************************************************************************************/
public class PpmCustomerTariffMap  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public PpmCustomerTariffMap() {
	}

	/**type*/
	private java.lang.Integer type;
	/**groupId*/
	private java.lang.String groupId;


	private List<String> customerIds;
	
	
	
	public List<String> getCustomerIds() {
		return customerIds;
	}

	public void setCustomerIds(List<String> customerIds) {
		this.customerIds = customerIds;
	}

	public java.lang.Integer getType() {
		return type;
	}

	public void setType(java.lang.Integer type) {
		this.type = type;
	}

	/**
	 * groupId
	 * @return the value of PPM_CUSTOMER_TARIFF_MAP.GROUP_ID
	 * @mbggenerated 2019-09-17 10:06:31
	 */
	public java.lang.String getGroupId() {
		return groupId;
	}

	/**
	 * groupId
	 * @param groupId the value for PPM_CUSTOMER_TARIFF_MAP.GROUP_ID
	 * @mbggenerated 2019-09-17 10:06:31
	 */
    	public void setGroupId(java.lang.String groupId) {
		this.groupId = groupId;
	}

	public PpmCustomerTariffMap(java.lang.Integer type 
	,java.lang.String groupId ) {
		super();
		this.type = type;
		this.groupId = groupId;
	}

}