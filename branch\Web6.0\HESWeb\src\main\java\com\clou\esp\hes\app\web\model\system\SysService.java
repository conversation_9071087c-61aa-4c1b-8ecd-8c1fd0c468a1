/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysService{ } 
 * 
 * 摘    要： sysService
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-01-29 08:53:20
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.system;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class SysService  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public SysService() {
	}

	/**介绍*/
	private java.lang.String introduction;
	/**端口*/
	private java.lang.String hostId;
	/**服务类型*/
	private String serviceType;
	/**是否在线*/
	private String isOnline;
	/**serverId*/
	private java.lang.String serverId;
	/**端口*/
	private java.lang.String ip;

	/**
	 * introduction
	 * @return the value of SYS_SERVICE.INTRODUCTION
	 * @mbggenerated 2018-01-29 08:53:20
	 */
	public java.lang.String getIntroduction() {
		return introduction;
	}

	/**
	 * introduction
	 * @param introduction the value for SYS_SERVICE.INTRODUCTION
	 * @mbggenerated 2018-01-29 08:53:20
	 */
    	public void setIntroduction(java.lang.String introduction) {
		this.introduction = introduction;
	}
	/**
	 * hostId
	 * @return the value of SYS_SERVICE.HOST_ID
	 * @mbggenerated 2018-01-29 08:53:20
	 */
	public java.lang.String getHostId() {
		return hostId;
	}

	/**
	 * hostId
	 * @param hostId the value for SYS_SERVICE.HOST_ID
	 * @mbggenerated 2018-01-29 08:53:20
	 */
    	public void setHostId(java.lang.String hostId) {
		this.hostId = hostId;
	}

	public void setServiceType(String serviceType) {
		this.serviceType = serviceType;
	}

	public void setIsOnline(String isOnline) {
		this.isOnline = isOnline;
	}

	/**
	 * serverId
	 * @return the value of SYS_SERVICE.SERVER_ID
	 * @mbggenerated 2018-01-29 08:53:20
	 */
	public java.lang.String getServerId() {
		return serverId;
	}

	/**
	 * serverId
	 * @param serverId the value for SYS_SERVICE.SERVER_ID
	 * @mbggenerated 2018-01-29 08:53:20
	 */
    	public void setServerId(java.lang.String serverId) {
		this.serverId = serverId;
	}

	public SysService(java.lang.String introduction 
			,java.lang.String hostId 
			,java.lang.String isOnline 
			,java.lang.String serviceType
			,java.lang.String serverId ) {
		super();
		this.introduction = introduction;
		this.hostId = hostId;
		this.serviceType = serviceType;
		this.isOnline = isOnline;
		this.serverId = serverId;
	}

	public java.lang.String getIp() {
		return ip;
	}

	public void setIp(java.lang.String ip) {
		this.ip = ip;
	}

	public String getServiceType() {
		return serviceType;
	}

	public String getIsOnline() {
		return isOnline;
	}
	
	@Override
	public String toString() {
		return "{id: " + ip + " ,ipintroduction: " + introduction + " ,hostId: " + hostId 
				+ " ,serviceType: " + serviceType + " ,isOnline: " + isOnline + " ,serverId: " + serverId +"}";
	}
}