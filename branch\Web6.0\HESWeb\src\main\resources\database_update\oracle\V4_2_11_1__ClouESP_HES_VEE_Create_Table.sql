CREATE TABLE ASSET_VEE_RULE 
(
  ID VARCHAR2(32 BYTE) NOT NULL, 
  NAME VARCHAR2(128 BYTE) NOT NULL, 
  MG_ID VARCHAR2(32 BYTE) NOT NULL, 
  CLASS_ID NUMBER(3, 0), 
  RU<PERSON>_DETAIL VARCHAR2(256 BYTE), 
  EVENT_ID VARCHAR2(32 BYTE), 
  RULE_TYPE NUMBER NOT NULL, 
  CONSTRAINT ASSET_VEE_RULE_PK PRIMARY KEY (ID,MG_ID)
);


CREATE TABLE DATA_VEE_EVENT 
(
  DEVICE_ID NVARCHAR2(32) NOT NULL, 
  TV DATE NOT NULL, 
  EVENT_ID NVARCHAR2(32) NOT NULL, 
  EVENT_DETAIL VARCHAR2(256 BYTE), 
  CONSTRAINT DATA_VEE_EVENT_PK PRIMARY KEY (DEVICE_ID, TV, EVENT_ID)
);

CREATE TABLE DICT_VEE_EVENT 
(
  ID VARCHAR2(32 BYTE) NOT NULL, 
  NAME VARCHAR2(128 BYTE) NOT NULL, 
  USER_DEFINE NUMBER(4, 0) NOT NULL, 
  METHOD VARCHAR2(32 BYTE) NOT NULL, 
  DATAITEM_ID VARCHAR2(64 BYTE), 
  DESCR VARCHAR2(256 BYTE), 
  SORT_ID NUMBER(4, 0), 
  CONSTRAINT DICT_VEE_EVENT_PK PRIMARY KEY (ID)
);

CREATE TABLE DICT_VEE_EVENT_PARAM 
(
  EVENT_ID VARCHAR2(32 BYTE) NOT NULL, 
  DATAITEM_ID VARCHAR2(64 BYTE) NOT NULL, 
  PRE_CYCLE NUMBER NOT NULL, 
  CYCLE_TYPE VARCHAR2(20 BYTE), 
  CONSTRAINT DICT_VEE_METHOD_PARAM_PK PRIMARY KEY(EVENT_ID, DATAITEM_ID, PRE_CYCLE)
); 

CREATE TABLE DICT_VEE_METHOD 
(
  ID VARCHAR2(32 BYTE) NOT NULL, 
  NAME VARCHAR2(64 BYTE), 
  SORT_ID NUMBER(4, 0), 
  TYPE NUMBER, 
  PACKAGE_ID VARCHAR2(256 BYTE), 
  METHOD_NAME VARCHAR2(256 BYTE), 
  DESCR VARCHAR2(1024 BYTE), 
  CONSTRAINT DICT_VEE_RULE_TYPE_PK PRIMARY KEY (ID)
);

