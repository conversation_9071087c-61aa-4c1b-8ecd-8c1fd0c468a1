/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictDataitem{ } 
 * 
 * 摘    要： 定炒数据/事件数据
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.dict;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.ExcelUtils;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.asset.ImportAssetGprsMeter;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;
import com.clou.esp.hes.app.web.model.dict.DictProtocol;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.dict.DictDataitemService;
import com.clou.esp.hes.app.web.service.dict.DictProtocolService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.excel.ExcelDataFormatter;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;
import com.power7000g.core.util.uuid.UUIDGenerator;

/**
 * <AUTHOR>
 * @时间：2017-11-22 03:30:03
 * @描述：定炒数据/事件数据类
 */
@Controller
@RequestMapping("/dictDataitemController")
public class DictDataitemController extends BaseController{

	public static final String channelTypeReplace ="1000:Rigister Data,1001:Active Energy Import,1002:Active Energy Export,1003:Reactive Energy Import,1004:Reactive Energy Export,"
			+ "2000:Instantaneous,2010:Power,2020:Voltage,2030:Current,2040:Power Factor,3000:Event";
	public static final String parseTypeReplace="bit-string:bit-string,date:date,date-time:date-time,double-long:double-long,double-long-unsigned:double-long-unsigned,"
			+ "enum:enum,float32:float32,long:long,long-unsigned:long-unsigned,octet-string:octet-string,special:special,unsigned:unsigned,visible-string:visible-string";
 	@Resource
    private DictDataitemService dictDataitemService;
 	@Resource
 	private DictProtocolService dictProtocolService;

	/**
	 * 跳转到定炒数据/事件数据列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
		List<DictProtocol> procols=dictProtocolService.getAllList();
		String proReplace=RoletoJson.listToReplaceStr(procols, "id", "name", ",");
		model.addAttribute("protocolReplace",proReplace);
		
		model.addAttribute("channelTypeReplace", channelTypeReplace);
		model.addAttribute("parseTypeReplace", parseTypeReplace);
        return new ModelAndView("/dict/dictDataitemList");
    }

	/**
	 * 跳转到定炒数据/事件数据新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dictDataitem")
	public ModelAndView dictDataitem(DictDataitem dictDataitem,HttpServletRequest request, Model model) {
		 if(StringUtil.isNotEmpty(dictDataitem.getId())&&StringUtil.isNotEmpty(dictDataitem.getProtocolId())){
            dictDataitem=dictDataitemService.get(dictDataitem);
			model.addAttribute("dictDataitem", dictDataitem);
		}
		
		List<DictProtocol> procols=dictProtocolService.getAllList();
		String proReplace=RoletoJson.listToReplaceStr(procols, "id", "name", ",");
		model.addAttribute("protocolReplace",proReplace);
		model.addAttribute("channelTypeReplace", channelTypeReplace);
		model.addAttribute("parseTypeReplace", parseTypeReplace);
		return new ModelAndView("/dict/dictDataitem");
	}


	/**
	 * 定炒数据/事件数据查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request,Boolean select,String id,String name,
    		String protocolCode,String protocolId,String dataitemType) {
		
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        if(select==null||!select) {
        	return j;
        }
        try {
    		jqGridSearchTo.put("id", id);
    		jqGridSearchTo.put("name", name);
    		jqGridSearchTo.put("protocolCode", protocolCode);
    		jqGridSearchTo.put("protocolId", protocolId);
    		jqGridSearchTo.put("dataitemType", dataitemType);
    		j=dictDataitemService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除定炒数据/事件数据信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DictDataitem dictDataitem,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dictDataitemService.deleteById(dictDataitem.getId())>0){
                j.setMsg("删除成功");
            }else{
                j.setSuccess(false);
                j.setMsg("删除失败");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
    
    /**
     * 保存定炒数据/事件数据信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DictDataitem dictDataitem,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DictDataitem t=new  DictDataitem();
        try {
	        if(StringUtil.isNotEmpty(dictDataitem.getOldId())){
	        	t.setId(dictDataitem.getOldId());
	        	t=dictDataitemService.get(t);
				MyBeanUtils.copyBeanNotNull2Bean(dictDataitem, t);
				//去空格
				t.setId(t.getId().trim());
				t.setProtocolCode(t.getProtocolCode()==null?"":t.getProtocolCode().trim());
				dictDataitemService.update(t);
				j.setMsg(MutiLangUtil.doMutiLang("system.updateSucc"));
			}else{
				//去空格
				dictDataitem.setId(dictDataitem.getId().trim());
				dictDataitem.setProtocolCode(dictDataitem.getProtocolCode()==null?"":dictDataitem.getProtocolCode().trim());
	            dictDataitemService.save(dictDataitem);
	            j.setMsg(MutiLangUtil.doMutiLang("system.addSucc"));
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
	
    /**
	 *  必须唯一
	 */
	@RequestMapping(value = "isExist")
	@ResponseBody
	public AjaxJson isExist(DictDataitem item,HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		StringBuffer sb = new StringBuffer();
		String oldId = item.getOldId();
		if(!item.getId().equals(oldId)) {
			DictDataitem tmp = this.dictDataitemService.get(item);
			if(tmp!=null) {
				sb.append(MutiLangUtil.doMutiLang("dictDataitem.idExist")+"<br/>");
			}
		}
		
  		if(sb.length()>0) {  
  			j.setSuccess(false);
  			j.setMsg(sb.toString());
  		}
  		return j;
	}
	
    /**
     * 导出模板
     * @param request
     * @param response
     * @throws IOException
     */
    @RequestMapping("downloadExcelTemplate")
	  public ResponseEntity<byte[]> downloadExcelTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException{
			String rootpath = request.getRealPath("/modules/meterTemplate/");//获取项目中的模板文件夹
			String fileName ="Channel_Batch_Template.xlsx"; 
			File file=new File(rootpath  +"/"+ fileName);  
			HttpHeaders headers = new HttpHeaders();    
			headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);  
			headers.set("Content-Disposition", "attachment; filename=\"" + fileName + "\"");  
			return new ResponseEntity<byte[]>(FileUtils.readFileToByteArray(file),headers, HttpStatus.OK);    
	  }
	  
	  
	  @RequestMapping(value  = "importFile")
	  public ModelAndView importFile(HttpServletRequest request, Model model) {
			return new ModelAndView("/asset/importChannel");
		}
	  
	  @RequestMapping("importChannelInfo")
	    @ResponseBody
	    public AjaxJson uploadImportMeterExcelAndCheckInfo(@RequestParam("excelTemplateFile") MultipartFile multfile, 
	    		HttpServletRequest request, HttpServletResponse response){
	    	AjaxJson json = new AjaxJson();
	        String fileName = multfile.getOriginalFilename();				// 获取文件名
	        String prefix = fileName.substring(fileName.lastIndexOf("."));	// 获取文件后缀

	        List<DictDataitem> meterList = new ArrayList<>();
	        SysUser su = TokenManager.getToken();
	        //如果后缀不是xls或者xlsx，则返回失败，不是一个正确的文件
	        if(".xls".equals(prefix) || ".xlsx".equals(prefix)){
	    		// 用uuid作为文件名，防止生成的临时文件重复
	    		File excelFile;
				try {
					excelFile = File.createTempFile(UUIDGenerator.generate(), prefix);
					multfile.transferTo(excelFile);
					// MultipartFile to File
					DictDataitem entity = new DictDataitem();
					ExcelUtils<DictDataitem> utils = new ExcelUtils<DictDataitem>(entity);
					ExcelDataFormatter edf = new ExcelDataFormatter();
					meterList = utils.readFromFile(edf, excelFile);
					//删除临时file文件
		    		this.deleteFile(excelFile);
					if(meterList.size() > 0){
						String validateStr = this.validateImport(meterList);
						if(StringUtils.isNotEmpty(validateStr)) {
							json.setSuccess(false);
			    			json.setMsg(validateStr);
						}else {
							for(DictDataitem item:meterList) {
								//如果channelId为空，跳过
								if(StringUtils.isEmpty(item.getId())) {
									continue;
								}
								//导入去除空格
								item.setId(item.getId().trim());
								item.setProtocolCode(item.getProtocolCode()==null?"":item.getProtocolCode().trim());
								this.dictDataitemService.saveOrUpdate(item);
							}
						}
					}else{
		    			json.setSuccess(false);
		    			json.setMsg(MutiLangUtil.doMutiLang("importGprsMeter.noDataInExcel"));
		    		}
		    		
	    		
				} catch (Exception e) {
					json.setSuccess(false);
					json.setMsg(MutiLangUtil.doMutiLang("importGprsMeter.verifyFail"));
					e.printStackTrace();
				}
	        }else{
	        	json.setSuccess(false);
	        	json.setMsg(MutiLangUtil.doMutiLang("importGprsMeter.selectFile"));
	        }
    	return json;
    }
	  
	  
	  public String validateImport(List<DictDataitem> items) {
		  StringBuffer sb = new StringBuffer();
		  for(int i =0;i<items.size();i++) {
			  DictDataitem item=items.get(0);
			  String tmp = "";
			  if(StringUtils.isEmpty(item.getProtocolId())) {
				  tmp+="Protocol ID can not be null,";
			  }
			  if(StringUtils.isEmpty(item.getId())) {
				  tmp+="Channel ID can not be null,";
			  }
			  if(StringUtils.isEmpty(item.getName())) {
				  tmp+="Channel Name can not be null,";
			  }
			  if(StringUtils.isEmpty(item.getProtocolCode())) {
				  tmp+="OBIS Code can not be null,";
			  }
			  if(StringUtils.isEmpty(item.getOpType())) {
				  tmp+="Operation Type can not be null,";
			  }
			  
			  if(tmp.length()>0) {
				  sb.append("Line"+(i+1)+":"+tmp+".");
				  sb.append("\r");
			  }
		  }
		  return sb.toString();
	  }
	  /**  
	     * 删除临时文件
	     * @param files  
	     */  
	    private void deleteFile(File... files) {  
	        for (File file : files) {  
	            if (file.exists()) {  
	                file.delete();  
	            }  
	        }  
	    }
}