function taskStatusCompleted () {
	var completed = parseInt($("#completed_span_id").html());
	$("#completed_span_id").html(completed + 1);
	var processing = parseInt($("#processing_span_id").html());
	$("#processing_span_id").html(processing - 1);	
}

function taskStatusSuccess () {
	var success = Number($("#success_span_id").text());
	$("#success_span_id").html(success+1);	
}

function taskStatusFailed () {
	var failed = Number($("#failed_span_id").text());
	$("#failed_span_id").html(failed+1);	
}

function getIPA(values) {
	// ip 
	var ip = values[0].value;
	$("#txtIpAddress").val(ip);
	
	// port
	var port = values[1].value;
	$("#txtPort").val(port);
	
	// backup ip
	var bakIp = values[2].value;
	$("#txtIpAddressBackup").val(bakIp);
	
	// backup port
	var bakPort = values[3].value;
	$("#txtPortBackup").val(bakPort);
	
	// apn
	var apn = values[4].value;
	$("#txtApn").val(apn);
}

function getUserInfo(structures) {

	
	var rowIds1 = $("#importantUserSettingList").jqGrid('getDataIDs');
	for(var k=0; k<rowIds1.length; k++) {
	//获取对应行的数据
	   var curRowData1 = jQuery("#importantUserSettingList").jqGrid('getRowData', rowIds1[k]);
	   curRowData1.isVipTxt ="";
	   $("#importantUserSettingList").setRowData(rowIds1[k], curRowData1);
	  
	}
	
	var pointNum = 0;
	for (var i=0; i<structures.length; i++) {
		for(var j=0; j<structures[i].values.length;j++){
			pointNum = structures[i].values[j].value;
		}
		var rowIds = $("#importantUserSettingList").jqGrid('getDataIDs');
		for(var k=0; k<rowIds.length; k++) {
		//获取对应行的数据
		   var curRowData = jQuery("#importantUserSettingList").jqGrid('getRowData', rowIds[k]);
		   curRowData.isVipTxt ="Yes";
		   if(curRowData.pointNum == pointNum){
			   $("#importantUserSettingList").setRowData(rowIds[k], curRowData);
		   }
		}
	}
}

function updateWriteOtherList(){
	
	var rowIdsAll = $("#importantUserSettingList").jqGrid('getDataIDs');
	for(var k=0; k<rowIdsAll.length; k++) {
	//获取对应行的数据
	   var curRowDataAll = jQuery("#importantUserSettingList").jqGrid('getRowData', rowIdsAll[k]);
	
	   if(curRowDataAll.keyFlag == 'Yes'){
		   curRowDataAll.isVipTxt ="Yes";
		   $("#importantUserSettingList").setRowData(rowIdsAll[k], curRowDataAll);
	   }else{
		   curRowDataAll.isVipTxt ="No";
		   $("#importantUserSettingList").setRowData(rowIdsAll[k], curRowDataAll);
		   
	   }
	   
	}

}

function getReset(values) {
	var reset = values[0].value;
	$("#selResetType").val(reset);
}

//显示抄读的表计参数记录
function showReadMeterInfo(obj) {

	if(obj == null || obj.structures.length <= 0) {
		return;
	}
	
	
	for (var i=0; i<obj.structures.length; i++) {
		var meterParam = obj.structures[i];
		//todo edison addRowData没有转
		if (meterParam.values.length < 14) {	//表计参数13个
			continue;
		}

		var meterParamValues = meterParam.values;
		var rowIdsAll = $("#dcuMeterList").jqGrid('getDataIDs');
		var meterSnDisplay = '';
		for(var k=0; k<rowIdsAll.length; k++) {
		//获取对应行的数据
	  	 var curRowDataAll = jQuery("#dcuMeterList").jqGrid('getRowData', rowIdsAll[k]);
	  	 if(meterParamValues[3].value == curRowDataAll.logicalName ){
	  	      meterSnDisplay = curRowDataAll.meterSn;
	  	      break;
	  	 }else{
	  		 meterSnDisplay = '';
	  	 }
	 	}
		
		var rowData = {meterSn: meterSnDisplay, 
					pointNum: meterParamValues[0].value, 
					status: meterParamValues[1].value,
					meterProperties: meterParamValues[2].value, 
					logicalName: meterParamValues[3].value,
					protocol: meterParamValues[4].value, 
					meterType : meterParamValues[5].value,
					totalDivType: meterParamValues[6].value, 
					keyFlag: meterParamValues[7].value, 
					feeRateCount: meterParamValues[8].value, 
					collectorAddress: meterParamValues[9].value, 
					comNum: meterParamValues[10].value, 
					baudRate: meterParamValues[11].value, 
					stopFlag: meterParamValues[12].value, 
					ct: meterParamValues[13].value, 
					pt: meterParamValues[14].value};
		$("#dcuMeterListRead").jqGrid("addRowData", meterParamValues[0].value, rowData, "last");
	}
	if(obj != null){
		for(var i=0; i<obj.length; i++){
			var rowData = {meterSn: obj[i].meterSn, pointNum: obj[i].pointNum, status: obj[i].status,
					meterProperties: obj[i].meterProperties, logicalName: obj[i].logicalName,
					protocol: obj[i].protocol, totalDivType: obj[i].totalDivType, keyFlag: obj[i].keyFlag
					, feeRateCount: obj[i].feeRateCount, collectorAddress: obj[i].collectorAddress, comNum: obj[i].comNum
					, baudRate: obj[i].baudRate, stopFlag: obj[i].stopFlag
					, ct: obj[i].ct, pt: obj[i].pt};
			$("#dcuMeterListRead").jqGrid("addRowData", obj[i].meterSn, rowData, "last");
		}
	}
}

function setSerialNumber(rowData,searchType){
	//
	var val = $("ul#dcu_config_ul li.active").attr("name");
	if(searchType=="Meter"){  //Meter  //Commnuicator
	}else if (searchType=="Commnuicator"){
	
		var dcuMeterListTitle = "集中器 SN:[ "+rowData.sn+" ] 计量点列表";
		$("div#gview_dcuMeterList div.ui-jqgrid-titlebar span.ui-jqgrid-title").html(dcuMeterListTitle);
		if(val=="1"){	
			$("#txtCommunicatorSn").val(rowData.sn);
			$("#txtCommunicatorId").val(rowData.id);
			dcuMeterListsearchOnEnterFn();
		}
		if(val == "2") {
			$("#txtCommunicationSn1").val(rowData.sn);
			$("#txtCommunicatorId1").val(rowData.id);
			$("#txtComType").val(rowData.comType);
			queryComType(rowData.comType);
			importantUserSettingListsearchOnEnterFn();
		}
	}
}

function queryComType(comType){
	layer.load();
	$.ajax({
		type : 'POST',
		url : getRootPathWeb() + '/dcuConfigurationController/queryComType.do',
		data : {
			comType : comType
		},
		dataType : 'json',
		success : function(data) {
			if(!data.success){
				middleTipMsg(data.msg,data.success);
				layer.closeAll('loading');
				return;
			}
			
			var comObj = data.obj;
			$("#txtComName").val(comObj.communication);
			
			layer.closeAll('loading');
		},
		error : function(msg) {
			window.parent.layer.msg(
					'<t:mutiLang lanKey="system.requestError" />', {
						icon : 2
					});
			layer.closeAll('loading');
		}
	});
}

function queryMeterInfoFun(){
	var communicatorId = $("#txtCommunicatorId").val();
		
	var data = {communicatorId:communicatorId};
	return data;
}

function queryImportantMeterInfoFun(){
	var communicatorId = $("#txtCommunicatorId1").val();
		
	var data = {communicatorId:communicatorId};
	return data;
}

function getBaudRate(){

	var  string = "1200:1200;";
	string += "2400:2400;";
	string += "9600:9600";

	
	return string;
}


function getStopFlag(){

	var  string = "1:1;";
	string += "0.5:0.5;";
	string += "1.5:1.5;";
	string += "2:2";
	
	return string;
}


function getIsVip(){

	var  string = "0:Normal User;";
	string += "1:Key User";
	return string;
}

function getMeterProperties(){

	var  string = "1:485表;";
	string += "2:模拟量;";
	string += "3:脉冲量;";
	string += "4:计算值;";
	string += "5:交流采样";
	return string;
}

function getMeterType(){

	var  string = "1:单相电子表;";
	string += "2:多功能表;";
	string += "3:其它类型;";
	string += "4:表箱终端;";
	string += "5:线路终端;";
	string += "6:监控终端";
	return string;
}

function getTotalDivType(){

	var  string = "0:用户表;";
	string += "1:总表";
	return string;
}

function getFeeRateCount(){

	var  string = "1:1;";
	string += "2:2;";
	string += "3:3;";
	string += "4:4;";
	string += "5:5;";
	string += "6:6;";
	string += "7:7;";
	string += "8:8";
	return string;
}




var myrowid;
/*
* 编辑user信息之后，点击其他位置，保存user信息到数据库
*/
function beforeSelectRowUser(rowid, e){
	//
	//var dgrv = $("#"+e.currentTarget.id).getGridParam('selrow'); ///获取最近一次选择的行
	var dgrv = myrowid; ///获取最近一次选择的行
	if(dgrv != null && dgrv != ""){
		if(rowid == dgrv){
			return ;
		}
		var rowData = $("#"+e.currentTarget.id).jqGrid("getRowData", dgrv);
//		if(rowData.name == null || rowData.name == ""){
//			middleErrorMsg(i18n.t("login.usernameNull"));
//			return ;
//		}
	 	$('#'+e.currentTarget.id).jqGrid('saveRow', dgrv,{
            keys : true,        //这里按[enter]保存  
            mtype : "POST",  
            restoreAfterError: true,  
            oneditfunc: function(rowid){  
              //编辑前处理方法
            },  
            successfunc: function(data){
	            //返回true记录为修改后的内容，为false内容还原
//            	var json = JSON.parse(data.responseText);
//            	if(json.success){
//            		middleSuccessMsg(json.msg);
//                }
            	lastsel="";
                return true;
            },  
            errorfunc: function(rowid, data){  
            	var json=JSON.parse(data.responseText);
            	middleErrorMsg(json.msg);
            }  
        });
	}
}

function editUserRowData(rowid,iRow,iCol,e){
	//双击前，重新查询下拉列表的数据项
	//
	$("#"+e.currentTarget.id).setColProp("baudRate",{
        edittype:"select",
        editoptions:{value:getBaudRate()},
        formatter:"select" 
     });  
	$("#"+e.currentTarget.id).setColProp("isVip",{
        edittype:"select",
        editoptions:{value:getIsVip()},
        formatter:"select" 
     });    
    $('#'+e.currentTarget.id).jqGrid('editRow',rowid,true);
    myrowid = rowid;

	var rowData = $("#"+e.currentTarget.id).jqGrid("getRowData", rowid);
    $('#'+e.currentTarget.id).jqGrid('editRow',rowid,{
        keys : true,        //这里按[enter]保存  
        mtype : "POST",  
        restoreAfterError: true,  
        oneditfunc: function(rowid){  
          //编辑前处理方法
        },  
        successfunc: function(data){
            //返回true记录为修改后的内容，为false内容还原
        	var json=JSON.parse(data.responseText);
        	if(json.success){
        		middleSuccessMsg(json.msg);
            }
        	lastsel="";
            return json.success;
        },  
        errorfunc: function(rowid, data){  
        	var json=JSON.parse(data.responseText);
        	middleErrorMsg(json.msg);
        }  
    });  
}

/**
 * 通过UCI接口获取终端表计参数数据
 * @returns
 */
var meterReadIndex = 0;
var meterReadArray = new Array();
function meterInfoGetFunc(){


	var ids = $('#dcuMeterList').jqGrid('getDataIDs');
	var txtCommunicatorSnVal = $('#txtCommunicatorSn').val();
	if(null==txtCommunicatorSnVal||""==txtCommunicatorSnVal||txtCommunicatorSnVal.length==0){
	 
		layer.msg(i18n.t("dcuConfiguration300.seleCommunicator"), function() {
		});
			return;
	}
	
	if(null==ids||""==ids||ids.length==0){
	 
		layer.msg(i18n.t("dcuConfiguration300.seleLeastOne"), function() {
		});
		return;
	}
	
	var pointNumEnd = $("#point_num_end_txt").val();
	var pointNumStart = $("#point_num_start_txt").val();
	
	if(!isDcuIntNum(pointNumStart) || !isDcuIntNum(pointNumEnd))
 	{
 　		layer.msg(i18n.t("dcuConfiguration300.pleaseEnterPositiveInt"), function() {
		});
			return;
 	}
 	
 	if(parseInt(pointNumEnd)<parseInt(pointNumStart))
 	{
 　		layer.msg(i18n.t("dcuConfiguration300.beginGreaterThanEnd"), function() {
		});
			return;
 	}
 	
 	var pointNumEndInt = parseInt($("#point_num_end_txt").val());
	var pointNumStartInt =  parseInt($("#point_num_start_txt").val());
	
	
	initGetSetShowInfo("读取表计信息",false);	
	$("#dcuMeterListRead").jqGrid("clearGridData");
	meterReadIndex = 0;
	meterReadArray = [];
	var meterIdString = "";	

	for(var i=pointNumStartInt; i<=pointNumEndInt; i++){
		meterIdString = meterIdString+i+",";
		if ((i+1)%10 == 0) {
			//每次读取10个表计参数
			meterIdString = meterIdString.substring(0, meterIdString.length-1);
			meterReadArray.push(meterIdString);
			meterIdString = "";
		}		
	}
	if (meterIdString.length > 0) {
		meterIdString = meterIdString.substring(0, meterIdString.length-1);	
		meterReadArray.push(meterIdString);		
	}
	if (meterReadArray.length > 0) {
		meterIdString = meterReadArray[0];
	}
	readNextArrayMeterParam(meterIdString);
}

//每10个表计参数分组抄读
function readNextArrayMeterParam(meterIdString){

	var meterIdArray = meterIdString.split(',');
	var params = new Array();	
	for(var i=0; i<=meterIdArray.length; i++){

		params[i] = meterIdArray[i];
	}
	ajaxGetDcuMeterParameters(params);	
	meterReadIndex++;
}

function ajaxGetDcuMeterParameters(params){

	var commId = $("#txtCommunicatorId").val();	
	var commSn = $("#txtCommunicatorSn").val();
	
	
	layer.load();
	$.ajax({
		type : 'POST',
		url : getRootPathWeb() + '/dcuConfigurationController/ajaxGetDcuMeterParameters300.do',
		data : {
			params : params,
			commId : commId,
			commSn : commSn
		},
		dataType : 'json',
		success : function(data) {
			if(!data.success){
				middleTipMsg(data.msg,data.success);
				layer.closeAll('loading');
				return;
			}
			
			layer.closeAll('loading');			
		},
		error : function(msg) {
			window.parent.layer.msg(
					'<t:mutiLang lanKey="system.requestError" />', {
						icon : 2
					});
			layer.closeAll('loading');
			var failed = parseInt($("#failed_span_id").html());
			$("#failed_span_id").html(failed + 1);
			var completed = parseInt($("#completed_span_id").html());
			$("#completed_span_id").html(completed + 1);
			var processing = parseInt($("#processing_span_id").html());
			$("#processing_span_id").html(processing - 1);			
		}
	});	
}

function isDcuIntNum(val){
   
      var varInt = parseInt(val);
 
　　　　if (!(/(^[1-9]\d*$)/.test(varInt))) { 
　　　　　　return false; 
　　　　}else { 
　　　　　  return true;
　　　　} 
　　 
    
}


function initGetSetShowInfo(taskType,flag) {

	var commSn = $("#txtCommunicatorSn").val();	
	var commId = $("#txtCommunicatorId").val();
	var re = /\S/;
	if(!re.test(commSn)){
		layer.msg(i18n.t("dcuConfiguration.pleaseChooseCommSn"), function() {
		});
		return null;		
	}
	
	//显示任务执行状态
	var myDate = new Date();
	$("#resultList").jqGrid("clearGridData");
	var rowData1 = {
			id:commId,
			communicatorSn:commSn,
			command:taskType,
			status:'Processing',
			requestTime:myDate.Format("dd-MM-yyyy HH:mm:ss")
	};
	$("#resultList").jqGrid("addRowData", commId, rowData1, "last");		
	//任务状态调初始化
	$("#total_span_id").html(1);
	$("#completed_span_id").html(0);
	$("#success_span_id").html(0);
	$("#failed_span_id").html(0);
	$("#processing_span_id").html(1);
	
	handleNormalParameterJqGrid('',flag);
}

/**
 * 通过UCI接口下发终端表计参数数据
 * @returns
 */
function meterInfoSetFunc(){

	initGetSetShowInfo("写入表计信息",true);
	var ids = $('#dcuMeterList').jqGrid('getGridParam','selarrrow');
	if(null==ids||""==ids||ids.length==0){
		layer.msg(i18n.t("dcuConfiguration300.seleLeastOne"), function() {
		});
		return;
	}	
	var params = new Array();
	$("#dcuMeterListRead").jqGrid("clearGridData");		//清空表格数据		
	for(var i=0; i<ids.length; i++){
		var rowData = $("#dcuMeterList").jqGrid('getRowData',ids[i]);
		
		var string =
		rowData.id+","+rowData.meterSn+","+rowData.pointNum+","+rowData.status+","+rowData.meterProperties
		+","+rowData.logicalName+","+rowData.protocol+","+rowData.meterType
		+","+rowData.totalDivType+","+rowData.keyFlag+","+rowData.feeRateCount
		+","+rowData.collectorAddress+","+rowData.comNum+","+rowData.baudRate+","+rowData.stopFlag+","+rowData.ct+","+rowData.pt;
		params[i] = string;
	}
	ajaxMeterInfoSetFunc(params);	
}

/**
 * 参数传值到后台
 * @param params 参数
 */
function ajaxMeterInfoSetFunc(params){
	var commId = $("#txtCommunicatorId").val();
	var commSn = $("#txtCommunicatorSn").val();	
	layer.load();
	$.ajax({
		type : 'POST',
		url : getRootPathWeb() + '/dcuConfigurationController/ajaxSetDcuMeterParameters300.do',
		data : {
			params : params,
			commId : commId,
			commSn : commSn
		},
		dataType : 'json',
		success : function(data) {
			if(!data.success){
				middleTipMsg(data.msg,data.success);
				layer.closeAll('loading');
				return;
			}
			
			layer.closeAll('loading');
		},
		error : function(msg) {
			window.parent.layer.msg(
					'<t:mutiLang lanKey="system.requestError" />', {
						icon : 2
					});
			layer.closeAll('loading');
		}
	});
}

function veriffCommuSn(){
	//
	var re = /\S/;
	if(!re.test($("#txtCommunicationSn1").val())){
		$("#snErr1").show();
		$("#snSpan1").text(i18n.t("dcuConfiguration.pleaseChooseCommSn"));
		return 1;
	}
	
	$("#snErr1").hide();
	$("#snSpan1").text("");
	
	return 0;
}

function veriffIp(flag){
	//
	flag = flag == undefined ? true : false;
	var re = /((25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))/;
	var ipval = $("#txtIpAddress").val();
	if(flag){//get
		if(null == ipval || "" == ipval){
			$("#ipErr1").hide();
			$("#ipSpan1").text("");
			return 0;
		}
	} else {
		if(null == ipval || "" == ipval){
			$("#ipErr1").show();
			$("#ipSpan1").text(i18n.t("dcuConfiguration.pleaseFillIn"));
			return 1;
		}
	}
	
	if(!re.test(ipval)){
		$("#ipErr1").show();
		$("#ipSpan1").text(i18n.t("dcuConfiguration.pleaseFillInCorrect"));
		return 1;
	}
	
	$("#ipErr1").hide();
	$("#ipSpan1").text("");
	
	return 0;
}

function veriffPort(flag){
	//
	flag = flag == undefined ? true : false;
	var re = /^(\d|[1-9]\d{1,3}|[1-5]\d{4}|6([0-4]\d{3}|5([0-4]\d{2}|5([0-2]\d|3[0-4]))))|65535$/;
	var ipval = $("#txtPort").val();
	if(flag){//get
		if(null == ipval || "" == ipval){
			$("#portErr1").hide();
			$("#portSpan1").text("");
			return 0;
		}
	}else{
		if(null == ipval || "" == ipval){
			$("#portErr1").show();
			$("#portSpan1").text(i18n.t("dcuConfiguration.pleaseFillIn"));
			return 1;
		}
	}
	
	if(!re.test(ipval)){
		$("#portErr1").show();
		$("#portSpan1").text(i18n.t("dcuConfiguration.pleaseFillInCorrect"));
		return 1;
	}
	
	$("#portErr1").hide();
	$("#portSpan1").text("");
	
	return 0;
}

function veriffIpBak(flag){
	//
	flag = flag == undefined ? true : false;
	var re = /((25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))/;
	var ipval = $("#txtIpAddressBackup").val();
	if(flag){//get
		if(null == ipval || "" == ipval){
			$("#ipErrbak").hide();
			$("#ipSpanbak").text("");
			return 0;
		}
	} else {
		if(null == ipval || "" == ipval){
			$("#ipErrbak").show();
			$("#ipSpanbak").text(i18n.t("dcuConfiguration.pleaseFillIn"));
			return 1;
		}
	}
	
	if(!re.test(ipval)){
		$("#ipErrbak").show();
		$("#ipSpanbak").text(i18n.t("dcuConfiguration.pleaseFillInCorrect"));
		return 1;
	}
	
	$("#ipErrbak").hide();
	$("#ipSpanbak").text("");
	
	return 0;
}

function veriffPortBak(flag){
	//
	flag = flag == undefined ? true : false;
	var re = /^(\d|[1-9]\d{1,3}|[1-5]\d{4}|6([0-4]\d{3}|5([0-4]\d{2}|5([0-2]\d|3[0-4]))))|65535$/;
	var ipval = $("#txtPortBackup").val();
	
	if(flag){//get
		if(null == ipval || "" == ipval){
			$("#portErrbak").hide();
			$("#portSpanbak").text("");
			return 0;
		}
	} else {
		if(null == ipval || "" == ipval){
			$("#portErrbak").show();
			$("#portSpanbak").text(i18n.t("dcuConfiguration.pleaseFillIn"));
			return 1;
		}
	}
	
	if(!re.test(ipval)){
		$("#portErrbak").show();
		$("#portSpanbak").text(i18n.t("dcuConfiguration.pleaseFillInCorrect"));
		return 1;
	}
	
	$("#portErrbak").hide();
	$("#portSpanbak").text("");
	
	return 0;
}

function veriffApn(flag){
	flag = flag == undefined ? true : false;
	var re = /^[^ ]+$/;
	var ipval = $("#txtApn").val();
	
	if(flag){//get
		if(null == ipval || "" == ipval){
			$("#apnErr").hide();
			$("#apnSpan").text("");
			return 0;
		}
	} else {
		if(null == ipval || "" == ipval){
			$("#apnErr").show();
			$("#apnSpan").text(i18n.t("dcuConfiguration.pleaseFillIn"));
			return 1;
		}
	}
	
	if(!re.test(ipval)){
		$("#apnErr").show();
		$("#apnSpan").text(i18n.t("dcuConfiguration.pleaseFillInCorrect"));
		return 1;
	}
	
	$("#apnErr").hide();
	$("#apnSpan").text("");
	
	return 0;
}


/**
 * 通过UCI接口获取终端参数数据
 * @returns
 */
function getFunc(){

otherStatusLineInit();
	if($("#autoImportant").val() == 1){
		
		handleOtherParameterJqGrid("Get ",false ,3);
		ajaxGetOtherParameters();
	}else if($("#autoResetCommand").val() == 1){
		middleTipMsg(i18n.t("dcuConfiguration.resetTypeCannotBeRead"));
		return;
	}else if($("#autoCommunication").val() == 1){
		var index = veriffCommuSn();
		if(index ==0){
			handleOtherParameterJqGrid("Get ",false ,1);
			ajaxGetOtherParameters();
		}
	}
	
}

function ajaxGetOtherParameters(){
	var commSn = $("#txtCommunicationSn1").val();
	var commId = $("#txtCommunicatorId1").val();
	
	if(null==commSn||""==commSn){
		middleTipMsg(i18n.t("dcuConfiguration.selectCommunicators"));
		
		return;
	}
	
	var sendType = 0;
		
	if($("#autoImportant").val() == 1){
		sendType = 3;
	}else if($("#autoResetCommand").val() == 1){
		sendType = 2;
	}else if($("#autoCommunication").val() == 1){
		sendType = 1;
	}
	
	
	layer.load();
	$.ajax({
		type : 'POST',
		url : getRootPathWeb() + '/dcuConfigurationController/ajaxGetOtherParameters.do',
		data : {
			commSn : commSn,
			commId : commId,
			sendType :sendType
		},
		dataType : 'json',
		success : function(data) {
			if(!data.success){
				middleTipMsg(data.msg,data.success);
				layer.closeAll('loading');
				return;
			}
			
			layer.closeAll('loading');
		},
		error : function(msg) {
			window.parent.layer.msg(
					'<t:mutiLang lanKey="system.requestError" />', {
						icon : 2
					});
			layer.closeAll('loading');
		}
	});
}

function setFunc(){
	otherStatusLineInit();
	if($("#autoImportant").val() == 1){
		handleOtherParameterJqGrid("Set ",true ,3);
		ajaxSetOtherParameters();
	}else if($("#autoResetCommand").val() == 1){
		handleOtherParameterJqGrid("Set ",true ,2);
		ajaxSetOtherParameters();

	}else if($("#autoCommunication").val() == 1){
		var index = veriffCommuSn();
		index += veriffIp(false);
		index += veriffPort(false);
		index += veriffIpBak(false);
		index += veriffPortBak(false);
		index += veriffApn(false);
		
		if(index ==0){
			handleOtherParameterJqGrid("Set ",true ,1);
			ajaxSetOtherParameters();
		}
	}
	
	
}

function ajaxSetOtherParameters(){
	var commSn = $("#txtCommunicationSn1").val();
	var commId = $("#txtCommunicatorId1").val();
	var ip = $("#txtIpAddress").val();
	var port = $("#txtPort").val();
	var ipBak = $("#txtIpAddressBackup").val();
	var portBak = $("#txtPortBackup").val();
	var apn = $("#txtApn").val();
	var resetType = 0;
	var sendType = 0;


	var ids = [];
	var rowIdsAll = $("#importantUserSettingList").jqGrid('getDataIDs');
	for(var k=0; k<rowIdsAll.length; k++) {
	//获取对应行的数据
	   var curRowDataAll = jQuery("#importantUserSettingList").jqGrid('getRowData', rowIdsAll[k]);
	
	   if(curRowDataAll.keyFlag == 'Yes'){
		   ids.push(rowIdsAll[k]);
	   }
	   
	}
	
	var params = new Array();

	
	if(null==port||""==port){
		port = 0;
	}
	if(null==ipBak||""==ipBak){
		ipBak = 0;
	}
	if(null==portBak||""==portBak){
		portBak = 0;
	}
	
	if(null==commSn||""==commSn){
		middleTipMsg(i18n.t("dcuConfiguration.selectCommunicators"));
		
		return;
	}
	
	if($("#autoImportant").val() == 1){
		resetType = 0;
		sendType = 3;

		if(null==ids||""==ids|| ids.length == 0){
			middleTipMsg("Please select the meter that you want to set as a key user.");
			return;
		}
		

		for(var i=0; i<ids.length; i++){
			var rowData = $("#importantUserSettingList").jqGrid('getRowData',ids[i]);
			var string = rowData.id+","+rowData.meterSn+","+rowData.pointNum;
			params[i] = string;
		}
		
	}else if($("#autoResetCommand").val() == 1){
		resetType = $('#selResetType option:selected').val();
		if(resetType == 0){
			otherStatusLineInit();
			
			middleTipMsg(i18n.t("dcuConfiguration.selectResetType"));
			
			return
		}
		sendType = 2;
	}else if($("#autoCommunication").val() == 1){
		resetType = 0;
		sendType = 1;
	}
	
	layer.load();
	$.ajax({
		type : 'POST',
		url : getRootPathWeb() + '/dcuConfigurationController/ajaxSetOtherParameters.do',
		data : {
			commSn : commSn,
			commId : commId,
			ip : ip,
			port : port,
			ipBak : ipBak,
			portBak : portBak,
			apn : apn,
			resetType : resetType,
			sendType : sendType,
			params : params
		},
		dataType : 'json',
		success : function(data) {
			if(!data.success){
				middleTipMsg(data.msg,data.success);
				layer.closeAll('loading');
				return;
			}
			
			layer.closeAll('loading');
		},
		error : function(msg) {
			window.parent.layer.msg(
					'<t:mutiLang lanKey="system.requestError" />', {
						icon : 2
					});
			layer.closeAll('loading');
		}
	});
}

//todo other 设置状态栏
function handleOtherParameterJqGrid(title,flag,tabFlag){

	var commParameterTitle = "HES Comm Parameter";
	
	if(!flag){
		if(tabFlag == 1){
			commParameterTitle =title+"Comm Parameter";
		}else if(tabFlag == 3){
			commParameterTitle =title+"Important User";
		}
	}else{
		if(tabFlag == 1){
			commParameterTitle =title+"Comm Parameter";
		}else if(tabFlag == 2){
			commParameterTitle =title+"Reset Type";
		}
		else if(tabFlag == 3){
			commParameterTitle =title+"Important User";
		}
	}

	var commId = $("#txtCommunicatorId1").val();
	var communication = $("#txtComName").val();
	var commSn = $("#txtCommunicationSn1").val();

	var myDate = new Date();
	
	var  requestTime = myDate.Format($("#dcuJsDateAndTimeFormatter").val());

	$("#otherCommand").html(commParameterTitle);
	
	var proccesingI18n = i18n.t("limiterList.processing");
	var proccessingStatusHtml = '<a style=\'color:#0000C6\' onclick="openLogExplorer(\''
	+ commId + '\',\'' + commSn + '\',\''+requestTime+'\');">'+proccesingI18n+'</a>';
	
	$("#otherStatus").html(proccessingStatusHtml);
	$("#otherRequestTime").html(requestTime);
	
	
}


//todo Normal 设置状态栏
function handleNormalParameterJqGrid(title,flag){

	var writeTitle = "写入表计信息.";
	var readTitle = "读取表计信息.";
	
	var commSn = $("#txtCommunicatorSn").val();
	var commId = $("#txtCommunicatorId").val();
	
	if(!flag){
		$("#normalCommand").html(readTitle);
	}else{
		$("#normalCommand").html(writeTitle);
	}


	var myDate = new Date();
	
	var  requestTime = myDate.Format($("#dcuJsDateAndTimeFormatter").val());

	var proccesingI18n = i18n.t("limiterList.processing");
	var proccessingStatusHtml = '<a style=\'color:#0000C6\' onclick="openLogExplorer(\''
	+ commId + '\',\'' + commSn + '\',\''+requestTime+'\');">'+proccesingI18n+'</a>';
	
	$("#normalStatus").html(proccessingStatusHtml);
	$("#normalRequestTime").html(requestTime);
	
	
}

function normalStatusLineInit(){
	$("#normalResponseTime").html('');
	$("#normalRequestTime").html('');
	$("#normalCommand").html('');
	$("#normalStatus").html('');
	$("#normalReason").html('');

}

function otherStatusLineInit(){
	$("#otherResponseTime").html('');
	$("#otherRequestTime").html('');
	$("#otherCommand").html('');
	$("#otherStatus").html('');
	$("#otherReason").html('');

}
