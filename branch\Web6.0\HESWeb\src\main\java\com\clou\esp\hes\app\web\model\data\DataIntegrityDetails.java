/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataIntegrity{ } 
 * 
 * 摘    要： dataIntegrity
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-12-01 06:03:45
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.data;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.google.common.collect.Maps;
import com.power7000g.core.excel.Excel;

public class DataIntegrityDetails  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DataIntegrityDetails() {
	}
	
	/***设备SN*/
	@Excel(name = "Serial Number", width = 15, groups = ValidGroup1.class)
	private String serizlNo;
	@Excel(name = "Time", width = 24, groups = ValidGroup1.class)
	private Date tv;
	@Excel(name = "Manufacturer", width = 12, groups = ValidGroup1.class)
	private String manufacturer;
	@Excel(name = "Model", width = 12, groups = ValidGroup1.class)
	private String model;
	@Excel(name = "Communication", width = 12, groups = ValidGroup1.class)
	private String communication;
	@Excel(name = "Profile", width = 20, groups = ValidGroup1.class)
	private String profile;
	@Excel(name = "Read Status", width = 10, groups = ValidGroup1.class)
	private String readStatus;

	public String getSerizlNo() {
		return serizlNo;
	}
	public void setSerizlNo(String serizlNo) {
		this.serizlNo = serizlNo;
	}
	public Date getTv() {
		return tv;
	}
	public void setTv(Date tv) {
		this.tv = tv;
	}
	
	public String getManufacturer() {
		return manufacturer;
	}
	public void setManufacturer(String manufacturer) {
		this.manufacturer = manufacturer;
	}
	public String getModel() {
		return model;
	}
	public void setModel(String model) {
		this.model = model;
	}
	public String getCommunication() {
		return communication;
	}
	public void setCommunication(String communication) {
		this.communication = communication;
	}
	public String getProfile() {
		return profile;
	}
	public void setProfile(String profile) {
		this.profile = profile;
	}
	public String getReadStatus() {
		return readStatus;
	}
	public void setReadStatus(String readStatus) {
		this.readStatus = readStatus;
	}
	public DataIntegrityDetails(String serizlNo, Date tv, String manufacturer, String model, String communication,
			String profile, String readStatus) {
		super();
		this.serizlNo = serizlNo;
		this.tv = tv;
		this.manufacturer = manufacturer;
		this.model = model;
		this.communication = communication;
		this.profile = profile;
		this.readStatus = readStatus;
	}

	public Map<String,String> getMap(SimpleDateFormat sdf ) {
		Map<String,String> map = Maps.newHashMap();
		map.put("serizlNo", serizlNo);
		map.put("tv", sdf.format(tv));
		map.put("manufacturer",manufacturer );
		map.put("model", model);
		map.put("serizlNo", serizlNo);
		map.put("communication", communication);
		map.put("profile",profile );
		map.put("readStatus",readStatus );
		
		return map;
	}
}