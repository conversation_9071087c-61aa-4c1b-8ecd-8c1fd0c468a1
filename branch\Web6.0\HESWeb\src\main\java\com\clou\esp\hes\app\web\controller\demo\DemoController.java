/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class TestUser{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-03-29 01:14:36
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.demo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.tag.vo.JqTreeUtil;
import com.clou.esp.hes.app.web.core.tag.vo.ZtreeNode;
import com.clou.esp.hes.app.web.core.tag.vo.ZtreeUtil;
import com.clou.esp.hes.app.web.model.asset.OnDemandRead;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;

/**
 * 
 * <AUTHOR>
 * 
 */
@Controller
@RequestMapping("/demoController")
public class DemoController extends BaseController {

	/**
	 * 临时首页地址
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "demo")
	public ModelAndView demo(HttpServletRequest request, Model model) {
		return new ModelAndView("/demo/demo");
	}
	
	@RequestMapping(value = "firmwareManagement")
	public ModelAndView firmwareManagement(HttpServletRequest request, Model model) {
		return new ModelAndView("/report/Firmware_Management_Upgrade");
	}
	
	@RequestMapping(value = "onDemandReads")
	public ModelAndView onDemandReads(HttpServletRequest request, Model model) {
		return new ModelAndView("/report/On_Demand_Reads");
	}
	
	@RequestMapping(value = "missDataTracingDetails")
	public ModelAndView dataEvent(HttpServletRequest request, Model model) {
		return new ModelAndView("/report/Miss_Data_Tracing_Details");
	}
	
	@RequestMapping(value = "missDataTracing")
	public ModelAndView meterEvent(HttpServletRequest request, Model model) {
		return new ModelAndView("/report/Miss_Data_Tracing");
	}
	
	@RequestMapping(value = "scheduleReadsReport")
	public ModelAndView scheduleReadsReport(HttpServletRequest request, Model model) {
		return new ModelAndView("/report/Schedule_Reads_Report");
	}
	@RequestMapping(value = "scheduleReadsReport1")
	public ModelAndView scheduleReadsReport1(HttpServletRequest request, Model model) {
		return new ModelAndView("/report/Schedule_Reads_Report1");
	}
	@RequestMapping(value = "scheduleReadsReport2")
	public ModelAndView scheduleReadsReport2(HttpServletRequest request, Model model) {
		return new ModelAndView("/report/Schedule_Reads_Report2");
	}
	
	@RequestMapping(value = "scheduleReadsReport3")
	public ModelAndView scheduleReadsReport3(HttpServletRequest request, Model model) {
		return new ModelAndView("/report/Schedule_Reads_Report3");
	}
	
	@RequestMapping(value = "scheduleReadsReport4")
	public ModelAndView scheduleReadsReport4(HttpServletRequest request, Model model) {
		return new ModelAndView("/report/Schedule_Reads_Report4");
	}
	
	@RequestMapping(value = "meterGroupMgmtDemo")
	public ModelAndView meterGroupMgmtDemo(HttpServletRequest request, Model model) {
		return new ModelAndView("/report/Meter_Group_Mgmt");
	}
	
	@RequestMapping(value = "addGroupDemo")
	public ModelAndView addGroupDemo(HttpServletRequest request, Model model) {
		return new ModelAndView("/report/add_group");
	}
	
	@RequestMapping(value = "meterConfiguration")
	public ModelAndView meterConfiguration(HttpServletRequest request, Model model) {
		return new ModelAndView("/report/add_group");
	}
	
	@RequestMapping(value = "getZtree")
	@ResponseBody
	public AjaxJson getZtree(String searchName,HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		List<Map<String, Object>> list = new ArrayList<>();
		for (int i = 0; i <= 10; i++) {
			Map<String, Object> m = new HashMap<String, Object>();
			m.put("id", "id" + i);
			m.put("name", "张三" + i);
			m.put("sex", i % 2 == 1 ? "男" : "女");
			m.put("birthday", DateUtils.date2Str(DateUtils.datetimeFormat));
			m.put("classes", i);
			m.put("grade", i + "年级");
			m.put("results", i * i + 1);
			m.put("testCount", i * i + i * 3 - 5);
			m.put("parent", "");
			m.put("expanded", true);
			List<Map<String, Object>> listk = new ArrayList<>();
			for (int jk = 0; jk < 5; jk++) {
				Map<String, Object> ms = new HashMap<String, Object>();
				ms.put("id", "id" + i + (jk + 10));
				ms.put("name", "王麻子" + (char) ('a' + jk) + jk);
				ms.put("sex", jk % 2 == 1 ? "男" : "女");
				ms.put("birthday", DateUtils.date2Str(DateUtils.datetimeFormat));
				ms.put("classes", jk);
				ms.put("grade", jk + "年级");
				ms.put("results", jk * jk + 1);
				ms.put("testCount", jk * jk + jk * 3 - 5);
				ms.put("parent", "id" + i);
				ms.put("expanded", true);
				if(!StringUtil.isNotEmpty(searchName)){
					List<Map<String, Object>> listkk = new ArrayList<>();
					for (int kk = 0; kk < 3; kk++) {
						Map<String, Object> msk = new HashMap<String, Object>();
						msk.put("id", "id" + i + (jk + 10) + kk);
						msk.put("name", "李四" + (char) ('a' + kk) + kk);
						msk.put("sex", kk % 2 == 1 ? "男" : "女");
						msk.put("birthday",
								DateUtils.date2Str(DateUtils.datetimeFormat));
						msk.put("classes", kk);
						msk.put("grade", kk + "年级");
						msk.put("results", kk * kk + 1);
						msk.put("testCount", kk * jk + kk * 3 - 5);
						msk.put("parent", "id" + i + (jk + 10));
						msk.put("expanded", true);
						listkk.add(msk);
					}
					ms.put("list", listkk);
				}
				listk.add(ms);
			}
			m.put("list", listk);
			list.add(m);
		}
		ZtreeUtil jtu = new ZtreeUtil();
		jtu.setIdFname("id");
		jtu.setNameFname("name");
		jtu.setpIdFname("parent");
		jtu.setSubsetFieldName("list");
		jtu.setOpenFname("expanded");
		j.setObj(jtu.getZtreeNodeData(list));
		return j;
	}
	/**
	 * jqgrid树形结构测试假数据
	 * @param searchName
	 * @param jqGridSearchTo
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getJqGridZtree")
	@ResponseBody
	public JqGridResponseTo getJqGridZtree(String searchName,JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
		JqGridResponseTo j = null;
		List<Map<String, Object>> list = new ArrayList<>();
		for (int i = 1; i <= 1; i++) {
			Map<String, Object> m = new HashMap<String, Object>();
			m.put("id", "id" + i);
			m.put("name", "Week" + i);
			m.put("results", "");
			m.put("parent", "");
			m.put("expanded", false);
			List<Map<String, Object>> listk = new ArrayList<>();
			for (int jk = 0; jk < 7; jk++) {
				Map<String, Object> ms = new HashMap<String, Object>();
				ms.put("id", "id" + i + (jk + 10));
				ms.put("name", getWeeks(""+(jk+1)));
				ms.put("results", jk);
				ms.put("parent", "id" + i);
				ms.put("expanded", true);
				listk.add(ms);
			}
			m.put("list", listk);
			list.add(m);
		}
		PageInfo<Map<String, Object>> pageInfo = new PageInfo<Map<String, Object>>();
		pageInfo.setList(list);
         j= JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
		JqTreeUtil jtu = new JqTreeUtil();
		jtu.setFields(jqGridSearchTo.getField());
		jtu.setIdFieldName("id");
		jtu.setParentFieldName("parent");
		jtu.setSubsetFieldName("list");
		jtu.setExpandedFieldName("expanded");
		j.setRows(jtu.getTreeGridData(list));
		System.out.println(j.toJSONString());
		return j;
	}
	
	public String getWeeks(String i){
		String str="";
		switch (i) {
		case "1":
			str="Sunday";
			break;
		case "2":
			str="Monday";
			break;
		case "3":
			str="Tuesday";
			break;
		case "4":
			str="Wednesday";
			break;
		case "5":
			str="Thursday";
			break;
		case "6":
			str="Friday";
			break;
		case "7":
			str="Saturday";
			break;
		}
		return str;
	}
	
	
	
	@RequestMapping(value = "findTestUserById")
	@ResponseBody
	public List<ZtreeNode> findTestUserById(String id) {
		List<ZtreeNode> treeList = new ArrayList<ZtreeNode>();
		System.out.println(id);
		if (StringUtil.isEmpty(id)||id.equals("0")) {
			for (int i = 0; i < 10; i++) {
				ZtreeNode zn = new ZtreeNode();
				zn.setId("a" + i);
				zn.setpId(id);
				zn.setName("一级" + i);
				zn.setIsParent(true);
				treeList.add(zn);
			}
		} else if (id.indexOf("a") >= 0) {
			for (int i = 0; i < 10; i++) {
				ZtreeNode zn = new ZtreeNode();
				zn.setId("b" + i);
				zn.setpId(id);
				zn.setName(id + "二级" + i);
				zn.setIsParent(true);
				treeList.add(zn);
			}
		} else if (id.indexOf("b") >= 0) {
			for (int i = 0; i < 10; i++) {
				ZtreeNode zn = new ZtreeNode();
				zn.setId("c" + i);
				zn.setpId(id);
				zn.setName(id + "三级" + i);
				zn.setIsParent(false);
				treeList.add(zn);
			}
		}
		return treeList;
	}

}