/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetScheduleSchemeDetail{ } 
 * 
 * 摘    要： 采集方案明细
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-03-06 03:10:48
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.asset;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.Globals;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.model.asset.AssetScheduleScheme;
import com.clou.esp.hes.app.web.model.asset.AssetScheduleSchemeDetail;
import com.clou.esp.hes.app.web.model.dict.DictProfile;
import com.clou.esp.hes.app.web.service.asset.AssetScheduleSchemeDetailService;
import com.clou.esp.hes.app.web.service.asset.AssetScheduleSchemeService;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.dict.DictProfileService;

/**
 * <AUTHOR>
 * @时间：2018-03-06 03:10:48
 * @描述：采集方案明细类
 */
@Controller
@RequestMapping("/assetScheduleSchemeDetailController")
public class AssetScheduleSchemeDetailController extends BaseController{

 	@Resource
    private AssetScheduleSchemeDetailService assetScheduleSchemeDetailService;
 	@Resource
    private DictProfileService dictProfileService;
 	@Resource
	private DataUserLogService dataUserLogService;
 	@Resource
    private AssetScheduleSchemeService assetScheduleSchemeService;
 	
	/**
	 * 跳转到采集方案明细列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
		DictProfile entity=new DictProfile();
		String profileTypes[] = {"1","4","5","13"};  
		entity.put("profileTypes",profileTypes);
		List<DictProfile> list = dictProfileService.getList(entity);
		String profileReplace = RoletoJson.listToReplaceStr(list, "id", "name",";");
		model.addAttribute("profileReplace", profileReplace);
        return new ModelAndView("/asset/assetScheduleSchemeDetailList");
    }

	/**
	 * 跳转到采集方案明细新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "assetScheduleSchemeDetail")
	public ModelAndView assetScheduleSchemeDetail(AssetScheduleSchemeDetail assetScheduleSchemeDetail,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(assetScheduleSchemeDetail.getTaskId())){
			try {
                assetScheduleSchemeDetail=assetScheduleSchemeDetailService.getEntity(assetScheduleSchemeDetail.getTaskId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
		}else{
			try {
				Date date=new Date();
				date=DateUtils.getDateAdd(date, 1, 81);
				assetScheduleSchemeDetail.setStartTime(DateUtils.parseDate("01/01/"+DateUtils.formatDate("yyyy")+" 00:00:00","MM/dd/yyyy HH:mm:ss"));
				assetScheduleSchemeDetail.setEndTime(DateUtils.parseDate("12/31/2099 23:59:59","MM/dd/yyyy HH:mm:ss"));
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		if(assetScheduleSchemeDetail.getTaskType()!=null){

			String protocolId = (String) request.getParameter("protocolId");
			
			DictProfile entity=new DictProfile();
			entity.setProfileType(assetScheduleSchemeDetail.getTaskType().toString());
			entity.setProtocolId(protocolId);
			List<DictProfile> list = dictProfileService.getList(entity);
			String profileReplace = RoletoJson.listToReplaceStr(list, "id", "name");
			model.addAttribute("profileReplace", profileReplace);
		}
		if(StringUtil.isNotEmpty(assetScheduleSchemeDetail.getProfileId())){
			assetScheduleSchemeDetail.setProfileId(assetScheduleSchemeDetail.getProfileId().replace(";", ","));
		}
		model.addAttribute("assetScheduleSchemeDetail", assetScheduleSchemeDetail);
		return new ModelAndView("/asset/assetScheduleSchemeDetail");
	}


	/**
	 * 采集方案明细查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=assetScheduleSchemeDetailService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除采集方案明细信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(AssetScheduleSchemeDetail assetScheduleSchemeDetail,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            assetScheduleSchemeDetail=assetScheduleSchemeDetailService.getEntity(assetScheduleSchemeDetail.getId());
            AssetScheduleScheme assetScheduleScheme = assetScheduleSchemeService.getEntity(assetScheduleSchemeDetail.getId());
            if(assetScheduleSchemeDetailService.deleteById(assetScheduleSchemeDetail.getTaskId())>0){
                j.setMsg("Successfully deleted");
                dataUserLogService.insertDataUserLog(su.getId(), "Collection Scheme Mgmt","Edit Scheme", "Edit scheme(Scheme Name="+assetScheduleScheme.getName()+")");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存采集方案明细信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })AssetScheduleSchemeDetail assetScheduleSchemeDetail,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        String startTimePatternData = request.getParameter("startTimePatternData");
        String endTimePatternData = request.getParameter("endTimePatternData");
        AssetScheduleSchemeDetail t=new  AssetScheduleSchemeDetail();
        

        try {
        assetScheduleSchemeDetail.setStartTime(DateUtils.parseDate(startTimePatternData, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
        assetScheduleSchemeDetail.setEndTime(DateUtils.parseDate(endTimePatternData, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
        
        SysUser su=TokenManager.getToken();
       	if(StringUtil.isNotEmpty(assetScheduleSchemeDetail.getProfileId())){
			assetScheduleSchemeDetail.setProfileId(assetScheduleSchemeDetail.getProfileId().replace(",", ";"));
		}
        if(StringUtil.isNotEmpty(assetScheduleSchemeDetail.getTaskId())){
        	t = assetScheduleSchemeDetailService.getEntity(assetScheduleSchemeDetail.getTaskId());
			MyBeanUtils.copyBeanNotNull2Bean(assetScheduleSchemeDetail, t);
				assetScheduleSchemeDetailService.update(t);
				j.setMsg(MutiLangUtil.doMutiLang("assetScheduleSchemeDetailList.successfullyModified"));
			}else{
	            assetScheduleSchemeDetailService.save(assetScheduleSchemeDetail);
	            j.setMsg(MutiLangUtil.doMutiLang("assetScheduleSchemeDetailList.addedSuccessfully"));
	          
			}
        AssetScheduleScheme assetScheduleScheme = assetScheduleSchemeService.getEntity(assetScheduleSchemeDetail.getId());
        dataUserLogService.insertDataUserLog(su.getId(), "Collection Scheme Mgmt","Edit Scheme", "Edit scheme(Scheme Name="+assetScheduleScheme.getName()+")");
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("assetCustomer.abnormalOperation"));
        }
        return j;
    }
	
	
	
}