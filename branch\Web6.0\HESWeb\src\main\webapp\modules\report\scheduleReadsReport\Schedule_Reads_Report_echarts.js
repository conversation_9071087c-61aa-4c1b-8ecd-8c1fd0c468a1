//echarts
			var myChart = echarts.init(document.getElementById('main'));
			var myChart1 = echarts.init(document.getElementById('main1'));
			var myChart2 = echarts.init(document.getElementById('main2'));
			var myChart3 = echarts.init(document.getElementById('main3'));
			var myChart4 = echarts.init(document.getElementById('main4'));
			var myChart5 = echarts.init(document.getElementById('main5'));
			var myChart6 = echarts.init(document.getElementById('main6'));
			var myChart7 = echarts.init(document.getElementById('main7'));
			var myChart8 = echarts.init(document.getElementById('main8'));
			var myChart9 = echarts.init(document.getElementById('main9'));
			var myChart10 = echarts.init(document.getElementById('main10'));
			var myChart11 = echarts.init(document.getElementById('main11'));
			var myChart12 = echarts.init(document.getElementById('main12'));
			var myChart13 = echarts.init(document.getElementById('main13'));
			// 指定图表的配置项和数据
			var option = {
				color: ['#33ccdb'],
				tooltip: {
					trigger: 'axis',
					formatter: function(params) {
						var result = params[0].seriesName + '</br>';
						for(var i = 0, l = params.length; i < l; i++) {
							params.forEach(function(item) {
								result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:' + item.color + '"></span>' + params[i].name + ' : ' + params[i].value + '%';
							});
						}

						return result;
					},
					axisPointer: { // 坐标轴指示器，坐标轴触发有效
						type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
					}
				},
				grid: {
					top: '8%',
					left: '3%',
					right: '4%',
					bottom: '3%',
					containLabel: true
				},
				xAxis: [{
					type: 'category',
					data: ['11-01', '11-02', '11-03', '11-04', '11-05', '11-06', '11-07', '11-08', '11-09', '11-10', '11-11'],
					axisTick: {
						alignWithLabel: true
					}
				}],
				yAxis: [{
					axisLabel: {
						formatter: '{value} %'
					},
					type: 'value',
					scale: true,
					max: 100,
					splitNumber: 5,
					boundaryGap: [0.05, 0.05],
					splitArea: {
						show: true
					},

				}],
				series: [{
					name: i18n.t('scheduleReadsReport.dmeterInteRadeio'),
					type: 'bar',
					barWidth: '60%',
					data: [93, 99, 98, 96, 94, 95, 93, 98, 97, 94, 99]
				}]
			};
			var option1 = {
				color: ['#1bc5ad'],
				tooltip: {
					trigger: 'axis',
					formatter: function(params) {
						var result = params[0].seriesName + '</br>';
						for(var i = 0, l = params.length; i < l; i++) {
							params.forEach(function(item) {
								result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:' + item.color + '"></span>' + params[i].name + ' : ' + params[i].value + '%';
							});
						}

						return result;
					},
					axisPointer: { // 坐标轴指示器，坐标轴触发有效
						type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
					}
				},
				grid: {
					top: '8%',
					left: '3%',
					right: '4%',
					bottom: '3%',
					containLabel: true
				},
				xAxis: [{
					type: 'category',
					data: ['2017-01', '2017-02', '2017-03', '2017-04', '2017-05', '2017-06', '2017-07', '2017-08', '2017-09', '2017-10', '2017-11', '2017-12'],
					axisTick: {
						alignWithLabel: true
					}
				}],
				yAxis: [{
					axisLabel: {
						formatter: '{value} %'
					},
					max: 100,
					type: 'value',
					scale: true,
					splitNumber: 5,
					boundaryGap: [0.05, 0.05],
					splitArea: {
						show: true,

					},
					formatter: '{value} %',
				}],
				series: [{
					name: i18n.t('scheduleReadsReport.mmeterInteRadeio'),
					type: 'bar',
					barWidth: '60%',
					data: [93, 99.9, 98, 96, 94, 95, 93, 98, 97, 94, 99, 96]
				}]
			};
			var option2 = {

				tooltip: {

					trigger: 'axis',
					formatter: function(params) {
						var result = params[0].name + '</br>';
						for(var i = 0, l = params.length; i < l; i++) {
							params.forEach(function(item) {
								result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:' + item.color + '"></span>' + params[i].seriesName + ' : ' + params[i].value + '%';
							});
						}

						return result;
					},
					axisPointer: { // 坐标轴指示器，坐标轴触发有效
						type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'

					}
				},
				legend: {
					data: ['Clou', 'Siemens', 'LandisGyr']
				},
				grid: {
					top: '8%',
					left: '3%',
					right: '4%',
					bottom: '3%',
					containLabel: true
				},
				xAxis: [{
					type: 'category',
					data: ['Clou', 'Siemens', 'LandisGyr'],
					axisTick: {
						alignWithLabel: true
					}
				}],
				yAxis: [{
					axisLabel: {
						formatter: '{value} %'
					},
					max: 100,
					min: 0,
					type: 'value',
					scale: true,
					splitNumber: 5,
					boundaryGap: [0.05, 0.05],
					splitArea: {
						show: true,

					},
					formatter: '{value} %',
				}],
				series: [{
					itemStyle: {
						normal: {
							color: function(params) {
								var colorList = ['rgb(255,100,100)', 'rgb(0,182,230)', '#1bc5ad'];
								return colorList[params.dataIndex];
							},
							
						}
					},
					name: i18n.t('scheduleReadsReport.dInteRatioManu'),

					type: 'bar',
					barWidth: '60%',
					data: [99.2, 98.6, 98.8]
				}]
			};

			var option3 = {

				tooltip: {
					trigger: 'axis',
					formatter: function(params) {
						var result = params[0].name + '</br>';
						for(var i = 0, l = params.length; i < l; i++) {
							params.forEach(function(item) {
								result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:' + item.color + '"></span>' + params[i].seriesName + ' : ' + params[i].value + '%';
							});
						}

						return result;
					},
					axisPointer: { // 坐标轴指示器，坐标轴触发有效
						type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
					}
				},
				legend: {
					data: ['Clou', 'Siemens', 'LandisGyr']
				},
				grid: {
					top: '8%',
					left: '3%',
					right: '4%',
					bottom: '3%',
					containLabel: true
				},
				xAxis: [{
					type: 'category',
					data: ['Clou', 'Siemens', 'LandisGyr'],
					axisTick: {
						alignWithLabel: true
					}
				}],
				yAxis: [{
					axisLabel: {
						formatter: '{value} %'
					},
					max: 100,
					min: 0,
					type: 'value',
					scale: true,
					splitNumber: 5,
					boundaryGap: [0.05, 0.05],
					splitArea: {
						show: true
					},
					formatter: '{value} %',
				}],
				series: [{
					itemStyle: {
						normal: {
							color: function(params) {
								var colorList = ['rgb(255,100,100)', 'rgb(0,182,230)', '#1bc5ad'];
								return colorList[params.dataIndex];
							},
							
						}
					},
					name: i18n.t('scheduleReadsReport.mmeterInteRadeio'),
					type: 'bar',
					barWidth: '60%',
					data: [93.5, 92, 93]
				}]
			};
			var option4 = {

				tooltip: {
					trigger: 'axis',

					axisPointer: { // 坐标轴指示器，坐标轴触发有效
						type: 'cross' // 默认为直线，可选为：'line' | 'shadow'
					}
				},
				legend: {
					
					data: ['Clou', 'Siemens', 'LandisGyr'],

				},
				grid: {
					top: '8%',
					left: '3%',
					right: '4%',
					bottom: '3%',
					containLabel: true
				},

				xAxis: {
					type: 'category',
					boundaryGap: false,
					data: ['11-01', '11-02', '11-03', '11-04', '11-05', '11-06', '11-07', '11-08', '11-09', '11-10']
				},
				yAxis: [{
					axisLabel: {
						formatter: '{value} %'
					},
					max: 100,
					type: 'value',
					scale: true,
					splitNumber: 5,
					boundaryGap: [0.2, 0.2],
					splitArea: {
						show: true
					},
					formatter: '{value} %',
				}],
				series: [{
					name: 'Clou',
					type: 'line',
					stack: 'Clou',
					smooth: true,
					itemStyle: {
						normal: {
							color: '#f44336',

							lineStyle: {
								color: '#f44336'
							}
						}
					},
					
					data: [98.8, 99, 98.6, 98.9, 98.6, 99.2, 98.7, 98.9, 99.1, 99]
				}, {
					name: 'Siemens',
					type: 'line',
					stack: 'Siemens',
					smooth: true,
					itemStyle: {
						normal: {
							color: '#089682',

							lineStyle: {
								color: '#089682'
							}
						}
					},
					
					data: [98, 97.5, 97.8, 97.4, 97.3, 97.9, 98.2, 98.0, 98.2, 98.3]
				}, {
					name: 'LandisGyr',
					type: 'line',
					stack: 'LandisGyr',
					smooth: true,
					itemStyle: {
						normal: {
							color: 'orange',

							lineStyle: {
								color: 'orange'
							}
						}
					},
					
					data: [98.2, 97.8, 98.4, 97.8, 98.2, 98.0, 98.6, 97.2, 97.6, 98]
				}]
			};
			var option5 = {
				title: {
					text: ''
				},
				tooltip: {
					trigger: 'axis',
                    axisPointer: { // 坐标轴指示器，坐标轴触发有效
						type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
					}
				},
				legend: {
					
					data: ['Clou', 'Siemens', 'LandisGyr']
				},
				grid: {
					top: '8%',
					left: '3%',
					right: '4%',
					bottom: '3%',
					containLabel: true
				},

				xAxis: {
					type: 'category',
					boundaryGap: false,
					data: ['2017-01', '2017-02', '2017-03', '2017-04', '2017-05', '2017-06', '2017-07', '2017-08', '2017-09', '2017-10', '2017-11', '2017-12']
				},
				yAxis: [{
					axisLabel: {
						formatter: '{value} %'
					},
					max: 100,
					/*min: 0,*/
					type: 'value',
					scale: true,
					splitNumber: 5,
					boundaryGap: [0.2, 0.2],
					splitArea: {
						show: true
					},
					formatter: '{value} %',
				}],
				series: [{
					name: 'Clou',
					type: 'line',
					stack: 'Clou',
					smooth: true,
					itemStyle: {
						normal: {
							color: '#f44336',

							lineStyle: {
								color: '#f44336'
							}
						}
					},
					data: [96, 98, 95, 97, 98, 94, 97, 98, 97, 94, 96.8, 98.2]
				}, {
					name: 'Siemens',
					type: 'line',
					stack: 'Siemens',
					smooth: true,
					itemStyle: {
						normal: {
							color: '#089682',

							lineStyle: {
								color: '#089682'
							}
						}
					},
					data: [92, 93, 91, 98, 93, 96, 92, 95, 93, 91, 97, 98, ]
				}, {
					name: 'LandisGyr',
					type: 'line',
					smooth: true,
					stack: 'LandisGyr',
					itemStyle: {
						normal: {
							color: 'orange',

							lineStyle: {
								color: 'orange'
							}
						}
					},
					data: [98.2, 97.8, 99.1, 96.8, 99.2, 99.4, 98.1, 97.2, 97.6, 99, 97.8, 98.8]
				}]
			};
			var option6 = {

				tooltip: {

					trigger: 'axis',
					formatter: function(params) {
						var result = params[0].name + '</br>';
						for(var i = 0, l = params.length; i < l; i++) {
							params.forEach(function(item) {
								result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:' + item.color + '"></span>' + params[i].seriesName + ' : ' + params[i].value + '%';
							});
						}

						return result;
					},
					axisPointer: { // 坐标轴指示器，坐标轴触发有效
						type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'

					}
				},
				legend: {
					data: ['Clou', 'Siemens', 'LandisGyr']
				},
				grid: {
					top: '8%',
					left: '3%',
					right: '4%',
					bottom: '3%',
					containLabel: true
				},
				xAxis: [{
					type: 'category',
					data: ['Clou', 'Siemens', 'LandisGyr'],
					axisTick: {
						alignWithLabel: true
					}
				}],
				yAxis: [{
					axisLabel: {
						formatter: '{value} %'
					},
					max: 100,
					min: 0,
					type: 'value',
					scale: true,
					splitNumber: 5,
					boundaryGap: [0.05, 0.05],
					splitArea: {
						show: true,

					},
					formatter: '{value} %',
				}],
				series: [{
					itemStyle: {
						normal: {
							color: function(params) {
								var colorList = ['rgb(255,100,100)', 'rgb(0,182,230)', '#1bc5ad'];
								return colorList[params.dataIndex];
							},
							
						}
					},
					name: 'Daily Integrity Ratio by Manufacturers',

					type: 'bar',
					barWidth: '60%',
					data: [99.2, 98.6, 98.8]
				}]
			};

			var option7 = {

				tooltip: {
					trigger: 'axis',
					formatter: function(params) {
						var result = params[0].name + '</br>';
						for(var i = 0, l = params.length; i < l; i++) {
							params.forEach(function(item) {
								result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:' + item.color + '"></span>' + params[i].seriesName + ' : ' + params[i].value + '%';
							});
						}

						return result;
					},
					axisPointer: { // 坐标轴指示器，坐标轴触发有效
						type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
					}
				},
				legend: {
					data: ['Clou', 'Siemens', 'LandisGyr']
				},
				grid: {
					top: '8%',
					left: '3%',
					right: '4%',
					bottom: '3%',
					containLabel: true
				},
				xAxis: [{
					type: 'category',
					data: ['Clou', 'Siemens', 'LandisGyr'],
					axisTick: {
						alignWithLabel: true
					}
				}],
				yAxis: [{
					axisLabel: {
						formatter: '{value} %'
					},
					max: 100,
					min: 0,
					type: 'value',
					scale: true,
					splitNumber: 5,
					boundaryGap: [0.05, 0.05],
					splitArea: {
						show: true
					},
					formatter: '{value} %',
				}],
				series: [{
					itemStyle: {
						normal: {
							color: function(params) {
								var colorList = ['rgb(255,100,100)', 'rgb(0,182,230)', '#1bc5ad'];
								return colorList[params.dataIndex];
							},
							
						}
					},
					name: i18n.t('scheduleReadsReport.mmeterInteRadeio'),
					type: 'bar',
					barWidth: '60%',
					data: [93.5, 92, 93]
				}]
			};
			var option8 = {

				tooltip: {
					trigger: 'axis',

					axisPointer: { // 坐标轴指示器，坐标轴触发有效
						type: 'cross' // 默认为直线，可选为：'line' | 'shadow'
					}
				},
				legend: {
					
					data: ['Clou', 'Siemens', 'LandisGyr'],

				},
				grid: {
					top: '8%',
					left: '3%',
					right: '4%',
					bottom: '3%',
					containLabel: true
				},

				xAxis: {
					type: 'category',
					boundaryGap: false,
					data: ['11-01', '11-02', '11-03', '11-04', '11-05', '11-06', '11-07', '11-08', '11-09', '11-10']
				},
				yAxis: [{
					axisLabel: {
						formatter: '{value} %'
					},
					max: 100,
					type: 'value',
					scale: true,
					splitNumber: 5,
					boundaryGap: [0.2, 0.2],
					splitArea: {
						show: true
					},
					formatter: '{value} %',
				}],
				series: [{
					name: 'Clou',
					type: 'line',
					stack: 'Clou',
					smooth: true,
					itemStyle: {
						normal: {
							color: '#f44336',

							lineStyle: {
								color: '#f44336'
							}
						}
					},
					
					data: [98.8, 99, 98.6, 98.9, 98.6, 99.2, 98.7, 98.9, 99.1, 99]
				}, {
					name: 'Siemens',
					type: 'line',
					stack: 'Siemens',
					smooth: true,
					itemStyle: {
						normal: {
							color: '#089682',

							lineStyle: {
								color: '#089682'
							}
						}
					},
					
					data: [98, 97.5, 97.8, 97.4, 97.3, 97.9, 98.2, 98.0, 98.2, 98.3]
				}, {
					name: 'LandisGyr',
					type: 'line',
					stack: 'LandisGyr',
					smooth: true,
					itemStyle: {
						normal: {
							color: 'orange',

							lineStyle: {
								color: 'orange'
							}
						}
					},
					
					data: [98.2, 97.8, 98.4, 97.8, 98.2, 98.0, 98.6, 97.2, 97.6, 98]
				}]
			};
			var option9 = {
				title: {
					text: ''
				},
				tooltip: {
					trigger: 'axis',
                    axisPointer: { // 坐标轴指示器，坐标轴触发有效
						type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
					}
				},
				legend: {
					
					data: ['Clou', 'Siemens', 'LandisGyr']
				},
				grid: {
					top: '8%',
					left: '3%',
					right: '4%',
					bottom: '3%',
					containLabel: true
				},

				xAxis: {
					type: 'category',
					boundaryGap: false,
					data: ['2017-01', '2017-02', '2017-03', '2017-04', '2017-05', '2017-06', '2017-07', '2017-08', '2017-09', '2017-10', '2017-11', '2017-12']
				},
				yAxis: [{
					axisLabel: {
						formatter: '{value} %'
					},
					max: 100,
					/*min: 0,*/
					type: 'value',
					scale: true,
					splitNumber: 5,
					boundaryGap: [0.2, 0.2],
					splitArea: {
						show: true
					},
					formatter: '{value} %',
				}],
				series: [{
					name: 'Clou',
					type: 'line',
					stack: 'Clou',
					smooth: true,
					itemStyle: {
						normal: {
							color: '#f44336',

							lineStyle: {
								color: '#f44336'
							}
						}
					},
					data: [96, 98, 95, 97, 98, 94, 97, 98, 97, 94, 96.8, 98.2]
				}, {
					name: 'Siemens',
					type: 'line',
					stack: 'Siemens',
					smooth: true,
					itemStyle: {
						normal: {
							color: '#089682',

							lineStyle: {
								color: '#089682'
							}
						}
					},
					data: [92, 93, 91, 98, 93, 96, 92, 95, 93, 91, 97, 98, ]
				}, {
					name: 'LandisGyr',
					type: 'line',
					smooth: true,
					stack: 'LandisGyr',
					itemStyle: {
						normal: {
							color: 'orange',

							lineStyle: {
								color: 'orange'
							}
						}
					},
					data: [98.2, 97.8, 99.1, 96.8, 99.2, 99.4, 98.1, 97.2, 97.6, 99, 97.8, 98.8]
				}]
			};
			var option10 = {

				tooltip: {

					trigger: 'axis',
					formatter: function(params) {
						var result = params[0].name + '</br>';
						for(var i = 0, l = params.length; i < l; i++) {
							params.forEach(function(item) {
								result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:' + item.color + '"></span>' + params[i].seriesName + ' : ' + params[i].value + '%';
							});
						}

						return result;
					},
					axisPointer: { // 坐标轴指示器，坐标轴触发有效
						type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'

					}
				},
				legend: {
					data: ['Clou', 'Siemens', 'LandisGyr']
				},
				grid: {
					top: '8%',
					left: '3%',
					right: '4%',
					bottom: '3%',
					containLabel: true
				},
				xAxis: [{
					type: 'category',
					data: ['Clou', 'Siemens', 'LandisGyr'],
					axisTick: {
						alignWithLabel: true
					}
				}],
				yAxis: [{
					axisLabel: {
						formatter: '{value} %'
					},
					max: 100,
					min: 0,
					type: 'value',
					scale: true,
					splitNumber: 5,
					boundaryGap: [0.05, 0.05],
					splitArea: {
						show: true,

					},
					formatter: '{value} %',
				}],
				series: [{
					itemStyle: {
						normal: {
							color: function(params) {
								var colorList = ['rgb(255,100,100)', 'rgb(0,182,230)', '#1bc5ad'];
								return colorList[params.dataIndex];
							},
							
						}
					},
					name: 'Daily Integrity Ratio by Manufacturers',

					type: 'bar',
					barWidth: '60%',
					data: [99.2, 98.6, 98.8]
				}]
			};

			var option11 = {

				tooltip: {
					trigger: 'axis',
					formatter: function(params) {
						var result = params[0].name + '</br>';
						for(var i = 0, l = params.length; i < l; i++) {
							params.forEach(function(item) {
								result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:' + item.color + '"></span>' + params[i].seriesName + ' : ' + params[i].value + '%';
							});
						}

						return result;
					},
					axisPointer: { // 坐标轴指示器，坐标轴触发有效
						type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
					}
				},
				legend: {
					data: ['Clou', 'Siemens', 'LandisGyr']
				},
				grid: {
					top: '8%',
					left: '3%',
					right: '4%',
					bottom: '3%',
					containLabel: true
				},
				xAxis: [{
					type: 'category',
					data: ['Clou', 'Siemens', 'LandisGyr'],
					axisTick: {
						alignWithLabel: true
					}
				}],
				yAxis: [{
					axisLabel: {
						formatter: '{value} %'
					},
					max: 100,
					min: 0,
					type: 'value',
					scale: true,
					splitNumber: 5,
					boundaryGap: [0.05, 0.05],
					splitArea: {
						show: true
					},
					formatter: '{value} %',
				}],
				series: [{
					itemStyle: {
						normal: {
							color: function(params) {
								var colorList = ['rgb(255,100,100)', 'rgb(0,182,230)', '#1bc5ad'];
								return colorList[params.dataIndex];
							},
							
						}
					},
					name: i18n.t('scheduleReadsReport.mmeterInteRadeio'),
					type: 'bar',
					barWidth: '60%',
					data: [93.5, 92, 93]
				}]
			};
			var option12 = {

				tooltip: {
					trigger: 'axis',

					axisPointer: { // 坐标轴指示器，坐标轴触发有效
						type: 'cross' // 默认为直线，可选为：'line' | 'shadow'
					}
				},
				legend: {
					
					data: ['Clou', 'Siemens', 'LandisGyr'],

				},
				grid: {
					top: '8%',
					left: '3%',
					right: '4%',
					bottom: '3%',
					containLabel: true
				},

				xAxis: {
					type: 'category',
					boundaryGap: false,
					data: ['11-01', '11-02', '11-03', '11-04', '11-05', '11-06', '11-07', '11-08', '11-09', '11-10']
				},
				yAxis: [{
					axisLabel: {
						formatter: '{value} %'
					},
					max: 100,
					type: 'value',
					scale: true,
					splitNumber: 5,
					boundaryGap: [0.2, 0.2],
					splitArea: {
						show: true
					},
					formatter: '{value} %',
				}],
				series: [{
					name: 'Clou',
					type: 'line',
					stack: 'Clou',
					smooth: true,
					itemStyle: {
						normal: {
							color: '#f44336',

							lineStyle: {
								color: '#f44336'
							}
						}
					},
					
					data: [98.8, 99, 98.6, 98.9, 98.6, 99.2, 98.7, 98.9, 99.1, 99]
				}, {
					name: 'Siemens',
					type: 'line',
					stack: 'Siemens',
					smooth: true,
					itemStyle: {
						normal: {
							color: '#089682',

							lineStyle: {
								color: '#089682'
							}
						}
					},
					
					data: [98, 97.5, 97.8, 97.4, 97.3, 97.9, 98.2, 98.0, 98.2, 98.3]
				}, {
					name: 'LandisGyr',
					type: 'line',
					stack: 'LandisGyr',
					smooth: true,
					itemStyle: {
						normal: {
							color: 'orange',

							lineStyle: {
								color: 'orange'
							}
						}
					},
					
					data: [98.2, 97.8, 98.4, 97.8, 98.2, 98.0, 98.6, 97.2, 97.6, 98]
				}]
			};
			var option13 = {
				title: {
					text: ''
				},
				tooltip: {
					trigger: 'axis',
                    axisPointer: { // 坐标轴指示器，坐标轴触发有效
						type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
					}
				},
				legend: {
					
					data: ['Clou', 'Siemens', 'LandisGyr']
				},
				grid: {
					top: '8%',
					left: '3%',
					right: '4%',
					bottom: '3%',
					containLabel: true
				},

				xAxis: {
					type: 'category',
					boundaryGap: false,
					data: ['2017-01', '2017-02', '2017-03', '2017-04', '2017-05', '2017-06', '2017-07', '2017-08', '2017-09', '2017-10', '2017-11', '2017-12']
				},
				yAxis: [{
					axisLabel: {
						formatter: '{value} %'
					},
					max: 100,
					/*min: 0,*/
					type: 'value',
					scale: true,
					splitNumber: 5,
					boundaryGap: [0.2, 0.2],
					splitArea: {
						show: true
					},
					formatter: '{value} %',
				}],
				series: [{
					name: 'Clou',
					type: 'line',
					stack: 'Clou',
					smooth: true,
					itemStyle: {
						normal: {
							color: '#f44336',

							lineStyle: {
								color: '#f44336'
							}
						}
					},
					data: [96, 98, 95, 97, 98, 94, 97, 98, 97, 94, 96.8, 98.2]
				}, {
					name: 'Siemens',
					type: 'line',
					stack: 'Siemens',
					smooth: true,
					itemStyle: {
						normal: {
							color: '#089682',

							lineStyle: {
								color: '#089682'
							}
						}
					},
					data: [92, 93, 91, 98, 93, 96, 92, 95, 93, 91, 97, 98, ]
				}, {
					name: 'LandisGyr',
					type: 'line',
					smooth: true,
					stack: 'LandisGyr',
					itemStyle: {
						normal: {
							color: 'orange',

							lineStyle: {
								color: 'orange'
							}
						}
					},
					data: [98.2, 97.8, 99.1, 96.8, 99.2, 99.4, 98.1, 97.2, 97.6, 99, 97.8, 98.8]
				}]
			};

			// 使用刚指定的配置项和数据显示图表。
			myChart.setOption(option);
			myChart1.setOption(option1);
			myChart2.setOption(option2);
			myChart3.setOption(option3);
			myChart4.setOption(option4);
			myChart5.setOption(option5);
			myChart6.setOption(option6);
			myChart7.setOption(option7);
			myChart8.setOption(option8);
			myChart9.setOption(option9);
			myChart10.setOption(option10);
			myChart11.setOption(option11);
			myChart12.setOption(option12);
			myChart13.setOption(option13);
			
			$(window).resize(function() {
				$(myChart).each(function(index, chart) {
					myChart.resize();
					myChart1.resize();
					myChart2.resize();
					myChart3.resize();
					myChart4.resize();
					myChart5.resize();
					myChart6.resize();
					myChart7.resize();
					myChart8.resize();
					myChart9.resize();
					myChart10.resize();
					myChart11.resize();
					myChart12.resize();
					myChart13.resize();
				});

			});
			var Echart_01 = echarts.init(document.getElementById("main"));
			var Echart_02 = echarts.init(document.getElementById("main1"));
			var Echart_03 = echarts.init(document.getElementById("main2"));
			var Echart_04 = echarts.init(document.getElementById("main3"));
			var Echart_05 = echarts.init(document.getElementById("main4"));
			var Echart_06 = echarts.init(document.getElementById("main5"));
			var Echart_07 = echarts.init(document.getElementById("main6"));
			var Echart_08 = echarts.init(document.getElementById("main7"));
			var Echart_09 = echarts.init(document.getElementById("main8"));
			var Echart_10 = echarts.init(document.getElementById("main9"));
			var Echart_11 = echarts.init(document.getElementById("main10"));
			var Echart_12 = echarts.init(document.getElementById("main11"));
			var Echart_13 = echarts.init(document.getElementById("main12"));
			var Echart_14 = echarts.init(document.getElementById("main13"));
			$('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
				Echart_01.resize();
				Echart_02.resize();
				Echart_03.resize();
				Echart_04.resize();
				Echart_05.resize();
				Echart_06.resize();
				Echart_07.resize();
				Echart_08.resize();
				Echart_09.resize();
				Echart_10.resize();
				Echart_11.resize();
				Echart_12.resize();
				Echart_13.resize();
				Echart_14.resize();
				

			});