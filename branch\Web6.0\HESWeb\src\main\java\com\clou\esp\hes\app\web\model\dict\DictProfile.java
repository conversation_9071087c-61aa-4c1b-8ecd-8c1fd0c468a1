/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictProfile{ } 
 * 
 * 摘    要： dictProfile
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-02-26 09:36:52
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.dict;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DictProfile  extends BaseEntity {

	private static final long serialVersionUID = -7463796321083969978L;

	public DictProfile() {}

	private String 		name;
	private String 		protocolId;
	private String 		protocolCode;
	private String 		profileType;

	private String      oldId;
	
	public String getProfileId() {
		return id;
	}
	
	public String getOldId() {
		return oldId;
	}

	public void setOldId(String oldId) {
		this.oldId = oldId;
	}


	public String getName() {
		return MutiLangUtil.assemblyI18nData(MutiLangUtil.PROFILE_I18N,name);
	}

	public void setName(String name) {
		this.name = name;
	}
	
	public String getProtocolId() {
		return protocolId;
	}

	public void setProtocolId(String protocolId) {
		this.protocolId = protocolId;
	}
	
	public String getProtocolCode() {
		return protocolCode;
	}

	public void setProtocolCode(String protocolCode) {
		this.protocolCode = protocolCode;
	}
	
	public String getProfileType() {
		return profileType;
	}

	public void setProfileType(String profileType) {
		this.profileType = profileType;
	}

	public DictProfile(String name 
	,String protocolId 
	,String protocolCode 
	,String profileType ) {
		super();
		this.name = name;
		this.protocolId = protocolId;
		this.protocolCode = protocolCode;
		this.profileType = profileType;
	}

}