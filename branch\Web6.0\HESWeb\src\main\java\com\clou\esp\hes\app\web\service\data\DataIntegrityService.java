/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataIntegrity{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-12-01 06:03:45
 * 最后修改时间：
 * 
 *********************************************************************************************************/

package com.clou.esp.hes.app.web.service.data;

import java.util.List;
import java.util.Map;

import com.clou.esp.hes.app.web.service.common.CommonService;
import com.clou.esp.hes.app.web.model.data.DataIntegrity;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

public interface DataIntegrityService extends CommonService<DataIntegrity> {
	/**
	 * 统计完成率
	 */
	public void statisticsIntegrity();

	/**
	 * 统计完成率求和
	 * 
	 * @param p
	 * @return
	 */
	public List<Map<String, Object>> getIntegritySum(Map<String, Object> p);

	/**
	 * 厂商、类型、通讯方式下的完成率求和
	 * 
	 * @param p
	 * @return
	 */
	public List<Map<String, Object>> getIntegrityNameSum(Map<String, Object> p);

	/**
	 * 统计完成率
	 */
	public void statisticMetersIntegrity();

	/**
	 * 获取统计数据根据参数
	 * 
	 * @param params
	 * @return
	 */
	public List<Map<String, Object>> getDataIntegrityByMap(
			Map<String, Object> params);
	/**
	 * 获取统计数据根据参数
	 * 
	 * @param params
	 * @return
	 */
	public List<Map<String, Object>> getDataIntegrityByMaps(
			Map<String, Object> params);
	
	/**
	 * 分页，电表
	 * 
	 * @param JqGridSearchTo
	 * @return String
	 */
	JqGridResponseTo getForMeterJqGrid(JqGridSearchTo jqGridSearchTo);
}