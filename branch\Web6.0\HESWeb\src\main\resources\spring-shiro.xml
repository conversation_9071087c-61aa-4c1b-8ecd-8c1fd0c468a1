<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:util="http://www.springframework.org/schema/util"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="
       http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
       http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">
	<description>== Shiro Components ==</description>

	<!-- 会话Session ID生成器 -->
	<bean id="sessionIdGenerator"
		class="org.apache.shiro.session.mgt.eis.JavaUuidSessionIdGenerator" />

	<!-- 会话Cookie模板 -->
	<bean id="sessionIdCookie" class="org.apache.shiro.web.servlet.SimpleCookie">
		<!--cookie的name，我故意取名叫xxxxbaidu -->
		<constructor-arg value="v_v-s-baidu" />
		<property name="httpOnly" value="true" />
		<!--cookie的有效时间 -->
		<property name="maxAge" value="-1" />
		<!-- 配置存储Session Cookie的domain为 一级域名 <property name="domain" value=".itboy.net"/> -->
	</bean>
	<!-- redis 的缓存 -->
	<bean id="jedisManager" class="com.clou.esp.hes.app.web.core.shiro.cache.JedisManager">
		<property name="jedisPool" ref="jedisPool" />
	</bean>
	<!-- session 创建、删除、查询 -->
	<bean id="jedisShiroSessionRepository"
		class="com.clou.esp.hes.app.web.core.shiro.cache.JedisShiroSessionRepository">
		<property name="jedisManager" ref="jedisManager" />
	</bean>
	<!-- custom shiro session listener -->
	<bean id="customSessionListener"
		class="com.clou.esp.hes.app.web.core.shiro.listenter.CustomSessionListener">
		<property name="shiroSessionRepository" ref="jedisShiroSessionRepository" />
	</bean>
	<!-- 用户信息记住我功能的相关配置 -->
	<bean id="rememberMeCookie" class="org.apache.shiro.web.servlet.SimpleCookie">
		<constructor-arg value="v_v-re-baidu" />
		<property name="httpOnly" value="true" />
		<!-- 配置存储rememberMe Cookie的domain为 一级域名 <property name="domain" value=".itboy.net"/> -->
		<property name="maxAge" value="2592000" /><!-- 30天时间，记住我30天 -->
	</bean>

	<!-- rememberMe管理器 -->
	<bean id="rememberMeManager" class="org.apache.shiro.web.mgt.CookieRememberMeManager">
		<!-- rememberMe cookie加密的密钥 建议每个项目都不一样 默认AES算法 密钥长度（128 256 512 位） -->
		<property name="cipherKey"
			value="#{T(org.apache.shiro.codec.Base64).decode('3AvVhmFLUs0KTA3Kprsdag==')}" />
		<property name="cookie" ref="rememberMeCookie" />
	</bean>
	<!-- 会话验证调度器 -->
	<bean id="sessionValidationScheduler"
		class="org.apache.shiro.session.mgt.ExecutorServiceSessionValidationScheduler">
		<!-- 间隔多少时间检查，不配置是60分钟 -->
		<property name="interval" value="${shiro.session.validate.timespan}" />
		<property name="sessionManager" ref="sessionManager" />
	</bean>
	<!-- 用户缓存 -->
	<bean id="customShiroCacheManager"
		class="com.clou.esp.hes.app.web.core.shiro.cache.impl.CustomShiroCacheManager">
		<property name="shiroCacheManager" ref="jedisShiroCacheManager" />
	</bean>
	<!-- shiro 缓存实现，对ShiroCacheManager，我是采用redis的实现 -->
	<bean id="jedisShiroCacheManager"
		class="com.clou.esp.hes.app.web.core.shiro.cache.impl.JedisShiroCacheManager">
		<property name="jedisManager" ref="jedisManager" />
	</bean>
	<!-- 安全管理器 -->
	<bean id="securityManager" class="org.apache.shiro.web.mgt.DefaultWebSecurityManager">
		<property name="realm" ref="sampleRealm" />
		<property name="sessionManager" ref="sessionManager" />
		<property name="rememberMeManager" ref="rememberMeManager" />
		<property name="cacheManager" ref="customShiroCacheManager" />
	</bean>
	<!-- custom shiro session listener -->
	<bean id="customShiroSessionDAO" class="com.clou.esp.hes.app.web.core.shiro.CustomShiroSessionDAO">
		<property name="shiroSessionRepository" ref="jedisShiroSessionRepository" />
		<property name="sessionIdGenerator" ref="sessionIdGenerator" />
	</bean>
	<!-- 手动操作Session，管理Session -->
	<bean id="customSessionManager"
		class="com.clou.esp.hes.app.web.core.shiro.session.CustomSessionManager">
		<property name="shiroSessionRepository" ref="jedisShiroSessionRepository" />
		<property name="customShiroSessionDAO" ref="customShiroSessionDAO" />
	</bean>


	<!-- 静态注入，相当于调用SecurityUtils.setSecurityManager(securityManager) -->
	<bean
		class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
		<property name="staticMethod"
			value="org.apache.shiro.SecurityUtils.setSecurityManager" />
		<property name="arguments" ref="securityManager" />
	</bean>
	<!-- 静态注入 jedisShiroSessionRepository -->
	<bean
		class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
		<property name="staticMethod"
			value="com.clou.esp.hes.app.web.core.shiro.filter.KickoutSessionFilter.setShiroSessionRepository" />
		<property name="arguments" ref="jedisShiroSessionRepository" />
	</bean>
	<!-- 授权 认证 -->
	<bean id="sampleRealm" class="com.clou.esp.hes.app.web.core.shiro.token.SampleRealm"></bean>

	<!-- Session Manager -->
	<bean id="sessionManager"
		class="org.apache.shiro.web.session.mgt.DefaultWebSessionManager">
			 <!-- 去掉URL中的JSESSIONID -->
        <property name="sessionIdUrlRewritingEnabled" value="false"/>
		<!-- 相隔多久检查一次session的有效性 -->
		<property name="sessionValidationInterval" value="${shiro.session.sessionValidationInterval}" />
		<!-- session 有效时间为半小时 （毫秒单位） -->
		<property name="globalSessionTimeout" value="${shiro.session.globalSessionTimeout}" />
		<property name="sessionDAO" ref="customShiroSessionDAO" />
		<!-- session 监听，可以多个。 -->
		<property name="sessionListeners">
			<list>
				<ref bean="customSessionListener" />
			</list>
		</property>
		<!-- 间隔多少时间检查，不配置是60分钟 -->
		<property name="sessionValidationScheduler" ref="sessionValidationScheduler" />
		<!-- 是否开启 检测，默认开启 -->
		<property name="sessionValidationSchedulerEnabled" value="false" />
		<!-- 是否删除无效的，默认也是开启 -->
		<property name="deleteInvalidSessions" value="true" />
		<!-- 会话Cookie模板 -->
		<property name="sessionIdCookieEnabled" value="true"/>
		<property name="sessionIdCookie" ref="sessionIdCookie" />
	
	</bean>
	<!-- 自定义角色过滤器 支持多个角色可以访问同一个资源 eg:/home.jsp = authc,roleOR[admin,user] 用户有admin或者user角色 
		就可以访问 -->

	<!-- 认证数据库存储 -->
	<bean id="shiroManager"
		class="com.clou.esp.hes.app.web.core.shiro.service.impl.ShiroManagerImpl" />
	<bean id="login" class="com.clou.esp.hes.app.web.core.shiro.filter.LoginFilter" />
	<bean id="permission" class="com.clou.esp.hes.app.web.core.shiro.filter.PermissionFilter" />
	<bean id="pcCodeOverdue" class="com.clou.esp.hes.app.web.core.shiro.filter.PcCodeOverdueFilter" />
	<!-- session 校验单个用户是否多次登录 -->
	<bean id="kickoutSessionFilter"
		class="com.clou.esp.hes.app.web.core.shiro.filter.KickoutSessionFilter">
		<property name="kickoutUrl" value="/loginController/toLogin.do?kickout" />
	</bean>
	<bean id="simple" class="com.clou.esp.hes.app.web.core.shiro.filter.SimpleAuthFilter" />
	<!-- 
	<bean id="role" class="com.clou.esp.hes.app.web.core.shiro.filter.RoleFilter" />
	-->

	<bean id="shiroFilter" class="org.apache.shiro.spring.web.ShiroFilterFactoryBean">
		<property name="securityManager" ref="securityManager" />
		<property name="loginUrl" value="/systemController/login.do" />
		<!-- TODO 待提取 -->
		<property name="successUrl" value="/" />
		<property name="unauthorizedUrl" value="/" />

		<!-- 初始配置，现采用自定义 -->
		<!-- <property name="filterChainDefinitions" > -->
		<!-- <value> -->
		<!-- /** = anon -->
		<!-- /page/login.jsp = anon -->
		<!-- /page/register/* = anon -->
		<!-- /page/index.jsp = authc -->
		<!-- /page/addItem* = authc,roles[数据管理员] -->
		<!-- /page/file* = authc,roleOR[普通用户,数据管理员] -->
		<!-- /page/listItems* = authc,roleOR[数据管理员,普通用户] -->
		<!-- /page/showItem* = authc,roleOR[数据管理员,普通用户] -->
		<!-- /page/updateItem*=authc,roles[数据管理员] -->
		<!-- </value> -->
		<!-- </property> -->
		<!-- 读取初始自定义权限内容 -->
		<property name="filterChainDefinitions" value="#{shiroManager.loadFilterChainDefinitions()}" />
		<property name="filters">
			<util:map>
				<entry key="login" value-ref="login"></entry>
				<entry key="permission" value-ref="permission"></entry>
  				<entry key="kickout" value-ref="kickoutSessionFilter"></entry>
  				<entry key="simple" value-ref="simple"></entry>
  				<entry key="pcCodeOverdue" value-ref="pcCodeOverdue"></entry>
  				<!--
				<entry key="role" value-ref="role"></entry>
				-->
			</util:map>
		</property>
	</bean>
	<!-- Shiro生命周期处理器 -->
	<bean id="lifecycleBeanPostProcessor" class="org.apache.shiro.spring.LifecycleBeanPostProcessor" />
	<!-- ============================================================================ -->
</beans>

