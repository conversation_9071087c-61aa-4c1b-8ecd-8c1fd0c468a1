/*添加信息框*/
function add_group_layer() {
    var close_link = window.parent.layer.open({
        type: 2,
        title: 'Add Group',
        shadeClose: true,
        shade: [0.7],
        btn: ['Add', 'Cancel'],
        area: ['40%', '55%'],
        content: getRootPathWeb()+'/demoController/addGroupDemo.do'
    });

    $('.close-link').click(function() {
        window.parent.layer.close(close_link);
    });

}
function del_group_layer() {

    var close_link = window.parent.layer.confirm('Do you decide to delete it？', {
        btn: ['Yes', 'no'] ,//按钮
         title: 'Information'
    }, function() {
        window.parent.layer.msg('Have been deleted', {
            icon: 1
        });
    }, function() {
        window.parent.layer.close(close_link);
    });

}


