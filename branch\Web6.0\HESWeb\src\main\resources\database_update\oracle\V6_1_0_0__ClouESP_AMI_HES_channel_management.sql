ALTER TABLE DICT_METER_DATA_STORAGE_TABLE ADD PROFILE_ID NVARCHAR2(32) NULL;

INSERT INTO DICT_MENU (ID, UTILITY_IDS, MENULEVEL, MENUNAME, MENUORDER, FUNCTIONURL, PARENTMENUID, MENU_INTRO) VALUES ('7057860cbedc11e79bb9623728c516f9', '29018328bd4011e79bb968f728c516f9', '2', 'Channel Management', '9', 'dictDataitemController/list.do', '2e858f11beda11e79bb968f728c516f9', '1');

INSERT INTO DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) VALUES ('1070', 'Channel Management',null, 'Channel Management');

alter table dict_dataitem_group drop primary key;
alter table dict_dataitem_group modify (primary key(ID));

alter table dict_dataitem drop primary key;
alter table dict_dataitem modify (primary key(ID));

update dict_menu t set t.functionurl='dcuConfigurationController/toDcuDlmsConfiguration.do' where t.functionid='1008';
delete from dict_menu where functionid='1020';