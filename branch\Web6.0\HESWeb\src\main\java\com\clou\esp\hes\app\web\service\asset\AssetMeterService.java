/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeter{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-06 08:04:55
 * 最后修改时间：
 * 
 *********************************************************************************************************/

package com.clou.esp.hes.app.web.service.asset;

import java.util.List;
import java.util.Map;






import org.apache.ibatis.annotations.Param;

import com.clou.esp.hes.app.web.service.common.CommonService;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.asset.AssetMeterDto;
import com.clou.esp.hes.app.web.model.asset.VendMeterInitialCreditAmount;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

public interface AssetMeterService extends CommonService<AssetMeter> {

	public int batchGetList(List<AssetMeter> mList);
	public int batchGetListPrepare(List<AssetMeter> mList);
	public int batchInsert(List<AssetMeter> mList);
	public int batchInsertPrepaid(List<AssetMeter> mList);
	public int batchInsertCredit(List<VendMeterInitialCreditAmount> mList);

	public List<AssetMeter> getListGroupByFwVersion();
	
	/**
	 * 根据model获取当前版本号
	 * @Description 
	 * @return List<AssetMeter>
	 * <AUTHOR> 
	 * @Time 2018年2月22日 下午3:34:02
	 */
	public List<AssetMeter> getFwVersionGroupByModel(String modelTypeId);
	/**
	 * 获取电表详细数据，包含集中器信息
	 * @Description 
	 * <AUTHOR> 
	 * @Time 2018年5月14日 下午3:34:02
	 */
	public AssetMeter getMeterDetailInfo(String id);
	/**
	 * 根据SN号获取电表数据
	 * @Description 
	 * @param sn
	 * @return AssetMeter
	 * <AUTHOR> 
	 * @Time 2018年8月22日 上午10:29:59
	 */
	public AssetMeter getEntityBySN(String sn);
	public List<AssetMeter> getBySns(List<String> sn);
	public List<AssetMeter> getByMacs(List<String> sn);
	public List<AssetMeter> getByIds(List<String> ids);
	public JqGridResponseTo getForJqGridAdvanced(JqGridSearchTo jqGridSearchTo);
	public List<AssetMeter> getForJqGridPLC(String sn) ;
	
	public List<String> getMeterIdsByCommunicatorId(String communicatorId);
	public List<AssetMeter> getListLimitTwenty(Map<String, Object> p);
	public List<AssetMeter> getListOfDCU(Map<String, Object> p);
	public List<AssetMeter> getPreListOfDCU(Map<String, Object> p);
	public AssetMeterDto getMeterDtoInfo(String id);
	
	public List<AssetMeter> getMeter4Template(JqGridSearchTo jqGridSearchTo);
	
	public void savePLCMeter(AssetMeter meter);
	
	public AssetMeter getEntityPLC(String sn);
	
	public AssetMeter getEntityPLCExtend(String id);
	
	public void deletePrepareMeter(List<String> ids);
	
	public long getCountPrepare(AssetMeter meter);
	
	public AssetMeter getEntityByMac(String mac);
	public AssetMeter getEntityByMacPLC(String mac);
	
	public void insertCredit(VendMeterInitialCreditAmount creditAmount);
	
	public void deletePpmAssetMeter(String meterId);
	
}