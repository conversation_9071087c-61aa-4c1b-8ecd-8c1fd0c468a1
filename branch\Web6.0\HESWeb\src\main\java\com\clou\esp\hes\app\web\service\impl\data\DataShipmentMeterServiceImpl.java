/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataShipmentMeter{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：赵久霆
 * 创建于：2018-11-18 07:02:58
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.data;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.data.DataShipmentMeterDao;
import com.clou.esp.hes.app.web.model.asset.ImportAssetGprsMeter;
import com.clou.esp.hes.app.web.model.data.DataShipmentMeter;
import com.clou.esp.hes.app.web.service.data.DataShipmentMeterService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dataShipmentMeterService")
public class DataShipmentMeterServiceImpl  extends CommonServiceImpl<DataShipmentMeter>  implements DataShipmentMeterService {

	@Resource
	private DataShipmentMeterDao dataShipmentMeterDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dataShipmentMeterDao);
    }
	@SuppressWarnings("rawtypes")
	public DataShipmentMeterServiceImpl() {}
	@Override
	public int batchInsert(List<ImportAssetGprsMeter> list) {
		return this.dataShipmentMeterDao.batchInsert(list);
	}
	
	
}