package ch.iec.tc57._2011.meterreadschedule;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import javax.xml.ws.Service;

/**
 * This class was generated by Apache CXF 3.1.1
 * 2018-02-01T09:54:34.793+08:00
 * Generated source version: 3.1.1
 * 
 */
@WebServiceClient(name = "MeterReadSchedule", 
                  wsdlLocation = "file:/F:/workspace/hesdesign/IEC61968/wsdl/MeterReadSchedule.wsdl",
                  targetNamespace = "http://iec.ch/TC57/2011/MeterReadSchedule") 
public class MeterReadSchedule extends Service {

    public final static URL WSDL_LOCATION;

    public final static QName SERVICE = new QName("http://iec.ch/TC57/2011/MeterReadSchedule", "MeterReadSchedule");
    public final static QName MeterReadSchedulePort = new QName("http://iec.ch/TC57/2011/MeterReadSchedule", "MeterReadSchedule_Port");
    static {
        URL url = null;
        try {
            url = new URL("file:/F:/workspace/hesdesign/IEC61968/wsdl/MeterReadSchedule.wsdl");
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(MeterReadSchedule.class.getName())
                .log(java.util.logging.Level.INFO, 
                     "Can not initialize the default wsdl from {0}", "file:/F:/workspace/hesdesign/IEC61968/wsdl/MeterReadSchedule.wsdl");
        }
        WSDL_LOCATION = url;
    }

    public MeterReadSchedule(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public MeterReadSchedule(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public MeterReadSchedule() {
        super(WSDL_LOCATION, SERVICE);
    }
    
    public MeterReadSchedule(WebServiceFeature ... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }

    public MeterReadSchedule(URL wsdlLocation, WebServiceFeature ... features) {
        super(wsdlLocation, SERVICE, features);
    }

    public MeterReadSchedule(URL wsdlLocation, QName serviceName, WebServiceFeature ... features) {
        super(wsdlLocation, serviceName, features);
    }    




    /**
     *
     * @return
     *     returns MeterReadSchedulePort
     */
    @WebEndpoint(name = "MeterReadSchedule_Port")
    public MeterReadSchedulePort getMeterReadSchedulePort() {
        return super.getPort(MeterReadSchedulePort, MeterReadSchedulePort.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns MeterReadSchedulePort
     */
    @WebEndpoint(name = "MeterReadSchedule_Port")
    public MeterReadSchedulePort getMeterReadSchedulePort(WebServiceFeature... features) {
        return super.getPort(MeterReadSchedulePort, MeterReadSchedulePort.class, features);
    }

}
