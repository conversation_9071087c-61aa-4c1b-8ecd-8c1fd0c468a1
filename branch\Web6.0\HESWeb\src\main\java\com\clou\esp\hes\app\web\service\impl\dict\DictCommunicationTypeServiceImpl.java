/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictCommunicationType{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-18 06:06:26
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.dict;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.dict.DictCommunicationTypeDao;
import com.clou.esp.hes.app.web.model.dict.DictCommunicationType;
import com.clou.esp.hes.app.web.service.dict.DictCommunicationTypeService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dictCommunicationTypeService")
public class DictCommunicationTypeServiceImpl  extends CommonServiceImpl<DictCommunicationType>  implements DictCommunicationTypeService {

	@Resource
	private DictCommunicationTypeDao dictCommunicationTypeDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dictCommunicationTypeDao);
    }
	@SuppressWarnings("rawtypes")
	public DictCommunicationTypeServiceImpl() {}
	
	
}