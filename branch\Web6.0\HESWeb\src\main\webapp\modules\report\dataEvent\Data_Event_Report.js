/*图表的js开始*/
var myChart0 = document.getElementById('main0');

//自适应宽高

var myChartContainer = function() {

	$("#main0").width($(".col-md-12").width());
	$("#main0").height("500px");

};
myChartContainer();

var myChart0 = echarts.init(myChart0);

//浏览器大小改变时重置大小
window.onresize = function() {
	myChartContainer();

	myChart0.resize();

};
var main0 = echarts.init(document.getElementById('main0'));

main0.setOption({
	color: ['#3398DB'],
	tooltip: {
		trigger: 'axis',
		axisPointer: { // 坐标轴指示器，坐标轴触发有效
			type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
		}
	},
	toolbox: {
		show: true,
		feature: {
			mark: {
				show: true
			},
			dataView: {
				show: true,
				readOnly: false
			},
			magicType: {
				show: true,
				type: ['line', 'bar']
			},
			restore: {
				show: true
			},
			saveAsImage: {
				show: true
			}
		}
	},
	grid: {
		left: '3%',
		right: '4%',
		bottom: '3%',
		containLabel: true
	},
	xAxis: [{
		type: 'category',
		data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
		axisTick: {
			alignWithLabel: true
		}
	}],
	yAxis: [{
		type: 'value'
	}],
	series: [{
		name: '直接访问',
		type: 'bar',
		barWidth: '60%',
		data: [52, 100, 152, 180, 140, 130, 60]
	}]
});

function echarts_toggle() {
	$("#jqgrid_content").hide();
	$("#main0").show();
	myChartContainer();
}

function jqgrid_toggle() {
	$("#jqgrid_content").show();
	$("#main0").hide();

}

/*以下代码是实现自动显示当前日期的功能函数*/
function today(t) {
	var today = new Date();
	var h = today.getFullYear();
	var m = today.getMonth() + 1;
	var d = today.getDate();
	var y = d - 1;
	m = m < 10 ? "0" + m : m; //  这里判断月份是否<10,如果是在月份前面加'0'
	d = d < 10 ? "0" + d : d; //  这里判断日期是否<10,如果是在日期前面加'0'
	if(t == 0) {
		return m + "/" + d + "/" + h;
	} else {
		var day1 = new Date();
		day1.setTime(day1.getTime() - 24 * 60 * 60 * 1000);
		return(day1.getMonth() + 1) + "/" + day1.getDate() + "/" + day1.getFullYear();
	}
}
document.getElementById("start").value = today(1);
document.getElementById("end").value = today(0); //获取文本id并且传入当前日期