alter table ASSET_METER add (KEY_FLAG DECIMAL(4,0));
alter table ASSET_METER add (SRC_ADDR DECIMAL(4,0) default 1);

alter table DATA_METER_EVENT add (ID_TYPE DECIMAL(4,0) default 1);
alter table DATA_SCHEDULE_MISS_DATA add (TASK_TV datetime(6));
alter table DATA_SCHEDULE_MISS_DATA add (TASK_STATE DECIMAL(2,0));
alter table DATA_SCHEDULE_MISS_DATA add (FAILED_INFO VARCHAR(64));


CREATE TABLE SYS_DATAITEM_CALC
(
	DATAITEM_ID VARCHAR(64) NOT NULL, 
	DAT<PERSON>ITEM_TYPE DECIMAL(4,0) NOT NULL
);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('11.26.0.1.1.1.12.0.0.0.0.0.0.0.224.3.72.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DAT<PERSON><PERSON>EM_TYPE) values ('11.26.0.1.1.1.12.0.0.0.0.1.0.0.224.3.72.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('11.26.0.1.1.1.12.0.0.0.0.2.0.0.224.3.72.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('11.26.0.1.1.1.12.0.0.0.0.3.0.0.224.3.72.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('11.26.0.1.1.1.12.0.0.0.0.4.0.0.224.3.72.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('11.26.0.1.19.1.12.0.0.0.0.0.0.0.224.3.72.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('11.26.0.1.19.1.12.0.0.0.0.1.0.0.224.3.72.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('11.26.0.1.19.1.12.0.0.0.0.2.0.0.224.3.72.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('11.26.0.1.19.1.12.0.0.0.0.3.0.0.224.3.72.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('11.26.0.1.19.1.12.0.0.0.0.4.0.0.224.3.72.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('11.26.0.1.5.1.12.0.0.0.0.0.0.0.224.3.73.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('11.26.0.1.5.1.12.0.0.0.0.1.0.0.224.3.73.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('11.26.0.1.5.1.12.0.0.0.0.2.0.0.224.3.73.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('11.26.0.1.5.1.12.0.0.0.0.3.0.0.224.3.73.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('11.26.0.1.5.1.12.0.0.0.0.4.0.0.224.3.73.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('11.26.0.1.13.1.12.0.0.0.0.0.0.0.224.3.73.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('11.26.0.1.13.1.12.0.0.0.0.1.0.0.224.3.73.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('11.26.0.1.13.1.12.0.0.0.0.2.0.0.224.3.73.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values  ('11.26.0.1.13.1.12.0.0.0.0.3.0.0.224.3.73.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('11.26.0.1.13.1.12.0.0.0.0.4.0.0.224.3.73.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('13.26.0.1.1.1.12.0.0.0.0.0.0.0.224.3.72.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('13.26.0.1.1.1.12.0.0.0.0.1.0.0.224.3.72.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('13.26.0.1.1.1.12.0.0.0.0.2.0.0.224.3.72.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('13.26.0.1.1.1.12.0.0.0.0.3.0.0.224.3.72.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('13.26.0.1.1.1.12.0.0.0.0.4.0.0.224.3.72.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('13.26.0.1.19.1.12.0.0.0.0.0.0.0.224.3.72.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('13.26.0.1.19.1.12.0.0.0.0.1.0.0.224.3.72.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('13.26.0.1.19.1.12.0.0.0.0.2.0.0.224.3.72.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('13.26.0.1.19.1.12.0.0.0.0.3.0.0.224.3.72.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('13.26.0.1.19.1.12.0.0.0.0.4.0.0.224.3.72.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('13.26.0.1.5.1.12.0.0.0.0.0.0.0.224.3.73.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('13.26.0.1.5.1.12.0.0.0.0.1.0.0.224.3.73.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('13.26.0.1.5.1.12.0.0.0.0.2.0.0.224.3.73.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('13.26.0.1.5.1.12.0.0.0.0.3.0.0.224.3.73.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('13.26.0.1.5.1.12.0.0.0.0.4.0.0.224.3.73.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('13.26.0.1.13.1.12.0.0.0.0.0.0.0.224.3.73.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('13.26.0.1.13.1.12.0.0.0.0.1.0.0.224.3.73.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('13.26.0.1.13.1.12.0.0.0.0.2.0.0.224.3.73.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('13.26.0.1.13.1.12.0.0.0.0.3.0.0.224.3.73.0',1);
Insert into SYS_DATAITEM_CALC (DATAITEM_ID,DATAITEM_TYPE) values ('13.26.0.1.13.1.12.0.0.0.0.4.0.0.224.3.73.0',1);

truncate table DICT_SERVICE_ATTRIBUTE;
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (4,'UCI.DittPort','Ditt proxy listen port','Integer','9999',null,null,null,5);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (3,'Schedule.TimeSynchronizationRange','Time synchronization range (Unit: Second)','Integer','180','180','600',null,45);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Export.StartTime','Meter data/event export start time','Date','03:32:00',null,null,'HH:mm:ss',3);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Export.Cycle','Meter data/event export cycle (Unit: Hour)','Integer','4','3','12',null,4);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Data.StorageCycle','Meter data/event storage cycle (Unit: Month)','Integer','12','3','65535',null,5);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (2,'MQBus.IP','Message bus server IP address','String','127.0.0.1','1','1',null,1);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (2,'MQBus.Port','Message bus server listen port','Integer','61616','0','65535',null,2);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (1,'Channel.AssetReloadCycle','Service reload asset from database (Unit: Hour)','Integer','12','6','65535',null,3);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (1,'Channel.DLMS.ListenPort','DLMS meter/DCU listen port','Integer','9800','0','65535',null,1);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (1,'Channel.Hall.ListenPort','Hall service listen port','Integer','0','0','65535',null,2);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (3,'Schedule.AssetReloadCycle','Service reload asset from database (Unit: Hour)','Integer','12','6','65535',null,4);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (3,'Schedule.RequestRetryTimes','The times of resending request after timeout','Integer','0','1','2',null,3);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (3,'Schedule.RequestTimeout','Request timeout (Unit: Second)','Integer','60','30','120',null,2);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (3,'Schedule.Concurrency','The count of concurrency task','Integer','5000','1000','50000',null,1);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (4,'UCI.AssetReloadCycle','Service reload asset from database (Unit: Hour)','Integer','12','6','65535',null,4);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (4,'UCI.RequestRetryTimes','The times of resending request after timeout','Integer','0','1','3',null,3);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (4,'UCI.RequestTimeout','Request timeout (Unit: Second)','Integer','60','30','120',null,2);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (4,'UCI.Concurrency','The count of concurrency task','Integer','1000','1000','50000',null,1);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Task.Export','If execute export task','Enum','Enable',null,null,'Enable;Disable',1);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Export.FtpUrl','Meter data/event export FTP server URL','String','ftp://127.0.0.1/ExportData',null,null,null,2);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (6,'Application.UCI.Interface.Url','UCI interface service URL','String','http://127.0.0.1:8080/UCI-1.0-SNAPSHOT',null,null,null,1);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (1,'Channel.SGPort','SG376 protocol server listen port','Integer','9999',null,null,null,4);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Integrity.StartTime','Schedule calculation task start time','Date','00:05:00',null,null,'HH:mm:ss',9);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (1,'Channel.CSGPort','CSG protocol server listen port','Integer','9904',null,null,null,5);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Task.Interval','If execute interval data task','Enum','Enable',null,null,'Enable;Disable',6);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Integrity.Cycle','Schedule calculation task cycle (Unit: Hour)','Integer','2',null,null,null,10);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Task.CalculationObject','If execute calculation object task','Enum','Enable',null,null,'Enable;Disable',7);
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (5,'Calculation.Task.SalesStatistic','If execute power sales statistic task','Enum','Enable',null,null,'Enable;Disable',8);
