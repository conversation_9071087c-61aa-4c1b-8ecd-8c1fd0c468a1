/*----线路档案表--*/
CREATE TABLE ASSET_LINE
(	
	ID varchar(32) not null, 
	S<PERSON> varchar(32) not null,
	ORG_ID varchar(32) not null,	
	NAME varchar(32), 
	LINE_TYPE DECIMAL(10,0), 
	V<PERSON><PERSON><PERSON>_LEVEL DECIMAL(10,0),
	primary key (id,sn,org_id)
);

/*----变压器档案表--*/
CREATE TABLE ASSET_TRANSFORMER
(	
	ID varchar(32) not null,
	S<PERSON> varchar(32), 
	NAME varchar(32), 
	ORG_ID varchar(32), 
	RATED_CAPACITY DECIMAL(10,2), 
	<PERSON><PERSON> varchar(64),
	primary key (id)
);
alter table ASSET_COMMUNICATOR add (SIM_NUM varchar(32));
alter table ASSET_METER add (<PERSON><PERSON> varchar(32));
alter table ASSET_METER add (LONGITUDE DECIMAL(10,4));
alter table ASSET_METER add (LATITUDE DECIMAL(10,4));

CREATE TABLE DATA_STATISTICS_DEVICE
(	
	ID varchar(32) not null,
	ID_TYPE varchar(32) not null,
	TV DATE not null,
	TV_TYPE DECIMAL not null,	
	PERCENT DECIMAL(10,2),  
	COUNT_CURRENT DECIMAL(10,2),
	primary key (ID,ID_TYPE,TV,TV_TYPE)
);

CREATE TABLE DATA_STATISTICS_EVENT
(	
	EVENT_ID varchar(64) not null, 
	TV DATE not null, 
	TV_TYPE DECIMAL not null,		
	EVENT_NAME varchar(128) not null, 
	PERCENT DECIMAL(10,2), 
	COUNT_CURRENT DECIMAL(10,2),
	primary key (EVENT_ID,TV,TV_TYPE)
);

insert into dict_service_attribute values (4, 'UCI.DittPort', 'Ditt proxy listen port', 'Integer', '9999', null,null,null,5);
insert into sys_service_attribute values ('20010003', 'UCI.DittPort', '9902');
insert into dict_service_attribute values (1, 'Channel.SGPort', 'Sg protocol server listen port', 'Integer', '9999', null,null,null,4);
insert into sys_service_attribute values ('20010001', 'Channel.SGPort', '9904');