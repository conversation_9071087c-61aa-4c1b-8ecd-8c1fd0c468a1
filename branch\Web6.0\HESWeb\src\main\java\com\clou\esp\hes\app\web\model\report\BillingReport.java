package com.clou.esp.hes.app.web.model.report;

import java.math.BigDecimal;
import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.power7000g.core.excel.Excel;


/**
 * @ClassName: billingReport
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年8月13日 上午9:35:35
 *
 */
public class BillingReport extends BaseEntity{

	private static final long serialVersionUID = 1L;
	
	@Excel(name = "Meter SN", width = 30)
	private String sn; // sn

	@Excel(name = "Time", width = 30)
	private String tv;
	
	@Excel(name = "Energy Consumption(KWh)", width = 30)
	private BigDecimal energy;
	@Excel(name = "Consumption(USD)", width = 30)
	private BigDecimal consumption;

	@Excel(name = "customer Name", width = 30)
	private String customerName;
	@Excel(name = "Customer SN", width = 30)
	private String customerSn;
	@Excel(name = "Customer Type", width = 30)
	private String customerType;
	
	public BillingReport(){	}

	
	public String getCustomerName() {
		return customerName;
	}



	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}



	public String getCustomerSn() {
		return customerSn;
	}



	public void setCustomerSn(String customerSn) {
		this.customerSn = customerSn;
	}



	public String getCustomerType() {
		return customerType;
	}



	public void setCustomerType(String customerType) {
		this.customerType = customerType;
	}



	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}

	public String getTv() {
		return tv;
	}

	public void setTv(String tv) {
		this.tv = tv;
	}

   

	public BigDecimal getEnergy() {
		return energy;
	}

	public void setEnergy(BigDecimal energy) {
		this.energy = energy;
	}

	public BigDecimal getConsumption() {
		return consumption;
	}

	public void setConsumption(BigDecimal consumption) {
		this.consumption = consumption;
	}
	
	
	

}