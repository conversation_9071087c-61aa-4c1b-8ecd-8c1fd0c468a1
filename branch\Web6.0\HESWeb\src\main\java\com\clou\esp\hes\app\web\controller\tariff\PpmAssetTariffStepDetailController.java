/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class PpmAssetTariffStepDetail{ } 
 * 
 * 摘    要： ppmAssetTariffStepDetail
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2019-09-25 03:58:52
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.tariff;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;

import com.clou.esp.hes.app.web.model.tariff.PpmAssetTariffStepDetail;
import com.clou.esp.hes.app.web.service.tariff.PpmAssetTariffStepDetailService;

/**
 * <AUTHOR>
 * @时间：2019-09-25 03:58:52
 * @描述：ppmAssetTariffStepDetail类
 */
@Controller
@RequestMapping("/ppmAssetTariffStepDetailController")
public class PpmAssetTariffStepDetailController extends BaseController{

 	@Resource
    private PpmAssetTariffStepDetailService ppmAssetTariffStepDetailService;

	/**
	 * 跳转到ppmAssetTariffStepDetail列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/tariff/ppmAssetTariffStepDetailList");
    }

	/**
	 * 跳转到ppmAssetTariffStepDetail新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "ppmAssetTariffStepDetail")
	public ModelAndView ppmAssetTariffStepDetail(PpmAssetTariffStepDetail ppmAssetTariffStepDetail,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(ppmAssetTariffStepDetail.getId())){
			try {
                ppmAssetTariffStepDetail=ppmAssetTariffStepDetailService.getEntity(ppmAssetTariffStepDetail.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("ppmAssetTariffStepDetail", ppmAssetTariffStepDetail);
		}
		return new ModelAndView("/tariff/ppmAssetTariffStepDetail");
	}


	/**
	 * ppmAssetTariffStepDetail查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=ppmAssetTariffStepDetailService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除ppmAssetTariffStepDetail信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(PpmAssetTariffStepDetail ppmAssetTariffStepDetail,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(ppmAssetTariffStepDetailService.deleteById(ppmAssetTariffStepDetail.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存ppmAssetTariffStepDetail信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })PpmAssetTariffStepDetail ppmAssetTariffStepDetail,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        PpmAssetTariffStepDetail t=new  PpmAssetTariffStepDetail();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(ppmAssetTariffStepDetail.getId())){
        	t=ppmAssetTariffStepDetailService.getEntity(ppmAssetTariffStepDetail.getId());
			MyBeanUtils.copyBeanNotNull2Bean(ppmAssetTariffStepDetail, t);
				ppmAssetTariffStepDetailService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            ppmAssetTariffStepDetailService.save(ppmAssetTariffStepDetail);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
	
	
	
}