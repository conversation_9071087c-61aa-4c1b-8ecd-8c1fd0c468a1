package com.clou.esp.hes.app.web.service.impl.asset;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.asset.AssetEntityRelationshipDao;
import com.clou.esp.hes.app.web.model.asset.AssetEntityRelationship;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.asset.AssetTransformer;
import com.clou.esp.hes.app.web.service.asset.AssetEntityRelationshipService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@Component
@Service("assetEntityRelationshipService")
public class AssetEntityRelationshipServiceImpl extends CommonServiceImpl<AssetEntityRelationship> implements  AssetEntityRelationshipService {

	@Resource
	private AssetEntityRelationshipDao assetEntityRelationshipDao;
	
	@Autowired
	public void setCommonService() {
		super.setCommonService(assetEntityRelationshipDao);
	}

	public AssetEntityRelationshipServiceImpl() {};
	@Override
	public JqGridResponseTo findLinkedEntityMeterList(JqGridSearchTo jqGridSearchTo) {
		PageHelper
			.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		PageInfo<AssetEntityRelationship> pageInfo = new PageInfo<AssetEntityRelationship>(assetEntityRelationshipDao.findLinkedEntityMeterList(jqGridSearchTo));
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}
	
	@Override
	public JqGridResponseTo findUnLinkedMeterList(JqGridSearchTo jqGridSearchTo) {
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		PageInfo<AssetMeter> pageInfo = new PageInfo<AssetMeter>(assetEntityRelationshipDao.findUnLinkedMeterList(jqGridSearchTo));
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}
	
	@Override
	public JqGridResponseTo findUnLinkedTransformerList(JqGridSearchTo jqGridSearchTo) {
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		PageInfo<AssetTransformer> pageInfo = new PageInfo<AssetTransformer>(assetEntityRelationshipDao.findUnLinkedTransformerList(jqGridSearchTo));
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}

	@Override
	public int batchInsert(List<AssetEntityRelationship> relationships) {
		return assetEntityRelationshipDao.batchInsert(relationships);
	}
	
	/**
	 * 根据实体删除记录
	 * @param entity
	 */
	@Override
	public void deleteRelationByEntity(AssetEntityRelationship entity) {
		assetEntityRelationshipDao.deleteRelationByEntity(entity);
	}

	@Override
	public JqGridResponseTo findLinkedEntityTransformerList(
			JqGridSearchTo jqGridSearchTo) {
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
	PageInfo<AssetEntityRelationship> pageInfo = new PageInfo<AssetEntityRelationship>(assetEntityRelationshipDao.findLinkedEntityTransformerList(jqGridSearchTo));
	return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}

	@Override
	public List<AssetMeter> findLinkedMeterList(JqGridSearchTo jqGridSearchTo) {
		return assetEntityRelationshipDao.findLinkedMeterList(jqGridSearchTo);
	}
}
