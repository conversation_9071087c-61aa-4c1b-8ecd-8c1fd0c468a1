/*counterUp*/
function startRun() {
				$('.counter').counterUp({
					delay: 10,
					time: 1000
				});
			}
			startRun()

$(document).ready(function(){
   		$('.ring_wrapper').each(function(index, el) {
        var num = $('#count').text() * 3.6;
        var percent = $('#count').text();
        var water = $('#water');
        var page =$('.page');
        var isInProgress;
      
        
        if (num<=180) {
            $(this).find('.right').css('transform', "rotate(" + num + "deg)");
            water.css({
            	'transform' : 'translate(0, ' + (100 - percent) + '%)'
            }); 
         
                
                $('.water__inner').css("height" , percent + '%');
          
        } else {
            $(this).find('.right').css('transform', "rotate(180deg)");
            $(this).find('.left').css('transform', "rotate(" + (num - 180) + "deg)");
            water.css({
            	'transform' : 'translate(0, ' + (100 - percent) + '%)'
            });
            
            $('.water__inner').css("height" , percent + '%');
            
        };
      
    });
});