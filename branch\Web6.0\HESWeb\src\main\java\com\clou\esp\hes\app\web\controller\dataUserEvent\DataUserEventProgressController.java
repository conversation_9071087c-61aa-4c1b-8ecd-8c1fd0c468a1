/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataUserEventProgress{ } 
 * 
 * 摘    要： dataUserEventProgress
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-11-16 02:57:56
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.dataUserEvent;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;

import com.clou.esp.hes.app.web.model.dataUserEvent.DataUserEventProgress;
import com.clou.esp.hes.app.web.service.dataUserEvent.DataUserEventProgressService;

/**
 * <AUTHOR>
 * @时间：2018-11-16 02:57:56
 * @描述：dataUserEventProgress类
 */
@Controller
@RequestMapping("/dataUserEventProgressController")
public class DataUserEventProgressController extends BaseController{

 	@Resource
    private DataUserEventProgressService dataUserEventProgressService;

	/**
	 * 跳转到dataUserEventProgress列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/dataUserEvent/dataUserEventProgressList");
    }

	/**
	 * 跳转到dataUserEventProgress新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dataUserEventProgress")
	public ModelAndView dataUserEventProgress(DataUserEventProgress dataUserEventProgress,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dataUserEventProgress.getId())){
			try {
                dataUserEventProgress=dataUserEventProgressService.getEntity(dataUserEventProgress.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dataUserEventProgress", dataUserEventProgress);
		}
		return new ModelAndView("/dataUserEvent/dataUserEventProgress");
	}


	/**
	 * dataUserEventProgress查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=dataUserEventProgressService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除dataUserEventProgress信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DataUserEventProgress dataUserEventProgress,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dataUserEventProgressService.deleteById(dataUserEventProgress.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存dataUserEventProgress信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DataUserEventProgress dataUserEventProgress,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DataUserEventProgress t=new  DataUserEventProgress();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dataUserEventProgress.getId())){
        	t=dataUserEventProgressService.getEntity(dataUserEventProgress.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dataUserEventProgress, t);
				dataUserEventProgressService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            dataUserEventProgressService.save(dataUserEventProgress);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
	
	
	
}