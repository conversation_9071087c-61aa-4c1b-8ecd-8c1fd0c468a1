/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataStatisticsCustomer{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-11-20 06:36:35
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.data;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.data.DataStatisticsCustomerDao;
import com.clou.esp.hes.app.web.model.data.DataStatisticsCustomer;
import com.clou.esp.hes.app.web.model.report.ImportExportReport;
import com.clou.esp.hes.app.web.service.data.DataStatisticsCustomerService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@Component
@Service("dataStatisticsCustomerService")
public class DataStatisticsCustomerServiceImpl  extends CommonServiceImpl<DataStatisticsCustomer>  implements DataStatisticsCustomerService {

	@Resource
	private DataStatisticsCustomerDao dataStatisticsCustomerDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dataStatisticsCustomerDao);
    }
	@SuppressWarnings("rawtypes")
	public DataStatisticsCustomerServiceImpl() {}
	@Override
	public List<DataStatisticsCustomer> findDataStatisticsCustomerList(
			Map<String, Object> p) {
		return dataStatisticsCustomerDao.findDataStatisticsCustomerList(p);
	}
	
	@Override
	public JqGridResponseTo findDataStatReport(JqGridSearchTo jqGridSearchTo) {
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		PageInfo<DataStatisticsCustomer> pageInfo = new PageInfo<DataStatisticsCustomer>(dataStatisticsCustomerDao.findDataStaticReport(jqGridSearchTo));
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}
	
	@Override
	public List<DataStatisticsCustomer> findDataStatReportList(JqGridSearchTo jqGridSearchTo) {
		return this.dataStatisticsCustomerDao.findDataStaticReport(jqGridSearchTo);
	}
	
	
}