/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetCommunicator{ } 
 * 
 * 摘    要： GPRS Module
DCU
Gateway
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-06 08:04:54
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.asset;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.util.DateTimeFormatterUtil;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.OrganizationUtils;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.model.asset.AssetCalcObj;
import com.clou.esp.hes.app.web.model.asset.AssetCalcObjMap;
import com.clou.esp.hes.app.web.model.asset.AssetLine;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroupMap;
import com.clou.esp.hes.app.web.model.asset.AssetTransformer;
import com.clou.esp.hes.app.web.model.data.DataStatisticsDevice;
import com.clou.esp.hes.app.web.model.data.DataStatisticsEvent;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;
import com.clou.esp.hes.app.web.model.system.SysOrg;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.asset.AssetCalcObjService;
import com.clou.esp.hes.app.web.service.asset.AssetLineManagementService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterGroupMapService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.asset.AssetTransformerService;
import com.clou.esp.hes.app.web.service.data.DataIntegrityService;
import com.clou.esp.hes.app.web.service.data.DataStatisticsDeviceService;
import com.clou.esp.hes.app.web.service.data.DataStatisticsEventService;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemService;
import com.clou.esp.hes.app.web.service.system.SysOrgService;
import com.google.common.collect.Lists;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.format.FormatUtil;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;

import net.sf.json.JSONArray;

/**
 * 
 * @ClassName: AssetLineManagementController
 * @Description: 线路档案
 * <AUTHOR>
 * @date 2018年7月10日 上午11:02:00
 *
 */
@Controller
@RequestMapping("/assetLineManagementController")
public class AssetLineManagementController extends BaseController{

	final static String TIME_FLAG =ResourceUtil.getSessionattachmenttitle("local.time.flag");
	
	@Resource
	private 	DataIntegrityService 			dataIntegrityService;
	@Resource
	private 	AssetLineManagementService 		assetLineManagementService;
	@Resource
	private 	AssetTransformerService 		assetTransformerService;
	@Resource
	private 	AssetMeterService 				assetMeterService;
	@Resource
	private 	DataStatisticsDeviceService 	dataStatisticsDeviceService;
	@Resource
	private 	DataStatisticsEventService 		dataStatisticsEventService;
	@Resource
	private 	DictDataitemService  			dictDataItemService;
	@Resource
	private 	SysOrgService 					sysOrgService;
	@Resource
	private 	DataUserLogService 				dataUserLogService;
	@Resource
	private 	AssetCalcObjService     		calcObjService;
	@Resource
	private 	AssetMeterGroupMapService  		assetMeterGroupMapService;
	
	
	
	@RequestMapping(value  = "assetLineManagement")
    public ModelAndView assetLineManagement(HttpServletRequest request, Model model,String id) {
		String typeReplace = "1:Busbar,2:Feeder";
		model.addAttribute("typeReplace", typeReplace);
		
		StringBuffer vlBuf = new StringBuffer();
		vlBuf.append("500:500kV,").append("220:220kV,").append("110:110kV,")
			.append("10:10kV,").append("6:6kV,").append("3.5:3.5kV");
		model.addAttribute("vlReplace", vlBuf.toString());
		
		AssetLine line = new AssetLine();
		if(StringUtil.isEmpty(id)) { // 新增
			
		} else { // 更新
			AssetLine entity = new AssetLine();
			entity.setId(id);
			
			line = assetLineManagementService.get(entity);
			model.addAttribute("assetLine", line);
		}
		
        return new ModelAndView("/asset/assetLineManagementEdit");
    }
	
	@RequestMapping(value = "findLinesForJqGrid")
    @ResponseBody
    public JqGridResponseTo findLinesForJqGrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request,String sn,String name,boolean flag,String orgId) {
		JqGridResponseTo response = null;
		if(!flag) {
			response = new JqGridResponseTo();
			return response;
		}
		
		jqGridSearchTo.put("sn", sn);
		jqGridSearchTo.put("name", name);
		List<String> orgIdList = OrganizationUtils.getOrgIds(sysOrgService, orgId);
		jqGridSearchTo.put("orgIds", orgIdList);
		response = assetLineManagementService.findLinesForJqGrid(jqGridSearchTo);
		return response;
	}
	
	@Transactional
	@RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(AssetLine line, HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        try {
        	SysUser su=TokenManager.getToken();
        	line = this.assetLineManagementService.get(line);
        	// 查询此线路所有计算对象
        	JqGridSearchTo jqGridSearchTo = new JqGridSearchTo();
        	jqGridSearchTo.put("entityId", line.getId());
    		jqGridSearchTo.put("entityType", 3);
        	List<AssetCalcObj> calcs = assetLineManagementService.findCalObjectsForList(jqGridSearchTo);
        	
        	// 删除计算映射表里边的所有数据
        	for (AssetCalcObj assetCalcObj : calcs) {
        		deleteCalObjMapByEntity(assetCalcObj);
			}
        	
        	// 删除计算对象
        	AssetCalcObj calcEntity = new AssetCalcObj();
        	calcEntity.setEntityId(line.getId());
        	assetLineManagementService.deleteCalcObjByEntityId(calcEntity);
        	
        	// 删除线路
            assetLineManagementService.deleteById(line.getId());
            dataUserLogService.insertDataUserLog(su.getId(), "Asset Managerment", "Delete Line","Delete Line(SN="+ line.getSn()+",Name="+ (StringUtils.isNotEmpty(line.getName())?line.getName():"")+")");
        } catch (Exception e) {
        	e.printStackTrace();
        	j.setSuccess(false);
        	j.setMsg(MutiLangUtil.doMutiLang("system.delFail"));
        }
        j.setMsg(MutiLangUtil.doMutiLang("system.delSuccess"));
        return j;
	}
	
	private void deleteCalObjMapByEntity(AssetCalcObj assetCalcObj) {
		// TODO Auto-generated method stub
		AssetCalcObjMap map = new AssetCalcObjMap();
		map.setId(assetCalcObj.getId());
		
		assetLineManagementService.deleteCalcObjMap(map);
	}

	@Transactional
	@RequestMapping(value = "save")
	@ResponseBody
	public AjaxJson save( HttpServletRequest request,AssetLine line) {
		AjaxJson j = new AjaxJson();
		SysUser su=TokenManager.getToken();
		try {
			if(StringUtil.isEmpty(line.getId())) {// 添加操作
				assetLineManagementService.save(line);
				dataUserLogService.insertDataUserLog(su.getId(), "Asset Managerment", "Add Line","Add Line(SN="+ line.getSn()+",Name="+ (StringUtils.isNotEmpty(line.getName())?line.getName():"")+")");
				j.setMsg(MutiLangUtil.doMutiLang("system.addSucc"));
			} else { // 更新操作
				assetLineManagementService.update(line);
				dataUserLogService.insertDataUserLog(su.getId(), "Asset Managerment", "Edit Line","Edit Line(SN="+ line.getSn()+",Name="+ (StringUtils.isNotEmpty(line.getName())?line.getName():"")+")");
				j.setMsg(MutiLangUtil.doMutiLang("system.updateSucc"));
			}
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			j.setSuccess(false);
			j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
			return j;
		}
		
		return j;
	}
	
	/*
	 * AssetCalcObj 增删改查
	 */
	@RequestMapping(value = "findCalObjForJqGrid")
    @ResponseBody
    public JqGridResponseTo findCalObjForJqGrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request,String id,boolean calcFlag) {
		JqGridResponseTo response = null;
		if(!calcFlag) {
			response = new JqGridResponseTo();
			return response;
		}
		
		jqGridSearchTo.put("entityId", id);
		jqGridSearchTo.put("entityType", 3);
		
		response = assetLineManagementService.findCalObjectsForJqGrid(jqGridSearchTo);
		return response;
	}

	@Transactional
	@RequestMapping(value = "delCalcObj")
    @ResponseBody
    public AjaxJson delCalcObj(AssetCalcObj obj, HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        try {
        	SysUser su=TokenManager.getToken();
            // 删除计算对象下面的map集合
            AssetCalcObjMap map = new AssetCalcObjMap();
            map.setId(obj.getId());
            assetLineManagementService.deleteCalcObjMap(map);
             
            // 删除计算对象
            obj = assetLineManagementService.getCalcObj(obj);
            assetLineManagementService.deleteCalcObj(obj);
            String entityType="";
 			String entityName="";
 			switch(obj.getEntityType()) {
 			case 3://line
 			default:
 				AssetLine line = assetLineManagementService.getEntity(obj.getEntityId());
 				entityType=" Line ";
 				if(line!=null&&StringUtils.isNotEmpty(line.getName())) {
 					entityName=line.getName();
 				}
 				break;
 			case 4://transformer
 				AssetTransformer transformer = assetTransformerService.getEntity(obj.getEntityId());
 				entityType=" Transformer ";
 				if(transformer!=null&&StringUtils.isNotEmpty(transformer.getName())) {
 					entityName=transformer.getName();
 				}
 				break;
 			case 5:// orgs
 				entityType=" Organization ";
 				SysOrg sysOrg = sysOrgService.getEntity(obj.getEntityId());
 				if(sysOrg!=null&&StringUtils.isNotEmpty(sysOrg.getName())) {
 					entityName=sysOrg.getName();
 				}
 				break;
 			}
	        dataUserLogService.insertDataUserLog(su.getId(), "Calculation Object Define", "Delete Calculation Object","Delete Calculation Object(Name="+obj.getName()+",EntityType="+entityType+",EntityName="+entityName+")");
        } catch (Exception e) {
        	e.printStackTrace();
        	j.setSuccess(false);
        	j.setMsg(MutiLangUtil.doMutiLang("system.delFail"));
        }
        j.setMsg(MutiLangUtil.doMutiLang("system.delSuccess"));
        return j;
	}
	
	@Transactional
	@RequestMapping(value = "saveCalcObj")
	@ResponseBody
	public AjaxJson saveCalcObj( HttpServletRequest request,AssetCalcObj obj) {
		AjaxJson j = new AjaxJson();
		try {
			if(StringUtil.isEmpty(obj.getId())) {// 添加操作
				obj.setId(UUID.randomUUID().toString().replace("-", ""));
				assetLineManagementService.saveCalcObj(obj);
				j.setMsg(MutiLangUtil.doMutiLang("system.addSucc"));
			} else { // 更新操作
				assetLineManagementService.updateCalcObj(obj);
				j.setMsg(MutiLangUtil.doMutiLang("system.updateSucc"));
			}
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			j.setSuccess(false);
			j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
			return j;
		}
		
		return j;
	}
	
	@RequestMapping(value  = "assetCalcManagement")
    public ModelAndView assetLineManagement(HttpServletRequest request, Model model,AssetCalcObj obj) {
		String typeReplace = "1:Line Losss,2:Import,3:Export";
		model.addAttribute("typeReplace", typeReplace);
		
		String tvTypeReplace = "1:Daily,2:Monthly";
		model.addAttribute("tvTypeReplace", tvTypeReplace);
		
		if(StringUtil.isEmpty(obj.getId())) {
			obj.setEntityType(3);
		} else {
			obj = assetLineManagementService.getCalcObj(obj);
		}
		model.addAttribute("assetCalcObj", obj);
		
		return new ModelAndView("/asset/assetCalcManagementEdit");
	}
	
	@RequestMapping(value = "findCalObjectMapsForJqGrid")
    @ResponseBody
    public JqGridResponseTo findCalObjectMapsForJqGrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request,String objId) {
		JqGridResponseTo response = null;
		if(null == objId || "".equals(objId)) {
			response = new JqGridResponseTo();
			return response;
		}
		jqGridSearchTo.put("id", objId);
		
		response = assetLineManagementService.findCalObjectMapsForJqGrid(jqGridSearchTo);
		return response;
	}
	
	@RequestMapping(value = "deleteCalcObjMap")
    @ResponseBody
    public AjaxJson deleteCalcObjMap(AssetCalcObjMap obj, HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
		SysUser su=TokenManager.getToken();
		try {
			AssetCalcObj calcObj = this.calcObjService.getEntity(obj.getId());
             assetLineManagementService.deleteCalcObjMap(obj);
 			 dataUserLogService.insertDataUserLog(su.getId(), "Calculation Object Define", "Edit Calculation Object","Edit Calculation Object(Name="+calcObj.getName()+")");
             j.setMsg(MutiLangUtil.doMutiLang("system.delSuccess"));
        } catch (Exception e) {
        	e.printStackTrace();
        	j.setSuccess(false);
        	j.setMsg(MutiLangUtil.doMutiLang("system.delFail"));
        }
        return j;
	}
	
	@Transactional
	@RequestMapping(value = "saveCalcObjMap")
	@ResponseBody
	public AjaxJson saveCalcObjMap( HttpServletRequest request,AssetCalcObjMap obj) {
		AjaxJson j = new AjaxJson();
		SysUser su=TokenManager.getToken();
		try {
			AssetCalcObj calcObj = this.calcObjService.getEntity(obj.getId());
			if(StringUtil.isEmpty(obj.getMeteringId())) {// 添加操作
				// 通过 metersn 查询 meter
				AssetMeter entity = new AssetMeter();
				entity.setSn(obj.getMeterSn());
				entity = assetMeterService.get(entity);
				if(null == entity) {
					j.setSuccess(false);
					j.setMsg(MutiLangUtil.doMutiLang("lineManagementList.meterNotExist"));
					return j;
				}
				
				obj.setMeteringId(entity.getId());
				obj.setMeteringType(1); // 暂时只有电表类型
				assetLineManagementService.saveCalcObjMap(obj);
				j.setMsg(MutiLangUtil.doMutiLang("system.addSucc"));
			} else { // 更新操作
				assetLineManagementService.updateCalcObjMap(obj);
				j.setMsg(MutiLangUtil.doMutiLang("system.updateSucc"));
			}
			dataUserLogService.insertDataUserLog(su.getId(), "Calculation Object Define", "Edit Calculation Object","Edit Calculation Object(Name="+calcObj.getName()+")");
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			j.setSuccess(false);
			j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
			return j;
		}
		
		return j;
	}
	
	@RequestMapping(value  = "assetCalcMapManagement")
    public ModelAndView assetCalcMapManagement(HttpServletRequest request, Model model,AssetCalcObjMap obj) {
		String typeReplace = "1:importAndExportReport.import,2:importAndExportReport.export";
		model.addAttribute("typeReplace", typeReplace);
		
		AssetCalcObj objTmp = calcObjService.getEntity(obj.getId());
		String dateType="Monthly";
		if(objTmp.getTvType()==1) {//日
			dateType="Daily";
		}
		String meterId = obj.getMeteringId();
		AssetMeterGroupMap  groupMap = new AssetMeterGroupMap();
		groupMap.setId(meterId);
		groupMap.setType("1");
		
		AssetMeterGroupMap map =this.assetMeterGroupMapService.get(groupMap);
		String mgId = "";
		if(map!=null) {
			mgId=map.getGroupId();
		}
		
		List<DictDataitem> dataItems = this.calcObjService.getDictDataitems(mgId, dateType);
		String channelReplace = RoletoJson.listToReplaceStr(dataItems, "id", "name");
		model.addAttribute("channelReplace", channelReplace);
		
		AssetCalcObjMap entity = assetLineManagementService.getCalcObjMap(obj);
		model.addAttribute("assetCalcObjMap", entity);
		
		return new ModelAndView("/asset/assetCalcMapManagementEdit");
	}
	
	@RequestMapping(value = "queryMeterSn")
	@ResponseBody
	public AjaxJson queryMeterSn( HttpServletRequest request,String sn) {
		AjaxJson j = new AjaxJson();
		
		AssetMeter meter = new AssetMeter();
		meter.setSn(sn.trim());
		List<AssetMeter> sns = assetLineManagementService.queryMeterSn(meter);
		
		j.setObj(sns);
		j.setSuccess(true);
		return j;
	}
	
	@RequestMapping(value  = "toWebPage")
    public ModelAndView toWebPage(HttpServletRequest request, Model model) {
		//获取昨日日期字符串
		Date d = new Date();
		Map<String, Object> paramsPie = new HashMap<String, Object>();
		d = FormatUtil.addDaysToDate(d, -1);
		String tv = DateUtils.date2Str(d, DateUtils.date_sdf);
		Map<String, Object> params = new HashMap<String, Object>();
		Map<String, Object> result = new HashMap<String, Object>();
		params.put("idTypes", "'5','6','7','8','9','10','11'");
		params.put("profileId", ResourceUtil.getSessionattachmenttitle("profileId"));
		params.put("tv", tv);
		params.put("tvType", 1);
		List<Map<String, Object>> diList = dataIntegrityService
				.getDataIntegrityByMap(params);
		if (diList != null && diList.size() > 0) {
			for (Map<String, Object> di : diList) {
				String idType = (String) di.get("ID_TYPE");
				BigDecimal integrity = (BigDecimal) di.get("INTEGRITY");
				if (integrity == null) {
					integrity = new BigDecimal(0);
				}
				if (StringUtil.isEmpty(idType)) {
					continue;
				}
				if (idType.equals("5")) {
					result.put("dayIntegrity",
							integrity.setScale(2, BigDecimal.ROUND_DOWN)
									.toString());
				} else if (idType.equals("6")) {
					result.put("hundredPercent",
							integrity.setScale(2, BigDecimal.ROUND_DOWN)
									.toString());
				} else if (idType.equals("7")) {
					result.put("middlePercent",
							integrity.setScale(2, BigDecimal.ROUND_DOWN)
									.toString());
				} else if (idType.equals("8")) {
					result.put("zeroPercent",
							integrity.setScale(2, BigDecimal.ROUND_DOWN)
									.toString());
				} else if (idType.equals("9")) {
					result.put("hundredTotal", integrity.intValue());
				} else if (idType.equals("10")) {
					result.put("middleTotal", integrity.intValue());
				} else if (idType.equals("11")) {
					result.put("zeroTotal", integrity.intValue());
				}
			}
		}
		if (result.get("dayIntegrity") == null) {
			result.put("dayIntegrity",
					new BigDecimal("0").setScale(2, BigDecimal.ROUND_DOWN)
							.toString());
		}
		if (result.get("hundredPercent") == null) {
			result.put("hundredPercent",
					new BigDecimal("0").setScale(2, BigDecimal.ROUND_DOWN)
							.toString());
		}
		if (result.get("middlePercent") == null) {
			result.put("middlePercent",
					new BigDecimal("0").setScale(2, BigDecimal.ROUND_DOWN)
							.toString());
		}
		if (result.get("zeroPercent") == null) {
			result.put("zeroPercent",
					new BigDecimal("0").setScale(2, BigDecimal.ROUND_DOWN)
							.toString());
		}
		if (result.get("hundredTotal") == null) {
			result.put("hundredTotal", 0);
		}
		if (result.get("middleTotal") == null) {
			result.put("middleTotal", 0);
		}
		if (result.get("zeroTotal") == null) {
			result.put("zeroTotal", 0);
		}
		request.setAttribute("dayIntegrity", result.get("dayIntegrity"));		//昨日抄读数据百分比
		request.setAttribute("hundredPercent", result.get("hundredPercent"));	//已抄读到的数据量
		request.setAttribute("middlePercent", result.get("middlePercent"));		//已抄读数据百分比
		request.setAttribute("zeroPercent", result.get("zeroPercent"));			//丢失数据量
		request.setAttribute("hundredTotal", result.get("hundredTotal"));		//丢失数据百分比
		request.setAttribute("middleTotal", result.get("middleTotal"));			//抄读失败数据量
		request.setAttribute("zeroTotal", result.get("zeroTotal"));				//抄读失败数据百分比
		//日数据和月数据报表的默认时间
		request.setAttribute("endDay",
				DateUtils.date2Str(d, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
		request.setAttribute("startDay", 
				DateUtils.date2Str(FormatUtil.addDaysToDate(d, -6), DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)));
		SimpleDateFormat sdf = new SimpleDateFormat(" MMMM, yyyy", Locale.ENGLISH);
		request.setAttribute("endMonth", 
				DateUtils.date2Str(d, sdf));
		request.setAttribute("startMonth",
				DateUtils.date2Str(FormatUtil.addMonthsToDate(d, -11), sdf));
		//以上为完整率数据 下面开始写表计数量的获取
		
		paramsPie.put("tvType", 1);// day

		// name=manufacturer;model;commType
		paramsPie.put("tv",tv);
        paramsPie.put("idType", 2);
		List<Map<String, Object>> dataStatisticsDeviceList = dataStatisticsDeviceService.getDataStatisticsDeviceByMaps(paramsPie);
		paramsPie.put("idType", 1);
		List<Map<String, Object>> dataStatisticsDeviceListTotal = dataStatisticsDeviceService.getDataStatisticsDeviceByMaps(paramsPie);
		paramsPie.put("idType", 11);
		List<Map<String, Object>> dataStatisticsConcentratorListTotal = dataStatisticsDeviceService.getDataStatisticsDeviceByMaps(paramsPie);
		
		List<DataStatisticsDevice> dataStatisticsDeviceListPie = new ArrayList();
		List<DataStatisticsDevice> dataStatisticsDeviceListPieTotal = new ArrayList();
		List<DataStatisticsDevice> dataStatisticsConcentratorListPieTotal = new ArrayList();
		if (dataStatisticsDeviceList != null && dataStatisticsDeviceList.size() > 0) {
			for (Map<String, Object> di : dataStatisticsDeviceList) {
				BigDecimal countCurrent = (BigDecimal) di.get("COUNT_CURRENT");
				BigDecimal percent = (BigDecimal) di.get("PERCENT");
				String name = (String) di.get("NAME");
				DataStatisticsDevice dataStatisticsDevice = new DataStatisticsDevice();
				dataStatisticsDevice.setCountCurrent(countCurrent);
				dataStatisticsDevice.setPercent(percent);
				dataStatisticsDevice.setName(name);
				dataStatisticsDeviceListPie.add(dataStatisticsDevice);
			}
		}
		
		if (dataStatisticsDeviceListTotal != null && dataStatisticsDeviceListTotal.size() > 0) {
			for (Map<String, Object> di : dataStatisticsDeviceListTotal) {
				BigDecimal countCurrent = (BigDecimal) di.get("COUNT_CURRENT");
				DataStatisticsDevice dataStatisticsDevice = new DataStatisticsDevice();
				dataStatisticsDevice.setCountCurrent(countCurrent);
				dataStatisticsDeviceListPieTotal.add(dataStatisticsDevice);
			}
		}
		
		if (dataStatisticsConcentratorListTotal != null && dataStatisticsConcentratorListTotal.size() > 0) {
			for (Map<String, Object> di : dataStatisticsConcentratorListTotal) {
				BigDecimal countCurrent = (BigDecimal) di.get("COUNT_CURRENT");
				DataStatisticsDevice dataStatisticsDevice = new DataStatisticsDevice();
				dataStatisticsDevice.setCountCurrent(countCurrent);
				dataStatisticsConcentratorListPieTotal.add(dataStatisticsDevice);
			}
		}
		
		// 按点击数倒序
        Collections.sort(dataStatisticsDeviceListPie, new Comparator<DataStatisticsDevice>() {
            public int compare(DataStatisticsDevice arg0, DataStatisticsDevice arg1) {
            	BigDecimal hits0 = arg0.getCountCurrent();
            	BigDecimal hits1 = arg1.getCountCurrent();
                if (hits0.compareTo(hits1) == -1) {
                    return 1;
                } else if (hits0.compareTo(hits1) == 0) {
                    return 0;
                } else {
                    return -1;
                }
            }
        });
	
	    	BigDecimal deviceTotal = new BigDecimal(0);
	    	BigDecimal deviceTotal1 = new BigDecimal(0);
			if(StringUtil.isNotEmpty(dataStatisticsDeviceListPieTotal) && dataStatisticsDeviceListPieTotal.size() > 0){
				deviceTotal = dataStatisticsDeviceListPieTotal.get(0).getCountCurrent();
				deviceTotal1 = dataStatisticsDeviceListPieTotal.get(0).getCountCurrent();
				request.setAttribute("dataStatisticsDeviceListPieTotal", dataStatisticsDeviceListPieTotal.get(0).getCountCurrent());	
			}
		
		  	List<DataStatisticsDevice> dataStatisticsDeviceListDisplay = new ArrayList();
	        int k =0;
	    	BigDecimal eventDeviceContent = new BigDecimal(0);
	        for(DataStatisticsDevice dataStatisticsDevice1 : dataStatisticsDeviceListPie){
	        	if(k < 4){
	        		eventDeviceContent = eventDeviceContent.add(dataStatisticsDevice1.getCountCurrent());
	        		dataStatisticsDeviceListDisplay.add(dataStatisticsDevice1);
	        	}
	        	k++;
	        }
	        
	        if(k > 4){
	            BigDecimal deviceTotalDisplay = new BigDecimal(0);
	       	 	BigDecimal subDisplay = new BigDecimal(0);
	       	 	subDisplay = deviceTotal.subtract(eventDeviceContent);
	       	 	
	            if(deviceTotal1.compareTo(new BigDecimal(0)) != 0 ){
	            	deviceTotalDisplay = deviceTotal.subtract(eventDeviceContent).divide(deviceTotal1, 2, BigDecimal.ROUND_HALF_UP);
				}
	 
		        DataStatisticsDevice dataStatisticsDevice2 = new DataStatisticsDevice();
		        dataStatisticsDevice2.setPercent(deviceTotalDisplay);
		        dataStatisticsDevice2.setCountCurrent(subDisplay);
		        dataStatisticsDevice2.setName("Other");
		        dataStatisticsDeviceListDisplay.add(dataStatisticsDevice2);
	        }
	 
		
        
		String deviceInfo = JSONArray.fromObject(dataStatisticsDeviceListDisplay).toString();
		request.setAttribute("dataStatisticsDeviceListPie", dataStatisticsDeviceListPie);
		request.setAttribute("deviceInfo", deviceInfo);
	
		
		if(StringUtil.isNotEmpty(dataStatisticsConcentratorListPieTotal) && dataStatisticsConcentratorListPieTotal.size() > 0){
			request.setAttribute("dataStatisticsConcentratorListPieTotal", dataStatisticsConcentratorListPieTotal.get(0).getCountCurrent());
		}
			
		
		//以上为表计数量 下面报警数据
		DataStatisticsEvent dataStatisticsEvent = new DataStatisticsEvent();
		dataStatisticsEvent.setTvTime(tv);
		List<DataStatisticsEvent> dataStatisticsEventList = dataStatisticsEventService.getList(dataStatisticsEvent);
		List<DataStatisticsEvent> dataStatisticsEventListNew = new ArrayList();
		BigDecimal bdTotal = new BigDecimal(0);
		BigDecimal bdTotal1 = new BigDecimal(0);
		for(DataStatisticsEvent dataStatisticsEvent1 : dataStatisticsEventList){
			if(!"total".equalsIgnoreCase(dataStatisticsEvent1.getEventName())){
				dataStatisticsEventListNew.add(dataStatisticsEvent1);
			}else{
				bdTotal = dataStatisticsEvent1.getCountCurrent();
				bdTotal1 = dataStatisticsEvent1.getCountCurrent();
			}
		}
		
		// 按点击数倒序
        Collections.sort(dataStatisticsEventListNew, new Comparator<DataStatisticsEvent>() {
            public int compare(DataStatisticsEvent arg0, DataStatisticsEvent arg1) {
            	BigDecimal hits0 = arg0.getCountCurrent();
            	BigDecimal hits1 = arg1.getCountCurrent();
                if (hits0.compareTo(hits1) == -1) {
                    return 1;
                } else if (hits0.compareTo(hits1) == 0) {
                    return 0;
                } else {
                    return -1;
                }
            }
        });
		
        List<DataStatisticsEvent> dataStatisticsEventListDisplay = new ArrayList();
        int i =0;
    	BigDecimal eventContent = new BigDecimal(0);
        for(DataStatisticsEvent dataStatisticsEvent1 : dataStatisticsEventListNew){
        	if(i < 4){
        		eventContent = eventContent.add(dataStatisticsEvent1.getCountCurrent());
        		dataStatisticsEventListDisplay.add(dataStatisticsEvent1);
        	}
        	i++;
        }
        
        if(i > 4){
        	 BigDecimal bdTotalDisplay = new BigDecimal(0);
        	 BigDecimal subDisplay = new BigDecimal(0);
        	 subDisplay = bdTotal.subtract(eventContent);
        	 
        	 if(bdTotal1.compareTo(new BigDecimal(0)) != 0 ){
                  bdTotalDisplay = bdTotal.subtract(eventContent).divide(bdTotal1, 2, BigDecimal.ROUND_HALF_UP);
			 }

             DataStatisticsEvent dataStatisticsEvent2 = new DataStatisticsEvent();
             dataStatisticsEvent2.setCountCurrent(subDisplay);
             dataStatisticsEvent2.setPercent(bdTotalDisplay);
             dataStatisticsEvent2.setEventName("Other");
             dataStatisticsEventListDisplay.add(dataStatisticsEvent2);
        }
       
        
		String eventInfo = JSONArray.fromObject(dataStatisticsEventListDisplay).toString();
		
		request.setAttribute("bdTotal", bdTotal);
		request.setAttribute("eventInfo", eventInfo);
		request.setAttribute("dataStatisticsEventList", dataStatisticsEventListNew);
		
		return new ModelAndView("/asset/testWebPage");
    }
	
	@RequestMapping(value  = "toWebPage1")
	@ResponseBody
    public AjaxJson toWebPage1(HttpServletRequest request,  String name, Model model) {
		//获取昨日日期字符串
		Date d = new Date();
		Map<String, Object> paramsPie = new HashMap<String, Object>();
		d = FormatUtil.addDaysToDate(d, -1);
		String tv = DateUtils.date2Str(d, DateUtils.date_sdf);
		//以上为完整率数据 下面开始写表计数量的获取
		paramsPie.put("tvType", 1);// day
		// name=manufacturer;model;commType
		if (StringUtil.isNotEmpty(name)) {
			if (name.equals("manufacturer")) {
				paramsPie.put("idType", 2);
			} else if (name.equals("model")) {
				paramsPie.put("idType", 3);
			} else if (name.equals("commType")) {
				paramsPie.put("idType", 4);
			}
		}
		
		paramsPie.put("tv",tv);
		List<Map<String, Object>> dataStatisticsDeviceList = dataStatisticsDeviceService.getDataStatisticsDeviceByMaps(paramsPie);
		
		paramsPie.put("idType", 1);
		List<Map<String, Object>> dataStatisticsDeviceListTotal = dataStatisticsDeviceService.getDataStatisticsDeviceByMaps(paramsPie);
		List<DataStatisticsDevice> dataStatisticsDeviceListPieTotal = new ArrayList();
		List<DataStatisticsDevice> dataStatisticsDeviceListPie = new ArrayList();

		if (dataStatisticsDeviceList != null && dataStatisticsDeviceList.size() > 0) {
			for (Map<String, Object> di : dataStatisticsDeviceList) {
				BigDecimal countCurrent = (BigDecimal) di.get("COUNT_CURRENT");
				BigDecimal percent = (BigDecimal) di.get("PERCENT");
				String name1 = (String) di.get("NAME");
				DataStatisticsDevice dataStatisticsDevice = new DataStatisticsDevice();
		
				DecimalFormat df = new DecimalFormat("#,###");
				String countCurrentFormatter = df.format(countCurrent);
	
				dataStatisticsDevice.setCountCurrentFormatter(countCurrentFormatter);
				dataStatisticsDevice.setCountCurrent(countCurrent);
				dataStatisticsDevice.setPercent(percent);
				dataStatisticsDevice.setName(name1);
				if(countCurrent.compareTo(new BigDecimal(0)) != 0 ){
					dataStatisticsDeviceListPie.add(dataStatisticsDevice);
				}
			
			}
		}
		
		
		// 按点击数倒序
        Collections.sort(dataStatisticsDeviceListPie, new Comparator<DataStatisticsDevice>() {
            public int compare(DataStatisticsDevice arg0, DataStatisticsDevice arg1) {
            	BigDecimal hits0 = arg0.getCountCurrent();
            	BigDecimal hits1 = arg1.getCountCurrent();
                if (hits0.compareTo(hits1) == -1) {
                    return 1;
                } else if (hits0.compareTo(hits1) == 0) {
                    return 0;
                } else {
                    return -1;
                }
            }
        });
		
		if (dataStatisticsDeviceListTotal != null && dataStatisticsDeviceListTotal.size() > 0) {
			for (Map<String, Object> di : dataStatisticsDeviceListTotal) {
				BigDecimal countCurrent = (BigDecimal) di.get("COUNT_CURRENT");
				DataStatisticsDevice dataStatisticsDevice = new DataStatisticsDevice();
				dataStatisticsDevice.setCountCurrent(countCurrent);
				dataStatisticsDeviceListPieTotal.add(dataStatisticsDevice);
			}
		}
		
		BigDecimal deviceTotal = new BigDecimal(0);
    	BigDecimal deviceTotal1 = new BigDecimal(0);
		if(StringUtil.isNotEmpty(dataStatisticsDeviceListPieTotal) && dataStatisticsDeviceListPieTotal.size() > 0){
			deviceTotal = dataStatisticsDeviceListPieTotal.get(0).getCountCurrent();
			deviceTotal1 = dataStatisticsDeviceListPieTotal.get(0).getCountCurrent();
			request.setAttribute("dataStatisticsDeviceListPieTotal", dataStatisticsDeviceListPieTotal.get(0).getCountCurrent());	
		}
	
	  	List<DataStatisticsDevice> dataStatisticsDeviceListDisplay = new ArrayList();
        int k =0;
    	BigDecimal eventDeviceContent = new BigDecimal(0);
        for(DataStatisticsDevice dataStatisticsDevice1 : dataStatisticsDeviceListPie){
        	if(k < 4){
        		eventDeviceContent = eventDeviceContent.add(dataStatisticsDevice1.getCountCurrent());
        		dataStatisticsDeviceListDisplay.add(dataStatisticsDevice1);
        	}
        	k++;
        }
        
        if(k > 4){
            BigDecimal deviceTotalDisplay = new BigDecimal(0);
            BigDecimal subDisplay = new BigDecimal(0);
            subDisplay = deviceTotal.subtract(eventDeviceContent);
            if(deviceTotal1.compareTo(new BigDecimal(0)) != 0 ){
            	deviceTotalDisplay = deviceTotal.subtract(eventDeviceContent).divide(deviceTotal1, 2, BigDecimal.ROUND_HALF_UP);
			}
	     
	        DataStatisticsDevice dataStatisticsDevice2 = new DataStatisticsDevice();
	        dataStatisticsDevice2.setCountCurrent(subDisplay);
	        dataStatisticsDevice2.setPercent(deviceTotalDisplay);
	        dataStatisticsDevice2.setName("Other");
	        dataStatisticsDeviceListDisplay.add(dataStatisticsDevice2);
        }

		String deviceInfo = JSONArray.fromObject(dataStatisticsDeviceListDisplay).toString();

		AjaxJson j=new AjaxJson();
		Map<String, Object> attributes = new HashMap<>();
		attributes.put("dataStatisticsDeviceListPie", dataStatisticsDeviceListPie);
		attributes.put("deviceInfo", deviceInfo);
	    j.setAttributes(attributes);
	    return j;
    }
	
	
	
}
