/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataFwuJob{ } 
 * 
 * 摘    要： dataFwuJob
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-12-29 08:37:48
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.data;

import java.util.Date;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DataFwuJobDfj extends BaseEntity {

	/**
	 * 意义，目的和功能，以及被用到的地方<br>
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DataFwuJobDfj() {
	}

	private String deviceTypeDfj;
	private String snDfj;
	private String manufacturerIdDfj;
	private String modelNameDfj;
	private String currentVesionDfj;
	private String newVersionDfj;
	private Date startTimeDfj;
	private Date expiryTimeDfj;
	private String stateDfj;
	private Date lastExecTimeDfj;
	private String failedReasonDfj;
	private String blockSizeDfj;
	private String blockCountDfj;
	private String fwuProGressDfj;
	private String meterIdDfj;

	public String getDeviceTypeDfj() {
		return deviceTypeDfj;
	}

	public void setDeviceTypeDfj(String deviceTypeDfj) {
		this.deviceTypeDfj = deviceTypeDfj;
	}

	public String getSnDfj() {
		return snDfj;
	}

	public void setSnDfj(String snDfj) {
		this.snDfj = snDfj;
	}

	public String getManufacturerIdDfj() {
		return manufacturerIdDfj;
	}

	public void setManufacturerIdDfj(String manufacturerIdDfj) {
		this.manufacturerIdDfj = manufacturerIdDfj;
	}

	public String getModelNameDfj() {
		return modelNameDfj;
	}

	public void setModelNameDfj(String modelNameDfj) {
		this.modelNameDfj = modelNameDfj;
	}

	public String getCurrentVesionDfj() {
		return currentVesionDfj;
	}

	public void setCurrentVesionDfj(String currentVesionDfj) {
		this.currentVesionDfj = currentVesionDfj;
	}

	public String getNewVersionDfj() {
		return newVersionDfj;
	}

	public void setNewVersionDfj(String newVersionDfj) {
		this.newVersionDfj = newVersionDfj;
	}

	public Date getStartTimeDfj() {
		return startTimeDfj;
	}

	public void setStartTimeDfj(Date startTimeDfj) {
		this.startTimeDfj = startTimeDfj;
	}

	public Date getExpiryTimeDfj() {
		return expiryTimeDfj;
	}

	public void setExpiryTimeDfj(Date expiryTimeDfj) {
		this.expiryTimeDfj = expiryTimeDfj;
	}

	public String getStateDfj() {
		return stateDfj;
	}

	public void setStateDfj(String stateDfj) {
		this.stateDfj = stateDfj;
	}

	public Date getLastExecTimeDfj() {
		return lastExecTimeDfj;
	}

	public void setLastExecTimeDfj(Date lastExecTimeDfj) {
		this.lastExecTimeDfj = lastExecTimeDfj;
	}

	public String getFailedReasonDfj() {
		return failedReasonDfj;
	}

	public void setFailedReasonDfj(String failedReasonDfj) {
		this.failedReasonDfj = failedReasonDfj;
	}

	public String getBlockSizeDfj() {
		return blockSizeDfj;
	}

	public void setBlockSizeDfj(String blockSizeDfj) {
		this.blockSizeDfj = blockSizeDfj;
	}

	public String getBlockCountDfj() {
		return blockCountDfj;
	}

	public void setBlockCountDfj(String blockCountDfj) {
		this.blockCountDfj = blockCountDfj;
	}

	public String getFwuProGressDfj() {
		return fwuProGressDfj;
	}

	public void setFwuProGressDfj(String fwuProGressDfj) {
		this.fwuProGressDfj = fwuProGressDfj;
	}

	public String getMeterIdDfj() {
		return meterIdDfj;
	}

	public void setMeterIdDfj(String meterIdDfj) {
		this.meterIdDfj = meterIdDfj;
	}

}