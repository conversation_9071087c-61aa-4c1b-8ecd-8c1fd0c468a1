/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeasurementProfileDi{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-02-28 02:48:00
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.asset;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.asset.AssetMeasurementProfileDiDao;
import com.clou.esp.hes.app.web.model.asset.AssetMeasurementProfileDi;
import com.clou.esp.hes.app.web.service.asset.AssetMeasurementProfileDiService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("assetMeasurementProfileDiService")
public class AssetMeasurementProfileDiServiceImpl  extends CommonServiceImpl<AssetMeasurementProfileDi>  implements AssetMeasurementProfileDiService {

	@Resource
	private AssetMeasurementProfileDiDao assetMeasurementProfileDiDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(assetMeasurementProfileDiDao);
    }
	@SuppressWarnings("rawtypes")
	public AssetMeasurementProfileDiServiceImpl() {}
	
	@Override
	public Integer deleteByEntity(AssetMeasurementProfileDi entity) {
		return assetMeasurementProfileDiDao.deleteByEntity(entity);
	}
	
	
}