/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataMeterDataEnergyMinutely{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-24 03:20:08
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.data;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.data.DataMdInsMinutelyDao;
import com.clou.esp.hes.app.web.model.data.DataMdInsMinutely;
import com.clou.esp.hes.app.web.service.data.DataMdInsMinutelyService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dataMdInsMinutelyService")
public class DataMdInsMinutelyServiceImpl  extends CommonServiceImpl<DataMdInsMinutely>  implements DataMdInsMinutelyService {

	@Resource
	private DataMdInsMinutelyDao 		dataMdInsMinutelyDao;

	@Override
	public DataMdInsMinutely getLastInfoByDeviceId(String deviceId) {
		return dataMdInsMinutelyDao.getLastInfoByDeviceId(deviceId);
	}
	
	@Override
	public String getLastProfileDaily(String deviceId) {
		return dataMdInsMinutelyDao.getLastProfileDaily(deviceId);
	}


	@Override
	public void setCommonService() {
		super.setCommonService(dataMdInsMinutelyDao);
	}

}