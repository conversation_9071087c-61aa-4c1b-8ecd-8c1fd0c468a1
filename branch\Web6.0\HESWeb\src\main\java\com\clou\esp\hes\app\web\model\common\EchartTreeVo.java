package com.clou.esp.hes.app.web.model.common;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.clou.esp.hes.app.web.core.util.SpringContextUtil;
import com.clou.esp.hes.app.web.model.asset.AssetCommunicator;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.service.asset.AssetCommunicatorService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.power7000g.core.util.json.AjaxJson;

public class EchartTreeVo {
	
	
	private 	String 		name;
	private 	String 		value;
	private 	Integer 	level;
	private 	String 		parentName;
	private     String      mac;
	
	private 	String  	shortAddress;
	private 	String  	signalIntensity;
	private 	String  	netStatus;
	private 	String  	phaseInfo;
	
	
	private List<EchartTreeVo> children =Lists.newArrayList();
	
	
	public String getParentName() {
		return parentName;
	}
	public void setParentName(String parentName) {
		this.parentName = parentName;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	public List<EchartTreeVo> getChildren() {
		return children;
	}
	public void setChildren(List<EchartTreeVo> children) {
		this.children = children;
	}
	public Integer getLevel() {
		return level;
	}
	public void setLevel(Integer level) {
		this.level = level;
	}
	public String getShortAddress() {
		return shortAddress;
	}
	public void setShortAddress(String shortAddress) {
		this.shortAddress = shortAddress;
	}
	public String getSignalIntensity() {
		return signalIntensity;
	}
	public void setSignalIntensity(String signalIntensity) {
		this.signalIntensity = signalIntensity;
	}
	public String getNetStatus() {
		return netStatus;
	}
	public void setNetStatus(String netStatus) {
		this.netStatus = netStatus;
	}
	public String getPhaseInfo() {
		return phaseInfo;
	}
	public void setPhaseInfo(String phaseInfo) {
		this.phaseInfo = phaseInfo;
	}
	
	
public String getMac() {
		return mac;
	}
	public void setMac(String mac) {
		this.mac = mac;
	}
	//	//节点名
//	var childSn = values[0].value;
//	//短地址
//	var shortAddress = values[1].value;
//	//父节点
//	var fatherSn = values[2].value;
//	//信号强度
//	var signalIntensity = values[3].value;
//	//网络状态
//	var netStatus = values[4].value;
//	//相位信息
//	var phaseInfo = values[7].value;
	public static void toTree(AjaxJson json,String commSn){
		Map<String,Set<String>> relationMap = Maps.newHashMap();
		Map<String,String> childMap = Maps.newHashMap();
		Map<String,Set<String>> fatherMap = Maps.newHashMap();
		Map<String,String> map = Maps.newHashMap();
		AssetMeterService assetMeterService =(AssetMeterService) SpringContextUtil.getBean("assetMeterService");
		AssetCommunicatorService assetCommunicatorService =(AssetCommunicatorService) SpringContextUtil.getBean("assetCommunicatorService");
		AssetCommunicator comm = new AssetCommunicator();
		comm.setSn(commSn);
		comm=assetCommunicatorService.get(comm);
		AssetMeter meterTmp = new AssetMeter();
		meterTmp.setCommunicatorId(comm.getId());
		List<AssetMeter> list1 = assetMeterService.getList(meterTmp);
		if(list1!=null) {
			map.put(comm.getMac(), commSn);
			for(AssetMeter meter:list1) {
				map.put(meter.getMac(), meter.getSn());
			}
		}
		
		//attributes.arrays.structures
		JSONObject jsonObject = (JSONObject)JSONObject.toJSON(json.getAttributes().get("arrays"));
		if(jsonObject==null) {
			return;
		}
		JSONArray array = jsonObject.getJSONArray("structures");
		if(array==null||array.size()==0) {
			return;
		}
		List<EchartTreeVo> list = Lists.newArrayList();
		List<String> unLinkList = Lists.newArrayList();
		for(int i=0;i<array.size();i++) {
			JSONObject jsonTmp = array.getJSONObject(i);
			JSONArray  arrayTmp = jsonTmp.getJSONArray("values");
			if(arrayTmp==null) {
				continue;
			}
			String childMac=arrayTmp.getJSONObject(0).getString("value");
			String shortAddress=arrayTmp.getJSONObject(1).getString("value");
			String fatherMac=arrayTmp.getJSONObject(2).getString("value");
			String signalIntensity=arrayTmp.getJSONObject(3).getString("value");
			String netStatus=arrayTmp.getJSONObject(4).getString("value");
			String phaseInfo=arrayTmp.getJSONObject(7).getString("value");
			
			EchartTreeVo vo = new EchartTreeVo();
			String childrenSn=map.get(childMac)==null?childMac:map.get(childMac);
			String fatherSn=map.get(fatherMac)==null?fatherMac:map.get(fatherMac);
			vo.setName(childrenSn);
			vo.setMac(childMac);
			vo.setValue(childrenSn);
			vo.setParentName(fatherSn);
			vo.setShortAddress(shortAddress);
			vo.setSignalIntensity(signalIntensity);
			vo.setPhaseInfo(phaseInfo);
			vo.setNetStatus(netStatus);
			//如果没有父节点,舍弃
			if(StringUtils.isEmpty(fatherMac)) {
				unLinkList.add(childrenSn);
			}else {
				childMap.put(childrenSn, fatherSn);
				Set<String> set = fatherMap.get(fatherSn);
				if(set==null) {
					set =Sets.newHashSet() ;
				}
				set.add(childrenSn);
				fatherMap.put(fatherSn, set);
				list.add(vo);
			}
		}
		
		for(String key : childMap.keySet()) {
			Set<String> tmpList =Sets.newHashSet();
			regulaMap(childMap, key, tmpList);
			relationMap.put(key, tmpList);
		}
		
		List<EchartTreeVo> baseList = Lists.newArrayList(list);//复制一份数据，里面包含有父节点但是没连到集中器上的电表
		EchartTreeVo treeData= EchartTreeVo.getTree(list,unLinkList,commSn,comm.getMac());
		
		//根据relationMap算出树的深度
        int sum = 0;
        Map<String,Integer> levelMap = Maps.newHashMap();
        if(baseList!=null) {
	        for(EchartTreeVo vo:baseList) {
	        	String snTmp = vo.getName();
	        	if(unLinkList.contains(snTmp)) {
	        		continue;
	        	}
	        	
	        	if(relationMap.get(snTmp)!=null) {
	        		int level = relationMap.get(snTmp).size();
	        		if(sum<level) {
	        			sum = level;
	        		}
	        		vo.setLevel(level);
	        		String levelStr = level+"";
	        		if(levelMap.get(levelStr)!=null) {
	        			levelMap.put(levelStr, levelMap.get(levelStr)+1);
	        		}else {
	        			levelMap.put(levelStr, 1);
	        		}
	        	};
	        }
        }
		
        //目前relationMap只找到了当前节点的父节点关系，下面找子节点关系
        for(String key :relationMap.keySet()) {
        	Set<String> setTmp = relationMap.get(key);
        	if(key.equals(commSn)) {//过滤掉集中器sn
        		continue;
        	}
        	if(fatherMap.get(key)!=null) {
        		regulaMap2(fatherMap, key, setTmp);
        	}
        }
        
        json.put("treeData",treeData);
		json.put("unLinkData", unLinkList);    
		json.put("relationMap", relationMap);
		json.put("total", array.size());
		json.put("lossed", unLinkList.size());
		json.put("levelMap", levelMap);
		json.put("treeDeep", sum);
	}
	
	public  static void regulaMap(Map<String,String> childMap,String key,Set<String> set) {
		String value = childMap.get(key);
		set.add(value);
		if(childMap.get(value)!=null) {
			regulaMap(childMap,value,set);
		}
	}
	
	public  static void regulaMap2(Map<String,Set<String>> fatherMap,String key,Set<String> set) {
			for(String keyTmp :fatherMap.get(key)) {
				set.add(keyTmp);//添加关系
				if(fatherMap.get(keyTmp)!=null) {
					regulaMap2(fatherMap, keyTmp, set);
				}
			}
	}
	
	
	 /**
     * @return 最终的树状结构的集合数据
     */
    public static EchartTreeVo getTree(List<EchartTreeVo> data,List<String> unLinkList,String commSn,String commMac) {
    	EchartTreeVo vo = new EchartTreeVo();
        List<EchartTreeVo> list = new ArrayList<>(data);
        // 遍历两次data来组装带有children关联性的对象，如果找到子级就删除menuList的数据
        for (EchartTreeVo entity : data) {
            for (EchartTreeVo entity2 : data) {
                if (entity.getName().equals(entity2.getParentName())) {
                    entity.getChildren().add(entity2);
                    list.remove(entity2);
                }
            }
        }
        
        
        vo.setName(commSn);
    	vo.setMac(commMac);
    	vo.setNetStatus("1");
        //这里没有根节点传入,找出跟节点
        if(list!=null&&list.size()>0) {
        	for (Iterator<EchartTreeVo> iterator = list.iterator(); iterator.hasNext();) {
				EchartTreeVo echartTreeVo = (EchartTreeVo) iterator.next();
				if(!commSn.equals(echartTreeVo.getParentName())) {
					//如果父节点不为跟节点,归为未连上
					unLinkList.add(echartTreeVo.getName());
					if(echartTreeVo.getChildren()!=null&&echartTreeVo.getChildren().size()>0) {
						getChildVo(echartTreeVo,unLinkList);
					}
					iterator.remove();
				}
			}
        	vo.setChildren(list);    
        }
        
        return vo;
    }
    
    public static void getChildVo(EchartTreeVo echartTreeVo,List<String> unLinkList) {
		for(EchartTreeVo vo :echartTreeVo.getChildren()) {
			unLinkList.add(vo.getName());
			if(vo.getChildren()!=null&&vo.getChildren().size()>0) {
				getChildVo(vo, unLinkList);
			}
		}
    }
    
}
