/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeterGroup{ } 
 * 
 * 摘    要： assetMeterGroup
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-01-16 01:58:30
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.asset;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.json.ValidForm;
import com.clou.esp.hes.app.web.core.shiro.service.ShiroManager;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.DateTimeFormatterUtil;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.core.util.XMLUtil;
import com.clou.esp.hes.app.web.model.asset.AssetMeasurementProfile;
import com.clou.esp.hes.app.web.model.asset.AssetMeasurementProfileDi;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroup;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroupValue;
import com.clou.esp.hes.app.web.model.asset.FriendlyPeriod;
import com.clou.esp.hes.app.web.model.asset.StepTariff;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;
import com.clou.esp.hes.app.web.model.dict.DictProfile;
import com.clou.esp.hes.app.web.model.dict.DictProtocol;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.model.tariff.PpmAssetTariffGroup;
import com.clou.esp.hes.app.web.model.tariff.PpmAssetTariffStep;
import com.clou.esp.hes.app.web.service.asset.AssetMeasurementProfileDiService;
import com.clou.esp.hes.app.web.service.asset.AssetMeasurementProfileService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterGroupMapService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterGroupService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterGroupValueService;
import com.clou.esp.hes.app.web.service.data.DataIntegrityService;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.demo.TestDataService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemService;
import com.clou.esp.hes.app.web.service.dict.DictProfileDataItemService;
import com.clou.esp.hes.app.web.service.dict.DictProfileService;
import com.clou.esp.hes.app.web.service.dict.DictProtocolService;
import com.clou.esp.hes.app.web.service.tariff.PpmAssetTariffGroupService;
import com.clou.esp.hes.app.web.service.tariff.PpmAssetTariffStepDetailService;
import com.clou.esp.hes.app.web.service.tariff.PpmAssetTariffStepService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.power7000g.core.util.oConvertUtils;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;
import com.power7000g.core.util.redis.JedisUtils;

import ch.iec.tc57._2011.meterdefineconfig_.Arrays;
import ch.iec.tc57._2011.meterdefineconfig_.Meter;
import ch.iec.tc57._2011.meterdefineconfig_.MeterDefineConfig;
import ch.iec.tc57._2011.meterdefineconfig_.MeterDefineConfig.ReadingType;
import ch.iec.tc57._2011.meterdefineconfig_.Structures;
import ch.iec.tc57._2011.meterdefineconfig_.Values;
import ch.iec.tc57._2011.meterdefineconfigmessage.MeterDefineConfigPayloadType;
import ch.iec.tc57._2011.meterdefineconfigmessage.MeterDefineConfigRequestMessageType;
import ch.iec.tc57._2011.schema.message.HeaderType;
import ch.iec.tc57._2011.schema.message.UserType;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * <AUTHOR>
 * @时间：2018-01-16 01:58:30
 * @描述：assetMeterGroup类
 */
@Controller
@RequestMapping("/assetMeterGroupController")
public class AssetMeterGroupController extends BaseController{
	
	final static String TIME_FLAG =ResourceUtil.getSessionattachmenttitle("local.time.flag");
	final static String TIME_FORMAT=ResourceUtil.getSessionattachmenttitle("local.date.time.formatter");
//	static SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//	static SimpleDateFormat SDF_VIEW = new SimpleDateFormat(TIME_FORMAT);
 	@Resource
    private AssetMeterGroupService assetMeterGroupService;
 	@Resource
    private DictProtocolService dictProtocolService;
 	@Resource
    private AssetMeterGroupValueService assetMeterGroupValueService;
 	@Resource
	private DictDataitemService dictDataitemService;
 	@Resource
    private AssetMeterGroupMapService assetMeterGroupMapService;
 	@Resource
    private DictProfileService dictProfileService;
 	@Resource
    private DictProfileDataItemService dictProfileDataItemService;
 	@Resource
    private AssetMeasurementProfileDiService assetMeasurementProfileDiService;
 	@Resource
    private AssetMeasurementProfileService assetMeasurementProfileService;
 	
 	@Resource
 	private TestDataService testDataService;
 	@Resource
	private DataIntegrityService dataIntegrityService;
 	@Resource
    private DataUserLogService dataUserLogService;
 	
	@Resource
    private PpmAssetTariffGroupService ppmAssetTariffGroupService;
	@Resource
    private PpmAssetTariffStepService ppmAssetTariffStepService;
	@Resource
    private PpmAssetTariffStepDetailService ppmAssetTariffStepDetailService;
	/**
	 * 跳转到assetMeterGroup列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/asset/assetMeterGroupList");
    }
	/**
	 * 时间选择说明
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "timeFormatHelp")
    public ModelAndView timeFormatHelp(HttpServletRequest request, Model model) {
        return new ModelAndView("/asset/timeFormatHelp");
    }
	
	
	/**
	 * 添加日数据
	 * @param startTime
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "addDay")
    public ModelAndView addDay(String startTime,HttpServletRequest request, Model model) {
		String[] spStr=startTime.split("[/ :]");
		model.addAttribute("arbitrary", spStr);
        return new ModelAndView("/asset/addDay");
    }
	
	@RequestMapping(value  = "addPeriod")
	public ModelAndView addPeriod(String startTime,HttpServletRequest request, Model model) {
		String[] spStr=startTime.split("[/ :]");
		model.addAttribute("arbitrary", spStr);
		return new ModelAndView("/asset/addPeriod");
	}
	/**
	 * 添加Season
	 * @param isAdd
	 * @param id
	 * @param weekReplace
	 * @param startTime
	 * @param weekProfile
	 * @param request
	 * @param model
	 * @return
	 * #1 = 
	 * 		$("#month").val('${arbitrary[0]}');
			$("#day").val('${arbitrary[1]}');
			$("#year").val('${arbitrary[2]}');
			$("#hour").val('${arbitrary[3]}');
			$("#minute").val('${arbitrary[4]}');
			$("#second").val('${arbitrary[5]}');
			$("#week").val('${arbitrary[6]}');
			ResourceUtil.getSessionattachmenttitle("local.time.flag")
	 */
	@RequestMapping(value  = "addSeason")
    public ModelAndView addSeason(String startTime,HttpServletRequest request, Model model) {
		model.addAttribute("startYear", DateUtils.getYear());
		model.addAttribute("endYear", DateUtils.getYear()+10);
		String[] spStr;
		String splitSymbolSlash = "[/ :,]";
		String splitSymbolCross = "[- :,]";
	//1 = MM/dd/yyyy, 2 = yyyy/MM/dd, 3=dd/MM/YYYY, 4=dd-MM-yyyy,5 =yyyy-mm-dd
		if("1".equals(TIME_FLAG)){
			spStr=startTime.split(splitSymbolSlash);
			if(spStr.length<7){
				startTime+=",FF";
				spStr=startTime.split(splitSymbolSlash);
			}
			model.addAttribute("arbitrary", spStr);
		}else if("2".equals(TIME_FLAG)){
			spStr=startTime.split(splitSymbolSlash);
			if(spStr.length<7){
				startTime+=",FF";
				spStr=startTime.split(splitSymbolSlash);
			}
			String[] newSpstr = new String[spStr.length];
			newSpstr[0] =spStr[1];
			newSpstr[1] =spStr[2];
			newSpstr[2] =spStr[0];
			newSpstr[3] =spStr[3];
			newSpstr[4] =spStr[4];
			newSpstr[5] =spStr[5];
			newSpstr[6] =spStr[6];

			model.addAttribute("arbitrary", newSpstr);
		}else if("3".equals(TIME_FLAG)){
			spStr=startTime.split(splitSymbolSlash);
			if(spStr.length<7){
				startTime+=",FF";
				spStr=startTime.split(splitSymbolSlash);
			}
			String[] newSpstr = new String[spStr.length];
			newSpstr[0] =spStr[1];
			newSpstr[1] =spStr[0];
			newSpstr[2] =spStr[2];
			newSpstr[3] =spStr[3];
			newSpstr[4] =spStr[4];
			newSpstr[5] =spStr[5];
			newSpstr[6] =spStr[6];
			
			model.addAttribute("arbitrary", newSpstr);
		}else if("4".equals(TIME_FLAG)){
			spStr=startTime.split(splitSymbolCross);
			if(spStr.length<7){
				startTime+=",FF";
				spStr=startTime.split(splitSymbolCross);
			}
			String[] newSpstr = new String[spStr.length];
			newSpstr[0] =spStr[1];
			newSpstr[1] =spStr[0];
			newSpstr[2] =spStr[2];
			newSpstr[3] =spStr[3];
			newSpstr[4] =spStr[4];
			newSpstr[5] =spStr[5];
			newSpstr[6] =spStr[6];
			model.addAttribute("arbitrary", newSpstr);
		}else if("5".equals(TIME_FLAG)){
			spStr=startTime.split(splitSymbolCross);
			if(spStr.length<7){
				startTime+=",FF";
				spStr=startTime.split(splitSymbolCross);
			}
			String[] newSpstr = new String[spStr.length];
			newSpstr[0] =spStr[1];
			newSpstr[1] =spStr[2];
			newSpstr[2] =spStr[0];
			newSpstr[3] =spStr[3];
			newSpstr[4] =spStr[4];
			newSpstr[5] =spStr[5];
			newSpstr[6] =spStr[6];
			model.addAttribute("arbitrary", newSpstr);
		}

        return new ModelAndView("/asset/addSeason");
    }
	/**
	 * 添加Special Day
	 * @param isAdd
	 * @param id
	 * @param dayReplace
	 * @param date
	 * @param dayProfile
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "addSpecialDay")
    public ModelAndView addSpecialDay(String date,HttpServletRequest request, Model model) {
		model.addAttribute("startYear", DateUtils.getYear());
		model.addAttribute("endYear", DateUtils.getYear()+10);
		String[] spStr;
		String splitSymbolSlash = "[/ :,]";
		String splitSymbolCross = "[- :,]";
	//1 = MM/dd/yyyy, 2 = yyyy/MM/dd, 3=dd/MM/YYYY, 4=dd-MM-yyyy,5 =yyyy-mm-dd
		if("1".equals(TIME_FLAG)){
			spStr=date.split(splitSymbolSlash);
			if(spStr.length<4){
				date+=",FF";
				spStr=date.split(splitSymbolSlash);
			}
			model.addAttribute("arbitrary", spStr);
		}else if("2".equals(TIME_FLAG)){
			spStr=date.split(splitSymbolSlash);
			if(spStr.length<4){
				date+=",FF";
				spStr=date.split(splitSymbolSlash);
			}
			String[] newSpstr = new String[spStr.length];
			newSpstr[0] =spStr[1];
			newSpstr[1] =spStr[2];
			newSpstr[2] =spStr[0];
			newSpstr[3] =spStr[3];
	

			model.addAttribute("arbitrary", newSpstr);
		}else if("3".equals(TIME_FLAG)){
			spStr=date.split(splitSymbolSlash);
			if(spStr.length<4){
				date+=",FF";
				spStr=date.split(splitSymbolSlash);
			}
			String[] newSpstr = new String[spStr.length];
			newSpstr[0] =spStr[1];
			newSpstr[1] =spStr[0];
			newSpstr[2] =spStr[2];
			newSpstr[3] =spStr[3];
		
			
			model.addAttribute("arbitrary", newSpstr);
		}else if("4".equals(TIME_FLAG)){
			spStr=date.split(splitSymbolCross);
			if(spStr.length<4){
				date+=",FF";
				spStr=date.split(splitSymbolCross);
			}
			String[] newSpstr = new String[spStr.length];
			newSpstr[0] =spStr[1];
			newSpstr[1] =spStr[0];
			newSpstr[2] =spStr[2];
			newSpstr[3] =spStr[3];
			
			model.addAttribute("arbitrary", newSpstr);
		}else if("5".equals(TIME_FLAG)){
			spStr=date.split(splitSymbolCross);
			if(spStr.length<4){
				date+=",FF";
				spStr=date.split(splitSymbolCross);
			}
			String[] newSpstr = new String[spStr.length];
			newSpstr[0] =spStr[1];
			newSpstr[1] =spStr[2];
			newSpstr[2] =spStr[0];
			newSpstr[3] =spStr[3];
	
			model.addAttribute("arbitrary", newSpstr);
		}
        return new ModelAndView("/asset/addSpecialDay");
    }
	
	@RequestMapping(value = "meterGroupMgmt")
	public ModelAndView meterGroupMgmt(HttpServletRequest request, Model model) {
		String requestType = request.getHeader("X-Requested-With");
		if("XMLHttpRequest".equals(requestType)){
			return null;
		}
		List<DictProtocol> list=dictProtocolService.getAllList();
		String protocolReplace = RoletoJson.listToReplaceStr(list, "id", "name");
		model.addAttribute("protocolReplace", protocolReplace);
		//添加假费率数据
		List<DictDataitem> ddList=new ArrayList<DictDataitem>();
		for(int i=1;i<=4;i++){
			DictDataitem dd=new DictDataitem();
			dd.setId(""+i);
			dd.setName("Rate"+i);
			ddList.add(dd);
		}
		String rateReplace = RoletoJson.listToReplaceStr(ddList, "id", "name",";");
		model.addAttribute("rateReplace", rateReplace);
		
		DictDataitem dd=new DictDataitem();
		dd.put("groupId", "1004002");
		List<DictDataitem> ddtList=dictDataitemService.getList(dd);
		String itemReplace = RoletoJson.listToReplaceStr(ddtList, "id", "name");
		model.addAttribute("itemReplace", itemReplace);
		model.addAttribute("itemList",ddtList);
		
		String obisReplace = RoletoJson.listToReplaceStr(dictDataitemService.getList(dd), "id", "protocolCode");
		model.addAttribute("obisReplace",obisReplace); 
		
//		//计算首页完整率
//		dataIntegrityService.statisticsIntegrity();
		return new ModelAndView("/asset/meterGroupMgmt");
	}
	
	/**
	 * 跳转到assetMeterGroup新增界面
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "assetMeterGroup")
	public ModelAndView assetMeterGroup(AssetMeterGroup assetMeterGroup,HttpServletRequest request, Model model) {
		List<DictProtocol> list=dictProtocolService.getAllList();
		String protocolReplace = RoletoJson.listToReplaceStr(list, "id", "name");
		model.addAttribute("protocolReplace", protocolReplace);
		try {
			if(StringUtil.isNotEmpty(assetMeterGroup.getId())){
	                assetMeterGroup=assetMeterGroupService.getEntity(assetMeterGroup.getId());
			}
			//获取年份，直接拼接字符串得到明年一月一号的时间
			Calendar cal = Calendar.getInstance();
			int year = cal.get(Calendar.YEAR);
			String time = String.valueOf(year + 1) + "-01-01 00:00:00";
			model.addAttribute("defaultsDate", 
					DateUtils.date2Str(DateUtils.parseDate(time, "yyyy-MM-dd HH:mm:ss"),
							new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"))));
		}
		catch (Exception e) {
			e.printStackTrace();
		}
//		model.addAttribute("defaultsDate", "01/01/" + String.valueOf(year + 1) + " 00:00:00");
		model.addAttribute("assetMeterGroup", assetMeterGroup);
		return new ModelAndView("/asset/assetMeterGroup");
	}
	
	/**
	 * 跳转到MeasurementGroup新增界面
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "toMeasurementGroup")
	public ModelAndView toMeasurementGroup(AssetMeterGroup assetMeterGroup, HttpServletRequest request, Model model) {
		//获取所有的协议类型
		List<DictProtocol> list = dictProtocolService.getAllList();
		String protocolReplace = RoletoJson.listToReplaceStr(list, "id", "name");
		model.addAttribute("protocolReplace", protocolReplace);
		if(StringUtil.isNotEmpty(assetMeterGroup.getType())){
			try {
                List<AssetMeterGroup> assetMeterGroupList = assetMeterGroupService.getList(assetMeterGroup);
                if(assetMeterGroupList.size() > 0){
                	String groupReplace = RoletoJson.listToReplaceStr(assetMeterGroupList, "id", "name");
            		model.addAttribute("groupReplace", groupReplace);
                }
            }
            catch (Exception e) {
                e.printStackTrace();
            }
		}
		model.addAttribute("assetMeterGroup", assetMeterGroup);
		return new ModelAndView("/asset/addMeasurementGroup");
	}
	
	/**
	 * 跳转到MeasurementGroup的新增profile页面
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "toAddProfile")
	public ModelAndView toAddProfile(String isAdd, String groupId, String type,
				String id, String profileId, String profileType, String profileName,
				String profileCycleType, String profileCycle, String protocolCode,
				DictProfile dictProfile, HttpServletRequest request, Model model) {
		//获取所有的profile类型
		DictProfile profile = new DictProfile();
		AssetMeterGroup assetMeterGroup = assetMeterGroupService.getEntity(groupId);
		profile.setProtocolId(assetMeterGroup.getProtocolId());
		profile.setProfileType(type);
		List<DictProfile> list = dictProfileService.getList(profile);
		model.addAttribute("protocolId", assetMeterGroup.getProtocolId());
		String profileReplace = RoletoJson.listToReplaceStr(list, "id", "name");
		model.addAttribute("profileReplace", profileReplace);
		if("0".equals(isAdd)){
			model.addAttribute("profileType", "1");
			model.addAttribute("profileCycleType", "Minutely");
			model.addAttribute("profileCycle", "15");
			model.addAttribute("profileCycleReplace", "10:10,15:15,30:30,60:60");
			model.addAttribute("profileName", list.get(0).getId());	//profile name 默认值
			model.addAttribute("isAdd", isAdd);
		}else{
			model.addAttribute("id", id);
			model.addAttribute("profileId", profileId);
			model.addAttribute("profileType", profileType);
			model.addAttribute("profileName", profileId);
			model.addAttribute("profileCycleType", profileCycleType);
			model.addAttribute("profileCycle", profileCycle);
			if("Minutely".equals(profileCycleType)){
				model.addAttribute("profileCycleReplace", "10:10,15:15,30:30,60:60");
			}else{
				model.addAttribute("profileCycleReplace", "1:1");
			}
			model.addAttribute("protocolCode", protocolCode.toString());
			model.addAttribute("isAdd", isAdd);		//0:新增   1:编辑
		}
		return new ModelAndView("/asset/addProfile");
	}
	
	/**
	 * 临时新建的profile 数据保存到redis中
	 * @param request
	 * @param model
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "toAddProfileSaveInRedis")
    public AjaxJson toAddProfileSaveInRedis(String groupId, String profileId, HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
        	List<AssetMeasurementProfileDi> list = new ArrayList<>();
            Map<String, Object> value = new HashMap<>();
    		value.put("listProfileDi", list);
    		JedisUtils.setObjectMap(groupId + profileId, value, 0);
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg("system.abnoOpera");
        }
        return j;
    }
	
	/**
	 * change点击事件，改变ProfileType
	 * @param assetMeterGroup
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "changeProfileType")
    @ResponseBody
    public AjaxJson changeProfileType(String type,String profileId, HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DictProfile dictProfile = new DictProfile();
        try {
        	//获取所有的profile类型
    		dictProfile.setProtocolId(profileId);
    		dictProfile.setProfileType(type);
    		List<DictProfile> list = dictProfileService.getList(dictProfile);
            j.setObj(list);
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg("system.abnoOpera");
        }
        return j;
    }
	
	/**
	 * 
	 * @param assetMeterGroup
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getReferenceGroupReplace")
    @ResponseBody
    public AjaxJson getReferenceGroupReplace(AssetMeterGroup assetMeterGroup,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
        	List<AssetMeterGroup> amgs=assetMeterGroupService.getList(assetMeterGroup);
            j.setObj(amgs);
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg("system.abnoOpera");
        }
        return j;
    }
	
	
	/**
	 * 验证名称唯一
	 */
	@RequestMapping(value = "checkMeterGroupName")
	@ResponseBody
	public ValidForm checkMeterGroupName(AssetMeterGroup assetMeterGroup, HttpServletRequest request,
			HttpServletResponse response) {
		ValidForm v = new ValidForm();
		String param = oConvertUtils.getString(request.getParameter("param"));
		String groupName = assetMeterGroup.getName();
		assetMeterGroup.setName(param);
		List<AssetMeterGroup> list = assetMeterGroupService.getList(assetMeterGroup);
		if (list.size() > 0 && !param.equals(groupName)) {
			v.setInfo("The Group Name already exists");
			v.setStatus("n");
		}
		return v;
	}


	/**
	 * assetMeterGroup查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        Map<String, Object> map = jqGridSearchTo.getMap();
        JqGridResponseTo j = null;
        try {
             j = assetMeterGroupService.getForJqGrid(jqGridSearchTo);
			if("2".equals(map.get("type"))){
				List<Object> list = (List<Object>) j.getRows();
				for (int i = 0; i < list.size(); i++) {
					Map<Object, Object> entity = (Map<Object, Object>) list.get(i);
					AssetMeterGroupValue value = new AssetMeterGroupValue();
	        		value.setGroupId(entity.get("id").toString());
	        		value.setDataitemId("*********");
	        		AssetMeterGroupValue valueTemp = assetMeterGroupValueService.get(value);	//获取对象
	        		MeterDefineConfigRequestMessageType requestMessage = (MeterDefineConfigRequestMessageType) 
	        				XMLUtil.convertXmlStrToObject(MeterDefineConfigRequestMessageType.class, valueTemp.getXmlValue());
	        		MeterDefineConfigPayloadType payloadType = requestMessage.getPayload();
	        		MeterDefineConfig config = payloadType.getMeterDefineConfig();
	        		List<Arrays> arraysList = config.getArrays();
	        		Arrays arrays = arraysList.get(0);
	        		List<Values> valuesList = arrays.getValues();
	        		Values values = valuesList.get(0);
	        		String activateTime = values.getValue();
	        		//转换时间格式, "yyyy-MM-dd HH:mm:ss", "MM/dd/yyyy HH:mm:ss")
	        		entity.put("touActivationTime", DateUtils.dateformat(activateTime, 
	        				"yyyy-MM-dd HH:mm:ss", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
				}
			}
			
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除assetMeterGroup信息
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(AssetMeterGroup assetMeterGroup,HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        try {
            SysUser su = TokenManager.getToken();
            assetMeterGroup = assetMeterGroupService.getEntity(assetMeterGroup.getId());
            if(assetMeterGroupService.deleteById(assetMeterGroup.getId()) > 0){
            	if("2".equals(assetMeterGroup.getType())){
            		//删除asset meter group value数据
            		AssetMeterGroupValue value = new AssetMeterGroupValue();
            		value.setGroupId(assetMeterGroup.getId());
            		value.setDataitemId("*********");
            		assetMeterGroupValueService.delete(value);
            	}
            	dataUserLogService.insertDataUserLog(su.getId(),
	            		"Meter Group Mgmt", "Delete Group", "Delete Group (Group Name=" + assetMeterGroup.getName() + ")");
                j.setMsg(MutiLangUtil.doMutiLang("system.delSuccess"));
            }else{
                j.setSuccess(false);
                j.setMsg(MutiLangUtil.doMutiLang("system.delFail"));
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
    /**
     * 保存TOU
     * @param groupId
     * @param dayList
     * @param weekList
     * @param seasonList
     * @param specialDayList
     * @param request
     * @return
     * j.put("weekDataitemId", "10002");
        j.put("seasonDataitemId", "10003");
        j.put("specialDayDataitemId", "10004");
     */
    @RequestMapping(value = "saveTouGroupData")
    @ResponseBody
    public AjaxJson saveTouGroupData(String groupId,String dayDataitemId,
    		String weekDataitemId,String seasonDataitemId,
    		String specialDayDataitemId,String dayList,String weekList
    		,String seasonList,String limiterList,String specialDayList,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        
        try {
	        JSONArray dayJsons=null;
	        if(StringUtil.isNotEmpty(dayList)){
	        	dayJsons=JSONArray.fromObject(dayList);
	        }
	        JSONArray weekJsons=null;
	        if(StringUtil.isNotEmpty(weekList)){
	        	weekJsons=JSONArray.fromObject(weekList);
	        }
	        JSONArray seasonJsons=null;
	        if(StringUtil.isNotEmpty(seasonList)){
	        	seasonJsons=JSONArray.fromObject(seasonList);
	        }
	        JSONArray specialDayJsons=null;
	        if(StringUtil.isNotEmpty(specialDayList)){
	        	specialDayJsons=JSONArray.fromObject(specialDayList);
	        }
	        JSONArray limiterJsons=null;
	        if(StringUtil.isNotEmpty(limiterList)){
	        	limiterJsons=JSONArray.fromObject(limiterList);
	        }
	        SysUser su=TokenManager.getToken();   
	        String basePath = ResourceUtil.getUciBasePath(request);
	        MeterDefineConfigRequestMessageType mdcrmt=new MeterDefineConfigRequestMessageType();
	   	 	HeaderType ht=new HeaderType();
	        Date date=new Date();
	        ht.setVerb("create");
	        ht.setNoun("MeterConfig");
	        ht.setTimestamp(DateUtils.dateToXmlDate(date));
	        ht.setSource("ClouESP HES");
	        ht.setMessageID(DateUtils.formatDate("yyyyMMddHHmmss")+su.getId());
	        ht.setAsyncReplyFlag(true);
	        ht.setReplyAddress(basePath+"/interfaces/ReplyMeterReadings?wsdl");
	        ht.setAckRequired(true);
	        UserType u =new UserType();
	        u.setOrganization(su.getOrgId());
	        u.setUserID(su.getId());
	        ht.setUser(u);
	        mdcrmt.setHeader(ht);
	        MeterDefineConfigPayloadType mdfpt=new MeterDefineConfigPayloadType();
	        MeterDefineConfig mdc=new MeterDefineConfig();
	        Meter meter=new Meter();
	        meter.setMRID("123");
	        mdc.getMeters().add(meter);
	        if(dayJsons!=null&&dayJsons.size()>0){
	        	Arrays a=new Arrays();
	        	a.setType("day_profile");
	        	for(int i=0;i<dayJsons.size();i++){
	        		JSONObject o=dayJsons.getJSONObject(i);
	        		if(StringUtil.isEmpty(o.getString("parent"))){
		        		Structures s=new Structures();
		        		s.setType("day");
		        		Values v=new Values();
		        		v.setType("day_id");
		        		v.setValue(o.getString("id"));
		        		s.getValues().add(v);
		        		Arrays da=new Arrays();
		        		da.setType("day_schedule");
		        		for(int k=0;k<dayJsons.size();k++){
		        			JSONObject oi=dayJsons.getJSONObject(k);
		        			if(StringUtil.isNotEmpty(oi.getString("parent"))&&oi.getString("parent").equals(o.getString("id"))){
		        				Structures ds=new Structures();
		        				Values startTime=new Values();
		        				startTime.setType("start_time");
		        				startTime.setValue(oi.getString("startTime"));
		        				ds.getValues().add(startTime);
		        				Values scriptName=new Values();
		        				scriptName.setType("script_logical_name");
		        				scriptName.setValue("********.100.255");
		        				ds.getValues().add(scriptName);
		        				Values rate=new Values();
		        				rate.setType("script_selector");
		        				rate.setValue(oi.getString("rate"));
		        				ds.getValues().add(rate);
		        				da.getStructures().add(ds);
		        			}
		        		}
		        		s.getArrays().add(da);
		        		a.getStructures().add(s);
	        		}
	        		
	        	}
	        	mdc.getArrays().add(a);
	        	ReadingType rt=new ReadingType();
	        	rt.setRef(dayDataitemId);
	        	mdc.setReadingType(rt);
	        	mdfpt.setMeterDefineConfig(mdc);
	        	AssetMeterGroupValue pojo=new AssetMeterGroupValue();
	        	pojo.setDataitemId(dayDataitemId);
	        	pojo.setGroupId(groupId);
	        	mdcrmt.setPayload(mdfpt);
	        	pojo.setXmlValue(XMLUtil.convertToXml(mdcrmt));
	        	assetMeterGroupValueService.update(pojo);
	        }
	        if(weekJsons!=null&&weekJsons.size()>0){
	        	Arrays a=new Arrays();
	        	a.setType("week_profile");
	        	for(int i=0;i<weekJsons.size();i++){
	        		JSONObject o=weekJsons.getJSONObject(i);
	        		if(StringUtil.isEmpty(o.getString("parent"))){
	        			Structures s=new Structures();
	        			s.setType("week");
	        			Values v=new Values();
	        			v.setType("week_profile_name");
	        			v.setValue(o.getString("id"));
	        			s.getValues().add(v);
	        			for(int k=0;k<weekJsons.size();k++){
	        				JSONObject ho=weekJsons.getJSONObject(k);
	        				if(StringUtil.isNotEmpty(ho.getString("parent"))&&o.getString("id").equals(ho.getString("parent"))){
	        					Values wv=new Values();
	        					wv.setType(ho.getString("weekName").toLowerCase());
	        					wv.setValue(ho.getString("dayProfile"));
	        	        		s.getValues().add(wv);
	        				}
	        				
	        			}
	        			a.getStructures().add(s);
	        		}
	        	}
	        	mdc.getArrays().clear();
	        	mdc.getArrays().add(a);
	        	ReadingType rt=new ReadingType();
	        	rt.setRef(weekDataitemId);
	        	mdc.setReadingType(rt);
	        	mdfpt.setMeterDefineConfig(mdc);
	        	AssetMeterGroupValue pojo=new AssetMeterGroupValue();
	        	pojo.setDataitemId(weekDataitemId);
	        	pojo.setGroupId(groupId);
	        	mdcrmt.setPayload(mdfpt);
	        	pojo.setXmlValue(XMLUtil.convertToXml(mdcrmt));
	        	assetMeterGroupValueService.update(pojo);
	        }
	        if(seasonJsons!=null&&seasonJsons.size()>0){
	        	Arrays a=new Arrays();
	        	a.setType("season_profile");
	        	for(int i=0;i<seasonJsons.size();i++){
	        		JSONObject o=seasonJsons.getJSONObject(i);
	        		Structures s=new Structures();
	        		s.setType("season");
	        		Values seasonName=new Values();
	        		seasonName.setType("season_profile_name");
	        		seasonName.setValue(o.getString("id"));
	        		s.getValues().add(seasonName);
	        		Values startTime=new Values();
	        		startTime.setType("season_start");
                    startTime.setValue(DateTimeFormatterUtil.getSimpleDateFormatToYYYYMMDDHHMMSS(o.getString("startTime"),TIME_FLAG));
	        		s.getValues().add(startTime);
	        		Values weekProfile=new Values();
	        		weekProfile.setType("week_name");
	        		weekProfile.setValue(o.getString("weekProfile"));
	        		s.getValues().add(weekProfile);
	        		a.getStructures().add(s);
	        	}
	        	mdc.getArrays().clear();
	        	mdc.getArrays().add(a);
	        	ReadingType rt=new ReadingType();
	        	rt.setRef(seasonDataitemId);
	        	mdc.setReadingType(rt);
	        	mdfpt.setMeterDefineConfig(mdc);
	        	AssetMeterGroupValue pojo=new AssetMeterGroupValue();
	        	pojo.setDataitemId(seasonDataitemId);
	        	pojo.setGroupId(groupId);
	        	mdcrmt.setPayload(mdfpt);
	        	pojo.setXmlValue(XMLUtil.convertToXml(mdcrmt));
	        	assetMeterGroupValueService.update(pojo);
	        }
	        if(specialDayJsons!=null&&specialDayJsons.size()>0){
	        	Arrays a=new Arrays();
	        	a.setType("spec_day");
	        	for(int i=0;i<specialDayJsons.size();i++){
	        		JSONObject o=specialDayJsons.getJSONObject(i);
	        		Structures s=new Structures();
	        		s.setType("spec_day_entry");
	        		Values v=new Values();
	        		v.setType("index");
	        		v.setValue(o.getString("id"));
	        		s.getValues().add(v);
	        		Values startTime=new Values();
	        		startTime.setType("specialday_date");
	        		
	        		// 2018-9-22 baijun 日期和周的分隔符由空格改为逗号
	        		startTime.setValue(DateTimeFormatterUtil.getSimpleDateFormatToYYYYMMDD(o.getString("date"),TIME_FLAG,","));
//	        		startTime.setValue(DateTimeFormatterUtil.getSimpleDateFormatToYYYYMMDD(o.getString("date"),TIME_FLAG));
	        		
	        		s.getValues().add(startTime);
	        		Values dayProfile=new Values();
	        		dayProfile.setType("day_id");
	        		dayProfile.setValue(o.getString("dayProfile"));
	        		s.getValues().add(dayProfile);
	        		a.getStructures().add(s);
	        	}
	        	mdc.getArrays().clear();
	        	mdc.getArrays().add(a);
	        	ReadingType rt=new ReadingType();
	        	rt.setRef(specialDayDataitemId);
	        	mdc.setReadingType(rt);
	        	mdfpt.setMeterDefineConfig(mdc);
	        	AssetMeterGroupValue pojo=new AssetMeterGroupValue();
	        	pojo.setDataitemId(specialDayDataitemId);
	        	pojo.setGroupId(groupId);
	        	mdcrmt.setPayload(mdfpt);
	        	pojo.setXmlValue(XMLUtil.convertToXml(mdcrmt));
	        	assetMeterGroupValueService.update(pojo);
	        }
	        if(limiterJsons!=null&&limiterJsons.size()>0){
	        	AssetMeterGroupValue pojoc=new AssetMeterGroupValue();
	        	pojoc.setGroupId(groupId);
	        	if(assetMeterGroupValueService.getCount(pojoc)!=limiterJsons.size()){
	        		assetMeterGroupValueService.delete(pojoc);
	        	}
	        	for(int i=0;i<limiterJsons.size();i++){
	        		JSONObject o=limiterJsons.getJSONObject(i);
	        		ReadingType rt=new ReadingType();
	        		rt.setRef(o.getString("id"));
	        		mdc.setReadingType(rt);
	        		Arrays a=new Arrays();
		        	a.setType("calendar_name");
		        	Values v=new Values();
		        	v.setType("calendar_name");
		        	String value = o.getString("value").trim();
//		        	if(value.contains(":")) {
//		        		if(value.length()!=19) {
//		        			j.setSuccess(false);
//		        			j.setMsg(MutiLangUtil.doMutiLang("limiterGroupList.dateError"));
//		        			return j;
//		        		}
//		        		
//		        		try {
//		        			value=SDF.format(SDF_VIEW.parse(value));
//		        		}catch(Exception ex) {
//		        			j.setSuccess(false);
//		        			j.setMsg(MutiLangUtil.doMutiLang("limiterGroupList.dateError"));
//		        			return j;
//		        		}
//		        	}
		        	v.setValue(value);
		        	a.getValues().add(v);
		        	mdc.getArrays().clear();
		        	mdc.getArrays().add(a);
		        	mdfpt.setMeterDefineConfig(mdc);
		        	AssetMeterGroupValue pojo=new AssetMeterGroupValue();
		        	pojo.setDataitemId(o.getString("id"));
		        	pojo.setGroupId(groupId);
		        	mdcrmt.setPayload(mdfpt);
		        	pojo.setXmlValue(XMLUtil.convertToXml(mdcrmt));
		        	assetMeterGroupValueService.saveOrUpdate(pojo);
	        	}
	        }
	        AssetMeterGroup assetMeterGroup=assetMeterGroupService.getEntity(groupId);
	        dataUserLogService.insertDataUserLog(su.getId(),
            		"Meter Group Mgmt", "Edit Group", "Edit Group (Group Name="+ assetMeterGroup.getName() +")");
	        j.setMsg("Saved successfully");
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
    /**
     * 获取TOU详细数据
     * @param groupId
     * @param request
     * @return
     */
    @RequestMapping(value = "getTouGroupData")
    @ResponseBody
    public AjaxJson getTouGroupData(String groupId,String groupType,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        if(StringUtil.isEmpty(groupId)){
        	j.setErrorMsg("The Group No is empty!");
        	return j;
        }
        if(StringUtil.isEmpty(groupType)){
        	j.setErrorMsg("The Type is empty!");
        	return j;
        }
        try {
        	if(groupType.equals("2")){
        		AssetMeterGroupValue amgv=new AssetMeterGroupValue();
        		amgv.setGroupId(groupId);
        		j.put("dayDataitemId", "37.0.1.9"); //5
        		j.put("weekDataitemId", "********"); //4
        		j.put("seasonDataitemId", "37.0.1.7");//3
        		j.put("specialDayDataitemId", "37.2.1.1");
        		List<AssetMeterGroupValue> list=assetMeterGroupValueService.getList(amgv);
        		for(AssetMeterGroupValue a:list){
        			if(StringUtil.isNotEmpty(a.getXmlValue())){
        				switch (a.getDataitemId()) {
        				case "37.0.1.9":
        					Integer dayNum=0;
        					MeterDefineConfigRequestMessageType mdcrmt=(MeterDefineConfigRequestMessageType) XMLUtil.convertXmlStrToObject(MeterDefineConfigRequestMessageType.class, a.getXmlValue());
        					if(mdcrmt==null){
        						break;
        					}
        					List<Structures> structures= mdcrmt.getPayload().getMeterDefineConfig().getArrays().get(0).getStructures();
        					List<Map<String,Object>> dayList=new ArrayList<Map<String,Object>>();
        					Map<String,Object> dayInterval=new HashMap<String, Object>();
        					for(Structures d:structures){
        						Map<String,Object> m=new HashMap<String,Object>();
        						m.put("id", d.getValues().get(0).getValue());
        						m.put("dayName","Day "+ d.getValues().get(0).getValue().replace("d", ""));
        						m.put("startTime", "");
        						m.put("rate", "");
        						m.put("isLeaf", "false");
								m.put("expanded", "true");
								m.put("loaded", "true");
								m.put("parent", "");
        						Integer num=Integer.parseInt(d.getValues().get(0).getValue().replace("d", ""));
        						if(dayNum<num){
        							dayNum=num;
        						}
        						dayList.add(m);
        						List<Structures> intervals=d.getArrays().get(0).getStructures();
        						int i=0;
        						for(;i<intervals.size();i++){
        							List<Values> values= intervals.get(i).getValues();
        							Map<String,Object> im=new HashMap<String,Object>();
        							im.put("id", "d"+d.getValues().get(0).getValue()+"i"+(i+1));
        							im.put("dayName","Interval "+ (i+1));
        							im.put("parent", d.getValues().get(0).getValue());
        							for(Values v:values){
        								if(v.getType().equals("script_selector")){
        									im.put("rate", v.getValue());
        								}else if(v.getType().equals("start_time")){
        									im.put("startTime", v.getValue());
        								}
        							}
        							im.put("isLeaf", "true");
        							im.put("expanded", "false");
        							im.put("loaded", "false");
        							dayList.add(im);
        						}
        						dayInterval.put(d.getValues().get(0).getValue(), i);
        					}
        					j.put("dayInterval", dayInterval);
        					j.put("dayNum", dayNum);
        					j.put("dayList", dayList);
        					break;
        				case "********":
        					MeterDefineConfigRequestMessageType wmdcrmt=(MeterDefineConfigRequestMessageType) XMLUtil.convertXmlStrToObject(MeterDefineConfigRequestMessageType.class, a.getXmlValue());
        					if(wmdcrmt==null){
        						break;
        					}
        					List<Structures> wstructures= wmdcrmt.getPayload().getMeterDefineConfig().getArrays().get(0).getStructures();
        					List<Map<String,Object>> weekList=new ArrayList<Map<String,Object>>();
        					Integer weekNum=0;
        					for(Structures d:wstructures){
        						List<Values> values= d.getValues();
        						String wId="";
        						for(Values v:values){
        							if(v.getType().equals("week_profile_name")){
        								Map<String,Object> m=new HashMap<String,Object>();
        								wId=v.getValue();
        								Integer num=Integer.parseInt(v.getValue().replace("w", ""));
        								if(weekNum<num){
        									weekNum=num;
        								}
        								m.put("id", v.getValue());
        								m.put("weekName","Week "+ v.getValue().replace("w", ""));
        								m.put("dayProfile", "");
        								m.put("isLeaf", "false");
        								m.put("expanded", "true");
        								m.put("loaded", "true");
        								m.put("parent", "");
        								weekList.add(m);
        								break;
        							}
        						}
        						for(Values v:values){
        							Map<String,Object> m=new HashMap<String,Object>();
        							if(v.getType().equals("week_profile_name")){
        								continue;
        							}
        							switch (v.getType()) {
        							case "monday":
        								m.put("id", wId+"d1");
        								break;
        							case "tuesday":
        								m.put("id", wId+"d2");
        								break;
        							case "wednesday":
        								m.put("id", wId+"d3");
        								break;
        							case "thursday":
        								m.put("id", wId+"d4");
        								break;
        							case "friday":
        								m.put("id", wId+"d5");
        								break;
        							case "saturday":
        								m.put("id", wId+"d6");
        								break;
        							case "sunday":
        								m.put("id", wId+"d7");
        								break;
        							}
        							m.put("weekName",StringUtil.firstUpperCase(v.getType()));
        							m.put("dayProfile", v.getValue());
        							m.put("isLeaf", "true");
        							m.put("expanded", "false");
        							m.put("loaded", "false");
        							m.put("parent", wId);
        							weekList.add(m);
        						}
        					}
        					j.put("weekNum", weekNum);
        					j.put("weekList", weekList);
        					break;
        				case "37.0.1.7":
        					MeterDefineConfigRequestMessageType smdcrmt=(MeterDefineConfigRequestMessageType) XMLUtil.convertXmlStrToObject(MeterDefineConfigRequestMessageType.class, a.getXmlValue());
        					if(smdcrmt==null){
        						break;
        					}
        					List<Structures> sstructures= smdcrmt.getPayload().getMeterDefineConfig().getArrays().get(0).getStructures();
        					List<Map<String,Object>> seasonList=new ArrayList<Map<String,Object>>();
        					Integer seasonNum=0;
        					for(Structures d:sstructures){
        						List<Values> values= d.getValues();		
        						Map<String,Object> m=new HashMap<String,Object>();
        						for(Values v:values){
        							switch (v.getType()) {
        							case "season_profile_name":
        								m.put("id", v.getValue());
        								m.put("seasonName","Season "+ v.getValue().replace("s", ""));
        								Integer num=Integer.parseInt(v.getValue().replace("s", ""));
        								if(seasonNum<num){
        									seasonNum=num;
        								}
        								break;
        							case "season_start":
        							
        								m.put("startTime",DateTimeFormatterUtil.getSimpleDateFormat_HHmmssStr(v.getValue(),TIME_FLAG) );
        								break;
        							case "week_name":
        								m.put("weekProfile", v.getValue());
        								break;
        							}
        						}
        						seasonList.add(m);
        					}
        					j.put("seasonNum", seasonNum);
        					j.put("seasonList", seasonList);
        					break;
        				case "37.2.1.1":
        					MeterDefineConfigRequestMessageType sdmdcrmt=(MeterDefineConfigRequestMessageType) XMLUtil.convertXmlStrToObject(MeterDefineConfigRequestMessageType.class, a.getXmlValue());
        					if(sdmdcrmt==null){
        						break;
        					}
        					List<Structures> sdstructures= sdmdcrmt.getPayload().getMeterDefineConfig().getArrays().get(0).getStructures();
        					List<Map<String,Object>> specialDayList=new ArrayList<Map<String,Object>>();
        					Integer specialDayNum=0;
        					for(Structures d:sdstructures){
        						List<Values> values= d.getValues();		
        						Map<String,Object> m=new HashMap<String,Object>();
        						for(Values v:values){
        							switch (v.getType()) {
        							case "index":
        								m.put("id", v.getValue());
        								Integer num=Integer.parseInt(v.getValue());
        								if(specialDayNum<num){
        									specialDayNum=num;
        								}
        								m.put("specialDayName","Secial Day "+ v.getValue());
        								break;
        							case "specialday_date":
        								//FFFF-01-01,FF

       								m.put("date", DateTimeFormatterUtil.getSimpleDateFormatStr(v.getValue(),TIME_FLAG) );
        								break;
        							case "day_id":
        								m.put("dayProfile", v.getValue());
        								break;
        							}
        						}
        						specialDayList.add(m);
        					}
        					j.put("specialDayNum", specialDayNum);
        					j.put("specialDayList", specialDayList);
        					break;
        				default:
        					break;
        				}
        			}
        		}
        	}else if(groupType.equals("3")){
        		AssetMeterGroupValue amgv=new AssetMeterGroupValue();
        		amgv.setGroupId(groupId);
        		List<AssetMeterGroupValue> list=assetMeterGroupValueService.getList(amgv);
        		List<Map<String,Object>> limiterList=new ArrayList<Map<String,Object>>();
        		for(AssetMeterGroupValue a:list){
        			if(a.getDataitemId().indexOf("38.0.0.")>=0){
        				Map<String,Object> m=new HashMap<String, Object>();
        				m.put("id", a.getDataitemId());
        				m.put("protocolCode", a.getDataitemId());
        				m.put("name", a.getDataitemId());
        				if(StringUtil.isNotEmpty(a.getXmlValue())){
        					MeterDefineConfigRequestMessageType lmdcrmt=(MeterDefineConfigRequestMessageType) XMLUtil.convertXmlStrToObject(MeterDefineConfigRequestMessageType.class, a.getXmlValue());
        					Values value= lmdcrmt.getPayload().getMeterDefineConfig().getArrays().get(0).getValues().get(0);
//        					if(value!=null&&value.getValue()!=null&&value.getValue().contains("-")) {
//        						try {
//        							m.put("value", SDF_VIEW.format(SDF.parse(value.getValue())));
//        						}catch(Exception ex) {
//        							ex.printStackTrace();
//        						}
//        					}else {
        						m.put("value", value.getValue());
        					//}
        				}else{
        					m.put("value", "0");
        				}
        				limiterList.add(m);
        			}
        		}
        		j.put("limiterList", limiterList);
        	}
	    }catch (Exception e) {
	        e.printStackTrace();
	        j.setSuccess(false);
	         j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
	    }
        return j;
    }
    
    
    
    /**
     * 保存assetMeterGroup信息
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })AssetMeterGroup assetMeterGroup,
    			BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        SysUser su = TokenManager.getToken();
        if(bindingResult.getFieldErrorCount()>0){
        	j.setErrorMsg(bindingResult.getAllErrors().get(0).getDefaultMessage());
        	return j;
        }
        AssetMeterGroup t=new  AssetMeterGroup();
        try {
	        AssetMeterGroup amg=new AssetMeterGroup();
	        amg.setName(assetMeterGroup.getName());
	        /*
	         * 根据Group名字进行查询，判断是否重名
	         */
	    	List<AssetMeterGroup> list = assetMeterGroupService.getList(amg);
	    	if(list.size()>1 || (list.size()>0 && !list.get(0).getId().equals(assetMeterGroup.getId()))){
	    		j.setErrorMsg("The Group Name already exists");
	    		return j;
	    	}
	        if(StringUtil.isNotEmpty(assetMeterGroup.getId())){
	        	if("2".equals(assetMeterGroup.getType())){
	        		AssetMeterGroupValue value = new AssetMeterGroupValue();
	        		value.setGroupId(assetMeterGroup.getId());
	        		value.setDataitemId("*********");
	        		assetMeterGroupValueService.delete(value);									//删除旧数据
	        		value.setXmlValue(XMLUtil.convertToXml(this.getTOU_ActivateTimeXML_Object(su, assetMeterGroup, request)));
	        		assetMeterGroupValueService.save(value);									//保存新数据
	        	}
	        	/*
	        	 * 判断数据是否存在GroupId，如果存在，则是对数据进行修改
	        	 */
				t = assetMeterGroupService.getEntity(assetMeterGroup.getId());
				MyBeanUtils.copyBeanNotNull2Bean(assetMeterGroup, t);
				assetMeterGroupService.update(t);
				dataUserLogService.insertDataUserLog(su.getId(),
	            		"Meter Group Mgmt", "Edit Group", "Edit Group (Group Name="+ t.getName() +")");
				j.setMsg("Update success!");
			}else{
				/*
				 * 不存在GroupId，则是新增group 数据
				 */
	            String id = (String) assetMeterGroupService.save(assetMeterGroup);
	            if("2".equals(assetMeterGroup.getType())){
	            	//保存TOU的激活时间XML
		            AssetMeterGroupValue value = new AssetMeterGroupValue();
	        		value.setGroupId(id);				//groupID
	        		value.setDataitemId("*********");
	        		value.setXmlValue(XMLUtil.convertToXml(this.getTOU_ActivateTimeXML_Object(su, assetMeterGroup, request)));//转换数据为XML字符串
	        		assetMeterGroupValueService.save(value);					//插入数据，保存的是阶梯汇率激活时间
	            }
	            if(StringUtils.isNotEmpty(id)){
	            	/*
	            	 * 判断是否选择引用组，即：PeferenceId；
	            	 * 如果存在，则继承引用组下属数据项
	            	 */
	            	if(StringUtil.isNotEmpty(assetMeterGroup.getReferenceGroupId())){
	            		AssetMeterGroupValue reference=new AssetMeterGroupValue();
	            		reference.setGroupId(assetMeterGroup.getReferenceGroupId());
	            		List<AssetMeterGroupValue> refList = assetMeterGroupValueService.getList(reference);
	            		for(AssetMeterGroupValue r:refList){
	            			//先删后加
	            			if("*********".equals(r.getDataitemId())){
//	            				AssetMeterGroupValue value = new AssetMeterGroupValue();
//		    	        		value.setGroupId(id);
//		    	        		value.setDataitemId("*********");
//		    	        		assetMeterGroupValueService.delete(value);	
	            				continue;
	            			}
	            			
	            			AssetMeterGroupValue amgv=new AssetMeterGroupValue();
	            			amgv.setDataitemId(r.getDataitemId());
	            			amgv.setGroupId(id);
	            			amgv.setXmlValue(r.getXmlValue());
	            			assetMeterGroupValueService.save(amgv);
	            		}
	            	}else{
	            		/*
	            		 * 不存在引用组，则新建下属数据项
	            		 */
	            		if(assetMeterGroup.getType().equals("2")){
	            			AssetMeterGroupValue amgv=new AssetMeterGroupValue();
	            			amgv.setGroupId(id);
	            			amgv.setDataitemId("37.0.1.9");
	            			assetMeterGroupValueService.save(amgv);
	            			AssetMeterGroupValue amgv1=new AssetMeterGroupValue();
	            			amgv1.setGroupId(id);
	            			amgv1.setDataitemId("********");
	            			assetMeterGroupValueService.save(amgv1);
	            			AssetMeterGroupValue amgv2=new AssetMeterGroupValue();
	            			amgv2.setGroupId(id);
	            			amgv2.setDataitemId("37.0.1.7");
	            			assetMeterGroupValueService.save(amgv2);
	            			AssetMeterGroupValue amgv3=new AssetMeterGroupValue();
	            			amgv3.setGroupId(id);
	            			amgv3.setDataitemId("37.2.1.1");
	            			assetMeterGroupValueService.save(amgv3);
	            		}else if(assetMeterGroup.getType().equals("3")){
	            			DictDataitem dd=new DictDataitem();
	            			dd.put("groupId", "1004002");
	            			List<DictDataitem> ddtList=dictDataitemService.getList(dd);
	            			for(DictDataitem d:ddtList){
	            				AssetMeterGroupValue amgv=new AssetMeterGroupValue();
	            				amgv.setGroupId(id);
	            				amgv.setDataitemId(d.getId());
	            				assetMeterGroupValueService.save(amgv);
	            			}
	            		}
	            	}
	            }
	            dataUserLogService.insertDataUserLog(su.getId(),
	            		"Meter Group Mgmt", "Add Group", "Add Group (Group Name="+ assetMeterGroup.getName() +")");
	            j.setMsg("Create success!");
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
    
    /**
     * 获取TOU的激活时间对象
     * @Description 
     * @return MeterDefineConfigRequestMessageType
     * <AUTHOR> 
     * @Time 2018年5月10日 下午3:11:06
     */
    public MeterDefineConfigRequestMessageType getTOU_ActivateTimeXML_Object(SysUser su, 
    		AssetMeterGroup assetMeterGroup, HttpServletRequest request){
        String basePath = ResourceUtil.getUciBasePath(request);
        MeterDefineConfigRequestMessageType requestMessageType = new MeterDefineConfigRequestMessageType();
   	 	HeaderType header = new HeaderType();
        Date date = new Date();
        header.setVerb("create");
        header.setNoun("MeterConfig");
        header.setTimestamp(DateUtils.dateToXmlDate(date));
        header.setSource("ClouESP HES");
        header.setMessageID(DateUtils.formatDate("yyyyMMddHHmmss")+su.getId());
        header.setAsyncReplyFlag(true);
        header.setReplyAddress(basePath+"/interfaces/ReplyMeterReadings?wsdl");
        header.setAckRequired(true);
        UserType user = new UserType();
        user.setOrganization(su.getOrgId());
        user.setUserID(su.getId());
        header.setUser(user);
        requestMessageType.setHeader(header);
        MeterDefineConfigPayloadType payloadType = new MeterDefineConfigPayloadType();
        MeterDefineConfig config = new MeterDefineConfig();
        Meter meter = new Meter();
        meter.setMRID("123");	//假数据
        config.getMeters().add(meter);
        Arrays arrays = new Arrays();
        Values values = new Values();
        values.setValue(DateUtils.dateformat(
        		assetMeterGroup.getTouActivationTime(), ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"), "yyyy-MM-dd HH:mm:ss"));
        arrays.getValues().add(values);
        config.getArrays().add(arrays);
        ReadingType readingType = new ReadingType();
        readingType.setRef("*********");		//TOU激活时间的dataitem ID
        config.setReadingType(readingType);
        payloadType.setMeterDefineConfig(config);
        requestMessageType.setPayload(payloadType);
        return requestMessageType;
    }
   
    /**
     * 新增或者修改Measurment Group信息
     * @param id
     * @return
     */
    @RequestMapping(value = "saveMeasurement")
    @ResponseBody
    @Transactional
    public AjaxJson saveMeasurement(@Validated(value = { ValidGroup1.class })AssetMeterGroup assetMeterGroup,
    			BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson json = new AjaxJson();
        if(bindingResult.getFieldErrorCount()>0){
        	json.setErrorMsg(bindingResult.getAllErrors().get(0).getDefaultMessage());
        	return json;
        }
        AssetMeterGroup t = new  AssetMeterGroup();
        SysUser su = TokenManager.getToken();
        try {
	        /*
	         * 根据Group名字进行查询，判断是否重名
	         */
        	AssetMeterGroup amg = new AssetMeterGroup();
        	amg.setName(assetMeterGroup.getName());
			amg.setType("1");
	    	List<AssetMeterGroup> list = assetMeterGroupService.getList(amg);
	    	if(list.size() > 1 || (list.size()>0 && !list.get(0).getId().equals(assetMeterGroup.getId()))){
	    		json.setErrorMsg("The Group Name already exists");
	    		return json;
	    	}
	        if(StringUtil.isNotEmpty(assetMeterGroup.getId())){
	        	/*
	        	 * 判断数据是否存在GroupId，如果存在，则是对数据进行修改
	        	 */
				t = assetMeterGroupService.getEntity(assetMeterGroup.getId());
				MyBeanUtils.copyBeanNotNull2Bean(assetMeterGroup, t);
				assetMeterGroupService.update(t);
				//添加操作日志
	            dataUserLogService.insertDataUserLog(su.getId(),
	            		"Meter Group Mgmt", "Edit Group", "Edit Group (Group Name="+ t.getName() +")");
				json.setMsg("Update success!");
			}else{
				/*
				 * 不存在GroupId，则是新增group 数据
				 */
				if(!StringUtil.isNotEmpty(assetMeterGroup.getType())){
					assetMeterGroup.setType("1");
				}
	            String newGroupId = (String) assetMeterGroupService.save(assetMeterGroup);
	            //添加操作日志
	            dataUserLogService.insertDataUserLog(su.getId(),
	            		"Meter Group Mgmt", "Add Group", "Add Group (Group Name="+ assetMeterGroup.getName() +")");
	            if(StringUtils.isNotEmpty(newGroupId)){
	            	/*
	            	 * 判断是否选择引用组，即：PeferenceId；
	            	 * 如果存在，则继承引用组下属数据项
	            	 */
	            	if(StringUtil.isNotEmpty(assetMeterGroup.getReferenceGroupId())){
	            		AssetMeasurementProfile profile = new AssetMeasurementProfile();
	            		profile.setMgId(assetMeterGroup.getReferenceGroupId());
	            		List<AssetMeasurementProfile> profileList = assetMeasurementProfileService.getList(profile);
	            		if(profileList.size() > 0){
	            			for (int i = 0; i < profileList.size(); i++) {
	            				AssetMeasurementProfile updateProfile = profileList.get(i);
	            				updateProfile.setMgId(newGroupId);
	            				assetMeasurementProfileService.save(updateProfile);
	            			}
	            		}
	            		AssetMeasurementProfileDi profileDi = new AssetMeasurementProfileDi();
	            		profileDi.setMgId(assetMeterGroup.getReferenceGroupId());
	            		List<AssetMeasurementProfileDi> profileDiList = assetMeasurementProfileDiService.getList(profileDi);
	            		if(profileDiList.size() > 0){
	            			for (int i = 0; i < profileDiList.size(); i++) {
	            				AssetMeasurementProfileDi updateProfileDi = profileDiList.get(i);
	            				updateProfileDi.setMgId(newGroupId);
	            				assetMeasurementProfileDiService.save(updateProfileDi);
	            			}
	            		}
	            	}
	            }
	            json.setMsg("Create success!");
			}
        }catch (Exception e) {
            e.printStackTrace();
            json.setSuccess(false);
            json.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return json;
    }
	
    /**
     * 保存assetMeterGroup信息，save Measurement Group下属数据
     * 		涉及数据表：
     * 				ASSET_MEASUREMENT_PROFILE
     * 				ASSET_MEASUREMENT_PROFILE_DI
     * @param id
     * @return
     */
    @SuppressWarnings("unchecked")
	@Transactional
    @ResponseBody
    @RequestMapping(value = "saveMeasurementGroup")
    public AjaxJson saveMeasurementGroup(String mgId, 
    		String profileList, HttpServletRequest request) {
        AjaxJson json = new AjaxJson();
        SysUser su = TokenManager.getToken();
        //获取group的详细信息
        AssetMeterGroup meterGroup = assetMeterGroupService.getEntity(mgId);
    	try {
    		//获取界面的数据
    		JSONArray profileJsons = null;
	        if(StringUtil.isNotEmpty(profileList)){
	        	profileJsons = JSONArray.fromObject(profileList);
	        }else{
	        	json.setSuccess(false);
	    		json.setMsg("Please Add Profile List!");
	    		return json;
	        }
	        /*
	         * 删除Channel List
	         * */
        	AssetMeasurementProfileDi profileDi = new AssetMeasurementProfileDi();
        	profileDi.setMgId(mgId);
    		//删除
        	if(profileDi.getMgId() != null){
        		int deleteRes = assetMeasurementProfileDiService.deleteByEntity(profileDi);
        		System.out.println("删除Channel List, 参数为---->" 
        				+ JSONObject.fromObject(profileDi) + "， 记录长度为： " + deleteRes);
        	}
	        /*
	         * 删除Profile List
	         * 		首先查询出结果集
	         * */
	        AssetMeasurementProfile profile = new AssetMeasurementProfile();
	        profile.setMgId(mgId);
	        if(profile.getMgId() != null){
	        	int result = assetMeasurementProfileService.deleteByEntity(profile);
	        	System.out.println("删除Profile List, 参数为---->" 
	        			+ JSONObject.fromObject(profile) + "， 记录长度为： " + result);
	        }
    		/*
    		 * 查询Group下属的profile和channel数据，
    		 * 获取现在的数据，然后覆盖（先删除旧数据，然后保存新数据）
    		 * 涉及数据表：AssetMeasurementProfileDi  AssetMeasurementProfile
    		 */
	        for (int i = 0; i < profileJsons.size(); i++) {
	        	JSONObject jsonObj = profileJsons.getJSONObject(i);
	        	AssetMeasurementProfile measurementProfile = new AssetMeasurementProfile();
	        	measurementProfile.setMgId(mgId);
	        	measurementProfile.setProfileId(jsonObj.getString("profileId"));
	        	if("".equals(jsonObj.getString("profileCycleType"))){
	        		measurementProfile.setProfileCycleType("none");
	        		measurementProfile.setProfileCycle("0");
	        	}else{
	        		measurementProfile.setProfileCycleType(jsonObj.getString("profileCycleType"));
	        		measurementProfile.setProfileCycle(jsonObj.getString("profileCycle"));
	        	}
	        	measurementProfile.setProtocolCode(jsonObj.getString("protocolCode"));
	        	measurementProfile.setProfileType(jsonObj.getString("profileType"));
	        	String result = (String)assetMeasurementProfileService.save(measurementProfile);
	        	System.out.println("新建profile,结果为--" + result + ", 参数为---->" + JSONObject.fromObject(measurementProfile));
	        	if(result != null){
	        		//获取Redis中保存的channel list
	        		Map<String, Object> redisValues = JedisUtils.getObjectMap(mgId + jsonObj.getString("profileId"));
	        		if(redisValues!=null) {
						List<AssetMeasurementProfileDi> redisList = (List<AssetMeasurementProfileDi>) redisValues.get("listProfileDi");
						if(redisList!=null&&redisList.size()>0) {
							assetMeasurementProfileDiService.batchSave(redisList);
						}
	        		}
	        	}
			}
	       
	        //添加操作日志
            dataUserLogService.insertDataUserLog(su.getId(), "Meter Group Mgmt", "Edit Group", "Edit Group (Group Name="+ meterGroup.getName() +")");
	        json.setMsg(MutiLangUtil.doMutiLang("system.saveSucc"));
    	} catch (Exception e) {
    		e.printStackTrace();
    		json.setSuccess(false);
    		json.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
    	}
        return json;
    }
    
    /**
     * 删除assetMeterGroup信息（包括meter group，profile list，channel list）
     * 		涉及数据表：
     * 				ASSET_METER_GROUP
     * 				ASSET_MEASUREMENT_PROFILE
     * 				ASSET_MEASUREMENT_PROFILE_DI
     * @param id
     * @return
     */
	@Transactional
    @ResponseBody
    @RequestMapping(value = "deleteMeasurementGroup")
    public AjaxJson deleteMeasurementGroup(String mgId, HttpServletRequest request) {
        AjaxJson json = new AjaxJson();
        //获取group的详细信息
        AssetMeterGroup meterGroup = assetMeterGroupService.getEntity(mgId);
        SysUser su = TokenManager.getToken();
    	try {
    		//删除Measurement Group
    		int delGroupResult = assetMeterGroupService.delete(meterGroup);
    		//添加操作日志
            dataUserLogService.insertDataUserLog(su.getId(), 
            		"Meter Group Mgmt", "Delete Group", "Delete Group (Group Name="+ meterGroup.getName() +")");
    		System.out.println("deleteMeasurementGroup --- 删除Measurement Group, 参数为---->" 
    				+ JSONObject.fromObject(meterGroup) + "， 记录长度为： " + delGroupResult);
    		if(delGroupResult <= 0){
    			json.setSuccess(false);
	            json.setMsg("Delete fail!");
	            return json;
	        }
    		//删除profile list
    		AssetMeasurementProfile measurementProfile = new AssetMeasurementProfile();
    		measurementProfile.setMgId(mgId);
	        if(measurementProfile.getMgId() != null){
	        	int result = assetMeasurementProfileService.deleteByEntity(measurementProfile);
	        	System.out.println("deleteMeasurementGroup --- 删除Profile List, 参数为---->" 
	        			+ JSONObject.fromObject(measurementProfile) + "， 记录长度为： " + result);
	        }
	        //删除channel list
    		AssetMeasurementProfileDi measurementProfileDi = new AssetMeasurementProfileDi();
    		measurementProfileDi.setMgId(mgId);
        	if(measurementProfileDi.getMgId() != null){
        		int deleteRes = assetMeasurementProfileDiService.deleteByEntity(measurementProfileDi);
        		System.out.println("deleteMeasurementGroup --- 删除Channel List, 参数为---->" 
        				+ JSONObject.fromObject(measurementProfileDi) + "， 记录长度为： " + deleteRes);
        	}
        	json.setMsg("Delete success!");
    	} catch (Exception e) {
    		e.printStackTrace();
    		json.setSuccess(false);
    		json.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
    	}
        return json;
    }
	
	/*******************************************************step tariff group**********************************************************************/
	
	/**
	 * 跳转到add step tariff group页面
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "addStepTariffGroup")
    public ModelAndView addStepTariffGroup(AssetMeterGroup assetMeterGroup, HttpServletRequest request, Model model) {
		//获取所有的协议类型
		List<DictProtocol> list = dictProtocolService.getAllList();
		String protocolReplace = RoletoJson.listToReplaceStr(list, "id", "name");
		model.addAttribute("protocolReplace", protocolReplace);
		if(StringUtil.isNotEmpty(assetMeterGroup.getType())){
			try {
                List<AssetMeterGroup> assetMeterGroupList = assetMeterGroupService.getList(assetMeterGroup);
                if(assetMeterGroupList.size() > 0){
                	String groupReplace = RoletoJson.listToReplaceStr(assetMeterGroupList, "id", "name");
            		model.addAttribute("groupReplace", groupReplace);
                }
            }
            catch (Exception e) {
                e.printStackTrace();
            }
		}
		Calendar cal = Calendar.getInstance();
		//获取年份，直接拼接字符串得到明年一月一号的时间
		int year = cal.get(Calendar.YEAR);
		model.addAttribute("assetMeterGroup", assetMeterGroup);
		
		String time = String.valueOf(year + 1) + "-01-01 00:00:00";
		try {
			model.addAttribute("defaultsDate", 
					DateUtils.date2Str(DateUtils.parseDate(time, "yyyy-MM-dd HH:mm:ss"),
							new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"))));
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		
	//	model.addAttribute("defaultsDate", "01/01/" + String.valueOf(year + 1) + " 00:00:00");
        return new ModelAndView("/asset/addStepTariffGroup");
    }
	
	/**
	 * 跳转到add step tariff data页面
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "addStepTariffData")
    public ModelAndView addStepTariffData(HttpServletRequest request, Model model) {
        return new ModelAndView("/asset/addStepTariffData");
    }
	
	/**
	 * 阶梯汇率 Step tariff 查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagridStepTariff")
    @ResponseBody
    public JqGridResponseTo datagridStepTariff(JqGridSearchTo jqGridSearchTo, HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j = assetMeterGroupService.datagridStepTariff(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
	
	/**
     * 获取阶梯汇率的所有数据
     * @param id
     * @return
     */
    @RequestMapping(value = "getStepTariffData")
    @ResponseBody
    public AjaxJson getStepTariffData(AssetMeterGroupValue value, HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        List<StepTariff> list = new ArrayList<>();
        List<FriendlyPeriod> periodList = new ArrayList<>();
        List<String> specialList = new ArrayList<>();
        List<String> touList = new ArrayList<>();
        
        try {
        	Map<String,AssetMeterGroupValue>  groupMap = Maps.newHashMap();  
        	List<AssetMeterGroupValue> valueList = assetMeterGroupValueService.getList(value);
        	if(valueList!=null) {
        		for(AssetMeterGroupValue group:valueList) {
        			groupMap.put(group.getDataitemId(), group);
        		}
        	}
        	
        	//阶梯汇率
        	AssetMeterGroupValue stepTariffEntity = groupMap.get("40.0.3.1");
        	//电价 
        	AssetMeterGroupValue priceEntity = groupMap.get("40.0.2.5");
        	
        	//友好时段
        	AssetMeterGroupValue periodEntity = groupMap.get("40.0.0.18"); 
        	//友好周末
        	AssetMeterGroupValue weekEntity = groupMap.get("40.0.0.19");
        	//友好假期
        	AssetMeterGroupValue specialEntity = groupMap.get("40.0.0.20");
        	
            if(StringUtil.isNotEmpty(value.getGroupId())){
            	/**
        		 * XML字符串反转成对象
        		 * 请求参数类： 包含Header Request Payload
        		 * //对象转化为xml存储
        		 *	XMLUtil.convertToXml(mdcrmt);
        		 */
            	if(StringUtil.isNotEmpty(stepTariffEntity)){	//解析阶梯值
            		MeterDefineConfigRequestMessageType requestMessageSTE = (MeterDefineConfigRequestMessageType) 
            				XMLUtil.convertXmlStrToObject(MeterDefineConfigRequestMessageType.class, stepTariffEntity.getXmlValue());
            		MeterDefineConfigPayloadType payloadType = requestMessageSTE.getPayload();
                    MeterDefineConfig config = payloadType.getMeterDefineConfig();
                    List<Arrays> arrays = config.getArrays();
                    List<Values> values = arrays.get(0).getValues();
                    if(arrays.size() > 0){
                    	for (int i = 0; i < values.size(); i++) {
                    		StepTariff temp = new StepTariff();
                    		temp.setId(String.valueOf(i+1));
                    		temp.setStepName(values.get(i).getType());
                    		if(i == 0){
                    			temp.setStartQuantity("0");							//第一条step的初始值是0
                    		}else{
                    			BigDecimal bd1 = new BigDecimal(values.get(i-1).getValue());  
                                BigDecimal bd2 = new BigDecimal("100");
                    			//temp.setStartQuantity(values.get(i-1).getValue());	//每个step的初始值都是上一个step的结束值
                        		temp.setStartQuantity(bd1.divide(bd2, 3, RoundingMode.HALF_UP).toString());
                    		}
                    		BigDecimal bd1 = new BigDecimal(values.get(i).getValue());  
                            BigDecimal bd2 = new BigDecimal("100");
                    		//temp.setEndQuantity(values.get(i).getValue());
                            temp.setEndQuantity(bd1.divide(bd2, 3, RoundingMode.HALF_UP).toString());
                    		list.add(temp);
						}
                    }
            	}
            	
            	
        		if(StringUtil.isNotEmpty(priceEntity)){		//解析电价值
        			MeterDefineConfigRequestMessageType requestMessagePE = (MeterDefineConfigRequestMessageType) 
            				XMLUtil.convertXmlStrToObject(MeterDefineConfigRequestMessageType.class, priceEntity.getXmlValue());
        			MeterDefineConfigPayloadType payloadType = requestMessagePE.getPayload();
                    MeterDefineConfig config = payloadType.getMeterDefineConfig();
                    List<Arrays> arrays = config.getArrays();
                    List<Structures> structures = arrays.get(0).getStructures();
                    Structures structure = structures.get(2);
                    List<Structures> structures_1  = structure.getArrays().get(0).getStructures();
                    if(structures_1.size() > 0){
                    	if(list!=null&&list.size()>0) {
	                    	for (int i = 0; i < structures_1.size(); i++) {
	                    		Structures structure_1 = structures_1.get(i);
	                    		for (int k = 0; k < list.size(); k++) {
	                    			//判断step名是否相等
	                    			if(list.get(k).getStepName().equals(structure_1.getValues().get(1).getType())){
	                    				BigDecimal bd1 = new BigDecimal(structure_1.getValues().get(1).getValue());
	                                    BigDecimal bd2 = new BigDecimal("10000");
	                            		list.get(k).setPrice(bd1.divide(bd2, 4, RoundingMode.HALF_UP).toString());
	                    			}
								}
							}
                    	}else {
                    		//如果step没有价格，看看tou有没
                    		Structures  structures2 = structures_1.get(0);
                    		BigDecimal bd2 = new BigDecimal("10000");
                    		String touPrice1 = structures2.getValues().get(1).getValue();
                    		if(Integer.parseInt(touPrice1)!=0) {
                    			touList.add("0,"+new BigDecimal(touPrice1).divide(bd2, 4, RoundingMode.HALF_UP).toString());
                    		}
                    		
                    		structures2 = structures_1.get(10);
                    		touPrice1 = structures2.getValues().get(1).getValue();
                    		if(Integer.parseInt(touPrice1)!=0) {
                    			touList.add("1,"+new BigDecimal(touPrice1).divide(bd2, 4, RoundingMode.HALF_UP).toString());
                    		}
                    		
                    		structures2 = structures_1.get(20);
                    		touPrice1 = structures2.getValues().get(1).getValue();
                    		if(Integer.parseInt(touPrice1)!=0) {
                    			touList.add("2,"+new BigDecimal(touPrice1).divide(bd2, 4, RoundingMode.HALF_UP).toString());
                    		}
                    		
                    		structures2 = structures_1.get(30);
                    		touPrice1 = structures2.getValues().get(1).getValue();
                    		if(Integer.parseInt(touPrice1)!=0) {
                    			touList.add("3,"+new BigDecimal(touPrice1).divide(bd2, 4, RoundingMode.HALF_UP).toString());
                    		}
                    	}
                    }
        		}
        		
        		
        		
        		if(StringUtil.isNotEmpty(periodEntity)){	
            		MeterDefineConfigRequestMessageType requestMessageSTE = (MeterDefineConfigRequestMessageType) 
            				XMLUtil.convertXmlStrToObject(MeterDefineConfigRequestMessageType.class, periodEntity.getXmlValue());
            		MeterDefineConfigPayloadType payloadType = requestMessageSTE.getPayload();
                    MeterDefineConfig config = payloadType.getMeterDefineConfig();
                    List<Arrays> arrays = config.getArrays();
                    if(arrays.size() > 0){
                    	List<Structures> structuresList = arrays.get(0).getStructures();
                    	for(int i=0;i<structuresList.size();i++) {
                    		FriendlyPeriod period = new FriendlyPeriod();
                    		Values valuesStart=structuresList.get(i).getValues().get(0);
                    		Values valuesEnd=structuresList.get(i).getValues().get(1);
	                    	period.setStart(valuesStart.getValue());
	                    	period.setEnd(valuesEnd.getValue());
	                    	periodList.add(period);
                    	}
                    }
        		}
        		
        		if(StringUtil.isNotEmpty(specialEntity)){
        			MeterDefineConfigRequestMessageType requestMessageSTE = (MeterDefineConfigRequestMessageType) 
        					XMLUtil.convertXmlStrToObject(MeterDefineConfigRequestMessageType.class, specialEntity.getXmlValue());
        			MeterDefineConfigPayloadType payloadType = requestMessageSTE.getPayload();
        			if(payloadType!=null) {
	        			MeterDefineConfig config = payloadType.getMeterDefineConfig();
	        			List<Arrays> arrays = config.getArrays();
	        			if(arrays.size() > 0){
	        				List<Structures> structuresList = arrays.get(0).getStructures();
	        				for(int i=0;i<structuresList.size();i++) {
	        					String special=structuresList.get(i).getValues().get(1).getValue();
	        					specialList.add(DateTimeFormatterUtil.getSimpleDateFormatStr(special,TIME_FLAG));
	        				}
	        			}
        			}
        		}
        		
        		String weekStr="0000000";
        		if(StringUtil.isNotEmpty(weekEntity)){	
        			MeterDefineConfigRequestMessageType requestMessageSTE = (MeterDefineConfigRequestMessageType) 
        					XMLUtil.convertXmlStrToObject(MeterDefineConfigRequestMessageType.class, weekEntity.getXmlValue());
        			MeterDefineConfigPayloadType payloadType = requestMessageSTE.getPayload();
        			MeterDefineConfig config = payloadType.getMeterDefineConfig();
        			List<Arrays> arrays = config.getArrays();
        			if(arrays.size() > 0&&arrays.get(0).getValues().size()>0){
        				weekStr = arrays.get(0).getValues().get(0).getValue();
        			}
        		}
        		
        		
        		Map<String,Object>  map = Maps.newHashMap();
        		map.put("stepTariff", list);
        		map.put("periodList", periodList);
        		map.put("weekStr", weekStr);
        		map.put("specialList", specialList);
        		map.put("touList", touList);
        		j.setAttributes(map);
        		
            }else{
                j.setSuccess(false);
                j.setMsg(MutiLangUtil.doMutiLang("system.selectNoData"));
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
    
    /**
     * 新增或者修改Step Tariff Group信息
     * @param id
     * @return
     */
    @RequestMapping(value = "saveStepTariffGroup")
    @ResponseBody
    @Transactional
    public AjaxJson saveStepTariffGroup(@Validated(value = { ValidGroup1.class })AssetMeterGroup assetMeterGroup,
    			BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson json = new AjaxJson();
        if(bindingResult.getFieldErrorCount()>0){
        	json.setErrorMsg(bindingResult.getAllErrors().get(0).getDefaultMessage());
        	return json;
        }
        AssetMeterGroup t = new  AssetMeterGroup();
        SysUser su = TokenManager.getToken();
        try {
	        /*
	         * 根据Group名字进行查询，判断是否重名
	         */
        	AssetMeterGroup amg = new AssetMeterGroup();
        	amg.setName(assetMeterGroup.getName());
	    	List<AssetMeterGroup> list = assetMeterGroupService.getList(amg);
	    	if(list.size() > 1 || (list.size()>0 && !list.get(0).getId().equals(assetMeterGroup.getId()))){
	    		json.setErrorMsg(MutiLangUtil.doMutiLang("stepTariffList.nameExist"));
	    		return json;
	    	}
	        if(StringUtil.isNotEmpty(assetMeterGroup.getId())){
	        	List<String> strList = new ArrayList<>();
	        	strList.add("40.0.3.2");
	        	strList.add("40.0.2.6");
	        	strList.add("41.0.2.6");
	        	for (int i = 0; i < strList.size(); i++) {
	        		AssetMeterGroupValue value = new AssetMeterGroupValue();
	        		value.setGroupId(assetMeterGroup.getId());
	        		value.setDataitemId(strList.get(i));
	        		AssetMeterGroupValue valueTemp = assetMeterGroupValueService.get(value);	//获取对象
	        		if(valueTemp!=null) {//有可能41没有
	        			assetMeterGroupValueService.delete(value);									//删除旧数据
		        		MeterDefineConfigRequestMessageType requestMessage = (MeterDefineConfigRequestMessageType) 
		        				XMLUtil.convertXmlStrToObject(MeterDefineConfigRequestMessageType.class, valueTemp.getXmlValue());
		        		MeterDefineConfigPayloadType payloadType = requestMessage.getPayload();
		        		MeterDefineConfig config = payloadType.getMeterDefineConfig();
		        		List<Arrays> arraysList = config.getArrays();
		        		Arrays arrays = arraysList.get(0);
		        		List<Values> valuesList = arrays.getValues();
		        		Values values = valuesList.get(0);
		        		values.setValue(DateUtils.dateformat(
		        				assetMeterGroup.getStepTariffActivationTime(), ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"), "yyyy-MM-dd HH:mm:ss"));//修改日期时间
		        		value.setXmlValue(XMLUtil.convertToXml(requestMessage));
		        		assetMeterGroupValueService.save(value);									//保存新数据
	        		}
				}
	        	/*
	        	 * 判断数据是否存在GroupId，如果存在，则是对数据进行修改
	        	 */
				t = assetMeterGroupService.getEntity(assetMeterGroup.getId());
				MyBeanUtils.copyBeanNotNull2Bean(assetMeterGroup, t);
				assetMeterGroupService.update(t);
				//PPM 特殊处理
				if(ResourceUtil.getSessionattachmenttitle("hesweb.project.name").indexOf("hesppm") > -1){

					if(ppmAssetTariffGroupService.deleteById(assetMeterGroup.getId()) > -1){
						PpmAssetTariffGroup ppmAssetTariffGroup = new PpmAssetTariffGroup();
						
						ppmAssetTariffGroup.setActivateTv(DateUtils.str2Date(assetMeterGroup.getStepTariffActivationTime(), DateTimeFormatterUtil.getSimpleDateFormat_HHmmss(TIME_FLAG)));
						ppmAssetTariffGroup.setId(t.getId());
						ppmAssetTariffGroup.setName(t.getName());
						ppmAssetTariffGroup.setDescr(t.getIntroduction());
						ppmAssetTariffGroup.setType(2);
						ppmAssetTariffGroupService.save(ppmAssetTariffGroup);
					}
					
				}
				//添加操作日志
	            dataUserLogService.insertDataUserLog(su.getId(),
	            		"Meter Group Mgmt", "Edit Group", "Edit Group (Group Name="+ t.getName() +")");
				json.setMsg(MutiLangUtil.doMutiLang("system.updateSucc"));
			}else{
				/*
				 * 不存在GroupId，则是新增group 数据
				 */
				assetMeterGroup.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
				assetMeterGroup.setType("4");
	            String newGroupId = (String) assetMeterGroupService.save(assetMeterGroup);
	            //添加操作日志
	            dataUserLogService.insertDataUserLog(su.getId(),
	            		"Meter Group Mgmt", "Add Group", "Add Group (Group Name="+ assetMeterGroup.getName() +")");
	            if(StringUtils.isNotEmpty(newGroupId)){
	            	/*
	            	 * 判断是否选择引用组，即：PeferenceId；
	            	 * 如果存在，则继承引用组下属数据项
	            	 */
	            	if(StringUtil.isNotEmpty(assetMeterGroup.getReferenceGroupId())){
	            		AssetMeterGroupValue value = new AssetMeterGroupValue();
	            		value.setGroupId(assetMeterGroup.getReferenceGroupId());	//设置引用组ID
	            		List<AssetMeterGroupValue> valueList = assetMeterGroupValueService.getList(value);	//获取引用组的所有数据
	    	        	for (int i = 0; i < valueList.size(); i++) {
		            		AssetMeterGroupValue valueTemp = valueList.get(i);
		            		if(valueTemp != null){
		            			if(StringUtil.isNotEmpty(valueTemp) && ("40.0.2.6".equals(valueTemp.getDataitemId())||"41.0.2.6".equals(valueTemp.getDataitemId())||"40.0.3.2".equals(valueTemp.getDataitemId()))){
		            				MeterDefineConfigRequestMessageType requestMessageTime = (MeterDefineConfigRequestMessageType) 
		            						XMLUtil.convertXmlStrToObject(MeterDefineConfigRequestMessageType.class, valueTemp.getXmlValue());
		            				MeterDefineConfigPayloadType payloadType = requestMessageTime.getPayload();
		            				MeterDefineConfig config = payloadType.getMeterDefineConfig();
		            				 List<Arrays> arraysList = config.getArrays();
		                	        Arrays arrays = arraysList.get(0);
		                	        List<Values> valuesList = arrays.getValues();
		                	        Values values = valuesList.get(0);
		                	        //日期用页面选择的
		                	        values.setValue(DateUtils.dateformat(assetMeterGroup.getStepTariffActivationTime(), ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"), "yyyy-MM-dd HH:mm:ss"));
		                	        valueTemp.setXmlValue(XMLUtil.convertToXml(requestMessageTime));//转换数据为XML字符串);
		            			}
	            			}
		            		valueTemp.setGroupId(newGroupId);				//新建组ID
		            		assetMeterGroupValueService.save(valueTemp);	//插入数据，保存的是阶梯汇率激活时间
	    	        	}
	            	}else{
	            		//无引用组，新增数据
	            		List<String> strList = new ArrayList<>();
	    	        	strList.add("40.0.3.2");
	    	        	strList.add("40.0.2.6");
	    	        	strList.add("41.0.2.6");
	    	        	for (int i = 0; i < strList.size(); i++) {
		        	        String basePath = ResourceUtil.getUciBasePath(request);
		        	        MeterDefineConfigRequestMessageType requestMessageType = new MeterDefineConfigRequestMessageType();
		        	   	 	HeaderType header = new HeaderType();
		        	        Date date = new Date();
		        	        header.setVerb("create");
		        	        header.setNoun("MeterConfig");
		        	        header.setTimestamp(DateUtils.dateToXmlDate(date));
		        	        header.setSource("ClouESP HES");
		        	        header.setMessageID(DateUtils.formatDate("yyyyMMddHHmmss")+su.getId());
		        	        header.setAsyncReplyFlag(true);
		        	        header.setReplyAddress(basePath+"/interfaces/ReplyMeterReadings?wsdl");
		        	        header.setAckRequired(true);
		        	        UserType user = new UserType();
		        	        user.setOrganization(su.getOrgId());
		        	        user.setUserID(su.getId());
		        	        header.setUser(user);
		        	        requestMessageType.setHeader(header);
		        	        MeterDefineConfigPayloadType payloadType = new MeterDefineConfigPayloadType();
		        	        MeterDefineConfig config = new MeterDefineConfig();
		        	        Meter meter = new Meter();
		        	        meter.setMRID("123");	//假数据
		        	        config.getMeters().add(meter);
		        	        Arrays arrays = new Arrays();
		        	        Values values = new Values();
		        	        values.setValue(DateUtils.dateformat(
		        	        		assetMeterGroup.getStepTariffActivationTime(), ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"), "yyyy-MM-dd HH:mm:ss"));
		        	        arrays.getValues().add(values);
		        	        config.getArrays().add(arrays);
		        	        ReadingType readingType = new ReadingType();
		        	        readingType.setRef(strList.get(i));
		        	        config.setReadingType(readingType);
		        	        payloadType.setMeterDefineConfig(config);
		        	        requestMessageType.setPayload(payloadType);
		        	        
		        	        AssetMeterGroupValue value = new AssetMeterGroupValue();
		            		value.setGroupId(newGroupId);	//组ID
		            		value.setDataitemId(strList.get(i));
		            		value.setXmlValue(XMLUtil.convertToXml(requestMessageType));//转换数据为XML字符串
		            		assetMeterGroupValueService.save(value);					//插入数据，保存的是阶梯汇率激活时间
	    	        	}
	    	        
	    	        	
	            	}
	            	
	            	
    	    		//PPM 特殊处理
    				if(ResourceUtil.getSessionattachmenttitle("hesweb.project.name").indexOf("hesppm") > -1){
    					PpmAssetTariffGroup ppmAssetTariffGroup = new PpmAssetTariffGroup();
    					ppmAssetTariffGroup.setId(newGroupId);
    					ppmAssetTariffGroup.setActivateTv(DateUtils.str2Date(assetMeterGroup.getStepTariffActivationTime(), DateTimeFormatterUtil.getSimpleDateFormat_HHmmss(TIME_FLAG)));
    					ppmAssetTariffGroup.setType(2);
    					ppmAssetTariffGroup.setDescr(assetMeterGroup.getIntroduction());
    					ppmAssetTariffGroup.setName(assetMeterGroup.getName());
    					ppmAssetTariffGroupService.save(ppmAssetTariffGroup); 
    				}
	            }
	            json.setMsg(MutiLangUtil.doMutiLang("system.addSucc"));
			}
        }catch (Exception e) {
            e.printStackTrace();
            json.setSuccess(false);
            json.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return json;
    }
    
    /**
     * 保存Step Tariff List信息
     * @param id
     * @return
     */
    @Transactional
    @ResponseBody
    @RequestMapping(value = "savePrepayList")
    public AjaxJson savePrepayList(String mgId, String stepList,
    		String fPeriodList,String fWeekList,String fSpecialList,String fTouList,HttpServletRequest request) {
        AjaxJson json = new AjaxJson();
        SysUser su = TokenManager.getToken();
        HeaderType headerType = getHeader(request);
        //获取group的详细信息
        AssetMeterGroup meterGroup = assetMeterGroupService.getEntity(mgId);
        List<StepTariff> stepTariffList = Lists.newArrayList();
        List<FriendlyPeriod> periodList = Lists.newArrayList();
        
        String priceChannelId ="40.0.2.5";
        String touPriceChannelId ="41.0.2.5";//冗余
        String stepChannelId="40.0.3.1";
        
        String periodChannelId="40.0.0.18";
	    String weekChannelId="40.0.0.19";
        String specialChannelId="40.0.0.20";
    	try {
    		//获取界面的数据
    		JSONArray profileJsons = JSONArray.fromObject(stepList);
    	
    		if(SecurityUtils.getSubject().isPermitted("StepTariffTabPageinPrepaidGroup")) {
	        	for (int i = 0; i < profileJsons.size(); i++) {
	        		JSONObject jsonObj = profileJsons.getJSONObject(i);
	        		StepTariff stepTariff = new StepTariff();
	        		stepTariff.setMgId(mgId);
	        		stepTariff.setStepName(jsonObj.getString("stepName"));
	        		stepTariff.setStartQuantity(jsonObj.getString("startQuantity"));
	        		stepTariff.setEndQuantity(jsonObj.getString("endQuantity"));
	        		stepTariff.setPrice(jsonObj.getString("price"));
	        		stepTariffList.add(stepTariff);
	        	}
	        	/**
	        	 * 校验阶梯数据是否正确
	        	 */
	        	String checkStep = this.checkStepList(stepTariffList);
	        	if(!checkStep.equals("0")){
	        		if(checkStep.equals("1")){
	        			json.setSuccess(false);
	        			json.setMsg(MutiLangUtil.doMutiLang("stepTariffList.reselectStepName"));
	        			return json;
		        	}else{
		        		json.setSuccess(false);
			    		json.setMsg("Start Quantity of "+ checkStep + MutiLangUtil.doMutiLang("stepTariffList.startQuantityError"));
			    		return json;
		        	}
	        	}
	        	/**
	        	 * 保存阶梯值数据XML
	        	 */
	        	 MeterDefineConfigRequestMessageType stepXML = this.getStepXML(meterGroup, stepTariffList,stepChannelId,headerType);
	        	 if(StringUtil.isNotEmpty(stepXML)){
	        		AssetMeterGroupValue value = new AssetMeterGroupValue();
					value.setGroupId(meterGroup.getId());	//组ID
					value.setDataitemId(stepChannelId);
					value.setXmlValue(XMLUtil.convertToXml(stepXML));	//转换数据为XML字符串
					assetMeterGroupValueService.delete(value);			//先删除旧数据
					assetMeterGroupValueService.save(value);			//插入阶梯数据
	        	 }
	        	 
	        	/**
	        	 * 保存电价值XML
	        	 */
	        	 if(stepTariffList!=null&&stepTariffList.size()>0) {
		        	 MeterDefineConfigRequestMessageType priceXML = this.getPriceXML(meterGroup, stepTariffList, priceChannelId,headerType);
		        	 if(StringUtil.isNotEmpty(priceXML)){
		        		AssetMeterGroupValue value = new AssetMeterGroupValue();
						value.setGroupId(meterGroup.getId());	//组ID
						value.setDataitemId(priceChannelId);
						value.setXmlValue(XMLUtil.convertToXml(priceXML));	//转换数据为XML字符串
						assetMeterGroupValueService.delete(value);			//先删除旧数据
						assetMeterGroupValueService.save(value);			//插入电价数据
		        	 }
	        	 }
	        	 
	        		//PPM 特殊处理
					if(ResourceUtil.getSessionattachmenttitle("hesweb.project.name").indexOf("hesppm") > -1){
						System.out.println(profileJsons.size());
						if(profileJsons.size() == 0){
							ppmAssetTariffStepService.deleteById(meterGroup.getId());
						}else{
							 List<PpmAssetTariffStep> ppmAssetTariffStepList = Lists.newArrayList();
							for (int i = 0; i < profileJsons.size(); i++) {
				        		JSONObject jsonObj = profileJsons.getJSONObject(i);
				        		PpmAssetTariffStep ppmAssetTariffStep = new PpmAssetTariffStep();
				        		ppmAssetTariffStep.setGroupId(meterGroup.getId());
				        		if(jsonObj.getString("stepName") !=null){
				        			Integer stepIndex = Integer.valueOf(jsonObj.getString("stepName").split(" ")[1]);
				        			ppmAssetTariffStep.setStepIndex(stepIndex);
				        		}
				        		
				        		BigDecimal startQuantity = new BigDecimal(jsonObj.getString("startQuantity"));
				        		BigDecimal endQuantity = new BigDecimal(jsonObj.getString("endQuantity"));
				        		BigDecimal price = new BigDecimal(jsonObj.getString("price"));
				        		
				        		ppmAssetTariffStep.setStartQuantity(startQuantity);
				        		ppmAssetTariffStep.setEndQuantity(endQuantity);
				        		ppmAssetTariffStep.setPrice(price);
				        		ppmAssetTariffStepList.add(ppmAssetTariffStep);
				        	}
							
							if(ppmAssetTariffStepService.deleteById(meterGroup.getId()) > -1){

								ppmAssetTariffStepService.batchSave(ppmAssetTariffStepList);
							}
						}
						
					}
	        	 
    		}
        	 
        	//添加权限校验，如果没有友好时段，不添加下面数据项
        	 if(SecurityUtils.getSubject().isPermitted("FriendlyTabPageinPrepaidGroup")) {
		        StringBuffer errMsg= new StringBuffer();
		        List<String>  dateList = Lists.newArrayList();
		        //处理友好时间段
		        if(StringUtil.isNotEmpty(fPeriodList)){
		        	profileJsons = JSONArray.fromObject(fPeriodList);
		        	for (int i = 0; i < profileJsons.size(); i++) {
		        		JSONObject jsonObj = profileJsons.getJSONObject(i);
		        		FriendlyPeriod per = new FriendlyPeriod();
		        		per.setMgId(mgId);
		        		per.setPeriodName(jsonObj.getString("periodName"));
		        		per.setStart(jsonObj.getString("start"));
		        		per.setEnd(jsonObj.getString("end"));
		        		per.setId(jsonObj.getString("id"));
		        		periodList.add(per);
		        		if(Integer.parseInt(per.getStart().replace(":", ""))>=Integer.parseInt(per.getEnd().replace(":",""))) {//开始时间段不能大于结束时间段
		        			errMsg.append(per.getPeriodName()+":"+MutiLangUtil.doMutiLang("stepTariffList.startLaterEnd")+"<br/>");
		        		}
		        		dateList.add(per.getStart()+"-"+per.getEnd());
		        	}
		        }
		        
		        if(StringUtil.isNotEmpty(fTouList)){
		        	profileJsons = JSONArray.fromObject(fTouList);
		        	List<String> touList = Lists.newArrayList();
		        	for (int i = 0; i < profileJsons.size(); i++) {
		        		JSONObject jsonObj = profileJsons.getJSONObject(i);
		        		String touName=jsonObj.getString("touName");
		        		if(touList.contains(touName)) {
		        			errMsg.append("TOU "+(Integer.parseInt(touName)+1)+MutiLangUtil.doMutiLang("stepTariffList.touExist")+"<br/>");
		        		}
		        		touList.add(touName);
		        	}
		        }
		        
		        if(checkOverlap(dateList)) {
		        	errMsg.append(MutiLangUtil.doMutiLang("stepTariffList.periodRepeat")+"<br/>");
		        }
		        
		        if(errMsg.length()>0) {
		        	json.setSuccess(false);
		    		json.setMsg(errMsg.toString());
		    		return json;
		        }
		        
		        /**
	        	 * 保存友好时段xml
	        	 */
	        	 MeterDefineConfigRequestMessageType periodXML = this.getFriendlyPeriodXML(periodList, periodChannelId, headerType);
	        	 if(StringUtil.isNotEmpty(periodXML)){
	        		AssetMeterGroupValue value = new AssetMeterGroupValue();
					value.setGroupId(meterGroup.getId());	//组ID
					value.setDataitemId(periodChannelId);
					value.setXmlValue(XMLUtil.convertToXml(periodXML));	//转换数据为XML字符串
					assetMeterGroupValueService.delete(value);			//先删除旧数据
					assetMeterGroupValueService.save(value);			//插入阶梯数据
	        	 }
	        	 
		        //处理友好周末
		        String weekStr="";
		        if(StringUtil.isNotEmpty(fWeekList)){
		        	profileJsons = JSONArray.fromObject(fWeekList);
		        	for (int i = 0; i < profileJsons.size(); i++) {
		        		JSONObject jsonObj = profileJsons.getJSONObject(i);
		        		String status=jsonObj.getString("status");
		        		weekStr+=status;
		        	}
		        }else {
		        	weekStr="0000000";
		        }
		        
	        	 MeterDefineConfigRequestMessageType weekXML = this.getFriendlyWeekXML(weekChannelId, weekStr,headerType);
	        	 if(StringUtil.isNotEmpty(weekXML)){
	        		AssetMeterGroupValue value = new AssetMeterGroupValue();
					value.setGroupId(meterGroup.getId());	//组ID
					value.setDataitemId(weekChannelId);
					value.setXmlValue(XMLUtil.convertToXml(weekXML));	//转换数据为XML字符串
					if(assetMeterGroupValueService.delete(value) > -1){

						assetMeterGroupValueService.save(value);			//插入阶梯数据
					}
	        	 }
		        
		        //处理友好特殊假日
		       	 MeterDefineConfigRequestMessageType specialXML = this.getFriendlySpecialXML(fSpecialList, specialChannelId, headerType);
		       	 if(StringUtil.isNotEmpty(specialXML)){
		       		 	AssetMeterGroupValue value = new AssetMeterGroupValue();
						value.setGroupId(meterGroup.getId());	//组ID
						value.setDataitemId(specialChannelId);
						value.setXmlValue(XMLUtil.convertToXml(specialXML));	//转换数据为XML字符串
						if(assetMeterGroupValueService.delete(value) > -1){

							assetMeterGroupValueService.save(value);	//先删除旧数据
						}			//插入阶梯数据
		       	 }
        	 }
	       	 
        	 if(SecurityUtils.getSubject().isPermitted("TOUTariffTabPageinPrepaidGroup")) {
		        //处理友好tou   step和tou只能有一个
		       	if(stepTariffList==null||stepTariffList.size()==0) {
			       	 MeterDefineConfigRequestMessageType touPriceXML = this.getTouPriceXML(fTouList, priceChannelId, headerType);
			       	 if(StringUtil.isNotEmpty(touPriceXML)){
			       		 AssetMeterGroupValue value = new AssetMeterGroupValue();
			       		 value.setGroupId(meterGroup.getId());	//组ID
			       		 value.setDataitemId(priceChannelId);
			       		 value.setXmlValue(XMLUtil.convertToXml(touPriceXML));	//转换数据为XML字符串
			       		 assetMeterGroupValueService.delete(value);				//先删除旧数据
			       		 assetMeterGroupValueService.save(value);				//插入阶梯数据
			       	 }
			       	 
			       	 //多存一份电价
			       	 MeterDefineConfigRequestMessageType touPriceXMLDubb = this.getTouPriceXML(fTouList, touPriceChannelId, headerType);
			       	 if(StringUtil.isNotEmpty(touPriceXMLDubb)){
			       		 AssetMeterGroupValue value = new AssetMeterGroupValue();
			       		 value.setGroupId(meterGroup.getId());	//组ID
			       		 value.setDataitemId(touPriceChannelId);
			       		 value.setXmlValue(XMLUtil.convertToXml(touPriceXMLDubb));	//转换数据为XML字符串
			       		 assetMeterGroupValueService.delete(value);				//先删除旧数据
			       		 assetMeterGroupValueService.save(value);				//插入阶梯数据
			       	 }
		       	}
        	 }
	      //添加操作日志
        	dataUserLogService.insertDataUserLog(su.getId(),"Meter Group Mgmt", "Edit Group", "Edit Group (Group Name="+ meterGroup.getName() +")");
	        json.setMsg("Save success!");
    	} catch (Exception e) {
    		e.printStackTrace();
    		json.setSuccess(false);
    		json.setMsg(MutiLangUtil.doMutiLang("system.saveFail"));
    	}
        return json;
    }
    
    /**
     * 校验阶梯汇率的数据是否正确
     * @param id
     * @return
     */
    public String checkStepList(List<StepTariff> stepTariffList){
    	String status = "0";
    	try {
    		for (int i = 0; i < stepTariffList.size(); i++) {
				if(!("Step "+String.valueOf(i+1)).equals(stepTariffList.get(i).getStepName())){
					status = "1";
					break;
				}
				if(i != 0){
					if(!(stepTariffList.get(i).getStartQuantity()).equals(stepTariffList.get(i-1).getEndQuantity())){
						status = stepTariffList.get(i).getStepName();
						break;
					}
				}
			}
    	} catch (Exception e) {
    		e.printStackTrace();
    	}
        return status;
    }
    
    /**
	 * Step Tariff Group验证名称唯一
	 */
	@RequestMapping(value = "checkStepTariffGroupGroupName")
	@ResponseBody
	public ValidForm checkStepTariffGroupGroupName(AssetMeterGroup assetMeterGroup, HttpServletRequest request,
			HttpServletResponse response) {
		ValidForm v = new ValidForm();
		String param = oConvertUtils.getString(request.getParameter("param"));
		assetMeterGroup.setName(param);
		assetMeterGroup.setType("4");
		List<AssetMeterGroup> list = assetMeterGroupService.getList(assetMeterGroup);
		if (list.size() > 0) {
			v.setInfo(MutiLangUtil.doMutiLang("stepTariffList.nameExist"));
			v.setStatus("n");
		}
		return v;
	}
	/**
     * 删除Step Tariff Group信息（包括meter group，step list）
     * 涉及数据表： ASSET_METER_GROUP ASSET_MEASUREMENT_PROFILE_DI
     * @param id
     * @return
     */
	@Transactional
    @ResponseBody
    @RequestMapping(value = "deleteStepTariffGroup")
    public AjaxJson deleteStepTariffGroup(String mgId, HttpServletRequest request) {
        AjaxJson json = new AjaxJson();
        //获取group的详细信息
        AssetMeterGroup meterGroup = assetMeterGroupService.getEntity(mgId);
        SysUser su = TokenManager.getToken();
    	try {
    		//删除Step Tariff Group
    		int delGroupResult = assetMeterGroupService.delete(meterGroup);
    		
    		
    		//添加操作日志
            dataUserLogService.insertDataUserLog(su.getId(), 
            		"Meter Group Mgmt", "Delete Group", "Delete Group (Group Name="+ meterGroup.getName() +")");
    		System.out.println("deleteStepTariffGroup --- 删除Step Tariff Group,"
    				+ " 参数为---->" + JSONObject.fromObject(meterGroup) + "， 记录长度为： " + delGroupResult);
    		if(delGroupResult <= 0){
    			json.setSuccess(false);
	            json.setMsg(MutiLangUtil.doMutiLang("system.delFail"));
	            return json;
	        }else{
	        	//PPM 特殊处理
				if(ResourceUtil.getSessionattachmenttitle("hesweb.project.name").indexOf("hesppm") > -1){

		    		ppmAssetTariffGroupService.deleteById(meterGroup.getId());
				}
	        }
    		

    		
    		//删除step list
    		AssetMeterGroupValue value = new AssetMeterGroupValue();
    		value.setGroupId(mgId);
        	int result = assetMeterGroupValueService.delete(value);
        	
        	//PPM 特殊处理
			if(ResourceUtil.getSessionattachmenttitle("hesweb.project.name").indexOf("hesppm") > -1){

	    		ppmAssetTariffStepService.deleteById(meterGroup.getId());
	    		ppmAssetTariffStepDetailService.deleteById(meterGroup.getId());
			}
        	
        	System.out.println("deleteStepTariffGroup --- 删除Step List, "
        			+ "参数为---->" + JSONObject.fromObject(value) + "， 记录长度为： " + result);
        	json.setMsg(MutiLangUtil.doMutiLang("system.delSuccess"));
    	} catch (Exception e) {
    		e.printStackTrace();
    		json.setSuccess(false);
    		json.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
    	}
        return json;
    }
	/**
	 * 查看要删除的电表组，是否绑定了电表
	 * @return
	 */
	@Transactional
	@ResponseBody
	@RequestMapping(value = "meterGroupCanDelete")
	public AjaxJson meterGroupCanDelete(String mgId, HttpServletRequest request) {
		AjaxJson json = new AjaxJson();
		//获取group的详细信息
		try {
			long l = assetMeterGroupMapService.countMeterByGroupId(mgId);
			if(l==0) {
				json.setSuccess(true);
			}else {
				json.setSuccess(false);
				json.setMsg(MutiLangUtil.doMutiLang("meterGroupUpgrade.hasMeter"));
			}
		} catch (Exception e) {
			e.printStackTrace();
			json.setSuccess(false);
			json.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
		}
		return json;
	}
	
	/**
	 * 跳转到Step Tariff Group的新增step页面
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "toAddStepTariffData")
	public ModelAndView toAddStepTariffData(String groupId, String startQuantity, HttpServletRequest request, Model model) {
		model.addAttribute("startQuantity", startQuantity);
		return new ModelAndView("/asset/addStepTariffData");
	}
	
    /**
     * 获取费率阶梯值xml
     */
    public MeterDefineConfigRequestMessageType getStepXML(AssetMeterGroup meterGroup, List<StepTariff> stepTariffList,String channelId,HeaderType headerType){
        MeterDefineConfigRequestMessageType requestMessageType = new MeterDefineConfigRequestMessageType();
        try {
            requestMessageType.setHeader(headerType);
            MeterDefineConfigPayloadType payloadType = new MeterDefineConfigPayloadType();
            MeterDefineConfig config = new MeterDefineConfig();
            Meter meter = new Meter();
            meter.setMRID("123");	//假数据
            config.getMeters().add(meter);
            Arrays arrays = new Arrays();
            /*
             * 存储汇率阶梯step值到values数组中，只存储每条阶梯的endQuantity
             * 默认可以保存小数点后三位，所以step value*1000取整保存
             */
            if(stepTariffList.size() > 0){
            	for (int i = 0; i < stepTariffList.size(); i++) {
            		Values values = new Values();
            		BigDecimal bd1 = new BigDecimal(stepTariffList.get(i).getEndQuantity());  
                    BigDecimal bd2 = new BigDecimal("100");//电表里面10w这里乘上100变成kwh
            		values.setValue(String.valueOf(bd1.multiply(bd2).longValue()));
            		values.setType(stepTariffList.get(i).getStepName());
            		arrays.getValues().add(values);
				}
            }
            config.getArrays().add(arrays);
            ReadingType readingType = new ReadingType();
            readingType.setRef(channelId);
            config.setReadingType(readingType);
            payloadType.setMeterDefineConfig(config);
            requestMessageType.setPayload(payloadType);
		} catch (Exception e) {
			e.printStackTrace();
		}
        return requestMessageType;
    }
    
    /**
     ** 获取友好时段xml
     */
    public MeterDefineConfigRequestMessageType getFriendlyPeriodXML(List<FriendlyPeriod> peroidList,String channelId,HeaderType headerType){
    	MeterDefineConfigRequestMessageType requestMessageType = new MeterDefineConfigRequestMessageType();
    	try {
    		requestMessageType.setHeader(headerType);
    		MeterDefineConfigPayloadType payloadType = new MeterDefineConfigPayloadType();
    		MeterDefineConfig config = new MeterDefineConfig();
    		Meter meter = new Meter();
    		meter.setMRID("123");	//假数据
    		config.getMeters().add(meter);
    		Arrays arrays = new Arrays();
    		
    		if(peroidList.size() > 0){
    			for (int i = 0; i < peroidList.size(); i++) {
    				Structures structures = new Structures();
    				FriendlyPeriod period = peroidList.get(i);
    				Values values = new Values();
    				values.setValue(period.getStart());
    				values.setType("start_time");
    				
    				Values values1 = new Values();
    				values1.setValue(period.getEnd());
    				values1.setType("end_time");
    				
    				structures.getValues().add(values);
    				structures.getValues().add(values1);
    				structures.setType("period_entry");
    				arrays.getStructures().add(structures);
    			}
    			arrays.setType("period");
    		}
    		
    		config.getArrays().add(arrays);
    		ReadingType readingType = new ReadingType();
    		readingType.setRef(channelId);
    		config.setReadingType(readingType);
    		payloadType.setMeterDefineConfig(config);
    		requestMessageType.setPayload(payloadType);
    	} catch (Exception e) {
    		e.printStackTrace();
    	}
    	return requestMessageType;
    }
    
    /**
     ** 获取友好假日xml
     */
    public MeterDefineConfigRequestMessageType getFriendlySpecialXML(String fSpecialList,String channelId,HeaderType headerType){
    	MeterDefineConfigRequestMessageType requestMessageType = new MeterDefineConfigRequestMessageType();
    	try {
    		requestMessageType.setHeader(headerType);
    		MeterDefineConfigPayloadType payloadType = new MeterDefineConfigPayloadType();
    		MeterDefineConfig config = new MeterDefineConfig();
    		Meter meter = new Meter();
    		meter.setMRID("123");	//假数据
    		config.getMeters().add(meter);
    		Arrays arrays = new Arrays();
    		if(StringUtil.isNotEmpty(fSpecialList)){
	        	JSONArray profileJsons = JSONArray.fromObject(fSpecialList);
	        	for (int i = 0; i < profileJsons.size(); i++) {
	        		JSONObject jsonObj = profileJsons.getJSONObject(i);
	        		String fDate=jsonObj.getString("date");
	        		String fSpecialId=jsonObj.getString("id");
    				Structures structures = new Structures();
    				if(StringUtils.isNotEmpty(fDate)) {
	    				Values values = new Values();
	    				values.setValue(fSpecialId);
	    				values.setType("index");
	    				
	    				Values values1 = new Values();
	    				values1.setValue(DateTimeFormatterUtil.getSimpleDateFormatToYYYYMMDD(fDate,TIME_FLAG,","));
	    				values1.setType("specialday_date");
	    				
	    				Values values2 = new Values();
	    				values2.setValue("1");
	    				values2.setType("day_id");
	    				
	    				structures.getValues().add(values);
	    				structures.getValues().add(values1);
	    				structures.getValues().add(values2);
	    				structures.setType("spec_day_entry");
	    				arrays.getStructures().add(structures);
    				}
    			}
    		}
    		
    		arrays.setType("spec_day");
    		config.getArrays().add(arrays);
    		ReadingType readingType = new ReadingType();
    		readingType.setRef(channelId);
    		config.setReadingType(readingType);
    		payloadType.setMeterDefineConfig(config);
    		requestMessageType.setPayload(payloadType);
    	} catch (Exception e) {
    		e.printStackTrace();
    	}
    	return requestMessageType;
    }
    
    public HeaderType getHeader(HttpServletRequest request) {
	    	SysUser su = TokenManager.getToken();
	    	String basePath = ResourceUtil.getUciBasePath(request);
	    	HeaderType header = new HeaderType();
			Date date = new Date();
			header.setVerb("create");
			header.setNoun("MeterConfig");
			header.setTimestamp(DateUtils.dateToXmlDate(date));
			header.setSource("ClouESP HES");
			header.setMessageID(DateUtils.formatDate("yyyyMMddHHmmss")+su.getId());
			header.setAsyncReplyFlag(true);
			header.setReplyAddress(basePath+"/interfaces/ReplyMeterReadings?wsdl");
			header.setAckRequired(true);
			UserType user = new UserType();
			user.setOrganization(su.getOrgId());
			user.setUserID(su.getId());
			header.setUser(user);
		return header;
    }
    /**
     ** 获取友好week的xml
     */
    public MeterDefineConfigRequestMessageType getFriendlyWeekXML(String channelId,String weekStr,HeaderType headerType){
    	MeterDefineConfigRequestMessageType requestMessageType = new MeterDefineConfigRequestMessageType();
    	try {
    		requestMessageType.setHeader(headerType);
    		MeterDefineConfigPayloadType payloadType = new MeterDefineConfigPayloadType();
    		MeterDefineConfig config = new MeterDefineConfig();
    		Meter meter = new Meter();
    		meter.setMRID("123");	//假数据
    		config.getMeters().add(meter);
    		
    		Arrays arrays = new Arrays();
			Values values = new Values();
			values.setValue(weekStr);
			arrays.getValues().add(values);
    		config.getArrays().add(arrays);
    		
    		ReadingType readingType = new ReadingType();
    		readingType.setRef(channelId);
    		config.setReadingType(readingType);
    		payloadType.setMeterDefineConfig(config);
    		requestMessageType.setPayload(payloadType);
    	} catch (Exception e) {
    		e.printStackTrace();
    	}
    	return requestMessageType;
    }
    
    /**
  	* 获取tou电价值xml
     */
    public MeterDefineConfigRequestMessageType getTouPriceXML(String touPrice,String priceChannelId,HeaderType headerType){
        MeterDefineConfigRequestMessageType requestMessageType = new MeterDefineConfigRequestMessageType();
        try {
            requestMessageType.setHeader(headerType);
            MeterDefineConfigPayloadType payloadType = new MeterDefineConfigPayloadType();
            MeterDefineConfig config = new MeterDefineConfig();
            Meter meter = new Meter();
            meter.setMRID("123");	//假数据
            config.getMeters().add(meter);
            Arrays arrays = new Arrays();
            
            Structures structures1 = new Structures();	//structures1: 默认数据，界面无输出
            Values values1_1 = new Values();
            values1_1.setValue("2");
            structures1.getValues().add(values1_1);
            Values values1_2 = new Values();
            values1_2.setValue("252");
            structures1.getValues().add(values1_2);
            arrays.getStructures().add(structures1);
            
            Structures structures2 = new Structures();	//structures2: 默认数据，界面无输出
            Values values2_1 = new Values();
            values2_1.setValue("3");
            structures2.getValues().add(values2_1);
            Values values2_2 = new Values();
            values2_2.setValue("1.0.15.8.0.255");
            structures2.getValues().add(values2_2);
            Values values2_3 = new Values();
            values2_3.setValue("2");
            structures2.getValues().add(values2_3);
            arrays.getStructures().add(structures2);
            
            Structures structures3 = new Structures();	//structures2: 阶梯汇率电价值
            Arrays structures3_arrays = new Arrays();
            /*
		             * 存储汇率阶梯step值到values数组中，只存储每条阶梯的endQuantity
		             * 默认可以保存小数点后三位，所以step value*1000取整保存
             */
            JSONArray profileJsons = JSONArray.fromObject(touPrice);
        	Map<Integer, String>  map = Maps.newHashMap();
        	for (int i = 0; i < profileJsons.size(); i++) {
        		JSONObject jsonObj = profileJsons.getJSONObject(i);
        		String touName=jsonObj.getString("touName");
        		String price=jsonObj.getString("price");
        		if(StringUtils.isNotEmpty(price)) {
        			map.put(Integer.parseInt(touName), price);// 0,1,2,3
        		}
        		
        	}
        	
        	for (int i = 0; i < 40; i++) {				//应UCI要求，必须保存40条电价，不足的补 0
        		Structures structures = new Structures();
        		Values values1 = new Values();
        		values1.setValue(String.valueOf(i));	//电价编号0~39
        		structures.getValues().add(values1);	
        		
        		Values values2 = new Values();
        		BigDecimal bd2 = new BigDecimal("10000");//电价保存倍率
        		int n =i/10;
        		String price = map.get(n);
        		if(StringUtils.isNotEmpty(price)){
        			BigDecimal bd1 = new BigDecimal(price);  
                    values2.setValue(String.valueOf(bd1.multiply(bd2).longValue()));	//电价保存为整数（*10000）
        		}else{
        			values2.setValue("0");
        		}
        		structures.getValues().add(values2);
				structures3_arrays.getStructures().add(structures);
        	}	
            	
            structures3.getArrays().add(structures3_arrays);
            arrays.getStructures().add(structures3);
            
            config.getArrays().add(arrays);
            ReadingType readingType = new ReadingType();
            readingType.setRef(priceChannelId);
            config.setReadingType(readingType);
            payloadType.setMeterDefineConfig(config);
            requestMessageType.setPayload(payloadType);
		} catch (Exception e) {
			e.printStackTrace();
		}
        return requestMessageType;
    }
    
    
    /**
     * 获取step电价值xml
     */
    public MeterDefineConfigRequestMessageType getPriceXML(AssetMeterGroup meterGroup, List<StepTariff> stepTariffList,String priceChannelId,HeaderType headerType){
    	MeterDefineConfigRequestMessageType requestMessageType = new MeterDefineConfigRequestMessageType();
    	try {
    		requestMessageType.setHeader(headerType);
    		MeterDefineConfigPayloadType payloadType = new MeterDefineConfigPayloadType();
    		MeterDefineConfig config = new MeterDefineConfig();
    		Meter meter = new Meter();
    		meter.setMRID("123");	//假数据
    		config.getMeters().add(meter);
    		Arrays arrays = new Arrays();
    		
    		Structures structures1 = new Structures();	//structures1: 默认数据，界面无输出
    		Values values1_1 = new Values();
    		values1_1.setValue("2");
    		structures1.getValues().add(values1_1);
    		Values values1_2 = new Values();
    		values1_2.setValue("252");
    		structures1.getValues().add(values1_2);
    		arrays.getStructures().add(structures1);
    		
    		Structures structures2 = new Structures();	//structures2: 默认数据，界面无输出
    		Values values2_1 = new Values();
    		values2_1.setValue("3");
    		structures2.getValues().add(values2_1);
    		Values values2_2 = new Values();
    		values2_2.setValue("1.0.15.8.0.255");
    		structures2.getValues().add(values2_2);
    		Values values2_3 = new Values();
    		values2_3.setValue("2");
    		structures2.getValues().add(values2_3);
    		arrays.getStructures().add(structures2);
    		
    		Structures structures3 = new Structures();	//structures2: 阶梯汇率电价值
    		Arrays structures3_arrays = new Arrays();
    		/*
    		 * 存储汇率阶梯step值到values数组中，只存储每条阶梯的endQuantity
    		 * 默认可以保存小数点后三位，所以step value*1000取整保存
    		 */
    		if(stepTariffList.size() > 0){
    			//for (int i = 0; i < stepTariffList.size(); i++) {
    			for (int i = 0; i < 40; i++) {				//应UCI要求，必须保存40条电价，不足的补 0
    				Structures structures = new Structures();
    				Values values1 = new Values();
    				values1.setValue(String.valueOf(i));	//电价编号0~39
    				structures.getValues().add(values1);	
    				Values values2 = new Values();
    				BigDecimal bd2 = new BigDecimal("10000");//电价保存倍率
    				if(i < stepTariffList.size()){
    					values2.setType(stepTariffList.get(i).getStepName());			//阶梯名字
    					BigDecimal bd1 = new BigDecimal(stepTariffList.get(i).getPrice());  
    					values2.setValue(String.valueOf(bd1.multiply(bd2).longValue()));	//电价保存为整数（*10000）
    					//values2.setValue(stepTariffList.get(i).getPrice());		
    				}else{
    					values2.setType("0");
    					BigDecimal bd1 = new BigDecimal(stepTariffList.get(stepTariffList.size()-1).getPrice());
    					values2.setValue(String.valueOf(bd1.multiply(bd2).longValue()));	//虚拟电价置最后一个阶梯的电价置
    				}
    				structures.getValues().add(values2);
    				structures3_arrays.getStructures().add(structures);
    			}
    		}
    		structures3.getArrays().add(structures3_arrays);
    		arrays.getStructures().add(structures3);
    		
    		config.getArrays().add(arrays);
    		ReadingType readingType = new ReadingType();
    		readingType.setRef(priceChannelId);
    		config.setReadingType(readingType);
    		payloadType.setMeterDefineConfig(config);
    		requestMessageType.setPayload(payloadType);
    	} catch (Exception e) {
    		e.printStackTrace();
    	}
    	return requestMessageType;
    }
    
    /**
     * 修改单条的step数据，校验数据是否填入正常
     * @Description 
     * @return AjaxJson
     * <AUTHOR> 
     * @Time 2018年4月25日 下午2:17:16
     */
    @ResponseBody
    @RequestMapping(value = "editOneStepDataCheck")
    public AjaxJson editOneStepDataCheck(StepTariff stepTariff, HttpServletRequest request){
    	AjaxJson json = new AjaxJson();
    	boolean status = false;
    	try {
    		//判断数字是否符合规范
    		boolean status1 = this.judgeFormat(stepTariff.getStartQuantity(), 3);
    		boolean status2 = this.judgeFormat(stepTariff.getEndQuantity(), 3);
    		boolean status3 = this.judgeFormat(stepTariff.getPrice(), 4);
    		if(status1 && status2 && status3){
    			status = true;
    		}else{
    			json.setMsg(MutiLangUtil.doMutiLang("stepTariffList.pleaseCorrectFormat"));
    			json.setSuccess(false);
    			return json;
    		}
			json.setSuccess(status);
    	} catch (Exception e) {
    		e.printStackTrace();
    		json.setSuccess(false);
    		json.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
    	}
        return json;
    }
    
    /**
     * 判断数字格式是否符合规范
     */
    public boolean judgeFormat(String num, int length){
    	//判断数字是否符合规范
    	boolean status = false;
    	if(length == 0){	//当传入的长度为0 时，只判断整数
    		Pattern patternInt = Pattern.compile("^[-\\+]?[\\d]*$");  
    	    boolean status1 = patternInt.matcher(num).matches();
    	    return status1;
    	}
		if(num.indexOf(".") == -1){
			status = this.judgeIntOrFloat(num);	//判断是否整数或者浮点数
			if(!status){
				status = false;
			}
		}else{
			status = this.judgeIntOrFloat(num);
			if(status){
				String[] strArr = num.split("\\.");
				if(strArr.length > 2){
					status = false;
				}else{ 
					if(strArr[1].length() > length)
						status = false;
				}
			}else{
				status = false;
			}
		}
		return status;
    }
    
    /**
     * 判断是否整数/浮点数
     */
    public boolean judgeIntOrFloat(String num){
    	boolean status = false;
    	Pattern patternInt = Pattern.compile("^[-\\+]?[\\d]*$");  
	    boolean status1 = patternInt.matcher(num).matches();
	    if(status1){
	    	status = true;
	    }
	    Pattern patternFloat = Pattern.compile("^[-\\+]?[.\\d]*$");  
	    boolean status2 = patternFloat.matcher(num).matches();
	    if(status2){
	    	status = true;
	    }
    	return status;
    }
    
    /**
     * 获取带周的时间
     */
    public String convertWeekDate(String time){
    	String newDate = "";
    	if(time.indexOf("/") > 0){
    		String[] times = time.split(" ");
    		String[] dates = time.split("/");
    		newDate = dates[2] + "-"+ dates[0] + "-" + dates[1] + " " + times[1];
    	}else if(time.indexOf("-") > 0){
    		String[] times = time.split(" ");
    		String[] dates = time.split("-");
    		newDate = dates[1] + "/"+ dates[2] + "/" + dates[0] + " " + times[1];
    	}
    	return newDate;
    }
    
    
    
    public static boolean checkOverlap(List<String> list) throws ParseException{ 
    	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    	SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    	String day = sdf.format(new Date());
        Collections.sort(list);//排序ASC 
           
        boolean flag = false;//是否重叠标识 
        for(int i=0; i<list.size(); i++){ 
            if(i>0){ 
                //跳过第一个时间段不做判断 
                String[] itime = list.get(i).split("-"); 
                for(int j=0; j<list.size(); j++){ 
                    //如果当前遍历的i开始时间小于j中某个时间段的结束时间那么则有重叠，反之没有重叠 
                    //这里比较时需要排除i本身以及i之后的时间段，因为已经排序了所以只比较自己之前(不包括自己)的时间段 
                    if(j==i || j>i){ 
                        continue; 
                    } 
                       
                    String[] jtime = list.get(j).split("-"); 
                    //此处DateUtils.compare为日期比较(返回负数date1小、返回0两数相等、返回正整数date1大) 
                    int compare = sdf1.parse(day+" "+itime[0]+":00").compareTo(sdf1.parse(day+" "+jtime[1]+":00")); 
                    if(compare<=0){ 
                        flag = true; 
                        break;//只要存在一个重叠则可退出内循环 
                    } 
                } 
            } 
               
            //当标识已经认为重叠了则可退出外循环 
            if(flag){ 
                break; 
            } 
        } 
           
        return flag; 
    } 
}