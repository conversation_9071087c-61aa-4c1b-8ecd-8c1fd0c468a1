/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeterGroupMap{ } 
 * 
 * 摘    要： assetMeterGroupMap
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-01-25 07:22:09
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.asset;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.datatype.DatatypeConfigurationException;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import ch.iec.tc57._2011.enddevicecontrols.RequestEndDeviceControlsPort;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl.EndDevices;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControls;
import ch.iec.tc57._2011.enddevicecontrolsmessage.EndDeviceControlsPayloadType;
import ch.iec.tc57._2011.enddevicecontrolsmessage.EndDeviceControlsRequestMessageType;
import ch.iec.tc57._2011.enddevicecontrolsmessage.EndDeviceControlsResponseMessageType;
import ch.iec.tc57._2011.schema.message.HeaderType;
import ch.iec.tc57._2011.schema.message.ReplyType;
import ch.iec.tc57._2011.schema.message.RequestType;
import ch.iec.tc57._2011.schema.message.UserType;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.CreatePdf;
import com.clou.esp.hes.app.web.core.util.DateTimeFormatterUtil;
import com.clou.esp.hes.app.web.core.util.ExcelUtils;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.core.util.UciInterfaceUtil;
import com.clou.esp.hes.app.web.core.util.XMLUtil;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.asset.AssetMeterDto;
import com.clou.esp.hes.app.web.model.asset.ConnOrDisconn;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.system.SysServiceAttributeService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.clou.esp.hes.app.web.validation.ValidGroup2;
import com.power7000g.core.excel.ExcelDataFormatter;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.redis.JedisUtils;

/**
 * <AUTHOR>
 * @时间：2018-05-11 11:14:09
 * @描述：Connect / Disconnect
 */
@Controller
@RequestMapping("/meterConnectOrDisconnectController")
public class MeterConnectOrDisconnectController extends BaseController{

	final static String TIME_FLAG =ResourceUtil.getSessionattachmenttitle("local.time.flag");
	
 	@Resource
    private AssetMeterService assetMeterService;
 	
 	@Resource
    private SysServiceAttributeService sysServiceAttributeService;
 	@Resource
    private DataUserLogService dataUserLogService;

 	/**
	 * 跳转到Connect / Disconnect界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "toConnectOrDisconnect")
	public ModelAndView toConnectOrDisconnect(HttpServletRequest request,
			Model model) {
		return new ModelAndView("/asset/connectOrDisconnect");
	}

	/**
	 * assetMeter查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j = assetMeterService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 电表拉合闸
     * @param id
     * @return
     */
    @RequestMapping(value = "meterConnectOrDisconnect")
    @ResponseBody
    public AjaxJson meterConnectOrDisconnect(String meterId, String type,String relayOption, HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        Map<String, Object> attributes = new HashMap<>();
        //获取当前用户token
    	SysUser sysUser = TokenManager.getToken();
    	EndDeviceControlsResponseMessageType responseMessageType = new EndDeviceControlsResponseMessageType();
        try {
        	if(StringUtil.isNotEmpty(meterId)){
        		AssetMeter meter = assetMeterService.getMeterDetailInfo(meterId);
        		//调用UCI拉合闸接口（合闸：1，拉闸：2）
    			responseMessageType = this.meterConnectOrDisconnect_UCI(meter, sysUser, type,relayOption, request);
    			ReplyType replyType = responseMessageType.getReply();
    			if(replyType != null){
    				//添加操作日志
    				AssetMeterDto dto = this.assetMeterService.getMeterDtoInfo(meterId);
    				if("1".equals(type)){
    					dataUserLogService.insertDataUserLog(sysUser.getId(), "Connect / Disconnect", "Connect meter", dto.getLog("Connect meter"));
    				}else{
    					dataUserLogService.insertDataUserLog(sysUser.getId(),  "Connect / Disconnect", "Disconnect meter", dto.getLog("Disconnect meter"));
    				}
    				if("0.0".equals(replyType.getError().get(0).getCode()) || "0.3".equals(replyType.getError().get(0).getCode())){
    					j.setMsg(replyType.getResult());
    					meter.setStatus("1");	//1:Processing,2:Success,3:Failed,4:Timeout
    				}else{
    					meter.setStatus("3");
    					j.setSuccess(false);
    					j.setMsg(replyType.getResult());
    				}
    				attributes.put("messageId", responseMessageType.getHeader().getMessageID());	//任务的唯一标识，messageId
    				attributes.put("type", type);

    				attributes.put("requestTime", DateUtils.date2Str(new Date(), DateTimeFormatterUtil.getSimpleDateFormat_HHmmss(TIME_FLAG)));

    				j.setAttributes(attributes);
    				j.setObj(meter);
    				//缓存数据入Redis（打印需要）
    				ConnOrDisconn entity = new ConnOrDisconn();
    				entity.setSn(meter.getSn());
    				entity.setCommSN(meter.getCommSn());
    				entity.setCommunication(meter.getCommunication());
    				entity.setCommand("1".equals(type) ? MutiLangUtil.doMutiLang("connOrDisconnList.connect")
    						: MutiLangUtil.doMutiLang("connOrDisconnList.disconnect"));
    				entity.setStatus(MutiLangUtil.doMutiLang("limiterList.processing"));
    				entity.setRequestTime(attributes.get("requestTime").toString());
    				JedisUtils.setObject(responseMessageType.getHeader().getMessageID(), entity, 0);
    			}else{
    				j.setSuccess(false);
					j.setMsg(MutiLangUtil.doMutiLang("connOrDisconnList.readTimeOut"));
    			}
        	}else{
        		j.setSuccess(false);
        		j.setMsg(MutiLangUtil.doMutiLang("connOrDisconnList.pleSelMeter"));
        	}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
        }
        return j;
    }
    
    /**
	 * 打印拉合闸数据表
	 * @throws Exception
	 * <AUTHOR>
	 */
	@RequestMapping(value = "exportList")
	@ResponseBody
	public void exportList(String ids, String type, HttpServletRequest request, HttpServletResponse response)throws Exception {
		if (StringUtil.isEmpty(ids)) {
			return;
		}else{
			List<ConnOrDisconn> list = new ArrayList<>();
			String[] idsArr = {};
			if(ids.indexOf(",") > 0){
				idsArr = ids.split(",");
			}
			if(idsArr.length > 0){
				for (int i = 0; i < idsArr.length; i++) {
		        	ConnOrDisconn entity = (ConnOrDisconn) JedisUtils.getObject(idsArr[i]);	//从Redis获取缓存的数据
		        	list.add(entity);
				}
			}else{
				ConnOrDisconn entity = (ConnOrDisconn) JedisUtils.getObject(ids);
				list.add(entity);
			}
			ExcelDataFormatter edf = new ExcelDataFormatter();
			if (type.equals("export")) {// export
				ExcelUtils.writeToFile(list, edf, System.currentTimeMillis() + ".xlsx", response, ValidGroup1.class);
			} else {
				CreatePdf.printPdf(list, edf, ValidGroup2.class, request, response);
			}
		}
	}
	
	/**
	 * 电表拉、合闸 调用UCI接口返回数据
	 * @Description 
	 * @throws DatatypeConfigurationException void
	 * <AUTHOR> 
	 * @Time 2018年5月14日 下午3:31:39
	 */
	public EndDeviceControlsResponseMessageType meterConnectOrDisconnect_UCI(AssetMeter meter, 
			SysUser sysUser, String type, String relayOption,HttpServletRequest request){
		String basePath = ResourceUtil.getUciBasePath(request);
		// Header
		HeaderType headerType = new HeaderType();
		Date currentDate = new Date();
		headerType.setVerb("created");
		headerType.setNoun("EndDeviceControls");
		headerType.setRevision("2.0");
		headerType.setTimestamp(DateUtils.dateToXmlDate(currentDate));
		headerType.setSource("ClouESP HES");
		headerType.setAsyncReplyFlag(true);
		headerType.setAckRequired(true);
		headerType.setReplyAddress(basePath+"/interfaces/ReplyEndDeviceControlsPort?wsdl");		//UCI回调地址
		headerType.setMessageID(meter.getSn() + UUID.randomUUID());	//同WEB界面数据的主键ID
		UserType userType = new UserType();
		userType.setOrganization(sysUser.getOrgId());
		userType.setUserID(sysUser.getId());
		headerType.setUser(userType);
		
		// payload
		EndDeviceControlsPayloadType payload = new EndDeviceControlsPayloadType();
		EndDeviceControls endDeviceControls = new EndDeviceControls();
		EndDeviceControl endDeviceControl = new EndDeviceControl();
		EndDeviceControl.EndDeviceControlType controlType = new EndDeviceControl.EndDeviceControlType();
		if("1".equals(type)){
			if("1".equals(relayOption)) {
				controlType.setRef("**********");	//主继电器合闸dataitem ID
			}else if("2".equals(relayOption)) {
				controlType.setRef("**********");	//扩展继电器合闸dataitem ID
			}
			endDeviceControl.setReason("Connect");
		}else{
			if("1".equals(relayOption)) {
				controlType.setRef("3.0.211.23");	//主继电器disconnect 拉闸
			}else if("2".equals(relayOption)) {
				controlType.setRef("3.0.211.24");	//扩展继电器disconnect 拉闸
			}
			endDeviceControl.setReason("Disconnect");
		}
		
		EndDevices endDevices = new EndDevices();
		endDevices.setMRID(meter.getSn());		//电表SN
		endDeviceControl.setEndDeviceControlType(controlType);
		endDeviceControl.getEndDevices().add(endDevices);
		endDeviceControls.getEndDeviceControl().add(endDeviceControl);
		payload.setEndDeviceControls(endDeviceControls);
		
		// request
		RequestType requestType = null;
		// EndDeviceControlsRequestMessageType
		EndDeviceControlsRequestMessageType requestMessageType = new EndDeviceControlsRequestMessageType();
		requestMessageType.setHeader(headerType);
		requestMessageType.setPayload(payload);
		requestMessageType.setRequest(requestType);
		//打印xml展示
		System.out.println(("1".equals(type)?"Connect":"Disconnect") + " request message is ==== " + XMLUtil.convertToXml(requestMessageType));
		
		// 实例化UCI接口
		RequestEndDeviceControlsPort requestPort = (RequestEndDeviceControlsPort)UciInterfaceUtil
        		.getInterface("RequestEndDeviceControlsPort", RequestEndDeviceControlsPort.class, sysServiceAttributeService);
		EndDeviceControlsResponseMessageType responseMessageType = new EndDeviceControlsResponseMessageType();
		try {
			//调用接口，返回参数
			responseMessageType = requestPort.createEndDeviceControls(requestMessageType);
		} catch (Exception e) {
			System.out.println(e.getMessage());
			e.printStackTrace();
		}
		return responseMessageType;
	}
}