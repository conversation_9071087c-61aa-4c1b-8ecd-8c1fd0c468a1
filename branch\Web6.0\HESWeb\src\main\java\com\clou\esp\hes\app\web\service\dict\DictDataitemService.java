/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictDataitem{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
 *********************************************************************************************************/

package com.clou.esp.hes.app.web.service.dict;

import java.util.List;

import com.clou.esp.hes.app.web.service.common.CommonService;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;

public interface DictDataitemService extends CommonService<DictDataitem> {

	public List<DictDataitem> getDictDataitemByGroupId(String groupId);
	public List<DictDataitem> getMeterDataEventExportList(DictDataitem temp);
	public List<DictDataitem> getList(DictDataitem entity);
	public List<DictDataitem> getListByIds(List<String> Ids);
	public String  getDataitemBySubId(String dataitemId,String protocalId);
}