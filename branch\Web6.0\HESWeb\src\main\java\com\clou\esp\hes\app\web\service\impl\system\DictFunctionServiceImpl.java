/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictFunction{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-01 09:42:07
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.system;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.system.DictFunctionDao;
import com.clou.esp.hes.app.web.model.system.DictFunction;
import com.clou.esp.hes.app.web.service.system.DictFunctionService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dictFunctionService")
public class DictFunctionServiceImpl  extends CommonServiceImpl<DictFunction>  implements DictFunctionService {

	@Resource
	private DictFunctionDao dictFunctionDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dictFunctionDao);
    }
	@SuppressWarnings("rawtypes")
	public DictFunctionServiceImpl() {}
	
	
}