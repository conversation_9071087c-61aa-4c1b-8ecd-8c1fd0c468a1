var device_read_channel_listisLoad = true;
var device_read_channel_listjqgridHeight = 'auto';
var communicatorMap = {};
communicatorMap['100'] = 'GPRS';
communicatorMap['101'] = 'Ethernet';
communicatorMap['102'] = '3G';
communicatorMap['103'] = '4G';
communicatorMap['200'] = 'RS485';
communicatorMap['201'] = 'G3 PLC';
communicatorMap['202'] = 'RF';
communicatorMap['203'] = 'FSK PLC';
var statusMap = {};
statusMap['0'] = 'Upcoming';
statusMap['1'] = 'Processing';
statusMap['2'] = 'Success';
statusMap['3'] = 'Failed';
statusMap['4'] = 'Timeout';
layer.load();
$(function() {
	var bodyHeight = document.body.scrollHeight;
	var rowDiv = window.document
			.getElementById('content_row_device_read_channel_list');
	if (rowDiv != null) {
		var rowHeight = rowDiv.offsetHeight;
		if (device_read_channel_listjqgridHeight == 'auto') {
			device_read_channel_listjqgridHeight = bodyHeight - rowHeight - 150
					- 215;
		}
	} else {
		if (device_read_channel_listjqgridHeight == 'auto') {
			device_read_channel_listjqgridHeight = bodyHeight - 150 - 215;
		}
	}
	device_read_channel_listfun();
	$('#gview_device_read_channel_list')
			.children('div.ui-jqgrid-titlebar')
			.append(
					"<div class=' titleBtnItem ' ><div class='btn_wrapper'   onclick=\"getExcelResultData('device_read_channel_list','Export','','60%','90%',false)\"  title=\"Export\" ><div class= 'ui-title-btn'><span class='glyphicon glyphicon-export'></span></div></div> <div class='btn_wrapper'   onclick=\"getPrintResultData('device_read_channel_list','Print','','60%','90%',false)\"  title=\"Print\" ><div class= 'ui-title-btn'><span class='glyphicon glyphicon-print'></span></div></div> </div>");
});
var device_read_channel_listfun = function() {
	if (device_read_channel_listisLoad) {
		device_read_channel_listisLoad = false;
		$.jgrid.defaults.styleUI = "Bootstrap";
		var headData = {
			'field' : 'id,sn,communicator,requestTime,status,reason,responseTime,dataChannel,value',
			'searchField' : '',
			'searchString' : ''
		};
		$("#device_read_channel_list")
				.jqGrid(
						{
							sortname : 'null',
							sortorder : 'asc',
							rownumbers : true,
							loadComplete : loadComplete,
							altRows : true,
							altclass : 'differ',
							url : 'http://10.8.160.214:8081/HESWeb/assetMeterController/startReadsDatagrid.do',
							mtype : "post",
							datatype : "json",
							height : device_read_channel_listjqgridHeight,
							width : "auto",
							autowidth : true,
							multiselect : false,
							colNames : [ '编号', 'Serial Numuber',
									'Communicator', 'Request Time', 'Status',
									'Reason', 'Response Time', 'Data Channel',
									'Value' ],
							colModel : [
									{
										name : 'id',
										index : 'id',
										editable : true,
										sortable : false,
										hidden : true,
										frozen : false
									},
									{
										name : 'sn',
										index : 'sn',
										editable : true,
										sortable : false,
										hidden : false,
										frozen : false
									},
									{
										name : 'communicator',
										index : 'communicator',
										editable : true,
										width : 120,
										sortable : false,
										hidden : false,
										formatter : function(cellvalue,
												options, rowObject) {
											var value = communicatorMap[cellvalue];
											if (value != null) {
												return value;
											} else if (cellvalue != null) {
												return cellvalue;
											} else {
												return '';
											}
										},
										frozen : false
									},
									{
										name : 'requestTime',
										index : 'requestTime',
										editable : true,
										width : 160,
										sortable : false,
										hidden : false,
										formatter : "date",
										formatoptions : {
											srcformat : 'm/d/Y H:i:s',
											newformat : 'm/d/Y H:i:s'
										},
										frozen : false
									},
									{
										name : 'status',
										index : 'status',
										editable : true,
										cellattr : addCellAttr,
										width : 120,
										sortable : false,
										hidden : false,
										formatter : function(cellvalue,
												options, rowObject) {
											var value = statusMap[cellvalue];
											if (value != null) {
												return value;
											} else if (cellvalue != null) {
												return cellvalue;
											} else {
												return '';
											}
										},
										align : 'left',
										frozen : false
									}, {
										name : 'reason',
										index : 'reason',
										editable : true,
										width : 188,
										sortable : false,
										hidden : false,
										frozen : false
									}, {
										name : 'responseTime',
										index : 'responseTime',
										editable : true,
										width : 160,
										sortable : false,
										hidden : false,
										formatter : "date",
										formatoptions : {
											srcformat : 'm/d/Y H:i:s',
											newformat : 'm/d/Y H:i:s'
										},
										frozen : false
									}, {
										name : 'dataChannel',
										index : 'dataChannel',
										editable : true,
										width : 222,
										sortable : false,
										hidden : false,
										frozen : false
									}, {
										name : 'value',
										index : 'value',
										editable : true,
										sortable : false,
										hidden : false,
										align : 'right',
										frozen : false
									}, ],
							rowNum : 99999,
							pagerpos : false,
							pgbuttons : false,
							viewrecords : true,
							caption : "Result",
							shrinkToFit : false,
							autoScroll : true,
							postData : headData,
						});
		jQuery("#device_read_channel_list").jqGrid('setFrozenColumns');
		$("#device_read_channel_list").closest(".ui-jqgrid-bdiv").css({
			"overflow-x" : "scroll"
		});
		$("#device_read_channel_list").closest(".ui-jqgrid-bdiv").css({
			"overflow-y" : "scroll"
		});
		$(window)
				.bind(
						"resize",
						function() {
							var width = $("#gbox_device_read_channel_list")
									.parent().width();
							if (width > 0) {
								$("#device_read_channel_list").setGridWidth(
										width);
							}
							var bodyHeight1 = document.body.scrollHeight;
							var rowDiv1 = window.document
									.getElementById('content_row_device_read_channel_list');
							if (rowDiv1 != null) {
								var rowHeight1 = rowDiv1.offsetHeight;
								device_read_channel_listjqgridHeight = bodyHeight1
										- rowHeight1 - 150 - 215;
							} else {
								device_read_channel_listjqgridHeight = bodyHeight1 - 150 - 215;
							}
							$("#device_read_channel_list").setGridHeight(
									device_read_channel_listjqgridHeight);
						});
		jQuery("#device_read_channel_list_resetBtn").click(function() {
			device_read_channel_listsearchOnEnterFn();
		});
		device_read_channel_listisLoad = true;
		layer.closeAll('loading');
	} else {
		setTimeout(function() {
			device_read_channel_listfun();
		}, 100);
	}
};
function device_read_channel_listsearchOnEnterFn() {
	var data = {};
	var d = statrReadsNew();
	if (d != null) {
		data = Object.assign(data, d);

		$('#device_read_channel_list')
				.jqGrid(
						'setGridParam',
						{
							async : false,
							cache : false,
							traditional : true,
							url : 'http://10.8.160.214:8081/HESWeb/assetMeterController/startReadsDatagrid.do',
							postData : data,
							page : 1
						}).trigger('reloadGrid');
	}
}
function device_read_channel_listoptFormatter(cellvalue, options, rowObject) {
	var querStr = '';
	return querStr;
}