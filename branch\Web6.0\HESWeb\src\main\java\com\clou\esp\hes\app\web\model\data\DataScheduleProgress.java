/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataScheduleProgress{ } 
 * 
 * 摘    要： dataScheduleProgress
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-12-01 06:03:46
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.data;

import java.math.BigDecimal;
import java.util.Date;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.excel.Excel;

public class DataScheduleProgress extends BaseEntity {

	/**
	 * 意义，目的和功能，以及被用到的地方<br>
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DataScheduleProgress() {
	}

	/** tv */
	private java.util.Date tv;
	/** update tv */
	private java.util.Date updateTv;
	/** com status */
	private BigDecimal comStatus;
	
	/** lastTaskTv */
	private java.util.Date lastTaskTv;
	/** successful, failed */
	private java.lang.String taskState;
	/** failedInfo */
	private java.lang.String failedInfo;
	//页面要展示的数据
	/*** Meter Sn */
	@Excel(name = "Meter Sn", width = 15, groups = ValidGroup1.class)
	private String meterSn;
	/*** Profile */
	@Excel(name = "Profile", width = 15, groups = ValidGroup1.class)
	private String profileId;
	private String mfrId;
	private String modelId;
	
	
	private String deviceId;
	private String profile;
	/*** Communicator Sn */
	@Excel(name = "Communicator Sn", width = 15, groups = ValidGroup1.class)
	private String communicatorSn;
	/*** communicator Type */
	@Excel(name = "Communicator Type", width = 15, groups = ValidGroup1.class)
	private String communicationType;
	
	/*** Progress */
	@Excel(name = "Progress", width = 15, groups = ValidGroup1.class)
	
	private Date progress;
	
	/*** progress Delay */
	@Excel(name = "Progress Delay", width = 15, groups = ValidGroup1.class)
	
	private String progressDelay;
	
	private Date deviceCreateTime;
	private String queryTime;
	
	private String profileCycleType;

	public Date getDeviceCreateTime() {
		return deviceCreateTime;
	}

	public void setDeviceCreateTime(Date deviceCreateTime) {
		this.deviceCreateTime = deviceCreateTime;
	}

	public java.util.Date getUpdateTv() {
		return updateTv;
	}

	public void setUpdateTv(java.util.Date updateTv) {
		this.updateTv = updateTv;
	}

	public BigDecimal getComStatus() {
		return comStatus;
	}

	public void setComStatus(BigDecimal comStatus) {
		this.comStatus = comStatus;
	}

	public String getMfrId() {
		return mfrId;
	}

	public void setMfrId(String mfrId) {
		this.mfrId = mfrId;
	}

	public String getModelId() {
		return modelId;
	}

	public void setModelId(String modelId) {
		this.modelId = modelId;
	}

	public String getQueryTime() {
		return queryTime;
	}

	public void setQueryTime(String queryTime) {
		this.queryTime = queryTime;
	}
	
	

	public String getProfileCycleType() {
		return profileCycleType;
	}

	public void setProfileCycleType(String profileCycleType) {
		this.profileCycleType = profileCycleType;
	}

	public String getProfile() {
		return profile;
	}

	public void setProfile(String profile) {
		this.profile = profile;
	}

	public String getMeterSn() {
		return meterSn;
	}

	public void setMeterSn(String meterSn) {
		this.meterSn = meterSn;
	}

	public String getCommunicatorSn() {
		return communicatorSn;
	}

	public void setCommunicatorSn(String communicatorSn) {
		this.communicatorSn = communicatorSn;
	}

	public String getCommunicationType() {
		return communicationType;
	}

	public void setCommunicationType(String communicationType) {
		this.communicationType = communicationType;
	}

	public Date getProgress() {
		return progress;
	}

	public void setProgress(Date progress) {
		this.progress = progress;
	}

	public String getProgressDelay() {
		return progressDelay;
	}

	public void setProgressDelay(String progressDelay) {
		this.progressDelay = progressDelay;
	}

	/**
	 * tv
	 * 
	 * @return the value of DATA_SCHEDULE_PROGRESS.TV
	 * @mbggenerated 2017-12-01 06:03:46
	 */
	public java.util.Date getTv() {
		return tv;
	}

	/**
	 * tv
	 * 
	 * @param tv
	 *            the value for DATA_SCHEDULE_PROGRESS.TV
	 * @mbggenerated 2017-12-01 06:03:46
	 */
	public void setTv(java.util.Date tv) {
		this.tv = tv;
	}

	/**
	 * lastTaskTv
	 * 
	 * @return the value of DATA_SCHEDULE_PROGRESS.LAST_TASK_TV
	 * @mbggenerated 2017-12-01 06:03:46
	 */
	public java.util.Date getLastTaskTv() {
		return lastTaskTv;
	}

	/**
	 * lastTaskTv
	 * 
	 * @param lastTaskTv
	 *            the value for DATA_SCHEDULE_PROGRESS.LAST_TASK_TV
	 * @mbggenerated 2017-12-01 06:03:46
	 */
	public void setLastTaskTv(java.util.Date lastTaskTv) {
		this.lastTaskTv = lastTaskTv;
	}

	/**
	 * successful, failed
	 * 
	 * @return the value of DATA_SCHEDULE_PROGRESS.TASK_STATE
	 * @mbggenerated 2017-12-01 06:03:46
	 */
	public java.lang.String getTaskState() {
		return taskState;
	}

	/**
	 * successful, failed
	 * 
	 * @param taskState
	 *            the value for DATA_SCHEDULE_PROGRESS.TASK_STATE
	 * @mbggenerated 2017-12-01 06:03:46
	 */
	public void setTaskState(java.lang.String taskState) {
		this.taskState = taskState;
	}

	/**
	 * failedInfo
	 * 
	 * @return the value of DATA_SCHEDULE_PROGRESS.FAILED_INFO
	 * @mbggenerated 2017-12-01 06:03:46
	 */
	public java.lang.String getFailedInfo() {
		return failedInfo;
	}

	/**
	 * failedInfo
	 * 
	 * @param failedInfo
	 *            the value for DATA_SCHEDULE_PROGRESS.FAILED_INFO
	 * @mbggenerated 2017-12-01 06:03:46
	 */
	public void setFailedInfo(java.lang.String failedInfo) {
		this.failedInfo = failedInfo;
	}

	public DataScheduleProgress(java.util.Date tv, java.util.Date lastTaskTv,
			java.lang.String taskState, java.lang.String failedInfo) {
		super();
		this.tv = tv;
		this.lastTaskTv = lastTaskTv;
		this.taskState = taskState;
		this.failedInfo = failedInfo;
	}

	public String getProfileId() {
		return profileId;
	}

	public void setProfileId(String profileId) {
		this.profileId = profileId;
	}

	public String getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}

}