/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictVeeEventParam{ } 
 * 
 * 摘    要： dictVeeEventParam
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2019-03-06 02:51:20
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.vee;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DictVeeEventParam  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DictVeeEventParam() {
	}

	/**paramKey*/
	private java.lang.String paramKey;
	/**paramDesc*/
	private java.lang.String paramDesc;
	/**defautValue*/
	private java.math.BigDecimal defautValue;

	/**
	 * paramKey
	 * @return the value of DICT_VEE_EVENT_PARAM.PARAM_KEY
	 * @mbggenerated 2019-03-06 02:51:20
	 */
	public java.lang.String getParamKey() {
		return paramKey;
	}

	/**
	 * paramKey
	 * @param paramKey the value for DICT_VEE_EVENT_PARAM.PARAM_KEY
	 * @mbggenerated 2019-03-06 02:51:20
	 */
    	public void setParamKey(java.lang.String paramKey) {
		this.paramKey = paramKey;
	}
	/**
	 * paramDesc
	 * @return the value of DICT_VEE_EVENT_PARAM.PARAM_DESC
	 * @mbggenerated 2019-03-06 02:51:20
	 */
	public java.lang.String getParamDesc() {
		return paramDesc;
	}

	/**
	 * paramDesc
	 * @param paramDesc the value for DICT_VEE_EVENT_PARAM.PARAM_DESC
	 * @mbggenerated 2019-03-06 02:51:20
	 */
    	public void setParamDesc(java.lang.String paramDesc) {
		this.paramDesc = paramDesc;
	}
	/**
	 * defautValue
	 * @return the value of DICT_VEE_EVENT_PARAM.DEFAUT_VALUE
	 * @mbggenerated 2019-03-06 02:51:20
	 */
	public java.math.BigDecimal getDefautValue() {
		return defautValue;
	}

	/**
	 * defautValue
	 * @param defautValue the value for DICT_VEE_EVENT_PARAM.DEFAUT_VALUE
	 * @mbggenerated 2019-03-06 02:51:20
	 */
    	public void setDefautValue(java.math.BigDecimal defautValue) {
		this.defautValue = defautValue;
	}

	public DictVeeEventParam(java.lang.String paramKey 
	,java.lang.String paramDesc 
	,java.math.BigDecimal defautValue ) {
		super();
		this.paramKey = paramKey;
		this.paramDesc = paramDesc;
		this.defautValue = defautValue;
	}

}