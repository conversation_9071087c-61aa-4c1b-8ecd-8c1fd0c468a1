package com.clou.esp.hes.app.web.model.tariff;

import com.clou.esp.hes.app.web.model.common.BaseEntity;



/*********************************************************************************************************
 * @see Copyright© 2019 Shenzhen Clou Electronics CO., LTD.  All rights reserved. 
 * @see 
 * @see File Name:public class PpmAssetTariffStepDetail{ } 
 * @see 
 * @see Description： ppmAssetTariffStepDetail
 * @version *******
 * <AUTHOR>
 * @see Create Time：2019-09-17 10:05:10
 * @see Last modification Time：2019-09-17 10:05:10
 * 
*********************************************************************************************************/
public class PpmAssetTariffStepDetail  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public PpmAssetTariffStepDetail() {
	}

	/**stepName*/
	private java.lang.String stepName;
	/**stepIndex*/
	private java.lang.Integer stepIndex;
	/**taxType*/
	private java.lang.Integer taxType;
	/**tariffName*/
	private java.lang.String tariffName;
	
	/**tariffName*/
	private java.lang.String oldTariffName;
	/**theFormula*/
	private java.lang.String theFormula;
	/**variableValue*/
	private java.math.BigDecimal variableValue;
	/**chargeMode*/
	private java.lang.Integer chargeMode;
	/**descr*/
	private java.lang.String descr;
	/**calculated*/
	private java.lang.String calculated;


	
	
	public java.lang.String getOldTariffName() {
		return oldTariffName;
	}

	public void setOldTariffName(java.lang.String oldTariffName) {
		this.oldTariffName = oldTariffName;
	}

	public java.lang.String getStepName() {
		return stepName;
	}

	public void setStepName(java.lang.String stepName) {
		this.stepName = stepName;
	}

	/**
	 * 实体编号（唯一标识）
	 */
	protected String groupId;
	

	/**
	 * 实体编号（唯一标识）
	 */
	protected String name;
	
	
	

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}

	public java.lang.Integer getStepIndex() {
		return stepIndex;
	}

	public void setStepIndex(java.lang.Integer stepIndex) {
		this.stepIndex = stepIndex;
	}

	/**
	 * tariffName
	 * @return the value of PPM_ASSET_TARIFF_STEP_DETAIL.TARIFF_NAME
	 * @mbggenerated 2019-09-17 10:05:10
	 */
	public java.lang.String getTariffName() {
		return tariffName;
	}

	/**
	 * tariffName
	 * @param tariffName the value for PPM_ASSET_TARIFF_STEP_DETAIL.TARIFF_NAME
	 * @mbggenerated 2019-09-17 10:05:10
	 */
    	public void setTariffName(java.lang.String tariffName) {
		this.tariffName = tariffName;
	}
	/**
	 * theFormula
	 * @return the value of PPM_ASSET_TARIFF_STEP_DETAIL.THE_FORMULA
	 * @mbggenerated 2019-09-17 10:05:10
	 */
	public java.lang.String getTheFormula() {
		return theFormula;
	}

	/**
	 * theFormula
	 * @param theFormula the value for PPM_ASSET_TARIFF_STEP_DETAIL.THE_FORMULA
	 * @mbggenerated 2019-09-17 10:05:10
	 */
    	public void setTheFormula(java.lang.String theFormula) {
		this.theFormula = theFormula;
	}
	/**
	 * variableValue
	 * @return the value of PPM_ASSET_TARIFF_STEP_DETAIL.VARIABLE_VALUE
	 * @mbggenerated 2019-09-17 10:05:10
	 */
	public java.math.BigDecimal getVariableValue() {
		return variableValue;
	}

	/**
	 * variableValue
	 * @param variableValue the value for PPM_ASSET_TARIFF_STEP_DETAIL.VARIABLE_VALUE
	 * @mbggenerated 2019-09-17 10:05:10
	 */
    	public void setVariableValue(java.math.BigDecimal variableValue) {
		this.variableValue = variableValue;
	}

	/**
	 * descr
	 * @return the value of PPM_ASSET_TARIFF_STEP_DETAIL.DESCR
	 * @mbggenerated 2019-09-17 10:05:10
	 */
	public java.lang.String getDescr() {
		return descr;
	}

	/**
	 * descr
	 * @param descr the value for PPM_ASSET_TARIFF_STEP_DETAIL.DESCR
	 * @mbggenerated 2019-09-17 10:05:10
	 */
    	public void setDescr(java.lang.String descr) {
		this.descr = descr;
	}
	/**
	 * calculated
	 * @return the value of PPM_ASSET_TARIFF_STEP_DETAIL.CALCULATED
	 * @mbggenerated 2019-09-17 10:05:10
	 */
	public java.lang.String getCalculated() {
		return calculated;
	}

	/**
	 * calculated
	 * @param calculated the value for PPM_ASSET_TARIFF_STEP_DETAIL.CALCULATED
	 * @mbggenerated 2019-09-17 10:05:10
	 */
    	public void setCalculated(java.lang.String calculated) {
		this.calculated = calculated;
	}
    	
    	

	public java.lang.Integer getTaxType() {
		return taxType;
	}

	public void setTaxType(java.lang.Integer taxType) {
		this.taxType = taxType;
	}

	public java.lang.Integer getChargeMode() {
		return chargeMode;
	}

	public void setChargeMode(java.lang.Integer chargeMode) {
		this.chargeMode = chargeMode;
	}

	public PpmAssetTariffStepDetail(java.lang.Integer stepIndex 
	,java.lang.Integer taxType 
	,java.lang.String tariffName 
	,java.lang.String theFormula 
	,java.math.BigDecimal variableValue 
	,java.lang.Integer chargeMode 
	,java.lang.String descr 
	,java.lang.String calculated ) {
		super();
		this.stepIndex = stepIndex;
		this.taxType = taxType;
		this.tariffName = tariffName;
		this.theFormula = theFormula;
		this.variableValue = variableValue;
		this.chargeMode = chargeMode;
		this.descr = descr;
		this.calculated = calculated;
	}

}