# Control logging for other open source packages
log4j.logger.com.opensymphony.oscache=ERROR
log4j.logger.net.sf.navigator=ERROR
log4j.logger.net.sf.acegisecurity=WARN
log4j.logger.net.sf.acegisecurity.intercept.event.LoggerListener=WARN
log4j.logger.org.apache.commons=ERROR
log4j.logger.org.apache.struts=WARN
log4j.logger.org.displaytag=ERROR
log4j.logger.org.springframework=WARN
log4j.logger.com.ibatis.db=WARN
log4j.logger.org.apache.velocity=WARN
# Don't show debug logs for WebTest
log4j.logger.com.canoo.webtest=WARN
# All hibernate log output of "info" level or higher goes to stdout.
# For more verbose logging, change the "info" to "debug" on the last line.
log4j.logger.org.hibernate.ps.PreparedStatementCache=WARN
log4j.logger.org.hibernate=WARN
# Changing the log level to DEBUG will result in Hibernate generated
# SQL to be logged.
log4j.logger.org.hibernate.SQL=ERROR
# Changing the log level to DEBUG will result in the PreparedStatement
# bound variable values to be logged.
log4j.logger.org.hibernate.type=ERROR



log4j.rootLogger=info,A1,R
log4j.appender.A1=org.apache.log4j.ConsoleAppender
log4j.appender.A1.Target=System.out
log4j.appender.A1.layout=org.apache.log4j.PatternLayout
log4j.appender.A1.layout.ConversionPattern=[%c]%m%n

log4j.appender.R=org.apache.log4j.DailyRollingFileAppender
log4j.appender.R.File=../HESWeblogs/HESWeb_log_.txt
log4j.appender.R.layout=org.apache.log4j.PatternLayout
log4j.appender.R.Append = true
log4j.appender.R.ImmediateFlush = true
log4j.appender.R.DatePattern = '.' yyyy - MM - dd '.txt'
log4j.appender.R.layout.ConversionPattern=[%p][%d{yyyy-MM-dd HH\:mm\:ss,SSS}][%c]%m%n

#3 druid
log4j.logger.druid.sql=INFO
log4j.logger.druid.sql.DataSource=info
log4j.logger.druid.sql.Connection=info
log4j.logger.druid.sql.Statement=info
log4j.logger.druid.sql.ResultSet=info

#4 mybatis \u663E\u793ASQL\u8BED\u53E5\u90E8\u5206
# MyBatis logging configuration...  
log4j.logger.org.mybatis.example.BlogMapper=TRACE  
log4j.logger.org.mybatis=DEBUG
log4j.logger.java.sql=DEBUG
log4j.logger.java.sql.Statement=DEBUG
log4j.logger.java.sql.ResultSet=DEBUG
log4j.logger.java.sql.PreparedStatement=DEBUG

log4j.logger.com.clou.esp.hes.app.web.dao=DEBUG
log4j.logger.com.clou.esp.hes.app.web.core.shiro.cache=DEBUG