/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictProfile{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-02-26 09:36:52
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.dict;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.dict.DictProfileDao;
import com.clou.esp.hes.app.web.model.dict.DictProfile;
import com.clou.esp.hes.app.web.service.dict.DictProfileService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dictProfileService")
public class DictProfileServiceImpl  extends CommonServiceImpl<DictProfile>  implements DictProfileService {

	@Resource
	private DictProfileDao dictProfileDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dictProfileDao);
    }
	@SuppressWarnings("rawtypes")
	public DictProfileServiceImpl() {}
	
	
}