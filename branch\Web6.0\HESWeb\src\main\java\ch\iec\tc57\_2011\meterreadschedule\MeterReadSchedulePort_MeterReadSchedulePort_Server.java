
package ch.iec.tc57._2011.meterreadschedule;

import javax.xml.ws.Endpoint;

/**
 * This class was generated by Apache CXF 3.1.1
 * 2018-02-01T09:54:34.776+08:00
 * Generated source version: 3.1.1
 * 
 */
 
public class MeterReadSchedulePort_MeterReadSchedulePort_Server{

    protected MeterReadSchedulePort_MeterReadSchedulePort_Server() throws java.lang.Exception {
        System.out.println("Starting Server");
        Object implementor = new MeterReadSchedule_PortImpl();
        String address = "http://iec.ch/TC57/2011/MeterReadSchedule";
        Endpoint.publish(address, implementor);
    }
    
    public static void main(String args[]) throws java.lang.Exception { 
        new MeterReadSchedulePort_MeterReadSchedulePort_Server();
        System.out.println("Server ready..."); 
        
        Thread.sleep(5 * 60 * 1000); 
        System.out.println("Server exiting");
        System.exit(0);
    }
}
