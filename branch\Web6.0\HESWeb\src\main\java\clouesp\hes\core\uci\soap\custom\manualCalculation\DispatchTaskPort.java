package clouesp.hes.core.uci.soap.custom.manualCalculation;


import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

/**
 * This class was generated by Apache CXF 3.1.18
 * 2019-04-18T11:18:00.950+08:00
 * Generated source version: 3.1.18
 * 
 */
@WebService(targetNamespace = "http://dispatch_task.custom.soap.uci.core.hes.clouesp/", name = "DispatchTaskPort")
@XmlSeeAlso({ObjectFactory.class})
public interface DispatchTaskPort {

    @WebMethod
    @RequestWrapper(localName = "dispatch", targetNamespace = "http://dispatch_task.custom.soap.uci.core.hes.clouesp/", className = "clouesp.hes.core.uci.soap.comstom.manualCalculation.Dispatch")
    @ResponseWrapper(localName = "dispatchResponse", targetNamespace = "http://dispatch_task.custom.soap.uci.core.hes.clouesp/", className = "clouesp.hes.core.uci.soap.comstom.manualCalculation.DispatchResponse")
    @WebResult(name = "return", targetNamespace = "")
    public boolean dispatch(
        @WebParam(name = "msgs", targetNamespace = "")
        java.util.List<DispatchMessage> msgs
    );
}
