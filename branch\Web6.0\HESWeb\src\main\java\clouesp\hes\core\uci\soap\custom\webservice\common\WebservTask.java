package clouesp.hes.core.uci.soap.custom.webservice.common;

import java.io.Serializable;

import com.clou.esp.hes.app.web.enums.system.TaskState;


/**
 * @ClassName: SoapTask
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年7月18日 下午3:38:00
 *
 */
public class WebservTask implements Serializable{
	
	private static final long serialVersionUID = 1L;
	private String id;// 唯一标识
	private String dataItemId; // 数据项 Id
	private TaskState status;// 任务运行状态
	private Object value; // 数据
	private String responseTime; // 响应时间
	private String meter; // 电表地址
	
	public WebservTask(){
	}
	
	public WebservTask(String id, String dataItemId, TaskState status, Object value) {
		this.id = id;
		this.dataItemId = dataItemId;
		this.status = status;
		this.value = value;
	}
	
	public WebservTask(String id, String dataItemId, TaskState status, Object value,String meter) {
		this(id,dataItemId,status,value);
		this.meter = meter;
	}
	
	public String getId() {
		return id;
	}
	
	public void setId(String id) {
		this.id = id;
	}
	
	public String getDataItemId() {
		return dataItemId;
	}

	public void setDataItemId(String dataItemId) {
		this.dataItemId = dataItemId;
	}

	public TaskState getStatus() {
		return status;
	}
	public void setStatus(TaskState status) {
		this.status = status;
	}

	public Object getValue() {
		return value;
	}

	public void setValue(Object value) {
		this.value = value;
	}

	public String getResponseTime() {
		return responseTime;
	}

	public void setResponseTime(String responseTime) {
		this.responseTime = responseTime;
	}

	public String getMeter() {
		return meter;
	}

	public void setMeter(String meter) {
		this.meter = meter;
	}
}
