<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding" defaultCharsetForPropertiesFiles="GBK">
    <file url="file://$PROJECT_DIR$/Common/Asset/src" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Common/Asset/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Common/AssetTrans/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Common/DbAccess/src" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Common/DbAccess/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Common/IntervalCalc/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Common/IntervalCalc/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Common/MQBus/src" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Common/MQBus/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Common/Protocol/src" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Common/Protocol/src/clouesp/hes/core/common/protocol/sg/data/TpData.java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Common/Protocol/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Common/logger/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Common/logger/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Common/loggerquery/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Common/loggerquery/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Common/storage/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Common/storage/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Common/task/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Common/task/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Core/Calculation/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Core/Calculation/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Core/InterFace/UCI_S/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Core/InterFace/UCI_S/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Core/InterFace/gyuci/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Core/InterFace/gyuci/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Core/Schedules/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Core/Schedules/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Core/UCI/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Core/UCI/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Core/channels/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Core/channels/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Core/hsuci/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Core/hxuci/src/main/java/cn/kaifa/inf/soap/ComModule.java" charset="GBK" />
    <file url="file://$PROJECT_DIR$/Core/hxuci/src/main/java/cn/kaifa/inf/soap/ComModuleConfig.java" charset="GBK" />
    <file url="file://$PROJECT_DIR$/Core/jnuci/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Core/loggerqueryser/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Core/loggerqueryser/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Core/syuci/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Core/syuci/src/main/java/ch/iec/tc57/_2011/meterdefineconfig/ReplyMeterDefineConfigPortImpl.java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/Core/syuci/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/../Mdm/MdmUCI/src/main/java" charset="UTF-8" />
    <file url="PROJECT" charset="UTF-8" />
  </component>
</project>