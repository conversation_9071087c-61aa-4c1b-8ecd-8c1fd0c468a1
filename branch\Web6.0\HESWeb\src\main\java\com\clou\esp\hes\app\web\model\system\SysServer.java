/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysServer{ } 
 * 
 * 摘    要： sysServer
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-03-29 03:12:41
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.system;

import java.util.List;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class SysServer  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public SysServer() {
	}

	/**introduction*/
	private java.lang.String introduction;
	/**ip*/
	private java.lang.String ip;
	/**haState*/
	private java.lang.String haState;
	/**是否在线*/
	private java.lang.String isOnline;
	/**服务类型*/
	private String serviceType;
	
	private String expanded;
	private String parent;
	private List<SysService> sysServiceList;

	/**
	 * introduction
	 * @return the value of SYS_SERVER.INTRODUCTION
	 * @mbggenerated 2018-03-29 03:12:41
	 */
	public java.lang.String getIntroduction() {
		return introduction;
	}

	/**
	 * introduction
	 * @param introduction the value for SYS_SERVER.INTRODUCTION
	 * @mbggenerated 2018-03-29 03:12:41
	 */
    	public void setIntroduction(java.lang.String introduction) {
		this.introduction = introduction;
	}
	/**
	 * ip
	 * @return the value of SYS_SERVER.IP
	 * @mbggenerated 2018-03-29 03:12:41
	 */
	public java.lang.String getIp() {
		return ip;
	}

	/**
	 * ip
	 * @param ip the value for SYS_SERVER.IP
	 * @mbggenerated 2018-03-29 03:12:41
	 */
    	public void setIp(java.lang.String ip) {
		this.ip = ip;
	}
	/**
	 * haState
	 * @return the value of SYS_SERVER.HA_STATE
	 * @mbggenerated 2018-03-29 03:12:41
	 */
	public java.lang.String getHaState() {
		return haState;
	}

	/**
	 * haState
	 * @param haState the value for SYS_SERVER.HA_STATE
	 * @mbggenerated 2018-03-29 03:12:41
	 */
    	public void setHaState(java.lang.String haState) {
		this.haState = haState;
	}
	/**
	 * isOnline
	 * @return the value of SYS_SERVER.IS_ONLINE
	 * @mbggenerated 2018-03-29 03:12:41
	 */
	public java.lang.String getIsOnline() {
		return isOnline;
	}

	/**
	 * isOnline
	 * @param isOnline the value for SYS_SERVER.IS_ONLINE
	 * @mbggenerated 2018-03-29 03:12:41
	 */
    	public void setIsOnline(java.lang.String isOnline) {
		this.isOnline = isOnline;
	}

	public SysServer(java.lang.String introduction 
	,java.lang.String ip 
	,java.lang.String haState 
	,java.lang.String isOnline ) {
		super();
		this.introduction = introduction;
		this.ip = ip;
		this.haState = haState;
		this.isOnline = isOnline;
	}

	public String getExpanded() {
		return expanded;
	}

	public void setExpanded(String expanded) {
		this.expanded = expanded;
	}

	public List<SysService> getSysServiceList() {
		return sysServiceList;
	}

	public void setSysServiceList(List<SysService> sysServiceList) {
		this.sysServiceList = sysServiceList;
	}

	public String getServiceType() {
		return serviceType;
	}

	public void setServiceType(String serviceType) {
		this.serviceType = serviceType;
	}

	public String getParent() {
		return parent;
	}

	public void setParent(String parent) {
		this.parent = parent;
	}
	
	@Override
	public String toString() {
		return "{id: " + ip + " ,ipintroduction: " + introduction + " ,ip :" + ip + " ,haState: " + haState + " ,isOnline: " + isOnline +"}";
	}
}