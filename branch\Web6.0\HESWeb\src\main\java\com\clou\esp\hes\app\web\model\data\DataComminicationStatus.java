/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataComminicationStatus{ } 
 * 
 * 摘    要： dataComminicationStatus
 * 版    本：1.0
 * 作    者：赵久霆
 * 创建于：2018-11-19 07:34:34
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.data;

import java.util.Date;

import org.apache.commons.lang.StringUtils;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.power7000g.core.excel.Excel;

public class DataComminicationStatus  extends BaseEntity {

    private static final long serialVersionUID = 1L;
    
	public DataComminicationStatus() {}


	private 	String 		ipAddr;
	private 	String 		ipPort;
	
	
	
	@Excel(name = "Device Type", width = 30)
	private 	String      deviceType;
	
	@Excel(name = "Meter SN", width = 30)
	private 	String      meterSn;
	@Excel(name = "Meter COM Type", width = 30)
	private 	String      meterComType;
	@Excel(name = "Communicator SN", width = 30)
	private 	String      commSn;
	@Excel(name = "Communicator COM Type", width = 30)
	private 	String      commComType;
	@Excel(name = "Status", width = 30)
	private 	String 		comStatus;
	@Excel(name = "Network Address", width = 30)
	private 	String 		networkAddress;
	@Excel(name = "Status Update Time", width = 30)
	private		Date 		updateTv;
	@Excel(name = "SIM Num", width = 30)
	private 	String      simNum;
	@Excel(name = "Organization", width = 30)
	private     String      orgName;
	
	private 	String      comType;
	

	public String getDeviceType() {
		return deviceType;
	}

	public void setDeviceType(String deviceType) {
		this.deviceType = deviceType;
	}

	public java.util.Date getUpdateTv() {
		return updateTv;
	}
	
    public void setUpdateTv(java.util.Date updateTv) {
		this.updateTv = updateTv;
	}
	
	public String getIpAddr() {
		return ipAddr;
	}

	public void setIpAddr(String ipAddr) {
		this.ipAddr = ipAddr;
	}
	
	public String getIpPort() {
		return ipPort;
	}

	public void setIpPort(String ipPort) {
		this.ipPort = ipPort;
	}
	
	
	public String getComStatus() {
		return comStatus;
	}

	public void setComStatus(String comStatus) {
		this.comStatus = comStatus;
	}


	public String getMeterSn() {
		return meterSn;
	}

	public void setMeterSn(String meterSn) {
		this.meterSn = meterSn;
	}
	
	public String getNetworkAddress() {
		if(StringUtils.isNotEmpty(ipAddr)&&StringUtils.isNotEmpty(ipPort)) {
			this.networkAddress = ipAddr+":"+ipPort;
		}else {
			this.networkAddress =  "";
		}
		return this.networkAddress;
	}
	
	public void setNetworkAddress(String networkAddress) {
		this.networkAddress = networkAddress;
	}

	public String getComType() {
		return comType;
	}

	public void setComType(String comType) {
		this.comType = comType;
	}

	public String getMeterComType() {
		return meterComType;
	}

	public void setMeterComType(String meterComType) {
		this.meterComType = meterComType;
	}

	public String getCommSn() {
		return commSn;
	}

	public void setCommSn(String commSn) {
		this.commSn = commSn;
	}

	public String getCommComType() {
		return commComType;
	}

	public void setCommComType(String commComType) {
		this.commComType = commComType;
	}

	public String getSimNum() {
		return simNum;
	}

	public void setSimNum(String simNum) {
		this.simNum = simNum;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}


}