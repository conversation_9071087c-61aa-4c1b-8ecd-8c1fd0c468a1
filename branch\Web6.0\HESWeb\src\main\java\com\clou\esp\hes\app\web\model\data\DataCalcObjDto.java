package com.clou.esp.hes.app.web.model.data;

import java.util.Map;

import com.google.common.collect.Maps;

/**
 * 
 * <AUTHOR>
 *
 */
public class DataCalcObjDto  {

	public DataCalcObjDto(){}
	
	private String[]  xInfo; 

	Map<String,Map<String,DataCalcObj>>  dataMap = Maps.newHashMap();

	public String[] getxInfo() {
		return xInfo;
	}

	public void setxInfo(String[] xInfo) {
		this.xInfo = xInfo;
	}

	public Map<String, Map<String, DataCalcObj>> getDataMap() {
		return dataMap;
	}

	public void setDataMap(Map<String, Map<String, DataCalcObj>> dataMap) {
		this.dataMap = dataMap;
	}

	public DataCalcObjDto(String[] xInfo, Map<String, Map<String, DataCalcObj>> dataMap) {
		super();
		this.xInfo = xInfo;
		this.dataMap = dataMap;
	}
	
	
	
}
