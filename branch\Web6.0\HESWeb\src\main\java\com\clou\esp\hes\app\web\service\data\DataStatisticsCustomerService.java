/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataStatisticsCustomer{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-11-20 06:36:35
 * 最后修改时间：
 * 
*********************************************************************************************************/

package com.clou.esp.hes.app.web.service.data;

import java.util.List;
import java.util.Map;

import com.clou.esp.hes.app.web.service.common.CommonService;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.clou.esp.hes.app.web.model.data.DataStatisticsCustomer;

public interface DataStatisticsCustomerService extends CommonService<DataStatisticsCustomer>{
	List<DataStatisticsCustomer> findDataStatisticsCustomerList(Map<String, Object> p);
	
	JqGridResponseTo      findDataStatReport(JqGridSearchTo jqGridSearchTo);
	List<DataStatisticsCustomer> findDataStatReportList(JqGridSearchTo jqGridSearchTo);
	
}