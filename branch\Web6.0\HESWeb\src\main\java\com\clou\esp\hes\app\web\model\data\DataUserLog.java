package com.clou.esp.hes.app.web.model.data;

import java.util.Date;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.itextpdf.text.pdf.PdfStructTreeController.returnType;
import com.power7000g.core.excel.Excel;

/**
 * 
 * <AUTHOR>
 *
 */
public class DataUserLog extends BaseEntity {

	private static final long serialVersionUID = 1L;
	
	public DataUserLog(){
	}
	
	@Excel(name = "Operation Time",width = 30)
	private Date tv;
	
	@Excel(name = "User", width = 30)
	private String name;
	
	private String userId;
	
	@Excel(name = "Log Type",width = 30)
	private String logType;
	
	@Excel(name = "Log Sub Type",width = 30)
	private String logSubType;
	
	@Excel(name = "Content",width = 60)
	private String detail;
	
	private String[] logTypes;
	private String[] userIds;
	private String[] logSubTypes;
	private Date startDate;
	private Date endDate;

	public String getShowContent() {
		return detail.replace("];[", "]<br/>[");
	}
	
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public Date getTv() {
		return tv;
	}

	public void setTv(Date tv) {
		this.tv = tv;
	}

	public String getLogType() {
		return logType;
	}

	public void setLogType(String logType) {
		this.logType = logType;
	}

	public String getLogSubType() {
		return logSubType;
	}

	public void setLogSubType(String logSubType) {
		this.logSubType = logSubType;
	}

	public String getDetail() {
		return detail;
	}

	public void setDetail(String detail) {
		this.detail = detail;
	}

	public String[] getUserIds() {
		return userIds;
	}

	public void setUserIds(String[] userIds) {
		this.userIds = userIds;
	}

	public String[] getLogSubTypes() {
		return logSubTypes;
	}

	public void setLogSubTypes(String[] logSubTypes) {
		this.logSubTypes = logSubTypes;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public String[] getLogTypes() {
		return logTypes;
	}

	public void setLogTypes(String[] logTypes) {
		this.logTypes = logTypes;
	}
	
	
}
