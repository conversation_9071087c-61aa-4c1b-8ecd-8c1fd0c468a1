$(document).ready(function() {
	$.jgrid.defaults.styleUI = "Bootstrap";
	var mydata = [{
		id: "1",
		invdate: "Clou CLK22 Upgrade to 1.02",invdates:"SN0001",
		name: "<PERSON><PERSON>",start:"11/20/2017 15:45:00",end:"12/20/2017 15:45:00",tax1: "1.40",tax2: "1.41",
		note: "01/20/2017 15:45:00",resaon: "280",resaons: "[Upload File], Communication failed.",status: "Done",status1: "Expired",resaon1:"5",resaon2:"0",act: "12/20/2017 15:45:00",act3: " 12/20/2017 15:00:00 ",
		amount: "CLK22",
		tax: "1.01",
		total: "1.02",
		totals: "01/23/2017 15:45:00"
	}, {
		id: "2",
		invdate: "Clou CLK22 Upgrade to 1.03",invdates:"SN0002",
		name: "<PERSON><PERSON>",start:"11/20/2017 15:45:00",end:"12/20/2017 15:45:00",tax1: "1.40",tax2: "1.41",
		note: "03/20/2017 13:45:00",resaon: "240",status: "Done",status1: "Done",resaon1:"0",resaon2:"0",act: "12/20/2017 15:45:00",act3: "  ",
		amount: "CLK22",
		tax: "1.02",
		total: "1.03",
		totals: "03/25/2017 13:45:00"
	}, {
		id: "3",
		invdate: "Clou CLK22 Upgrade to 1.30",invdates:"SN0003",
		name: "Clou",start:"11/20/2017 15:45:00",end:"12/20/2017 15:45:00",tax1: "1.40",tax2: "1.41",
		note: "06/20/2017 15:25:00",resaon: "290",status: "Done",status1: "Expired",resaons: "[Key error]",resaon1:"8",resaon2:"0",act: "12/20/2017 15:45:00",act3: " 12/20/2017 15:00:00  ",
		amount: "CLK22",
		tax: "1.02",
		total: "1.30",
		totals: "06/25/2017 18:45:00"
	}, {
		id: "4",
		invdate: "Clou CLK22 Upgrade to 1.40",invdates:"SN0004",
		name: "Clou",start:"11/20/2017 15:45:00",end:"12/20/2017 15:45:00",tax1: "1.40",tax2: "1.41",
		note: "08/20/2017 07:45:00",resaon: "233",status: "Done",status1: "Done",resaon1:"0",resaon2:"0",act: "12/20/2017 15:45:00",act3: "  ",
		amount: "CLK22",
		tax: "1.30",
		total: "1.40",
		totals: "08/25/2017 08:45:00"
	}, {
		id: "5",
		invdate: "Clou CLK22 Upgrade to 1.41",invdates:"SN0005",
		name: "Clou",start:"11/20/2017 15:45:00",end:"12/20/2017 15:45:00",tax1: "1.40",tax2: "1.41",
		note: "11/20/2017 09:15:00",resaon: "218",status: "Done",status1: "Done",resaon1:"4",resaon2:"0",act: "12/20/2017 15:45:00",act3: "  ",
		amount: "CLK22",
		tax: "1.40",
		total: "1.41",
		totals: "12/20/2017 09:45:00"
	}, {
		id: "6",
		invdate: "Clou CLK22 Upgrade to 1.30",invdates:"SN0006",
		name: "Clou",start:"11/20/2017 15:45:00",end:"12/20/2017 15:45:00",tax1: "1.40",tax2: "1.41",
		note: "11/20/2017 15:45:00",resaon: "247",status: "Done",status1: "Done",resaon1:"0",resaon2:"0",act: "12/20/2017 15:45:00",act3: "  ",
		amount: "CLK22",
		tax: "1.03",
		total: "1.02",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "7",
		invdate: "Clou CLK22 Upgrade to 1.30",invdates:"SN0007",
		name: "Clou",start:"11/20/2017 15:45:00",end:"12/20/2017 15:45:00",tax1: "1.40",tax2: "1.41",
		note: "11/20/2017 15:45:00",resaon: "254",status: "Done",resaons: "[Upload File], Communication failed.",status1: "Expired",resaon1:"8",resaon2:"0",act: "12/20/2017 15:45:00",act3: "12/20/2017 15:00:00   ",
		amount: "CLK22",
		tax: "1.01",
		total: "1.02",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "8",
		invdate: "Clou CLK22 Upgrade to 1.30",invdates:"SN0008",
		name: "Clou",start:"11/20/2017 15:45:00",end:"12/20/2017 15:45:00",tax1: "1.40",tax2: "1.41",
		note: "11/20/2017 15:45:00",resaon: "235",status: "Done",status1: "Done",resaon1:"0",resaon2:"0",act: "12/20/2017 15:45:00",act3: "  ",
		amount: "CLK22",
		tax: "1.02",
		total: "1.03",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "9",
		invdate: "Clou CLK22 Upgrade to 1.30",invdates:"SN0009",
		name: "Clou",start:"11/20/2017 15:45:00",end:"12/20/2017 15:45:00",tax1: "1.40",tax2: "1.41",
		note: "11/20/2017 15:45:00",resaon: "240",status: "Done",status1: "Done",resaon1:"0",resaon2:"0",act: "12/20/2017 15:45:00",act3: "  ",
		amount: "CLK22",
		tax: "1.03",
		total: "1.02",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "10",
		invdate: "Clou CLK22 Upgrade to 1.30",invdates:"SN0010",
		name: "Clou",start:"11/20/2017 15:45:00",end:"12/20/2017 15:45:00",tax1: "1.40",tax2: "1.41",
		note: "11/20/2017 15:45:00",resaon: "240",resaons: "[Upload File], Communication failed.",status: "Done",status1: "Expired",resaon1:"16",resaon2:"0",act: "12/20/2017 15:45:00",act3: "  12/20/2017 15:00:00 ",
		amount: "CLK22",
		tax: "1.03",
		total: "1.02",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "10",
		invdate: "Clou CLK22 Upgrade to 1.30",invdates:"SN0011",
		name: "Clou",start:"11/20/2017 15:45:00",end:"12/20/2017 15:45:00",tax1: "1.40",tax2: "1.41",
		note: "11/20/2017 15:45:00",resaon: "240",resaons: " ",status: "Done",status1: "Done",resaon1:"0",resaon2:"0",act: "12/20/2017 15:45:00",act3: "  ",
		amount: "CLK22",
		tax: "1.03",
		total: "1.02",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "10",
		invdate: "Clou CLK22 Upgrade to 1.30",invdates:"SN0012",
		name: "Clou",start:"11/20/2017 15:45:00",end:"12/20/2017 15:45:00",tax1: "1.40",tax2: "1.41",
		note: "11/20/2017 15:45:00",resaon: "240",status: "Done",status1: "Done",resaon1:"0",resaon2:"0",act: "12/20/2017 15:45:00",act3: "  ",
		amount: "CLK22",
		tax: "1.03",
		total: "1.02",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "10",
		invdate: "Clou CLK22 Upgrade to 1.30",invdates:"SN0013",
		name: "Clou",start:"11/20/2017 15:45:00",end:"12/20/2017 15:45:00",tax1: "1.40",tax2: "1.41",
		note: "11/20/2017 15:45:00",resaon: "240",status: "Done",status1: "Done",resaon1:"0",resaon2:"0",act: "12/20/2017 15:45:00",act3: "  ",
		amount: "CLK22",
		tax: "1.03",
		total: "1.02",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "10",
		invdate: "Clou CLK22 Upgrade to 1.30",invdates:"SN0014",
		name: "Clou",start:"11/20/2017 15:45:00",end:"12/20/2017 15:45:00",tax1: "1.40",tax2: "1.41",
		note: "11/20/2017 15:45:00",resaon: "240",status: "Done",status1: "Done",resaon1:"0",resaon2:"0",act: "12/20/2017 15:45:00",act3: "  ",
		amount: "CLK22",
		tax: "1.03",
		total: "1.02",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "10",
		invdate: "Clou CLK22 Upgrade to 1.30",invdates:"SN0015",
		name: "Clou",start:"11/20/2017 15:45:00",end:"12/20/2017 15:45:00",tax1: "1.40",tax2: "1.41",
		note: "11/20/2017 15:45:00",resaon: "240",status: "Done",status1: "Done",resaon1:"0",resaon2:"0",act: "12/20/2017 15:45:00",act3: "  ",
		amount: "CLK22",
		tax: "1.03",
		total: "1.02",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "10",
		invdate: "Clou CLK22 Upgrade to 1.30",invdates:"SN0016",
		name: "Clou",start:"11/20/2017 15:45:00",end:"12/20/2017 15:45:00",tax1: "1.40",tax2: "1.41",
		note: "11/20/2017 15:45:00",resaon: "240",status: "Done",status1: "Done",resaon1:"0",resaon2:"0",act: "12/20/2017 15:45:00",act3: "  ",
		amount: "CLK22",
		tax: "1.03",
		total: "1.02",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "10",
		invdate: "Clou CLK22 Upgrade to 1.30",invdates:"SN0017",
		name: "Clou",start:"11/20/2017 15:45:00",end:"12/20/2017 15:45:00",tax1: "1.40",tax2: "1.41",
		note: "11/20/2017 15:45:00",resaon: "240",status: "Done",status1: "Done",resaon1:"0",resaon2:"0",act: "12/20/2017 15:45:00",act3: "  ",
		amount: "CLK22",
		tax: "1.03",
		total: "1.02",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "10",
		invdate: "Clou CLK22 Upgrade to 1.30",invdates:"SN0018",
		name: "Clou",start:"11/20/2017 15:45:00",end:"12/20/2017 15:45:00",tax1: "1.40",tax2: "1.41",
		note: "11/20/2017 15:45:00",resaon: "240",status: "Done",status1: "Done",resaon1:"0",resaon2:"0",act: "12/20/2017 15:45:00",act3: "  ",
		amount: "CLK22",
		tax: "1.03",
		total: "1.02",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "10",
		invdate: "SN0019",
		name: "Clou",start:"11/20/2017 15:45:00",end:"12/20/2017 15:45:00",tax1: "1.40",tax2: "1.41",
		note: "11/20/2017 15:45:00",resaon: "240",status: "Done",status1: "Done",resaon1:"0",resaon2:"0",act: "12/20/2017 15:45:00",act3: "  ",
		amount: "CLK22",
		tax: "1.03",
		total: "1.02",
		totals: "12/20/2017 15:45:00"
	}, ];
	$("#table_list21").jqGrid({
		data: mydata,
		datatype: "local",
		autowidth: true,
		gridview: true,
		height: "110px",
		rownumbers: true,
		altRows: true, //单双行样式不同  
		altclass: 'differ',
		colNames: ["Action","","Planning Description","Manufacturer", "Model", "Current Version", "New Version", "Start Time ","Expiry Time","Status","Done","Expired","Running"],
		colModel: [
		{
			 name: "acts",
                index: "acts",
                editable: false,
                width: 45,
                sortable: false,
                search: false,
                align: "center",
                formatter: alarmFormatter,
                frozen: true,

		},{
			name: "id",
			index: "id",
			editable: true,
			width: 100,
			frozen: true,
			search: false,
			align: "right",
			hidden: true,

		}, {
			name: "invdate",
			index: "invdate",
			editable: true,
			width: 160,
		
			sortable: true,
		}, {
			name: "name",
			index: "name",
			editable: true,
			width: 80,
		}, {
			name: "amount",
			index: "amount",
			editable: true,
			width: 50,
			

			sorttype: "float",
		}, {
			name: "tax",
			index: "tax",
			editable: true,
			width: 110,
			sorttype: "float"
		}, {
			name: "total",
			index: "total",
			editable: true,
			width: 110,
			sorttype: "float",
			stype: "select"
		}, {
			name: "note",
			index: "note",
			editable: true,
			align: "right",
			width: 110,
			search: false,
			sortable: false
		},
		{
			name: "totals",
			index: "totals",
			editable: false,
			search: false,
			width: 110,
			sortable: false
		},{
			name: "status",
			index: "status",
			editable: false,
			search: false,
			width:40,
			sortable: false
		},{
			name: "resaon",
			index: "resaon",
			editable: false,
			search: false,
			width:40,
			sortable: false,
			align: "right",
		},
		{
			name: "resaon1",
			index: "resaon1",
			editable: false,
			search: false,
			width: 70,
			sortable: false,
			align: "right",
		},
		{
			name: "resaon2",
			index: "resaon2",
			editable: false,
			search: false,
			width: 80,
			sortable: false,
			align: "right",
		}
	
		],
		viewrecords: true,
		caption: ' Plans  ',
		/*editurl: "/RowEditing",*/
		shrinkToFit: true,
		autoScroll: true
	});
	//Result=Failed 的时候改变字体的颜色为红色
  function addCellAttr(rowId, val, rawObject, cm, rdata) {
        if (rawObject.status1 == 'Expired') {
            return "style='color:red'";
        }
    }
  /*图标函数*/
    function alarmFormatter(cellvalue, options, rowdata) {
        return '<a onclick="role_layer_del()" title="delete" ><i class="fa fa-trash-o" style="font-size:16px"></i>  </a>';
    }
   
	$("#table_list21").jqGrid("navGrid", "#pager_list21", {
			edit: false,
			add: false,
			del: false,
			search: false
		},

		{
			height: 200,
			reloadAfterSubmit: true
		});
 

	
	
$("#table_list22").jqGrid({
		data: mydata,
		datatype: "local",
		autowidth: true,
		gridview: true,
		height: "300px",
		rowNum: 40,
        /*direction:"rtl",*/
		rownumbers: true,
		altRows: true, //单双行样式不同  
		altclass: 'differ',
		rowList: [10, 20, 40],
			colNames: ["Action","","Serial Number","Manufacturer", "Model", "Current Version", "New Version", "Start Time ","Expiry Time","Status","Reason","Last Execute Time"],
		colModel: [
		{
			 name: "acts",
                index: "acts",
                editable: false,
                width:60,
                sortable: false,
                search: false,
                align: "center",
                formatter: alarmFormatter,
                frozen: true,

		},{
			name: "id",
			index: "id",
			editable: true,
			width: 80,
			frozen: true,
			search: false,
			
			hidden: true,

		}, {
			name: "invdates",
			index: "invdates",
			editable: true,
			width: 100,
			sortable: true,
		}, {
			name: "name",
			index: "name",
			editable: true,
			width: 110,
		}, {
			name: "amount",
			index: "amount",
			editable: true,
			width: 70,
			sorttype: "float",
		}, {
			name: "tax1",
			index: "tax1",
			editable: true,
			width: 110,
			sorttype: "float"
		}, {
			name: "tax2",
			index: "tax2",
			editable: true,
			width: 110,
			sorttype: "float",
			stype: "select"
		}, {
			name: "start",
			index: "note",
			editable: true,
			align: "right",
			width: 140,
			search: false,
			sortable: false
		},
		{
			name: "end",
			index: "act",
			editable: false,
			search: false,
			width: 140,
			sortable: false
		},{
			name: "status1",
			index: "status1",
			editable: false,
			search: false,
			width: 80,
			sortable: false,
			sorttype: "float",
			stype: "select",
			cellattr: addCellAttr//Result=Failed 的时候改变字体的颜色为红色
		},{
			name: "resaons",
			index: "resaons",
			editable: false,
			search: false,
			width: 180,
			sortable: false
		},
		{
			name: "act3",
			index: "act3",
			editable: false,
			search: false,
			width: 180,
			sortable: false
		}
	
		],
		pager: "#pager_list22",
		viewrecords: true,
		caption: '  Jobs',
		/*editurl: "/RowEditing",*/
		shrinkToFit: true,
		autoScroll: true
	});
  /*图标函数*/
    function alarmFormatter(cellvalue, options, rowdata) {
        return '<a onclick="role_layer_del()" title="delete" ><i class="fa fa-trash-o"style="font-size:16px"></i>  </a>';
    }
   
	$("#table_list22").jqGrid("navGrid", "#pager_list22", {
			edit: false,
			add: false,
			del: false,
			search: false
		},

		{
			height: 200,
			reloadAfterSubmit: true
		});
		var width = $(".jqGrid_wrapper").width();
		$("#table_list21").setGridWidth(width);
		$("#table_list22").setGridWidth(width);
 	$(window).bind("resize", function() {
		var width = $(".jqGrid_wrapper").width();
		$("#table_list21").setGridWidth(width);
		var width = $(".jqGrid_wrapper").width();
		$("#table_list22").setGridWidth(width);
	});
	$(".ui-jqgrid-titlebar").append(" <div class=' titleBtnItem ' > <div class='btn_wrapper' title='Print'><div class= 'ui-title-btn'><span class='glyphicon glyphicon-print'></span></div></div> <div class='btn_wrapper' title='Export'><div class= 'ui-title-btn'><span class='glyphicon glyphicon-export'></span></div></div></div>");

});
