/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetCommunicatorFavorite{ } 
 * 
 * 摘    要： assetCommunicatorFavorite
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-03-09 07:39:35
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.asset;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;

import com.clou.esp.hes.app.web.model.asset.AssetCommunicatorFavorite;
import com.clou.esp.hes.app.web.service.asset.AssetCommunicatorFavoriteService;

/**
 * <AUTHOR>
 * @时间：2018-03-09 07:39:35
 * @描述：assetCommunicatorFavorite类
 */
@Controller
@RequestMapping("/assetCommunicatorFavoriteController")
public class AssetCommunicatorFavoriteController extends BaseController{

 	@Resource
    private AssetCommunicatorFavoriteService assetCommunicatorFavoriteService;

	/**
	 * 跳转到assetCommunicatorFavorite列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/asset/assetCommunicatorFavoriteList");
    }

	/**
	 * 跳转到assetCommunicatorFavorite新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "assetCommunicatorFavorite")
	public ModelAndView assetCommunicatorFavorite(AssetCommunicatorFavorite assetCommunicatorFavorite,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(assetCommunicatorFavorite.getId())){
			try {
                assetCommunicatorFavorite=assetCommunicatorFavoriteService.getEntity(assetCommunicatorFavorite.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("assetCommunicatorFavorite", assetCommunicatorFavorite);
		}
		return new ModelAndView("/asset/assetCommunicatorFavorite");
	}


	/**
	 * assetCommunicatorFavorite查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=assetCommunicatorFavoriteService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除assetCommunicatorFavorite信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(AssetCommunicatorFavorite assetCommunicatorFavorite,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(assetCommunicatorFavoriteService.deleteById(assetCommunicatorFavorite.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存assetCommunicatorFavorite信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })AssetCommunicatorFavorite assetCommunicatorFavorite,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        AssetCommunicatorFavorite t=new  AssetCommunicatorFavorite();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(assetCommunicatorFavorite.getId())){
        	t=assetCommunicatorFavoriteService.getEntity(assetCommunicatorFavorite.getId());
			MyBeanUtils.copyBeanNotNull2Bean(assetCommunicatorFavorite, t);
				assetCommunicatorFavoriteService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            assetCommunicatorFavoriteService.save(assetCommunicatorFavorite);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
	
	
	
}