/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetFavorite{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-12-03 01:08:17
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.asset;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.asset.AssetFavoriteDao;
import com.clou.esp.hes.app.web.model.asset.AssetFavorite;
import com.clou.esp.hes.app.web.service.asset.AssetFavoriteService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("assetFavoriteService")
public class AssetFavoriteServiceImpl  extends CommonServiceImpl<AssetFavorite>  implements AssetFavoriteService {

	@Resource
	private AssetFavoriteDao assetFavoriteDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(assetFavoriteDao);
    }
	@SuppressWarnings("rawtypes")
	public AssetFavoriteServiceImpl() {}
	
	
}