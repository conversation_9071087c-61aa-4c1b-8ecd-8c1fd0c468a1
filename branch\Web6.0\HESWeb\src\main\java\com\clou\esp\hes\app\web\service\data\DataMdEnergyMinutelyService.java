/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataMeterDataEnergyMinutely{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-24 03:20:08
 * 最后修改时间：
 * 
*********************************************************************************************************/

package com.clou.esp.hes.app.web.service.data;

import java.util.List;

import com.clou.esp.hes.app.web.service.common.CommonService;
import com.clou.esp.hes.app.web.model.data.DataIntegrityDetails;
import com.clou.esp.hes.app.web.model.data.DataMdEnergyMinutely;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

public interface DataMdEnergyMinutelyService extends CommonService<DataMdEnergyMinutely>{
	/**
	 * 抄读率详情分页
	 * @param jqGridSearchTo
	 * @return
	 */
	public JqGridResponseTo getIntegrityDetailsForJqGrid(JqGridSearchTo jqGridSearchTo);
	
	/**
	 * 根据对象获取抄读列表
	 * @param dataIntegrityDetails
	 * @return
	 */
	public List<DataIntegrityDetails> getDataIntegrityDetailList(DataIntegrityDetails dataIntegrityDetails);
	
}