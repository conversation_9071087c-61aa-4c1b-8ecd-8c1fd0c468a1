/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataMeterEvent{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-24 01:56:05
 * 最后修改时间：
 * 
 *********************************************************************************************************/

package com.clou.esp.hes.app.web.service.data;

import java.util.List;
import java.util.Map;

import com.clou.esp.hes.app.web.model.data.DataMeterEvent;
import com.clou.esp.hes.app.web.service.common.CommonService;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

public interface DataMeterEventService extends CommonService<DataMeterEvent> {

	public int batchSaves(String sql, String osql);

	public int batchInsert(List<DataMeterEvent> eList);
	
	public JqGridResponseTo getForJqGrid(JqGridSearchTo jqGridSearchTo);
	
	public Long getCountByOrgIds(Map<String, Object> params);
	
	public List<DataMeterEvent>   getTodayEventByDeviceId(String deviceId,String protocolId);

	public List<String> getTodayEventByDeviceIds(List<String> deviceIds);

}