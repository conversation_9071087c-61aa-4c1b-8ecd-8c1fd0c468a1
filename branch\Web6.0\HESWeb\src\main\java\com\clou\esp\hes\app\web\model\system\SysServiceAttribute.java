/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysServiceAttribute{ } 
 * 
 * 摘    要： sysServiceAttribute
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-03-29 03:17:56
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.system;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class SysServiceAttribute  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public SysServiceAttribute() {
	}

	/**attributeName*/
	private java.lang.String attributeName;
	/**attributeValue*/
	private java.lang.String attributeValue;

	/**
	 * attributeName
	 * @return the value of SYS_SERVICE_ATTRIBUTE.ATTRIBUTE_NAME
	 * @mbggenerated 2018-03-29 03:17:56
	 */
	public java.lang.String getAttributeName() {
		return attributeName;
	}

	/**
	 * attributeName
	 * @param attributeName the value for SYS_SERVICE_ATTRIBUTE.ATTRIBUTE_NAME
	 * @mbggenerated 2018-03-29 03:17:56
	 */
    	public void setAttributeName(java.lang.String attributeName) {
		this.attributeName = attributeName;
	}
	/**
	 * attributeValue
	 * @return the value of SYS_SERVICE_ATTRIBUTE.ATTRIBUTE_VALUE
	 * @mbggenerated 2018-03-29 03:17:56
	 */
	public java.lang.String getAttributeValue() {
		return attributeValue;
	}

	/**
	 * attributeValue
	 * @param attributeValue the value for SYS_SERVICE_ATTRIBUTE.ATTRIBUTE_VALUE
	 * @mbggenerated 2018-03-29 03:17:56
	 */
    	public void setAttributeValue(java.lang.String attributeValue) {
		this.attributeValue = attributeValue;
	}

	public SysServiceAttribute(java.lang.String attributeName 
	,java.lang.String attributeValue ) {
		super();
		this.attributeName = attributeName;
		this.attributeValue = attributeValue;
	}

}