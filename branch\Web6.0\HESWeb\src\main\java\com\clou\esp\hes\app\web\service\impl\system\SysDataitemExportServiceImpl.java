/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysDataitemExport{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-04-04 06:11:28
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.system;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.system.SysDataitemExportDao;
import com.clou.esp.hes.app.web.model.system.SysDataitemExport;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.clou.esp.hes.app.web.service.system.SysDataitemExportService;

@Component
@Service("sysDataitemExportService")
public class SysDataitemExportServiceImpl  extends CommonServiceImpl<SysDataitemExport>  implements SysDataitemExportService {

	@Resource
	private SysDataitemExportDao sysDataitemExportDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(sysDataitemExportDao);
    }
	public SysDataitemExportServiceImpl() {}
	
	@Override
	public void deleteAllData() {
		sysDataitemExportDao.deleteAllData();
	}
	
}