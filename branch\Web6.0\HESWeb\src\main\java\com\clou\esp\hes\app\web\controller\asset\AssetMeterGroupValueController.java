/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeterGroupValue{ } 
 * 
 * 摘    要： assetMeterGroupValue
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-01-16 01:58:30
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.asset;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;

import com.clou.esp.hes.app.web.model.asset.AssetMeterGroupValue;
import com.clou.esp.hes.app.web.service.asset.AssetMeterGroupValueService;

/**
 * <AUTHOR>
 * @时间：2018-01-16 01:58:30
 * @描述：assetMeterGroupValue类
 */
@Controller
@RequestMapping("/assetMeterGroupValueController")
public class AssetMeterGroupValueController extends BaseController{

 	@Resource
    private AssetMeterGroupValueService assetMeterGroupValueService;

	/**
	 * 跳转到assetMeterGroupValue列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/asset/assetMeterGroupValueList");
    }

	/**
	 * 跳转到assetMeterGroupValue新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "assetMeterGroupValue")
	public ModelAndView assetMeterGroupValue(AssetMeterGroupValue assetMeterGroupValue,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(assetMeterGroupValue.getId())){
			try {
                assetMeterGroupValue=assetMeterGroupValueService.getEntity(assetMeterGroupValue.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("assetMeterGroupValue", assetMeterGroupValue);
		}
		return new ModelAndView("/asset/assetMeterGroupValue");
	}


	/**
	 * assetMeterGroupValue查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=assetMeterGroupValueService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除assetMeterGroupValue信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(AssetMeterGroupValue assetMeterGroupValue,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(assetMeterGroupValueService.deleteById(assetMeterGroupValue.getId())>0){
                j.setMsg("删除成功");
            }else{
                j.setSuccess(false);
                j.setMsg("删除失败");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
    
    /**
     * 保存assetMeterGroupValue信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })AssetMeterGroupValue assetMeterGroupValue,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        AssetMeterGroupValue t=new  AssetMeterGroupValue();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(assetMeterGroupValue.getId())){
        	t=assetMeterGroupValueService.getEntity(assetMeterGroupValue.getId());
			MyBeanUtils.copyBeanNotNull2Bean(assetMeterGroupValue, t);
				assetMeterGroupValueService.update(t);
				j.setMsg("修改成功");
				
			}else{
	            assetMeterGroupValueService.save(assetMeterGroupValue);
	            j.setMsg("创建成功");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
	
	
	
}