var select = false;
var calcObjId;
var calcObjName;

function queryCalcObject() {
	var entityType = $('#entityType option:selected').val();
	
	if('0' == entityType) {
		$('#entityName').html("");
		$('#entityName').append('<option selected="selected" value="0">'+i18n.t("calculationObjectList.all")+'</option>');$('#entityName').append('<option selected="selected" value="0">'+i18n.t("calculationObjectList.all")+'</option>');
		return;
	}
	
	layer.load();
	$.ajax({
		type : 'POST',
		url : getRootPathWeb() + '/assetCalationObjectController/queryCalcObject.do',
		data : {
			entityType : entityType
		},
		dataType : 'json',
		success : function(data) {
			if(!data.success){
				middleTipMsg(data.msg,data.success);
				layer.closeAll('loading');
				return;
			}
			
			$('#entityName').html("");
			var objList = data.obj;
			if(objList != null) {
				$('#entityName').append('<option selected="selected" value="0">'+i18n.t("calculationObjectList.all")+'</option>');
				for(var i in objList){
					$('#entityName').append('<option selected="selected" value="'+objList[i].id+'">'+objList[i].name+'</option>');
				}
				$('#entityName').find("option").eq(0).prop("selected",true)
			}
			
			layer.closeAll('loading');
		},
		error : function(msg) {
			window.parent.layer.msg(
					'<t:mutiLang lanKey="system.requestError" />', {
						icon : 2
					});
			layer.closeAll('loading');
		}
	});
}

function queryCalcDataFun(){
	var name = $("#name").val();
	var type = $("#type").val();
	var tvType = $("#cycle").val();
	var entityType = $("#entityType").val();
	var entityName = $("#entityName").val();
	//var entityId = $('#entityName option:selected').val();
		
	var data = {name:name,type:type,tvType:tvType,entityType:entityType,entityName:entityName,select:select};
	return data;
}

function queryCalcDataOnclick() {
	select = true;
	calculationObjectListsearchOnEnterFn();
	calcObjId = null; // 复位
	calcObjName = null;
}

function formatCalcCellValue(cellvalue,rdata,rowid){
	cellvalue = $('#entityName option:selected').text()
	return cellvalue;
}

function formatCalObjTypeCellValue(cellvalue){
	if(cellvalue == null || cellvalue == ""){
		return;
	}
	switch(cellvalue) {
	case 1:return i18n.t("calculationObjectList.lineLoss");    
	case 2:return i18n.t("calculationObjectList.import");
	case 3:return i18n.t("calculationObjectList.export");  
	}
}

function editCalcData(id){
	add('calculationObjectList','system.edit',getRootPathWeb() +'/assetCalationObjectController/assetCalcManagement.do?id='+id,'40%','420px',false);
}
	


function deleteCalcObj(id){
	var rowData = $("#calculationObjectList").jqGrid("getRowData", id);
	
	var close_link = window.parent.layer.confirm(i18n.t("system.aydeltherdate"), 
		{
			btn: [i18n.t("system.determine"), i18n.t("system.cancel")],
			title: i18n.t("system.delete")
		}, function() {
			window.parent.layer.close(close_link);
			layer.load();
			$.ajax({
				async : true,
	            cache : false,
	            traditional: true,
				type: 'POST',
				url: getRootPathWeb()+'/assetLineManagementController/delCalcObj.do?&id=' + rowData.id,
				dataType: 'json',
				success: function(data) {
					if(data.success) {
						$("#calculationObjectList").trigger('reloadGrid');	//刷新界面
						$("#calculationObjectMapList").trigger('reloadGrid');	//刷新界面
						middleTipMsg(data.msg,true);
					} else {
						window.parent.layer.msg(data.msg, {icon: 2});
					}
					layer.closeAll('loading');
				},
				error: function(msg) {
					window.parent.layer.msg(i18n.t("system.requestError"), {icon: 2});
					layer.closeAll('loading');
				}
			});
		});
}

function editCalcObjRowData(rowid,iRow,iCol,e){
	if(rowid == null || rowid == ""){
		alert("rowid is null");
		return;
	}
	var rowData = $("#calculationObjectList").jqGrid('getRowData',rowid);
	calcObjId = rowData.id;
	calcObjName=rowData.name;
	calculationObjectMapListsearchOnEnterFn();
	$("#gview_calculationObjectMapList .ui-jqgrid-title").text(rowData.name);
}

function queryCalcMapDataFun(){
	var objId = calcObjId;
//	if(null == objId || "" == objId){
//		return;
//	}
	
	var data = {objId:objId};
	return data;
}

function editCalcDataMap(id,meteringId,type,dataitemId){
	add('calculationObjectMapList','system.edit',getRootPathWeb() +'/assetLineManagementController/assetCalcMapManagement.do?id='+id +'&meteringId='+meteringId+'&type='+type+'&dataitemId='+dataitemId,'60%','60%',false);
}
	
function addCalcDataMap(){
	var id = calcObjId;
	if(id == null || "" == id) {
		middleTipMsg("lineManagementList.noCalObjFound",false);
		return;
	}
	add('calculationObjectMapList','system.add',getRootPathWeb() +'/assetLineManagementController/assetCalcMapManagement.do?id='+id,'40%','300px',false);
}


function addCalcMap(mapType){
	if(!calcObjName){
		middleTipMsg("lineManagementList.noCalObjFound",false);
		return;
	}
	
	var addTitle=i18n.t("calculationObjectList.addMeteringPoint")+"-"+calcObjName;
	 window.parent.layer.open({
			type : 2,
			title : addTitle,
			shadeClose : false,
			shade : [ 0.7 ],
			btn : [ 'OK', 'Cancel' ],
			maxmin : true, //开启最大化最小化按钮
			area : [ "70%","70%"],
			content : getRootPathWeb()+'/assetCalationObjectController/addCalcMap.do?mapType='+mapType+'&objId='+calcObjId,
			yes : function(index, layero) {
				var body = window.parent.layer.getChildFrame('body', index);
				var calcTable=$(body).find('#calcMapList');
				var rowIds=calcTable.jqGrid('getGridParam','selarrrow');//calcTable.jqGrid('getDataIDs');
				
				 var jsonArr = new Array(); 
	            for(var k=0; k<rowIds.length; k++) {
	            	calcTable.jqGrid('saveRow',rowIds[k]); 
	            	var dataRow=calcTable.jqGrid('getRowData',rowIds[k]);
	            	 jsonArr.push(dataRow);
	            }
	            
	            $.ajax({
	                type:"POST", //请求方式  
	                url:getRootPathWeb()+"/assetCalationObjectController/saveCalcObjMapBatch.do",
	                cache: false,     
	                data:{//传参  
	                    "objId": $(body).find('#objId').val(),
	                    "calcMaps":JSON.stringify(jsonArr)   
	                },
	                dataType: 'json',   //返回值类型 
	                success:function(result){        
	                	if (result.success) {
	                		window.parent.layer.msg(i18n.t("system.operSucce"), {icon : 1});
	                	}else{
	                		window.parent.layer.msg(i18n.t("system.systemException"), {icon : 2});
	                	}
	                	setTimeout("window.parent.layer.close(\""+index+"\");", 500);
	                	$("#refresh_calculationObjectMapList").click();
	                }  
	            })
		},
		cancel : function() {
			//右上角关闭回调
			//return false 开启该代码可禁止点击该按钮关闭
		}
	});
	
	//add('calculationObjectMapList',addStr+'-'+calcObjName,getRootPathWeb() +'/assetCalationObjectController/addCalcMap.do?mapType='+mapType+'&objId='+calcObjId,'700px','80%',false);
}

