package com.clou.esp.hes.app.web.service.demo;

import com.clou.esp.hes.app.web.model.demo.TestUser;
import com.clou.esp.hes.app.web.service.common.CommonService;

/**
 * 测试数据管理服务
 * 
 * <AUTHOR>
 * 
 */
public interface TestDataService extends CommonService<TestUser> {
	/**
	 * 创建通讯器和电表数据
	 * 
	 * @param meterModel
	 *            电表类型型号
	 * @param meterCount
	 *            电表数量
	 * @param communicatorModel
	 *            通讯器类型型号
	 * @param communicatorCount
	 *            通讯器数量
	 */
	public void createCommunicatorMeter(String meterModel, String mcommType,
			int meterCount, String communicatorModel, String ccommType,
			int communicatorCount);

	/**
	 * 为电表插入测试数据
	 */
	public void insertMeterTestData();

	/**
	 * 插入电表负荷曲线进度表数据和电表负荷曲线缺失数据流水
	 */
	public void insertMeterProgressMissData();

	/**
	 * 插入电表上报事件测试数据
	 */
	public void insertMeterEventTestData();

	/**
	 * 插入电表负荷曲线进度表数据和电表负荷曲线缺失数据流水
	 */
	public void saveMeterProgressMissData();
}
