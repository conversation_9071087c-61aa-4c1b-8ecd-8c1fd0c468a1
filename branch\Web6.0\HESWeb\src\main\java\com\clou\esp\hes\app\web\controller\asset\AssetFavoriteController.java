/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetFavorite{ } 
 * 
 * 摘    要： assetFavorite
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-12-03 01:08:17
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.asset;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;

import com.clou.esp.hes.app.web.model.asset.AssetFavorite;
import com.clou.esp.hes.app.web.service.asset.AssetFavoriteService;

/**
 * <AUTHOR>
 * @时间：2018-12-03 01:08:17
 * @描述：assetFavorite类
 */
@Controller
@RequestMapping("/assetFavoriteController")
public class AssetFavoriteController extends BaseController{

 	@Resource
    private AssetFavoriteService assetFavoriteService;

	/**
	 * 跳转到assetFavorite列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/asset/assetFavoriteList");
    }

	/**
	 * 跳转到assetFavorite新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "assetFavorite")
	public ModelAndView assetFavorite(AssetFavorite assetFavorite,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(assetFavorite.getId())){
			try {
                assetFavorite=assetFavoriteService.getEntity(assetFavorite.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("assetFavorite", assetFavorite);
		}
		return new ModelAndView("/asset/assetFavorite");
	}


	/**
	 * assetFavorite查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=assetFavoriteService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除assetFavorite信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(AssetFavorite assetFavorite,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(assetFavoriteService.deleteById(assetFavorite.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存assetFavorite信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })AssetFavorite assetFavorite,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        AssetFavorite t=new  AssetFavorite();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(assetFavorite.getId())){
        	t=assetFavoriteService.getEntity(assetFavorite.getId());
			MyBeanUtils.copyBeanNotNull2Bean(assetFavorite, t);
				assetFavoriteService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            assetFavoriteService.save(assetFavorite);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
	
	
	
}