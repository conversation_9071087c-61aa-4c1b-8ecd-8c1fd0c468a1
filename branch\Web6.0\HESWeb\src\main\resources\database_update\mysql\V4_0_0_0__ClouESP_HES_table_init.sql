create table DICT_COMMUNICATION_TYPE
(
  id           varchar(32) not null,
  name         varchar(32),
  introduction varchar(256),
  primary key (id)
);

create table DICT_MANUFACTURER
(
  id           VARCHAR(32) not null,
  name         <PERSON><PERSON>HA<PERSON>(64),
  introduction VARCHAR(256),
  primary key (id)
);

create table DICT_MENU
(
  id           VARCHAR(32) not null,
  utility_ids  VARCHAR(600),
  menulevel    DECIMAL(6,0),
  menuname     VARCHAR(50) not null,
  menuorder    DECIMAL(11,0),
  functionurl  VARCHAR(100),
  parentmenuid VARCHAR(32),
  menu_intro   VARCHAR(300),
  functionid   VARCHAR(32),
  hide_tab	   VARCHAR(64),
  primary key (ID)
);

create table DICT_PROTOCOL
(
  id           VARCHAR(32) not null,
  name         VARCHAR(64),
  introduction VARCHAR(256),
  primary key (id)
);

create table DICT_DEVICE_MODEL
(
  id               VARCHAR(32) not null,
  name             <PERSON><PERSON><PERSON><PERSON>(64),
  manufacturer_id  VARCHAR(32),
  protocol_id      VARCHAR(32),
  introduction     VARCHAR(256),
  device_type_icon VARCHAR(128),
  device_type      DECIMAL(4,0),
  primary key (id),
  foreign key (MANUFACTURER_ID) references DICT_MANUFACTURER (ID),
  foreign key (PROTOCOL_ID) references DICT_PROTOCOL (ID)
);

create table SYS_ORG
(
  id             VARCHAR(32) not null,
  utility_id     VARCHAR(32),
  name           VARCHAR(100) not null,
  description    VARCHAR(256),
  parent_org_tid VARCHAR(32),
  org_code       VARCHAR(64),
  org_type       VARCHAR(1),
  mobile         VARCHAR(32),
  fax            VARCHAR(32),
  address        VARCHAR(100),
  order_no       DECIMAL(4,0),
  primary key (id)
);

create table ASSET_COMMUNICATOR
(
  id           VARCHAR(32) not null,
  sn           VARCHAR(32) not null,
  name         VARCHAR(64),
  utility_id   VARCHAR(32) not null,
  org_id       VARCHAR(32) not null,
  model        VARCHAR(32),
  manufacturer VARCHAR(32),
  network_ip   VARCHAR(32),
  network_port DECIMAL(4,0),
  pass         VARCHAR(64),
  device_type  DECIMAL(11,0) ,
  mac          VARCHAR(32),
  com_type     VARCHAR(32),
  fw_version   VARCHAR(32),
  remove_flag  DECIMAL(4,0) default 0,
  primary key (id),
  foreign key (ORG_ID) references SYS_ORG (ID),
  foreign key (MANUFACTURER) references DICT_MANUFACTURER (ID),
  foreign key (MODEL) references DICT_DEVICE_MODEL (ID),
  foreign key (COM_TYPE) references DICT_COMMUNICATION_TYPE (ID)
);
create index ASSET_COMM_INDEX_MANU on ASSET_COMMUNICATOR (MANUFACTURER);
create unique index IDX_ASSET_COMMUNICATOR_SN on ASSET_COMMUNICATOR (SN);
create index IDX_ASSET_COMM_COM_TYPE on ASSET_COMMUNICATOR (COM_TYPE);
create index IDX_ASSET_COMM_MODEL on ASSET_COMMUNICATOR (MODEL);
create index IDX_ASSET_COMM_ORG on ASSET_COMMUNICATOR (ORG_ID);
create index IDX_ASSET_COMM_UTILITY on ASSET_COMMUNICATOR (UTILITY_ID);
create unique index ASSET_COMMUNICATOR_INDEX1 on ASSET_COMMUNICATOR (MAC);

create table ASSET_COMMUNICATOR_FAVORITE
(
  id      VARCHAR(32) not null,
  user_id VARCHAR(32) not null,
  primary key (ID, USER_ID)
);

create table ASSET_MEASUREMENT_PROFILE
(
  mg_id              VARCHAR(32) not null,
  profile_id         VARCHAR(32) not null,
  profile_cycle_type VARCHAR(32),
  profile_cycle      DECIMAL(10,0),
  protocol_code      VARCHAR(32),
  profile_type       DECIMAL(4,0),
  primary key (MG_ID, PROFILE_ID)
);

create table ASSET_MEASUREMENT_PROFILE_DI
(
  mg_id       VARCHAR(32) not null,
  profile_id  VARCHAR(32) not null,
  dataitem_id VARCHAR(64) not null,
  sort_id     DECIMAL(10,0),
  primary key (MG_ID, PROFILE_ID, DATAITEM_ID)
);

create table ASSET_METER
(
  id              VARCHAR(32) not null,
  sn              VARCHAR(32) not null,
  name            VARCHAR(64),
  utility_id      VARCHAR(32),
  org_id          VARCHAR(32) not null,
  communicator_id VARCHAR(32) not null,
  model           VARCHAR(32) not null,
  manufacturer    VARCHAR(32) not null,
  password        VARCHAR(64) default 00000001,
  hls_ak          VARCHAR(64) default 0000000000000000,
  hls_ek          VARCHAR(64) default 0000000000000000,
  index_dcu       DECIMAL(4,0) default 0,
  mac             VARCHAR(32),
  com_type        VARCHAR(32) not null,
  is_encrypt      VARCHAR(3) default 1,
  auth_type       VARCHAR(3) default 5,
  fw_version      VARCHAR(32),
  remove_flag     DECIMAL(4,0) default 0,
  primary key (id),
  foreign key (COMMUNICATOR_ID) references ASSET_COMMUNICATOR (ID),
  foreign key (COM_TYPE) references DICT_COMMUNICATION_TYPE (ID),
  foreign key (MANUFACTURER) references DICT_MANUFACTURER (ID),
  foreign key (MODEL) references DICT_DEVICE_MODEL (ID),
  foreign key (ORG_ID) references SYS_ORG (ID)
);
create index IDX_ASSET_MANUFACTURER on ASSET_METER (MANUFACTURER);
create index IDX_ASSET_METER_COM_TYPE on ASSET_METER (COM_TYPE);
create index IDX_ASSET_METER_ORG on ASSET_METER (ORG_ID);
create index IDX_ASSET_METER_SN on ASSET_METER (SN);
create index IDX_ASSET_METER_UTILITY_ID on ASSET_METER (UTILITY_ID);
create index IDX_ASSET_MODEL on ASSET_METER (MODEL);
create index IDX_COMMUNICATOR_ID on ASSET_METER (COMMUNICATOR_ID);
create unique index IDX_ASSET_METER_MAC on ASSET_METER (MAC);

create table ASSET_METER_FAVORITE
(
  id      VARCHAR(32) not null,
  user_id VARCHAR(32) not null,
  primary key (ID, USER_ID)
);

create table ASSET_METER_GROUP
(
  id           VARCHAR(32) not null,
  name         VARCHAR(64),
  protocol_id  VARCHAR(32),
  type         VARCHAR(32),
  introduction VARCHAR(256),
  primary key (ID)
);

CREATE TABLE ASSET_METER_GROUP_MAP 
(
  ID		VARCHAR(32) not null, 
  TYPE		DECIMAL(10,0) not null COMMENT '0:SCHEDULE; 1: MEASUREMENT; 2:TOU 3:LIMITER 4 STEP', 
  GROUP_ID	VARCHAR(32) not null,
  primary key (ID,TYPE)
);

create table ASSET_METER_GROUP_VALUE
(
  group_id    VARCHAR(32) not null,
  dataitem_id VARCHAR(64) not null,
  xml_value   longtext,
  primary key (GROUP_ID, DATAITEM_ID)
);

create table ASSET_ROUTER
(
  communicator_id VARCHAR(32) not null,
  channel_id      VARCHAR(32),
  schedule_id     VARCHAR(32),
  primary key (COMMUNICATOR_ID),
  foreign key (COMMUNICATOR_ID) references ASSET_COMMUNICATOR (ID)
);

create table ASSET_SCHEDULE_SCHEME
(
  id           VARCHAR(32) not null,
  name         VARCHAR(64),
  protocol_id  VARCHAR(32),
  introduction VARCHAR(256),
  primary key (ID)
);

create table ASSET_SCHEDULE_SCHEME_DETAIL
(
  id              VARCHAR(32) not null,
  task_id         VARCHAR(32) not null,
  profile_id      VARCHAR(128) not null,
  start_time      datetime(6),
  end_time        datetime(6),
  task_cycle_type VARCHAR(32),
  task_cycle      DECIMAL(10,0),
  task_type       DECIMAL(4,0) not null,
  primary key (ID, TASK_ID)
);

create table DATA_DI_EXPORT_PROGRESS
(
  dataitem_id VARCHAR(64) not null,
  tv          datetime(6) not null,
  export_tv   datetime(6) not null,
  export_result VARCHAR(20),
  export_reason VARCHAR(512),
  primary key (DATAITEM_ID)
);

create table DATA_FWU_JOB
(
  id             VARCHAR(32) not null,
  plan_id        VARCHAR(32) not null,
  device_id      VARCHAR(32),
  device_type    VARCHAR(32),
  current_vesion VARCHAR(128),
  new_version    VARCHAR(128),
  state          VARCHAR(32),
  last_exec_time datetime(6),
  failed_reason  VARCHAR(256),
  fwu_step       VARCHAR(128),
  block_size     DECIMAL(10,0),
  block_count    DECIMAL(10,0),
  fwu_progress   DECIMAL(4,0),
  primary key (ID)
);
create index IDX_DFJ_DEVICE_ID on DATA_FWU_JOB (DEVICE_ID);
create index SYS_C0010029 on DATA_FWU_JOB (PLAN_ID);

create table DATA_FWU_PLAN
(
  id               VARCHAR(32) not null,
  device_type      VARCHAR(32) not null,
  manufacturer_id  VARCHAR(32),
  device_model     VARCHAR(32),
  current_vesion   VARCHAR(256),
  introduction     VARCHAR(256),
  start_time       datetime(6),
  expiry_time      datetime(6),
  new_version      VARCHAR(256),
  file_path        VARCHAR(256),
  task_start_time  datetime(6),
  task_end_time    datetime(6),
  task_cycle       DECIMAL(4,0),
  operation_id     VARCHAR(32),
  state            VARCHAR(32),
  image_identifier VARCHAR(128),
  file_path_server VARCHAR(256),
  primary key (ID)
);

create table DATA_INTEGRATION_REQUEST
(
  id               VARCHAR(32) not null,
  tv               datetime(6) not null,
  from_id          VARCHAR(32) not null,
  request_type     VARCHAR(64) not null,
  request_sub_type VARCHAR(64),
  request_detail   VARCHAR(1024),
  response_detail  VARCHAR(1024),
  response_result  VARCHAR(64),
  primary key (ID, TV, FROM_ID, REQUEST_TYPE)
);
create index INDEX_DIR_ID on DATA_INTEGRATION_REQUEST (ID);
create index INDEX_DIR_TV on DATA_INTEGRATION_REQUEST (TV);
create index INDEX_DIR_TYPE on DATA_INTEGRATION_REQUEST (REQUEST_TYPE);

create table DATA_INTEGRITY
(
  id           VARCHAR(32) not null,
  id_type      VARCHAR(32) not null COMMENT '1: meter 3:  manufacturer 4: model 5: communication type' ,
  profile_id   VARCHAR(32) not null,
  tv           datetime(6) not null,
  integrity    DECIMAL(4,0) not null,
  count_actual DECIMAL(10,0),
  count_total  DECIMAL(10,0),
  tv_type      DECIMAL(10,0) not null,
  primary key (ID, ID_TYPE, PROFILE_ID, TV, TV_TYPE)
);
create index IDX_DATA_INTEGRITY_PROID on DATA_INTEGRITY (PROFILE_ID);
create index IDX_DATA_INTEGRITY_TV on DATA_INTEGRITY (TV);
create index IDX_DATA_INTEGRITY__ID on DATA_INTEGRITY (ID);

create table DATA_INTEGRITY_METER
(
  id           VARCHAR(32) not null,
  profile_id   VARCHAR(32) not null,
  tv           datetime(6) not null,
  integrity    DECIMAL(4,0),
  count_actual DECIMAL(4,0),
  count_total  DECIMAL(4,0),
  primary key (ID, PROFILE_ID, TV)
);
create index IDX_INTEGRITY_METER_PROID on DATA_INTEGRITY_METER (PROFILE_ID);
create index IDX_DATA_INTEGRITY_METER_TV on DATA_INTEGRITY_METER (TV);
create index IDX_DATA_INTEGRITY_METER__ID on DATA_INTEGRITY_METER (ID);

create table DATA_MD_DEMAND_MONTHLY
(
  device_id VARCHAR(32) not null,
  tv        datetime(6) not null,
  value1    VARCHAR(64),
  value2    VARCHAR(64),
  value3    VARCHAR(64),
  value4    VARCHAR(64),
  value5    VARCHAR(64),
  value6    VARCHAR(64),
  value7    VARCHAR(64),
  value8    VARCHAR(64),
  value9    VARCHAR(64),
  value10   VARCHAR(64),
  value11   VARCHAR(64),
  value12   VARCHAR(64),
  value13   VARCHAR(64),
  value14   VARCHAR(64),
  value15   VARCHAR(64),
  value16   VARCHAR(64),
  value17   VARCHAR(64),
  value18   VARCHAR(64),
  value19   VARCHAR(64),
  value20   VARCHAR(64),
  value21   VARCHAR(64),
  value22   VARCHAR(64),
  value23   VARCHAR(64),
  value24   VARCHAR(64),
  value25   VARCHAR(64),
  value26   VARCHAR(64),
  value27   VARCHAR(64),
  value28   VARCHAR(64),
  value29   VARCHAR(64),
  value30   VARCHAR(64),
  value31   VARCHAR(64),
  value32   VARCHAR(64),
  value33   VARCHAR(64),
  value34   VARCHAR(64),
  value35   VARCHAR(64),
  value36   VARCHAR(64),
  value37   VARCHAR(64),
  value38   VARCHAR(64),
  value39   VARCHAR(64),
  value40   VARCHAR(64),
  update_tv datetime(6),
  primary key (DEVICE_ID, TV)
);
create index IDX_MD_DEMAND_M_UPDATETV on DATA_MD_DEMAND_MONTHLY (UPDATE_TV);

create table DATA_MD_BILLING_DAILY
(
  device_id VARCHAR(32) not null,
  tv        datetime(6) not null,
  value1    VARCHAR(64),
  value2    VARCHAR(64),
  value3    VARCHAR(64),
  value4    VARCHAR(64),
  value5    VARCHAR(64),
  value6    VARCHAR(64),
  value7    VARCHAR(64),
  value8    VARCHAR(64),
  update_tv datetime(6),
  primary key (DEVICE_ID, TV)
);
create index IDX_MD_BILLING_D_UPDATETV on DATA_MD_BILLING_DAILY (UPDATE_TV);

create table DATA_MD_ENERGY_MONTHLY
(
  device_id VARCHAR(32) not null,
  tv        datetime(6) not null,
  value1    VARCHAR(64),
  value2    VARCHAR(64),
  value3    VARCHAR(64),
  value4    VARCHAR(64),
  value5    VARCHAR(64),
  value6    VARCHAR(64),
  value7    VARCHAR(64),
  value8    VARCHAR(64),
  value9    VARCHAR(64),
  value10   VARCHAR(64),
  value11   VARCHAR(64),
  value12   VARCHAR(64),
  value13   VARCHAR(64),
  value14   VARCHAR(64),
  value15   VARCHAR(64),
  value16   VARCHAR(64),
  value17   VARCHAR(64),
  value18   VARCHAR(64),
  value19   VARCHAR(64),
  value20   VARCHAR(64),
  value21   VARCHAR(64),
  value22   VARCHAR(64),
  value23   VARCHAR(64),
  value24   VARCHAR(64),
  value25   VARCHAR(64),
  value26   VARCHAR(64),
  value27   VARCHAR(64),
  value28   VARCHAR(64),
  value29   VARCHAR(64),
  value30   VARCHAR(64),
  value31   VARCHAR(64),
  value32   VARCHAR(64),
  value33   VARCHAR(64),
  value34   VARCHAR(64),
  value35   VARCHAR(64),
  value36   VARCHAR(64),
  value37   VARCHAR(64),
  value38   VARCHAR(64),
  value39   VARCHAR(64),
  value40   VARCHAR(64),
  update_tv datetime(6),
  primary key (DEVICE_ID, TV)
);
create index IDX_MD_ENERGY_M_UPDATETV on DATA_MD_ENERGY_MONTHLY (UPDATE_TV);

create table DATA_MD_PROFILE_DAILY
(
  device_id VARCHAR(32) not null,
  tv        datetime(6) not null,
  value1    VARCHAR(64),
  value2    VARCHAR(64),
  value3    VARCHAR(64),
  value4    VARCHAR(64),
  value5    VARCHAR(64),
  value6    VARCHAR(64),
  value7    VARCHAR(64),
  value8    VARCHAR(64),
  value9    VARCHAR(64),
  value10   VARCHAR(64),
  value11   VARCHAR(64),
  value12   VARCHAR(64),
  value13   VARCHAR(64),
  value14   VARCHAR(64),
  value15   VARCHAR(64),
  value16   VARCHAR(64),
  value17   VARCHAR(64),
  value18   VARCHAR(64),
  value19   VARCHAR(64),
  value20   VARCHAR(64),
  value21   VARCHAR(64),
  value22   VARCHAR(64),
  value23   VARCHAR(64),
  value24   VARCHAR(64),
  value25   VARCHAR(64),
  value26   VARCHAR(64),
  value27   VARCHAR(64),
  value28   VARCHAR(64),
  value29   VARCHAR(64),
  value30   VARCHAR(64),
  value31   VARCHAR(64),
  value32   VARCHAR(64),
  value33   VARCHAR(64),
  value34   VARCHAR(64),
  value35   VARCHAR(64),
  value36   VARCHAR(64),
  value37   VARCHAR(64),
  value38   VARCHAR(64),
  value39   VARCHAR(64),
  value40   VARCHAR(64),
  update_tv datetime(6),
  primary key (DEVICE_ID, TV)
);
create index IDX_MD_PROFILE_D_UPDATETV on DATA_MD_PROFILE_DAILY (UPDATE_TV);

create table DATA_MD_PROFILE_MINUTELY
(
  device_id VARCHAR(32) not null,
  tv        datetime(6) not null,
  value1    VARCHAR(64),
  value2    VARCHAR(64),
  value3    VARCHAR(64),
  value4    VARCHAR(64),
  value5    VARCHAR(64),
  value6    VARCHAR(64),
  value7    VARCHAR(64),
  value8    VARCHAR(64),
  value9    VARCHAR(64),
  value10   VARCHAR(64),
  update_tv datetime(6),
  value11   VARCHAR(64),
  value12   VARCHAR(64),
  value13   VARCHAR(64),
  value14   VARCHAR(64),
  primary key (DEVICE_ID, TV)
);
create index IDX_MD_PROFILE_MIN_UPDATETV on DATA_MD_PROFILE_MINUTELY (UPDATE_TV);

create table DATA_METER_EVENT
(
  device_id    VARCHAR(32) not null,
  tv           datetime(6) not null,
  event_id     VARCHAR(64) not null,
  event_detail VARCHAR(256),
  update_tv    datetime(6),
  primary key (DEVICE_ID, TV, EVENT_ID)
);
create index DATA_METER_EVENT_INDEX1 on DATA_METER_EVENT (UPDATE_TV);

create table DATA_PARAMETER_JOB
(
  id             VARCHAR(32) not null,
  plan_id        VARCHAR(32),
  state          VARCHAR(32),
  last_exec_time datetime(6),
  failed_reason  VARCHAR(128),
  device_id		 VARCHAR(32), 
  primary key (ID)
);

create table DATA_PARAMETER_PLAN
(
  id              VARCHAR(32) not null,
  introduction    VARCHAR(64) not null,
  start_time      datetime(6) not null,
  end_time        datetime(6) not null,
  task_start_time datetime(6) not null,
  task_end_time   datetime(6) not null,
  group_type      DECIMAL(10,0) not null,
  group_id        VARCHAR(32) not null,
  operator_id     VARCHAR(32) not null,
  task_cycle      DECIMAL(10,0) not null,
  state           VARCHAR(32),
  expiry_time	  datetime(6) not null,
  primary key (ID)
);

create table DATA_SCHEDULE_MISS_DATA
(
  device_id  VARCHAR(32) not null,
  profile_id VARCHAR(32) not null,
  tv         datetime(6) not null,
  primary key (DEVICE_ID, PROFILE_ID, TV)
);

create table DATA_SCHEDULE_PROGRESS
(
  device_id    VARCHAR(32) not null,
  profile_id   VARCHAR(32) not null,
  tv           datetime(6) not null,
  last_task_tv datetime(6),
  task_state   VARCHAR(2),
  failed_info  VARCHAR(64),
  primary key (DEVICE_ID, PROFILE_ID)
);

create table DATA_TIME_SYNCHRONIZATION
(
  id            VARCHAR(32) not null,
  tv            datetime(6) not null,
  meter_tv      datetime(6),
  system_tv     datetime(6),
  syn_tv        datetime(6),
  syn_result    DECIMAL(10,0),
  failed_reason VARCHAR(128),
  primary key (ID, TV)
);

create table DATA_USER_LOG
(
  tv           datetime(6) not null,
  user_id      VARCHAR(32) not null,
  log_type     VARCHAR(64) not null,
  log_sub_type VARCHAR(64) not null,
  detail       VARCHAR(256)
);
create index DATA_USER_LOG_INDEX1 on DATA_USER_LOG (TV);
create index DATA_USER_LOG_INDEX2 on DATA_USER_LOG (USER_ID);
create index DATA_USER_LOG_INDEX3 on DATA_USER_LOG (LOG_TYPE);
create index DATA_USER_LOG_INDEX4 on DATA_USER_LOG (LOG_SUB_TYPE);

create table DICT_DATAITEM
(
  id            VARCHAR(64) not null,
  protocol_id   VARCHAR(32) not null,
  name          VARCHAR(128),
  protocol_code VARCHAR(32),
  unit          VARCHAR(32),
  op_type       VARCHAR(16),
  show_unit       FLOAT,
  primary key (ID, PROTOCOL_ID)
);

create table DICT_DATAITEM_GROUP
(
  id          VARCHAR(32) not null,
  name        VARCHAR(64),
  app_type    VARCHAR(32) comment '1:DATA;2:EVENT;3:ORD;4:PARAMETER',
  protocol_id VARCHAR(32) not null,
  sort_id     DECIMAL(4,0),
  primary key (ID, PROTOCOL_ID)
);

create table DICT_DATAITEM_GROUP_MAP
(
  group_id     VARCHAR(32) not null,
  dataitem_id  VARCHAR(64) not null,
  sort_id      DECIMAL(4,0),
  primary key (GROUP_ID, DATAITEM_ID)
);

create table DICT_DATAITEM_PARSE_DLMS
(
  code       VARCHAR(32) not null,
  parse_type VARCHAR(32),
  parse_len  DECIMAL(3,0) ,
  scale      DECIMAL(3,0) ,
  primary key (CODE)
);

create table DICT_DEVICE_TYPE
(
  id   DECIMAL(4,0) not null,
  name VARCHAR(64),
  primary key (ID)
);

create table DICT_FUNCTION
(
  id             VARCHAR(32) not null,
  functionname   VARCHAR(50) not null,
  functionurl    VARCHAR(100),
  function_intro VARCHAR(300),
  primary key (ID)
);

create table DICT_METER_DATA_STORAGE_TABLE
(
  id           VARCHAR(32) not null,
  name         VARCHAR(64),
  column_count DECIMAL(4,0),
  primary key (ID)
);

create table DICT_METER_DATA_STORAGE_INFO
(
  id          VARCHAR(64) not null,
  table_id    VARCHAR(32) not null,
  field_index DECIMAL(4,0) not null,
  primary key (ID),
  foreign key (TABLE_ID) references DICT_METER_DATA_STORAGE_TABLE (ID)
);

create table DICT_OPERATION
(
  id            VARCHAR(32) not null,
  operationname VARCHAR(50),
  function_id   VARCHAR(32),
  operationurl  VARCHAR(128),
  ishide        DECIMAL(4,0),
  description   VARCHAR(255),
  primary key (ID)
);
create index DICT_OPERATION_INDEX1 on DICT_OPERATION (FUNCTION_ID);

create table DICT_PROFILE
(
  id            VARCHAR(32) not null,
  name          VARCHAR(64),
  protocol_id   VARCHAR(32),
  protocol_code VARCHAR(32),
  profile_type  DECIMAL(4,0),
  primary key (ID)
);

create table DICT_PROFILE_DATA_ITEM
(
  profile_id  VARCHAR(32) not null,
  dataitem_id VARCHAR(64) not null,
  sort_id     DECIMAL(4,0),
  primary key (PROFILE_ID, DATAITEM_ID),
  foreign key (PROFILE_ID) references DICT_PROFILE (ID)
);

create table DICT_SERVICE_ATTRIBUTE
(
  service_type      DECIMAL(10,0)  not null,
  attribute_name    VARCHAR(64) not null,
  attribute_desc    VARCHAR(512) not null,
  attribute_type    VARCHAR(32) not null,
  attribute_default VARCHAR(128),
  attribute_min     VARCHAR(32),
  attribute_max     VARCHAR(32),
  opt               VARCHAR(256),
  sort_id           DECIMAL(4,0),
  primary key (SERVICE_TYPE, ATTRIBUTE_NAME)
);
create index DICT_SERVICE_ATTRIBUTE_INDEX1 on DICT_SERVICE_ATTRIBUTE (ATTRIBUTE_NAME);
create index PK_DICT_SERVICE_ATTRIBUTE on DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE);

create table DICT_USER_LOG
(
  log_type     VARCHAR(64),
  log_sub_type VARCHAR(200),
  sort_id      DECIMAL(4,0),
  primary key (log_type, log_sub_type)
);

create table SYS_DATAITEM_EXPORT
(
  dataitem_id   VARCHAR(64) not null,
  dataitem_type DECIMAL(4,0) not null,
  primary key (dataitem_id)
);

create table SYS_MENU
(
  id           VARCHAR(32) not null,
  utility_ids  VARCHAR(600),
  menulevel    DECIMAL(6,0),
  menuname     VARCHAR(50) not null,
  menuorder    DECIMAL(11,0) ,
  functionurl  VARCHAR(100),
  parentmenuid VARCHAR(32),
  menu_intro   VARCHAR(300),
  functionid   VARCHAR(32),
  primary key (ID)
);

create table SYS_ROLE
(
  id          VARCHAR(32) not null,
  utility_id  VARCHAR(32),
  name        VARCHAR(64) not null,
  description VARCHAR(256),
  primary key (ID)
);

create table SYS_ROLE_MENU
(
  role_id   VARCHAR(32) not null,
  menu_id   VARCHAR(32) not null,
  operation VARCHAR(1000),
  primary key (ROLE_ID, MENU_ID)
);

create table SYS_SERVER
(
  id           VARCHAR(32) not null,
  introduction VARCHAR(256),
  ip           VARCHAR(32),
  ha_state     DECIMAL(4,0),
  is_online    DECIMAL(4,0),
  primary key (ID)
);

create table SYS_SERVICE
(
  id           VARCHAR(32) not null,
  introduction VARCHAR(256) default '',
  host_id      VARCHAR(32) not null,
  service_type DECIMAL(4,0) not null,
  is_online    DECIMAL(4,0) default 1 not null,
  server_id    VARCHAR(32) not null,
  primary key (ID),
  foreign key (SERVER_ID) references SYS_SERVER (ID)
);

create table SYS_SERVICE_ATTRIBUTE
(
  id              VARCHAR(32) not null,
  attribute_name  VARCHAR(64) not null,
  attribute_value VARCHAR(64),
  primary key (ID,attribute_name)
);

create table SYS_USER
(
  id              VARCHAR(32) not null,
  utility_id      VARCHAR(32),
  org_id          VARCHAR(32),
  role_id         VARCHAR(32),
  name            VARCHAR(64) not null,
  username        VARCHAR(32) not null,
  password        VARCHAR(32) not null,
  email           VARCHAR(64),
  mobile_phone    VARCHAR(32),
  profile_file    VARCHAR(32),
  user_type       DECIMAL(4,0),
  signature       longblob,
  userkey         VARCHAR(200),
  delete_flag     DECIMAL(6,0),
  user_state      DECIMAL(4,0),
  last_login_time datetime(6),
  primary key (ID)
);

create table SYS_UTILITY
(
  id          VARCHAR(32) not null,
  state       DECIMAL(4,0),
  name        VARCHAR(64) not null,
  description VARCHAR(256),
  primary key (ID)
);

create table SYS_VERSION
(
  version_type   VARCHAR(32) not null,
  version_string VARCHAR(32),
  update_time    datetime(6),
  primary key (VERSION_TYPE)
);

/*-============================================-*/
/*----创建序列  --*/
/*============================================*/
drop table if exists sequence;  
CREATE TABLE sequence  
(  
	seq_name VARCHAR(50) NOT NULL,  
	min_value INT NOT NULL,  
	max_value INT NOT NULL,  
	current_val INT NOT NULL,  
	increment_val INT DEFAULT 1 NOT NULL,  
	PRIMARY KEY (seq_name)  
);  
