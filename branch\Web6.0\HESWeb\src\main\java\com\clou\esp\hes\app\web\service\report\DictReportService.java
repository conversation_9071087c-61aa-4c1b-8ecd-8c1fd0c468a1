/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictReport{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-05-22 04:14:25
 * 最后修改时间：
 * 
*********************************************************************************************************/

package com.clou.esp.hes.app.web.service.report;

import java.util.List;

import com.clou.esp.hes.app.web.model.data.DataCalcObj;
import com.clou.esp.hes.app.web.model.report.BillingReport;
import com.clou.esp.hes.app.web.model.report.DictReport;
import com.clou.esp.hes.app.web.model.report.LineLossDetailReport;
import com.clou.esp.hes.app.web.model.report.LineLossReport;
import com.clou.esp.hes.app.web.service.common.CommonService;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

public interface DictReportService extends CommonService<DictReport>{
	
	JqGridResponseTo findLineLossSingleJqGrid(JqGridSearchTo jqGridSearchTo);
//	JqGridResponseTo findLineLossOrganizationJqGrid(JqGridSearchTo jqGridSearchTo);
	List<LineLossReport> findLineLossSingleReport(JqGridSearchTo jqGridSearchTo);
	List<LineLossReport> findLineLossOrgReport(JqGridSearchTo jqGridSearchTo);
	List<BillingReport> findBillingOrgReport(JqGridSearchTo jqGridSearchTo);
	JqGridResponseTo findBillingOrganizationJqGrid(JqGridSearchTo jqGridSearchTo);
	List<BillingReport> findBillingOrganizationMobile(JqGridSearchTo jqGridSearchTo);
	JqGridResponseTo findLineLossDetailReport(JqGridSearchTo jqGridSearchTo);
	List<LineLossDetailReport> findLineLossDetailExcel(JqGridSearchTo jqGridSearchTo);
	List<DataCalcObj> getDataCalcObj(JqGridSearchTo jqGridSearchTo);
}