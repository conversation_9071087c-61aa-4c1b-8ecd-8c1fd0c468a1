/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictMeterDataStorageInfo{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.dict;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.dict.DictMeterDataStorageInfoDao;
import com.clou.esp.hes.app.web.model.dict.DictMeterDataStorageInfo;
import com.clou.esp.hes.app.web.service.dict.DictMeterDataStorageInfoService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@Component
@Service("dictMeterDataStorageInfoService")
public class DictMeterDataStorageInfoServiceImpl  extends CommonServiceImpl<DictMeterDataStorageInfo>  implements DictMeterDataStorageInfoService {

	@Resource
	private DictMeterDataStorageInfoDao dictMeterDataStorageInfoDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dictMeterDataStorageInfoDao);
    }
	@SuppressWarnings("rawtypes")
	public DictMeterDataStorageInfoServiceImpl() {}
	
	
	@Override
	public JqGridResponseTo unBindForJqGrid(JqGridSearchTo jqGridSearchTo) {
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		PageInfo<DictMeterDataStorageInfo> pageInfo = new PageInfo<DictMeterDataStorageInfo>(this.dictMeterDataStorageInfoDao.unBindForJqGrid(jqGridSearchTo));
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}
	
	@Override
	public Long getMaxColumnIndex(String tableId) {
		return this.dictMeterDataStorageInfoDao.getMaxColumnIndex(tableId);
	}
	
	@Override
	public void deleteByTableId(String tableId) {
		 this.dictMeterDataStorageInfoDao.deleteByTableId(tableId);
	}
	
	
}