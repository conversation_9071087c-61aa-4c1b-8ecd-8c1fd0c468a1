/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetVeeRule{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-12-20 04:22:02
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.vee;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.vee.AssetVeeRuleDao;
import com.clou.esp.hes.app.web.model.vee.AssetVeeRule;
import com.clou.esp.hes.app.web.service.vee.AssetVeeRuleService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("assetVeeRuleService")
public class AssetVeeRuleServiceImpl  extends CommonServiceImpl<AssetVeeRule>  implements AssetVeeRuleService {

	@Resource
	private AssetVeeRuleDao assetVeeRuleDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(assetVeeRuleDao);
    }
	@SuppressWarnings("rawtypes")
	public AssetVeeRuleServiceImpl() {}
	
	
}