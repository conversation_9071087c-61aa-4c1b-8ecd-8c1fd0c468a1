/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataScheduleMissData{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-12-01 06:03:45
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.data;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.data.DataScheduleMissDataDao;
import com.clou.esp.hes.app.web.model.asset.AssetCalcObj;
import com.clou.esp.hes.app.web.model.data.DataScheduleMissData;
import com.clou.esp.hes.app.web.service.data.DataScheduleMissDataService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@Component
@Service("dataScheduleMissDataService")
public class DataScheduleMissDataServiceImpl  extends CommonServiceImpl<DataScheduleMissData>  implements DataScheduleMissDataService {

	@Resource
	private DataScheduleMissDataDao dataScheduleMissDataDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dataScheduleMissDataDao);
    }
	@SuppressWarnings("rawtypes")
	public DataScheduleMissDataServiceImpl() {}
	@Override
	public List<DataScheduleMissData> getListMissDataTv(
			DataScheduleMissData dataScheduleMissData) {
		// TODO Auto-generated method stub
		return dataScheduleMissDataDao.getListMissDataTv(dataScheduleMissData);
	}
	@Override
	public JqGridResponseTo getForJqGrid1(JqGridSearchTo jqGridSearchTo) {
		// TODO Auto-generated method stub
		PageHelper
		.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		
		PageInfo<DataScheduleMissData> pageInfo = new PageInfo<DataScheduleMissData>(dataScheduleMissDataDao.getForJqGrid1(jqGridSearchTo));
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}
	@Override
	public List<DataScheduleMissData> getList1(
			DataScheduleMissData dataScheduleMissData) {
		// TODO Auto-generated method stub
		return dataScheduleMissDataDao.getList1(dataScheduleMissData);
	}
	@Override
	public List<DataScheduleMissData> getMissDataList(
			DataScheduleMissData dataScheduleMissData) {
		// TODO Auto-generated method stub
		return dataScheduleMissDataDao.getMissDataList(dataScheduleMissData);
	}
	
	
}