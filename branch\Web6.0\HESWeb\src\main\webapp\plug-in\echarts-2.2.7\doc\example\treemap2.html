<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="ECharts">
    <meta name="author" content="<EMAIL>">
    <title>ECharts · Example</title>

    <link rel="shortcut icon" href="../asset/ico/favicon.png">

    <link href="../asset/css/font-awesome.min.css" rel="stylesheet">
    <link href="../asset/css/bootstrap.css" rel="stylesheet">
    <link href="../asset/css/carousel.css" rel="stylesheet">
    <link href="../asset/css/echartsHome.css" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->

    <script src="./www/js/echarts.js"></script>
    <script src="../asset/js/codemirror.js"></script>
    <script src="../asset/js/javascript.js"></script>

    <link href="../asset/css/codemirror.css" rel="stylesheet">
    <link href="../asset/css/monokai.css" rel="stylesheet">
</head>

<body>
    <!-- Fixed navbar -->
    <div class="navbar navbar-default navbar-fixed-top" role="navigation" id="head"></div>

    <div class="container-fluid">
        <div class="row-fluid example">
            <div id="sidebar-code" class="col-md-4">
                <div class="well sidebar-nav">
                    <div class="nav-header"><a href="#" onclick="autoResize()" class="glyphicon glyphicon-resize-full" id ="icon-resize" ></a>option</div>
                    <textarea id="code" name="code">
option = {
    title : {
        text: '手机占有率',
        subtext: '虚构数据'
    },
    tooltip : {
        trigger: 'item',
        formatter: "{b}: {c}"
    },
    toolbox: {
        show : true,
        feature : {
            mark : {show: true},
            dataView : {show: true, readOnly: false},
            restore : {show: true},
            saveAsImage : {show: true}
        }
    },
    hoverable : true,
    series : [
        {
            name:'矩形图',
            type:'treemap',
            itemStyle: {
                normal: {
                    label: {
                        show: true,
                        formatter: "{b}: {c}",
                        textStyle: {
                            color: '#00ffdd',
                            fontFamily: 'Times New Roman",Georgia,Serif',
                            fontSize: 20,
                            fontStyle: 'italic',
                            fontWeight:  'bolder'
                        }
                    },
                    borderWidth: 1,
                    borderColor: '#000'
                },
                emphasis: {
                    label: {
                        show: true,
                        textStyle: {
                            color: '#0000ff',
                            fontFamily: 'Times New Roman",Georgia,Serif',
                            fontSize: 18,
                            fontStyle: 'normal',
                            fontWeight:  'bold'
                        }
                    },
                    color: '#cc99cc',
                    borderWidth: 3,
                    borderColor: '#996699'
                }
            },
            data:[
                {
                    name: '三星',
                    value: 6,
                    itemStyle: {
                        normal: {
                            label: {
                                show: true,
                                formatter : "{b}最多",
                                x: 60,
                                y: 65,
                                textStyle: {
                                    color: '#ccc',
                                    fontSize: 16
                                }
                            },
                            color: '#ccff99',
                            borderWidth: 1
                        },
                        emphasis: {
                            label: {
                                show: true,
                                formatter : "{b}-{c}",
                                x: 80,
                                y: 85,
                                textStyle: {
                                    color: 'red',
                                    fontSize: 18
                                }
                            },
                            color: '#cc9999',
                            borderWidth: 3,
                            borderColor: '#999999'
                        }
                    },
                    children: [
                        {
                            name: 'S4',
                            value: 6,
                            children: [
                                {
                                    name: '2012',
                                    value: 6
                                },
                                {
                                    name: '2013',
                                    value: 4
                                },
                                {
                                    name: '2014',
                                    value: 3
                                }
                            ]
                        },
                        {
                            name: 'note 3',
                            value: 6
                        },
                        {
                            name: 'S5',
                            value: 4
                        },
                        {
                            name: 'S6',
                            value: 3
                        }
                    ]
                },
                {
                    name: '小米',
                    value: 4,
                    itemStyle: {
                        normal: {
                            color: '#99ccff',
                        },
                        emphasis: {
                            label: {
                                show: false
                            }
                        }
                    }
                },
                {
                    name: '苹果',
                    value: 4,
                    itemStyle: {
                        normal: {
                            color: '#9999cc',
                        }
                    }
                },
                {
                    name: '魅族',
                    value: 3,
                    itemStyle: {
                        normal: {
                            color: '#99cccc',
                        }
                    }
                },
                {
                    name: '华为',
                    value: 2,
                    itemStyle: {
                        normal: {
                            color: '#ccffcc',
                        }
                    }
                },
                {
                    name: '联想',
                    value: 2,
                    itemStyle: {
                        normal: {
                            color: '#ccccff',
                        }
                    }
                },
                {
                    name: '中兴',
                    value: 1,
                    itemStyle: {
                        normal: {
                            label: {
                                show: true,
                                formatter: "{b}: {c}",
                            },
                            borderWidth: 3
                        },
                        emphasis: {
                            label: {
                                show: true
                            },
                            color: '#cc9999',
                            borderWidth: 3,
                            borderColor: '#999999'
                        }
                    }
                }
            ]
        }
    ]
};
                    </textarea>
              </div><!--/.well -->
            </div><!--/span-->
            <div id="graphic" class="col-md-8">
                <div id="main" class="main"></div>
                <div>
                    <button type="button" class="btn btn-sm btn-success" onclick="refresh(true)">刷 新</button>
                    <span class="text-primary">切换主题</span>
                    <select id="theme-select"></select>

                    <span id='wrong-message' style="color:red"></span>
                </div>
            </div><!--/span-->
        </div><!--/row-->
        
        </div><!--/.fluid-container-->

    <footer id="footer"></footer>
    <!-- Le javascript
    ================================================== -->
    <!-- Placed at the end of the document so the pages load faster -->
    <script src="../asset/js/jquery.min.js"></script>
    <script type="text/javascript" src="../asset/js/echartsHome.js"></script>
    <script src="../asset/js/bootstrap.min.js"></script>
    <script src="../asset/js/echartsExample.js"></script>
</body>
</html>
