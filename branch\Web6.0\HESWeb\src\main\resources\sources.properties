# 
# Properties file for EventSource objects to be instantiated.
#
# Place this file in the CLASSPATH (e.g. WEB-INF/classes) or directly under WEB-INF.
#
# $Id: sources.properties,v 1.2 2007/11/10 14:12:16 justb Exp $
#
# Each EventSource is defined as <key>=<classname>
# 1. <key> should be unique within this file but may be any name
# 2. <classname> is the full class name
#
#
# Define Pull Sources here. These classes must be derived from
# nl.justobjects.pushlet.core.EventPullSource
# Inner classes are separated with a $ sign from the outer class. 
source1=nl.justobjects.pushlet.test.TestEventPullSources$TemperatureEventPullSource
source2=nl.justobjects.pushlet.test.TestEventPullSources$SystemStatusEventPullSource
source3=nl.justobjects.pushlet.test.TestEventPullSources$PushletStatusEventPullSource
source4=nl.justobjects.pushlet.test.TestEventPullSources$AEXStocksEventPullSource
source5=nl.justobjects.pushlet.test.TestEventPullSources$WebPresentationEventPullSource
source6=nl.justobjects.pushlet.test.TestEventPullSources$PingEventPullSource
#source7=com.power7000.web.pushlet.PushletData$HwPushlet

# TO BE DONE IN NEXT VERSION
# define Push Sources here. These must implement the interface
# nl.justobjects.pushlet.core.EventSource