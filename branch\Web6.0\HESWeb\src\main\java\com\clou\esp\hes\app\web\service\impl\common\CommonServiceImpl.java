package com.clou.esp.hes.app.web.service.impl.common;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.clou.esp.hes.app.web.model.common.IdEntity;
import com.clou.esp.hes.app.web.model.data.DataUserLog;
import com.clou.esp.hes.app.web.service.common.CommonService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.uuid.UUIDGenerator;

/**
 * 
 * <AUTHOR>
 * @param <T>
 * 
 */
public abstract class CommonServiceImpl<T extends BaseEntity> implements
		CommonService<T> {

	public CrudDao<T> crudDao;

	public void setCommonService(CrudDao<T> crudDao) {
		this.crudDao = crudDao;
	}

	@Override
	public Serializable save(T entity) {
		if (StringUtil.isEmpty(entity.getId())) {
			entity.setId(UUIDGenerator.generate());
		}
		crudDao.insert(entity);
		return entity.getId();
	}

	public Long getCount(T entity) {
		return crudDao.getCount(entity);
	}

	@Override
	public void batchSave(List<T> entitys) {
		for (T entity : entitys) {
			crudDao.insert(entity);
		}
	}

	@Override
	public Integer delete(T entity) {
		return crudDao.delete(entity);
	}

	@Override
	public Integer deleteById(String id) {
		return crudDao.deleteById(id);
	}

	@Override
	public void deleteAllEntitie(Collection<T> entities) {
		for (T entity : entities) {
			crudDao.delete(entity);
		}
	}

	@Override
	public Integer update(T entity) {
		return crudDao.update(entity);
	}

	@Override
	public void saveOrUpdate(T entity) {
		if (crudDao.get(entity) != null) {
			crudDao.update(entity);
		} else {
			crudDao.insert(entity);
		}
	}

	@Override
	public void batchUpdate(List<T> entitys) {
		for (T entity : entitys) {
			crudDao.update(entity);
		}
	}

	@Override
	public void batchSaveOrUpdate(List<T> entitys) {
		for (T entity : entitys) {
			if (crudDao.get(entity) != null) {
				crudDao.update(entity);
			} else {
				crudDao.insert(entity);
			}
		}
	}

	@Override
	public T get(T entity) {
		T t = crudDao.get(entity);
	//	return MutiLangUtil.assemblyI18nData(t);
		return t;
	}

	@Override
	public T getEntity(String id) {
		T t = crudDao.getEntity(id);
	//	return MutiLangUtil.assemblyI18nData(t);
		return t;
	}

	@Override
	public List<T> getList(T pojo) {
		List<T> list = crudDao.getList(pojo);
		//list = MutiLangUtil.assemblyI18nData(list);
		return list;
	}
	@Override
	public List<T> getAllList(){
		
		List<T> list = crudDao.getAllList();
	//	list = MutiLangUtil.assemblyI18nData(list);
		return list;
	}

	

	@Override
	public JqGridResponseTo getForJqGrid(JqGridSearchTo jqGridSearchTo) {	
		PageHelper
				.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		List<T> list = crudDao.getForJqGrid(jqGridSearchTo);
//		list = MutiLangUtil.assemblyI18nData(list);
		PageInfo<T> pageInfo = new PageInfo<T>(list);
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}
	
	@Override
	public void insertDataUserLog(String userId, String logType, String logSubType, String detail){
		DataUserLog log = new DataUserLog();
		log.setTv(new Date());
		log.setUserId(userId);
		log.setLogType(logType);
		log.setLogSubType(logSubType);
		log.setDetail(detail);
		crudDao.insertDataUserLog(log);
	}
}
