package com.clou.esp.hes.app.web.controller.report;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.clou.esp.hes.app.web.core.util.AssetSelectTools;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.dict.DictMeterDataStorageInfo;
import com.clou.esp.hes.app.web.model.dict.DictMeterDataStorageTable;
import com.clou.esp.hes.app.web.model.report.EnergyDataDailyReport;
import com.clou.esp.hes.app.web.service.asset.AssetCommunicatorService;
import com.clou.esp.hes.app.web.service.asset.AssetCustomerService;
import com.clou.esp.hes.app.web.service.asset.AssetEntityRelationshipService;
import com.clou.esp.hes.app.web.service.asset.AssetLineManagementService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.asset.AssetTransformerService;
import com.clou.esp.hes.app.web.service.dict.DictMeterDataStorageInfoService;
import com.clou.esp.hes.app.web.service.dict.DictMeterDataStorageTableService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
/**
 * <AUTHOR>
 * @since 2017年2月7日
 */
public class EneryDataDailyReportBean {
	@Resource
	private AssetMeterService 				assetMeterService;
	@Resource
	private AssetCommunicatorService 				assetCommunicatorService;
	@Resource
	private DictMeterDataStorageInfoService  dictMeterDataStorageInfoService;
	@Resource
	private DictMeterDataStorageTableService  dictMeterDataStorageTableService;
	@Resource
	private AssetEntityRelationshipService  assetEntityRelationshipService;
	@Resource
	private AssetCustomerService  assetCustomerService;
	@Resource
	private AssetLineManagementService  assetLineService;
	@Resource
	private AssetTransformerService  assetTransformerService;
	
//	orgIds="+orgId+"&time="+startTime+"&assetType="+assetType+"&sn="+sn+"
    public List<Map<String,Object>> loadReportData(String dsName,String datasetName,Map<String,Object> parameters){
    	
    	List<Map<String,Object>> listAll = new ArrayList();
    	String orgIds = parameters.get("orgIds").toString();
    	String time = parameters.get("time").toString();
    	String assetType = parameters.get("assetType").toString();
    	String sn = parameters.get("sn").toString();
    	List<AssetMeter> meters = AssetSelectTools.getAssetByType(assetMeterService,assetCommunicatorService,assetEntityRelationshipService,assetCustomerService,assetLineService,assetTransformerService,orgIds, assetType, sn);
        if(meters == null && meters.size() == 0){
        	return listAll;
        }
    	String[] profileDaily = new String[]{"*********.********.0.0.0.0.*********.72.0"
    			,"*********.*********.0.0.0.0.*********.72.0"
    			,"*********.********.0.0.0.0.*********.73.0"
    			,"*********.*********.0.0.0.0.*********.73.0"};
    	
    	String[] intervalDaily = new String[]{"********.********.0.0.0.0.*********.72.0"
    			,"********.*********.0.0.0.0.*********.72.0"
    			,"********.********.0.0.0.0.*********.73.0"
    			,"********.*********.0.0.0.0.*********.73.0"};
    	
    	 Map<String,Object> params = new HashMap();
    	 List<String> meterIds = Lists.newArrayList();
		 Map<String,AssetMeter> meterMap = Maps.newHashMap();
		 if(StringUtil.isNotEmpty(meters)){
			 for(AssetMeter assetMeter : meters){
				meterIds.add(assetMeter.getId());
				meterMap.put(assetMeter.getId(), assetMeter);
			 }
			params.put("deviceIds", meterIds);
		 }
		 
		 String pattern ="MM/dd/yyyy";
	 	 SimpleDateFormat sdf = new SimpleDateFormat(pattern);
	
    	 String tableName = "";
         if(profileDaily.length>0){
         	DictMeterDataStorageInfo dictMeterDataStorageInfo =dictMeterDataStorageInfoService.getEntity(profileDaily[0]);
         	if(dictMeterDataStorageInfo==null){
         	//	return j;
         	}
         	DictMeterDataStorageTable dictMeterDataStorageTable=dictMeterDataStorageTableService.getEntity(dictMeterDataStorageInfo.getTableId());
         	if(dictMeterDataStorageTable==null){
         		//return j;
         	}
         	tableName=dictMeterDataStorageTable.getName();
         }
         Map<String,Object> dsm=new HashMap<String, Object>();
         dsm.put("ids", profileDaily);
         DictMeterDataStorageInfo dmd=new DictMeterDataStorageInfo();
         dmd.setExtData(dsm);
         List<DictMeterDataStorageInfo> dmdList= dictMeterDataStorageInfoService.getList(dmd);
    	
         String viewField="";
         String viewFieldMysql="";
         for(int i=0;i<dmdList.size();i++){
         	
         		if(profileDaily[0].equals(dmdList.get(i).getId())){
         			viewField+=",rtrim(to_char(a.VALUE"+dmdList.get(i).getFieldIndex()+",'FM999999990.9999'),'.') as activeEnergyImport";
             		viewFieldMysql+=",cast(a.VALUE"+dmdList.get(i).getFieldIndex()+" as decimal(10,3)) as activeEnergyImport";
         		}else if(profileDaily[1].equals(dmdList.get(i).getId())){
         			viewField+=",rtrim(to_char(a.VALUE"+dmdList.get(i).getFieldIndex()+",'FM999999990.9999'),'.') as activeEnergyExport";
             		viewFieldMysql+=",cast(a.VALUE"+dmdList.get(i).getFieldIndex()+" as decimal(10,3)) as activeEnergyImport";
         		}else if(profileDaily[2].equals(dmdList.get(i).getId())){
         			viewField+=",rtrim(to_char(a.VALUE"+dmdList.get(i).getFieldIndex()+",'FM999999990.9999'),'.') as reactiveEnergyImport";
             		viewFieldMysql+=",cast(a.VALUE"+dmdList.get(i).getFieldIndex()+" as decimal(10,3)) as reactiveEnergyImport";
         		}else if(profileDaily[3].equals(dmdList.get(i).getId())){
         			viewField+=",rtrim(to_char(a.VALUE"+dmdList.get(i).getFieldIndex()+",'FM999999990.9999'),'.') as reactiveEnergyExport";
             		viewFieldMysql+=",cast(a.VALUE"+dmdList.get(i).getFieldIndex()+" as decimal(10,3)) as reactiveEnergyExport";
         		}	
         }

         params.put("tableName", tableName.toUpperCase()); 
         params.put("viewField", viewField);
         params.put("viewFieldMysql", viewFieldMysql);
         try {
			params.put("times_start", sdf.parse(time));
		} catch (ParseException e2) {
			// TODO Auto-generated catch block
			e2.printStackTrace();
		}
		
         List<Map<String,Object>> list =  dictMeterDataStorageTableService.getEnergyDataDailyByMaps(params);
         String viewField1="";
         String viewFieldMysql1="";
         for(int i=0;i<dmdList.size();i++){
          	
      		if(profileDaily[0].equals(dmdList.get(i).getId())){
      			viewField1+=",rtrim(to_char(a.VALUE"+dmdList.get(i).getFieldIndex()+",'FM999999990.9999'),'.') as activeEnergyImport1";
          		viewFieldMysql1+=",cast(a.VALUE"+dmdList.get(i).getFieldIndex()+" as decimal(10,3)) as activeEnergyImport1";
      		}else if(profileDaily[1].equals(dmdList.get(i).getId())){
      			viewField1+=",rtrim(to_char(a.VALUE"+dmdList.get(i).getFieldIndex()+",'FM999999990.9999'),'.') as activeEnergyExport1";
          		viewFieldMysql1+=",cast(a.VALUE"+dmdList.get(i).getFieldIndex()+" as decimal(10,3)) as activeEnergyImport1";
      		}else if(profileDaily[2].equals(dmdList.get(i).getId())){
				viewField1+=",rtrim(to_char(a.VALUE"+dmdList.get(i).getFieldIndex()+",'FM999999990.9999'),'.') as reactiveEnergyImport1";
				viewFieldMysql1+=",cast(a.VALUE"+dmdList.get(i).getFieldIndex()+" as decimal(10,3)) as reactiveEnergyImport1";
			}else if(profileDaily[3].equals(dmdList.get(i).getId())){
				viewField1+=",rtrim(to_char(a.VALUE"+dmdList.get(i).getFieldIndex()+",'FM999999990.9999'),'.') as reactiveEnergyExport1";
				viewFieldMysql1+=",cast(a.VALUE"+dmdList.get(i).getFieldIndex()+" as decimal(10,3)) as reactiveEnergyExport1";
			}	
         }
         
         params.put("tableName", tableName.toUpperCase()); 
         params.put("viewField", viewField1);
         params.put("viewFieldMysql", viewFieldMysql1);
     	 try {
			params.put("times_start",DateUtils.parseDate(DateUtils.formatAddDate(time, pattern, 1), pattern));
		} catch (ParseException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}


         List<Map<String,Object>> list1 =  dictMeterDataStorageTableService.getEnergyDataDailyByMaps(params);
         
   
         String tableName2 = "";
         if(intervalDaily.length>0){
         	DictMeterDataStorageInfo dictMeterDataStorageInfo =dictMeterDataStorageInfoService.getEntity(intervalDaily[0]);
         	if(dictMeterDataStorageInfo==null){
         	//	return j;
         	}
         	DictMeterDataStorageTable dictMeterDataStorageTable=dictMeterDataStorageTableService.getEntity(dictMeterDataStorageInfo.getTableId());
         	if(dictMeterDataStorageTable==null){
         		//return j;
         	}
         	tableName2=dictMeterDataStorageTable.getName();
         }
         Map<String,Object> dsm1=new HashMap<String, Object>();
         dsm1.put("ids", intervalDaily);
         DictMeterDataStorageInfo dmd1=new DictMeterDataStorageInfo();
         dmd1.setExtData(dsm1);
         List<DictMeterDataStorageInfo> dmdList1= dictMeterDataStorageInfoService.getList(dmd1);
    	
         String viewField2="";
         String viewFieldMysql2="";
         for(int i=0;i<dmdList1.size();i++){
         	
         		if(intervalDaily[0].equals(dmdList1.get(i).getId())){
         			viewField2+=",rtrim(to_char(a.VALUE"+dmdList1.get(i).getFieldIndex()+",'FM999999990.9999'),'.') as activeEnergyImport2";
             		viewFieldMysql2+=",cast(a.VALUE"+dmdList1.get(i).getFieldIndex()+" as decimal(10,3)) as activeEnergyImport2";
         		}else if(intervalDaily[1].equals(dmdList1.get(i).getId())){
         			viewField2+=",rtrim(to_char(a.VALUE"+dmdList1.get(i).getFieldIndex()+",'FM999999990.9999'),'.') as activeEnergyExport2";
             		viewFieldMysql2+=",cast(a.VALUE"+dmdList1.get(i).getFieldIndex()+" as decimal(10,3)) as activeEnergyImport2";
         		}else if(intervalDaily[2].equals(dmdList1.get(i).getId())){
					viewField2+=",rtrim(to_char(a.VALUE"+dmdList1.get(i).getFieldIndex()+",'FM999999990.9999'),'.') as reactiveEnergyImport2";
					viewFieldMysql2+=",cast(a.VALUE"+dmdList1.get(i).getFieldIndex()+" as decimal(10,3)) as reactiveEnergyImport2";
         		}else if(intervalDaily[3].equals(dmdList1.get(i).getId())){
					viewField2+=",rtrim(to_char(a.VALUE"+dmdList1.get(i).getFieldIndex()+",'FM999999990.9999'),'.') as reactiveEnergyExport2";
					viewFieldMysql2+=",cast(a.VALUE"+dmdList1.get(i).getFieldIndex()+" as decimal(10,3)) as reactiveEnergyExport2";
         		}	
         }
         params.put("tableName", tableName2.toUpperCase()); 
         params.put("viewField", viewField2);
         params.put("viewFieldMysql", viewFieldMysql2);
         try {
			params.put("times_start", sdf.parse(time));
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

         List<Map<String,Object>> list2 =  dictMeterDataStorageTableService.getEnergyDataDailyByMaps(params);
 	
        System.out.println(meters.size());
        List<EnergyDataDailyReport> energyDataDailyReportLists = new ArrayList();
        if(list !=null && list.size() >0){

    		int i = 0;
        	for(Map<String, Object> map  : list){

        		String id = (String)map.get("ID");
        		AssetMeter assetMeter = meterMap.get(id);
        		if(assetMeter != null){
        			EnergyDataDailyReport energyDataDailyReport = new EnergyDataDailyReport();
        			energyDataDailyReport.setId(id);
        			energyDataDailyReport.setMeterSn(assetMeter.getSn());
        			energyDataDailyReport.setPt(assetMeter.getPt());
        			energyDataDailyReport.setCt(assetMeter.getCt());
        			energyDataDailyReport.setIndex(++i);
        			String activeEnergyImport = (String)map.get("ACTIVEENERGYIMPORT");
        	
        			energyDataDailyReport.setDataName("Active energy import");
        			energyDataDailyReport.setStartCode(activeEnergyImport);
        			
        			energyDataDailyReportLists.add(energyDataDailyReport);
        			
        			
        			EnergyDataDailyReport energyDataDailyReport1 = new EnergyDataDailyReport();
        			energyDataDailyReport1.setId(id);
        			energyDataDailyReport1.setMeterSn(assetMeter.getSn());
        			energyDataDailyReport1.setPt(assetMeter.getPt());
        			energyDataDailyReport1.setCt(assetMeter.getCt());
        			energyDataDailyReport1.setIndex(++i);
        			String activeEnergyExport = (String)map.get("ACTIVEENERGYEXPORT");
        	
        			energyDataDailyReport1.setDataName("Active energy export");
        			energyDataDailyReport1.setStartCode(activeEnergyExport);
        			energyDataDailyReportLists.add(energyDataDailyReport1);
        			
        			EnergyDataDailyReport energyDataDailyReport2 = new EnergyDataDailyReport();
        			energyDataDailyReport2.setId(id);
        			energyDataDailyReport2.setMeterSn(assetMeter.getSn());
        			energyDataDailyReport2.setPt(assetMeter.getPt());
        			energyDataDailyReport2.setCt(assetMeter.getCt());
        			energyDataDailyReport2.setIndex(++i);
        			String reactiveEnergyImport = (String)map.get("REACTIVEENERGYIMPORT");
        	
        			energyDataDailyReport2.setDataName("Reactive energy import");
        			energyDataDailyReport2.setStartCode(reactiveEnergyImport);
        			energyDataDailyReportLists.add(energyDataDailyReport2);
        			
        			
        			EnergyDataDailyReport energyDataDailyReport3 = new EnergyDataDailyReport();
        			energyDataDailyReport3.setId(id);
        			energyDataDailyReport3.setMeterSn(assetMeter.getSn());
        			energyDataDailyReport3.setPt(assetMeter.getPt());
        			energyDataDailyReport3.setCt(assetMeter.getCt());
        			energyDataDailyReport3.setIndex(++i);
        			String reactiveEnergyExport = (String)map.get("REACTIVEENERGYEXPORT");
        	
        			energyDataDailyReport3.setDataName("Reactive energy export");
        			energyDataDailyReport3.setStartCode(reactiveEnergyExport);
        			energyDataDailyReportLists.add(energyDataDailyReport3);

        		}

        	}
        }
        
        if(energyDataDailyReportLists !=null && energyDataDailyReportLists.size() >0){
        	Map<String,String> idMaps= new HashMap();
        	for(EnergyDataDailyReport energyDataDailyReport : energyDataDailyReportLists){
        		String id = energyDataDailyReport.getId();
        		if(list1 !=null && list1.size() >0){
    	        	for(Map<String, Object> map  : list1){
    	        		String mapId = (String)map.get("ID");
    	        		   
    	        		if(idMaps.get(mapId+energyDataDailyReport.getDataName()+"1") == null){
    	        			if(mapId.equals(id)){
    	        				if(energyDataDailyReport.getDataName().equals("Active energy import")){
    	        					String activeEnergyImport = (String)map.get("ACTIVEENERGYIMPORT1");
    	        					energyDataDailyReport.setEndCode(activeEnergyImport);
    	        				}else if(energyDataDailyReport.getDataName().equals("Active energy export")){
    	        					String activeEnergyExport = (String)map.get("ACTIVEENERGYEXPORT1");
    	        					energyDataDailyReport.setEndCode(activeEnergyExport);
    	        				}else if(energyDataDailyReport.getDataName().equals("Reactive energy import")){
    	        					String reactiveEnergyImport = (String)map.get("REACTIVEENERGYIMPORT1");
    	        					energyDataDailyReport.setEndCode(reactiveEnergyImport);
    	        				}else if(energyDataDailyReport.getDataName().equals("Reactive energy export")){
    	        					String reactiveEnergyExport = (String)map.get("REACTIVEENERGYEXPORT1");
    	        					energyDataDailyReport.setEndCode(reactiveEnergyExport);
    	        				}
    	        				
        	        			idMaps.put(id+energyDataDailyReport.getDataName()+"1", "1");
        	        			break;
        	        		}
    	        		}
    	        		
    	        		
    	        	}
        		}
        		
        		if(list2 !=null && list2.size() >0){
    	        	for(Map<String, Object> map  : list2){
    	        		String mapId = (String)map.get("ID");

    	        		if(idMaps.get(mapId+energyDataDailyReport.getDataName()+"2") == null){
		        			if(mapId.equals(id)){
		        				if(energyDataDailyReport.getDataName().equals("Active energy import")){
		        					String activeEnergyImport = (String)map.get("ACTIVEENERGYIMPORT2");
		        					energyDataDailyReport.setElectricQuantity(activeEnergyImport);
		        				}else if(energyDataDailyReport.getDataName().equals("Active energy export")){
									String activeEnergyExport = (String)map.get("ACTIVEENERGYEXPORT2");
									energyDataDailyReport.setElectricQuantity(activeEnergyExport);
								}else if(energyDataDailyReport.getDataName().equals("Reactive energy import")){
									String reactiveEnergyImport = (String)map.get("REACTIVEENERGYIMPORT2");
									energyDataDailyReport.setElectricQuantity(reactiveEnergyImport);
								}else if(energyDataDailyReport.getDataName().equals("Reactive energy export")){
									String reactiveEnergyExport = (String)map.get("REACTIVEENERGYEXPORT2");
									energyDataDailyReport.setElectricQuantity(reactiveEnergyExport);
								}
				        			idMaps.put(id+energyDataDailyReport.getDataName()+"2", "1");
				        			break;
			        			}
    	        		}
	
    	        	}
        		}
        	}
        		
        	}
       

        if(energyDataDailyReportLists !=null && energyDataDailyReportLists.size() >0){
            for(EnergyDataDailyReport energyDataDailyReport : energyDataDailyReportLists){
            	Map<String ,Object> m= new HashMap();
            	m.put("index", energyDataDailyReport.getIndex());
            	m.put("meterSn", energyDataDailyReport.getMeterSn());
            	m.put("pt", energyDataDailyReport.getPt());
            	m.put("ct", energyDataDailyReport.getCt());
            	m.put("dataName", energyDataDailyReport.getDataName());
            	m.put("startCode", energyDataDailyReport.getStartCode());
            	m.put("endCode", energyDataDailyReport.getEndCode());
            	m.put("electricQuantity", energyDataDailyReport.getElectricQuantity());
            	listAll.add(m);
            }
        	
        }
    	return listAll;
    }
    public List<Map<String,Object>> buildReport(String dsName,String datasetName,Map<String,Object> parameters){
        return null;
    }
}