{"login": {"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pswd": "Пароль", "usernameNull": "Заполните логин!", "pswdNull": "Введите пароль!", "remeberMe": "Запомнить меня", "login": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loginOnOtherPlace": "Вы зашли из нового места, пожалуйста, войдите снова!", "accountDisabled": "Учетная запись отключена,Вы вынуждены выйти!", "world": "<PERSON><PERSON><PERSON>", "America": "Америка", "Nigeria": "Нигерия", "Australia": "Австралия", "ACP": "Американская облачная платформа", "notExistUser": "Такого аккаунта нет!", "LockedUser": "Логин заблокирован!", "errorPwd": "Неверный пароль!", "disabledUser": "Логин отключен!", "errorUser": "Исключение логина!", "loginFirst": "Пожалуйста, войдите сначала!", "logoutFailed": "Не удалось выйти из системы, попробуйте снова.!", "logout": "Выйти", "tenantIsStop": "Аккаунт заблокирован!", "accLgedElsewhere": "Вы зашли в другое место!", "userDontExist": "Такого аккаунта нет!"}, "system": {"systemName": "ClouESP", "systemVersion": "V5.0", "copyRight": "Copyright © 2018 SHENZHEN CLOU. All rights reserved.", "determine": "OK", "cancel": "Отмена", "notifications": "Уведомления", "information": "Информация", "message": "Сообщение", "aydeltherdate": "Вы действительно хотите удалить эту запись?", "submit": "OK", "pseleData": "Выберите данные!", "pleaseInterNum": "Введите номер!", "reqexc": "Аномальный запрос!", "delete": "Удалить", "add": "Добавить", "edit": "Редактировать", "inquiryTitle": "Уведомления", "delSuccess": "Удаление завершено!", "delFail": "Не удалось удалить", "abnoOpera": "Аномальная операция!", "addSucc": "Добавление завершено!", "updateSucc": "Обновление завершено!", "saveSucc": "Сохранение завершено!", "saveFail": "Не удалось сохранить!", "cancelSucc": "Отмена завершена!", "cancelFail": "Не удалось отменить!", "requestError": "Ошибка запроса!", "operSucce": "Операция завершена!", "nameExist": "Данный аккаунт уже зарегистрирован!", "systemException": "Ошибка системы!", "UCIException": "UCI Ошибка!", "selectNoData": "Не нашли данные!", "operation": "Операция", "export": "Экспорт", "print": "Распечатать", "start": "начало", "end": "Окончание", "validSucc": "Верификация прошла"}, "home": {"devicesStatistic": "Статистика устройств", "meters": "Счетчики", "concentrators": "Концентраторы", "selectiveCategory": "Категория выбора", "manufacturer": "Производитель", "model": "Модель", "communication": "Коммуникация", "name": " Имя", "number": "Номер", "rate": " Процент", "collectionIntegrity": "Процент целостности сбора", "completeRate": " Процент завершения", "completedNumber": "Количество завершения", "partialNumber": "Количество завершения частью", "failedNumber": "Количество незаверешения", "eventsStatistic": "Статистика событий", "eventsTotal": "Итог событий", "classifiedStatistcOfEvents": "Классифицированная статистика событий", "overallDaily": "Ежедневный процент целостности сбора (%)", "classifiedStatistcOfMeter": "Классифицированная статистика счетчиков", "supplyStaticOfSubOrgan": "Статистика энергоснабжения суборганизации", "supplyStatistic": "Статистика энергоснабжения", "salesStatisticOfCustomer": "Статистика энергопотребления", "organization": "Организация", "type": "Тип", "powerSupply": "Энергоснабжение(kWh)", "customerNumber": "Номер", "supplyStatisticDaily": "Статистика энергоснабжения в прошлые 7 дней(kWh)", "supplyStatistic15Daily": "Статистика энергоснабжения в прошлые 15 дней(kWh)", "supplyStatistic30Daily": "Статистика энергоснабжения в прошлые 30 дней(kWh)", "supplyStatistic6Month": "Статистика энергоснабжения в прошлые 6 месяцев(kWh)", "supplyStatistic12Month": "Статистика энергоснабжения в прошлые 12 месяцев(kWh)", "Last7days": "7", "Last15days": "15", "Last30days": "30", "supplyCount": "Статистика энергоснабжения суборганизации", "onlineMeter": "Счетчики в сети", "onlineCommunicator": "Коммуникаторы в сети", "onlineCommunicators": "Коммуникаторы в сети", "onlineConcentrators": "Концентраторы в сети", "yesterdayPowerSupply": "Энергоснабжение в предыдущий день(kWh)", "other": "Другие", "noData": "Нет данных", "Last6months": "6", "Last12months": "12"}, "error403": {"title": "Страница не найдена!", "content": "К сожалению, данная страница не авторизована~"}, "error404": {"title": "Страница не найдена!", "content": "К сожалению, данная страница может попасть в Марс~"}, "error500": {"title": "Ошибка внутреннего сервера!", "content": "Может быть ошибка с сервером..."}, "noPermissions": {"code": "10002", "title": "Нет доступа!", "content": "Свяжитесь с администратором для получения доступа..."}, "index": {"home": " Главная", "logout": "Выход", "search": "Поиск", "meter": "Счетчик", "commnuicator": "Концентратор", "serialNumber": "Серийный номер", "name": "Название", "mac": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model": "Модель", "searchFor": "Поиск...", "favorites": "Избранное", "myProfile": "Мой профиль", "passwordSetting": "Настройка пароля", "confCanFavorite": "Вы действительно хотите удалить это из избранных?", "advancedSearch": "Расширенный поиск", "frimwareVersion": "Версия прошивки", "measurementGroup": "Группа измерений", "collectionSchemeGroup": "Группа схемы сбора данных", "line": "Линия", "transformer": "Трансформатор", "manufacturer": "Производитель", "commuicationType": "Тип связи", "more": "Больше", "selected": "Выбранный", "pleaseChoose": "Выберите", "communicatorSn": "Коммуникатор SN", "organization": "Организация", "headAlarmTitle": "Отчет о событиях тревоги", "supplyStatisticsReport": "Отчет статистики энергоснабжения", "noAlarm": "Нет тревоги", "customer": "Потребитель", "telephoneNum": "Номер телефона", "tel": "Тел.", "voltageLevel": "Уровень напряжения"}, "connectOrDisconnect": {"function": "Функция", "system": "Система", "url": "URL", "connectDisconnect": "Включение / Выключение", "power7000": "Power7000"}, "scheduleReadsReport": {"title": "Отчет сбора по расписанию", "overview": "Общее", "manufacturer": "Производитель", "model": "Модель", "communication": "Коммуникация", "ydayInteRate": "Процент целостности сбора вчера", "ydayCompRate": "Процент завершения сбора вчера", "completed": "Завершено", "partial": "Завершено частью", "failed": "Не Завершено", "details": "Подробнее", "dMeterInteRate": "Ежедневный процент целостности сбора", "start": "Начало", "end": "Окончание", "mmeterInteRate": "Месячный процент целостности сбора", "dInteRateManu": "Ежедневный процент целостности сбора по производителю", "mInteRateManu": "Месячный процент целостности сбора по производителю", "dInteRateSMmanu": "Ежедневный процент целостности по выбранным производителям", "mInteRateSMmanu": "Месячный процент целостности по выбранным производителям", "dInteRateModels": "Ежедневный процент целостности по моделям", "mInteRateModels": "Месячный процент целостности по моделям", "dInteRateSMModel": "Ежедневный процент целостности по одному/нескольким моделям", "mInteRateSMModel": "Месячный процент целостности по одному/нескольким моделям", "dInteRateCommun": "Ежедневный процент целостности по концентраторам", "mInteRateCommun": "Месячный процент целостности по концентраторам", "dInteRateSMCommun": "Ежедневный процент целостности по одному/нескольким концентраторам", "mInteRateSMCommun": "Месячный процент целостности по одному/нескольким концентраторам", "dInteRateOrg": "Полный сбор за день", "mInteRateOrg": "Полная мощность лунного сбора", "dInteRateSMOrg": "Полный сбор по одному/несколько организационных организаций в день", "mInteRateSMOrg": "Полная собранность отдельных/нескольких организационных организаций в месяц", "dailyInteRate": "Ежедневный процент целостности ", "monthlyInteRate": "Месячный процент целостности ", "dmeterInteRadeio": "Ежедневный процент целостности опроса", "mmeterInteRadeio": "Месячный процент целостности опроса", "dInteRatioManu": "Ежедневный процент целостности опроса по производителям", "calcIntegrityRate": "Расчет процента целостности", "calcLineLoss": "расчет потери линии", "holdManual": "пересчитывается,подождите"}, "assetScheduleSchemeDetailList": {"title": "Список задач", "taskId": "Серийный номер", "id": "id", "taskType": "<PERSON>и<PERSON> задач", "profileId": "Название профиля", "taskCycleType": "Тип цикла задач", "taskCycle": "Цикл задач", "startTime": "Время начала", "endTime": "Время завершения", "pseleData": "Данная референтная схема и не может быть удалена!", "confirmDel": "Вы действительно хотите удалить данную задачу?", "addReadsTask": "Задача опроса данных", "addEvenTask": "Задача опроса событий", "addTimeTask": "Задача синхронизации времени", "collSchemeMage": "Управление схемой сбора", "daily": "Ежедневный", "hourly": "Часовой", "minutely": "Минутный", "monthly": "Месячный", "pleaseChoose": "---Выберите---", "successfullyModified": "Успешно изменено", "addedSuccessfully": "Успешно добавлено"}, "assetScheduleSchemeList": {"title": "Список схемы", "id": "Id", "name": "Название схемы", "meterStandard": "Стандарт счетчика", "meterNumber": "Количество счетчиков", "referenceScheme": "Референтная схема", "description": "Описание", "pseleData": "Выберите список данных схемы", "confirmDel": "Вы действительно хотите удалить данную схему?", "canBeDeleted": "Схема связанная с счетчиками не может быть удалено!", "protocolId": "Id протокол"}, "dataIntegrityList": {"headTitle": "Отслеживание пропущенных данных", "evenExportTitle": "Экспорт Данных & Событий Счетчика", "id": "Нет", "serizlNo": "SN счетчика", "tv": "Дата", "mfrId": "Производитель", "modelId": "Модель", "communicator": "SN Коцнетратора", "commId": "Связь", "progress": "Процесс", "lastTask": "Последняя задача", "taskResult": "Результат задачи", "failureCause": "Причина неудачи", "integrity": "Процент(%)", "integrityRate": "Процент целостности (%)", "analysis": "Референтный анализ", "export": "Экспорт", "print": "Распечатать", "taskResultFailed": "Не удалось", "taskResultSuccess": "Успешно", "all": "Все", "delayDay": "Задержка процесса (Days)", "progressDelayReport": "Отчет о задержке процесса", "missDataReport": "Отчет о неопрошенных счетчиках", "integrityRateReport": "Отчет о целостности", "updateTv": "Время обновления статуса", "comStatus": "Com статус"}, "dataIntegrityDetails": {"id": "Нет", "serizlNo": "SN", "tv": "Время", "manufacturer": "Производитель", "model": "Модель", "communication": "Связь", "profile": "Профиль", "readStatus": "Статус опроса"}, "dataMeterEventList": {"headTitle": "Отчет событий счетчика", "headAlarmTitle": "Отчет о событиях сигнализации", "deviceId": "Номер оборудования", "eventId": "Номер событий", "sn": "SN", "export": "Экспорт", "print": "Распечатать", "tv": "Время", "eventType": "Тип событий", "event": "Событие", "eventDetail": "Подробности событий"}, "dayList": {"id": "Номер ", "dayName": "Номер дня", "startTime": "Время начала", "rate": "Процент", "add": "Добавить", "addDay": "Добавить день", "hour": "<PERSON><PERSON><PERSON>", "minute": "Минута", "second": "Секунда", "delete": "Удалить", "title": "Дневный список", "pleaseSelectData": "Выберите день из списка!", "pleSetRateDayList": "Пожалуйста, настройте тариф в дневном списке!", "lastOneTimePeriod": "Дневные данные в последний период времени!", "areBeAtMost": "Не более 255 дней разрешено создавать!", "inteAllAtMost": "Не более 8 интервалов разрешено создавать в один день!"}, "device_list": {"headTitle": "Отчет событий счетчика", "pageTitle": "Конфигурация счетчика", "title": "Список оборудований", "id": "Номер", "sn": "SN счетчика", "modelName": "Модель", "communicator": "SN концентратора", "comType": "Связь", "set": "Настроить", "get": "Получить", "delete": "Удалить", "parameterType": "Тип пара<PERSON><PERSON><PERSON><PERSON><PERSON>", "operation": "Операция", "profiles": "Профили", "channels": "Каналы", "seleLeastOne": "Выберите не менее чем одни данные таблицы！", "leastOneChoose": "Выберите неменьше одной копии данных опроса", "communicatorList": "Список коммуникаторов"}, "device_read_parameter_list": {"title": "Результаты", "id": "Номер", "sn": "SN счетчика", "parameterType": "Тип пара<PERSON><PERSON><PERSON><PERSON><PERSON>", "parameterItem": "Параметры", "meterGroup": "Группа счетчиков", "requestTime": "Время опроса", "responseTime": "Время ответа", "status": "Статус", "reason": "Причина", "delete": "Удалить", "export": "Экспорт", "print": "Распечатать", "total": "Общее", "completed": "Завершено", "success": "Успешно", "failed": "Не удалось", "processing": "В процессе", "tariffType": "Тип тарифа", "active": "Активно", "passive": "Не активно", "touGroup": "TOU Группа", "limiterGroup": "Варианты пороговых значений", "pleAddTask": "Пожалуйста, добавьте задачу!", "areToExcTask": "Вы действительно хотите выполнить этот список задач?", "stepTariffGroup": "Группа ступенчатых тарифов"}, "linkedEntityRelationship": {"meterSN": "Счетчик SN", "logicalName": "Логическое наименование", "customerType": "Тип клиента", "organization": "Организация", "linkedMeter": "Связанный счетчик", "linkedMeterList": "Связанный список счетчиков", "linkedTransformer": "Связанный трансформатор", "linkedTransformerList": "Связанный список трансформаторов", "communicatorSN": "коммуникатор SN", "communicatorName": "наименование коммуникаторов", "transformerName": "Наименование трансформаторов", "transformerSN": "Трансформатор SN", "selectLeastLine": "Выберите линию.", "selectLeastTransformer": "Выберите линию трнсформа<PERSON>о<PERSON>ов."}, "deviceList": {"title": "Управление активами", "basicTitle": "Основная информация", "communication": "COM информация", "groupPro": "Групповая информация", "other": "Другие", "addGprsMeter": "Добавить GPRS счетчик", "addMeterCommun": "Записать счетчик в концентратор", "addCommunicatior": "Добавить концентратор", "id": "Номер", "deviceSN": "SN оборудование", "deviceType": "Тип оборудования", "name": "Наименование", "mac": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model": "Модель", "communicationType": "Ти<PERSON> концентратора", "manufacturer": "Производитель", "limiter": "Пороговое значение", "tou": "TOU", "stepTariff": "Ступенчатый тариф", "schemeGroup": "Группа схемы сбора данных", "meaGroup": "Группа измерения", "encrypt": "Шифрование", "ek": "EK", "ak": "AK", "llsPwd": "LLS Пароль", "authType": "Тип аутентификации", "port": "Порт", "ip": "IP", "organation": "Организация", "channel": "Каналы", "schedule": "Расписание", "firmVer": "Версия прошивки", "pleaseChoose": "---Выберите, пожалуйста---", "meterNullMsg": "Измеритель отсутсвует", "commnuicatorNullMsg": "Коцентратор отсутсвует", "pleSelDevice": "Выберите список оборудования!", "ipValidErrorMsg": "Формат Ip адреса неправильный ", "noDeviceFound": "Никакого оборудования не  нашло", "deviceNumExists": "Номер оборудования уже существует", "pleSeleCOmmun": "Выберите концентратор!", "pleSaveOrDel": "Сохраните или удалите данные!", "tCommunHChild": "Данная связь имеет дочерние данные！", "pleSelOrganation": "Выберите организацию!", "addr": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simNum": "SIM Num", "meterTitle": "Счетчик", "commTitle": "Коммуникатор", "meterInfo": "Счетчик", "commInfo": "Коммуникатор", "commSN": "Коммуникатор SN", "communicator": "Коммуникатор", "ct": "CT", "pt": "PT", "indexDcu": "Индекс В DCU", "comPort": "COM порт", "keyMeter": "Клавиатурный счетчик"}, "divFMUPlanMeterList": {"pleSelDevType": "Выберите тип оборудования!", "pleSelManufac": "Выберите производитель!", "pleEntPlanDesc": "Введите описание плана!", "pleSelPlanTime": "Выберите Время начала плана!", "pleSelPlanExpTime": "Выберите Время истечения срока плана!", "planExpPlanTime": "Время истечения срока должно быть после начала плана!", "pleEntNewVer": "Введите новую версию!", "pleEntImgeIden": "Введите идентификатор изображения!", "pleSelFirFile": "Выберите файл прошивки!", "pleSelTaskStartTime": "Выберите время запуска задачи!", "pleSelTaskEndTime": "Выберите время окончания задачи!", "taskStartLaterSTime": "Время начала задачи должно быть равным или позже завершения задачи!", "pleSelTaskCyc": "Выберите цикл задачи!", "confCreatePlan": "Вы действительно хотите создать этот план?", "pleSelModel": "Выберите модель!", "plan": "<PERSON><PERSON><PERSON><PERSON>", "planReport": "Отчет плана", "jobReport": "Отчет работы", "deviceSearch": "Поиск оборудования", "manufacturer": "Производитель", "model": "Модель", "deviceSN": "SN счетчика", "planCreation": "Создание плана", "planDesc": "Описание плана", "planStartTime": "Время начала плана", "planExpTime": "Время истечения срока плана", "newVersion": "Новая версия", "imageIdentifier": "Идентификатор изображения", "firmwareFile": "Файл прошивки", "taskStartTime": "Время начала задачи", "taskEndTime": "Время завершения задачи", "taskCycle": "Цикл задачи", "hour1": "1 час", "hour2": "2 часа", "hour3": "3 часа", "hour4": "4 часа", "hour5": "5 часов", "hour6": "6 часов", "hour7": "7 часов", "hour8": "8 часов", "hour9": "9 часов", "hour10": "10 часов", "hour11": "11 часов", "hour12": "12 часов", "deviceType": "Тип оборудования", "currentVersion": "Текущая версия", "expTimeAfterStaTime": "Истечение срока действия плана должно быть позже начала плана!", "pleaEntNewVer": "Пожалуйста, введите новую версию!", "pleaEntImaIdent": "Пожалуйста, введите идентификатор изображения!", "pleaSelFirmFile": "Пожалуйста, выберите файл прошивки!", "pleaSelStartTime": "Пожалуйста, выберите время начала задачи!", "pleaSelEndTime": "Пожалуйста, выберите время окончания задачи!", "startTimeLaterThanEndTime": "Время начала задачи должно быть равно или позже, чем время окончания задачи!", "pleaSelTaskCycle": "Пожалуйста, выберите цикл задач!", "confCreaPlan": "Подтвердите создание этого плана?", "information": "Информация"}, "addAssetGPRSMeter": {"sn": "SN", "comSn": "SN концентратора", "refCommun": "Референтный концентратор", "referenceMeter": "Референтный счетчик", "refSnIsEm": "SN референтного счетчика отсутствует!", "successMatch": "Успешное сочетание", "devTypeMis": "Тип оборудования не совпадает", "pleMatchData": "Сочетайте данные сначала!"}, "FMUPlanMeterList": {"title": "Список оборудования", "sn": "SN счетчик", "manufacturerName": "Производитель", "modelName": "Модель", "export": "Экспорт", "print": "Распечатать", "fwVersion": "Текущая версия"}, "FMUPlanMeterList0": {"title": "Список оборудования", "sn": "SN счетчика", "manufacturerName": "Производитель", "modelName": "Модель", "currentVesion": "Текущая версия", "newVersion": "Новая версия", "startTime": "Время начала", "export": "Экспорт", "print": "Распечатать", "expiryTime": "Время истечения срока"}, "jobReportJobList": {"title": "Jobs", "opt": "Действие", "id": "ID", "meterIdDfj": "ID счетчика", "deviceTypeDfj": "Тип оборудования", "snDfj": "SN счетчика", "manufacturerIdDfj": "Производитель", "modelNameDfj": "Модель", "currentVesionDfj": "Старая версия", "newVersionDfj": "Новая версия", "startTimeDfj": "Время начала", "expiryTimeDfj": "Время истечения срока", "lastExecTimeDfj": "Время последнего выполнения", "stateDfj": "Статус", "blockSizeDfj": "Размер блока (байт)", "blockCountDfj": "Подсчет переданного блока", "fwuProgressDfj": "Процесс", "export": "Экспорт", "print": "Распечатать", "confToCancJob": "Вы действительно хотите отменить данную задачу?", "cancelled": "Отмено", "failedReasonDfj": "Причина", "runing": "Загружается", "done": "Завершено", "cancel": "Отменить", "waiting": "Ожидается", "expired": "Истек", "jobNoExcute": "Задача еще не началась!"}, "limiterGroupList": {"id": "ID", "type": "Тип", "name": "Имя группы", "protocolId": "Стандарт счетчика", "meterNumber": "Номер счетчика", "introduction": "Описание", "meterCount": "Количество счетчиков", "title": "Список вариантов пороговых значений", "titleDetails": "Подробности вариантов пороговых значений", "add": "Добавить вариант пороговых значений", "delete": "Удалить", "pleaseSelectData": "Выберите вариант из списка вариантов пороговых значений!"}, "limiterList": {"title": "Конфигурация пороговых значений", "id": "Номер", "name": "<PERSON>у<PERSON><PERSON><PERSON>", "value": "Значение", "meterCount": "Количество счетчиков", "getProgress": "Получить прогресс", "day": "День", "week": "Неделя", "season": "Сезон", "upcoming": "Наступающий", "processing": "В процессе", "success": "Успешно", "failed": "Не удалось", "timeout": "Время истекло", "cancelled": "Отмена", "meterCanBeEm": "счетчик не может отсутствовать！", "choLeastCopRead": "Выберите не менее чем одну копию показания！", "seleLeastOneData": "Выберите не менее чем одни данные таблицы！", "pleLimitCofVal": "Настройте конфигурацию пороговых значений!", "pleLimitCofValNo": "Настройте номер конфигурации пороговых значений !", "calGetPtogress": "В процессе получения календаря", "speDayGetPtogress": "В процессе получения особых дней ", "stepTariffGetPtogress": "В процессе получения ступенчатых тарифов"}, "measurementGroupList": {"title": "Список группы измерения", "titleDetails": "Подробности группы измерения", "add": "Добавить группу измерения", "delete": "Удалить", "id": "ID", "type": "Тип", "name": "Имя группы", "protocolId": "Стандарт счетчика", "referenceGroup": "Референтная группа", "meterNumber": "Номер счетчика", "introduction": "Описание", "meterCount": "Количество счетчиков", "editProfile": "Редактирование профилей", "pleaseSelectData": "Выберите группу из списка группы измерения!", "confirmToDel": "Вы действительно хотите удалить данную группу?", "addProfile": "Добавить профиль"}, "meterDataReportList": {"frequency": "Частоты", "headTitle": "Отчет данных счетчиков", "intervalEnergy": "Interval Energy Data", "times": "Время", "groupId": "Группа", "channel": "Канал данных", "serizlName": "SN", "export": "Экспорт", "print": "Распечатать", "graphVisualiz": "Графическая визуализация", "noDataChannel": "Никакого канала данных не выбрано", "tableView": "Таблица", "lineChart": "Линейный график", "columnChart": "Колонка", "reduction": "Снижение", "saveAsPicture": "Сохранить как картинку", "mustSingTable": "Должна быть одна статистика", "pleSelColumn": "Выберите одну колонку", "searchFor": "Поиск...", "selectSn": "Выберите SN", "resultOver": "Более 200 наборов результатов, пожалуйста, выберите критерии заново!", "selectAll": "Выберите все", "allSelected": "Все выбрано"}, "planReportJobList": {"title": "Jobs", "opt": "Действие", "id": "ID", "meterId": "ID счетчика", "sn": "SN счетчика", "manufacturerName": "Производитель", "modelName": "Модель", "currentVesion": "Текущая версия", "newVersion": "Новая версия", "lastExecTime": "Время последнего выполнения", "state": "Статус", "blockSize": "Размер блока (байт)", "blockCount": "Transferred Block Count", "fwuProgress": "Процесс", "failedReason": "Причина", "export": "Экспорт", "print": "Распечатать", "cancel": "Отменить", "confiCanCelJob": "Вы действительно хотите отменить это задание?"}, "planReportList": {"title": "Планы", "opt": "Действие", "id": "id", "deviceType": "Тип оборудования", "introduction": "Описание плана", "manufacturerId": "Производитель", "modelName": "Модель", "currentVesion": "Текущая версия", "newVersion": "Новая версия", "taskCycle": "Цикл задач", "filePath": "Имя файла", "startTime": "Время начала", "expiryTime": "Время истечения срока действия", "taskStartTimeStr": "Время запуска задачи", "taskEndTimeStr": "Время завершения задачи", "state": "Если истекло время", "done": "Готово", "expired": "Истечение", "running": "Работающий", "export": "Экспорт", "print": "Распечатать", "cancelled": "Отменен", "cancel": "Отмена", "conToCanPlan": "Вы действительно хотите отменить данный план?", "waiting": "Ожидается", "meter": "Счетчик", "communicator": "Коммуникатор", "valid": "Действует"}, "profileList": {"title": "Список профилей", "add": "Добавить", "delete": "Удалить", "id": "ID", "mgId": " ID группы измерений", "profileId": "ID профили", "profileType": "Тип профиля", "profileType_view": "Тип профиля", "profileName": "Наименование профиля", "profileCycleType": "Тип интервалов профиля", "profileCycle": "Интервал профиля", "protocolCode": "<PERSON>од", "pleaseSelectData": "Выберите профиль из списка профилей!", "pleSelAntOne": "Данный профиль уже добавлен, выберите другой!", "confirmToDel": "Вы действительно хотите удалить данный профиль?"}, "seasonList": {"id": "ID", "seasonName": "ID сезона", "startTime": "Время начала", "weekProfile": "Недельные профили", "title": "Список сезонов", "add": "Добавить", "delete": "Удаить", "month": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dayofMonth": "День месяца", "year": "Год", "dayofWeek": "День недели", "hour": "<PERSON>а<PERSON>ы", "minute": "Минута", "second": "Втор<PERSON>й", "pleaseSelectData": "Выберите сезон из списка сезонов!", "pleSetSeasonList": "Пожалуйста, настройте профиль недели в списке сезонов!", "seasonAreBeAtMost": "Не более 255 сезонов разрешено создавать!", "seleLeastOneData": "Выберите не меньше одной таблицы данных !", "noMeterGroup": "Пожалуйста, проверьте, была ли конфигурирована Группа счетчика или нет! "}, "selectedDataChannelList": {"title": "Выбранные каналы данных", "up": "Ввер<PERSON>", "down": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Удалить", "id": "ID", "mgId": "ID группы измерений", "profileId": "ID профилей", "dataitemId": "ID данных", "dataitemName": "Каналы данных", "sortId": "ID сортировки"}, "specialDayList": {"id": "ID", "specialDayName": "ID особых дней", "date": "Дата", "dayProfile": "Дневной профиль", "title": "Список особых дней", "month": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dayofMonth": "День месяца", "year": "Год", "dayofWeek": "День недели", "pleaseSelectData": "Выберите день из списка особых дней!", "pleSpeDayDprofile": "Выберите дневной профиль для особого дня!", "daysBeAtMost": "Не более 255 дней разрешено создавать!", "dayAssCanBeDel": "День, связанный со специальным дневным списком, нельзя удалить!"}, "sysIntegrationLogList": {"title": "<PERSON><PERSON><PERSON><PERSON>л интеграции системы", "id": "ID", "sn": "SN", "fromId": "Система", "tv": "Время запроса", "requestType": "Тип запроса", "requestSubType": "Подтип запроса", "responseResult": "Результат ответа", "export": "Экспорт", "print": "Распечатать"}, "timeSynchronizationLog": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> синхронизации времени", "id": "ID", "sn": "SN", "tv": "Время заданий", "strSynResult": "Результат синхронизации", "meterTv": "Время счетчика", "systemTv": "Время системы", "synTv": "Синхронизация времени", "failedReason": "Причина неудачи", "export": "Экспорт", "print": "Распечатать"}, "touGroupList": {"id": "ID", "title": "Управление группами счетчиков", "touTitle": "Список группы TOU", "touTitle_": "Подробность группы TOU", "measurement": "Измерение", "tou": "TOU", "limiter": "Ограничения порога", "addTouGroup": "Добавить группу TOU", "calendar": "Календарь", "specialDay": "Особые дни", "delete": "Удалить", "type": "Тип", "name": "Имя группы", "protocolId": "Станда<PERSON><PERSON> сче<PERSON><PERSON>и<PERSON>ов", "meterNumber": "Номер счетчиков", "introduction": "Описание", "meterCount": "Количество счетчиков", "referenceGroup": "Референтная группа", "pleaseSelectData": "Выберите группу из списка TOU группы!", "canNotBeDel": "Не может удалить!", "confirmToDel": "Вы действительно хотите удалить данную группу?"}, "tracingLogList": {"headTitle": "Управление группами счетчиков", "meterTracingLog": "<PERSON>у<PERSON><PERSON>л отслеживания счетчиков", "timeSynLog": "<PERSON><PERSON><PERSON><PERSON><PERSON> синхронизации времени", "sysInteLog": "<PERSON><PERSON><PERSON><PERSON>л интеграции системы", "userLog": "<PERSON><PERSON><PERSON><PERSON>л пользователя", "deviceSn": "SN счетчика", "service": "Обслуживание", "logLevel": "Уровень журналов", "startTime": "Время начала", "endTime": "Время окончания", "reqType": "Тип запроса", "user": "Пользователь", "logType": "<PERSON><PERSON><PERSON>", "logSubType": "Под<PERSON><PERSON><PERSON>", "clikcSearchBut": "Нажмите кнопку поиска", "sychResult": "Синхронизация результата", "title": "<PERSON>у<PERSON><PERSON>л трека", "export": "Экспорт", "print": "Распечатать", "id": "ID", "date": "Время запроса", "serviceId": "Обслуживание", "type": "<PERSON><PERSON><PERSON>", "level": "Уровень журналов", "content": "Содержание", "success": "Успешно", "failed": "Не удалось", "normal": "Нормально"}, "userLogList": {"title": "User Log", "userId": "Id пользователь", "tv": "Время операции", "name": "Пользователь", "logType": "<PERSON><PERSON><PERSON>", "logSubType": "Под<PERSON><PERSON><PERSON>", "detail": "Содержание", "startTimeCanNot": "Время начала или окончания не может быть пустым!", "logTimeBeWDay": "Время запроса журнала должно быть в течение дня!"}, "unSelectedDataChannelList": {"title": "Дополнительные каналы данных", "add": "Добавить", "id": "ID", "mgId": "ID группы измерения", "profileId": "ID профиля", "dataitemId": "ID данных", "dataitemName": "Каналы данных", "sortId": "ID сортировки", "pleaseSelectData": "Выберите канал данных из списка!"}, "weekList": {"id": "ID", "weekName": "ID Недели", "dayProfile": "Дневной профиль", "add": "Добавить", "delete": "Удалить", "title": "Список неделей", "pleaseSelectData": "Выберите неделю из списка!", "pleSetWeekList": "Выберите дневной профиль из списка неделей!", "youCanSingleDay": "Вы не можете удалить один день!", "seasonListCanBeDel": "Неделя связанная со списком сезонов не может удалиться!", "weeksToBeAtMost": "Не более 255 неделей разрешено создавать!", "weekCanBeDel": "День связанный со списком неделей не может удалиться!"}, "sysUserList": {"headTitle": "Пользователь & Роль", "title": "Список пользователей", "orgPassword": "Оригинальная пароль", "newPassword": "Новая пароль", "confirm": "Подтвердить", "enterNewPasswordAgain": "Введите пароль еще раз", "askYouForgotPwd": "Обратитесь к администратору если забыли пароль.", "id": "Номер", "username": "Аккаунт", "name": "Имя", "email": "Почта", "mobilePhone": "Номер телефона", "userState": "Статус", "userType": "Тип пользователей", "roleId": "Роль", "orgId": "Организация", "lastLoginTime": "Время последнего входа", "opt": "Действие", "user": "Пользователь", "role": "Роль", "organization": "Организация", "disable": "Запрещать", "enable": "Включить", "addUser": "Добавить пользователя", "delUser": "Удалить пользователя", "resPwd": "Сброс пароля", "oldPasswordError": "Ошибка пароля!", "newPasswordError": "Пароли не совпадают!", "resetPassSucc": "Удалось сбросить пароль!", "confirmDisable": "Вы действительно хотите отключить данную учетную запись?", "confirmEnable": "Вы действительно хотите включить данную учетную запись ?", "selectUser": "Выберете пользователя!", "myProfile": "Мой профиль", "passSet": "Настройка пароля", "administrator": "Администратор", "normal": "Нормально", "password": "Пароль", "pleaEnterPass": "Выберите пароль", "roleName": "Роль"}, "sysRoleList": {"title": "Список ролей", "id": "НОмер", "name": "Имя", "userCount": "Учетная запись", "description": "Описание", "delete": "Удалить", "addRole": "Добавить роль", "rolenameNull": "Имя не может быть пустым!", "selectRole": "Выберете роль!", "deleteRole": "Вы действительно хотите удалить данную роль?", "queryResultNull": "Результат запроса отсутствует!", "unableDel_user": "Данный роль имеет дочерние пользователи, которые не могут быть удалены!", "operaConfig": "Конфигурация операции", "roleNameExist": "Имя роля уже существует!"}, "meterConfigurationList": {"systemFunction": "Функция системы", "title": "Конфигурация операции", "id": "ID", "isSelect": "Выбрать", "operationname": "Операция", "description": "Описание"}, "organizationList": {"title": "Список организации", "addOrganization": "Добавить организацию", "delete": "Delete", "name": "Имя", "id": "ID", "userCount": "Учетная запись", "meterCount": "Количество счетчиков", "mobile": "Номер телефона", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Операция", "contactMan": "Контактное лицо"}, "sysOrganizationList": {"id": "ID", "parentOrg": "Родительская организация", "name": "Имя", "mobile": "Номер телефона", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Описание", "unableDel_org": "Организация имеет многие организации, которые не могут удалить!", "unableDel_comm": "Организация имеет многие коммуникаторы, которые  не могут удалить!", "unableDel_meter": "Организация имеет многие счетчики, которые не могут быть удалить!", "unableDel_user": "Организация имеет многих пользователей, которые не могут быть удалить!", "moveOrg": "Переместить организацию", "moveOrgError": "Невозможно выбрать организацию!", "selectOrg": "Выберете одну организаицю!", "organization": "Организация", "addOrganization": "Добавить организацию", "moveSuccess": "Удалось переместить", "admitFourLevelOrg": " Не более 4 уровня организаций разрешено создавать!", "deleteOrg": "Вы действительно хотите удалить данную организацию?", "noMoveToChildOrg": "Не разрешено переместить организацию в свою подчиненную организацию!"}, "device_read_result_list": {"id": "ID", "sn": "Серийный номер", "profileId": "Id профиля", "profileName": "Профиль", "communication": "Связь", "requestTime": "Время опроса", "dataChannel": "Каналы данных", "value": "Значение", "reponseTime": "Время данных", "startTime": "Время начала", "endTime": "Время окончания", "status": "Статус", "statusView": "Статус", "export": "Экспорт", "print": "Распечатать"}, "device_read_schedule_list": {"id": "ID", "sn": "Серийный номер", "model": "Модель", "communication": "Связь", "profileId": "ID профиля ", "profile": "Профиль", "startTime": "Время начала", "endTime": "Время окончания"}, "device_read_channel_list": {"title": "Результаты", "checkReadTitle": "Кликньте кнопку для опроса", "id": "ID", "sn": "SN счетчика", "communicator": "SN концентратора", "commmunication": "Связь", "dataChannel": "Каналы данных", "value": "Значение", "status": "Статус", "statusView": "Статус", "requestTime": "Время опроса", "responseTime": "Время ответа", "export": "Экспорт", "print": "Распечатать", "total": "В итоге", "completed": "Завершено", "success": "Успешно", "failed": "Не удалось", "processing": "В процессе", "read": "Опросить", "cancel": "Отмена", "cancelFailure": "Отменить неудачу"}, "deploymentAndClusterManagement": {"title": "Разработка & Управление клиентами", "serviceConfig": "Сервер & Конфигурация сервиса", "channelService": "Подробности конфигурации", "name": "Имя", "ip": "IP", "hostId": "ID Хост", "server": "Сервер", "type": "Тип", "ipOrHostId": "IP/Хост ID", "status": "Статус", "properties": "Свойство", "value": "Значение", "description": "Описание", "addServer": "Добавить сервер", "addService": "Дабавить сервис", "deleteServer": "Удалить сервер", "deleteService": "Удалить сервис", "action": "Действие", "save": "Сохранить", "editChannel": "Редактировать канал", "unableDel_server": "Сервер имеет многие сервисы, которые не могут удалить!", "unableDel_service": "Сервис имеет многие свойства. Которые не могут удалить!", "serviceExistSystem": "Данный сервис уже существует в системе!", "serviceExistServer": "Данный сервис уже существует в сервере!", "ipExistSystem": "Данн<PERSON>й ip уже существует в системе!", "hostIdExistServer": "id хост уже существует в сервере!", "pleaseSelectService": "Выберете один сервис!", "pleaseAddChannel": "Добавьте список сервисов каналы!", "nameExist": "Имя уже существует в системе!", "stop": "Остановлено", "running": "В процессе", "channel": "<PERSON><PERSON><PERSON><PERSON>", "messageBus": "<PERSON><PERSON><PERSON> сообщений", "schedule": "Расписание", "UCI": "UCI", "calculation": "Расчет", "application": "Применение"}, "serviceConfig": {"title": "Конфигурация сервиса", "id": "ID", "parent": "ID родителя", "serviceTypeTemp": "Тип сервиса", "introduction": "Имя", "serviceType": "Тип", "ip": "IP/Хост ID", "isOnline": "Статус", "delete": "Удалить"}, "meterDataEventExport": {"title": "Данные счетчиков & Экспорт событий", "dataChannel": "Канал данных", "exportProgress": "Процесс экспорта", "exportTime": "Время экспорта", "result": "Результат", "unableDel_server": "Сервер имеет многие сервисы, которые не могут удалить!", "unableDel_service": "Сервис имеет многие свойства, которые не могут удалить!", "serviceExistSystem": "Данный сервис уже существует в системе!", "serviceExistServer": "Данный сервис уже существует в сервере!", "ipExistSystem": "Данн<PERSON>й ip уже существует в системе!", "hostIdExistServer": "Данный хост id  уже существует в сервере!", "pleaseSelectService": "Выберете один сервис!", "pleaseAddChannel": "Добавьте список сервиса каналы!", "nameExist": "Данное имя уже существует в системе!"}, "stepTariffList": {"title": "Список группы прогрессивных тарифов", "titleDetails": "Подробности группы прогрессивных тарифов", "id": "Номер", "stepTariff": "Ступенчатые тарифы", "description": "Описание", "groupName": "Имя группы", "stepName": "Ступенчатое имя", "meterStandard": "Станда<PERSON><PERSON> сче<PERSON><PERSON>и<PERSON>ов", "startQuantity": "Начальное количество (kWh)", "meterNumber": "Номер счетчика", "endQuantity": "Окончательное количество  (kWh)", "activateTime": "Активировать время", "price": "Цена ($)", "referenceGroup": "Референтная группа", "addGroup": "Добавить группу ступенчатого тарифа", "addStep": "Добавить ступечатое", "pleaseSelectData": "Выберите ступенчатый тариф в данном списке!", "nameExist": "Имя группы ступенчатых тарифов уже существует!", "stepNameExist": "Имя ступенчатых тарифов уже существует!", "selectStepTariff": "Выберите группу ступенчатых тарифов!", "deleteStepTariff": "Вы действительно хотите удалить данную группу ступенчатых тарифов ?", "selectStep": "Выберите один ступенчатый тариф!", "delStep": "Вы действительно хотите удалить данный ступенчатый тариф?", "endGreaterThanStart": "Окончательное количество должно быть больше начального количества!", "pleaseAddStep": "Добавьте список ступенчатый!", "pleaseCorrectFormat": "Введите правильный формат данных!", "reselectStepName": "Выберите имя ступенчатого тарифа еще раз!", "startQuantityError": "Должны соответствовать окончательному количеству предыдущего шага!", "delete": "Удалить", "save": "Сохранить", "stepTariffGetPtogress": "Получается ступенчатый тариф"}, "meterConfiguration": {"title": "Конфигурация счетчиков", "canNotBeDelete": "Невозможно удалить задачу при обработке!", "pleaseSelectData": "Выберите задачу в этом списке!", "confirmDeleteTask": "Вы должны удалить эту задачу?", "stepNameExist": "Данный имя ступенчатого тарифа уже существует,выберите другое!"}, "meterGroupUpgrade": {"plan": "<PERSON><PERSON><PERSON><PERSON>", "planReport": "Отчет плана", "jobReport": "Отчет работы", "deviceSearch": "Поиск оборудования", "groupType": "Тип группы", "group": "Группа", "measurement": "Измерение", "TOU": "TOU", "limiter": "Пороговое значение", "stepTariff": "Ступенчатый тариф", "friendly": "Благоприятное время", "search": "Поиск", "deviceSN": "SN счетчика", "pleSelGroupType": "Выберите тип группы!", "pleSelGroup": "Выберите группу!", "pleEntPlanDesc": "Введите описание плана!", "noDeviceFound": "Не найдено оборудование!", "startEqualExpiry": "Время истечения срока действия программы должно быть после начала плана!", "taskStartEqualEnd": "Время начала задачи должно быть равно или позже времени завершения задачи!", "ifExpried": "Если истекло", "valid": "Действующий", "expired": "Истечение", "startTime": "Время начала", "endTime": "Время завершения", "noFoundPlan": "Не найдено плана!", "pleaseSelectAplan": "Выберите план!", "createPlanSucc": "Создание завершено, новый созданный план может проверить в таблице отчета планов.", "exceptionMsg": "Invoking exception of job service, please check and create plan again!", "jobUpgradeSucc": "Обновление всех заданий плана завершено!", "all": "Все", "pleaseSelModel": "Выберите модель!"}, "divMGU_PlanMeterList": {"sn": "SN счетчика", "manufacturer": "Производитель", "model": "Модель", "groupType": "Тип группы", "group": "Группа", "startTime": "Время начала плана", "expiryTime": "Время истечения плана "}, "MGU_planReportList": {"opt": "Действие", "introduction": "Описание плана", "groupType": "Тип группы", "groupName": "Имя группы", "model": "Модель", "state": "Состояние", "taskCycle": "Цикл задач", "done": "Готово", "expired": "Истечение", "running": "Работающий", "waiting": "Ожидается", "startTime": "Время начала плана", "expiryTime": "Время истечения плана", "taskStartTime": "Время начала задачи", "taskEndTime": "Время истечения задачи"}, "MGU_planReportJobList": {"title": "Работы", "id": "ID", "meterId": "ID счетчика", "sn": "SN счетчика", "manufacturer": "Производитель", "model": "Модель", "status": "Статус", "lastExecTime": "Последнее время выполнения", "reason": "Причина", "running": "Работающий", "done": "Завершено", "cancel": "Отмена", "waiting": "Ожидается", "expired": "Истечение", "export": "Экспорт", "print": "Распечатать", "failedReason": "Причина", "addPlan": "Добавить план"}, "connOrDisconnList": {"title": "Результаты", "mainTitle": "Включение / Выключение", "sn": "SN Счетчик", "commSN": "SN Коцентратор", "communication": "Связь", "command": "Команда", "status": "Статус", "requestTime": "Время опроса", "responseTime": "Время ответа", "connect": "Влкючение", "disconnect": "Выключение", "pleSelMeter": "Выберите счетчик!", "export": "Экспорт", "print": "Распечатать", "readTimeOut": "Время опроса истекло", "failReason": "Причина", "relayOptional": "Реле опционально", "internal": "Главное реле", "external": "Внешнее реле1"}, "billingReportList": {"reportList": "Список отчетов", "startTime": "Время начала", "endTime": "Время окончания", "organization": "Организация", "lineLossObjectType": "Тип", "timeType": "Тип времени", "lineLossObject": "Объект потери линии", "import": "Импорт(KWh)", "export": "Экспорт(KWh)", "loss": "Потерия(KWh)", "rate": "Процент(%)", "date": "Дата", "lineLossStartTimeTip": "Выберите время начала потери линии!", "lineLossEndTimeTip": "Выберите время окончания потери линии!", "timeIssueAlert": "Время завершения плана должно быть позже времени начала потери линии!", "transformer": "Трансформатор", "line": "Линия", "meter": "Счетчик", "communicator": "Концентратор", "daily": "Ежедневный", "monthly": "Месячный", "title": "Отчет Билинг", "sn": "SN Счетчик", "time": "Время", "energy": "Энергопотребление(KWh)", "consumption": "Сумма(USD)", "rateOther": "Процент(%)", "serach": "Поиск", "reportTime": "Время отчета", "assetSelectTip": "Выберите оборудование !"}, "lineLossReportList": {"reportList": "Список отчетов", "startTime": "Время начала", "endTime": "Время окончания", "organization": "Организаиця", "lineLossObjectType": "Тип", "timeType": "Тип времени", "lineLossObject": "Объект потерилл линии", "import": "Импорт(KWh)", "export": "Экспорт(KWh)", "loss": "Потерия(KWh)", "rate": "Процент(%)", "date": "Дата", "lineLossStartTimeTip": "Выберите время начала потерии линии!", "lineLossEndTimeTip": "Выберите время окончания потерии линии!", "timeIssueAlert": "Время завершения плана должно быть позже времени начала потери линии!", "transformer": "Трансформаторы", "line": "Линия", "meter": "Счетчик", "communicator": "Концентраторы", "daily": "Ежедневный", "monthly": "Месячный", "title": "Отчет потерии линии", "rateOther": "Процент(%)", "type": "Тип", "meterName": "Имя счетчика", "tv": "Tv", "name": "Канал данных", "dataValue": "Значение даты", "objectName": "Объект потерии линии", "entity": "Сущность", "entityName": "Имя сущности", "objectComparison": "Объект сравнения", "yoyComparison": "YoY сравнение", "qoqComparison": "QoQ сравнение", "cancelComparison": "Отмена сравнения", "yoy": "YoY", "qoq": "QoQ"}, "importAndExportReport": {"organization": "Организаиця", "timeType": "Тип времени", "dataType": "Тип даты", "startTime": "Время начала", "endTime": "Время окончания", "result": "Результат", "time": "Время", "value": "Значение(kWh)", "meterSn": "SN счетчик", "import": "Импорт", "export": "Экспорт", "detail": "Подробнее", "noRecords": "Отсутствие записи", "timeNotExist": "Шкала времени не существует!", "title": "Отчеты Импорт & Экспорт", "curveType": "Тип графика"}, "lineManagementList": {"title": "Линия", "lineSn": "SN Линии", "lineName": "Имя линии", "lineList": "Список линии", "name": "Имя", "organization": "Организаиця", "type": "Тип", "voltageLevel": "Уровень напряжения", "properties": "Свойства", "basicInformation": "Базовая информация", "transformers": "Трансформаторы", "calculationObject": "Объект расчета", "sn": "SN", "transformerList": "Список трансформаторов", "transformerSn": "SN трансформатор", "calObjList": "Список объектов расчета", "calculationObjectName": "Имя объектов расчета", "cycle": "<PERSON>и<PERSON><PERSON>", "calculationObjectProperties": "Свойства объекта расчета", "dataChannelList": "Список каналов данных", "meterSn": "SN счетчик", "meterName": "Имя счетчика", "dataChannel": "Канал данных", "addDataChannel": "Добавить канал данных", "addDataChannelFromTransformers": "Добавить канал данных с трансформатора", "addLine": "Добавить линию", "opt": "Действие", "busbar": "Busbar", "feeder": "<PERSON>ede<PERSON>", "unit": "kV", "meterNotExist": "Не найден счетчик!", "noCalObjFound": "Не найдень объект расчета!", "nolineFound": "Не найдена линия!"}, "transformerManagementList": {"title": "Трансформаторы", "transformerSn": "SN трансформаторы", "transformerName": "Имя трансформатора", "transformerList": "Список трансформатора", "name": "Имя", "organization": "Организация", "ratedCapacity": "Номинальная мощность", "properties": "Свойства", "basicInformation": "Базовая информация", "calculationObject": "Проект расчета", "sn": "SN", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "calculationObjectList": "Список объектов расчета", "calculationObjectName": "Имя объектов расчета", "type": "Тип", "cycle": "<PERSON>и<PERSON><PERSON>", "calculationObjectProperties": "Свойства объектов расчета", "dataChannelList": "Список канала данных", "meterSn": "SN счетчик", "meterName": "Имя счетчика", "dataChannel": "Канал данных", "addDataChannel": "Добавить канал данных", "noTrFound": "Не найден трансформатор!"}, "calculationObjectList": {"define": "Определение расчета объекта ", "title": "Расчета объекта", "name": "Имя", "type": "Тип", "cycle": "<PERSON>и<PERSON><PERSON>", "entityType": "Entity Type", "entityName": "Entity Name", "calculationObject": "Объект расчета", "properties": "Свойства", "calculationObjectList": "Список объектов расчета", "meterSn": "SN счетчик", "dataChannel": "Канал данных", "addDataChannel": "Добавить канал данных", "lineLoss": "Потерия линии", "import": "Импорт", "export": "Экспорт", "daily": "Ежедневный", "monthly": "Месячный", "line": "Линия", "transformer": "Трансформатор", "organization": "Организация", "pleaseChoose": "---Выберите---", "all": "Все", "addBySearch": "Добавление через поиск", "addFromRelationship": "Добавелние через связанные", "addMeteringPoint": "Добавление точку измерения", "defaultType": "Тип по умолчанию", "defaultDataChannel": "Канал данных по умолчанию", "gridLossReport": "Отчет потерии электросети", "rangeOfLossRate": "Дипазон процентов потерии (%)", "reference": "Справка"}, "dcuConfiguration": {"title": "DCU Конфигурация", "meterListUpload": "Загрузка списка счетчиков", "otherParameter": "Другой параметр", "communicatorSn": "SN концентратор", "set": "Настройка", "meterTitle": " Список счетчиков", "meterSn": "SN счетчик", "pointNum": "Точка Num", "logicalName": "Логическое наименование", "baudRate": "Скорость передачи", "comNum": "COM Num", "protocol": "Протокол", "communicatorAddress": "Адрес концентратора", "result": "Результат", "communication": "Связь", "status": "Статус", "requestTime": "Время запроса", "responseTime": "Время ответа", "reason": "Причина", "get": "Получить", "hesCommunicationParameter": "Hes параметры связи", "encryptionMode": "Режим шифрования", "encryptionType": "Тип шифрования", "encryptionKey": "Ключ шифрования", "globalEncryption": "Глобальное шифрование", "dedicatedEncryption": "Выделенное шифрование", "unencryted": "Незашифрованная", "ipAddress": "IP адрес", "port": "Порт", "userName": "Пользовательское имя", "password": "Пароль", "dialedNumber": "Набранный номер", "ipAddressBackup": "IP-адрес резервного копирования", "portBackup": "Порт - Резервное копирование", "apn": "APN", "resetCommand": "Команда сброса", "resetType": "Тип сброса", "command": "Команда", "pleaseChooseCommSn": "Выберите comm sn!", "pleaseFillIn": "Выберите информацию!", "pleaseFillInCorrect": "Выберите правильную информацию!", "pleaseChoose": "---Выберите---", "hardwareReset": "Перезагрузка оборудования", "dataReset": "Сброс данных", "parameterAndDataReset": "Сброс параметров и данных", "parameterExceptHesReset": "Сброс параметра(За исключением параметры связи HES", "importantUserSettingList": "Настройка важных пользователей", "meterModel": "Модель счетчика", "importantFlag": "Key Meter Flag -System", "concentratorimportantFlag": "Key Meter Flag -Concentrator", "resetTypeCannotBeRead": "Тип сброса не может опросить", "selectCommunicators": "Выберите коммуникаторы.", "selectImportantUser": "Выберите счетчик в качестве важного потребителя.", "selectResetType": "Выберите тип сброса.", "wiringMethod": "Метод подключения проводов", "comType": "COM тип", "versionInfo": "Информация о версии", "equipmentNumber": "Номер оборудования", "softwareVersion": "Версия ПО", "softwareDate": "Дата ПО", "SoftwareItem": "элемент ПО", "communicationProtocol": "Протокол связи", "hardwareVersion": "Версия аппаратного обеспечения", "hardwareDate": "Дата аппаратного обеспечения", "versionInformationWrite": "Информация о концентраторе не может записаться.", "importUserPleaseSelect": " Выберите счетчик в качестве важного потребителя.", "indexDcu": "Индекс Dcu"}, "importGprsMeter": {"importFromShipmentFile": "Импорт из файла отправки", "importGprsMeter": "Импорт GPRS счетчик", "num1": "1", "num2": "2", "num3": "3", "num4": "4", "step1": "Шаг 1", "step2": "Шаг 2", "step3": "Шаг 3", "step4": "Шаг 4", "assetType": "Тип актива", "GPRSMeter": "GPRS Счетчик", "assetFile": "Файд актива", "manufacturer": "Производитель", "model": "Модель", "firmwareVersion": "Версия прошивки", "measurementGroup": "Группа измерения", "collectionSchemeGroup": "Группа сбора данных", "meterExist1": "Счетчик с номером SN ", "meterExist2": "Существует.", "checkProgress": "Проверить прогресс", "importProgress": "Прогресс импорта", "selectFile": "Выберите файл", "checkStatus": "Проверка файлов Excel, не переключайте!", "pleaseSelectFile": "Выберите файл Excel!", "noDataInExcel": "В файле Excel нет данных!", "verificationFailed": "Не удалось верифицировать, проверьте и повторно импортируйте!", "pleaseSelMeaGroup": "Выберите группу измерения!", "pleaseSelColSchGroup": "Выберите группу расписания сбора!", "checkCompleted": "Проверите завершенное!", "importStatus": "Импортируется файлы Excel, не переключайте!", "deviceTypeIsEmpty": "Тип устройства пуст!", "pleaseSelCommType": "Выберите Тип связи!", "shipmentFileType": "Тип файла отгрузки", "meterSipmentFile": "Файл отгрузки счетчиков", "shipmentFile": "Файл отгрузки", "verifySuccess": "Верификация успешна", "verifyFail": "Верификация не удалась"}, "customerList": {"customer": "потребитель", "customerList": "Список потребителей", "customerSn": "Потребитель SN", "customerType": "Тип потребителя", "customerName": "Имя потребителя", "industryType": "Тип деятельности", "onlyOneError": "выбранный счетчик или введенный SN ссылается на другого клиента. "}, "dataComminicationStatus": {"title": "Отчет о состоянии связи", "statusUpdateTime": "Время обновления статуса", "networkAddress": "Сетевой адрес", "commComType": "Коммуникатор COM Тип", "meterComType": "Тип счетчика COM", "offlineTimeDays": "Офлайн время (Days)", "offline": "О<PERSON><PERSON><PERSON><PERSON><PERSON>", "online": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "saleStatReport": {"missData": "Пропущенные данные"}, "veeList": {"validatingReport": "Отчет по валидации", "exception tatal": "Exception Tatal", "class1": "Класс1", "class2": "Класс2", "class3": "Класс3", "class4": "Класс4", "class5": "Класс5", "veeGroup": "VEE группа", "veeGroupList": "VEE лист группы", "groupName": "Наименование группы", "meterCount": "Количество счетчиков", "descr": "Описание", "veeRuleList": "Список правил VEE", "ruleName": "Название правила", "ruleType": "Тип правила", "class": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataChannel": "Канал данных", "event": "Событие", "method": "Метод", "estimationReportProgressDelay": "Отчет об оценке - Задержка прогресса", "estimationReportMissData": "Отчет об оценке- Пропушенные данные", "meterSn": "Счетчик SN", "profile": "Профиль", "communicatorSn": "Коммуникатор SN", "communicationType": "Тип связи", "progress": "Прогресс", "progressDelay": "Задержка прогресса", "time": "Время", "missDataListDetail": "Подробное о пропущенных данных", "missDataList": "Список пропущенных данных", "progressDelayList": "Список задержек прогресса", "progressDelayListDetail": "Подробное о задержках прогресса", "serizlNo": "Устройство SN", "missData": "Пропущенные данные", "deadline": "Предельный срок", "delayExported": "Only delay data can be exported", "missExported": "Only miss data can be exported", "rowExport": "Select the row to export", "taskTv": "Task Time", "ruleStatus": "Правило статуса", "ruleClass": "Класс правила", "editVeeRule": "Редактирование правила Vee", "addVeeRule": "добавление правила Rule", "pleSeleVeeGroup": "Выберите группы Vee!", "pleFillName": "Наименование не может быть пустым !", "pleCycleCount": "Количество циклов должно быть числами!", "eventRuleDesc": "Описание о правиле событий", "dataItemName": "Наименование каналы данных", "dataItemKey": "К<PERSON><PERSON>ч канала данных", "cycleCount": "Количество циклов", "cycleType": "Ти<PERSON>и<PERSON>ов", "paramKey": "<PERSON><PERSON><PERSON><PERSON> параме<PERSON><PERSON>ов", "defaultValue": "Значение параметров", "dataItemList": "Список канала данных", "veeEventParamList": "Список параметров"}, "dict": {"Reports": "Reports", "Cusomer Billing Report": "Отчет билинг", "Line Loss Report": "Отчет потери линии", "Supply Statistics Report": "Отчет статистики подачи электроэнергии", "Sales Statistics Report": "Отчет статистики продажи электроэнергии", "Non-Residential": "Non-Residential", "Residential": "Residential", "Production": "Production", "Farming": "Farming", "Animal Husbandry": "Animal Husbandry", "Fishery": "Fishery", "Mining": "Mining", "Lodging & Catering": "Lodging & Catering", "Minutely": "Minutely", "Hourly": "Hourly", "Daily": "Daily", "Monthly": "Monthly", "Yes": "Yes", "No": "No", "Data Profile": "Data Profile", "Event Profile": "Event Profile"}, "dcuConfiguration300": {"title": "测量点参数管理", "communicatorSn": "集中器资产编号", "measurementPointRange": "测量点范围", "meterTitleHES": "计量点列表", "communication": "communication", "meterSn": "资产编号", "pointNum": "测量点编号", "status": "状态", "meterProperties": "表计性质", "mac": "测量点地址", "meterType": "电能表类型", "totalDivType": "总分类型", "isVip": "重点用户属性", "feeRateCount": "最大费率数", "collectorAddress": "采集器地址", "comNum": "端口号", "taChange": "TA变比", "tvChange": "TV变比", "command": "命令", "reason": "原因", "requestTime": "请求时间", "responseTime": "响应时间", "protocol": "规约", "meterTitleDCU": "表计列表 (从DCU读取)", "seleCommunicator": "请选择集中器！", "seleLeastOne": "至少选择表中的一条数据！", "pleaseEnterPositiveInt": "请输入正整数", "beginGreaterThanEnd": "起始测量点必须大于终止测量点"}, "dictDataItemGroup": {"Energy": "Энергия", "Load Profile Minutely": "Профиль нагрузки минутный", "TOU": "Тар<PERSON><PERSON>", "Standard Event Log": "<PERSON><PERSON><PERSON><PERSON><PERSON> стандартных событий", "Instantaneous Profile Minutely": "Текущий профиль минутный", "Demand Profile Minutely": "Минутный профиль мощности", "Energy Billing Profile Daily": " Ежедневный биллинговый профиль энергии", "Energy Billing Profile Monthly": "Ежемесячный биллинговый профиль энергии", "Demand Billing Profile Monthly": "Ежемесячный биллинговый профиль мощности", "Interval Data Daily": "Контрольный период данных ежедневно", "Interval Data Monthly": "Контрольный период данных ежемесячно"}, "dictDataItem": {"Load Profile Minutely": "中中中", "Active energy import (+A) [Unit: kWh]": "Активная энергия прямая (+A) [Unit: kWh]", "Active energy import (+A) rate 1 [Unit: kWh]": "Активная энергия прямая (+A) тариф 1 [Unit: kWh]", "Active energy import (+A) rate 2 [Unit: kWh]": "Активная энергия прямая (+A) тариф 2 [Unit: kWh]", "Active energy import (+A) rate 3 [Unit: kWh]": "Активная энергия прямая (+A) тариф 3 [Unit: kWh]", "Active energy import (+A) rate 4 [Unit: kWh]": "Активная энергия прямая (+A) тариф 4 [Unit: kWh]", "Active energy export (-A) [Unit: kWh]": "Активная энергия обратная (-A) [Unit: kWh]", "Active energy export (-A) rate 1 [Unit: kWh]": "Активная энергия обратная (-A) тариф 1 [Unit: kWh]", "Active energy export (-A) rate 2 [Unit: kWh]": "Активная энергия обратная (-A) тариф 2 [Unit: kWh]", "Active energy export (-A) rate 3 [Unit: kWh]": "Активная энергия обратная (-A) тариф 3 [Unit: kWh]", "Active energy export (-A) rate 4 [Unit: kWh]": "Активная энергия обратная (-A) тариф 4 [Unit: kWh]", "Reactive energy import (+R) (QI+QII) [Unit: kVarh]": "Реактивная энергия прямая (+R) (QI+QII) [Unit: kVarh]", "Reactive energy import (+R) (QI+QII) rate 1 [Unit: kVarh]": "Реактивная энергия прямая (+R) (QI+QII) тариф 1 [Unit: kVarh]", "Reactive energy import (+R) (QI+QII) rate 2 [Unit: kVarh]": "Реактивная энергия прямая (+R) (QI+QII) тариф 2 [Unit: kVarh]", "Reactive energy import (+R) (QI+QII) rate 3 [Unit: kVarh]": "Реактивная энергия прямая (+R) (QI+QII) тариф 3 [Unit: kVarh]", "Reactive energy import (+R) (QI+QII) rate 4 [Unit: kVarh]": "Реактивная энергия прямая (+R) (QI+QII) тариф 4 [Unit: kVarh]", "Reactive energy export (-R) (QIII+QIV) [Unit: kVarh]": "Реактивная энергия обратная (-R) (QIII+QIV) [Unit: kVarh]", "Reactive energy export (-R) (QIII+QIV) rate 1 [Unit: kVarh]": "Реактивная энергия обратная (-R) (QIII+QIV) тариф 1 [Unit: kVarh]", "Reactive energy export (-R) (QIII+QIV) rate 2 [Unit: kVarh]": "Реактивная энергия обратная (-R) (QIII+QIV) тариф 2 [Unit: kVarh]", "Reactive energy export (-R) (QIII+QIV) rate 3 [Unit: kVarh]": "Реактивная энергия обратная (-R) (QIII+QIV) тариф 3 [Unit: kVarh]", "Reactive energy export (-R) (QIII+QIV) rate 4 [Unit: kVarh]": "Реактивная энергия обратная (-R) (QIII+QIV) тариф 4 [Unit: kVarh]", "Reactive energy QI (+Rl) [Unit: kVarh]": "Реактивная энергия QI (+Rl) [Unit: kVarh]", "Reactive energy QI (+Rl) rate 1 [Unit: kVarh]": "Реактивная энергия QI (+Rl) тариф 1 [Unit: kVarh]", "Reactive energy QI (+Rl) rate 2 [Unit: kVarh]": "Реактивная энергия QI (+Rl) тариф 2 [Unit: kVarh]", "Reactive energy QI (+Rl) rate 3 [Unit: kVarh]": "Реактивная энергия QI (+Rl) тариф 3 [Unit: kVarh]", "Reactive energy QI (+Rl) rate 4 [Unit: kVarh]": "Реактивная энергия QI (+Rl) тариф 4 [Unit: kVarh]", "Reactive energy QII (+Rc) [Unit: kVarh]": "Реактивная энергия QII (+Rc) [Unit: kVarh]", "Reactive energy QII (+Rc) rate 1 [Unit: kVarh]": "Реактивная энергия QII (+Rc) тариф 1 [Unit: kVarh]", "Reactive energy QII (+Rc) rate 2 [Unit: kVarh]": "Реактивная энергия QII (+Rc) тариф 2 [Unit: kVarh]", "Reactive energy QII (+Rc) rate 3 [Unit: kVarh]": "Реактивная энергия QII (+Rc) тариф 3 [Unit: kVarh]", "Reactive energy QII (+Rc) rate 4 [Unit: kVarh]": "Реактивная энергия QII (+Rc) тариф 4 [Unit: kVarh]", "Reactive energy QIII (+Rl) [Unit: kVarh]": "Реактивная энергия QIII (+Rl) [Unit: kVarh]", "Reactive energy QIII (-Rl) rate 1 [Unit: kVarh]": "Реактивная энергия QIII (-Rl) тариф 1 [Unit: kVarh]", "Reactive energy QIII (-Rl) rate 2 [Unit: kVarh]": "Реактивная энергия QIII (-Rl) тариф 2 [Unit: kVarh]", "Reactive energy QIII (-Rl) rate 3 [Unit: kVarh]": "Реактивная энергия QIII (-Rl) тариф 3 [Unit: kVarh]", "Reactive energy QIII (-Rl) rate 4 [Unit: kVarh]": "Реактивная энергия QIII (-Rl) тариф 4 [Unit: kVarh]", "Reactive energy QIII (+Rl) rate 1 [Unit: kVarh]": "Реактивная энергия QIII (+Rl) тариф 1 [Unit: kVarh]", "Reactive energy QIII (+Rl) rate 2 [Unit: kVarh]": "Реактивная энергия QIII (+Rl) тариф 2 [Unit: kVarh]", "Reactive energy QIII (+Rl) rate 3 [Unit: kVarh]": "Реактивная энергия QIII (+Rl) тариф 3 [Unit: kVarh]", "Reactive energy QIII (+Rl) rate 4 [Unit: kVarh]": "Реактивная энергия QIII (+Rl) тариф 4 [Unit: kVarh]", "Reactive energy QIV (-Rc) [Unit: kVarh]": "Реактивная энергия QIV (-Rc) [Unit: kVarh]", "Reactive energy QIV (-Rc) rate 1 [Unit: kVarh]": "Реактивная энергия QIV (-Rc) тариф 1 [Unit: kVarh]", "Reactive energy QIV (-Rc) rate 2 [Unit: kVarh]": "Реактивная энергия QIV (-Rc) тариф 2 [Unit: kVarh]", "Reactive energy QIV (-Rc) rate 3 [Unit: kVarh]": "Реактивная энергия QIV (-Rc) тариф 3 [Unit: kVarh]", "Reactive energy QIV (-Rc) rate 4 [Unit: kVarh]": "Реактивная энергия QIV (-Rc) тариф 4 [Unit: kVarh]", "Apparent energy import (+E) [Unit: kVarh]": "Полная энергия прямая (+E) [Unit: kVarh]", "Apparent energy export (-E) [Unit: kVarh]": "Полная энергия Обратная (-E) [Unit: kVarh]", "Import active demand [Unit:kW]": "Прямая активная мощность  [Unit: kW]", "Export active demand [Unit:kW]": "Обратная активная мощность [Unit: kW]", "Import reactive demand [Unit:kVar]": "Прямая рективная мощность [Unit: kVar]", "Export reactive demand [Unit:kVar]": "Обратная рективная мощность [Unit: kVar]", "Instantaneous voltage L1 [Unit: V] ": "Мгновенное напряжениеL1 [Unit: V]", "Instantaneous voltage L2 [Unit: V]": "Мгновенное напряжение L2 [Unit: V]", "Instantaneous voltage L3 [Unit: V]": "Мгновенное напряжение L3 [Unit: V]", "Instantaneous current L1 [Unit: A]": "Мгновенный ток L1 [Unit: A]", "Instantaneous current L2 [Unit: A]": "Мгновенный ток L2 [Unit: A]", "Instantaneous current L3 [Unit: A]": "Мгновенный ток L3 [Unit: A]", "Neutral current [Unit: A]": "нейтральный ток [Unit: A]", "Instantaneous active import power (+A) [Unit: kW]": "Мгновенная активная мощность прямая (+A) [Unit: kW]", "Instantaneous active export power (-A) [Unit: kW]": "Мгновенная активная мощность обратная (-A) [Unit: kW]", "Instantaneous reactive import power (+R) [Unit: kVar]": "Мгновенная реактивная мощность прямая (+R) [Unit: kVar]", "Instantaneous reactive export power (-R) [Unit: kVar]": "Мгновенная реактивная мощность обратная (-R) [Unit: kVar]", "Instantaneous apparent export power [Unit: kVa]": "Мгновенная полная мощность обратная [Unit: kVa]", "Instantaneous Power factor (PF) L1": "Мгновенный коэффициент мощности (PF) L1", "Instantaneous Power factor (PF) L2": "Мгновенный коэффициент мощности (PF) L2", "Instantaneous Power factor (PF) L3": "Мгновенный коэффициент мощности (PF) L3", "Frequency [Unit: Hz]": "Частоты [Unit: Hz]", "Meter Serial Number": "Серийный номер счетчика", "Manufactory Identifier": "Идентификатор производителей", "Logic Device Name": "Логическое наименование", "Firmware Version": "Версия прошивки", "Clock": "<PERSON>а<PERSON>ы", "Relay Control State": "Статус управления реле", "Relay Control Mode": "Режим управления реле", "Currently active step": "Currently active step", "Currently active price": "Currently active price", "Tariff index": "Тарифный указатель", "Max credit limit": "Максимальный кредитный лимит", "Key expiry number": "Номер ключа истечения", "Max vend limit": "Max vend limit", "Supply group code": "Supply group code", "Key type": "Key type", "Key Revision number": "Key Revision number", "STS software versopm": "STS software versopm", "Current time purchase credit": "Current time purchase credit", "Cumulative purchase credit": "Cumulative purchase credit", "Residual credit": "Residual credit", "Cumulative consume credit": "Cumulative consume credit", "Current month consume credit": "Current month consume credit", "Current day consume credit": "Current day consume credit", "Date of passive step active": "Date of passive step active", "Watchdog Error": " Watchdog Error", "Normal Voltage L1": "Normal Voltage L1", "Under Limit Threshold of miss Voltage [Unit: V]": "Under <PERSON><PERSON> of miss Voltage [Unit: V]"}, "menu": {"Data Collection": "Сбор данных", "Tools": "Инструменты", "System": "систем", "Provisioning": "Конфигурация установки", "Meter Data Report": "Отчет данных", "Meter Event Report": "Отчет событий", "Schedule Reads Report": "Отчет целостности опроса счетчиков по расписанию", "Miss Data Tracing": "Отслеживание пропавших данных", "Collection Scheme Management": "Управление схемой сбора", "Meter Group Management": "Управление группой счетчиков", "Meter Configuration": "Конфигурация счетчиков", "DCU Configuration": "Конфигурация концентратора", "Firmware Upgrade": "Обновление прошивки", "On Demand Reads": "Опрос онлайн", "Connect / Disconnect": " Управление реле", "Asset Management": "Управление системой", "Deployment Management": "Конфигурация системы", "Log Explorer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Permission": "Управление полномочиями", "Data Export Management": "Конфигурация данных", "Meter Group Upgrade": "Управление группы счетчиков", "Data Management": "Управление данными", "DCU Configuration - SG376": "Настройка параметра концентратора -376", "On Demand Reads - CSG": "Всегда копируй", "Calculation Object Define": "Вычисление объекта определено", "VEE Management": "Управление данными", "Reports": "Отчет потери", "Communication Status Report": "Отчет о состоянии связи", "Grid Loss Management": "Управление повреждениями сети", "Template Reports": "Самоопределение"}, "courtsTopology": {"title": "Courts Topology"}, "profile": {"Load Profile (Minutely)": "Загрузи профиль", "Interval Profile": "Профиль с интервалом", "Energy Billing Profile Jingxiang (Daily)": "Краткий обзор энергетических счетов", "Standard Event Log": "Стандар<PERSON>ный журнал событий", "Time Synchronization": "Синхронизация времени", "Fraud Event Log": "<PERSON><PERSON><PERSON><PERSON><PERSON> мошенничества", "Relay Control Event Log": "Ретранслятор управляет журналом событий", "Power Quality Event Log": "Жу<PERSON><PERSON>л событий с электрической энергией", "Communication Event Log": "Жу<PERSON>нал событий связи", "Under voltage Event Log": "<PERSON>у<PERSON><PERSON>л событий с низким напряжением"}}