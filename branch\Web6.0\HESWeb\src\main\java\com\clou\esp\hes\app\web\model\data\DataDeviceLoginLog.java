/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataDeviceLoginLog{ } 
 * 
 * 摘    要： dataDeviceLoginLog
 * 版    本：1.0
 * 作    者：刘义柯
 * 创建于：2020-10-30 07:36:03
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.data;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.power7000g.core.excel.Excel;

public class DataDeviceLoginLog  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DataDeviceLoginLog() {
	}

	@Excel(name = "Logical Name", width = 30)
	/**设备逻辑地址*/
	private java.lang.String mac;
	
	@Excel(name = "Update Time", width = 30)
	/**更新时间*/
	private java.util.Date updateTv;
	
	@Excel(name = "IP Address", width = 30)
	/**IP地址*/
	private java.lang.String ipAddr;
	/**端口*/
	@Excel(name = "Port", width = 30)
	private java.lang.String ipPort;
	/**0:Offline 1:Online*/
	private java.lang.String comStatus;

	/**
	 * 设备逻辑地址
	 * @return the value of DATA_DEVICE_LOGIN_LOG.MAC
	 * @mbggenerated 2020-10-30 07:36:03
	 */
	public java.lang.String getMac() {
		return mac;
	}

	/**
	 * 设备逻辑地址
	 * @param mac the value for DATA_DEVICE_LOGIN_LOG.MAC
	 * @mbggenerated 2020-10-30 07:36:03
	 */
    	public void setMac(java.lang.String mac) {
		this.mac = mac;
	}
	/**
	 * 更新时间
	 * @return the value of DATA_DEVICE_LOGIN_LOG.UPDATE_TV
	 * @mbggenerated 2020-10-30 07:36:03
	 */
	public java.util.Date getUpdateTv() {
		return updateTv;
	}

	/**
	 * 更新时间
	 * @param updateTv the value for DATA_DEVICE_LOGIN_LOG.UPDATE_TV
	 * @mbggenerated 2020-10-30 07:36:03
	 */
    	public void setUpdateTv(java.util.Date updateTv) {
		this.updateTv = updateTv;
	}
	/**
	 * IP地址
	 * @return the value of DATA_DEVICE_LOGIN_LOG.IP_ADDR
	 * @mbggenerated 2020-10-30 07:36:03
	 */
	public java.lang.String getIpAddr() {
		return ipAddr;
	}

	/**
	 * IP地址
	 * @param ipAddr the value for DATA_DEVICE_LOGIN_LOG.IP_ADDR
	 * @mbggenerated 2020-10-30 07:36:03
	 */
    	public void setIpAddr(java.lang.String ipAddr) {
		this.ipAddr = ipAddr;
	}
	/**
	 * 端口
	 * @return the value of DATA_DEVICE_LOGIN_LOG.IP_PORT
	 * @mbggenerated 2020-10-30 07:36:03
	 */
	public java.lang.String getIpPort() {
		return ipPort;
	}

	/**
	 * 端口
	 * @param ipPort the value for DATA_DEVICE_LOGIN_LOG.IP_PORT
	 * @mbggenerated 2020-10-30 07:36:03
	 */
    	public void setIpPort(java.lang.String ipPort) {
		this.ipPort = ipPort;
	}
	/**
	 * 0:Offline 1:Online
	 * @return the value of DATA_DEVICE_LOGIN_LOG.COM_STATUS
	 * @mbggenerated 2020-10-30 07:36:03
	 */
	public java.lang.String getComStatus() {
		return comStatus;
	}

	/**
	 * 0:Offline 1:Online
	 * @param comStatus the value for DATA_DEVICE_LOGIN_LOG.COM_STATUS
	 * @mbggenerated 2020-10-30 07:36:03
	 */
    	public void setComStatus(java.lang.String comStatus) {
		this.comStatus = comStatus;
	}

	public DataDeviceLoginLog(java.lang.String mac 
	,java.util.Date updateTv 
	,java.lang.String ipAddr 
	,java.lang.String ipPort 
	,java.lang.String comStatus ) {
		super();
		this.mac = mac;
		this.updateTv = updateTv;
		this.ipAddr = ipAddr;
		this.ipPort = ipPort;
		this.comStatus = comStatus;
	}

}