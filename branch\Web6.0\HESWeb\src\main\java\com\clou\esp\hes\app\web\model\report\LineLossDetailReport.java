package com.clou.esp.hes.app.web.model.report;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.power7000g.core.excel.Excel;


/**
 * @ClassName: LineLossReport
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年8月13日 上午9:35:35
 *
 */
public class LineLossDetailReport extends BaseEntity{

	private static final long serialVersionUID = 1L;
	@Excel(name = "Line Object Name", width = 30)
	private String objectName; // sn
	@Excel(name = "Type", width = 30)
	private String type; // sn
	@Excel(name = "Meter SN", width = 30)
	private String meterName; // 组织机构
	@Excel(name = "Tv", width = 30)
	private String tv; // 线损对象类型
	@Excel(name = "Data Name", width = 30)
	private String name; // 时间类型
	@Excel(name = "Data Value", width = 30)
	private String dataValue;
	
	
	public LineLossDetailReport(){
		
	}

	

	public String getObjectName() {
		return objectName;
	}



	public void setObjectName(String objectName) {
		this.objectName = objectName;
	}



	public String getType() {
		return type;
	}


	public void setType(String type) {
		this.type = type;
	}


	public String getMeterName() {
		return meterName;
	}


	public void setMeterName(String meterName) {
		this.meterName = meterName;
	}


	public String getTv() {
		return tv;
	}


	public void setTv(String tv) {
		this.tv = tv;
	}


	public String getName() {
		return name;
	}


	public void setName(String name) {
		this.name = name;
	}


	public String getDataValue() {
		return dataValue;
	}


	public void setDataValue(String dataValue) {
		this.dataValue = dataValue;
	}
	
	
	
	

}