/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetScheduleScheme{ } 
 * 
 * 摘    要： 采集方案
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-03-06 03:10:17
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.asset;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.clou.esp.hes.app.web.validation.ValidGroup1;

public class AssetScheduleScheme  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public AssetScheduleScheme() {
	}

	/**名称*/
	@Length(min = 1, max = 64, message = "{AssetScheduleScheme.name.length.error}", groups = ValidGroup1.class)
	private java.lang.String name;
	/**规约编号*/
	@NotEmpty(message = "{AssetScheduleScheme.protocolId.empty.error}", groups = ValidGroup1.class)
	private java.lang.String protocolId;
	/**Meter Standard*/
	private java.lang.String meterStandard;
	/**电表数量*/
	private Long meterNumber;
	/**说明*/
	private java.lang.String introduction;
	/***/
	private String referenceSchemeId;

	/**
	 * 名称
	 * @return the value of ASSET_SCHEDULE_SCHEME.NAME
	 * @mbggenerated 2018-03-06 03:10:17
	 */
	public java.lang.String getName() {
		return name;
	}

	/**
	 * 名称
	 * @param name the value for ASSET_SCHEDULE_SCHEME.NAME
	 * @mbggenerated 2018-03-06 03:10:17
	 */
    	public void setName(java.lang.String name) {
		this.name = name;
	}
	/**
	 * 规约编号
	 * @return the value of ASSET_SCHEDULE_SCHEME.PROTOCOL_ID
	 * @mbggenerated 2018-03-06 03:10:17
	 */
	public java.lang.String getProtocolId() {
		return protocolId;
	}

	/**
	 * 规约编号
	 * @param protocolId the value for ASSET_SCHEDULE_SCHEME.PROTOCOL_ID
	 * @mbggenerated 2018-03-06 03:10:17
	 */
    	public void setProtocolId(java.lang.String protocolId) {
		this.protocolId = protocolId;
	}

	public java.lang.String getMeterStandard() {
		return meterStandard;
	}

	public Long getMeterNumber() {
		return meterNumber;
	}

	
	public void setMeterStandard(java.lang.String meterStandard) {
		this.meterStandard = meterStandard;
	}

	public void setMeterNumber(Long meterNumber) {
		this.meterNumber = meterNumber;
	}

	

	public java.lang.String getIntroduction() {
		return introduction;
	}

	public void setIntroduction(java.lang.String introduction) {
		this.introduction = introduction;
	}

	public String getReferenceSchemeId() {
		return referenceSchemeId;
	}

	public void setReferenceSchemeId(String referenceSchemeId) {
		this.referenceSchemeId = referenceSchemeId;
	}


}