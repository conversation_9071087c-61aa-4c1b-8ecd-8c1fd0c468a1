/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataStatisticsDevice{ } 
 * 
 * 摘    要： dataStatisticsDevice
 * 版    本：1.0
 * 作    者：赵久霆
 * 创建于：2018-09-19 07:28:24
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.data;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.DateTimeFormatterUtil;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;

import com.clou.esp.hes.app.web.model.data.DataStatisticsDevice;
import com.clou.esp.hes.app.web.service.data.DataStatisticsDeviceService;

/**
 * <AUTHOR>
 * @时间：2018-09-19 07:28:24
 * @描述：dataStatisticsDevice类
 */
@Controller
@RequestMapping("/dataStatisticsDeviceController")
public class DataStatisticsDeviceController extends BaseController{
	final static String TIME_FLAG =ResourceUtil.getSessionattachmenttitle("local.time.flag");
 	@Resource
    private DataStatisticsDeviceService dataStatisticsDeviceService;

 	
	/**
	 * 
	 * @param tv
	 * @param startTv
	 * @param endTv
	 * @param type
	 * @param name
	 * @return
	 */
	@RequestMapping(value = "getDataStatisticsDeviceByMaps")
	@ResponseBody
	public AjaxJson getDataStatisticsDeviceByMaps(String tv, String startTv,
			String endTv, String type, String name, String chartType) {
		AjaxJson j = new AjaxJson();
		Map<String, Object> params = new HashMap<String, Object>();
		if (type != null && type.equals("month")) {
			SimpleDateFormat sdf = new SimpleDateFormat(" MMMM, yyyy",
					Locale.ENGLISH);
			params.put("tvType", 2);// day//month
			if (StringUtil.isNotEmpty(tv)) {
				params.put("tv", DateUtils.date2Str(
						DateUtils.str2Date(tv, sdf), DateUtils.month_sdf));
			}
			if (StringUtil.isNotEmpty(startTv) && StringUtil.isNotEmpty(endTv)) {
				params.put("startTv", DateUtils.date2Str(
						DateUtils.str2Date(startTv, sdf), DateUtils.month_sdf));
				params.put("endTv", DateUtils.date2Str(
						DateUtils.str2Date(endTv, sdf), DateUtils.month_sdf));
			}
		} else {
			params.put("tvType", 1);// day//month
			if (StringUtil.isNotEmpty(tv)) {
				params.put("tv", DateUtils.date2Str(
						DateUtils.str2Date(tv, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)),
						DateUtils.date_sdf));
			}
			if (StringUtil.isNotEmpty(startTv) && StringUtil.isNotEmpty(endTv)) {
				params.put("startTv", DateUtils.date2Str(
						DateUtils.str2Date(startTv, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)),
						DateUtils.date_sdf));
				params.put("endTv", DateUtils.date2Str(
						DateUtils.str2Date(endTv, DateTimeFormatterUtil.getSimpleDateFormat(TIME_FLAG)),
						DateUtils.date_sdf));
			}
		}
		// name=manufacturer;model;commType
		if (StringUtil.isNotEmpty(name)) {
			if (name.equals("manufacturer")) {
				params.put("idType", 2);
			} else if (name.equals("model")) {
				params.put("idType", 3);
			} else if (name.equals("commType")) {
				params.put("idType", 4);
			}
		}
		if (StringUtil.isNotEmpty(chartType)) {
			params.put("chartType", chartType);
		}
		List<Map<String, Object>> diList = dataStatisticsDeviceService.getDataStatisticsDeviceByMaps(params);
		List<String> idList = new ArrayList<String>();
		List<String> nameList = new ArrayList<String>();
		List<Double> integrityList = new ArrayList<Double>();
		List<String> tvList = new ArrayList<String>();
		List<Map<String, Object>> attrList = new ArrayList<Map<String, Object>>();
		Map<String, Object> attrs = new HashMap<String, Object>();
		SimpleDateFormat MMyyyy = new SimpleDateFormat("MM-yyyy");
	//	SimpleDateFormat MMdd = new SimpleDateFormat("MM/dd");
		if (diList != null && diList.size() > 0) {
			for (Map<String, Object> di : diList) {
				BigDecimal integrity = (BigDecimal) di.get("INTEGRITY");
				integrityList.add(integrity.setScale(2, BigDecimal.ROUND_DOWN)
						.doubleValue());
				String id = (String) di.get("ID");
				idList.add(id);
				String n = (String) di.get("NAME");
				nameList.add(n);
				String time = "";
				if (type != null && type.equals("month")) {
					time = DateUtils.date2Str((Date) di.get("TV"), MMyyyy);
				} else {
					time = DateUtils.date2Str((Date) di.get("TV"), DateTimeFormatterUtil.getSimpleDateFormat_DDMM(TIME_FLAG));

				}
				if (!attrs.containsKey(time)) {
					tvList.add(time);
				}
				attrs.put(time, time);
				if (attrs.containsKey(n)) {
					Map<String, Object> attr = (Map<String, Object>) attrs
							.get(n);
					List<Double> iList = (List<Double>) attr.get("iList");
					iList.add(integrity.setScale(2, BigDecimal.ROUND_DOWN)
							.doubleValue());
				} else {
					Map<String, Object> attr = new HashMap<String, Object>();
					List<Double> iList = new ArrayList<Double>();
					iList.add(integrity.setScale(2, BigDecimal.ROUND_DOWN)
							.doubleValue());
					attr.put("name", n);
					attr.put("iList", iList);
					attr.put("id", id);
					attrs.put(n, attr);
					attrList.add(attr);
				}
			}
		}

		Map<String, Object> attributes = new HashMap<String, Object>();
		attributes.put("idList", idList);
		attributes.put("nameList", nameList);
		attributes.put("integrityList", integrityList);
		attributes.put("tvList", tvList);
		attributes.put("attrs", attrList);
		j.setAttributes(attributes);

		return j;
	}
 	
	/**
	 * 跳转到dataStatisticsDevice列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/data/dataStatisticsDeviceList");
    }

	/**
	 * 跳转到dataStatisticsDevice新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dataStatisticsDevice")
	public ModelAndView dataStatisticsDevice(DataStatisticsDevice dataStatisticsDevice,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dataStatisticsDevice.getId())){
			try {
                dataStatisticsDevice=dataStatisticsDeviceService.getEntity(dataStatisticsDevice.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dataStatisticsDevice", dataStatisticsDevice);
		}
		return new ModelAndView("/data/dataStatisticsDevice");
	}


	/**
	 * dataStatisticsDevice查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=dataStatisticsDeviceService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除dataStatisticsDevice信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DataStatisticsDevice dataStatisticsDevice,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dataStatisticsDeviceService.deleteById(dataStatisticsDevice.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存dataStatisticsDevice信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DataStatisticsDevice dataStatisticsDevice,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DataStatisticsDevice t=new  DataStatisticsDevice();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dataStatisticsDevice.getId())){
        	t=dataStatisticsDeviceService.getEntity(dataStatisticsDevice.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dataStatisticsDevice, t);
				dataStatisticsDeviceService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            dataStatisticsDeviceService.save(dataStatisticsDevice);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
	
	
	
}