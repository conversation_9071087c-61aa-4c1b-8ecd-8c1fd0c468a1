package com.clou.esp.hes.app.web.model.demo.req;

import java.io.Serializable;
import java.util.Date;

import javax.jws.soap.SOAPBinding;
import javax.jws.soap.SOAPBinding.Style;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * 孟加拉接口项目请求数据头数据对象
 * 
 * <AUTHOR>
 * 
 */
@SOAPBinding(style = Style.RPC)
@XmlRootElement(name = "Header")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = { "Verb", "Noun", "Timestamp", "Source", "AsyncReplyFlag",
		"ReplyAddress", "AckRequired", "MessageID", "CorrelationID", "Revision" })
public class Header implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public Header() {
		super();
	}

	public Header(String verb, String noun, Date timestamp, String source,
			String asyncReplyFlag, String replyAddress, String ackRequired,
			String messageID, String correlationID, String revision) {
		super();
		Verb = verb;
		Noun = noun;
		Timestamp = timestamp;
		Source = source;
		AsyncReplyFlag = asyncReplyFlag;
		ReplyAddress = replyAddress;
		AckRequired = ackRequired;
		MessageID = messageID;
		CorrelationID = correlationID;
		Revision = revision;
	}

	public String Verb;

	public String Noun;

	public Date Timestamp;

	public String Source;

	public String AsyncReplyFlag;

	public String ReplyAddress;

	public String AckRequired;

	public String MessageID;

	public String CorrelationID;

	public String Revision;

	public String getVerb() {
		return Verb;
	}

	public void setVerb(String verb) {
		Verb = verb;
	}

	public String getNoun() {
		return Noun;
	}

	public void setNoun(String noun) {
		Noun = noun;
	}

	public Date getTimestamp() {
		return Timestamp;
	}

	public void setTimestamp(Date timestamp) {
		Timestamp = timestamp;
	}

	public String getSource() {
		return Source;
	}

	public void setSource(String source) {
		Source = source;
	}

	public String getAsyncReplyFlag() {
		return AsyncReplyFlag;
	}

	public void setAsyncReplyFlag(String asyncReplyFlag) {
		AsyncReplyFlag = asyncReplyFlag;
	}

	public String getReplyAddress() {
		return ReplyAddress;
	}

	public void setReplyAddress(String replyAddress) {
		ReplyAddress = replyAddress;
	}

	public String getAckRequired() {
		return AckRequired;
	}

	public void setAckRequired(String ackRequired) {
		AckRequired = ackRequired;
	}

	public String getMessageID() {
		return MessageID;
	}

	public void setMessageID(String messageID) {
		MessageID = messageID;
	}

	public String getCorrelationID() {
		return CorrelationID;
	}

	public void setCorrelationID(String correlationID) {
		CorrelationID = correlationID;
	}

	public String getRevision() {
		return Revision;
	}

	public void setRevision(String revision) {
		Revision = revision;
	}

	@Override
	public String toString() {
		return "Header [Verb=" + Verb + ", Noun=" + Noun + ", Timestamp="
				+ Timestamp + ", Source=" + Source + ", AsyncReplyFlag="
				+ AsyncReplyFlag + ", ReplyAddress=" + ReplyAddress
				+ ", AckRequired=" + AckRequired + ", MessageID=" + MessageID
				+ ", CorrelationID=" + CorrelationID + ", Revision=" + Revision
				+ "]";
	}

}
