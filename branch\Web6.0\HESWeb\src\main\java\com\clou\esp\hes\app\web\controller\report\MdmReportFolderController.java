/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class MdmReportFolder{ } 
 * 
 * 摘    要： mdmReportFolder
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2020-06-23 04:02:24
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.report;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.tag.vo.ZtreeUtil;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.enums.system.EnumUserType;
import com.clou.esp.hes.app.web.model.report.AssetReportTemplate;
import com.clou.esp.hes.app.web.model.report.MdmReportFolder;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.report.AssetReportRoleMapService;
import com.clou.esp.hes.app.web.service.report.AssetReportTemplateService;
import com.clou.esp.hes.app.web.service.report.MdmReportFolderService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @时间：2020-06-23 04:02:24
 * @描述：mdmReportFolder类
 */
@Controller
@RequestMapping("/mdmReportFolderController")
public class MdmReportFolderController extends BaseController {

 	@Resource
    private MdmReportFolderService mdmReportFolderService;
	@Resource
    private AssetReportRoleMapService assetReportRoleMapService;
	@Resource
    private AssetReportTemplateService assetReportTemplateService;
	
	final static String HESWEB_SYSTEM_NAME =  ResourceUtil.getSessionattachmenttitle("hesweb.system.name");
	/**
	 * 跳转到mdmReportFolder列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/report/mdmReportFolderList");
    }

	/**
	 * 跳转到mdmReportFolder新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "mdmReportFolder")
	public ModelAndView mdmReportFolder(MdmReportFolder mdmReportFolder, HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(mdmReportFolder.getId())){
			try {
                mdmReportFolder=mdmReportFolderService.getEntity(mdmReportFolder.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("mdmReportFolder", mdmReportFolder);
		}
		return new ModelAndView("/report/mdmReportFolder");
	}


	/**
	 * mdmReportFolder查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=mdmReportFolderService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }

	@RequestMapping(value = "opFolderPage")
	public ModelAndView opFolderPage(HttpServletRequest request, Model model) {

		model.addAttribute("entityName", request.getParameter("entityName"));
		model.addAttribute("entityId", request.getParameter("entityId"));
		return new ModelAndView("/report/opFolderPage");
	}

	/**
	 * 删除mdmReportFolder信息
	 *
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "del")
	@ResponseBody
	public AjaxJson del(String foldId,HttpServletRequest request) {
		AjaxJson j=new AjaxJson();
		try {

			if(mdmReportFolderService.deleteById(foldId)>0){
				MdmReportFolder mdmReportFolder = new MdmReportFolder();
				mdmReportFolder.setParentId(foldId);
				mdmReportFolderService.delete(mdmReportFolder);
				j.setMsg("Successfully deleted");
			}else{
				j.setSuccess(false);
				j.setMsg("Failed to delete");
			}
		}
		catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg("Abnormal operation");
		}
		return j;
	}

	/**
	 * 删除mdmReportFolder信息
	 *
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "reviseFolder")
	@ResponseBody
	public AjaxJson reviseFolder(String foldId,String foldName,String flag,HttpServletRequest request) {
		AjaxJson j=new AjaxJson();
		try {
			if("1".equals(flag)){
				MdmReportFolder mdmReportFolder = new MdmReportFolder();
				mdmReportFolder.setParentId(foldId);

				List<MdmReportFolder> mdmReportFolderList = mdmReportFolderService.getList(mdmReportFolder);
				for(MdmReportFolder mdmReportFolder1 : mdmReportFolderList){
					if(foldName.equals(mdmReportFolder1.getName())){
						j.setSuccess(false);
						j.setMsg("Folder Name is Existed");
						return j;
					}
				}

				mdmReportFolder.setName(foldName);

				mdmReportFolderService.save(mdmReportFolder);
			}else if("2".equals(flag)){
				MdmReportFolder mdmReportFolder = mdmReportFolderService.getEntity(foldId);

				MdmReportFolder mdmReportFolder1 = new MdmReportFolder();
				mdmReportFolder1.setParentId(mdmReportFolder.getParentId());

				List<MdmReportFolder> mdmReportFolderList = mdmReportFolderService.getList(mdmReportFolder1);
				for(MdmReportFolder mdmReportFolder2 : mdmReportFolderList){
					if(foldName.equals(mdmReportFolder2.getName())){
						j.setSuccess(false);
						j.setMsg("Folder Name is Existed");
						return j;
					}
				}

				mdmReportFolder.setName(foldName);

				mdmReportFolderService.update(mdmReportFolder);
			}

			j.setMsg("Successfully Operate");

		}
		catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg("Abnormal operation");
		}
		return j;
	}




	/**
     * 保存mdmReportFolder信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })MdmReportFolder mdmReportFolder, BindingResult bindingResult, HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        MdmReportFolder t=new  MdmReportFolder();
        try {
       SysUser su= TokenManager.getToken();
        if(StringUtil.isNotEmpty(mdmReportFolder.getId())){
        	t=mdmReportFolderService.getEntity(mdmReportFolder.getId());
			MyBeanUtils.copyBeanNotNull2Bean(mdmReportFolder, t);
				mdmReportFolderService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            mdmReportFolderService.save(mdmReportFolder);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    
    /**
 	 * 获取报表目录的树形结构 选择框
 	 * @param searchName
 	 * @param request
 	 * @return
 	 */
 	@RequestMapping(value = "getReportFolderZtree")
 	@ResponseBody
 	public AjaxJson getReportFolderZtree(String orgSearchInputId, HttpServletRequest request) {
 		AjaxJson j = new AjaxJson();
 		
 		try {

 	 		MdmReportFolder entity = new MdmReportFolder();
 			entity.setParentId("0");
 			List<MdmReportFolder> mdmReportFolderList = mdmReportFolderService.getList(entity);
 		
 			getReportFolderZtreeData(mdmReportFolderList,null);
 			
 			ZtreeUtil jtu = new ZtreeUtil();
 			jtu.setIdFname("id");
 			jtu.setNameFname("name");
 			jtu.setpIdFname("parentId");
 			jtu.setSubsetFieldName("mdmReportFolderList");
 			jtu.setOpenFname("expanded");
 			j.setObj(jtu.getZtreeNodeData(mdmReportFolderList));
 			System.out.println(" json tree --> " + j.toString());
 		} catch (Exception e) {
 			e.printStackTrace();
 		}
 		return j;
 	}
 	
 	
 	  /**
 	 	 * 获取报表目录的树形结构 固定框增删改查
 	 	 * @param searchName
 	 	 * @param request
 	 	 * @return
 	 	 */
 	 	@RequestMapping(value = "getReportFolderOpZtree")
 	 	@ResponseBody
 	 	public AjaxJson getReportFolderOpZtree(String orgSearchInputId, HttpServletRequest request) {
 	 		AjaxJson j = new AjaxJson();
 			List<Map<String, Object>> list = new ArrayList<>();

 	 		try {
 	 			getReportFolderZtreeOpData(request,list);
 	 			
 	 			ZtreeUtil jtu = new ZtreeUtil();
 	 			jtu.setIdFname("id");
 	 			jtu.setNameFname("name");
 	 			jtu.setpIdFname("parent");
 	 			jtu.setSubsetFieldName("list");
 	 			jtu.setOpenFname("expanded");
 	 			jtu.setIconFname("iconFname");
 	 			jtu.setIsParentFname("isParent");
 	 			jtu.setIsAjaxingFname("isAjaxing");
 	 			j.setObj(jtu.getZtreeNodeData(list));
 	 			System.out.println(" json tree --> " + j.toString());
 	 		} catch (Exception e) {
 	 			e.printStackTrace();
 	 		}
 	 		return j;
 	 	}
 	 	
 	

	/**
	 * 递归方法，查询组织机构数据,弹出页的ztree
	 * @Description 
	 * @param list void
	 * <AUTHOR> 
	 * @Time 2018年3月22日 下午7:04:55
	 */
	public void getReportFolderZtreeData(List<MdmReportFolder> list,String orgCode){
		for (MdmReportFolder mdmReportFolder : list) {
			mdmReportFolder.setExpanded(true);	//默认false
			MdmReportFolder entity = new MdmReportFolder();
		
            entity.setParentId(mdmReportFolder.getId());
            List<MdmReportFolder> mdmReportFolderList = mdmReportFolderService.getList(entity);
            if(mdmReportFolderList.size() > 0){
            	getReportFolderZtreeData(mdmReportFolderList,orgCode);
            	mdmReportFolder.setMdmReportFolderList(mdmReportFolderList);
            }
		}
	}
	
	
	/**
	 * 递归方法，查询组织机构数据,弹出页的ztree
	 * @Description 
	 * @param list void
	 * <AUTHOR> 
	 * @Time 2018年3月22日 下午7:04:55
	 */
	public void getReportFolderZtreeOpData(HttpServletRequest request,List<Map<String, Object>> list){
		
		String path = request.getContextPath();
 		MdmReportFolder entity = new MdmReportFolder();
		entity.setParentId("0");
		
		List<MdmReportFolder> mdmReportFolderList = mdmReportFolderService.getList(entity);
		
		for (MdmReportFolder mdmReportFolder : mdmReportFolderList) {
				Map<String, Object> m = new HashMap<String, Object>();
				m.put("id", mdmReportFolder.getId());
				m.put("name", mdmReportFolder.getName());
				m.put("parent", "");
				m.put("isParent", true);
				m.put("expanded", true);
				m.put("isAjaxing", true);
				m.put("functionUrl", "one");
				
				m.put("iconFname", path + "/theme/img/org.png");
		
				MdmReportFolder entitySon = new MdmReportFolder();
				entitySon.setParentId(mdmReportFolder.getId());
				List<MdmReportFolder> mdmReportFolderSonList = mdmReportFolderService.getList(entitySon);
				
				List<Map<String, Object>> listk = new ArrayList<>();
				
				for (MdmReportFolder mdmReportFolderSon : mdmReportFolderSonList) {
					Map<String, Object> dm = new HashMap<String, Object>();
					dm.put("id", mdmReportFolderSon.getId());
					dm.put("name", mdmReportFolderSon.getName());
					dm.put("parent", mdmReportFolder.getId());
					dm.put("isParent", false);
					dm.put("expanded", false);
					dm.put("isAjaxing", true);
					dm.put("functionUrl", "two");
					
					dm.put("iconFname", path + "/theme/img/org.png");
					
					MdmReportFolder entityGrandSon = new MdmReportFolder();
					entityGrandSon.setParentId(mdmReportFolderSon.getId());
					List<MdmReportFolder> mdmReportFolderGrandSonList = mdmReportFolderService.getList(entityGrandSon);
					
					if(mdmReportFolderGrandSonList != null && mdmReportFolderGrandSonList.size() > 0){
						dm.put("isParent", true);
					}
					
					List<Map<String, Object>> listkk = new ArrayList<>();
					
					for (MdmReportFolder mdmReportFolderGrandSon : mdmReportFolderGrandSonList) {
						Map<String, Object> ddm = new HashMap<String, Object>();
						ddm.put("id", mdmReportFolderGrandSon.getId());
						ddm.put("name", mdmReportFolderGrandSon.getName());
						ddm.put("parent", mdmReportFolderSon.getId());
						ddm.put("isParent", false);
						ddm.put("expanded", false);
						ddm.put("isAjaxing", true);
						ddm.put("functionUrl", "three");
						
						ddm.put("iconFname", path + "/theme/img/org.png");
						
						listkk.add(ddm);
					}
					
					if(listkk.size()>0){
						dm.put("list", listkk);
						listk.add(dm);
					}else{
						listk.add(dm);
					}
					
				}
				
				if(listk.size()>0){
					m.put("list", listk);
					list.add(m);
				}
		}
	}
	
	
	  /**
	 	 * 获取报表目录的树形结构 固定框增删改查 带虚拟节点
	 	 * @param searchName
	 	 * @param request
	 	 * @return
	 	 */
	 	@RequestMapping(value = "getReportFolderOpZtree1")
	 	@ResponseBody
	 	public AjaxJson getReportFolderOpZtree1(String orgSearchInputId, HttpServletRequest request) {
	 		AjaxJson j = new AjaxJson();
			List<Map<String, Object>> list = new ArrayList<>();

	 		try {
	 			getReportFolderZtreeOpData1(request,list);
	 			
	 			ZtreeUtil jtu = new ZtreeUtil();
	 			jtu.setIdFname("id");
	 			jtu.setNameFname("name");
	 			jtu.setpIdFname("parent");
	 			jtu.setSubsetFieldName("list");
	 			jtu.setOpenFname("expanded");
	 			jtu.setIconFname("iconFname");
	 			jtu.setIsParentFname("isParent");
	 			jtu.setIsAjaxingFname("isAjaxing");
	 			j.setObj(jtu.getZtreeNodeData(list));
	 			System.out.println(" json tree --> " + j.toString());
	 		} catch (Exception e) {
	 			e.printStackTrace();
	 		}
	 		return j;
	 	}
	
	 	
	 	/**
		 * 递归方法，查询组织机构数据,弹出页的ztree 带虚拟节点
		 * @Description 
		 * @param list void
		 * <AUTHOR> 
		 * @Time 2018年3月22日 下午7:04:55
		 */
		public void getReportFolderZtreeOpData1(HttpServletRequest request,List<Map<String, Object>> list){

			SysUser su= TokenManager.getToken();
			
			String path = request.getContextPath();
	 		MdmReportFolder entity = new MdmReportFolder();
			entity.setParentId("0");
			List<MdmReportFolder> mdmReportFolderList = mdmReportFolderService.getList(entity);	

			 boolean isRoleExist = false;
			 
			 if(su.getUserType()== EnumUserType.SUPER_USER.getIndex()){
				 isRoleExist = true;
			 }
			 
			for (MdmReportFolder mdmReportFolder : mdmReportFolderList) {
					Map<String, Object> m = new HashMap<String, Object>();
					m.put("id", mdmReportFolder.getId());
					m.put("name", mdmReportFolder.getName());
					m.put("parent", "");
					m.put("isParent", true);
					m.put("expanded", true);
					m.put("isAjaxing", true);
					m.put("functionUrl", "one");
					
					m.put("iconFname", path + "/theme/img/org.png");
			        //老子开始
					MdmReportFolder entitySon = new MdmReportFolder();
					entitySon.setParentId(mdmReportFolder.getId());
					List<MdmReportFolder> mdmReportFolderSonList = mdmReportFolderService.getList(entitySon);	
					
					List<Map<String, Object>> listk = new ArrayList<>();
		            
					AssetReportTemplate assetReportTemplate = new AssetReportTemplate();
					assetReportTemplate.setFolderId(mdmReportFolder.getId());
					assetReportTemplate.setReportType(new BigDecimal(1));

					 if(!isRoleExist){
						 assetReportTemplate.setRoleId(su.getRoleId());
					 }
					 List<AssetReportTemplate> assetReportTemplateDailyList = assetReportTemplateService.getList(assetReportTemplate);
					 
					 AssetReportTemplate assetReportTemplate1 = new AssetReportTemplate();
					 assetReportTemplate1.setFolderId(mdmReportFolder.getId());
					 assetReportTemplate1.setReportType(new BigDecimal(2));
					 if(!isRoleExist){
						 assetReportTemplate1.setRoleId(su.getRoleId());
					 }
					 List<AssetReportTemplate> assetReportTemplateMonthlyList = assetReportTemplateService.getList(assetReportTemplate1);
					 
					 
					 //有问题的地方  循环后用新的list
					 
						 
					 	String dailyReportId =  mdmReportFolder.getId()+":DailyReport";
						String monthlyReportId =  mdmReportFolder.getId()+":MonthlyReport";
						
						if(assetReportTemplateDailyList != null && assetReportTemplateDailyList.size() > 0){
							Map<String, Object> dm = new HashMap<String, Object>();
							dm.put("id", dailyReportId);
							dm.put("name", MutiLangUtil.doMutiLang("index.dailyReport"));
							dm.put("parent", mdmReportFolder.getId());
							dm.put("expanded", false);
							dm.put("functionUrl", "dailyReportV");
							dm.put("iconFname", path + "/theme/img/org-lines.png");
							dm.put("isParent", true);
							
							
							List<Map<String, Object>> listDaily = new ArrayList<>();
							
							 for(AssetReportTemplate assetReportTemplateDay : assetReportTemplateDailyList){
									Map<String, Object> dm1 = new HashMap<String, Object>();
									dm1.put("id", assetReportTemplateDay.getId());
									dm1.put("name", assetReportTemplateDay.getReportName());
									dm1.put("parent", dailyReportId);
									dm1.put("expanded", false);
									dm1.put("functionUrl", "dailyReport");
									dm1.put("iconFname", request.getContextPath() + "/theme/img/forms.png");
									listDaily.add(dm1);
							 }
							 
							 if(listDaily.size()>0){
									dm.put("list", listDaily);
									listk.add(dm);
							 }else{
								 listk.add(dm);
							 }
							 
						}
						
						if(assetReportTemplateMonthlyList !=null && assetReportTemplateMonthlyList.size() > 0){
							Map<String, Object> dm = new HashMap<String, Object>();
							dm.put("id", monthlyReportId);
							dm.put("name", MutiLangUtil.doMutiLang("index.monthlyReport"));
							dm.put("parent", mdmReportFolder.getId());
							dm.put("expanded", false);
							dm.put("functionUrl", "monthlyReportV");
							dm.put("iconFname", path + "/theme/img/org-lines.png");
							dm.put("isParent", true);

							List<Map<String, Object>> listMonthly = new ArrayList<>();
							
							 for(AssetReportTemplate assetReportTemplateMonth : assetReportTemplateMonthlyList){
									Map<String, Object> dm1 = new HashMap<String, Object>();
									dm1.put("id", assetReportTemplateMonth.getId());
									dm1.put("name", assetReportTemplateMonth.getReportName());
									dm1.put("parent", monthlyReportId);
									dm1.put("expanded", false);
									dm1.put("functionUrl", "monthlyReport");
									dm1.put("iconFname", request.getContextPath() + "/theme/img/forms.png");
									listMonthly.add(dm1);
							 }
							 
							 if(listMonthly.size()>0){
									dm.put("list", listMonthly);
									listk.add(dm);
							 }else{
								 listk.add(dm);
							 }
							
						}
						
					
					
					for (MdmReportFolder mdmReportFolderSon : mdmReportFolderSonList) {
						Map<String, Object> dm = new HashMap<String, Object>();
						dm.put("id", mdmReportFolderSon.getId());
						dm.put("name", mdmReportFolderSon.getName());
						dm.put("parent", mdmReportFolder.getId());
						dm.put("isParent", false);
						dm.put("expanded", false);
						dm.put("isAjaxing", true);
						dm.put("functionUrl", "two");
						
						dm.put("iconFname", path + "/theme/img/org.png");
						
						//儿子开始
						MdmReportFolder entityGrandSon = new MdmReportFolder();
						entityGrandSon.setParentId(mdmReportFolderSon.getId());
						List<MdmReportFolder> mdmReportFolderGrandSonList = mdmReportFolderService.getList(entityGrandSon);	
						

						List<Map<String, Object>> listkk = new ArrayList<>();
						
						if(mdmReportFolderGrandSonList != null && mdmReportFolderGrandSonList.size() > 0){
							dm.put("isParent", true);
						}
						

						 AssetReportTemplate assetReportTemplateSon = new AssetReportTemplate();
						 assetReportTemplateSon.setFolderId(mdmReportFolderSon.getId());
						 assetReportTemplateSon.setReportType(new BigDecimal(1));
						 if(!isRoleExist){
							 assetReportTemplateSon.setRoleId(su.getRoleId());
						 }
						 List<AssetReportTemplate> assetReportTemplateDailyListSon = assetReportTemplateService.getList(assetReportTemplateSon);
						 
						 AssetReportTemplate assetReportTemplateSon1 = new AssetReportTemplate();
						 assetReportTemplateSon1.setFolderId(mdmReportFolderSon.getId());
						 assetReportTemplateSon1.setReportType(new BigDecimal(2));
						 if(!isRoleExist){
							 assetReportTemplateSon1.setRoleId(su.getRoleId());
						 }
						 List<AssetReportTemplate> assetReportTemplateMonthlyListSon = assetReportTemplateService.getList(assetReportTemplateSon1);

                         if((assetReportTemplateDailyListSon != null && assetReportTemplateDailyListSon.size() > 0)
                        		 || (assetReportTemplateMonthlyListSon != null && assetReportTemplateMonthlyListSon.size() > 0)){
                        		dm.put("isParent", true);
                         }
						 
						 //有问题的地方  循环后用新的list
					 	String dailyReportSonId =  mdmReportFolderSon.getId()+":DailyReport";
						String monthlyReportSonId =  mdmReportFolderSon.getId()+":MonthlyReport";
						
						if(assetReportTemplateDailyListSon != null && assetReportTemplateDailyListSon.size() > 0){
							Map<String, Object> dmsonDaily = new HashMap<String, Object>();
							dmsonDaily.put("id", dailyReportSonId);
							dmsonDaily.put("name", MutiLangUtil.doMutiLang("index.dailyReport"));
							dmsonDaily.put("parent", mdmReportFolderSon.getId());
							dmsonDaily.put("expanded", false);
							dmsonDaily.put("functionUrl", "dailyReportV");
							dmsonDaily.put("iconFname", path + "/theme/img/org-lines.png");
							dmsonDaily.put("isParent", true);
							
							
							List<Map<String, Object>> listDailySon = new ArrayList<>();
							
							 for(AssetReportTemplate assetReportTemplateDay : assetReportTemplateDailyListSon){
									Map<String, Object> dm1 = new HashMap<String, Object>();
									dm1.put("id", assetReportTemplateDay.getId());
									dm1.put("name", assetReportTemplateDay.getReportName());
									dm1.put("parent", dailyReportSonId);
									dm1.put("expanded", false);
									dm1.put("functionUrl", "dailyReport");
									dm1.put("iconFname", request.getContextPath() + "/theme/img/forms.png");
									listDailySon.add(dm1);
							 }
							 
							 if(listDailySon.size()>0){
								 dmsonDaily.put("list", listDailySon);
								 listkk.add(dmsonDaily);
							 }else{
								 listkk.add(dmsonDaily);
							 }
							 
						}
						
						if(assetReportTemplateMonthlyListSon !=null && assetReportTemplateMonthlyListSon.size() > 0){
							Map<String, Object> dmMonthlySon = new HashMap<String, Object>();
							dmMonthlySon.put("id", monthlyReportSonId);
							dmMonthlySon.put("name", MutiLangUtil.doMutiLang("index.monthlyReport"));
							dmMonthlySon.put("parent", mdmReportFolderSon.getId());
							dmMonthlySon.put("expanded", false);
							dmMonthlySon.put("functionUrl", "monthlyReportV");
							dmMonthlySon.put("iconFname", path + "/theme/img/org-lines.png");
							dmMonthlySon.put("isParent", true);

							List<Map<String, Object>> listMonthlySon = new ArrayList<>();
							
							 for(AssetReportTemplate assetReportTemplateMonth : assetReportTemplateMonthlyListSon){
									Map<String, Object> dm1 = new HashMap<String, Object>();
									dm1.put("id", assetReportTemplateMonth.getId());
									dm1.put("name", assetReportTemplateMonth.getReportName());
									dm1.put("parent", monthlyReportSonId);
									dm1.put("expanded", false);
									dm1.put("functionUrl", "monthlyReport");
									dm1.put("iconFname", request.getContextPath() + "/theme/img/forms.png");
									listMonthlySon.add(dm1);
							 }
							 
							 if(listMonthlySon.size()>0){
								 dmMonthlySon.put("list", listMonthlySon);
								 listkk.add(dmMonthlySon);
							 }else{
								 listkk.add(dmMonthlySon);
							 }
							
						}
								 
							 
						
						for (MdmReportFolder mdmReportFolderGrandSon : mdmReportFolderGrandSonList) {
							Map<String, Object> ddm = new HashMap<String, Object>();
							ddm.put("id", mdmReportFolderGrandSon.getId());
							ddm.put("name", mdmReportFolderGrandSon.getName());
							ddm.put("parent", mdmReportFolderSon.getId());
							ddm.put("isParent", false);
							ddm.put("expanded", false);
							ddm.put("isAjaxing", true);
							ddm.put("functionUrl", "three");
							
							ddm.put("iconFname", path + "/theme/img/org.png");
							
							//孙子开始
							
							List<Map<String, Object>> listkkk = new ArrayList<>();
							
							 AssetReportTemplate assetReportTemplateGrandSon = new AssetReportTemplate();
							 assetReportTemplateGrandSon.setFolderId(mdmReportFolderGrandSon.getId());
							 assetReportTemplateGrandSon.setReportType(new BigDecimal(1));
							 if(!isRoleExist){
								 assetReportTemplateGrandSon.setRoleId(su.getRoleId());
							 }
							 List<AssetReportTemplate> assetReportTemplateDailyListGrandSon = assetReportTemplateService.getList(assetReportTemplateGrandSon);
							 
							 AssetReportTemplate assetReportTemplateGrandSon1 = new AssetReportTemplate();
							 assetReportTemplateGrandSon1.setFolderId(mdmReportFolderGrandSon.getId());
							 assetReportTemplateGrandSon1.setReportType(new BigDecimal(2));
							 if(!isRoleExist){
								 assetReportTemplateGrandSon1.setRoleId(su.getRoleId());
							 }
							 List<AssetReportTemplate> assetReportTemplateMonthlyListGrandSon = assetReportTemplateService.getList(assetReportTemplateGrandSon1);
							 
							 //有问题的地方  循环后用新的list
							 
							  if((assetReportTemplateDailyListGrandSon != null && assetReportTemplateDailyListGrandSon.size() > 0)
		                        		 || (assetReportTemplateMonthlyListGrandSon != null && assetReportTemplateMonthlyListGrandSon.size() > 0)){
								  ddm.put("isParent", true);
		                      }
							  
						 	String dailyReportGrandSonId =  mdmReportFolderGrandSon.getId()+":DailyReport";
							String monthlyReportGrandSonId =  mdmReportFolderGrandSon.getId()+":MonthlyReport";
							
							if(assetReportTemplateDailyListGrandSon != null && assetReportTemplateDailyListGrandSon.size() > 0){
								Map<String, Object> dmGrandsonDaily = new HashMap<String, Object>();
								dmGrandsonDaily.put("id", dailyReportGrandSonId);
								dmGrandsonDaily.put("name", MutiLangUtil.doMutiLang("index.dailyReport"));
								dmGrandsonDaily.put("parent", mdmReportFolderGrandSon.getId());
								dmGrandsonDaily.put("expanded", false);
								dmGrandsonDaily.put("functionUrl", "dailyReportV");
								dmGrandsonDaily.put("iconFname", path + "/theme/img/org-lines.png");
								dmGrandsonDaily.put("isParent", true);
								
								List<Map<String, Object>> listDailyGrandSon = new ArrayList<>();
								
								 for(AssetReportTemplate assetReportTemplateDay : assetReportTemplateDailyListGrandSon){
										Map<String, Object> dm1 = new HashMap<String, Object>();
										dm1.put("id", assetReportTemplateDay.getId());
										dm1.put("name", assetReportTemplateDay.getReportName());
										dm1.put("parent", dailyReportGrandSonId);
										dm1.put("expanded", false);
										dm1.put("functionUrl", "dailyReport");
										dm1.put("iconFname", request.getContextPath() + "/theme/img/forms.png");
										listDailyGrandSon.add(dm1);
								 }
								 
								 if(listDailyGrandSon.size()>0){
									 dmGrandsonDaily.put("list", listDailyGrandSon);
									 listkkk.add(dmGrandsonDaily);
								 }else{
									 listkkk.add(dmGrandsonDaily);
								 }
								 
							}
							
							if(assetReportTemplateMonthlyListGrandSon !=null && assetReportTemplateMonthlyListGrandSon.size() > 0){
								Map<String, Object> dmMonthlyGrandSon = new HashMap<String, Object>();
								dmMonthlyGrandSon.put("id", monthlyReportGrandSonId);
								dmMonthlyGrandSon.put("name", MutiLangUtil.doMutiLang("index.monthlyReport"));
								dmMonthlyGrandSon.put("parent", mdmReportFolderGrandSon.getId());
								dmMonthlyGrandSon.put("expanded", false);
								dmMonthlyGrandSon.put("functionUrl", "monthlyReportV");
								dmMonthlyGrandSon.put("iconFname", path + "/theme/img/org-lines.png");
								dmMonthlyGrandSon.put("isParent", true);

								List<Map<String, Object>> listMonthlyGrandSon = new ArrayList<>();
								
								 for(AssetReportTemplate assetReportTemplateMonth : assetReportTemplateMonthlyListSon){
										Map<String, Object> dm1 = new HashMap<String, Object>();
										dm1.put("id", assetReportTemplateMonth.getId());
										dm1.put("name", assetReportTemplateMonth.getReportName());
										dm1.put("parent", monthlyReportGrandSonId);
										dm1.put("expanded", false);
										dm1.put("functionUrl", "monthlyReport");
										dm1.put("iconFname", request.getContextPath() + "/theme/img/forms.png");
										listMonthlyGrandSon.add(dm1);
								 }
								 
								 if(listMonthlyGrandSon.size()>0){
									 dmMonthlyGrandSon.put("list", listMonthlyGrandSon);
									 listkkk.add(dmMonthlyGrandSon);
								 }else{
									 listkkk.add(dmMonthlyGrandSon);
								 }
								
							}
							
							
							 if(listkkk.size()>0){
								 ddm.put("list", listkkk);
								 listkk.add(ddm);
							 }else{
								 listkk.add(ddm);
							 }
							
						}
						
						if(listkk.size()>0){
							dm.put("list", listkk);
							listk.add(dm);
						}else{
							listk.add(dm);
						}
						
					}
					
					if(listk.size()>0){
						m.put("list", listk);
						list.add(m);
					}
			}
		}
		
	
	
}