package clouesp.hes.core.uci.soap.custom.webservice.meterdefineconfig.response;

import java.io.UnsupportedEncodingException;

import com.clou.esp.hes.app.web.core.pushlet.PushletData;
import com.clou.esp.hes.app.web.model.common.EchartTreeVo;
import com.power7000g.core.util.json.AjaxJson;

import ch.iec.tc57._2011.meterdefineconfigmessage.MeterDefineConfigResponseMessageType;
import ch.iec.tc57._2011.schema.message.HeaderType;
import ch.iec.tc57._2011.schema.message.ReplyType;
import clouesp.hes.core.uci.soap.custom.webservice.abstractt.SoapRecvAbstract;


/**
 * @ClassName: MeterDefineConfigResponseAbstract
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年7月18日 下午1:38:58
 *
 */
public abstract class MeterDefineConfigResponseAbstract extends SoapRecvAbstract {
	
	@Override
	public void parseHeader() {
		// TODO Auto-generated method stub
		MeterDefineConfigResponseMessageType responseMessageType = (MeterDefineConfigResponseMessageType) this.object;
		HeaderType headerType = responseMessageType.getHeader();
		
		this.verb = headerType.getVerb();
		this.noun = headerType.getNoun();
		this.timestamp = headerType.getTimestamp();
		this.source = headerType.getSource();
		this.userId = headerType.getUser().getUserID();
		this.organization = headerType.getUser().getOrganization();
		this.messageId = headerType.getMessageID();
		this.correlationId = headerType.getCorrelationID();
	}
	
	@Override
	public void parseReply() {
		// TODO Auto-generated method stub
		MeterDefineConfigResponseMessageType responseMessageType = (MeterDefineConfigResponseMessageType) this.object;
		ReplyType reply = responseMessageType.getReply();
		if(null != reply){
			this.result = reply.getResult();
			this.code = reply.getError().get(0).getCode();
		}
	}
	
	@Override
	public void pushlet(AjaxJson json) {
		// TODO Auto-generated method stub
		try {
			//todo edison dataitemId 去区分pushlet渠道
			
			if(json.getAttributes().containsKey("dataItemId") && "200.200.200.200".equals(json.getAttributes().get("dataItemId")))
			{
				PushletData.pushlet("deviceDebugPushletChannel", json, userId);
			}else if(json.getAttributes().containsKey("dataItemId")
					&& ("40.0.7.61".equals(json.getAttributes().get("dataItemId"))
							|| "40.0.7.62".equals(json.getAttributes().get("dataItemId"))
							|| "40.0.7.63".equals(json.getAttributes().get("dataItemId"))
							|| "40.0.7.64".equals(json.getAttributes().get("dataItemId"))
							|| "40.0.7.65".equals(json.getAttributes().get("dataItemId"))
							|| "40.0.7.66".equals(json.getAttributes().get("dataItemId"))
							|| "40.0.7.67".equals(json.getAttributes().get("dataItemId"))
							|| "40.0.7.68".equals(json.getAttributes().get("dataItemId"))
							|| "40.0.7.69".equals(json.getAttributes().get("dataItemId"))))
			{
				PushletData.pushlet("moduleConfigPushletChannel", json, userId);
			}else{
				if(json.getAttributes().containsKey("dataItemId")&& ("40.0.7.42".equals(json.getAttributes().get("dataItemId")))){
					EchartTreeVo.toTree(json,this.sn); 
				}
				PushletData.pushlet("dcuConfigPushletChannel", json, userId);
			}
		} catch (UnsupportedEncodingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
}
