package com.clou.esp.hes.app.web.model.asset;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.power7000g.core.excel.Excel;

public class AssetMeterTemplate extends BaseEntity{
    private static final long serialVersionUID = 1L;
    // 电表sn
	@Excel(name = "Serial Number", width = 30)
	private String serialNumber;
	
	// 通信地址，此地址插入到ppm_asset_meter表中
	@Excel(name = "STS Address", width = 30)
	private String commAddress; 
	
	@Excel(name = "Logical Address", width = 30)
	private String mac;
	
	@Excel(name = "Delivery Date", width = 30)
	private String deliveryDate;
	
	@Excel(name = "Device Type", width = 30)
	private String deviceType;
	
	@Excel(name = "Communication Method", width = 30)
	private String comType;
	
	@Excel(name = "Meter FW", width = 30)
	private String firmwareVersion;
	
	@Excel(name = "Meter HW", width = 30)
	private String meterHWVer;
	
	@Excel(name = "Module SN", width = 30)
	private String moduleSn;
	
	@Excel(name = "Module HW", width = 30)
	private String moduleHw;
	
	@Excel(name = "Module FW", width = 30)
	private String moduleFw;
	
	@Excel(name = "GPRS IMEI", width = 30)
	private String gprsModemIMEI;
	
	// ak
	@Excel(name = "Authentication Key", width = 30)
	private String ak;

	// ek
	@Excel(name = "Encryption Key", width = 30)
	private String ek;
	
	// key Meter
	@Excel(name = "Key Meter", width = 30)
	private String keyMeter;
	
	// key对应的id值
	private String deviceTypeId;
	private String comTypeId;
	private String authTypeId;
	private Integer keyMeterId;
	private String manufacturerId;
	
	public String getManufacturerId() {
		return manufacturerId;
	}

	public void setManufacturerId(String manufacturerId) {
		this.manufacturerId = manufacturerId;
	}

	public String getSerialNumber() {
		return serialNumber;
	}

	public void setSerialNumber(String serialNumber) {
		this.serialNumber = serialNumber;
	}

	public String getCommAddress() {
		return commAddress;
	}

	public void setCommAddress(String commAddress) {
		this.commAddress = commAddress;
	}

	public String getFirmwareVersion() {
		return firmwareVersion;
	}

	public void setFirmwareVersion(String firmwareVersion) {
		this.firmwareVersion = firmwareVersion;
	}

	public String getMeterHWVer() {
		return meterHWVer;
	}

	public void setMeterHWVer(String meterHWVer) {
		this.meterHWVer = meterHWVer;
	}

	public String getMac() {
		return mac;
	}

	public void setMac(String mac) {
		this.mac = mac;
	}

	public String getDeviceType() {
		return deviceType;
	}

	public void setDeviceType(String deviceType) {
		this.deviceType = deviceType;
	}

	public String getComType() {
		return comType;
	}

	public void setComType(String comType) {
		this.comType = comType;
	}

	public String getModuleFw() {
		return moduleFw;
	}

	public void setModuleFw(String moduleFw) {
		this.moduleFw = moduleFw;
	}

	public String getGprsModemIMEI() {
		return gprsModemIMEI;
	}

	public void setGprsModemIMEI(String gprsModemIMEI) {
		this.gprsModemIMEI = gprsModemIMEI;
	}


	public String getKeyMeter() {
		return keyMeter;
	}

	public void setKeyMeter(String keyMeter) {
		this.keyMeter = keyMeter;
	}

	public String getAuthTypeId() {
		return authTypeId;
	}

	public void setAuthTypeId(String authTypeId) {
		this.authTypeId = authTypeId;
	}

	public String getDeliveryDate() {
		return deliveryDate;
	}

	public void setDeliveryDate(String deliveryDate) {
		this.deliveryDate = deliveryDate;
	}

	public String getModuleSn() {
		return moduleSn;
	}

	public void setModuleSn(String moduleSn) {
		this.moduleSn = moduleSn;
	}

	public String getModuleHw() {
		return moduleHw;
	}

	public void setModuleHw(String moduleHw) {
		this.moduleHw = moduleHw;
	}

	public String getAk() {
		return ak;
	}

	public void setAk(String ak) {
		this.ak = ak;
	}

	public String getEk() {
		return ek;
	}

	public void setEk(String ek) {
		this.ek = ek;
	}

	public Integer getKeyMeterId() {
		return keyMeterId;
	}

	public void setKeyMeterId(Integer keyMeterId) {
		this.keyMeterId = keyMeterId;
	}

	public String getDeviceTypeId() {
		return deviceTypeId;
	}

	public void setDeviceTypeId(String deviceTypeId) {
		this.deviceTypeId = deviceTypeId;
	}

	public String getComTypeId() {
		return comTypeId;
	}

	public void setComTypeId(String comTypeId) {
		this.comTypeId = comTypeId;
	}
}
