/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictOperation{ } 
 * 
 * 摘    要： 操作权限字典表
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-01 09:42:07
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.system;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.model.system.DictOperation;
import com.clou.esp.hes.app.web.service.system.DictOperationService;

/**
 * <AUTHOR>
 * @时间：2017-11-01 09:42:07
 * @描述：操作权限字典表类
 */
@Controller
@RequestMapping("/dictOperationController")
public class DictOperationController extends BaseController{

 	@Resource
    private DictOperationService dictOperationService;

	/**
	 * 跳转到操作权限字典表列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/system/dictOperationList");
    }

	/**
	 * 跳转到操作权限字典表新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dictOperation")
	public ModelAndView dictOperation(DictOperation dictOperation,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dictOperation.getId())){
			try {
                dictOperation=dictOperationService.getEntity(dictOperation.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dictOperation", dictOperation);
		}
		return new ModelAndView("/system/dictOperation");
	}


	/**
	 * 操作权限字典表查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        String functionId=request.getParameter("functionId");
        jqGridSearchTo.put("functionId", functionId);
        try {
             j=dictOperationService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除操作权限字典表信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DictOperation dictOperation,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dictOperationService.deleteById(dictOperation.getId())>0){
                j.setMsg("删除成功");
            }else{
                j.setSuccess(false);
                j.setMsg("删除失败");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
    
    /**
     * 保存操作权限字典表信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DictOperation dictOperation,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DictOperation t=new  DictOperation();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dictOperation.getId())){
        	t=dictOperationService.getEntity(dictOperation.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dictOperation, t);
				dictOperationService.update(t);
				j.setMsg("修改成功");
				
			}else{
				
				DictOperation dop=new DictOperation();
				dop.setFunctionId(dictOperation.getFunctionId());
				Long count = dictOperationService.getCount(dop);
				dictOperation.setId(dictOperation.getFunctionId()+StringUtil.leftFillStr(""+(count+1), '0', 3));
	            dictOperationService.save(dictOperation);
	            j.setMsg("创建成功");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
	
	
	
}