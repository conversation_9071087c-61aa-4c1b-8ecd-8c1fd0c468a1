/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictProtocol{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-01-16 02:01:08
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.dict;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.dict.DictProtocolDao;
import com.clou.esp.hes.app.web.model.dict.DictProtocol;
import com.clou.esp.hes.app.web.service.dict.DictProtocolService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dictProtocolService")
public class DictProtocolServiceImpl  extends CommonServiceImpl<DictProtocol>  implements DictProtocolService {

	@Resource
	private DictProtocolDao dictProtocolDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dictProtocolDao);
    }
	@SuppressWarnings("rawtypes")
	public DictProtocolServiceImpl() {}
	
	
}