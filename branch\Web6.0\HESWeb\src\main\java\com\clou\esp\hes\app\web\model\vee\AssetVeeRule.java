/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetVeeRule{ } 
 * 
 * 摘    要： assetVeeRule
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-12-20 04:22:02
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.vee;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class AssetVeeRule  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public AssetVeeRule() {
	}

	/**name*/
	private java.lang.String name;
	/**mgId*/
	private java.lang.String mgId;
	/**methodId*/
	private java.lang.String methodId;
	
	/**classId*/
	private java.math.BigDecimal classId;
	/**ruleDetail*/
	private java.lang.String ruleDetail;
	/**eventId*/
	private java.lang.String eventId;
	/**dataitemId*/
	private java.lang.String dataitemId;
	/**descr*/
	private java.lang.String descr;
	/**introduction*/
	private java.lang.String introduction;
	
	
	/**ruleType*/
	private java.math.BigDecimal ruleType;
	/**ruleType*/
	private java.math.BigDecimal ruleStatus;
	/**ruleType*/
	private java.lang.String ruleStatusName;
	/**电表数量*/
	private Long meterNumber;

	/**rule Type name*/
	private java.lang.String ruleTypeName;
	
	/**method name*/
	private java.lang.String methodName;
	
	/**data channel*/
	private java.lang.String dataChannel;
	
	/**event name*/
	private java.lang.String eventName;
	

	
	public java.lang.String getRuleStatusName() {
		return ruleStatusName;
	}

	public void setRuleStatusName(java.lang.String ruleStatusName) {
		this.ruleStatusName = ruleStatusName;
	}

	public java.math.BigDecimal getRuleStatus() {
		return ruleStatus;
	}

	public void setRuleStatus(java.math.BigDecimal ruleStatus) {
		this.ruleStatus = ruleStatus;
	}

	public java.lang.String getRuleTypeName() {
		return ruleTypeName;
	}

	public void setRuleTypeName(java.lang.String ruleTypeName) {
		this.ruleTypeName = ruleTypeName;
	}

	public java.lang.String getMethodName() {
		return methodName;
	}

	public void setMethodName(java.lang.String methodName) {
		this.methodName = methodName;
	}

	public java.lang.String getDataChannel() {
		return dataChannel;
	}

	public void setDataChannel(java.lang.String dataChannel) {
		this.dataChannel = dataChannel;
	}

	public java.lang.String getEventName() {
		return eventName;
	}

	public void setEventName(java.lang.String eventName) {
		this.eventName = eventName;
	}

	public java.lang.String getIntroduction() {
		return introduction;
	}

	public void setIntroduction(java.lang.String introduction) {
		this.introduction = introduction;
	}

	public Long getMeterNumber() {
		return meterNumber;
	}

	public void setMeterNumber(Long meterNumber) {
		this.meterNumber = meterNumber;
	}

	/**
	 * name
	 * @return the value of ASSET_VEE_RULE.NAME
	 * @mbggenerated 2018-12-20 04:22:02
	 */
	public java.lang.String getName() {
		return name;
	}

	/**
	 * name
	 * @param name the value for ASSET_VEE_RULE.NAME
	 * @mbggenerated 2018-12-20 04:22:02
	 */
    	public void setName(java.lang.String name) {
		this.name = name;
	}
	/**
	 * mgId
	 * @return the value of ASSET_VEE_RULE.MG_ID
	 * @mbggenerated 2018-12-20 04:22:02
	 */
	public java.lang.String getMgId() {
		return mgId;
	}

	/**
	 * mgId
	 * @param mgId the value for ASSET_VEE_RULE.MG_ID
	 * @mbggenerated 2018-12-20 04:22:02
	 */
    	public void setMgId(java.lang.String mgId) {
		this.mgId = mgId;
	}
	/**
	 * methodId
	 * @return the value of ASSET_VEE_RULE.METHOD_ID
	 * @mbggenerated 2018-12-20 04:22:02
	 */
	public java.lang.String getMethodId() {
		return methodId;
	}

	/**
	 * methodId
	 * @param methodId the value for ASSET_VEE_RULE.METHOD_ID
	 * @mbggenerated 2018-12-20 04:22:02
	 */
    	public void setMethodId(java.lang.String methodId) {
		this.methodId = methodId;
	}
	/**
	 * classId
	 * @return the value of ASSET_VEE_RULE.CLASS_ID
	 * @mbggenerated 2018-12-20 04:22:02
	 */
	public java.math.BigDecimal getClassId() {
		return classId;
	}

	/**
	 * classId
	 * @param classId the value for ASSET_VEE_RULE.CLASS_ID
	 * @mbggenerated 2018-12-20 04:22:02
	 */
    	public void setClassId(java.math.BigDecimal classId) {
		this.classId = classId;
	}
	/**
	 * ruleDetail
	 * @return the value of ASSET_VEE_RULE.RULE_DETAIL
	 * @mbggenerated 2018-12-20 04:22:02
	 */
	public java.lang.String getRuleDetail() {
		return ruleDetail;
	}

	/**
	 * ruleDetail
	 * @param ruleDetail the value for ASSET_VEE_RULE.RULE_DETAIL
	 * @mbggenerated 2018-12-20 04:22:02
	 */
    	public void setRuleDetail(java.lang.String ruleDetail) {
		this.ruleDetail = ruleDetail;
	}
	/**
	 * eventId
	 * @return the value of ASSET_VEE_RULE.EVENT_ID
	 * @mbggenerated 2018-12-20 04:22:02
	 */
	public java.lang.String getEventId() {
		return eventId;
	}

	/**
	 * eventId
	 * @param eventId the value for ASSET_VEE_RULE.EVENT_ID
	 * @mbggenerated 2018-12-20 04:22:02
	 */
    	public void setEventId(java.lang.String eventId) {
		this.eventId = eventId;
	}
	/**
	 * dataitemId
	 * @return the value of ASSET_VEE_RULE.DATAITEM_ID
	 * @mbggenerated 2018-12-20 04:22:02
	 */
	public java.lang.String getDataitemId() {
		return dataitemId;
	}

	/**
	 * dataitemId
	 * @param dataitemId the value for ASSET_VEE_RULE.DATAITEM_ID
	 * @mbggenerated 2018-12-20 04:22:02
	 */
    	public void setDataitemId(java.lang.String dataitemId) {
		this.dataitemId = dataitemId;
	}
	/**
	 * descr
	 * @return the value of ASSET_VEE_RULE.DESCR
	 * @mbggenerated 2018-12-20 04:22:02
	 */
	public java.lang.String getDescr() {
		return descr;
	}

	/**
	 * descr
	 * @param descr the value for ASSET_VEE_RULE.DESCR
	 * @mbggenerated 2018-12-20 04:22:02
	 */
    	public void setDescr(java.lang.String descr) {
		this.descr = descr;
	}
	/**
	 * ruleType
	 * @return the value of ASSET_VEE_RULE.RULE_TYPE
	 * @mbggenerated 2018-12-20 04:22:02
	 */
	public java.math.BigDecimal getRuleType() {
		return ruleType;
	}

	/**
	 * ruleType
	 * @param ruleType the value for ASSET_VEE_RULE.RULE_TYPE
	 * @mbggenerated 2018-12-20 04:22:02
	 */
    	public void setRuleType(java.math.BigDecimal ruleType) {
		this.ruleType = ruleType;
	}

	public AssetVeeRule(java.lang.String name 
	,java.lang.String mgId 
	,java.lang.String methodId 
	,java.math.BigDecimal classId 
	,java.lang.String ruleDetail 
	,java.lang.String eventId 
	,java.lang.String dataitemId 
	,java.lang.String descr 
	,java.math.BigDecimal ruleType ) {
		super();
		this.name = name;
		this.mgId = mgId;
		this.methodId = methodId;
		this.classId = classId;
		this.ruleDetail = ruleDetail;
		this.eventId = eventId;
		this.dataitemId = dataitemId;
		this.descr = descr;
		this.ruleType = ruleType;
	}

}