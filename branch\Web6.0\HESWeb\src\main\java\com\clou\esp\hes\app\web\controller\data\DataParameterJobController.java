/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataParameterJob{ } 
 * 
 * 摘    要： dataParameterJob
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-04-27 09:13:12
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.data;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.CreatePdf;
import com.clou.esp.hes.app.web.core.util.ExcelUtils;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.clou.esp.hes.app.web.validation.ValidGroup3;
import com.power7000g.core.excel.ExcelDataFormatter;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.model.data.DataFwuJob;
import com.clou.esp.hes.app.web.model.data.DataParameterJob;
import com.clou.esp.hes.app.web.service.data.DataParameterJobService;

/**
 * <AUTHOR>
 * @时间：2018-04-27 09:13:12
 * @描述：dataParameterJob类
 */
@Controller
@RequestMapping("/dataParameterJobController")
public class DataParameterJobController extends BaseController{

 	@Resource
    private DataParameterJobService dataParameterJobService;

	/**
	 * 跳转到dataParameterJob列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/data/dataParameterJobList");
    }

	/**
	 * 跳转到dataParameterJob新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dataParameterJob")
	public ModelAndView dataParameterJob(DataParameterJob dataParameterJob,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dataParameterJob.getId())){
			try {
                dataParameterJob=dataParameterJobService.getEntity(dataParameterJob.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dataParameterJob", dataParameterJob);
		}
		return new ModelAndView("/data/dataParameterJob");
	}


	/**
	 * dataParameterJob查询分页 tab2
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request,String deviceType) {
        TagUtil.setFieldValue(jqGridSearchTo, request);		//取传递的参数到JqGridSearchTo
        Map<String, Object> map = jqGridSearchTo.getMap();
        JqGridResponseTo j = null;
        if(StringUtils.isEmpty(deviceType)) {
        	return j;
        }
        try {
        	if(StringUtil.isNotEmpty(map.get("planId"))){	//判断值是否为空，为空直接返回
        		map.put("deviceType", deviceType);
        		j = dataParameterJobService.getForJqGrid(jqGridSearchTo);
        	}
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
	
	/**
	 * dataParameterJob查询分页 tab3
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagridTab3")
    @ResponseBody
    public JqGridResponseTo datagridTab3(JqGridSearchTo jqGridSearchTo,HttpServletRequest request,String deviceType,String deviceType_s) {
        TagUtil.setFieldValue(jqGridSearchTo, request);		//取传递的参数到JqGridSearchTo
        Map<String, Object> map = jqGridSearchTo.getMap();
        JqGridResponseTo j = null;
        try {
        	if(StringUtils.isEmpty(deviceType)) {
        		return j;
        	}
        	map.put("deviceType_s", deviceType_s);
        	map.put("deviceType", deviceType);
        	j = dataParameterJobService.getForJqGrid_ForJobList(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除dataParameterJob信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DataParameterJob dataParameterJob,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dataParameterJobService.deleteById(dataParameterJob.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存dataParameterJob信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DataParameterJob dataParameterJob,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DataParameterJob t=new  DataParameterJob();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dataParameterJob.getId())){
        	t=dataParameterJobService.getEntity(dataParameterJob.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dataParameterJob, t);
				dataParameterJobService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            dataParameterJobService.save(dataParameterJob);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
	
    /**
	 * 打印或导出meter group upgrade功能的tab3 界面数据
	 * @param type
	 * @param dfj
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@RequestMapping(value = "exportOrPrintJobReportJobList")
	@ResponseBody
	public void exportOrPrintJobReportJobList(String type, String groupType, String groupId, String state, String sn,
			String startTime, String endTime, HttpServletRequest request, HttpServletResponse response,String deviceType)throws Exception {
		if (StringUtil.isEmpty(type)) {
			return;
		}
		DataParameterJob job = new DataParameterJob();
		job.setGroupType(groupType);
		job.put("groupId", groupId);
		job.setState(state);
		job.setSn(sn);
		job.put("deviceType", deviceType);
		
//		//默认一周
//		SimpleDateFormat sdf = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
//	    Calendar lastDate = Calendar.getInstance();
//	    lastDate.add(Calendar.DATE, -30);	//昨天
//		job.put("startTime", DateUtils.dateformat(sdf.format(lastDate.getTime()), ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"), "yyyy-MM-dd HH:mm:ss"));
//		lastDate.add(Calendar.DATE, 30);
//		job.put("endTime", DateUtils.dateformat(sdf.format(lastDate.getTime()), ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"), "yyyy-MM-dd HH:mm:ss"));
		List<DataParameterJob> list = dataParameterJobService.exportOrPrintJobReportJobList(job);
		if(!(list.size() > 0)){		//当查询的数据为空值时，插入一条空数据，防止导出Excel报错
			DataParameterJob temp = new DataParameterJob();
			temp.setState("");
			temp.setLastExecTime(new Date());
			temp.setFailedReason("");
			temp.setSn("");
			temp.setModel("");
			temp.setManufacturer("");
			list.add(temp);
		}
		ExcelDataFormatter edf = new ExcelDataFormatter();
		// 1:Runing,2:Done,3:Cancel,4:Waiting,5:Expired
		Map<String, String> trt = new HashMap<String, String>();
		trt.put("1", "Runing");
		trt.put("2", "Done");
		trt.put("3", "Cancel");
		trt.put("4", "Waiting");
		trt.put("5", "Expired");
		edf.set("state", trt);
		// 要日期格式化使用以下方式
		Map<String, String> tvs = new HashMap<String, String>();
		tvs.put("lastExecTime", "MM/dd/yyyy HH:mm:ss");
		edf.set("lastExecTime", tvs);
		if (type.equals("export")) {// export
			ExcelUtils.writeToFile(list, edf, System.currentTimeMillis() + ".xlsx", response, ValidGroup1.class);
		} else {
			CreatePdf.printPdf(list, edf, ValidGroup3.class, request, response);
		}
	}
	
}