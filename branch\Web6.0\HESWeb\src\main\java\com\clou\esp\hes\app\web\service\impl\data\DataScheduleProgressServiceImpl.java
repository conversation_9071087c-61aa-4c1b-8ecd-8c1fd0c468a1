/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataScheduleProgress{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-12-01 06:03:46
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.data;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.data.DataScheduleProgressDao;
import com.clou.esp.hes.app.web.model.data.DataScheduleProgress;
import com.clou.esp.hes.app.web.model.data.DataScheduleProgress1;
import com.clou.esp.hes.app.web.service.data.DataScheduleProgressService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dataScheduleProgressService")
public class DataScheduleProgressServiceImpl  extends CommonServiceImpl<DataScheduleProgress>  implements DataScheduleProgressService {

	@Resource
	private DataScheduleProgressDao dataScheduleProgressDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dataScheduleProgressDao);
    }
	@SuppressWarnings("rawtypes")
	public DataScheduleProgressServiceImpl() {}
	@Override
	public List<DataScheduleProgress1> getList1(
			DataScheduleProgress dataScheduleProgress) {
		// TODO Auto-generated method stub
		return dataScheduleProgressDao.getList1(dataScheduleProgress);
	}
	
	
}