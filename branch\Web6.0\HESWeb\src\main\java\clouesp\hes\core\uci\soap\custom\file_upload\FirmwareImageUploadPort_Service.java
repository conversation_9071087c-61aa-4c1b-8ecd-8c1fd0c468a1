package clouesp.hes.core.uci.soap.custom.file_upload;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import javax.xml.ws.Service;

/**
 * This class was generated by Apache CXF 3.1.14
 * 2018-04-25T17:50:26.665+08:00
 * Generated source version: 3.1.14
 * 
 */
@WebServiceClient(name = "FirmwareImageUploadPort", 
                  wsdlLocation = "http://10.8.165.49:8080/UCI-1.0-SNAPSHOT/services/FirmwareImageUpload?wsdl",
                  targetNamespace = "http://file_upload.custom.soap.uci.core.hes.clouesp/") 
public class FirmwareImageUploadPort_Service extends Service {

    public final static URL WSDL_LOCATION;

    public final static QName SERVICE = new QName("http://file_upload.custom.soap.uci.core.hes.clouesp/", "FirmwareImageUploadPort");
    public final static QName FirmwareImageUploadPortPort = new QName("http://file_upload.custom.soap.uci.core.hes.clouesp/", "FirmwareImageUploadPortPort");
    static {
        URL url = null;
        try {
            url = new URL("http://10.8.165.49:8080/UCI-1.0-SNAPSHOT/services/FirmwareImageUpload?wsdl");
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(FirmwareImageUploadPort_Service.class.getName())
                .log(java.util.logging.Level.INFO, 
                     "Can not initialize the default wsdl from {0}", "http://10.8.165.49:8080/UCI-1.0-SNAPSHOT/services/FirmwareImageUpload?wsdl");
        }
        WSDL_LOCATION = url;
    }

    public FirmwareImageUploadPort_Service(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public FirmwareImageUploadPort_Service(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public FirmwareImageUploadPort_Service() {
        super(WSDL_LOCATION, SERVICE);
    }
    
    public FirmwareImageUploadPort_Service(WebServiceFeature ... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }

    public FirmwareImageUploadPort_Service(URL wsdlLocation, WebServiceFeature ... features) {
        super(wsdlLocation, SERVICE, features);
    }

    public FirmwareImageUploadPort_Service(URL wsdlLocation, QName serviceName, WebServiceFeature ... features) {
        super(wsdlLocation, serviceName, features);
    }    




    /**
     *
     * @return
     *     returns FirmwareImageUploadPort
     */
    @WebEndpoint(name = "FirmwareImageUploadPortPort")
    public FirmwareImageUploadPort getFirmwareImageUploadPortPort() {
        return super.getPort(FirmwareImageUploadPortPort, FirmwareImageUploadPort.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns FirmwareImageUploadPort
     */
    @WebEndpoint(name = "FirmwareImageUploadPortPort")
    public FirmwareImageUploadPort getFirmwareImageUploadPortPort(WebServiceFeature... features) {
        return super.getPort(FirmwareImageUploadPortPort, FirmwareImageUploadPort.class, features);
    }

}
