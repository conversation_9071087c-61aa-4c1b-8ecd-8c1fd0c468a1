{"rows": [{"id": "id1", "birthday": "2017-03-23 18:02:37", "level": 1, "results": 2, "isLeaf": false, "name": "Season1", "testCount": -1, "parent": "", "grade": "1年级", "class": 1, "expanded": false}, {"id": "id110", "birthday": "2017-03-23 18:02:37", "sex": "10/20/2017 00:00:00", "level": 2, "results": 1, "isLeaf": true, "name": "Start Time", "testCount": -5, "parent": "id1", "grade": "0年级", "class": 0, "expanded": false}, {"id": "id111", "birthday": "2017-03-23 18:02:37", "sex": "Week1", "level": 2, "results": 2, "isLeaf": true, "name": "Week", "testCount": -1, "parent": "id1", "grade": "1年级", "class": 1, "expanded": false}, {"id": "id2", "birthday": "2017-03-23 18:02:37", "level": 1, "results": 5, "isLeaf": false, "sex": " ", "name": "Season2", "testCount": 5, "parent": "", "grade": "2年级", "class": 2, "expanded": false}, {"id": "id210", "birthday": "2017-03-23 18:02:37", "level": 2, "results": 1, "isLeaf": true, "name": "Start Time", "sex": " 10/20/2017 02:00:00", "testCount": -5, "parent": "id2", "grade": "0年级", "class": 0, "expanded": false}, {"id": "id211", "birthday": "2017-03-23 18:02:37", "level": 2, "results": 2, "isLeaf": true, "name": "Week", "sex": "Week2", "testCount": -1, "parent": "id2", "grade": "1年级", "class": 1, "expanded": false}, {"id": "id3", "birthday": "2017-03-23 18:02:37", "level": 1, "results": 10, "isLeaf": false, "sex": " ", "name": "Season3", "testCount": 13, "parent": "", "grade": "3年级", "class": 3, "expanded": false}, {"id": "id310", "birthday": "2017-03-23 18:02:37", "level": 2, "results": 1, "isLeaf": true, "name": "Start Time", "sex": " 10/20/2017 06:00:00", "testCount": -5, "parent": "id3", "grade": "0年级", "class": 0, "expanded": false}, {"id": "id311", "birthday": "2017-03-23 18:02:37", "level": 2, "results": 2, "isLeaf": true, "name": "Week", "sex": "Week3", "testCount": -1, "parent": "id3", "grade": "1年级", "class": 1, "expanded": false}, {"id": "id4", "birthday": "2017-03-23 18:02:37", "level": 1, "results": 17, "isLeaf": false, "sex": " ", "name": "Season4", "testCount": 23, "parent": "", "grade": "4年级", "class": 4, "expanded": false}, {"id": "id410", "birthday": "2017-03-23 18:02:37", "level": 2, "results": 1, "isLeaf": true, "name": "Start Time", "sex": "08:00:00", "testCount": -5, "parent": "id4", "grade": "0年级", "class": 0, "expanded": false}, {"id": "id411", "birthday": "2017-03-23 18:02:37", "level": 2, "results": 2, "isLeaf": true, "name": "Week", "sex": "Week4", "testCount": -1, "parent": "id4", "grade": "1年级", "class": 1, "expanded": false}, {"id": "id5", "birthday": "2017-03-23 18:02:37", "level": 1, "results": 26, "isLeaf": false, "sex": " ", "name": "Season5", "testCount": 35, "parent": "", "grade": "5年级", "class": 5, "expanded": false}, {"id": "id510", "birthday": "2017-03-23 18:02:37", "level": 2, "results": 1, "isLeaf": true, "name": "Start Time", "sex": "12:00:00", "testCount": -5, "parent": "id5", "grade": "0年级", "class": 0, "expanded": false}, {"id": "id511", "birthday": "2017-03-23 18:02:37", "level": 2, "results": 2, "isLeaf": true, "name": "Week", "sex": "Week5", "testCount": -1, "parent": "id5", "grade": "1年级", "class": 1, "expanded": false}, {"id": "id6", "birthday": "2017-03-23 18:02:37", "level": 1, "results": 26, "isLeaf": false, "sex": " ", "name": "Season6", "testCount": 35, "parent": "", "grade": "5年级", "class": 5, "expanded": false}, {"id": "id610", "birthday": "2017-03-23 18:02:37", "level": 2, "results": 1, "isLeaf": true, "name": "Start Time", "sex": "12:00:00", "testCount": -5, "parent": "id6", "grade": "0年级", "class": 0, "expanded": false}, {"id": "id611", "birthday": "2017-03-23 18:02:37", "level": 2, "results": 2, "isLeaf": true, "name": "Week", "sex": "Week5", "testCount": -1, "parent": "id6", "grade": "1年级", "class": 1, "expanded": false}, {"id": "id7", "birthday": "2017-03-23 18:02:37", "level": 1, "results": 26, "isLeaf": false, "sex": " ", "name": "Season7", "testCount": 35, "parent": "", "grade": "5年级", "class": 5, "expanded": false}, {"id": "id710", "birthday": "2017-03-23 18:02:37", "level": 2, "results": 1, "isLeaf": true, "name": "Start Time", "sex": "12:00:00", "testCount": -5, "parent": "id7", "grade": "0年级", "class": 0, "expanded": false}, {"id": "id711", "birthday": "2017-03-23 18:02:37", "level": 2, "results": 2, "isLeaf": true, "name": "Week", "sex": "Week7", "testCount": -1, "parent": "id7", "grade": "1年级", "class": 1, "expanded": false}]}