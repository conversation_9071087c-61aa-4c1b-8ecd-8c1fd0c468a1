package clouesp.hes.core.uci.soap.custom.webservice.common;


/**
 * @ClassName: ResetType
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年7月23日 下午2:01:19
 *
 */
public enum ResetTypeDlms {
	HardWareReset(1,"*********"),
	DataReset(2,"*********"),
	ParameterAndDataReset(3,"*********"),
	ParameterAndDataReset_ExceptHesCommunicationParameter(4,"*********");
	
	private int index;
	private String dataItemId;
	
	private ResetTypeDlms(int index,String dataItemId) {
		this.index = index;
		this.dataItemId = dataItemId;
	}
	
	public static ResetTypeDlms parse(int index) {
		for (ResetTypeDlms resetType : values()) {
			if(index == resetType.getIndex()) {
				return resetType;
			}
		}
		return null;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	public String getDataItemId() {
		return dataItemId;
	}

	public void setDataItemId(String dataItemId) {
		this.dataItemId = dataItemId;
	}
}
