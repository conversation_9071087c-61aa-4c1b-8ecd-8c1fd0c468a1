/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysMenu{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-10-27 09:17:08
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.system;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.system.DictOperationDao;
import com.clou.esp.hes.app.web.dao.system.SysMenuDao;
import com.clou.esp.hes.app.web.dao.system.SysRoleMenuDao;
import com.clou.esp.hes.app.web.enums.system.EnumUserType;
import com.clou.esp.hes.app.web.model.dict.DictMenu;
import com.clou.esp.hes.app.web.model.system.SysRole;
import com.clou.esp.hes.app.web.model.system.SysRoleMenu;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.clou.esp.hes.app.web.service.system.SysMenuService;
import com.power7000g.core.util.base.StringUtil;

@Component
@Service("sysMenuService")
public class SysMenuServiceImpl  extends CommonServiceImpl<DictMenu>  implements SysMenuService {

	@Resource
	private SysMenuDao sysMenuDao;
	@Resource
	private SysRoleMenuDao sysRoleMenuDao;
	@Resource
	private DictOperationDao dictOperationDao;
	
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(sysMenuDao);
    }
	@SuppressWarnings("rawtypes")
	public SysMenuServiceImpl() {}
	
	@Override
	public List<DictMenu> getMenusByUserId(SysUser su) {
		List<DictMenu> menus=new ArrayList<DictMenu>();
		if(su.getUserType()==EnumUserType.SUPER_USER.getIndex()){
			menus = sysMenuDao.getMenusByUserId(null, "0");
			menus = getMenus(menus, null);
		}else{
			menus = sysMenuDao.getMenusByUserId(su.getId(), "0");
			menus = getMenus(menus, su.getId());
		}
		return menus;
	}
	
	private List<DictMenu> getMenus(List<DictMenu> menus, String userId) {
		if (menus == null) {
			return menus;
		}
		for (DictMenu sf : menus) {
			List<DictMenu> childList = sysMenuDao.getMenusByUserId(
					userId, sf.getId());
			
			sf.setChildList(childList);
			if (childList != null && childList.size() > 0) {
				getMenus(childList, userId);
			}
		}
		return menus;
	}
	@Override
	public Set<String> getPermissionByRoleId(String roleId) {
		Set<String> permissions = new HashSet<String>();
		if(StringUtil.isNotEmpty(roleId)){
			List<SysRoleMenu> srfList = sysRoleMenuDao
					.getAllUserPermissionByRoleId(roleId);
			if (srfList == null || srfList.size() == 0) {
				return permissions;
			}
			for (SysRoleMenu rf : srfList) {
				DictMenu f = sysMenuDao.getEntity(rf.getMenuId());
				if (f == null) {
					continue;
				}
				String url = f.getFunctionurl();
				if(StringUtil.isNotEmpty(url))
					permissions.add(url);
				String operation = rf.getOperation();
				if (StringUtil.isEmpty(operation)) {
					continue;
				}
				String operations[] = operation.split(",");
				if (operations == null || operations.length == 0) {
					continue;
				}
				List<String> operUrl=dictOperationDao.getOperations(operations);
				if(operUrl!=null&&operUrl.size()>0)
					permissions.addAll(operUrl);
			}
		}else{
			List<String> operUrl=dictOperationDao.getOperations(null);
			if(operUrl!=null&&operUrl.size()>0)
				permissions.addAll(operUrl);
		}
		return permissions;
	}
	
	@Override
	public List<DictMenu> getMenuListByRole(SysRole entity) {
		return sysMenuDao.getMenuListByRole(entity);
	}
	
	
	@Override
	public List<String> getMenusUrlUserId(SysUser su) {
		List<String> menuUrls=new ArrayList<String>();
		List<DictMenu> menus=new ArrayList<DictMenu>();
		if(su==null){
			menus = sysMenuDao.getMenusByUserId(null, null);
		}else{
			if(su.getUserType()==EnumUserType.SUPER_USER.getIndex()){
				menus = sysMenuDao.getMenusByUserId(null, null);
			}else{
				menus = sysMenuDao.getMenusByUserId(su.getId(), null);
			}
		}
		for(DictMenu m:menus){
			if(StringUtil.isNotEmpty(m.getFunctionurl())){
				menuUrls.add(m.getFunctionurl());
			}
		}
		return menuUrls;
	}
	
	
	@Override
	public Map<String, String> getMenusHideTab() {
		Map<String,String> map=new HashMap<String, String>();
		List<DictMenu> menus=new ArrayList<DictMenu>();
		menus = sysMenuDao.getMenusByUserId(null, null);
		for(DictMenu m:menus){
			if(StringUtil.isNotEmpty(m.getFunctionurl())&&StringUtil.isNotEmpty(m.getHideTab())){
				map.put(m.getFunctionurl(), m.getHideTab());
			}
		}
		return map;
	}
	
	
}