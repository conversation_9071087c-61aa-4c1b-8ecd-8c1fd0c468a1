/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetScheduleSchemeDetail{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-03-06 03:10:48
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.asset;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.asset.AssetScheduleSchemeDetailDao;
import com.clou.esp.hes.app.web.model.asset.AssetScheduleSchemeDetail;
import com.clou.esp.hes.app.web.service.asset.AssetScheduleSchemeDetailService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("assetScheduleSchemeDetailService")
public class AssetScheduleSchemeDetailServiceImpl  extends CommonServiceImpl<AssetScheduleSchemeDetail>  implements AssetScheduleSchemeDetailService {

	@Resource
	private AssetScheduleSchemeDetailDao assetScheduleSchemeDetailDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(assetScheduleSchemeDetailDao);
    }
	@SuppressWarnings("rawtypes")
	public AssetScheduleSchemeDetailServiceImpl() {}
	
	
}