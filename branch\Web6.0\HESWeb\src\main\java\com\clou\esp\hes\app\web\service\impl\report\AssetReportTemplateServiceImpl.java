/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetReportTemplate{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2019-03-22 01:50:10
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.report;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.report.AssetReportTemplateDao;
import com.clou.esp.hes.app.web.model.report.AssetReportTemplate;
import com.clou.esp.hes.app.web.service.report.AssetReportTemplateService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("assetReportTemplateService")
public class AssetReportTemplateServiceImpl  extends CommonServiceImpl<AssetReportTemplate>  implements AssetReportTemplateService {

	@Resource
	private AssetReportTemplateDao assetReportTemplateDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(assetReportTemplateDao);
    }
	@SuppressWarnings("rawtypes")
	public AssetReportTemplateServiceImpl() {}
	
	
}