package clouesp.hes.core.uci.soap.custom.manualCalculation;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>dispatch complex type�� Java �ࡣ
 * 
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 * 
 * <pre>
 * &lt;complexType name="dispatch"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="msgs" type="{http://dispatch_task.custom.soap.uci.core.hes.clouesp/}DispatchMessage" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "dispatch1", propOrder = {
    "msgs"
})
public class Dispatch {

    protected List<DispatchMessage> msgs;

    /**
     * Gets the value of the msgs property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the msgs property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getMsgs().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link DispatchMessage }
     * 
     * 
     */
    public List<DispatchMessage> getMsgs() {
        if (msgs == null) {
            msgs = new ArrayList<DispatchMessage>();
        }
        return this.msgs;
    }

}
