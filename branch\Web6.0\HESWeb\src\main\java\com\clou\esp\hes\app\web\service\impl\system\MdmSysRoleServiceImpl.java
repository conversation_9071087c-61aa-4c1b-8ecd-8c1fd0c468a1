/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysRole{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-10-27 09:17:08
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.system;

import com.clou.esp.hes.app.web.dao.system.MdmSysRoleDao;
import com.clou.esp.hes.app.web.model.system.SysRole;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.clou.esp.hes.app.web.service.system.MdmSysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Component
@Service("mdmSysRoleService")
public class MdmSysRoleServiceImpl extends CommonServiceImpl<SysRole> implements MdmSysRoleService {

	@Resource
	private MdmSysRoleDao mdmSysRoleDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(mdmSysRoleDao);
    }
	public MdmSysRoleServiceImpl() {}
	
	@Override
	public List<SysRole> vaildRoleName(String roleName) {
		return mdmSysRoleDao.vaildRoleName(roleName);
	}
	
	
	
}