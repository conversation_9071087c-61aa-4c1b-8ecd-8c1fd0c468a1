/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataParameterPlan{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-04-27 09:13:12
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.data;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import ch.iec.tc57._2011.enddevicecontrols.FaultMessage;
import ch.iec.tc57._2011.enddevicecontrols.RequestEndDeviceControlsPort;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl.EndDeviceControlType;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl.EndDevices;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl.EndDevices.Names;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl.EndDevices.Names.NameType;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControls;
import ch.iec.tc57._2011.enddevicecontrolsmessage.EndDeviceControlsPayloadType;
import ch.iec.tc57._2011.enddevicecontrolsmessage.EndDeviceControlsRequestMessageType;
import ch.iec.tc57._2011.enddevicecontrolsmessage.EndDeviceControlsResponseMessageType;
import ch.iec.tc57._2011.schema.message.HeaderType;
import ch.iec.tc57._2011.schema.message.UserType;

import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.UciInterfaceUtil;
import com.clou.esp.hes.app.web.core.util.XMLUtil;
import com.clou.esp.hes.app.web.dao.asset.AssetMeterGroupMapDao;
import com.clou.esp.hes.app.web.dao.data.DataParameterJobDao;
import com.clou.esp.hes.app.web.dao.data.DataParameterPlanDao;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroupMap;
import com.clou.esp.hes.app.web.model.data.DataParameterPlan;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.data.DataParameterPlanService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.clou.esp.hes.app.web.service.system.SysServiceAttributeService;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.uuid.UUIDGenerator;
import com.power7000g.core.util.verify.ValidationUtils;

@Component
@Service("dataParameterPlanService")
public class DataParameterPlanServiceImpl  extends CommonServiceImpl<DataParameterPlan>  implements DataParameterPlanService {

	@Resource
	private DataParameterPlanDao dataParameterPlanDao;
	@Resource
	private AssetMeterGroupMapDao assetMeterGroupMapDao;
	@Resource
	private DataParameterJobDao dataParameterJobDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dataParameterPlanDao);
    }
	public DataParameterPlanServiceImpl() {}
	
	/**
	 * 保存meter group upgrade计划
	 */
	@Override
	@Transactional
	public AjaxJson saveMGU_Plan(DataParameterPlan plan, SysServiceAttributeService sysServiceAttributeService){
		AjaxJson j = new AjaxJson();
		String valityMsg = ValidationUtils.validateObj(plan);
		if (StringUtil.isNotEmpty(valityMsg)) {
			j.setErrorMsg(valityMsg);
			return j;
		}
		// 比对日期大小
		Date startTime = plan.getStartTime();
		Date expiryTime = plan.getExpiryTime();
		plan.setEndTime(expiryTime);
		if (!startTime.before(expiryTime)) {
			j.setErrorMsg(MutiLangUtil.doMutiLang("meterGroupUpgrade.startEqualExpiry"));
			return j;
		}
		Date d = new Date();
		Date taskStartTime = plan.getTaskStartTime();
		Date taskEndtime = plan.getTaskEndTime();
		taskStartTime = DateUtils.str2Date(DateUtils.date2Str(d, DateUtils.date_sdf) 
				+ " " + DateUtils.date2Str(taskStartTime, DateUtils.HHmmss), DateUtils.datetimeFormat);
		taskEndtime = DateUtils.str2Date(DateUtils.date2Str(d, DateUtils.date_sdf) 
				+ " " + DateUtils.date2Str(taskEndtime, DateUtils.HHmmss), DateUtils.datetimeFormat);
		String groupType = plan.getGroupType();	//电表组类型
		String groupId = plan.getGroupId();		//组ID
		String sn = plan.getSn();				//sn号
		String planId = UUIDGenerator.generate();//升级计划ID
		plan.setId(planId);
		try {
			// 查询电表集合
			if("1".equals(plan.getDeviceType())) {
				AssetMeterGroupMap group = new AssetMeterGroupMap();
				group.setGroupType(groupType);
				group.setGroupId(groupId);
				group.setSn(sn);	
				List<AssetMeterGroupMap> meterList = assetMeterGroupMapDao.getPlanMeterList_MGU_pojo(group);
				if (meterList == null || meterList.size() == 0) {
					j.setErrorMsg(MutiLangUtil.doMutiLang("meterGroupUpgrade.noDeviceFound"));
					return j;
				}
				dataParameterPlanDao.insert(plan);					//保存升级计划
				j.setObj(planId);
				dataParameterJobDao.batchInsertJob(plan);			//批量插入job
				//调用UCI接口，下发meter group upgrade升级计划
				EndDeviceControlsResponseMessageType resMsg = createEndDeviceControls(plan, sysServiceAttributeService);
				System.out.println("Meter group upgrade create Plan return result is ===>" + XMLUtil.convertToXml(resMsg));
			}else	if("2".equals(plan.getDeviceType())) {
				dataParameterPlanDao.insert(plan);					//保存升级计划
				j.setObj(planId);
				dataParameterJobDao.batchInsertJob(plan);			//批量插入job
				//调用UCI接口，下发meter group upgrade升级计划
				EndDeviceControlsResponseMessageType resMsg = createEndDeviceControls(plan, sysServiceAttributeService);
				System.out.println("Meter group upgrade create Plan return result is ===>" + XMLUtil.convertToXml(resMsg));
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		j.setMsg(MutiLangUtil.doMutiLang("meterGroupUpgrade.createPlanSucc"));
		return j;
	}
	
	/**
	 * 获取旧升级计划的未完成的job，生成新的plan和job
	 */
	@Override
	@Transactional
	public AjaxJson cretePlanAgain(DataParameterPlan plan, String oldPlanId, SysServiceAttributeService sysServiceAttributeService){
		AjaxJson j = new AjaxJson();
		String valityMsg = ValidationUtils.validateObj(plan);
		if (StringUtil.isNotEmpty(valityMsg)) {
			j.setErrorMsg(valityMsg);
			return j;
		}
		// 比对日期大小
		Date startTime = plan.getStartTime();
		Date expiryTime = plan.getExpiryTime();
		plan.setEndTime(expiryTime);
		if (!startTime.before(expiryTime)) {
			j.setErrorMsg(MutiLangUtil.doMutiLang("meterGroupUpgrade.startEqualExpiry"));
			return j;
		}
		Date d = new Date();
		Date taskStartTime = plan.getTaskStartTime();
		Date taskEndtime = plan.getTaskEndTime();
		taskStartTime = DateUtils.str2Date(DateUtils.date2Str(d, DateUtils.date_sdf) 
				+ " " + DateUtils.date2Str(taskStartTime, DateUtils.HHmmss), DateUtils.datetimeFormat);
		taskEndtime = DateUtils.str2Date(DateUtils.date2Str(d, DateUtils.date_sdf) 
				+ " " + DateUtils.date2Str(taskEndtime, DateUtils.HHmmss), DateUtils.datetimeFormat);
//		if (taskEndtime.before(taskStartTime)) {
//			j.setErrorMsg(MutiLangUtil.doMutiLang("meterGroupUpgrade.taskStartEqualEnd"));
//			return j;
//		}
		String planId = UUIDGenerator.generate();	//升级计划ID
		plan.setId(planId);
		try {
			dataParameterPlanDao.insert(plan);		//保存升级计划
			j.setObj(planId);
			Map<String, Object> extData = new HashMap<String, Object>();
			extData.put("oldPlanId", oldPlanId);	//设置旧的升级计划ID
			plan.setExtData(extData);
			int insertNum = dataParameterJobDao.batchInsert_cretePlanAgainJob(plan);	//批量插入job
			System.out.println("Insert job number is ===>" + insertNum);
			//调用UCI接口，下发meter group upgrade升级计划
			EndDeviceControlsResponseMessageType resMsg = createEndDeviceControls(plan, sysServiceAttributeService);
			System.out.println("Meter group upgrade create Plan return result is ===>" + XMLUtil.convertToXml(resMsg));
		} catch (Exception e) {
			e.printStackTrace();
		}
		j.setMsg(MutiLangUtil.doMutiLang("meterGroupUpgrade.createPlanSucc"));
		return j;
	}

	private EndDeviceControlsResponseMessageType createEndDeviceControls(
			DataParameterPlan plan, SysServiceAttributeService sysServiceAttributeService) throws FaultMessage {
		RequestEndDeviceControlsPort port = (RequestEndDeviceControlsPort) 
				UciInterfaceUtil.getInterface("RequestEndDeviceControlsPort", RequestEndDeviceControlsPort.class,sysServiceAttributeService);
		EndDeviceControlsRequestMessageType reqMsg = new EndDeviceControlsRequestMessageType();
		SysUser su = TokenManager.getToken();
		HeaderType header = new HeaderType();
		Date date = new Date();
		header.setVerb("create");
		header.setNoun("EndDeviceControls");
		header.setTimestamp(DateUtils.dateToXmlDate(date));
		header.setSource("ClouESP HES");
		header.setAsyncReplyFlag(false);
		header.setReplyAddress("");
		header.setAckRequired(true);
		UserType u = new UserType();
		u.setOrganization(su.getOrgId());
		u.setUserID(su.getId());
		header.setUser(u);
		header.setMessageID(DateUtils.formatDate("yyyyMMdd") + su.getId());
		header.setCorrelationID(DateUtils.formatDate("yyyyMMdd") + su.getId());
		reqMsg.setHeader(header);
		EndDeviceControlsPayloadType payload = new EndDeviceControlsPayloadType();
		EndDeviceControls endDeviceControls = new EndDeviceControls();
		List<EndDeviceControl> endDeviceControl = endDeviceControls.getEndDeviceControl();
		EndDeviceControl edc = new EndDeviceControl();
		edc.setMRID(plan.getId());			// 放的固件升级计划主键ID
		edc.setReason("periodicity");		// immediately 立即开始， periodicity
		EndDeviceControlType edct = new EndDeviceControlType();
		edct.setRef("********");			//电表组升级dataitem ID
		edc.setEndDeviceControlType(edct);
		List<EndDeviceControl.EndDevices> endDevices = edc.getEndDevices();
		EndDevices ed = new EndDevices();
		List<EndDeviceControl.EndDevices.Names> names = ed.getNames();
		Names name1 = new Names();
		name1.setName(DateUtils.date2Str(plan.getStartTime(), DateUtils.date_sdf));
		NameType nt1 = new NameType();
		nt1.setName("plan start time");
		name1.setNameType(nt1);
		Names name2 = new Names();
		name2.setName(DateUtils.date2Str(plan.getExpiryTime(), DateUtils.date_sdf));
		NameType nt2 = new NameType();
		nt2.setName("plan end time");
		name2.setNameType(nt2);
		Names name3 = new Names();
		name3.setName(DateUtils.date2Str(plan.getTaskStartTime(), DateUtils.HHmmss));
		NameType nt3 = new NameType();
		nt3.setName("job start time");
		name3.setNameType(nt3);
		Names name4 = new Names();
		name4.setName(DateUtils.date2Str(plan.getTaskEndTime(), DateUtils.HHmmss));
		NameType nt4 = new NameType();
		nt4.setName("job end time");
		name4.setNameType(nt4);
		Names name5 = new Names();
		name5.setName(plan.getTaskCycle().toString());
		NameType nt5 = new NameType();
		nt5.setName("task cycle");
		name5.setNameType(nt5);
		names.add(name1);
		names.add(name2);
		names.add(name3);
		names.add(name4);
		names.add(name5);
		endDevices.add(ed);
		endDeviceControl.add(edc);
		payload.setEndDeviceControls(endDeviceControls);
		reqMsg.setPayload(payload);
		System.out.println("XML format is ==" + XMLUtil.convertToXml(reqMsg));
		EndDeviceControlsResponseMessageType resMsg = port.createEndDeviceControls(reqMsg);	//result OK就是成功
		return resMsg;
	}
	
	@Override
	public List<DataParameterPlan> exportPlanReportList(DataParameterPlan plan) {
		return dataParameterPlanDao.exportPlanReportList(plan);
	}
}