<?xml version="1.0" encoding="utf-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="
    		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
			http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.0.xsd
			http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.0.xsd
			http://www.springframework.org/schema/jee http://www.springframework.org/schema/jee/spring-jee-4.0.xsd
			http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-4.0.xsd"
	default-lazy-init="false">
	<bean
		class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
		<property name="locations">
			<value>file:#{systemEnvironment['POWER7000G_WEB_CONFIG_HOME']}/init-web.properties</value>
		</property>
	</bean>
	<bean id="jedisPoolConfig" class="redis.clients.jedis.JedisPoolConfig">
		<property name="maxIdle" value="${redis.maxIdle}" /><!-- 最大闲置 -->
		<property name="minIdle" value="${redis.minIdle}" /><!-- 最小闲置 -->
		<property name="testOnBorrow" value="${redis.testOnBorrow}" /><!-- 
			可以获取 -->
	</bean>
	<!-- redis 配置,也可以把配置挪到properties配置文件中,再读取 -->
	<bean id="jedisPool" class="redis.clients.jedis.JedisPool">
		<constructor-arg index="0" ref="jedisPoolConfig" />
		<constructor-arg index="2" value="${redis.port}" name="port"
			type="int" />
		<constructor-arg index="3" value="${redis.timeout}"
			name="timeout" type="int" />
		<constructor-arg index="1" value="${redis.host}" name="host"
			type="java.lang.String" />
		<!-- 如果你需要配置密码，请打开这里。 <constructor-arg index="4" value="你的密码" name="password" 
			type="java.lang.String"/> -->
	</bean>
</beans>