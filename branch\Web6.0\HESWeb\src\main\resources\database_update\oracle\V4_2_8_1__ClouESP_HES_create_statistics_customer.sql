CREATE TABLE DATA_STATISTICS_CUSTOMER
(	"ID" VARCHAR2(32 BYTE), 
	"ID_TYPE" VARCHAR2(32 BYTE), 
	"TV" DATE, 
	"TV_TYPE" NUMBER, 
	"PERCENT" NUMBER(10,2), 
	"COUNT_CURRENT" NUMBER(12,2), 
	"ORG_ID" VARCHAR2(32 BYTE), 
	"COUNT_ACTUAL" NUMBER(12,0), 
	"COUNT_TOTAL" NUMBER(12,0),
	primary key (ID,ID_TYPE,TV,TV_TYPE,ORG_ID)
);

update DICT_SERVICE_ATTRIBUTE set ATTRIBUTE_DEFAULT = '9904' where ATTRIBUTE_NAME = 'Channel.SGPort';
Insert into DICT_SERVICE_ATTRIBUTE (SERVICE_TYPE,ATTRIBUTE_NAME,ATTRIBUTE_DESC,ATTRIBUTE_TYPE,ATTRIBUTE_DEFAULT,ATTRIBUTE_MIN,ATTRIBUTE_MAX,OPT,SORT_ID) values (1,'Channel.CSGPort','CSG protocol server listen port','Integer','9905',null,null,null,4);

Insert into SYS_SERVICE_ATTRIBUTE (ID,ATTRIBUTE_NAME,ATTRIBUTE_VALUE) values ('20010001','Channel.CSGPort',9905);


Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('2dd2e5a5beda11e79bb777f728c53333','29018328bd4011e79bb968f728c516f9',1,'Home',1,'systemController/home.do','0','1','1000',null);
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('40422ec4bedc11e79bb568f728c516f9','29018328bd4011e79bb968f728c516f9',2,'Communication Status Report',4,'dataComminicationStatusController/list.do','1e858f11beda11e79bb968f728c516f9','1','1023',null);

Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1000','Home','systemController/home.do','Home');
Insert into DICT_FUNCTION (ID,FUNCTIONNAME,FUNCTIONURL,FUNCTION_INTRO) values ('1023','Communication Status Report','dataComminicationStatusController/list.do','Data Collection');

Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1000001','View Data Collection Home Page','1000','ViewDataCollectionHomePage',0,'View Data Collection Home Page');
Insert into DICT_OPERATION (ID,OPERATIONNAME,FUNCTION_ID,OPERATIONURL,ISHIDE,DESCRIPTION) values ('1000002','View Supply '||'&'||' Sales Home Page','1000','ViewSupplySalesHomePage',0,'View Supply '||'&'||' Sales Home Page');