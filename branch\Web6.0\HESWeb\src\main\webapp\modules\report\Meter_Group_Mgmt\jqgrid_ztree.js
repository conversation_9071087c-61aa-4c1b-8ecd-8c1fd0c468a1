$(document).ready(function() {
	$.jgrid.defaults.styleUI = "Bootstrap";
	$("#table").jqGrid({
		treeGrid: true, //是否树形getRootPathWeb()+'/modules/report/Meter_Group_Mgmt/
		treeGridModel: 'adjacency', //treeGrid模式，跟json元数据有关 ,adjacency/nested  
		ExpandColumn: 'name',
		url: getRootPathWeb()+'/modules/report/Meter_Group_Mgmt/jqgrid_ztree.json',
		datatype: "json",
		width: "30%",
		height: "200px",
		autowidth: true,
		rowNum: 10,
		rowList: [10, 20, 30],
		colNames: ["Day Name", "Value"],
		colModel: [{
			name: 'name',
			index: 'name',
			width: 90,
			editable: true,
			stype: "select",
			cellattr: addCellAttr,
		}, {
			name: "sex",
			index: "sex",
			align: "left",
			editable: true,
			width: 80,

		}],
		viewrecords: true,
		autoScroll: true,
		treeIcons: {
			leaf: ' '
		},
		caption: "Day List",
		jsonReader: {
			repeatitems: false //不需要再去后台刷新，否则可能有问题，所以最好第一次就加载所有数据 
		},
		postData: {
			'field': 'id,name,sex'
		}
	});

	function addCellAttr(rowId, val, rawObject, cm, rdata) {
		if(rawObject.name == 'Start Time') {
			rawObject.sex = '<input value="00:00:00" class=" f_right m-bottom_5 m-top_5 m_date_W jqgrid_tree_time" id="start" type="text" onClick="WdatePicker({isShowClear:false,lang:\'en\',dateFmt:\'HH:mm:ss\'})">';

		} else if(rawObject.name == 'Rate') {
			rawObject.sex = '<select class="jqgrid_tree_select"><option value ="Rate1">Rate1</option><option value ="Rate2">Rate2</option><option value="Rate3">Rate3</option><option value="Rate4">Rate4</option></select>';

		} else {
			return "";
		}
	}
	$("#table1").jqGrid({
		treeGrid: true, //是否树形
		treeGridModel: 'adjacency', //treeGrid模式，跟json元数据有关 ,adjacency/nested  
		ExpandColumn: 'name',
		url: getRootPathWeb()+'/modules/report/Meter_Group_Mgmt/jqgrid_ztree1.json',
		datatype: "json",
		width: "30%",
		autowidth: true,
		rowNum: 10,
		rowList: [10, 20, 30],
		colNames: ["Week Name", "Value"],
		colModel: [{
			name: "name",
			index: "name",
			editable: true,
			search: true,
			align: "left",
			width: 80,
			frozen: true,
		}, {
			name: "sex",
			index: "sex",
			align: "left",
			editable: true,
			width: 80,
			editable: true,
			formatter: alarmFormatter,
		}],
		/*	pager: "#pager",*/
		//					rownumbers:true,
		viewrecords: true,
		/*shrinkToFit:true,  */
		autoScroll: true,
		treeIcons: {
			leaf: ' '
		},
		caption: "Week List",
		jsonReader: {
			repeatitems: false //不需要再去后台刷新，否则可能有问题，所以最好第一次就加载所有数据 
		},
		height: "200px",
		postData: {
			'field': 'id,name,sex'
		}
	});

	function alarmFormatter(cellvalue, options, rowdata) {
		if(rowdata.level == 1) {
			return '';

		} else if(rowdata.level == 2) {

			return '<select class="jqgrid_tree_select"><option value =">Day1">Day1</option><option value =">Day2">Day2</option><option value=">Day3">Day3</option><option value=">Day4">Day4</option><option value =">Day5">Day5</option><option value =">Day6">Day6</option><option value =">Day7">Day7</option></select>';
		}
	}
	$("#table2").jqGrid({
		treeGrid: true, //是否树形
		treeGridModel: 'adjacency', //treeGrid模式，跟json元数据有关 ,adjacency/nested  
		ExpandColumn: 'name',
		url: getRootPathWeb()+'/modules/report/Meter_Group_Mgmt/jqgrid_ztree2.json',
		datatype: "json",
		width: "10%",
		autowidth: true,
		rowNum: 10,
		rowList: [10, 20, 30],
		colNames: ["Season Name", "Value"],
		colModel: [{
			name: "name",
			index: "name",
			editable: true,
			search: true,
			align: "left",
			width: 80,
			frozen: true,
			stype: "select",
			cellattr: addCellAttrs,
		}, {
			name: "sex",
			index: "sex",
			align: "left",
			editable: true,
			width: 80,
			editable: true,

		}],
		/*	pager: "#pager",*/
		//					rownumbers:true,
		viewrecords: true,
		/*shrinkToFit:true,  */
		autoScroll: true,
		treeIcons: {
			leaf: ' '
		},
		caption: "Season  List",
		jsonReader: {
			repeatitems: false //不需要再去后台刷新，否则可能有问题，所以最好第一次就加载所有数据 
		},
		height: "200px",
		postData: {
			'field': 'id,name,sex'
		}
	});

	function addCellAttrs(rowId, val, rawObject, cm, rdata) {
		if(rawObject.name == 'Start Time') {
			rawObject.sex = '<input value="01/04/2018 00:00:00" class=" f_right m-bottom_5 m-top_5 m_date_W jqgrid_tree_time" id="start" type="text" onClick="WdatePicker({isShowClear:false,lang:\'en\',dateFmt:\'MM/dd/yyyy HH:mm:ss\'})">';

		} else if(rawObject.name == 'Week') {
			rawObject.sex = '<select class="jqgrid_tree_select"><option value ="Week1">Week1</option><option value ="Week2">Week2</option><option value="Week3">Week3</option><option value="Week4">Week4</option><option value ="Week5">Week5</option><option value ="Week6">Week6</option><option value ="Week7">Week7</option></select>';

		}  else {
			return "";
		}
		
		
	}
	$("#table3").jqGrid({
		treeGrid: true, //是否树形
		treeGridModel: 'adjacency', //treeGrid模式，跟json元数据有关 ,adjacency/nested  
		ExpandColumn: 'name',
		url: getRootPathWeb()+'/modules/report/Meter_Group_Mgmt/jqgrid_ztree3.json',
		datatype: "json",
		width: "100%",
		autowidth: true,
		rowNum: 10,
		rowList: [10, 20, 30],
		colNames: ["Special Day Name", "Value"],
		colModel: [{
			name: "name",
			index: "name",
			editable: true,
			search: true,
			align: "left",
			width: 80,
			frozen: true,
			stype: "select",
			cellattr: addCellAttr3,
		}, {
			name: "sex",
			index: "sex",
			align: "left",
			editable: true,
			width: 80,
			editable: true,
		}],
		/*	pager: "#pager",*/
		//					rownumbers:true,
		viewrecords: true,
		/*shrinkToFit:true,  */
		autoScroll: true,
		treeIcons: {
			leaf: ' '
		},
		caption: "Special Day  List",
		jsonReader: {
			repeatitems: false //不需要再去后台刷新，否则可能有问题，所以最好第一次就加载所有数据 
		},
		height: "200px",
		postData: {
			'field': 'id,name,sex'
		}
	});

	function addCellAttr3(rowId, val, rawObject, cm, rdata) {
		if(rawObject.name == 'Day ID') {
			rawObject.sex = '<select class="jqgrid_tree_select"><option value =">Day1">Day1</option><option value =">Day2">Day2</option><option value=">Day3">Day3</option><option value=">Day4">Day4</option><option value =">Day5">Day5</option><option value =">Day6">Day6</option><option value =">Day7">Day7</option></select>';

		} else if(rawObject.name == 'Date') {
			rawObject.sex = '<input value="01/04/2018" class=" f_right m-bottom_5 m-top_5 m_date_W jqgrid_tree_time" id="start" type="text" onClick="WdatePicker({isShowClear:false,lang:\'en\',dateFmt:\'MM/dd/yyyy\'})">';
		}else {
			return "";
		}
	}

	$(window).bind("resize", function() {
		var width = $(".col-md-4 .jqGrid_wrapper").width();
		$("#table").setGridWidth(width);
		$("#table1").setGridWidth(width);
		$("#table2").setGridWidth(width);
		var widths = $(".col-md-12 .jqGrid_wrapper").width()-15;
		$("#table3").setGridWidth(widths);
	});
	$('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
		var width = $(".col-md-4 .jqGrid_wrapper").width();
		$("#table").setGridWidth(width);
		$("#table1").setGridWidth(width);
		$("#table2").setGridWidth(width);
		var widths = $(".col-md-12 .jqGrid_wrapper").width()-15;
		$("#table3").setGridWidth(widths);

	});
	/*添加操作按钮*/
	var content = " <div class=' titleBtnItem '><div class='btn_wrapper' title='Add'><div class='ui-title-btn'><span class='glyphicon glyphicon-plus'></span></div></div><div class='btn_wrapper padding-top8' title='Delete'><div class='ui-title-btn'><span class='glyphicon glyphicon-trash'></span></div></div></div>"
	$("#gbox_table .ui-jqgrid-titlebar").append(content);
	$("#gbox_table1 .ui-jqgrid-titlebar").append(content);
	$("#gbox_table2 .ui-jqgrid-titlebar").append(content);
	$("#gbox_table3 .ui-jqgrid-titlebar").append(content);

});