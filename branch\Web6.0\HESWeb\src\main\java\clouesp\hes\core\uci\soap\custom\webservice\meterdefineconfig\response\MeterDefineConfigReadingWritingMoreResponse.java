package clouesp.hes.core.uci.soap.custom.webservice.meterdefineconfig.response;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import clouesp.hes.core.uci.soap.custom.webservice.common.ReplyError;
import clouesp.hes.core.uci.soap.custom.webservice.common.WebservTask;

import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.enums.system.TaskState;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.redis.JedisUtils;

import ch.iec.tc57._2011.meterdefineconfig_.Arrays;
import ch.iec.tc57._2011.meterdefineconfig_.Meter;
import ch.iec.tc57._2011.meterdefineconfigmessage.MeterDefineConfigPayloadType;
import ch.iec.tc57._2011.meterdefineconfigmessage.MeterDefineConfigResponseMessageType;


/**
 * @ClassName: MeterDefineConfigReadingResponse
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年7月18日 下午1:40:34
 *
 */
public class MeterDefineConfigReadingWritingMoreResponse extends
		MeterDefineConfigResponseAbstract {

	@Override
	public void parsePayload() {
		// TODO Auto-generated method stub
		MeterDefineConfigResponseMessageType responseMessageType = (MeterDefineConfigResponseMessageType) this.object;
		responseMessageType.getHeader();
		
		MeterDefineConfigPayloadType payload = responseMessageType.getPayload();
		if(null != payload) {
			this.sn = (String) payload.getMeterDefineConfig().getMeters().get(0).getMRID();
			this.dataItemId = payload.getMeterDefineConfig().getReadingType().getRef();
			
			this.ajaxJson = createAjaxJson(payload);
			System.out.println("UCI_interface return meter parameters is === " + this.ajaxJson);
		}
	}

	/**
	 * 
	 * @Title: createAjaxJson
	 * @Description: 创建 ajaxJson 对象
	 * @param payload
	 * @return
	 * @return AjaxJson
	 * @throws
	 */
	private AjaxJson createAjaxJson(MeterDefineConfigPayloadType payload) {
		// TODO Auto-generated method stub
		AjaxJson resultJson = new AjaxJson();
		resultJson.put("messageId", messageId);
		resultJson.setMsg(result);
		if(!this.code.equals(ReplyError.SUCCESS.getCode())) {
			resultJson.setSuccess(false);
			resultJson.put("status", TaskState.Failed.getState());
		} else {
			resultJson.put("status", TaskState.Success.getState());
		}
		if(this.timestamp != null){

			Date responseTime=DateUtils.xmlDate2Date(this.timestamp);
			
			resultJson.put("responseTime", DateUtils.formatDate(responseTime, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
			
		}else{
			resultJson.put("responseTime", DateUtils.formatDate(new Date(), ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
		}
		resultJson.put("dataItemId", dataItemId);
		resultJson.put("verb", verb);
		
/*		switch(this.dataItemId) {
		case "200.0.4.10": // 电表档案下发(多次返回结果的模式)
			resultJson = createMoreResponse(payload,resultJson);
			break;
		default: // 一般模式，一次性全部返回结果
			resultJson.put("webservTask", JedisUtils.getObject(messageId));
			JedisUtils.delObject(messageId);
			break;
		}*/
		resultJson.put("webservTask", JedisUtils.getObject(messageId));
		JedisUtils.delObject(messageId);
		
		List<Arrays> arrayss = payload.getMeterDefineConfig().getArrays();
		if(null == arrayss || arrayss.isEmpty()) {
			return resultJson;
		}
		
		resultJson.put("arrays", arrayss.get(0));		
		return resultJson;
	}

	/**
	 * @Title: createMoreResponse
	 * @Description: 应对多次返回结果的模式
	 * @param payload
	 * @param json
	 * @return void
	 * @throws
	 */
	private AjaxJson createMoreResponse(MeterDefineConfigPayloadType payload,AjaxJson json) {
		// TODO Auto-generated method stub
		List<WebservTask> tasks = new ArrayList<WebservTask>();
		List<Meter> meters = payload.getMeterDefineConfig().getMeters();
		for (Meter meter : meters) {
			// 此处需要对状态进行封装
			WebservTask task = (WebservTask)JedisUtils.getObject(messageId + meter.getMRID());
			boolean flag = Boolean.parseBoolean(String.valueOf(meter.getStatus()));
			if(flag)
				task.setStatus(TaskState.Success);
			else
				task.setStatus(null);
			//task.setStatus(TaskState.parseState(String.valueOf(meter.getStatus())));
			tasks.add(task);
			JedisUtils.delObject(messageId + meter.getMRID()); // 返回一条删除一条
		}
		
		json.put("tasks", tasks);
		
		return json;
	}


}
