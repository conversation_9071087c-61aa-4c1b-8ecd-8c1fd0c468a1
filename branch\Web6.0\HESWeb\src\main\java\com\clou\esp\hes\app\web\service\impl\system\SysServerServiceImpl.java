/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysServer{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-03-29 03:12:41
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.system;

import java.util.List;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import com.clou.esp.hes.app.web.dao.system.SysServerDao;
import com.clou.esp.hes.app.web.model.system.SysServer;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.clou.esp.hes.app.web.service.system.SysServerService;

@Component
@Service("sysServerService")
public class SysServerServiceImpl  extends CommonServiceImpl<SysServer>  implements SysServerService {

	@Resource
	private SysServerDao sysServerDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(sysServerDao);
    }
	public SysServerServiceImpl() {}
	
	@Override
	public List<SysServer> vaildServerName(String name) {
		return sysServerDao.vaildServerName(name);
	}
	
	
}