/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictDataitemGroupMap{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.dict;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.dict.DictDataitemGroupMapDao;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;
import com.clou.esp.hes.app.web.model.dict.DictDataitemGroupMap;
import com.clou.esp.hes.app.web.service.dict.DictDataitemGroupMapService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@Component
@Service("dictDataitemGroupMapService")
public class DictDataitemGroupMapServiceImpl  extends CommonServiceImpl<DictDataitemGroupMap>  implements DictDataitemGroupMapService {

	@Resource
	private DictDataitemGroupMapDao dictDataitemGroupMapDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dictDataitemGroupMapDao);
    }
	public DictDataitemGroupMapServiceImpl() {}
	
	@Override
	public DictDataitemGroupMap selectParamterTypeNameByItemId(String itemId) {
		return dictDataitemGroupMapDao.selectParamterTypeNameByItemId(itemId);
	}
	@Override
	public DictDataitemGroupMap getItemGroupMapInfo(DictDataitemGroupMap itemInfo) {
		return dictDataitemGroupMapDao.getItemGroupMapInfo(itemInfo);
	}

	@Override
	public JqGridResponseTo unBindForJqGrid(JqGridSearchTo jqGridSearchTo) {
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		PageInfo<DictDataitem> pageInfo = new PageInfo<DictDataitem>(this.dictDataitemGroupMapDao.unBindForJqGrid(jqGridSearchTo));
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}
}