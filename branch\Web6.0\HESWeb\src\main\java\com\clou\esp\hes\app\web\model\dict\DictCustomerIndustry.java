/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictDataitem{ } 
 * 
 * 摘    要： 定炒数据/事件数据
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.dict;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DictCustomerIndustry  extends BaseEntity {
	//DICT_CUSTOMER_TYPE
    
	private static final long serialVersionUID = 8051632327607644226L;

	public DictCustomerIndustry() {}

	private java.lang.String name;
	
	public java.lang.String getName() {
		return MutiLangUtil.assemblyI18nData(MutiLangUtil.DICT_I18N,name);
	}

    public void setName(java.lang.String name) {
		this.name = name;
	}

   

}