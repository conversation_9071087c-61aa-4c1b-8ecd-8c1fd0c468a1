/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysServiceAttribute{ } 
 * 
 * 摘    要： sysServiceAttribute
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-03-29 03:17:56
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.system;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;

import com.clou.esp.hes.app.web.model.system.SysServiceAttribute;
import com.clou.esp.hes.app.web.service.system.SysServiceAttributeService;

/**
 * <AUTHOR>
 * @时间：2018-03-29 03:17:56
 * @描述：sysServiceAttribute类
 */
@Controller
@RequestMapping("/sysServiceAttributeController")
public class SysServiceAttributeController extends BaseController{

 	@Resource
    private SysServiceAttributeService sysServiceAttributeService;

	/**
	 * 跳转到sysServiceAttribute列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/system/sysServiceAttributeList");
    }

	/**
	 * 跳转到sysServiceAttribute新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "sysServiceAttribute")
	public ModelAndView sysServiceAttribute(SysServiceAttribute sysServiceAttribute,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(sysServiceAttribute.getId())){
			try {
                sysServiceAttribute=sysServiceAttributeService.getEntity(sysServiceAttribute.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("sysServiceAttribute", sysServiceAttribute);
		}
		return new ModelAndView("/system/sysServiceAttribute");
	}


	/**
	 * sysServiceAttribute查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=sysServiceAttributeService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除sysServiceAttribute信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(SysServiceAttribute sysServiceAttribute,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(sysServiceAttributeService.deleteById(sysServiceAttribute.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存sysServiceAttribute信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })SysServiceAttribute sysServiceAttribute,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        SysServiceAttribute t=new  SysServiceAttribute();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(sysServiceAttribute.getId())){
        	t=sysServiceAttributeService.getEntity(sysServiceAttribute.getId());
			MyBeanUtils.copyBeanNotNull2Bean(sysServiceAttribute, t);
				sysServiceAttributeService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            sysServiceAttributeService.save(sysServiceAttribute);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
	
	
	
}