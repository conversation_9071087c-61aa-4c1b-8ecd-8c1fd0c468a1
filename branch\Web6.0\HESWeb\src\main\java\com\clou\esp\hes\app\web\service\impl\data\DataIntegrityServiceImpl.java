/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataIntegrity{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-12-01 06:03:45
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.data;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.dao.asset.AssetCommunicatorDao;
import com.clou.esp.hes.app.web.dao.asset.AssetMeterDao;
import com.clou.esp.hes.app.web.dao.data.DataIntegrityDao;
import com.clou.esp.hes.app.web.model.asset.AssetCommunicator;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.data.DataIntegrity;
import com.clou.esp.hes.app.web.model.data.DataScheduleMissData;
import com.clou.esp.hes.app.web.model.data.DataScheduleProgress;
import com.clou.esp.hes.app.web.service.data.DataIntegrityService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.format.FormatUtil;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@Component
@Service("dataIntegrityService")
public class DataIntegrityServiceImpl extends CommonServiceImpl<DataIntegrity>
		implements DataIntegrityService {
	
	private static Logger logger = Logger.getLogger(DataIntegrityServiceImpl.class);
	@Resource
	private DataIntegrityDao dataIntegrityDao;
	@Resource
	private AssetCommunicatorDao assetCommunicatorDao;
	@Resource
	private AssetMeterDao assetMeterDao;

	@Autowired
	public void setCommonService() {
		super.setCommonService(dataIntegrityDao);
	}

	public DataIntegrityServiceImpl() {
	}

	@Override
	public List<Map<String, Object>> getIntegritySum(Map<String, Object> p) {
		return dataIntegrityDao.getIntegritySum(p);
	}

	@Override
//	@Transactional
	public void statisticsIntegrity() {
		try {
			logger.info("Start Statistics Integrity: Time is --->" + DateUtils.date2Str(new Date(), DateUtils.datetimeFormat));
			long start = System.currentTimeMillis();
			Date d = new Date();
			String str = DateUtils.date2Str(d, DateUtils.datetimeFormat);
			String str1 = str.substring(0, 10);
			str1 += " 00:00:00";
			d = DateUtils.str2Date(str1, DateUtils.datetimeFormat);
			//统计前一天的数据，-1 为计算昨天日期
			d = FormatUtil.addDaysToDate(d, -1);
			String tv = DateUtils.date2Str(d, DateUtils.date_sdf);
//			String mtv = DateUtils.date2Str(d, DateUtils.month_sdf);
			//删除昨日日数据/月数据, type = (1,2)
			dataIntegrityDao.deleteByTv(tv, "day");
			dataIntegrityDao.deleteByTv(tv, "month1");
			dataIntegrityDao.deleteByTv(tv, "month2");
			/**
			 * DataIntegrity
			 * DataScheduleProgress
			 * DataScheduleMissData
			 */
			dataIntegrityDao.batchInsert(DateUtils.date2Str(d, DateUtils.date_sdf), "1",ResourceUtil.getSessionattachmenttitle("profileId"));
			// 修改 当天统计出来的为负数的为0
			dataIntegrityDao.updateErrorData(tv);
			/**
			 *  manufacturer day month
			 *  统计厂商类型日、月数据
			 */
			statisticNameIntegrity(d, "month", "manufacturer");
			statisticNameIntegrity(d, "day", "manufacturer");
			/**
			 *  model day month
			 *  统计电表类型日、月数据
			 */
			statisticNameIntegrity(d, "month", "model");
			statisticNameIntegrity(d, "day", "model");
			/**
			 *  communication type day month
			 *  统计通讯类型日、月数据
			 */
			statisticNameIntegrity(d, "month", "commType");
			statisticNameIntegrity(d, "day", "commType");
			/**
			 *  Completed Reads day
			 *  Missed Reads day
			 *  Failed Reads day
			 *  抄读完成、数据丢失、抄读失败的数据
			 */
			statisticMeterIntegrity(d);
			/**
			 *  total day month
			 *  统计日、月数据汇总
			 */
			statisticTotalIntegrity(d, "day");
			statisticTotalIntegrity(d, "month");
			System.out.println("("+ d +")总耗时===" + (System.currentTimeMillis() - start));
			logger.info("End Statistics Integrity: Time is --->" + DateUtils
					.date2Str(new Date(), DateUtils.datetimeFormat) + ", Time Consuming --->" + (System.currentTimeMillis() - start));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Override
	public void statisticMetersIntegrity() {
		Date d = new Date();
		d = FormatUtil.addDaysToDate(d, -1);
		String tv = DateUtils.date2Str(d, DateUtils.date_sdf);
		dataIntegrityDao.deleteByTv(tv, "day");
		dataIntegrityDao.deleteByTv(tv, "month");
		// meter
		List<AssetMeter> mList = dataIntegrityDao
				.getAllMeterIntegrityByProfileId(ResourceUtil.getSessionattachmenttitle("profileId"), tv);
		batchInsertIntegrity("1", mList, d);
		// manufacturer
		// mList = dataIntegrityDao.getAllMeterIntegrityByIdType("2", tv);
		// batchInsertIntegrity("2", mList, d);
		// model
		// mList = dataIntegrityDao.getAllMeterIntegrityByIdType("3", tv);
		// batchInsertIntegrity("3", mList, d);
		// communication type
		// mList = dataIntegrityDao.getAllMeterIntegrityByIdType("4", tv);
		// batchInsertIntegrity("4", mList, d);

		// manufacturer day month
		statisticNameIntegrity(d, "month", "manufacturer");
		statisticNameIntegrity(d, "day", "manufacturer");
		// model day month
		statisticNameIntegrity(d, "month", "model");
		statisticNameIntegrity(d, "day", "model");
		// communication type day month
		statisticNameIntegrity(d, "month", "commType");
		statisticNameIntegrity(d, "day", "commType");
		// Completed Reads day
		// Missed Reads day
		// Failed Reads day
		statisticMeterIntegrity(d);
		// total day month
		statisticTotalIntegrity(d, "day");
		statisticTotalIntegrity(d, "month");
	}

	private void statisticTotalIntegrity(Date d, String type) {
		Map<String, Object> p = new HashMap<String, Object>();
		p.put("type", type);
		if (type != null && type.equals("day")) {
			p.put("tv", DateUtils.date2Str(d, DateUtils.date_sdf));
		} else {
			p.put("tv", DateUtils.date2Str(d, DateUtils.month_sdf));
		}
		List<Map<String, Object>> isList = getIntegritySum(p);
		BigDecimal integrity = new BigDecimal(0);
		BigDecimal countCctual = null;
		BigDecimal countTotal = null;
		if (isList != null && isList.size() > 0) {
			Map<String, Object> m = isList.get(0);
			countCctual = (BigDecimal) m.get("COUNTCCTUAL");
			countTotal = (BigDecimal) m.get("COUNTTOTAL");
			if (countTotal != null && countTotal.doubleValue() > 0) {
				integrity = new BigDecimal(countCctual.doubleValue()
						/ countTotal.doubleValue() * 100d).setScale(2,
						BigDecimal.ROUND_DOWN);
			}
		}
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-01 00:00:00");
		if (type != null && type.equals("day")) {
			saveIntegrity(d, "5", 1, integrity, countCctual, countTotal);
		} else {
			saveIntegrity(DateUtils.str2Date(
					DateUtils.formatDate(d, "yyyy-MM-01 00:00:00"), sdf), "5",
					2, integrity, countCctual, countTotal);
		}

	}

	private void statisticMeterIntegrity(Date d) {
		DataIntegrity di = new DataIntegrity();
		di.setIdType("1");
		di.setProfileId(ResourceUtil.getSessionattachmenttitle("profileId"));
		di.setStv(DateUtils.date2Str(d, DateUtils.date_sdf));
		long total = getCount(di);		//根据profile ID 和时间获取数量
		di.setIntegrity(new BigDecimal("0"));
		long zeroTotal = getCount(di);
		di.setIntegrity(new BigDecimal("100"));
		long hundredTotal = getCount(di);
		di.setIntegrity(null);
		di.setStartIntegrity(new BigDecimal("0"));
		di.setEndIntegrity(new BigDecimal("100"));
		long middleTotal = getCount(di);
		if (total != 0) {
			saveIntegrity(d, "11", 1, new BigDecimal(zeroTotal), null, null);
			saveIntegrity(d, "10", 1, new BigDecimal(middleTotal), null, null);
			saveIntegrity(d, "9", 1, new BigDecimal(hundredTotal), null, null);
			saveIntegrity(
					d,
					"8",
					1,
					new BigDecimal(Double.parseDouble(zeroTotal + "")
							/ Double.parseDouble(total + "") * 100d).setScale(
							2, BigDecimal.ROUND_DOWN), null, null);
			saveIntegrity(d, "7", 1,
					new BigDecimal(Double.parseDouble(middleTotal + "")
							/ Double.parseDouble(total + "") * 100d).setScale(
							2, BigDecimal.ROUND_DOWN), null, null);
			saveIntegrity(d, "6", 1,
					new BigDecimal(Double.parseDouble(hundredTotal + "")
							/ Double.parseDouble(total + "") * 100d).setScale(
							2, BigDecimal.ROUND_DOWN), null, null);

		} else {
			saveIntegrity(d, "6", 1, new BigDecimal(0), null, null);
			saveIntegrity(d, "7", 1, new BigDecimal(0), null, null);
			saveIntegrity(d, "8", 1, new BigDecimal(0), null, null);
			saveIntegrity(d, "9", 1, new BigDecimal(0), null, null);
			saveIntegrity(d, "10", 1, new BigDecimal(0), null, null);
			saveIntegrity(d, "11", 1, new BigDecimal(0), null, null);
		}
	}

	private void saveIntegrity(Date d, String idType, int tvType,
			BigDecimal integrity, BigDecimal countCctual, BigDecimal countTotal) {
		DataIntegrity di = new DataIntegrity();
		di.setIdType(idType);
		di.setProfileId(ResourceUtil.getSessionattachmenttitle("profileId"));
		di.setTvType(tvType);
		if (tvType == 1) {
//			di.setTv(FormatUtil.addMinutesToDate(d, 1));
			di.setTv(d);
		} else {
			di.setTv(d);
		}
		di.setIntegrity(integrity);
		if (countCctual != null) {
			di.setCountActual(countCctual.intValue());
		} else {
			di.setCountActual(0);
		}
		if (countCctual != null) {
			di.setCountTotal(countTotal.intValue());
		} else {
			di.setCountTotal(0);
		}
		di.setId(idType);
		dataIntegrityDao.insert(di);
		// save(di);
	}

	private void statisticNameIntegrity(Date d, String type, String name) {
		if (d == null) {
			return;
		}
		if (StringUtil.isEmpty(name)) {
			return;
		}
		Map<String, Object> p = new HashMap<String, Object>();
		if (type != null && type.equals("month")) {
			p.put("tv", DateUtils.date2Str(d, DateUtils.month_sdf));
		} else {
			p.put("tv", DateUtils.date2Str(d, DateUtils.date_sdf));
		}
		p.put("type", type);// day//month
		p.put("name", name);// name=manufacturer;model;commType
		p.put("chartType", "chart");// chart
		List<Map<String, Object>> isList = getIntegrityNameSum(p);
		if (isList == null || isList.size() <= 0) {
			return;
		}
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-01 00:00:00");
		List<DataIntegrity> diList = new ArrayList<DataIntegrity>();
		for (Map<String, Object> m : isList) {
			DataIntegrity di = new DataIntegrity();
			String diId = (String) m.get("ID");
			if (StringUtil.isEmpty(diId)) {
				continue;
			}
			di.setId(diId);
			if (name.equals("manufacturer")) {
				di.setIdType("2");
			} else if (name.equals("model")) {
				di.setIdType("3");
			} else if (name.equals("commType")) {
				di.setIdType("4");
			} else {
				continue;
			}
			di.setProfileId(ResourceUtil.getSessionattachmenttitle("profileId"));
			if (type != null && type.equals("month")) {
				di.setTvType(2);
				di.setTv(DateUtils.str2Date(
						DateUtils.formatDate(d, "yyyy-MM-01 00:00:00"), sdf));
			} else {
				di.setTvType(1);
//				di.setTv(FormatUtil.addMinutesToDate(d, 1));	//添加分钟到日期
				di.setTv(d);
			}

			BigDecimal countCctual = (BigDecimal) m.get("COUNTCCTUAL");
			BigDecimal countTotal = (BigDecimal) m.get("COUNTTOTAL");
			BigDecimal integrity = new BigDecimal("0");
			if (countTotal != null && countTotal.doubleValue() != 0) {
				integrity = new BigDecimal(countCctual.doubleValue()
						/ countTotal.doubleValue() * 100d).setScale(2,
						BigDecimal.ROUND_DOWN);
			}
			di.setIntegrity(integrity);
			di.setCountTotal(countTotal.intValue());
			di.setCountActual(countCctual.intValue());
			diList.add(di);
		}
		if (diList != null && diList.size() > 0) {
			dataIntegrityDao.batchSave(diList);
			diList.clear();
		}

	}

	private void batchInsertIntegrity(String idType, List<AssetMeter> mList,
			Date d) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
		d = DateUtils.str2Date(DateUtils.date2Str(d, sdf), sdf);
		long time = d.getTime();
		Date nd = FormatUtil.addDaysToDate(d, 1);
		long ntime = nd.getTime();
		if (mList != null && mList.size() > 0) {
			List<DataIntegrity> diList = new ArrayList<DataIntegrity>();
			int size = mList.size();
			for (int i = 1; i <= mList.size(); i++) {
				AssetMeter m = mList.get(i - 1);
				DataIntegrity di = new DataIntegrity();
				if (idType.equals("1")) {
					di.setId(m.getId());
					BigDecimal zero = new BigDecimal("0");
					long count = 0;
					if (m.getDspTv() == null) {
						di.setIntegrity(zero);
					} else {
						long dspTime = m.getDspTv().getTime();
						if (dspTime < time) {
							di.setIntegrity(zero);
						} else if (dspTime >= ntime) {
							di.setIntegrity(new BigDecimal(
									100.0d - (Double.parseDouble(m
											.getMissDataCount() + "") / 24.0d * 100.0d)));
							count = 24 - m.getMissDataCount();
						} else {
							count = (dspTime - time) / 1000 / 60 / 60 + 1
									- m.getMissDataCount();
							if (count <= 0) {
								count = 0;
							}
							di.setIntegrity(new BigDecimal((Double
									.parseDouble(count + "") / 24.0d * 100.0d)));
						}
					}
					di.setCountTotal(24);
					di.setCountActual(Integer.parseInt(count + ""));
				} else {
					di.setId(m.getIntegrityId());
					di.setIntegrity(m.getAve());
				}
				di.setTvType(1);
				di.setIdType(idType);
				di.setProfileId(ResourceUtil.getSessionattachmenttitle("profileId"));
				di.setTv(d);
				diList.add(di);
				if (i == size) {
					if (diList != null && diList.size() > 0) {
						dataIntegrityDao.batchSave(diList);
						diList.clear();
					}
					break;
				}
				if (i % 30000 == 0) {
					if (diList != null && diList.size() > 0) {
						dataIntegrityDao.batchSave(diList);
						diList.clear();
					}
				}

			}
		}
	}

	@Override
	public List<Map<String, Object>> getIntegrityNameSum(Map<String, Object> p) {
		return dataIntegrityDao.getIntegrityNameSum(p);
	}

	@Override
	public List<Map<String, Object>> getDataIntegrityByMap(
			Map<String, Object> params) {
		return dataIntegrityDao.getDataIntegrityByMap(params);
	}

	@Override
	public List<Map<String, Object>> getDataIntegrityByMaps(
			Map<String, Object> params) {
		return dataIntegrityDao.getDataIntegrityByMaps(params);
	}

	@Override
	public JqGridResponseTo getForMeterJqGrid(JqGridSearchTo jqGridSearchTo) {
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		List<DataIntegrity> listMeter = dataIntegrityDao.getForMeterJqGrid(jqGridSearchTo);
		PageInfo<DataIntegrity> pageInfoMeter = new PageInfo<DataIntegrity>(listMeter);
		return JqGridHandler.GetJqGridPageJsonData(pageInfoMeter, jqGridSearchTo);
	}

}