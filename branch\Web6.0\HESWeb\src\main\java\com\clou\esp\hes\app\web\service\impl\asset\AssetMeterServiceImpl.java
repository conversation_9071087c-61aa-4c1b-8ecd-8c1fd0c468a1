/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeter{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-06 08:04:55
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.asset;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.asset.AssetMeterDao;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.asset.AssetMeterDto;
import com.clou.esp.hes.app.web.model.asset.VendMeterInitialCreditAmount;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@Component
@Service("assetMeterService")
public class AssetMeterServiceImpl extends CommonServiceImpl<AssetMeter>	implements AssetMeterService {

	@Resource
	private AssetMeterDao assetMeterDao;

	@Autowired
	public void setCommonService() {
		super.setCommonService(assetMeterDao);
	}

	public AssetMeterServiceImpl() {
	}

	@Override
	public int batchInsert(List<AssetMeter> mList) {
		return assetMeterDao.batchInsert(mList);
	}

	@Override
	public List<AssetMeter> getListGroupByFwVersion() {
		return assetMeterDao.getListGroupByFwVersion();
	}

	@Override
	public List<AssetMeter> getFwVersionGroupByModel(String modelTypeId) {
		return assetMeterDao.getFwVersionGroupByModel(modelTypeId);
	}

	@Override
	public AssetMeter getMeterDetailInfo(String id) {
		return assetMeterDao.getMeterDetailInfo(id);
	}

	@Override
	public AssetMeter getEntityBySN(String sn) {
		return assetMeterDao.getEntityBySN(sn);
	}

	@Override
	public List<AssetMeter> getBySns(List<String> sns) {
		return assetMeterDao.getBySns(sns);
	}
	@Override
	public List<AssetMeter> getByMacs(List<String> sns) {
		return assetMeterDao.getByMacs(sns);
	}
	
	@Override
	public JqGridResponseTo getForJqGridAdvanced(JqGridSearchTo jqGridSearchTo) {
		// TODO Auto-generated method stub

		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		PageInfo<AssetMeter> pageInfo = new PageInfo<AssetMeter>(assetMeterDao.getForJqGridAdvanced(jqGridSearchTo));
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
		
	}

	@Override
	public List<String> getMeterIdsByCommunicatorId(String communicatorId) {
		return this.assetMeterDao.getMeterIdsByCommunicatorId(communicatorId);
	}

	@Override
	public List<AssetMeter> getListLimitTwenty(Map<String, Object> p) {
		// TODO Auto-generated method stub
		return  this.assetMeterDao.getListLimitTwenty(p);
	}
	
	@Override
	public List<AssetMeter> getMeter4Template(JqGridSearchTo jqGridSearchTo) {
		return  this.assetMeterDao.getForJqGrid(jqGridSearchTo);
	}

	@Override
	public List<AssetMeter> getListOfDCU(Map<String, Object> p) {
		// TODO Auto-generated method stub
		return  this.assetMeterDao.getListOfDCU(p);
	}
	
	@Override
	public List<AssetMeter> getPreListOfDCU(Map<String, Object> p) {
		// TODO Auto-generated method stub
		return  this.assetMeterDao.getPreListOfDCU(p);		
	}
	
	@Override
	public List<AssetMeter> getByIds(List<String> ids) {
		return this.assetMeterDao.getByIds(ids);
	}

	@Override
	public AssetMeterDto getMeterDtoInfo(String id) {
		List<AssetMeterDto> dtos= this.assetMeterDao.getMeterDtoInfo(id);
		if(dtos!=null&&dtos.size()>0) {
			return dtos.get(0);
		}
		return null;
	}
	
	public void savePLCMeter(AssetMeter meter) {
		this.assetMeterDao.savePLCMeter(meter);
	}

	@Override
	public AssetMeter getEntityPLC(String sn) {
		return this.assetMeterDao.getEntityPLC(sn);
	}

	@Override
	public List<AssetMeter> getForJqGridPLC(String sn) {
		return this.assetMeterDao.getForJqGridPLC(sn);
	}

	public AssetMeter getEntityPLCExtend(String id) {
		return this.assetMeterDao.getEntityPLCExtend(id);
	}
	
	@Override
	public void deletePrepareMeter(List<String> ids) {
		this.assetMeterDao.deletePrepareMeter(ids);
	}
	
	@Override
	public long getCountPrepare(AssetMeter meter) {
		return this.assetMeterDao.getCountPrepare(meter);
	}
	@Override
	public AssetMeter getEntityByMac(String mac) {
		return this.assetMeterDao.getEntityByMac(mac);
	}
	@Override
	public AssetMeter getEntityByMacPLC(String mac) {
		return this.assetMeterDao.getEntityByMacPLC(mac);
	}

	@Override
	public void insertCredit(VendMeterInitialCreditAmount creditAmount) {
		this.assetMeterDao.insertCredit(creditAmount);
	}

	@Override
	public int batchInsertPrepaid(List<AssetMeter> mList) {
		return this.assetMeterDao.batchInsertPrepaid(mList);
	}

	@Override
	public int batchInsertCredit(List<VendMeterInitialCreditAmount> mList) {
		return this.assetMeterDao.batchInsertCredit(mList);
	}
	@Override
	public int batchGetList(List<AssetMeter> mList) {
		return this.assetMeterDao.batchGetList(mList);
	}@Override
	public int batchGetListPrepare(List<AssetMeter> mList) {
		return this.assetMeterDao.batchGetListPrepare(mList);
	}

	@Override
	public void deletePpmAssetMeter(String meterId) {
		 this.assetMeterDao.deletePpmAssetMeter(meterId);
	}
	
}