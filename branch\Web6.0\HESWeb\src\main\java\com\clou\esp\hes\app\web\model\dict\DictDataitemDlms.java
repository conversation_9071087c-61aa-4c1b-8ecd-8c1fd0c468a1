/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictDataitemDlms{ } 
 * 
 * 摘    要： dlms规约数据项
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.dict;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DictDataitemDlms  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DictDataitemDlms() {
	}

	/**appType*/
	private java.lang.String appType;
	/**name*/
	private java.lang.String name;

	/**
	 * appType
	 * @return the value of DICT_DATAITEM_DLMS.APP_TYPE
	 * @mbggenerated 2017-11-22 03:30:03
	 */
	public java.lang.String getAppType() {
		return appType;
	}

	/**
	 * appType
	 * @param appType the value for DICT_DATAITEM_DLMS.APP_TYPE
	 * @mbggenerated 2017-11-22 03:30:03
	 */
    	public void setAppType(java.lang.String appType) {
		this.appType = appType;
	}
	/**
	 * name
	 * @return the value of DICT_DATAITEM_DLMS.NAME
	 * @mbggenerated 2017-11-22 03:30:03
	 */
	public java.lang.String getName() {
		return name;
	}

	/**
	 * name
	 * @param name the value for DICT_DATAITEM_DLMS.NAME
	 * @mbggenerated 2017-11-22 03:30:03
	 */
    	public void setName(java.lang.String name) {
		this.name = name;
	}

	public DictDataitemDlms(java.lang.String appType 
	,java.lang.String name ) {
		super();
		this.appType = appType;
		this.name = name;
	}

}