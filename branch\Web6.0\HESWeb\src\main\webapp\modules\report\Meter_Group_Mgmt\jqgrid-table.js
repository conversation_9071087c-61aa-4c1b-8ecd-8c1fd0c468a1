$(document).ready(function() {
	$.jgrid.defaults.styleUI = "Bootstrap";
	var mydata = [{
		id: "1",
		invdate: "TOU Group Name5",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		note: "190",
		amount: "Done",
		tax: "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "2",
		invdate: "TOU Group Name5",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		note: "170",
		amount: "Done",
		tax: "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total1]",
		totals: "430.00"
	}, {
		id: "3",
		invdate: "TOU Group Name5",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		note: "190",
		amount: "Done",
		tax: "12/20/2017 15:45:04",
		total: "Active Energy -Import [Register] [Total2]",
		totals: "430.00"

	}, {
		id: "4",
		invdate: "TOU Group Name5",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		note: "230",
		amount: "Done",
		tax: "12/20/2017 15:45:05",
		total: "Active Energy -Import [Register] [Total3]",
		totals: "430.00"
	}, {
		id: "5",
		invdate: "TOU Group Name6",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		amount: "Failed",
	}, {
		id: "6",
		invdate: "TOU Group Name6",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		note: "180",
		amount: "Done",
		tax: "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total1]",
		totals: "430.00"
	}, {
		id: "7",
		invdate: "TOU Group Name6",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		note: "190",
		amount: "Done",
		tax: "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total2]",
		totals: "430.00"
	}, {
		id: "8",
		invdate: "TOU Group Name6",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		note: "170",
		amount: "Done",
		tax: "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total3]",
		totals: "430.00",
	}, {
		id: "9",
		invdate: "TOU Group Name7",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		note: "180",
		amount: "Done",
		tax: "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "TOU Group Name7",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		amount: "Failed",
	}, {
		id: "10",
		invdate: "TOU Group Name7",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		amount: "Failed",

	}, {
		id: "10",
		invdate: "TOU Group Name7",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		note: "190",
		amount: "Done",
		tax: "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total2]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "TOU Group Name8",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		amount: "Failed",
	}, {
		id: "10",
		invdate: "TOU Group Name8",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		note: "170",
		amount: "Done",
		tax: "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "TOU Group Name8",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		amount: "Failed",
	}, {
		id: "10",
		invdate: "TOU Group Name8",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		note: "170",
		amount: "Done",
		tax: "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "TOU Group Name9",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		note: "170",
		amount: "Done",
		tax: "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "TOU Group Name9",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		note: "170",
		amount: "Done",
		tax: "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "TOU Group Name9",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		note: "170",
		amount: "Done",
		tax: "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "TOU Group Name9",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		amount: "Failed",

	}, {
		id: "10",
		invdate: "TOU Group Name5",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		amount: "Failed",
	}, {
		id: "10",
		invdate: "TOU Group Name5",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		amount: "Failed",
	}, {
		id: "10",
		invdate: "TOU Group Name5",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		amount: "Failed",

	}, {
		id: "10",
		invdate: "TOU Group Name5",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		note: "170",
		amount: "Failed",
		tax: "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "TOU Group Name5",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		note: "170",
		amount: "Failed",
		tax: "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "TOU Group Name5",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		note: "170",
		amount: "Failed",

	}, {
		id: "10",
		invdate: "TOU Group Name5",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		note: "170",
		amount: "Failed",
		tax: "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "TOU Group Name5",
		name: "12/20/2017 15:45:00",
		Reason: "Group Description xxxxxxxx1",
		Meter_Standard: "IEC 62056 DLMS",
		note: "170",
		amount: "Failed",
		tax: "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}];
	  var lastsel;
	$("#table_list").jqGrid({
		data: mydata,
		datatype: "local",
		autowidth: true,
		gridview: true,
		height: "109px",
		caption: 'TOU Group List  ',
		/*direction:"rtl",*/
		rownumbers: true,
		altRows: true, //单双行样式不同  
		altclass: 'differ',
		editurl:'http://************:8081/HESWeb/systemController/index.do',
		colNames: ["", "Group Name", "Meter Standard","Meter Number", "Description"],
		colModel: [{
				name: "id",
				index: "id",
				editable: true,
				width: 60,
				frozen: true,
				search: false,
				align: "right",
				hidden: true,
				sorttype: "int"

			}, {
				name: "invdate",
				index: "invdate",
				editable: true,
				width: 100,
				editable : true
			},
			{
				name: "Meter_Standard",
				index: "Meter_Standard",
				editable: true,
				width: 100,editable : false
			},
			{
				name: "note",
				index: "note",
				editable: true,
				width: 100,
			},
			{
				name: "Reason",
				index: "Reason",
				editable: true,
				width: 70,editable : true

			}
		],
		deepempty : true,
		viewrecords: true,
		multiselect: false,
		ondblClickRow : function(id) {
          if (id && id !== lastsel) {
            jQuery('#table_list').jqGrid('restoreRow', lastsel);
            jQuery('#table_list').jqGrid('editRow', id, true);
            lastsel = id;
          }
      },
		/*editurl: "/RowEditing",*/
		shrinkToFit: true,
		autoScroll: true
	});
	$("#table_list").jqGrid("navGrid", "#pager_list", {
			edit: false,
			add: false,
			del: false,
			search: false
		},

		{
			height: 200,
			reloadAfterSubmit: true
		});
	var width = $("#table_list").parents(".jqGrid_wrapper").width();
	$("#table_list").setGridWidth(width);
	$("#pager_list").width(width);
	$(window).bind("resize", function() {
		var width = $("#table_list").parents(".jqGrid_wrapper").width();
		$("#table_list").setGridWidth(width);
		$("#pager_list").width(width);
	});
	$("#table_list1").jqGrid({
		data: mydata,
		datatype: "local",
		autowidth: true,
		gridview: true,
		height: "109px",
		caption: 'Limiter Group List',
		/*direction:"rtl",*/
		rownumbers: true,
		altRows: true, //单双行样式不同  
		altclass: 'differ',

		colNames: ["", "Group Name", "Meter Standard","Meter Number", "Description"],
		colModel: [{
				name: "id",
				index: "id",
				editable: true,
				width: 60,
				frozen: true,
				search: false,
				align: "right",
				hidden: true,
				sorttype: "int"

			}, {
				name: "invdate",
				index: "invdate",
				editable: true,
				width: 100,
			},{
				name: "Meter_Standard",
				index: "Meter_Standard",
				editable: true,
				width: 100,editable : false
			},
			{
				name: "note",
				index: "note",
				editable: true,
				width: 100,
			},
			{
				name: "Reason",
				index: "Reason",
				editable: true,
				width: 70,

			}
		],
		viewrecords: true,
		deepempty : true,
		multiselect: false,
		/*editurl: "/RowEditing",*/
		shrinkToFit: true,
		autoScroll: true
	});
	//Result=Failed 的时候改变字体的颜色为红色
	function addCellAttr(rowId, val, rawObject, cm, rdata) {
		if(rawObject.amount == 'Failed') {
			return "style='color:red'";
		}
	}

	$("#table_list1").jqGrid("navGrid", "#pager_list1", {
			edit: false,
			add: false,
			del: false,
			search: false
		},

		{
			height: 200,
			reloadAfterSubmit: true
		});

	$(window).bind("resize", function() {
		var width = $("#tab_1_1 .col-md-12 .jqGrid_wrapper").width();
		$("#table_list").setGridWidth(width);
	
			var widths = $("#tab_1_2 .col-md-12 .jqGrid_wrapper").width();
		$("#table_list1").setGridWidth(widths);
	});
	$('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
		var width = $("#tab_1_1 .col-md-12 .jqGrid_wrapper").width();
		$("#table_list").setGridWidth(width);
		var widths = $("#tab_1_2 .col-md-12 .jqGrid_wrapper").width();
		$("#table_list1").setGridWidth(widths);
	});

$("#tab_1_2 #gbox_table_list1 .ui-jqgrid-titlebar").append(" <div class=' titleBtnItem '><div class='btn_wrapper' title='Add' onclick='add_group_layer()'><div class='ui-title-btn'><span class='glyphicon glyphicon-plus'></span></div></div><div class='btn_wrapper padding-top8' title='Delete' onclick='del_group_layer()'><div class='ui-title-btn'><span class='glyphicon glyphicon-trash'></span></div></div></div>");

});