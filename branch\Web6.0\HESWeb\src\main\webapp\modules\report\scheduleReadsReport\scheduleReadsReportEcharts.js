//echarts
$(document).ready(function() {
	autoHeight();
	myChart = echarts.init(document.getElementById('main'));
	myChart1 = echarts.init(document.getElementById('main1'));
	myChart2 = echarts.init(document.getElementById('main2'));
	myChart3 = echarts.init(document.getElementById('main3'));
	myChart6 = echarts.init(document.getElementById('main6'));
	myChart7 = echarts.init(document.getElementById('main7'));
	myChart10 = echarts.init(document.getElementById('main10'));
	myChart11 = echarts.init(document.getElementById('main11'));
	myChart4 = echarts.init(document.getElementById('main4'));
	myChart5 = echarts.init(document.getElementById('main5'));
	myChart8 = echarts.init(document.getElementById('main8'));
	myChart9 = echarts.init(document.getElementById('main9'));
	myChart12 = echarts.init(document.getElementById('main12'));
	myChart13 = echarts.init(document.getElementById('main13'));
	myChart14 = echarts.init(document.getElementById('main14'));
	myChart15 = echarts.init(document.getElementById('main15'));
	myChart16 = echarts.init(document.getElementById('main16'));
	myChart17 = echarts.init(document.getElementById('main17'));
	myChart.resize();
	myChart1.resize();
	myChart2.resize();
	myChart3.resize();
	myChart4.resize();
	myChart5.resize();
	myChart6.resize();
	myChart7.resize();
	myChart8.resize();
	myChart9.resize();
	myChart10.resize();
	myChart11.resize();
	myChart12.resize();
	myChart13.resize();
	myChart14.resize();
	myChart15.resize();
	myChart16.resize();
	myChart17.resize();
	getDailyMeterReadsIntegrity();
	getMonthlyMeterReadsIntegrity();
	
	getDailyModelIntegrityBarCharts();
	getMonthModelIntegrityBarCharts();
	getDailyModelIntegrity();
	getMonthModelIntegrity();
	
	getDailyCommTypeIntegrityBarCharts();
	getMonthCommTypeIntegrityBarCharts();
	getDailyCommTypeIntegrity();
	getMonthCommTypeIntegrity();
	
	
	getDailyManufacturerIntegrityBarCharts();
	getMonthManufacturerIntegrityBarCharts();
	getDailyManufacturerIntegrity();
	getMonthManufacturerIntegrity();
	
	getDailyOrgIntegrityBarCharts();
	getMonthOrgIntegrityBarCharts();
	getDailyOrgIntegrity();
	getMonthOrgIntegrity();
	
});

function autoHeight() {
	var height = document.body.clientHeight;
	$("#main").height(height - 330);
	$("#main1").height(height - 330);
	$("#main4").height(height - 360);
	$("#main5").height(height - 360);
	$("#main8").height(height - 360);
	$("#main9").height(height - 360);
	$("#main12").height(height - 360);
	$("#main13").height(height - 360);
	$("#main16").height(height - 360);
	$("#main17").height(height - 360);
}

var myChart = null;
var myChart1 = null;
var myChart2 = null;
var myChart3 = null;
var myChart4 = null;
var myChart5 = null;
var myChart6 = null;
var myChart7 = null;
var myChart8 = null;
var myChart9 = null;
var myChart10 = null;
var myChart11 = null;
var myChart12 = null;
var myChart13 = null;
var myChart14 = null;
var myChart15 = null;
var myChart16 = null;
var myChart17 = null;
function sleep(numberMillis) {
	var now = new Date();
	var exitTime = now.getTime() + numberMillis;
	while (true) {
		now = new Date();
		if (now.getTime() > exitTime)
			return;
	}
}
function getDailyMeterReadsIntegrity() {
	myChart.showLoading();
	setTimeout("myChart.hideLoading(); ", 30000);
	var selectOrgId = $("#selectScheOrgId").val();
	var profileId = $("#selectProfileCycleType").val();
	console.log('profileId: ' + profileId)
	var startDay = $("#startDay").val();
	var endDay = $("#endDay").val();
	if (startDay == null || startDay == '') {
		return;
	}
	if (endDay == null || endDay == '') {
		return;
	}
	$.ajax({
		type : 'POST',
		url : getRootPathWeb() + '/dataIntegrityController/getDailyMeterReadsIntegrity.do',
		data : 'startTv=' + startDay + '&&endTv=' + endDay + '&&type=day&&defaultOrgId='+selectOrgId + '&profileId=' + profileId,
		dataType : 'json',
		success : function(data) {
			myChart.hideLoading();
			var tvList = null;
			var integrityList = null;
			if (data.success) {
				tvList = data.attributes.tvList;
				integrityList = data.attributes.integrityList;
			}else {
				window.parent.layer.msg(data.msg, {
					icon : 2
				});
				
			   return;
			}
			var option = {
				color : [ '#33ccdb' ],
				tooltip : {
					trigger : 'axis',
					formatter : function(params) {
						var result = params[0].seriesName + '</br>';
						for (var i = 0, l = params.length; i < l; i++) {
							params.forEach(function(item) {
								result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'
										+ item.color + '"></span>'
										+ params[i].name + ' : ' + params[i].value .toFixed(2) + '%';
							});
						}
						return result;
					},
					axisPointer : { // 坐标轴指示器，坐标轴触发有效
						type : 'cross' // 默认为直线，可选为：'line' | 'shadow'
					}
				},
				grid : {
					top : '12%',
					left : '3%',
					right : '4%',
					bottom : '3%',
					containLabel : true
				},
				xAxis : [ {
					type : 'category',
					data : tvList,
					boundaryGap: false,
					/*axisTick : {
						alignWithLabel : true
					}*/
				} ],
				yAxis : [ {
					axisLabel : {
						formatter : '{value} %'
					},
					type : 'value',
					scale : true,
					max : 100,
					min : 0,
					splitNumber : 5,
					boundaryGap : [ 0.05, 0.05 ],
					splitArea : {
						show : true
					},
				} ],
				series : [ {
					name : i18n.t('scheduleReadsReport.dMeterInteRate'),
					type : 'line',
					smooth: true,
					itemStyle: {
						normal: {
							color: '#33ccdb',

							areaStyle: {

								type: 'default',
								opacity: 0.5,
							}
						}
					},
					/*barWidth : '60%',*/
					data : integrityList,
					label : {
						normal : {
							show : true,
							position : 'top',
							formatter : function(c) {
								var f = parseFloat(c.value);
								if (isNaN(f)) {
									return false;
								}
								var f = Math.round(c.value * 100) / 100;
								var s = f.toString();
								var rs = s.indexOf('.');
								if (rs < 0) {
									rs = s.length;
									s += '.';
								}
								while (s.length <= rs + 2) {
									s += '0';
								}
								return s + '%';
							}
						}
					},
					cursor : 'default'
				} ]
			};
			myChart.setOption(option);
		},
		error : function(msg) {
		}
	});
}

function getMonthlyMeterReadsIntegrity() {
	myChart1.showLoading();
	setTimeout("myChart1.hideLoading(); ", 30000);
	var selectOrgId = $("#selectScheOrgId").val();
	var profileId = $("#selectProfileCycleType").val();
	var startMonth = $("#startMonth").val();
	var endMonth = $("#endMonth").val();
	if (startMonth == null || startMonth == '') {
		return;
	}
	if (endMonth == null || endMonth == '') {
		return;
	}
	$
			.ajax({
				type : 'POST',
				url : getRootPathWeb()
						+ '/dataIntegrityController/getDailyMeterReadsIntegrity.do',
				data : 'startTv=' + startMonth + '&&endTv=' + endMonth
						+ '&&type=month&&defaultOrgId='+selectOrgId
						+ '&profileId='+profileId,
				dataType : 'json',
				success : function(data) {
					myChart1.hideLoading();
					var tvList = null;
					var integrityList = null;
					if (data.success) {
						tvList = data.attributes.tvList;
						integrityList = data.attributes.integrityList;
					}else {
						window.parent.layer.msg(data.msg, {
							icon : 2
						});
						
					   return;
					}
					var option1 = {
						color : [ '#1bc5ad' ],
						tooltip : {
							trigger : 'axis',
							formatter : function(params) {
								var result = params[0].seriesName + '</br>';
								for (var i = 0, l = params.length; i < l; i++) {
									params
											.forEach(function(item) {
												result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'
														+ item.color
														+ '"></span>'
														+ params[i].name
														+ ' : '
														+ params[i].value
																.toFixed(2)
														+ '%';
											});
								}

								return result;
							},
							axisPointer : { // 坐标轴指示器，坐标轴触发有效
								type : 'cross' // 默认为直线，可选为：'line' | 'shadow'
							}
						},
						grid : {
							top : '12%',
							left : '3%',
							right : '4%',
							bottom : '3%',
							containLabel : true
						},
						xAxis : [ {
							type : 'category',
							data : tvList,
							boundaryGap: false,
							/*axisTick : {
								alignWithLabel : true
							}*/
						} ],
						yAxis : [ {
							axisLabel : {
								formatter : '{value} %'
							},
							max : 100,
							min : 0,
							type : 'value',
							scale : true,
							splitNumber : 5,
							boundaryGap : [ 0.05, 0.05 ],
							splitArea : {
								show : true,

							},
							formatter : '{value} %',
						} ],
						series : [ {
							name : i18n.t('scheduleReadsReport.mmeterInteRate'),
							type : 'line',
							smooth: true,
							itemStyle: {
								normal: {
									color: '#1bc5ad',

									areaStyle: {

										type: 'default',
										opacity: 0.5,
									}
								}
							},
							data : integrityList,
							label : {
								normal : {
									show : true,
									position : 'top',
									formatter : function(c) {
										var f = parseFloat(c.value);
										if (isNaN(f)) {
											return false;
										}
										var f = Math.round(c.value * 100) / 100;
										var s = f.toString();
										var rs = s.indexOf('.');
										if (rs < 0) {
											rs = s.length;
											s += '.';
										}
										while (s.length <= rs + 2) {
											s += '0';
										}
										return s + '%';
									}
								}
							},
							cursor : 'default'
						} ]
					};
					myChart1.setOption(option1);
				},
				error : function(msg) {
				}
			});

}

function getDailyManufacturerIntegrityBarCharts() {
	
	myChart2.showLoading();
	setTimeout("myChart2.hideLoading(); ", 30000);
	var tv = $("#dailyManufacturerIntegrity").val();
	var selectOrgId = $("#selectScheOrgId").val();
	var profileId = $("#selectProfileCycleType").val();
	if (tv == null || tv == '') {
		return;
	}
	$
			.ajax({
				type : 'POST',
				url : getRootPathWeb()
						+ '/dataIntegrityController/getDailyNameIntegrity.do',
				data : 'tv=' + tv + '&&type=day&&name=manufacturer&&defaultOrgId='+selectOrgId
					+ '&profileId='+profileId,
				dataType : 'json',
				success : function(data) {
					myChart2.hideLoading();
					var nameList = null;
					var integrityList = null;
					var idList = null;
					var cList = new Array();
					if (data.success) {
						idList = data.attributes.idList;
						if (idList != null && idList.length > 0) {
							for (var i = 0; i < idList.length; i++) {
								var color = getColor();
								var id = idList[i];
								if (id != null) {
									if (id == '101') {
										color = 'rgb(255,100,100)';
									} else if (id == '102') {
										color = 'rgb(0,182,230)';
									} else if (id == '103') {
										color = '#e4ca41';
									}
								}
								cList[i] = color;
							}
						}
						nameList = data.attributes.nameList;
						integrityList = data.attributes.integrityList;
					}else {
						window.parent.layer.msg(data.msg, {
							icon : 2
						});
						
					   return;
					}
					var option2 = {
						tooltip : {

							trigger : 'axis',
							formatter : function(params) {
								var result = params[0].name + '</br>';
								for (var i = 0, l = params.length; i < l; i++) {
									params
											.forEach(function(item) {
												result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'
														+ item.color
														+ '"></span>'
														+ params[i].seriesName
														+ ' : '
														+ params[i].value
																.toFixed(2)
														+ '%';
											});
								}

								return result;
							},
							axisPointer : { // 坐标轴指示器，坐标轴触发有效
								type : 'shadow' // 默认为直线，可选为：'line' | 'shadow'

							}
						},
						legend : {
							type: 'scroll',
							data : nameList
						},
						grid : {
							top : '12%',
							left : '3%',
							right : '4%',
							bottom : '3%',
							containLabel : true
						},
						xAxis : [ {
							zlevel: 0,
							z: 2,
							type : 'category',
							data : nameList,
							axisTick : {
								alignWithLabel : true
							}
						} ],
						yAxis : [ {
							axisLabel : {
								formatter : '{value} %'
							},
							max : 100,
							min : 60,
							type : 'value',
							scale : true,
							splitNumber : 5,
							boundaryGap : [ 0.05, 0.05 ],
							splitArea : {
								show : true,

							},
							formatter : '{value} %',
						} ],
						series : [ {
							itemStyle : {
								normal : {
									color : function(params) {
										// var colorList = [ 'rgb(255,100,100)',
										// 'rgb(0,182,230)', '#1bc5ad' ];
										// return colorList[params.dataIndex];
										return cList[params.dataIndex];
									},

								}
							},
							name : i18n.t('scheduleReadsReport.dailyInteRate'),
							type : 'bar',
							barWidth : '60%',
							data : integrityList,
							label : {
								normal : {
									show : true,
									position : 'top',
									formatter : function(c) {
										var f = parseFloat(c.value);
										if (isNaN(f)) {
											return false;
										}
										var f = Math.round(c.value * 100) / 100;
										var s = f.toString();
										var rs = s.indexOf('.');
										if (rs < 0) {
											rs = s.length;
											s += '.';
										}
										while (s.length <= rs + 2) {
											s += '0';
										}
										return s + '%';
									}
								}
							},
							cursor : 'default'
						} ]
					};
					myChart2.setOption(option2);
				},
				error : function(msg) {
				}
			});
}

function getMonthManufacturerIntegrityBarCharts() {
	myChart3.showLoading();
	setTimeout("myChart3.hideLoading(); ", 30000);
	var tv = $("#monthManufacturerIntegrity").val();
	var selectOrgId = $("#selectScheOrgId").val();
	var profileId = $("#selectProfileCycleType").val();
	if (tv == null || tv == '') {
		return;
	}
	$
			.ajax({
				type : 'POST',
				url : getRootPathWeb()
						+ '/dataIntegrityController/getDailyNameIntegrity.do',
				data : 'tv=' + tv + '&&type=month&&name=manufacturer&&defaultOrgId='+selectOrgId
					+ '&profileId='+profileId,
				dataType : 'json',
				success : function(data) {
					myChart3.hideLoading();
					var nameList = null;
					var integrityList = null;
					var idList = null;
					var cList = new Array();
					if (data.success) {
						idList = data.attributes.idList;
						if (idList != null && idList.length > 0) {
							for (var i = 0; i < idList.length; i++) {
								var color = getColor();
								var id = idList[i];
								if (id != null) {
									if (id == '101') {
										color = 'rgb(255,100,100)';
									} else if (id == '102') {
										color = 'rgb(0,182,230)';
									} else if (id == '103') {
										color = '#e4ca41';
									}
								}
								cList[i] = color;
							}
						}
						nameList = data.attributes.nameList;
						integrityList = data.attributes.integrityList;
					}else {
						window.parent.layer.msg(data.msg, {
							icon : 2
						});
						
					   return;
					}
					var option3 = {

						tooltip : {
							trigger : 'axis',
							formatter : function(params) {
								var result = params[0].name + '</br>';
								for (var i = 0, l = params.length; i < l; i++) {
									params
											.forEach(function(item) {
												result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'
														+ item.color
														+ '"></span>'
														+ params[i].seriesName
														+ ' : '
														+ params[i].value
																.toFixed(2)
														+ '%';
											});
								}

								return result;
							},
							axisPointer : { // 坐标轴指示器，坐标轴触发有效
								type : 'shadow' // 默认为直线，可选为：'line' | 'shadow'
							}
						},
						legend : {
							type: 'scroll',
							data : nameList
						},
						grid : {
							top : '12%',
							left : '3%',
							right : '4%',
							bottom : '3%',
							containLabel : true
						},
						xAxis : [ {
							zlevel: 0,
							z: 2,
							type : 'category',
							data : nameList,
							axisTick : {
								alignWithLabel : true
							}
						} ],
						yAxis : [ {
							axisLabel : {
								formatter : '{value} %'
							},
							max : 100,
							min : 60,
							type : 'value',
							scale : true,
							splitNumber : 5,
							boundaryGap : [ 0.05, 0.05 ],
							splitArea : {
								show : true
							},
							formatter : '{value} %',
						} ],
						series : [ {
							itemStyle : {
								normal : {
									color : function(params) {
										// var colorList = [ 'rgb(255,100,100)',
										// 'rgb(0,182,230)', '#1bc5ad' ];
										// return colorList[params.dataIndex];
										return cList[params.dataIndex];
									},

								}
							},
							name : i18n.t('scheduleReadsReport.monthlyInteRate'),
							type : 'bar',
							barWidth : '60%',
							data : integrityList,
							label : {
								normal : {
									show : true,
									position : 'top',
									formatter : function(c) {
										var f = parseFloat(c.value);
										if (isNaN(f)) {
											return false;
										}
										var f = Math.round(c.value * 100) / 100;
										var s = f.toString();
										var rs = s.indexOf('.');
										if (rs < 0) {
											rs = s.length;
											s += '.';
										}
										while (s.length <= rs + 2) {
											s += '0';
										}
										return s + '%';
									}
								}
							},
							cursor : 'default'
						} ]
					};
					myChart3.setOption(option3);
				},
				error : function(msg) {
				}
			});
}

function getDailyModelIntegrityBarCharts() {
	myChart6.showLoading();
	setTimeout("myChart6.hideLoading(); ", 30000);
	var tv = $("#dailyModelIntegrity").val();
	var selectOrgId = $("#selectScheOrgId").val();
	var profileId = $("#selectProfileCycleType").val();
	if (tv == null || tv == '') {
		return;
	}
	$
			.ajax({
				type : 'POST',
				url : getRootPathWeb()
						+ '/dataIntegrityController/getDailyNameIntegrity.do',
				data : 'tv=' + tv + '&&type=day&&name=model&&defaultOrgId='+selectOrgId
					+ '&profileId='+profileId,
				dataType : 'json',
				success : function(data) {
					myChart6.hideLoading();
					var nameList = null;
					var integrityList = null;
					var idList = null
					var cList = new Array();
					if (data.success) {
						idList = data.attributes.idList;
						if (idList != null && idList.length > 0) {
							for (var i = 0; i < idList.length; i++) {
								var color = getColor();
								var id = idList[i];
								if (id != null) {
									if (id == '101001') {
										color = 'rgb(255,100,100)';
									} else if (id == '101002') {
										color = '#F5C229';
									} else if (id == '101003') {
										color = '#C5D43D';
									} else if (id == '102001') {
										color = '#91C7AE';
									} else if (id == '102002') {
										color = '#AFDEE4';
									} else if (id == '102003') {
										color = '#089682';
									} else if (id == '103001') {
										color = 'rgb(0,182,230)';
									} else if (id == '103002') {
										color = '#E98F65';
									}
								}
								cList[i] = color;
							}
						}
						nameList = data.attributes.nameList;
						integrityList = data.attributes.integrityList;
					}else {
						window.parent.layer.msg(data.msg, {
							icon : 2
						});
						
					   return;
					}
					var option6 = {

						tooltip : {

							trigger : 'axis',
							formatter : function(params) {
								var result = params[0].name + '</br>';
								for (var i = 0, l = params.length; i < l; i++) {
									params
											.forEach(function(item) {
												result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'
														+ item.color
														+ '"></span>'
														+ params[i].seriesName
														+ ' : '
														+ params[i].value
																.toFixed(2)
														+ '%';
											});
								}

								return result;
							},
							axisPointer : { // 坐标轴指示器，坐标轴触发有效
								type : 'shadow' // 默认为直线，可选为：'line' | 'shadow'

							}
						},
						legend : {
							type: 'scroll',
							data : nameList
						},
						grid : {
							top : '12%',
							left : '3%',
							right : '4%',
							bottom : '3%',
							containLabel : true
						},
						xAxis : [ {
							zlevel: 0,
							z: 2,
							type : 'category',
							data : nameList,
							axisTick : {
								alignWithLabel : true
							}
						} ],
						yAxis : [ {
							axisLabel : {
								formatter : '{value} %'
							},
							max : 100,
							min : 60,
							type : 'value',
							scale : true,
							splitNumber : 5,
							boundaryGap : [ 0.05, 0.05 ],
							splitArea : {
								show : true,

							},
							formatter : '{value} %',
						} ],
						series : [ {
							itemStyle : {
								normal : {
									color : function(params) {
										// var colorList = [ 'rgb(255,100,100)',
										// 'rgb(0,182,230)', '#1bc5ad' ];
										// return colorList[params.dataIndex];
										return cList[params.dataIndex];
									},

								}
							},
							name : i18n.t('scheduleReadsReport.dailyInteRate'),

							type : 'bar',
							barWidth : '60%',
							data : integrityList,
							label : {
								normal : {
									show : true,
									position : 'top',
									formatter : function(c) {
										var f = parseFloat(c.value);
										if (isNaN(f)) {
											return false;
										}
										var f = Math.round(c.value * 100) / 100;
										var s = f.toString();
										var rs = s.indexOf('.');
										if (rs < 0) {
											rs = s.length;
											s += '.';
										}
										while (s.length <= rs + 2) {
											s += '0';
										}
										return s + '%';
									}
								}
							},
							cursor : 'default'
						} ]
					};
					myChart6.setOption(option6);
				},
				error : function(msg) {
				}
			});
}

function getMonthModelIntegrityBarCharts() {
	myChart7.showLoading();
	setTimeout("myChart7.hideLoading(); ", 30000);
	var tv = $("#monthModelIntegrity").val();
	var selectOrgId = $("#selectScheOrgId").val();
	var profileId = $("#selectProfileCycleType").val();
	if (tv == null || tv == '') {
		return;
	}
	$
			.ajax({
				type : 'POST',
				url : getRootPathWeb()
						+ '/dataIntegrityController/getDailyNameIntegrity.do',
				data : 'tv=' + tv + '&&type=month&&name=model&&defaultOrgId='+selectOrgId
					+ '&profileId='+profileId,
				dataType : 'json',
				success : function(data) {
					myChart7.hideLoading();
					var nameList = null;
					var integrityList = null;
					var idList = null;
					var cList = new Array();
					if (data.success) {
						idList = data.attributes.idList;
						if (idList != null && idList.length > 0) {
							for (var i = 0; i < idList.length; i++) {
								var color = getColor();
								var id = idList[i];
								if (id != null) {
									if (id == '101001') {
										color = 'rgb(255,100,100)';
									} else if (id == '101002') {
										color = '#F5C229';
									} else if (id == '101003') {
										color = '#C5D43D';
									} else if (id == '102001') {
										color = '#91C7AE';
									} else if (id == '102002') {
										color = '#AFDEE4';
									} else if (id == '102003') {
										color = '#089682';
									} else if (id == '103001') {
										color = 'rgb(0,182,230)';
									} else if (id == '103002') {
										color = '#E98F65';
									}
								}
								cList[i] = color;
							}
						}
						nameList = data.attributes.nameList;
						integrityList = data.attributes.integrityList;
					}else {
						window.parent.layer.msg(data.msg, {
							icon : 2
						});
						
					   return;
					}
					var option7 = {

						tooltip : {
							trigger : 'axis',
							formatter : function(params) {
								var result = params[0].name + '</br>';
								for (var i = 0, l = params.length; i < l; i++) {
									params
											.forEach(function(item) {
												result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'
														+ item.color
														+ '"></span>'
														+ params[i].seriesName
														+ ' : '
														+ params[i].value
																.toFixed(2)
														+ '%';
											});
								}

								return result;
							},
							axisPointer : { // 坐标轴指示器，坐标轴触发有效
								type : 'shadow' // 默认为直线，可选为：'line' | 'shadow'
							}
						},
						legend : {
							type: 'scroll',
							data : nameList
						},
						grid : {
							top : '12%',
							left : '3%',
							right : '4%',
							bottom : '3%',
							containLabel : true
						},
						xAxis : [ {
							zlevel: 0,
							z: 2,
							type : 'category',
							data : nameList,
							axisTick : {
								alignWithLabel : true
							}
						} ],
						yAxis : [ {
							axisLabel : {
								formatter : '{value} %'
							},
							max : 100,
							min : 60,
							type : 'value',
							scale : true,
							splitNumber : 5,
							boundaryGap : [ 0.05, 0.05 ],
							splitArea : {
								show : true
							},
							formatter : '{value} %',
						} ],
						series : [ {
							itemStyle : {
								normal : {
									color : function(params) {
										// var colorList = [ 'rgb(255,100,100)',
										// 'rgb(0,182,230)', '#1bc5ad' ];
										// return colorList[params.dataIndex];
										return cList[params.dataIndex];
									},

								}
							},
							name : i18n.t('scheduleReadsReport.monthlyInteRate'),
							type : 'bar',
							barWidth : '60%',
							data : integrityList,
							label : {
								normal : {
									show : true,
									position : 'top',
									formatter : function(c) {
										var f = parseFloat(c.value);
										if (isNaN(f)) {
											return false;
										}
										var f = Math.round(c.value * 100) / 100;
										var s = f.toString();
										var rs = s.indexOf('.');
										if (rs < 0) {
											rs = s.length;
											s += '.';
										}
										while (s.length <= rs + 2) {
											s += '0';
										}
										return s + '%';
									}
								}
							},
							cursor : 'default'
						} ]
					};
					myChart7.setOption(option7);
				},
				error : function(msg) {
				}
			});
}

function getDailyCommTypeIntegrityBarCharts() {
	myChart10.showLoading();
	setTimeout("myChart10.hideLoading(); ", 30000);
	var tv = $("#dailyCommTypeIntegrity").val();
	var selectOrgId = $("#selectScheOrgId").val();
	var profileId = $("#selectProfileCycleType").val();
	if (tv == null || tv == '') {
		return;
	}
	$
			.ajax({
				type : 'POST',
				url : getRootPathWeb()
						+ '/dataIntegrityController/getDailyNameIntegrity.do',
				data : 'tv=' + tv + '&&type=day&&name=commType&&defaultOrgId='+selectOrgId
					+ '&profileId='+profileId,
				dataType : 'json',
				success : function(data) {
					myChart10.hideLoading();
					var nameList = null;
					var integrityList = null;
					var idList = null;
					var cList = new Array();
					if (data.success) {
						idList = data.attributes.idList;
						if (idList != null && idList.length > 0) {
							for (var i = 0; i < idList.length; i++) {
								var color = getColor();
								var id = idList[i];
								if (id != null) {
									if (id == '100') {
										color = 'rgb(255,100,100)';
									} else if (id == '101') {
										color = '#F5C229';
									} else if (id == '102') {
										color = '#C5D43D';
									} else if (id == '103') {
										color = '#91C7AE';
									} else if (id == '200') {
										color = '#AFDEE4';
									} else if (id == '201') {
										color = '#089682';
									} else if (id == '202') {
										color = '#6BCF86';
									} else if (id == '203') {
										color = '#E98F65';
									}
								}
								cList[i] = color;
							}
						}
						nameList = data.attributes.nameList;
						integrityList = data.attributes.integrityList;
					}else {
						window.parent.layer.msg(data.msg, {
							icon : 2
						});
						
					   return;
					}
					var option10 = {

						tooltip : {

							trigger : 'axis',
							formatter : function(params) {
								var result = params[0].name + '</br>';
								for (var i = 0, l = params.length; i < l; i++) {
									params
											.forEach(function(item) {
												result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'
														+ item.color
														+ '"></span>'
														+ params[i].seriesName
														+ ' : '
														+ params[i].value
																.toFixed(2)
														+ '%';
											});
								}

								return result;
							},
							axisPointer : { // 坐标轴指示器，坐标轴触发有效
								type : 'shadow' // 默认为直线，可选为：'line' | 'shadow'

							}
						},
						legend : {
							type: 'scroll',
							data : nameList
						},
						grid : {
							top : '12%',
							left : '3%',
							right : '4%',
							bottom : '3%',
							containLabel : true
						},
						xAxis : [ {
							zlevel: 0,
							z: 2,
							type : 'category',
							data : nameList,
							axisTick : {
								alignWithLabel : true
							}
						} ],
						yAxis : [ {
							axisLabel : {
								formatter : '{value} %'
							},
							max : 100,
							min : 60,
							type : 'value',
							scale : true,
							splitNumber : 5,
							boundaryGap : [ 0.05, 0.05 ],
							splitArea : {
								show : true,

							},
							formatter : '{value} %',
						} ],
						series : [ {
							itemStyle : {
								normal : {
									color : function(params) {
										// var colorList = [ 'rgb(255,100,100)',
										// 'rgb(0,182,230)', '#1bc5ad' ];
										// return colorList[params.dataIndex];
										return cList[params.dataIndex];
									},

								}
							},
							name : 'Daily Integrity Rate',

							type : 'bar',
							barWidth : '60%',
							data : integrityList,
							label : {
								normal : {
									show : true,
									position : 'top',
									formatter : function(c) {
										var f = parseFloat(c.value);
										if (isNaN(f)) {
											return false;
										}
										var f = Math.round(c.value * 100) / 100;
										var s = f.toString();
										var rs = s.indexOf('.');
										if (rs < 0) {
											rs = s.length;
											s += '.';
										}
										while (s.length <= rs + 2) {
											s += '0';
										}
										return s + '%';
									}
								}
							},
							cursor : 'default'
						} ]
					};
					myChart10.setOption(option10);
				},
				error : function(msg) {
				}
			});
}

function getMonthCommTypeIntegrityBarCharts() {
	myChart11.showLoading();
	setTimeout("myChart11.hideLoading(); ", 30000);
	var tv = $("#monthCommTypeIntegrity").val();
	var selectOrgId = $("#selectScheOrgId").val();
	var profileId = $("#selectProfileCycleType").val();
	if (tv == null || tv == '') {
		return;
	}
	$
			.ajax({
				type : 'POST',
				url : getRootPathWeb()
						+ '/dataIntegrityController/getDailyNameIntegrity.do',
				data : 'tv=' + tv + '&&type=month&&name=commType&&defaultOrgId='+selectOrgId
					+ '&profileId='+profileId,
				dataType : 'json',
				success : function(data) {
					myChart11.hideLoading();
					var nameList = null;
					var integrityList = null;
					var idList = null;
					var cList = new Array();
					if (data.success) {
						idList = data.attributes.idList;
						if (idList != null && idList.length > 0) {
							for (var i = 0; i < idList.length; i++) {
								var color = getColor();
								var id = idList[i];
								if (id != null) {
									if (id == '100') {
										color = 'rgb(255,100,100)';
									} else if (id == '101') {
										color = '#F5C229';
									} else if (id == '102') {
										color = '#C5D43D';
									} else if (id == '103') {
										color = '#91C7AE';
									} else if (id == '200') {
										color = '#AFDEE4';
									} else if (id == '201') {
										color = '#089682';
									} else if (id == '202') {
										color = '#6BCF86';
									} else if (id == '203') {
										color = '#E98F65';
									}
								}
								cList[i] = color;
							}
						}
						nameList = data.attributes.nameList;
						integrityList = data.attributes.integrityList;
					}else {
						window.parent.layer.msg(data.msg, {
							icon : 2
						});
						
					   return;
					}
					var option11 = {

						tooltip : {
							trigger : 'axis',
							formatter : function(params) {
								var result = params[0].name + '</br>';
								for (var i = 0, l = params.length; i < l; i++) {
									params
											.forEach(function(item) {
												result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'
														+ item.color
														+ '"></span>'
														+ params[i].seriesName
														+ ' : '
														+ params[i].value
																.toFixed(2)
														+ '%';
											});
								}

								return result;
							},
							axisPointer : { // 坐标轴指示器，坐标轴触发有效
								type : 'shadow' // 默认为直线，可选为：'line' | 'shadow'
							}
						},
						legend : {
							type: 'scroll',
							data : nameList
						},
						grid : {
							top : '12%',
							left : '3%',
							right : '4%',
							bottom : '3%',
							containLabel : true
						},
						xAxis : [ {
							zlevel: 0,
							z: 2,
							type : 'category',
							data : nameList,
							axisTick : {
								alignWithLabel : true
							}
						} ],
						yAxis : [ {
							axisLabel : {
								formatter : '{value} %'
							},
							max : 100,
							min : 60,
							type : 'value',
							scale : true,
							splitNumber : 5,
							boundaryGap : [ 0.05, 0.05 ],
							splitArea : {
								show : true
							},
							formatter : '{value} %',
						} ],
						series : [ {
							itemStyle : {
								normal : {
									color : function(params) {
										// var colorList = [ 'rgb(255,100,100)',
										// 'rgb(0,182,230)', '#1bc5ad' ];
										// return colorList[params.dataIndex];
										return cList[params.dataIndex];
									},

								}
							},
							name : i18n.t('scheduleReadsReport.monthlyInteRate'),
							type : 'bar',
							barWidth : '60%',
							data : integrityList,
							label : {
								normal : {
									show : true,
									position : 'top',
									formatter : function(c) {
										var f = parseFloat(c.value);
										if (isNaN(f)) {
											return false;
										}
										var f = Math.round(c.value * 100) / 100;
										var s = f.toString();
										var rs = s.indexOf('.');
										if (rs < 0) {
											rs = s.length;
											s += '.';
										}
										while (s.length <= rs + 2) {
											s += '0';
										}
										return s + '%';
									}
								}
							},
							cursor : 'default'
						} ]
					};
					myChart11.setOption(option11);
				},
				error : function(msg) {
				}
			});
}

function getDailyManufacturerIntegrity() {
	myChart4.showLoading();
	setTimeout("myChart4.hideLoading(); ", 30000);
	var startTv = $("#startDailyManufacturerIntegrity").val();
	var selectOrgId = $("#selectScheOrgId").val();
	var profileId = $("#selectProfileCycleType").val();
	if (startTv == null || startTv == '') {
		return;
	}
	var endTv = $("#endDailyManufacturerIntegrity").val();
	if (endTv == null || endTv == '') {
		return;
	}
	$
			.ajax({
				type : 'POST',
				url : getRootPathWeb()
						+ '/dataIntegrityController/getDailyNameIntegrity.do',
				data : 'startTv=' + startTv + '&&endTv=' + endTv
						+ '&&type=day&&name=manufacturer&&chartType=chart&&defaultOrgId='+selectOrgId
					+ '&profileId='+profileId,
				dataType : 'json',
				success : function(data) {
					myChart4.hideLoading();
					var nameList = new Array();
					var series = new Array();
					var tvList = null;
					if (data.success) {
						var attrs = data.attributes.attrs;
						if (attrs != null && attrs.length > 0) {
							for (var i = 0; i < attrs.length; i++) {
								var attr = attrs[i];
								nameList[i] = attr.name;
								var color = getColor();
								var id = attr.id;
								if (id != undefined && id != null) {
									if (id == '101') {
										color = 'rgb(255,100,100)';
									} else if (id == '102') {
										color = 'rgb(0,182,230)';
									} else if (id == '103') {
										color = '#e4ca41';
									}
								}
								series[i] = {
									name : attr.name,
									type : 'line',
									stack : attr.name,
									smooth : true,
									symbol : 'circle',
									symbolSize : 4,
									itemStyle : {
										normal : {
											color : color,
											areaStyle : {
												type : 'default',
												opacity : 0.2,
											}
										}
									},
									data : attr.iList
								};
							}
						}
						tvList = data.attributes.tvList;
					}else {
						window.parent.layer.msg(data.msg, {
							icon : 2
						});
						
					   return;
					}
					var option4 = {

						tooltip : {
							trigger : 'axis',
							formatter : function(params)// 数据格式
							{
								var result = params[0].name + '</br>';
								params
										.forEach(function(item) {
											result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'
													+ item.color
													+ '"></span>'
													+ item.seriesName
													+ ' : '
													+ item.value.toFixed(2)
													+ "%<br/>";
										});
								return result;
							},
							axisPointer : { // 坐标轴指示器，坐标轴触发有效
								type : 'cross' // 默认为直线，可选为：'line' | 'shadow'
							}
						},
						legend : {
							type: 'scroll',
							data : nameList,

						},
						grid : {
							top : '12%',
							left : '3%',
							right : '4%',
							bottom : '3%',
							containLabel : true
						},

						xAxis : {
							type : 'category',
							boundaryGap : false,
							data : tvList
						},
						yAxis : [ {
							axisLabel : {
								formatter : '{value} %'
							},
							max : 100,
							// min : 0,
							type : 'value',
							scale : true,
							splitNumber : 5,
							boundaryGap : [ 0, 0 ],
							splitArea : {
								show : true
							},
							formatter : '{value} %',
						} ],
						series : series
					};
					myChart4.setOption(option4);
				},
				error : function(msg) {
				}
			});

}

function getMonthManufacturerIntegrity() {
	myChart5.showLoading();
	setTimeout("myChart5.hideLoading(); ", 30000);
	var startTv = $("#startMonthManufacturerIntegrity").val();
	var selectOrgId = $("#selectScheOrgId").val();
	var profileId = $("#selectProfileCycleType").val();
	if (startTv == null || startTv == '') {
		return;
	}
	var endTv = $("#endMonthManufacturerIntegrity").val();
	if (endTv == null || endTv == '') {
		return;
	}
	$
			.ajax({
				type : 'POST',
				url : getRootPathWeb()
						+ '/dataIntegrityController/getDailyNameIntegrity.do',
				data : 'startTv=' + startTv + '&&endTv=' + endTv
						+ '&&type=month&&name=manufacturer&&chartType=chart&&defaultOrgId='+selectOrgId
					+ '&profileId='+profileId,
				dataType : 'json',
				success : function(data) {
					myChart5.hideLoading();
					var nameList = new Array();
					var series = new Array();
					var tvList = null;
					if (data.success) {
						var attrs = data.attributes.attrs;
						if (attrs != null && attrs.length > 0) {
							for (var i = 0; i < attrs.length; i++) {
								var attr = attrs[i];
								nameList[i] = attr.name;
								var color = getColor();
								var id = attr.id;
								if (id != undefined && id != null) {
									if (id == '101') {
										color = 'rgb(255,100,100)';
									} else if (id == '102') {
										color = 'rgb(0,182,230)';
									} else if (id == '103') {
										color = '#e4ca41';
									}
								}
								series[i] = {
									name : attr.name,
									type : 'line',
									stack : attr.name,
									smooth : true,
									symbol : 'circle',
									symbolSize : 4,
									itemStyle : {
										normal : {
											color : color,
											areaStyle : {
												type : 'default',
												opacity : 0.2,
											}
										}
									},
									data : attr.iList
								};
							}
						}
						tvList = data.attributes.tvList;
					}else {
						window.parent.layer.msg(data.msg, {
							icon : 2
						});
						
					   return;
					}
					var option5 = {
						title : {
							text : ''
						},
						tooltip : {
							trigger : 'axis',
							formatter : function(params)// 数据格式
							{
								var result = params[0].name + '</br>';
								params
										.forEach(function(item) {
											result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'
													+ item.color
													+ '"></span>'
													+ item.seriesName
													+ ' : '
													+ item.value.toFixed(2)
													+ "%<br/>";
										});
								return result;
							},
							axisPointer : { // 坐标轴指示器，坐标轴触发有效
								type : 'shadow' // 默认为直线，可选为：'line' | 'shadow'
							}
						},
						legend : {
							type: 'scroll',
							data : nameList
						},
						grid : {
							top : '12%',
							left : '3%',
							right : '4%',
							bottom : '3%',
							containLabel : true
						},

						xAxis : {
							type : 'category',
							boundaryGap : false,
							data : tvList
						},
						yAxis : [ {
							axisLabel : {
								formatter : '{value} %'
							},
							max : 100,
							// min : 0,
							type : 'value',
							scale : true,
							splitNumber : 5,
							boundaryGap : [ 0, 0 ],
							splitArea : {
								show : true
							},
							formatter : '{value} %',
						} ],
						series : series
					};
					myChart5.setOption(option5);
				},
				error : function(msg) {
				}
			});

}

function getDailyModelIntegrity() {
	myChart8.showLoading();
	setTimeout("myChart8.hideLoading(); ", 30000);
	var startTv = $("#startDailyModelIntegrity").val();
	var selectOrgId = $("#selectScheOrgId").val();
	var profileId = $("#selectProfileCycleType").val();
	if (startTv == null || startTv == '') {
		return;
	}
	var endTv = $("#endDailyModelIntegrity").val();
	if (endTv == null || endTv == '') {
		return;
	}
	$
			.ajax({
				type : 'POST',
				url : getRootPathWeb()
						+ '/dataIntegrityController/getDailyNameIntegrity.do',
				data : 'startTv=' + startTv + '&&endTv=' + endTv
						+ '&&type=day&&name=model&&chartType=chart&&defaultOrgId='+selectOrgId
					+ '&profileId='+profileId,
				dataType : 'json',
				success : function(data) {
					myChart8.hideLoading();
					var nameList = new Array();
					var series = new Array();
					var tvList = null;
					if (data.success) {
						var attrs = data.attributes.attrs;
						if (attrs != null && attrs.length > 0) {
							for (var i = 0; i < attrs.length; i++) {
								var attr = attrs[i];
								nameList[i] = attr.name;
								var color = getColor();
								var id = attr.id;
								if (id != null) {
									if (id == '101001') {
										color = 'rgb(255,100,100)';
									} else if (id == '101002') {
										color = '#F5C229';
									} else if (id == '101003') {
										color = '#C5D43D';
									} else if (id == '102001') {
										color = '#91C7AE';
									} else if (id == '102002') {
										color = '#AFDEE4';
									} else if (id == '102003') {
										color = '#089682';
									} else if (id == '103001') {
										color = 'rgb(0,182,230)';
									} else if (id == '103002') {
										color = '#E98F65';
									}
								}
								series[i] = {
									name : attr.name,
									type : 'line',
									stack : attr.name,
									smooth : true,
									symbol : 'circle',
									symbolSize : 4,
									itemStyle : {
										normal : {
											color : color,
											areaStyle : {
												type : 'default',
												opacity : 0.2,
											}
										}
									},
									data : attr.iList
								};
							}
						}
						tvList = data.attributes.tvList;
					}else {
						window.parent.layer.msg(data.msg, {
							icon : 2
						});
						
					   return;
					}
					var option8 = {

						tooltip : {
							trigger : 'axis',
							formatter : function(params)// 数据格式
							{
								var result = params[0].name + '</br>';
								params
										.forEach(function(item) {
											result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'
													+ item.color
													+ '"></span>'
													+ item.seriesName
													+ ' : '
													+ item.value.toFixed(2)
													+ "%<br/>";
										});
								return result;
							},
							axisPointer : { // 坐标轴指示器，坐标轴触发有效
								type : 'cross' // 默认为直线，可选为：'line' | 'shadow'
							}
						},
						legend : {
							type: 'scroll',
							data : nameList,
						},
						grid : {
							top : '12%',
							left : '3%',
							right : '4%',
							bottom : '3%',
							containLabel : true
						},

						xAxis : {
							type : 'category',
							boundaryGap : false,
							data : tvList
						},
						yAxis : [ {
							axisLabel : {
								formatter : '{value} %'
							},
							max : 100,
							// min : 0,
							type : 'value',
							scale : true,
							splitNumber : 5,
							boundaryGap : [ 0, 0 ],
							splitArea : {
								show : true
							},
							formatter : '{value} %',
						} ],
						series : series
					};
					myChart8.setOption(option8);
				},
				error : function(msg) {
				}
			});

}

function getMonthModelIntegrity() {
	myChart9.showLoading();
	setTimeout("myChart9.hideLoading(); ", 30000);
	var startTv = $("#startMonthModelIntegrity").val();
	var selectOrgId = $("#selectScheOrgId").val();
	var profileId = $("#selectProfileCycleType").val();
	if (startTv == null || startTv == '') {
		return;
	}
	var endTv = $("#endMonthModelIntegrity").val();
	if (endTv == null || endTv == '') {
		return;
	}
	$
			.ajax({
				type : 'POST',
				url : getRootPathWeb()
						+ '/dataIntegrityController/getDailyNameIntegrity.do',
				data : 'startTv=' + startTv + '&&endTv=' + endTv
						+ '&&type=month&&name=model&&chartType=chart&&defaultOrgId='+selectOrgId
					+ '&profileId='+profileId,
				dataType : 'json',
				success : function(data) {
					myChart9.hideLoading();
					var nameList = new Array();
					var series = new Array();
					var tvList = null;
					if (data.success) {
						var attrs = data.attributes.attrs;
						if (attrs != null && attrs.length > 0) {
							for (var i = 0; i < attrs.length; i++) {
								var attr = attrs[i];
								nameList[i] = attr.name;
								var color = getColor();
								var id = attr.id;
								if (id != null) {
									if (id == '101001') {
										color = 'rgb(255,100,100)';
									} else if (id == '101002') {
										color = '#F5C229';
									} else if (id == '101003') {
										color = '#C5D43D';
									} else if (id == '102001') {
										color = '#91C7AE';
									} else if (id == '102002') {
										color = '#AFDEE4';
									} else if (id == '102003') {
										color = '#089682';
									} else if (id == '103001') {
										color = 'rgb(0,182,230)';
									} else if (id == '103002') {
										color = '#E98F65';
									}
								}
								series[i] = {
									name : attr.name,
									type : 'line',
									stack : attr.name,
									smooth : true,
									symbol : 'circle',
									symbolSize : 4,
									itemStyle : {
										normal : {
											color : color,
											areaStyle : {
												type : 'default',
												opacity : 0.2,
											}
										}
									},
									data : attr.iList
								};
							}
						}
						tvList = data.attributes.tvList;
					}else {
						window.parent.layer.msg(data.msg, {
							icon : 2
						});
						
					   return;
					}
					var option9 = {
						title : {
							text : ''
						},
						tooltip : {
							trigger : 'axis',
							formatter : function(params)// 数据格式
							{
								var result = params[0].name + '</br>';
								params
										.forEach(function(item) {
											result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'
													+ item.color
													+ '"></span>'
													+ item.seriesName
													+ ' : '
													+ item.value.toFixed(2)
													+ "%<br/>";
										});
								return result;
							},
							axisPointer : { // 坐标轴指示器，坐标轴触发有效
								type : 'shadow' // 默认为直线，可选为：'line' | 'shadow'
							}
						},
						legend : {
							type: 'scroll',
							data : nameList
						},
						grid : {
							top : '12%',
							left : '3%',
							right : '4%',
							bottom : '3%',
							containLabel : true
						},

						xAxis : {
							type : 'category',
							boundaryGap : false,
							data : tvList
						},
						yAxis : [ {
							axisLabel : {
								formatter : '{value} %'
							},
							max : 100,
							// min : 0,
							type : 'value',
							scale : true,
							splitNumber : 5,
							boundaryGap : [ 0, 0 ],
							splitArea : {
								show : true
							},
							formatter : '{value} %',
						} ],
						series : series
					};
					myChart9.setOption(option9);
				},
				error : function(msg) {
				}
			});

}

function getDailyCommTypeIntegrity() {
	myChart12.showLoading();
	setTimeout("myChart12.hideLoading(); ", 30000);
	var startTv = $("#startDailyCommTypeIntegrity").val();
	var selectOrgId = $("#selectScheOrgId").val();
	var profileId = $("#selectProfileCycleType").val();
	if (startTv == null || startTv == '') {
		return;
	}
	var endTv = $("#endDailyCommTypeIntegrity").val();
	if (endTv == null || endTv == '') {
		return;
	}
	$
			.ajax({
				type : 'POST',
				url : getRootPathWeb()
						+ '/dataIntegrityController/getDailyNameIntegrity.do',
				data : 'startTv=' + startTv + '&&endTv=' + endTv
						+ '&&type=day&&name=commType&&chartType=chart&&defaultOrgId='+selectOrgId
					+ '&profileId='+profileId,
				dataType : 'json',
				success : function(data) {
					myChart12.hideLoading();
					var nameList = new Array();
					var series = new Array();
					var tvList = null;
					if (data.success) {
						var attrs = data.attributes.attrs;
						if (attrs != null && attrs.length > 0) {
							for (var i = 0; i < attrs.length; i++) {
								var attr = attrs[i];
								nameList[i] = attr.name;
								var color = getColor();
								var id = attr.id;
								if (id != null) {
									if (id == '100') {
										color = 'rgb(255,100,100)';
									} else if (id == '101') {
										color = '#F5C229';
									} else if (id == '102') {
										color = '#C5D43D';
									} else if (id == '103') {
										color = '#91C7AE';
									} else if (id == '200') {
										color = '#AFDEE4';
									} else if (id == '201') {
										color = '#089682';
									} else if (id == '202') {
										color = '#6BCF86';
									} else if (id == '203') {
										color = '#E98F65';
									}
								}
								series[i] = {
									name : attr.name,
									type : 'line',
									stack : attr.name,
									smooth : true,
									symbol : 'circle',
									symbolSize : 4,
									itemStyle : {
										normal : {
											color : color,
											areaStyle : {
												type : 'default',
												opacity : 0.2,
											}
										}
									},
									data : attr.iList
								};
							}
						}
						tvList = data.attributes.tvList;
					}else {
						window.parent.layer.msg(data.msg, {
							icon : 2
						});
						
					   return;
					}
					var option12 = {
						tooltip : {
							trigger : 'axis',
							formatter : function(params)// 数据格式
							{
								var result = params[0].name + '</br>';
								params
										.forEach(function(item) {
											result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'
													+ item.color
													+ '"></span>'
													+ item.seriesName
													+ ' : '
													+ item.value.toFixed(2)
													+ "%<br/>";
										});
								return result;
							},
							axisPointer : { // 坐标轴指示器，坐标轴触发有效
								type : 'cross' // 默认为直线，可选为：'line' | 'shadow'
							}
						},
						legend : {
							type: 'scroll',
							data : nameList,

						},
						grid : {
							top : '12%',
							left : '3%',
							right : '4%',
							bottom : '3%',
							containLabel : true
						},
						xAxis : {
							type : 'category',
							boundaryGap : false,
							data : tvList
						},
						yAxis : [ {
							axisLabel : {
								formatter : '{value} %'
							},
							max : 100,
							// min : 0,
							type : 'value',
							scale : true,
							splitNumber : 5,
							boundaryGap : [ 0, 0 ],
							splitArea : {
								show : true
							},
							formatter : '{value} %',
						} ],
						series : series
					};
					myChart12.setOption(option12);
				},
				error : function(msg) {
				}
			});

}

function getMonthCommTypeIntegrity() {
	myChart13.showLoading();
	setTimeout("myChart13.hideLoading(); ", 30000);
	var startTv = $("#startMonthCommTypeIntegrity").val();
	var selectOrgId = $("#selectScheOrgId").val();
	var profileId = $("#selectProfileCycleType").val();
	if (startTv == null || startTv == '') {
		return;
	}
	var endTv = $("#endMonthCommTypeIntegrity").val();
	if (endTv == null || endTv == '') {
		return;
	}
	$
			.ajax({
				type : 'POST',
				url : getRootPathWeb()
						+ '/dataIntegrityController/getDailyNameIntegrity.do',
				data : 'startTv=' + startTv + '&&endTv=' + endTv
						+ '&&type=month&&name=commType&&chartType=chart&&defaultOrgId='+selectOrgId
					+ '&profileId='+profileId,
				dataType : 'json',
				success : function(data) {
					myChart13.hideLoading();
					var nameList = new Array();
					var series = new Array();
					var tvList = null;
					if (data.success) {
						var attrs = data.attributes.attrs;
						if (attrs != null && attrs.length > 0) {
							for (var i = 0; i < attrs.length; i++) {
								var attr = attrs[i];
								nameList[i] = attr.name;
								var color = getColor();
								var id = attr.id;
								if (id != null) {
									if (id == '100') {
										color = 'rgb(255,100,100)';
									} else if (id == '101') {
										color = '#F5C229';
									} else if (id == '102') {
										color = '#C5D43D';
									} else if (id == '103') {
										color = '#91C7AE';
									} else if (id == '200') {
										color = '#AFDEE4';
									} else if (id == '201') {
										color = '#089682';
									} else if (id == '202') {
										color = '#6BCF86';
									} else if (id == '203') {
										color = '#E98F65';
									}
								}
								series[i] = {
									name : attr.name,
									type : 'line',
									stack : attr.name,
									smooth : true,
									symbol : 'circle',
									symbolSize : 4,
									itemStyle : {
										normal : {
											color : color,
											areaStyle : {
												type : 'default',
												opacity : 0.2,
											}
										}
									},
									data : attr.iList
								};
							}
						}
						tvList = data.attributes.tvList;
					}else {
						window.parent.layer.msg(data.msg, {
							icon : 2
						});
						
					   return;
					}
					var option13 = {
						title : {
							text : ''
						},
						tooltip : {
							trigger : 'axis',
							formatter : function(params)// 数据格式
							{
								var result = params[0].name + '</br>';
								params
										.forEach(function(item) {
											result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'
													+ item.color
													+ '"></span>'
													+ item.seriesName
													+ ' : '
													+ item.value.toFixed(2)
													+ "%<br/>";
										});
								return result;
							},
							axisPointer : { // 坐标轴指示器，坐标轴触发有效
								type : 'shadow' // 默认为直线，可选为：'line' | 'shadow'
							}
						},
						legend : {
							type: 'scroll',
							data : nameList
						},
						grid : {
							top : '12%',
							left : '3%',
							right : '4%',
							bottom : '3%',
							containLabel : true
						},

						xAxis : {
							type : 'category',
							boundaryGap : false,
							data : tvList
						},
						yAxis : [ {
							axisLabel : {
								formatter : '{value} %'
							},
							max : 100,
							// min : 0,
							type : 'value',
							scale : true,
							splitNumber : 5,
							boundaryGap : [ 0, 0 ],
							splitArea : {
								show : true
							},
							formatter : '{value} %',
						} ],
						series : series
					};
					myChart13.setOption(option13);
				},
				error : function(msg) {
				}
			});

}

function getDailyOrgIntegrityBarCharts() {
	myChart14.showLoading();
	setTimeout("myChart14.hideLoading(); ", 30000);
	var tv = $("#dailyOrgIntegrity").val();
	var selectOrgId = $("#selectScheOrgId").val();
	var profileId = $("#selectProfileCycleType").val();
	if (tv == null || tv == '') {
		return;
	}
	$
			.ajax({
				type : 'POST',
				url : getRootPathWeb()
						+ '/dataIntegrityController/getDailyNameIntegrity.do',
				data : 'tv=' + tv + '&&type=day&&name=organization&&defaultOrgId='+selectOrgId
					+ '&profileId='+profileId,
				dataType : 'json',
				success : function(data) {
					myChart14.hideLoading();
					var nameList = null;
					var integrityList = null;
					var idList = null;
					var cList = new Array();
					if (data.success) {
						idList = data.attributes.idList;
						if (idList != null && idList.length > 0) {
							for (var i = 0; i < idList.length; i++) {
								var color = getColor();
								var id = idList[i];
								
								if (i == 0) {
									color = 'rgb(255,100,100)';
								} else if (i == 1) {
									color = '#F5C229';
								} else if (i == 2) {
									color = '#C5D43D';
								} else if (i == 3) {
									color = '#91C7AE';
								} else if (i == 4) {
									color = '#AFDEE4';
								} else if (i == 5) {
									color = '#089682';
								} else if (i == 6) {
									color = '#6BCF86';
								} else if (i == 7) {
									color = '#E98F65';
								}
								
								cList[i] = color;
							}
						}
						nameList = data.attributes.nameList;
						integrityList = data.attributes.integrityList;
					}else {
						window.parent.layer.msg(data.msg, {
							icon : 2
						});
						
					   return;
					}
					var option2 = {
						tooltip : {

							trigger : 'axis',
							formatter : function(params) {
								var result = params[0].name + '</br>';
								for (var i = 0, l = params.length; i < l; i++) {
									params
											.forEach(function(item) {
												result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'
														+ item.color
														+ '"></span>'
														+ params[i].seriesName
														+ ' : '
														+ params[i].value
																.toFixed(2)
														+ '%';
											});
								}

								return result;
							},
							axisPointer : { // 坐标轴指示器，坐标轴触发有效
								type : 'shadow' // 默认为直线，可选为：'line' | 'shadow'

							}
						},
						legend : {
							type: 'scroll',
							data : nameList
						},
						grid : {
							top : '12%',
							left : '3%',
							right : '4%',
							bottom : '3%',
							containLabel : true
						},
						xAxis : [ {
							zlevel: 0,
							z: 2,
							type : 'category',
							data : nameList,
							
							axisLabel: {
                      		  interval: 0
                      		}
	
						} ],
						yAxis : [ {
							axisLabel : {
								formatter : '{value} %'
							},
							max : 100,
							min : 60,
							type : 'value',
							scale : true,
							splitNumber : 5,
							boundaryGap : [ 0.05, 0.05 ],
							splitArea : {
								show : true,

							},
							formatter : '{value} %',
						} ],
						series : [ {
							itemStyle : {
								normal : {
									color : function(params) {
										// var colorList = [ 'rgb(255,100,100)',
										// 'rgb(0,182,230)', '#1bc5ad' ];
										// return colorList[params.dataIndex];
										return cList[params.dataIndex];
									},

								}
							},
							name : i18n.t('scheduleReadsReport.dailyInteRate'),
							type : 'bar',
							barWidth : '60%',
							data : integrityList,
							label : {
								normal : {
									show : true,
									position : 'top',
									formatter : function(c) {
										var f = parseFloat(c.value);
										if (isNaN(f)) {
											return false;
										}
										var f = Math.round(c.value * 100) / 100;
										var s = f.toString();
										var rs = s.indexOf('.');
										if (rs < 0) {
											rs = s.length;
											s += '.';
										}
										while (s.length <= rs + 2) {
											s += '0';
										}
										return s + '%';
									}
								}
							},
							cursor : 'default'
						} ]
					};
					myChart14.setOption(option2);
				},
				error : function(msg) {
				}
			});
}


function getMonthOrgIntegrityBarCharts() {
	myChart15.showLoading();
	setTimeout("myChart15.hideLoading(); ", 30000);
	var tv = $("#monthOrgIntegrity").val();
	var selectOrgId = $("#selectScheOrgId").val();
	var profileId = $("#selectProfileCycleType").val();
	if (tv == null || tv == '') {
		return;
	}
	$
			.ajax({
				type : 'POST',
				url : getRootPathWeb()
						+ '/dataIntegrityController/getDailyNameIntegrity.do',
				data : 'tv=' + tv + '&&type=month&&name=organization&&defaultOrgId='+selectOrgId
					+ '&profileId='+profileId,
				dataType : 'json',
				success : function(data) {
					myChart15.hideLoading();
					var nameList = null;
					var integrityList = null;
					var idList = null;
					var cList = new Array();
					if (data.success) {
						idList = data.attributes.idList;
						if (idList != null && idList.length > 0) {
							for (var i = 0; i < idList.length; i++) {
								var color = getColor();
								var id = idList[i];
								
								if (i == 0) {
									color = 'rgb(255,100,100)';
								} else if (i == 1) {
									color = '#F5C229';
								} else if (i == 2) {
									color = '#C5D43D';
								} else if (i == 3) {
									color = '#91C7AE';
								} else if (i == 4) {
									color = '#AFDEE4';
								} else if (i == 5) {
									color = '#089682';
								} else if (i == 6) {
									color = '#6BCF86';
								} else if (i == 7) {
									color = '#E98F65';
								}
								
								
								cList[i] = color;
							}
						}
						nameList = data.attributes.nameList;
						integrityList = data.attributes.integrityList;
					}else {
						window.parent.layer.msg(data.msg, {
							icon : 2
						});
						
					   return;
					}
					var option3 = {

						tooltip : {
							trigger : 'axis',
							formatter : function(params) {
								var result = params[0].name + '</br>';
								for (var i = 0, l = params.length; i < l; i++) {
									params
											.forEach(function(item) {
												result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'
														+ item.color
														+ '"></span>'
														+ params[i].seriesName
														+ ' : '
														+ params[i].value
																.toFixed(2)
														+ '%';
											});
								}

								return result;
							},
							axisPointer : { // 坐标轴指示器，坐标轴触发有效
								type : 'shadow' // 默认为直线，可选为：'line' | 'shadow'
							}
						},
						legend : {
							type: 'scroll',
							data : nameList
						},
						grid : {
							top : '12%',
							left : '3%',
							right : '4%',
							bottom : '3%',
							containLabel : true
						},
						xAxis : [ {
							zlevel: 0,
							z: 2,
							type : 'category',
							data : nameList,
							axisLabel: {
                      		  interval: 0
                      		}
						} ],
						yAxis : [ {
							axisLabel : {
								formatter : '{value} %'
							},
							max : 100,
							min : 60,
							type : 'value',
							scale : true,
							splitNumber : 5,
							boundaryGap : [ 0.05, 0.05 ],
							splitArea : {
								show : true
							},
							formatter : '{value} %',
						} ],
						series : [ {
							itemStyle : {
								normal : {
									color : function(params) {
										// var colorList = [ 'rgb(255,100,100)',
										// 'rgb(0,182,230)', '#1bc5ad' ];
										// return colorList[params.dataIndex];
										return cList[params.dataIndex];
									},

								}
							},
							name : i18n.t('scheduleReadsReport.monthlyInteRate'),
							type : 'bar',
							barWidth : '60%',
							data : integrityList,
							label : {
								normal : {
									show : true,
									position : 'top',
									formatter : function(c) {
										var f = parseFloat(c.value);
										if (isNaN(f)) {
											return false;
										}
										var f = Math.round(c.value * 100) / 100;
										var s = f.toString();
										var rs = s.indexOf('.');
										if (rs < 0) {
											rs = s.length;
											s += '.';
										}
										while (s.length <= rs + 2) {
											s += '0';
										}
										return s + '%';
									}
								}
							},
							cursor : 'default'
						} ]
					};
					myChart15.setOption(option3);
				},
				error : function(msg) {
				}
			});
}


function getDailyOrgIntegrity() {
	myChart16.showLoading();
	setTimeout("myChart16.hideLoading(); ", 30000);
	var startTv = $("#startDailyOrgIntegrity").val();
	var selectOrgId = $("#selectScheOrgId").val();
	var profileId = $("#selectProfileCycleType").val();
	if (startTv == null || startTv == '') {
		return;
	}
	var endTv = $("#endDailyOrgIntegrity").val();
	if (endTv == null || endTv == '') {
		return;
	}
	$
			.ajax({
				type : 'POST',
				url : getRootPathWeb()
						+ '/dataIntegrityController/getDailyNameIntegrity.do',
				data : 'startTv=' + startTv + '&&endTv=' + endTv
						+ '&&type=day&&name=organization&&chartType=chart&&defaultOrgId='+selectOrgId
					+ '&profileId='+profileId,
				dataType : 'json',
				success : function(data) {
					myChart16.hideLoading();
					var nameList = new Array();
					var series = new Array();
					var tvList = null;
					if (data.success) {
						var attrs = data.attributes.attrs;
						if (attrs != null && attrs.length > 0) {
							for (var i = 0; i < attrs.length; i++) {
								var attr = attrs[i];
								nameList[i] = attr.name;
								var color = getColor();
								var id = attr.id;
								
								if (i == 0) {
									color = 'rgb(255,100,100)';
								} else if (i == 1) {
									color = '#F5C229';
								} else if (i == 2) {
									color = '#C5D43D';
								} else if (i == 3) {
									color = '#91C7AE';
								} else if (i == 4) {
									color = '#AFDEE4';
								} else if (i == 5) {
									color = '#089682';
								} else if (i == 6) {
									color = '#6BCF86';
								} else if (i == 7) {
									color = '#E98F65';
								}
								
								series[i] = {
									name : attr.name,
									type : 'line',
									stack : attr.name,
									smooth : true,
									symbol : 'circle',
									symbolSize : 4,
									itemStyle : {
										normal : {
											color : color,
											areaStyle : {
												type : 'default',
												opacity : 0.2,
											}
										}
									},
									data : attr.iList
								};
							}
						}
						tvList = data.attributes.tvList;
					}else {
						window.parent.layer.msg(data.msg, {
							icon : 2
						});
						
					   return;
					}
					var option4 = {

						tooltip : {
							trigger : 'axis',
							formatter : function(params)// 数据格式
							{
								var result = params[0].name + '</br>';
								params
										.forEach(function(item) {
											result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'
													+ item.color
													+ '"></span>'
													+ item.seriesName
													+ ' : '
													+ item.value.toFixed(2)
													+ "%<br/>";
										});
								return result;
							},
							axisPointer : { // 坐标轴指示器，坐标轴触发有效
								type : 'cross' // 默认为直线，可选为：'line' | 'shadow'
							}
						},
						legend : {
							 type: 'scroll',
							 orient: 'horizontal', 
							 top: -6, 
							 data : nameList
						},
						grid : {
							top : '12%',
							left : '3%',
							right : '4%',
							bottom : '3%',
							containLabel : true
						},

						xAxis : {
							type : 'category',
							boundaryGap : false,
							data : tvList
						},
						yAxis : [ {
							axisLabel : {
								formatter : '{value} %'
							},
							max : 100,
							// min : 0,
							type : 'value',
							scale : true,
							splitNumber : 5,
							boundaryGap : [ 0, 0 ],
							splitArea : {
								show : true
							},
							formatter : '{value} %',
						} ],
						series : series
					};
					myChart16.setOption(option4);
				},
				error : function(msg) {
				}
			});

}

function getMonthOrgIntegrity() {
	myChart17.showLoading();
	setTimeout("myChart17.hideLoading(); ", 30000);
	var startTv = $("#startMonthOrgIntegrity").val();
	var selectOrgId = $("#selectScheOrgId").val();
	var profileId = $("#selectProfileCycleType").val();
	if (startTv == null || startTv == '') {
		return;
	}
	var endTv = $("#endMonthOrgIntegrity").val();
	if (endTv == null || endTv == '') {
		return;
	}
	$
			.ajax({
				type : 'POST',
				url : getRootPathWeb()
						+ '/dataIntegrityController/getDailyNameIntegrity.do',
				data : 'startTv=' + startTv + '&&endTv=' + endTv
						+ '&&type=month&&name=organization&&chartType=chart&&defaultOrgId='+selectOrgId
					+ '&profileId='+profileId,
				dataType : 'json',
				success : function(data) {
					myChart17.hideLoading();
					var nameList = new Array();
					var series = new Array();
					var tvList = null;
					if (data.success) {
						var attrs = data.attributes.attrs;
						if (attrs != null && attrs.length > 0) {
							for (var i = 0; i < attrs.length; i++) {
								var attr = attrs[i];
								nameList[i] = attr.name;
								var color = getColor();
								var id = attr.id;
								
								if (i == 0) {
									color = 'rgb(255,100,100)';
								} else if (i == 1) {
									color = '#F5C229';
								} else if (i == 2) {
									color = '#C5D43D';
								} else if (i == 3) {
									color = '#91C7AE';
								} else if (i == 4) {
									color = '#AFDEE4';
								} else if (i == 5) {
									color = '#089682';
								} else if (i == 6) {
									color = '#6BCF86';
								} else if (i == 7) {
									color = '#E98F65';
								}
								
								series[i] = {
									name : attr.name,
									type : 'line',
									stack : attr.name,
									smooth : true,
									symbol : 'circle',
									symbolSize : 4,
									itemStyle : {
										normal : {
											color : color,
											areaStyle : {
												type : 'default',
												opacity : 0.2,
											}
										}
									},
									data : attr.iList
								};
							}
						}
						tvList = data.attributes.tvList;
					}else {
						window.parent.layer.msg(data.msg, {
							icon : 2
						});
						
					   return;
					}
					var option5 = {
						title : {
							text : ''
						},
						tooltip : {
							trigger : 'axis',
							formatter : function(params)// 数据格式
							{
								var result = params[0].name + '</br>';
								params
										.forEach(function(item) {
											result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'
													+ item.color
													+ '"></span>'
													+ item.seriesName
													+ ' : '
													+ item.value.toFixed(2)
													+ "%<br/>";
										});
								return result;
							},
							axisPointer : { // 坐标轴指示器，坐标轴触发有效
								type : 'shadow' // 默认为直线，可选为：'line' | 'shadow'
							}
						},
						legend : {
							type: 'scroll',
							orient: 'horizontal', 
							top: -6, 
							data : nameList
						},
						grid : {
							top : '12%',
							left : '3%',
							right : '4%',
							bottom : '3%',
							containLabel : true
						},

						xAxis : {
							type : 'category',
							boundaryGap : false,
							data : tvList
						},
						yAxis : [ {
							axisLabel : {
								formatter : '{value} %'
							},
							max : 100,
							// min : 0,
							type : 'value',
							scale : true,
							splitNumber : 5,
							boundaryGap : [ 0, 0 ],
							splitArea : {
								show : true
							},
							formatter : '{value} %',
						} ],
						series : series
					};
					myChart17.setOption(option5);
				},
				error : function(msg) {
				}
			});

}




// 指定图表的配置项和数据
// 使用刚指定的配置项和数据显示图表。
$(window).resize(function() {
	autoHeight();
	$(myChart).each(function(index, chart) {
		myChart.resize();
		myChart1.resize();
		myChart2.resize();
		myChart3.resize();
		myChart4.resize();
		myChart5.resize();
		myChart6.resize();
		myChart7.resize();
		myChart8.resize();
		myChart9.resize();
		myChart10.resize();
		myChart11.resize();
		myChart12.resize();
		myChart13.resize();
		myChart14.resize();
		myChart15.resize();
		myChart16.resize();
		myChart17.resize();
	});
});
var Echart_01 = echarts.init(document.getElementById("main"));
var Echart_02 = echarts.init(document.getElementById("main1"));
var Echart_03 = echarts.init(document.getElementById("main2"));
var Echart_04 = echarts.init(document.getElementById("main3"));
var Echart_05 = echarts.init(document.getElementById("main4"));
var Echart_06 = echarts.init(document.getElementById("main5"));
var Echart_07 = echarts.init(document.getElementById("main6"));
var Echart_08 = echarts.init(document.getElementById("main7"));
var Echart_09 = echarts.init(document.getElementById("main8"));
var Echart_10 = echarts.init(document.getElementById("main9"));
var Echart_11 = echarts.init(document.getElementById("main10"));
var Echart_12 = echarts.init(document.getElementById("main11"));
var Echart_13 = echarts.init(document.getElementById("main12"));
var Echart_14 = echarts.init(document.getElementById("main13"));
var Echart_15 = echarts.init(document.getElementById("main14"));
var Echart_16 = echarts.init(document.getElementById("main15"));
var Echart_17 = echarts.init(document.getElementById("main16"));
var Echart_18 = echarts.init(document.getElementById("main17"));
$('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
	Echart_01.resize();
	Echart_02.resize();
	Echart_03.resize();
	Echart_04.resize();
	Echart_05.resize();
	Echart_06.resize();
	Echart_07.resize();
	Echart_08.resize();
	Echart_09.resize();
	Echart_10.resize();
	Echart_11.resize();
	Echart_12.resize();
	Echart_13.resize();
	Echart_14.resize();
	Echart_15.resize();
	Echart_16.resize();
	Echart_17.resize();
	Echart_18.resize();
});
