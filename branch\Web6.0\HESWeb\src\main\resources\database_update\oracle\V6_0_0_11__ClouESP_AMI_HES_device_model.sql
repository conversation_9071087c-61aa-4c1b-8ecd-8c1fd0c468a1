alter table DICT_DEVICE_MODEL add (INIT_CREDIT_AMOUNT NUMBER(10,4) DEFAULT 0.0);
update DICT_DEVICE_MODEL set INIT_CREDIT_AMOUNT = 10.0 where id= '101001';
update DICT_DEVICE_MODEL set INIT_CREDIT_AMOUNT = 15.0 where id= '101002';
update DICT_DEVICE_MODEL set INIT_CREDIT_AMOUNT = 0 where id= '101003';
update DICT_DEVICE_MODEL set INIT_CREDIT_AMOUNT = 10.0 where id= '102001';
update DICT_DEVICE_MODEL set INIT_CREDIT_AMOUNT = 15.0 where id= '102002';
update DICT_DEVICE_MODEL set INIT_CREDIT_AMOUNT = 0 where id= '102003';
update DICT_DEVICE_MODEL set INIT_CREDIT_AMOUNT = 15.0 where id= '103001';
update DICT_<PERSON><PERSON><PERSON>_MODEL set INIT_CREDIT_AMOUNT = 0 where id= '103002';

INSERT INTO ppm_sys_menu (ID, UTIL<PERSON>Y_IDS, MENULEVEL, MENUNAME, MENUORDER, FUNCTIONURL, PARENTMENUID, MENU_INTRO, FUNCTIONID, HIDE_TAB) VALUES ('1e858f11beda11e79bb968f728cb4019', '1', '2', 'Meter Data Monthly Supplement', '5', 'meterDataMonthlySupplementController/list.do', '1e858f11beda11e79bb968f728c50005', NULL, '2016', NULL);
INSERT INTO ppm_dict_function (ID, FUNCTIONNAME, FUNCTIONURL, FUNCTION_INTRO) VALUES ('2016', 'Meter Data Monthly Supplement', 'meterDataMonthlySupplementController/list.do', 'System');
INSERT INTO ppm_dict_operation (ID, OPERATIONNAME, FUNCTION_ID, OPERATIONURL, ISHIDE, DESCRIPTION) VALUES ('2016001', 'Meter Data Monthly Supplement', '2016', 'addMeterDataMonthlySupplement', '1', 'Meter Data Monthly Supplement');
alter table data_md_energy_monthly add "FLAG" NUMBER DEFAULT 1 NOT NULL ENABLE;
alter table ASSET_CUSTOMER add "GEO_CODE" VARCHAR2(32 BYTE);

UPDATE PPM_DICT_DETAIL SET GUI_DISPLAY_NAME = 'Cancellation Token' WHERE DICT_ID='54' AND INNER_VALUE='15';
UPDATE PPM_DICT_DETAIL SET GUI_DISPLAY_NAME = 'Disable' WHERE DICT_ID='84' AND INNER_VALUE='0';
UPDATE PPM_DICT_DETAIL SET GUI_DISPLAY_NAME = 'Enable' WHERE DICT_ID='84' AND INNER_VALUE='1';
UPDATE PPM_DICT_DETAIL SET GUI_DISPLAY_NAME = 'Destroy' WHERE DICT_ID='65' AND INNER_VALUE='3';
UPDATE PPM_DICT_DETAIL SET GUI_DISPLAY_NAME = 'Meter Cover Opened' WHERE DICT_ID='80' AND INNER_VALUE='140';
UPDATE PPM_DICT_DETAIL SET GUI_DISPLAY_NAME = 'Stopped' WHERE DICT_ID='66' AND INNER_VALUE='2';
UPDATE PPM_DICT_DETAIL SET GUI_DISPLAY_NAME = 'Successed' WHERE DICT_ID='85' AND INNER_VALUE='1';
UPDATE PPM_DICT_DETAIL SET GUI_DISPLAY_NAME = 'Failed' WHERE DICT_ID='85' AND INNER_VALUE='2';
UPDATE PPM_DICT_DETAIL SET GUI_DISPLAY_NAME = 'Disconnector in [Ready for Reconnection]', README = 'Disconnector in [Ready for Reconnection]' WHERE DICT_ID='80' AND INNER_VALUE='47';
UPDATE PPM_DICT_DETAIL SET GUI_DISPLAY_NAME = 'Unable to process the request - transaction attempted and failed' WHERE DICT_ID='91' AND INNER_VALUE='42';

INSERT INTO ppm_dict_user_log (LOG_TYPE, LOG_SUB_TYPE, SORT_ID) VALUES ('Step Tariff', 'Delete Step Tariff', '6');
INSERT INTO ppm_dict_user_log (LOG_TYPE, LOG_SUB_TYPE, SORT_ID) VALUES ('Step Tariff', 'Add Step Tariff', '7');
INSERT INTO ppm_dict_user_log (LOG_TYPE, LOG_SUB_TYPE, SORT_ID) VALUES ('Step Tariff', 'Modify Step Tariff', '8');
INSERT INTO ppm_dict_user_log (LOG_TYPE, LOG_SUB_TYPE, SORT_ID) VALUES ('Step Tariff Detail', 'Delete Step Tariff Detail', '9');
INSERT INTO ppm_dict_user_log (LOG_TYPE, LOG_SUB_TYPE, SORT_ID) VALUES ('Step Tariff Detail', 'Add Step Tariff Detail', '10');
INSERT INTO ppm_dict_user_log (LOG_TYPE, LOG_SUB_TYPE, SORT_ID) VALUES ('Step Tariff Detail', 'Modify Step Tariff Detail', '11');
INSERT INTO ppm_dict_user_log (LOG_TYPE, LOG_SUB_TYPE, SORT_ID) VALUES ('Tariff Group Management', 'Set Passvie Tariff Group', '12');
INSERT INTO ppm_dict_user_log (LOG_TYPE, LOG_SUB_TYPE, SORT_ID) VALUES ('Tariff Group Management', 'Activate Tariff Group', '13');

CREATE OR REPLACE PROCEDURE Total_month_electricity_consumption_One (
  INPUT_ORG_ID IN VARCHAR2 DEFAULT 100,  
  INPUT_ORG_CODE IN VARCHAR2 DEFAULT 100,  
  INPUT_DATE IN DATE DEFAULT sysdate    
) AS
  s_DATE VARCHAR (30);
  s_Value VARCHAR (30);
  ID VARCHAR(32);
  CURSOR cur_1 IS 
	select dmim.TV, sum(dmim.value1)+sum(dmim.value6) as Month_Consumpiton 
	from data_md_interval_monthly dmim
	left join asset_meter am on am.id=dmim.DEVICE_ID
	left join sys_org so on am.org_id=so.id
	where dmim.TV = s_DATE and so.org_code like INPUT_ORG_CODE;
BEGIN
/**
        功能说明：根据传入的管理机构，计算上一个月的售电量，保存到 数据库表中
        作者：王波  创建日期：2019年11月20日
		调用方法：call `Total_month_electricity_consumption_One`(Input_Org_ID,Input_Org_Code);
	***/
    -- 在游标循环到最后会将 done 设置为 1
	s_DATE := to_date(INPUT_DATE, '%Y-%m-01');  
  /* 打开游标 */
  open cur_1;
  loop 
    fetch cur_1 into s_DATE,s_Value;
    exit when cur_1%notfound;
    s_DATE := to_date(s_DATE,'%Y-%m');
    select lower(sys_guid()) INTO ID from dual;
	DELETE FROM ppm_total_month_electricity_consumption WHERE ORG_ID = Input_Org_ID AND Months = s_DATE;
	INSERT INTO ppm_total_month_electricity_consumption(ID,ORG_ID,Months,Month_Electricity) 
	VALUES(ID,INPUT_ORG_ID,s_DATE,s_Value);
  end loop;
  commit;
  CLOSE cur_1;
END Total_month_electricity_consumption_One;

CREATE OR REPLACE PROCEDURE Total_month_electricity_consumption_All AS 
  s_ORG_ID VARCHAR (100);
  s_ORG_Code VARCHAR (100);  
  CURSOR cur_1 IS SELECT  ID,ORG_CODE  FROM sys_org  ORDER BY ID asc ;
BEGIN
  open cur_1;
  loop 
    fetch cur_1 into s_ORG_ID,s_ORG_Code;
    exit when cur_1%notfound;
    s_ORG_CODE := s_ORG_CODE||'%';
    Total_month_electricity_consumption_One(s_ORG_ID, s_ORG_Code);
  end loop;
  commit;
  CLOSE cur_1;
END Total_month_electricity_consumption_One;

CREATE OR REPLACE PROCEDURE Total_month_electricity_consumption_ALL AS 
  s_ORG_ID VARCHAR (100);
  s_ORG_Code VARCHAR (100);  
  s_ORG_Date Date;  
  CURSOR cur_1 IS SELECT  ID,ORG_CODE  FROM sys_org  ORDER BY ID asc ;
BEGIN
  open cur_1;
  loop 
    fetch cur_1 into s_ORG_ID,s_ORG_Code;
    exit when cur_1%notfound;
    s_ORG_CODE := s_ORG_CODE||'%';
    Total_month_electricity_consumption_One(s_ORG_ID, s_ORG_Code);
  end loop;
  commit;
  CLOSE cur_1;
END TOTAL_MONTH_SALES_AMOUNT_ALL;

CREATE OR REPLACE PROCEDURE Total_Month_Sales_Amount_One (
  INPUT_ORG_ID IN VARCHAR2 DEFAULT 100,  
  INPUT_ORG_CODE IN VARCHAR2 DEFAULT 100  
) AS
  s_DATE VARCHAR (30);
  s_Value VARCHAR (30);
  ID VARCHAR(32);
  CURSOR cur_1 IS 
	SELECT to_date(vhi.SALES_DATE, '%Y-%m') AS StatMonth,
	   SUM(CASE WHEN recharge_type = 0 THEN vhi.Customer_Payment_Amount
		   WHEN recharge_type = 1 THEN - 1 * vhi.Uninstall_Amount
		   ELSE 0
		   END) AS SaleAmount
	FROM
		ppm_vend_historical_info vhi
		LEFT JOIN sys_org so on vhi.org_id=so.id
	WHERE
		vhi.SALES_DATE BETWEEN to_date(add_months(trunc(sysdate),-1), '%Y-%m-01') AND sysdate
	    AND vhi.Recharge_Type IN ('0')
	    AND vhi.Receipt_State IN ('0')
	    AND so.org_code like Input_Org_Code
	GROUP BY to_date(vhi.SALES_DATE, '%Y-%m');
BEGIN
	s_DATE := to_date(add_months(trunc(sysdate),-1), '%Y-%m-01'); 
 	/* 打开游标 */
  open cur_1;
  loop 
    fetch cur_1 into s_DATE,s_Value;
    exit when cur_1%notfound;
    s_DATE := to_date(s_DATE,'%Y-%m');
    select lower(sys_guid()) INTO ID from dual;
	DELETE FROM ppm_total_month_sales_amount WHERE ORG_ID = Input_Org_ID AND Months = s_DATE;
	INSERT INTO ppm_total_month_sales_amount(ID,ORG_ID,Months,Month_Amount) 
	VALUES(ID,INPUT_ORG_ID,s_DATE,s_Value);
  end loop;
  commit;
  CLOSE cur_1;
END Total_Month_Sales_Amount_One;

CREATE OR REPLACE PROCEDURE Total_Month_Sales_Amount_All AS
  s_ORG_ID VARCHAR (100);
  s_ORG_Code VARCHAR (100);  
  CURSOR cur_1 IS SELECT  ID,ORG_CODE  FROM sys_org  ORDER BY ID asc ;
BEGIN 
    /* 打开游标 */
  open cur_1;
  loop 
    fetch cur_1 into s_ORG_ID,s_ORG_Code;
    exit when cur_1%notfound;
    s_ORG_CODE := s_ORG_CODE||'%';
    Total_Month_Sales_Amount_One(s_ORG_ID, s_ORG_Code);
  end loop;
  commit;
  CLOSE cur_1;
END Total_Month_Sales_Amount_All;

Declare 
  i Integer; 
Begin 
   dbms_job.submit(i,'Total_Month_Sales_Amount_All;',Sysdate,'sysdate+1/24'); 
end; 

Declare 
  i Integer; 
Begin 
   dbms_job.submit(i,'Total_month_electricity_consumption_All;',Sysdate,'sysdate+1/24'); 
end; 

delete from dict_dataitem_group_map where dataitem_id like '2.36.26.0.%' and group_id = '1002005';
delete from dict_dataitem where id like '2.36.26.0.%';

insert into dict_dataitem values('2.36.26.0.0', 100, 'Data initialization', '7#0.0.99.98.0.255#2#0', null, 'R', 1, null, null);
insert into dict_dataitem values('2.36.26.0.1', 100, 'Soft Version change', '7#0.0.99.98.0.255#2#1', null, 'R', 1, null, null);

insert into dict_dataitem values('2.36.26.0.4', 100, 'Terminal Power Failure', '7#0.0.99.98.0.255#2#4', null, 'R', 1, null, null);
insert into dict_dataitem values('2.36.26.0.5', 100, 'Terminal Power on', '7#0.0.99.98.0.255#2#5', null, 'R', 1, null, null);

insert into dict_dataitem values('2.36.26.0.6', 100, 'GPRS Module Pullout', '7#0.0.99.98.0.255#2#6', null, 'R', 1, null, null);
insert into dict_dataitem values('2.36.26.0.7', 100, 'PLC Module Pullout', '7#0.0.99.98.0.255#2#7', null, 'R', 1, null, null);

insert into dict_dataitem values('2.36.26.0.9', 100, 'Terminal time', '7#0.0.99.98.0.255#2#9', null, 'R', 1, null, null);

insert into dict_dataitem values('2.36.26.0.16', 100, 'Terminal Cover opened', '7#0.0.99.98.0.255#2#16', null, 'R', 1, null, null);
insert into dict_dataitem values('2.36.26.0.17', 100, 'Terminal Cover closed', '7#0.0.99.98.0.255#2#17', null, 'R', 1, null, null);

insert into dict_dataitem values('2.36.26.0.19', 100, 'A Phase Miss', '7#0.0.99.98.0.255#2#19', null, 'R', 1, null, null);
insert into dict_dataitem values('2.36.26.0.20', 100, 'B Phase Miss', '7#0.0.99.98.0.255#2#20', null, 'R', 1, null, null);
insert into dict_dataitem values('2.36.26.0.21', 100, 'C Phase Miss', '7#0.0.99.98.0.255#2#21', null, 'R', 1, null, null);

insert into dict_dataitem values('2.36.26.0.22', 100, 'A Phase Lower', '7#0.0.99.98.0.255#2#22', null, 'R', 1, null, null);
insert into dict_dataitem values('2.36.26.0.23', 100, 'B Phase Lower', '7#0.0.99.98.0.255#2#23', null, 'R', 1, null, null);
insert into dict_dataitem values('2.36.26.0.24', 100, 'C Phase Lower', '7#0.0.99.98.0.255#2#24', null, 'R', 1, null, null);

insert into dict_dataitem values('2.36.26.0.25', 100, 'A Phase High', '7#0.0.99.98.0.255#2#25', null, 'R', 1, null, null);
insert into dict_dataitem values('2.36.26.0.26', 100, 'B Phase High', '7#0.0.99.98.0.255#2#26', null, 'R', 1, null, null);
insert into dict_dataitem values('2.36.26.0.27', 100, 'C Phase High', '7#0.0.99.98.0.255#2#27', null, 'R', 1, null, null);

insert into dict_dataitem values('2.36.26.0.28', 100, 'CPU Temperature High', '7#0.0.99.98.0.255#2#28', null, 'R', 1, null, null);
insert into dict_dataitem values('2.36.26.0.29', 100, 'Terminal Top Cover opened', '7#0.0.99.98.0.255#2#29', null, 'R', 1, null, null);
insert into dict_dataitem values('2.36.26.0.30', 100, 'Terminal Top Cover closed', '7#0.0.99.98.0.255#2#30', null, 'R', 1, null, null);

insert into dict_dataitem_group_map values('1002005', '2.36.26.0.0', 0);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.1', 1);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.4', 4);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.5', 5);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.6', 6);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.7', 7);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.9', 9);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.16', 16);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.17', 17);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.19', 19);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.20', 20);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.21', 21);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.22', 22);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.23', 23);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.24', 24);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.25', 25);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.26', 26);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.27', 27);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.28', 28);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.29', 29);
insert into dict_dataitem_group_map values('1002005', '2.36.26.0.30', 30);

-- 文件MD5变更，该位置开始需手动执行 2019-12-18 19:18
alter table asset_customer modify (NAME varchar2(200));
alter table PPM_VENDING_STATION add cms_id varchar2(100);

-- 文件MD5变更，该位置开始需手动执行 2019-12-21 15:18
alter table ppm_vend_historical_info add (CENCOBRO_BANK number(10,0) default null);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1002', 'SOCIÉTÉ GÉNÉRALE SOCIAL SECURITY BANK', 'SOCIÉTÉ GÉNÉRALE SOCIAL SECURITY BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1003', 'STANDARD CHARTERED BANK GHANA LIMITED', 'STANDARD CHARTERED BANK GHANA LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1004', 'BARCLAYS BANK (GHANA) LIMITED', 'BARCLAYS BANK (GHANA) LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1005', 'Ghana Commercial Bank', 'Ghana Commercial Bank', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1007', 'PRUDENTIAL BANK LIMITED', 'PRUDENTIAL BANK LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1009', 'ECOBANK GHANA LIMITED', 'ECOBANK GHANA LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1011', 'AGRICULTURAL DEVELOPMENT BANK OF GHANA', 'AGRICULTURAL DEVELOPMENT BANK OF GHANA', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1013', 'CAL BANK LIMITED', 'CAL BANK LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1014', 'STANBIC BANK (GHANA) LIMITED', 'STANBIC BANK (GHANA) LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1017', 'HFC BANK LIMITED', 'HFC BANK LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1018', 'GUARANTY TRUST BANK (GHANA) LIMITED', 'GUARANTY TRUST BANK (GHANA) LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1019', 'ZENITH BANK (GHANA) LIMITED', 'ZENITH BANK (GHANA) LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1021', 'BANK OF AFRICA (GHANA)', 'BANK OF AFRICA (GHANA)', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1022', 'UNIBANK GHANA LIMITED', 'UNIBANK GHANA LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1023', 'UNIVERSAL MERCHANT BANK', 'UNIVERSAL MERCHANT BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1024', 'FIDELITY BANK GHANA LIMITED', 'FIDELITY BANK GHANA LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1025', 'UNITED BANK FOR AFRICA (GHANA) LIMITED', 'UNITED BANK FOR AFRICA (GHANA) LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1026', 'UT BANK LIMITED', 'UT BANK LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1027', 'FIRST ATLANTIC BANK LIMITED', 'FIRST ATLANTIC BANK LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1028', 'ENERGY BANK (GHANA) LIMITED', 'ENERGY BANK (GHANA) LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1029', 'SAHEL SAHARA', 'SAHEL SAHARA', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1034', 'GHANA INTER BANK-HEAD OFFICE, UK', 'GHANA INTER BANK-HEAD OFFICE, UK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1080', 'ACCESS BANK (GHANA) LIMITED', 'ACCESS BANK (GHANA) LIMITED', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '1090', 'FIRST CAPITAL PLUS (FCP) BANK', 'FIRST CAPITAL PLUS (FCP) BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3171', 'ADANSI RURAL BANK', 'ADANSI RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3172', 'AHANTAMAN RURAL BANK-ABURA', 'AHANTAMAN RURAL BANK-ABURA', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3173', 'ASANTE AKIM RURAL BANK JUANSA', 'ASANTE AKIM RURAL BANK JUANSA', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3174', 'ASAWINSO RURAL BANK', 'ASAWINSO RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3175', 'ATWIMA RURAL BANK', 'ATWIMA RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3176', 'DANGBE RURAL BANK', 'DANGBE RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3177', 'JUABENG RURAL BANK', 'JUABENG RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3178', 'KUMAWUMAN RURAL BANK', 'KUMAWUMAN RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3179', 'KWAEBIBIREM RURAL BANK', 'KWAEBIBIREM RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3180', 'KWANWUMA RURAL BANK', 'KWANWUMA RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3181', 'MPONUA RURAL BANK', 'MPONUA RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3182', 'MUMUADU RURAL BANK', 'MUMUADU RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3183', 'NZEMA MANLE RURAL BANK', 'NZEMA MANLE RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3184', 'ODOTOBRI/RURAL BANK', 'ODOTOBRI/RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3185', 'OTUASEKAN RURAL BANK', 'OTUASEKAN RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3186', 'SEKYERE RURAL BANK', 'SEKYERE RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3187', 'UPPER AMENFI RURAL BANK', 'UPPER AMENFI RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3188', 'UPPER MANYA KROBO RURAL BANK', 'UPPER MANYA KROBO RURAL BANK', NULL);
INSERT INTO ppm_dict_detail (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME, README, obis_code) VALUES ('204', '3189', 'ROYAL BANK', 'ROYAL BANK', NULL);

-- 重建PPM_CMS_PREP_TRANS_EXT, modified at 2020.1.7 15:46
CREATE TABLE PPM_CMS_PREP_TRANS_EXT
   (	
   "TS" DATE NOT NULL ENABLE, 
	"TRANSACTION_ID" NVARCHAR2(32) NOT NULL ENABLE, 
	"NUM_APA" NVARCHAR2(32), 
	"CO_MARCA" NVARCHAR2(32), 
	"CUST_NAME" NVARCHAR2(64), 
	"CUSTOMER_NUMBER" NVARCHAR2(32), 
	"SERVICE_POINT_NO" NVARCHAR2(32), 
	"VENDOR_ID" NVARCHAR2(32), 
	"RECPT_NO" NVARCHAR2(64) NOT NULL ENABLE, 
	"TOKEN_NO" NVARCHAR2(64), 
	"PMETHOD" NVARCHAR2(32), 
	"CO_CONCEPTO" NVARCHAR2(32) NOT NULL ENABLE, 
	"CSMO_FACT" NUMBER(12,4), 
	"CYCLE" NUMBER(2,0), 
	"CYCLE_DATE" DATE, 
	"IMP_CONCEPTO" NUMBER(15,4), 
	"DEBT_REF_NO" NVARCHAR2(32), 
	"COD_UNICOM" NUMBER(10,0), 
	"OPERATOR_NAME" NVARCHAR2(64), 
	"CO_SISTEMA" NVARCHAR2(32) NOT NULL ENABLE, 
	"NUM_CHEQUE" NVARCHAR2(32), 
	"COD_CENCOBRO_BANK" NUMBER(10,0), 
	"EXPORTED" NUMBER(2,0), 
	"EXPORTED_TS" DATE, 
	"IS_STORE" NVARCHAR2(2), 
	PRIMARY KEY (TRANSACTION_ID)
   );
   
-- 文件MD5变更，该位置开始需手动执行 2019-12-24 15:18
ALTER TABLE PPM_VEND_CUSTOMER_DEBT ADD (DEBT_SOURCE varchar2(20)); 
ALTER TABLE ppm_vend_free_token_manage ADD (CREATE_DATE date default sysdate);

INSERT INTO ppm_dict_operation (ID, OPERATIONNAME, FUNCTION_ID, OPERATIONURL, ISHIDE, DESCRIPTION) VALUES ('2005001', 'Recharge', '2005', '/vendFreeTokenManageController/saveVendFreeTokenInfo.do', '1', 'Free Token Recharge');
INSERT INTO ppm_dict_operation (ID, OPERATIONNAME, FUNCTION_ID, OPERATIONURL, ISHIDE, DESCRIPTION) VALUES ('2005002', 'Print', '2005', '/vendFreeTokenManageController/print.do', '1', 'Print Tiket Again');
INSERT INTO ppm_dict_operation (ID, OPERATIONNAME, FUNCTION_ID, OPERATIONURL, ISHIDE, DESCRIPTION) VALUES ('2005003', 'Send Token', '2005', 'sendFreeRechargeToken', '1', 'Send Free Recharge Token');
INSERT INTO ppm_dict_operation (ID, OPERATIONNAME, FUNCTION_ID, OPERATIONURL, ISHIDE, DESCRIPTION) VALUES ('2005004', 'Send Token '||'&'||' Print', '2005', 'issuedFreeTokenAndPrint', '1', 'Send Free Token And Print');
INSERT INTO ppm_dict_operation (ID, OPERATIONNAME, FUNCTION_ID, OPERATIONURL, ISHIDE, DESCRIPTION) VALUES ('2005005', 'Print Button', '2005', 'printFreeTokenTicket', '1', 'Print Free Token Ticket');

INSERT INTO ppm_sys_menu (ID, UTILITY_IDS, MENULEVEL, MENUNAME, MENUORDER, FUNCTIONURL, PARENTMENUID, MENU_INTRO, FUNCTIONID, HIDE_TAB) VALUES ('1e858f11beda11e79bb968f728c52005', '1', '2', 'Free Token', '5', 'vendFreeTokenManageController/vendFreeToken.do', '1e858f11beda11e79bb968f728c50002', NULL, '2005', NULL);
INSERT INTO ppm_dict_function (ID, FUNCTIONNAME, FUNCTIONURL, FUNCTION_INTRO) VALUES ('2005', 'Free Token', 'vendFreeTokenManageController/vendFreeToken.do', 'Recharge');

-- 文件MD5变更，该位置开始需手动执行 2019-12-30 15:18
ALTER TABLE ppm_vend_historical_info ADD (POWER_TOTAL NUMBER(20,4));
ALTER TABLE ppm_vend_historical_info ADD (POWER_LASTMONTH NUMBER(20,4));

-- 文件MD5变更，该位置开始需手动执行 2019-01-13 17:18
INSERT INTO PPM_SYS_MENU(ID, UTILITY_IDS, MENULEVEL, MENUNAME, MENUORDER, FUNCTIONURL, PARENTMENUID, MENU_INTRO, FUNCTIONID, HIDE_TAB) VALUES ('1e458f11beda11e79bb968f728c50011', '29018328bd4011e79bb968f728c516f9', '2', 'Energy Sales Breakdown', '11', 'uReportController/energySalesBreakdown.do', '1e858f11beda11e79bb968f728c50006', NULL, NULL, NULL);

-- 文件MD5变更，该位置开始需手动执行 2020-02-02 19:18
ALTER TABLE PPM_VEND_STATION_RECHARGE ADD CENCOBRO_BANK NUMBER(10,0);

INSERT INTO ppm_sys_menu (ID, UTILITY_IDS, MENULEVEL, MENUNAME, MENUORDER, FUNCTIONURL, PARENTMENUID, MENU_INTRO, FUNCTIONID, HIDE_TAB) VALUES ('1c458f11beda11e79bb968f728c50010', '1', '2', 'Vending Station Recharge Report', '8', 'uReportController/stationRechargeReport.do', '1e858f11beda11e79bb968f728c50006', NULL, NULL, NULL);
INSERT INTO ppm_sys_menu (ID, UTILITY_IDS, MENULEVEL, MENUNAME, MENUORDER, FUNCTIONURL, PARENTMENUID, MENU_INTRO, FUNCTIONID, HIDE_TAB) VALUES ('1c458f11beda11e79bb968f728c50011', '1', '2', 'Customer Billing Data Report', '9', 'uReportController/customerBillingDataReport.do', '1e858f11beda11e79bb968f728c50006', NULL, NULL, NULL);

-- 新增预收取库表，文件MD5变更，该位置开始需手动执行 2020-02-06 20:16
CREATE TABLE PPM_VEND_HISTORICAL_DETAIL 
(	
	ID VARCHAR2(32 BYTE), 
	CUSTOMER_ID VARCHAR2(32 BYTE),    
	METER_ID VARCHAR2(32 BYTE),
	ESTIMATION_SALES_ID VARCHAR2(32 BYTE),   
	SUPPLYMENT_SALES_ID VARCHAR2(32 BYTE),
	SALES_DATE DATE, 
	DEBT_AMOUNT NUMBER(10,4) DEFAULT '0.0000', 
	TAX_AMOUNT NUMBER(10,4) DEFAULT '0.0000', 
	OTHER_TOTAL_AMOUNT NUMBER(10,4) DEFAULT '0.0000',
	SALES_STATUS NUMBER(2,0) DEFAULT NULL,
	UPDATE_DATE DATE DEFAULT NULL, 
	SAVE_DB_DATE DATE DEFAULT CURRENT_TIMESTAMP,
	PRIMARY KEY (CUSTOMER_ID,METER_ID,SALES_DATE)
);
    
	COMMENT ON COLUMN PPM_VEND_HISTORICAL_DETAIL.SALES_DATE IS '标注该月份客户是否收取过税费等费用，时间格式化：YYYY-MM-01 00:00:00';
	COMMENT ON COLUMN PPM_VEND_HISTORICAL_DETAIL.SALES_ID IS '充值ID, ppm_vend_historical_info表中的ID字段';
	COMMENT ON COLUMN PPM_VEND_HISTORICAL_DETAIL.DEBT_ID IS '债务ID';
	COMMENT ON COLUMN PPM_VEND_HISTORICAL_DETAIL.DEBT_AMOUNT IS '该月份收取的债务费用';
	COMMENT ON COLUMN PPM_VEND_HISTORICAL_DETAIL.TAX_AMOUNT IS '该月份收取的税费等费用';  
	COMMENT ON COLUMN PPM_VEND_HISTORICAL_DETAIL.OTHER_TOTAL_AMOUNT IS '该月份收取的其它费用'; 
	COMMENT ON COLUMN PPM_VEND_HISTORICAL_DETAIL.SALES_STATUS IS '该月份费用收取状态,0:未收取任何费用,1:已预收取部分费用,2:待预收取部分费用,3:已收取全部费用,4:待收取全部费用';  
	
	ALTER TABLE PPM_DATA_PREPAID_PROGRESS ADD LAST_ESTIMATION_DATE DATE;
	ALTER TABLE ppm_vend_historical_info ADD LAST_ESTIMATION_DATE DATE;
	
INSERT INTO ppm_sys_menu (ID, UTILITY_IDS, MENULEVEL, MENUNAME, MENUORDER, FUNCTIONURL, PARENTMENUID, MENU_INTRO, FUNCTIONID, HIDE_TAB)
VALUES ('13458f11beda11e79bb968f728c50a13', '1', '2', 'Installed Single Meters Report', '13', 'uReportController/installedSingleMeterReport.do', '1e858f11beda11e79bb968f728c50006', NULL, NULL, NULL);

INSERT INTO ppm_sys_menu (ID, UTILITY_IDS, MENULEVEL, MENUNAME, MENUORDER, FUNCTIONURL, PARENTMENUID, MENU_INTRO, FUNCTIONID, HIDE_TAB)
VALUES ('14458f11beda11e79bb968f728c50a14', '1', '2', 'Installed Three Meters Report', '14', 'uReportController/installedThreeMeterReport.do', '1e858f11beda11e79bb968f728c50006', NULL, NULL, NULL);