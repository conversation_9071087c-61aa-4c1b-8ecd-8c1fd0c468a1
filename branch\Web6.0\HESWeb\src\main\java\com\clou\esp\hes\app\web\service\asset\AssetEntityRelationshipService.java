package com.clou.esp.hes.app.web.service.asset;



import java.util.List;

import com.clou.esp.hes.app.web.model.asset.AssetEntityRelationship;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.service.common.CommonService;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

public interface AssetEntityRelationshipService extends CommonService<AssetEntityRelationship>{

	List<AssetMeter> findLinkedMeterList(JqGridSearchTo jqGridSearchTo);
	
	JqGridResponseTo findLinkedEntityMeterList(JqGridSearchTo jqGridSearchTo);
	
	JqGridResponseTo findLinkedEntityTransformerList(JqGridSearchTo jqGridSearchTo);
	
	JqGridResponseTo findUnLinkedMeterList(JqGridSearchTo jqGridSearchTo);
	
	JqGridResponseTo findUnLinkedTransformerList(JqGridSearchTo jqGridSearchTo);

	int  batchInsert(List<AssetEntityRelationship> relationships);
	
	void deleteRelationByEntity(AssetEntityRelationship entity);
	
}
