/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysOrg{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-10-27 09:17:08
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.system;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.system.SysOrgDao;
import com.clou.esp.hes.app.web.model.system.SysOrg;
import com.clou.esp.hes.app.web.service.system.SysOrgService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("sysOrgService")
public class SysOrgServiceImpl  extends CommonServiceImpl<SysOrg>  implements SysOrgService {

	@Resource
	private SysOrgDao sysOrgDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(sysOrgDao);
    }
	@SuppressWarnings("rawtypes")
	public SysOrgServiceImpl() {}
	
	@Override
	public List<SysOrg> getListByparentOrgTid(SysOrg sysOrg) {
		return sysOrgDao.getListByparentOrgTid(sysOrg);
	}
	
	@Override
	public List<SysOrg> getListByOrgCode(SysOrg sysOrg) {
		return sysOrgDao.getListByOrgCode(sysOrg);
	}
	
	
}