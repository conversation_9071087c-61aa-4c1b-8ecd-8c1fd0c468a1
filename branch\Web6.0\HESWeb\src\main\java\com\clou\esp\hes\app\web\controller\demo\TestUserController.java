/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class TestUser{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-03-29 01:14:36
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.demo;

import java.io.UnsupportedEncodingException;
import java.util.Date;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import ch.iec.tc57._2011.getmeterreadings_.EndDevice;
import ch.iec.tc57._2011.getmeterreadings_.GetMeterReadings;
import ch.iec.tc57._2011.getmeterreadings_.Name;
import ch.iec.tc57._2011.getmeterreadings_.NameType;
import ch.iec.tc57._2011.getmeterreadings_.ReadingType;
import ch.iec.tc57._2011.meterreadings.FaultMessage;
import ch.iec.tc57._2011.meterreadings.GetMeterReadingsPort;
import ch.iec.tc57._2011.meterreadingsmessage.GetMeterReadingsPayloadType;
import ch.iec.tc57._2011.meterreadingsmessage.MeterReadingsRequestMessageType;
import ch.iec.tc57._2011.meterreadingsmessage.MeterReadingsResponseMessageType;
import ch.iec.tc57._2011.schema.message.HeaderType;
import ch.iec.tc57._2011.schema.message.RequestType;
import ch.iec.tc57._2011.schema.message.UserType;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.pushlet.PushletData;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.util.SpringContextUtil;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.demo.TestUserService;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.json.AjaxJson;

/**
 * <AUTHOR>
 * @时间：2017-03-29 01:14:36
 * @描述：测试用户类
 */
@Controller
@RequestMapping("/testUserController")
public class TestUserController extends BaseController{

	@Resource
	private TestUserService testUserService;
	/**
	 * 跳转到测试用户列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value="list")
	public ModelAndView list(HttpServletRequest request, Model model) {
		AjaxJson j=new AjaxJson();
		//TestUser testUser=testUserService.getEntity("bfe091bdad6011e792050023248e36fd");
		//model.addAttribute("testUser", testUser);
		/*PARTWebService cmsService = (PARTWebService)SpringContextUtil.getBean("PARTWebService");
		Header h=new Header();
		Payload p=new Payload();
		h.setAsyncReplyFlag("2");
		h.setCorrelationID("234234");
		h.setNoun("dddddd");
		Reply r=cmsService.doCommand(h, p);
		System.out.println(r.toString());
		//System.out.println(mcr);
		TestUser u=new TestUser();
		u.setBirthday(new Date());
		u.setName("王麻子");
		u.setNickName("嘛啊子");
		u.setSex("0");
		u.setTel("13715058465");
		testUserService.save(u);*/
		 /*SysUser su=TokenManager.getToken();
		GetMeterReadingsPort port = (GetMeterReadingsPort)SpringContextUtil.getBean("GetMeterReadingsPort");
		 System.out.println("Invoking getMeterReadings...");
		 System.out.println(port.toString());
		 MeterReadingsRequestMessageType mr=new MeterReadingsRequestMessageType();
		 HeaderType ht=new HeaderType();
		 Date date=new Date();
		 ht.setVerb("get");
		 ht.setNoun("MeterReadings");
		 ht.setTimestamp(DateUtils.dateToXmlDate(date));
		 ht.setSource("ClouESP HES");
		 ht.setMessageID("123456");
		 ht.setAsyncReplyFlag(true);
		 ht.setReplyAddress("http://127.0.0.1:8080");
		 ht.setAckRequired(true);
		 UserType u =new UserType();
		 u.setOrganization(su.getUsername());
		 u.setUserID(su.getId());
		 ht.setUser(u);
		 mr.setHeader(ht);
		 RequestType rt=new RequestType();
        GetMeterReadings getGetMeterReadings = new GetMeterReadings();
        ReadingType readingType = new ReadingType();
        Name n=new Name();
        NameType nt=new NameType();
        //cim数据编码，在dict_dataitem表中可以找到，dataitem_id
        n.setName("*******.********.0.0.0.0.*******.72.0");
        nt.setName("ReadingType");
        n.setNameType(nt);
        readingType.getNames().add(n);
        getGetMeterReadings.getReadingType().add(readingType);
        EndDevice ed=new EndDevice();
        ed.setMRID("014000000017");
        getGetMeterReadings.getEndDevice().add(ed);
        GetMeterReadingsPayloadType gmrpt=new GetMeterReadingsPayloadType();
        gmrpt.setGetMeterReadings(getGetMeterReadings);
         mr.setPayload(gmrpt);
		 mr.setRequest(rt);
		 try {
			MeterReadingsResponseMessageType s= port.getMeterReadings(mr);
			j.setObj(s);
			try {
				PushletData.pushlet("testMsg", j, su.getId());
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
			}
			model.addAttribute("testUser", j);
			System.out.println(j.toString());
		} catch (FaultMessage e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}*/
		try {
			SysUser s=TokenManager.getToken();
			AjaxJson json = new AjaxJson();
			json.setMsg("This user account is logged elsewhere!");
			PushletData.pushlet("logingSin", json, s.getId());
		} catch (UnsupportedEncodingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return new ModelAndView("/demo/testUser");
	}
	/**
	 * 临时首页地址
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value="index")
	public ModelAndView index(HttpServletRequest request, Model model) {
		return new ModelAndView("/index/index");
	}


}