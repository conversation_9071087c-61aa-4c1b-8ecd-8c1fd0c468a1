/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysRoleMenu{ } 
 * 
 * 摘    要： 角色菜单表
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-10-27 09:28:59
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.system;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

/**
 * <AUTHOR>
 *
 */
public class SysRoleMenu  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public SysRoleMenu() {
	}
	/**
	 * 菜单ID
	 */
	private java.lang.String menuId;
	/**
	 * 角色ID
	 */
	private java.lang.String roleId;

	/**操作权限*/
	private java.lang.String operation;
	
	/**菜单名称*/
	private java.lang.String menuName;
	
	/**菜单等级*/
	private java.lang.Integer menuLevel;
	
	/**父菜单编号 0=一级菜单；*/
	private java.lang.String parentMenuId;
	
	/**functionId；*/
	private java.lang.String functionId;

	/**
	 * 操作权限
	 * @return the value of SYS_ROLE_MENU.OPERATION
	 * @mbggenerated 2017-10-27 09:28:59
	 */
	public java.lang.String getOperation() {
		return operation;
	}

	/**
	 * 操作权限
	 * @param operation the value for SYS_ROLE_MENU.OPERATION
	 * @mbggenerated 2017-10-27 09:28:59
	 */
    	public void setOperation(java.lang.String operation) {
		this.operation = operation;
	}
    	
    	
    

	public java.lang.String getMenuId() {
		return menuId;
	}

	public void setMenuId(java.lang.String menuId) {
		this.menuId = menuId;
	}

	public java.lang.String getRoleId() {
		return roleId;
	}

	public void setRoleId(java.lang.String roleId) {
		this.roleId = roleId;
	}

	public SysRoleMenu(java.lang.String operation ) {
		super();
		this.operation = operation;
	}

	public java.lang.String getMenuName() {
		return menuName;
	}

	public void setMenuName(java.lang.String menuName) {
		this.menuName = menuName;
	}

	public java.lang.Integer getMenuLevel() {
		return menuLevel;
	}

	public void setMenuLevel(java.lang.Integer menuLevel) {
		this.menuLevel = menuLevel;
	}

	public java.lang.String getParentMenuId() {
		return parentMenuId;
	}

	public void setParentMenuId(java.lang.String parentMenuId) {
		this.parentMenuId = parentMenuId;
	}

	public java.lang.String getFunctionId() {
		return functionId;
	}

	public void setFunctionId(java.lang.String functionId) {
		this.functionId = functionId;
	}


}