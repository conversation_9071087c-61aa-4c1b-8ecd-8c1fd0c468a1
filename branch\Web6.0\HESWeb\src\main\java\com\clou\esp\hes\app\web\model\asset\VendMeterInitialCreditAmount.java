package com.clou.esp.hes.app.web.model.asset;



import com.clou.esp.hes.app.web.model.common.BaseEntity;

/*********************************************************************************************************
 * @see Copyright © 2018 Shenzhen Clou Electronics CO., LTD.  All rights reserved. 
 * @see 
 * @see File Name:public class VendMeterInitialCreditAmount{ } 
 * @see 
 * @see Description： Vend_Meter_initial_credit_amount 电表预置金额记录表：预付费电表，常规情况下：单相表预置
 * @version *******
 * <AUTHOR>
 * @see Create Time：2018-06-06 20:08:49
 * @see Last modification Time：2018-06-06 20:08:49
 * 
*********************************************************************************************************/
public class VendMeterInitialCreditAmount  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public VendMeterInitialCreditAmount() {
	}

	/**电表ID*/
	private java.lang.String meterId;
	/**预付费表预置金额*/
	private java.math.BigDecimal initCreditAmount;
	/**缴费时间*/
	private java.util.Date paymentTime;
	/**状态：0表示未处理、1表示已经缴费处理完毕*/
	private java.lang.Integer state;
	/**售电历史ID：此ID 记录的是对应收取该费用的售电记录，便于检索。*/
	private java.lang.String salesId;
	/**创建时间*/
	private java.util.Date createDate;
	/**备注*/
	private java.lang.String readme;

	/**
	 * 电表ID
	 * @return the value of VEND_METER_INITIAL_CREDIT_AMOUNT.METER_ID
	 * @mbggenerated 2018-06-06 20:08:49
	 */
	public java.lang.String getMeterId() {
		return meterId;
	}

	/**
	 * 电表ID
	 * @param meterId the value for VEND_METER_INITIAL_CREDIT_AMOUNT.METER_ID
	 * @mbggenerated 2018-06-06 20:08:49
	 */
    	public void setMeterId(java.lang.String meterId) {
		this.meterId = meterId;
	}
	/**
	 * 预付费表预置金额
	 * @return the value of VEND_METER_INITIAL_CREDIT_AMOUNT.INIT_CREDIT_AMOUNT
	 * @mbggenerated 2018-06-06 20:08:49
	 */
	public java.math.BigDecimal getInitCreditAmount() {
		return initCreditAmount;
	}

	/**
	 * 预付费表预置金额
	 * @param initCreditAmount the value for VEND_METER_INITIAL_CREDIT_AMOUNT.INIT_CREDIT_AMOUNT
	 * @mbggenerated 2018-06-06 20:08:49
	 */
    	public void setInitCreditAmount(java.math.BigDecimal initCreditAmount) {
		this.initCreditAmount = initCreditAmount;
	}
	/**
	 * 缴费时间
	 * @return the value of VEND_METER_INITIAL_CREDIT_AMOUNT.PAYMENT_TIME
	 * @mbggenerated 2018-06-06 20:08:49
	 */
	public java.util.Date getPaymentTime() {
		return paymentTime;
	}

	/**
	 * 缴费时间
	 * @param paymentTime the value for VEND_METER_INITIAL_CREDIT_AMOUNT.PAYMENT_TIME
	 * @mbggenerated 2018-06-06 20:08:49
	 */
    	public void setPaymentTime(java.util.Date paymentTime) {
		this.paymentTime = paymentTime;
	}
	/**
	 * 状态：0表示未处理、1表示已经缴费处理完毕
	 * @return the value of VEND_METER_INITIAL_CREDIT_AMOUNT.STATE
	 * @mbggenerated 2018-06-06 20:08:49
	 */
	public java.lang.Integer getState() {
		return state;
	}

	/**
	 * 状态：0表示未处理、1表示已经缴费处理完毕
	 * @param state the value for VEND_METER_INITIAL_CREDIT_AMOUNT.STATE
	 * @mbggenerated 2018-06-06 20:08:49
	 */
    	public void setState(java.lang.Integer state) {
		this.state = state;
	}
	/**
	 * 售电历史ID：此ID 记录的是对应收取该费用的售电记录，便于检索。
	 * @return the value of VEND_METER_INITIAL_CREDIT_AMOUNT.SALES_ID
	 * @mbggenerated 2018-06-06 20:08:49
	 */
	public java.lang.String getSalesId() {
		return salesId;
	}

	/**
	 * 售电历史ID：此ID 记录的是对应收取该费用的售电记录，便于检索。
	 * @param salesId the value for VEND_METER_INITIAL_CREDIT_AMOUNT.SALES_ID
	 * @mbggenerated 2018-06-06 20:08:49
	 */
    	public void setSalesId(java.lang.String salesId) {
		this.salesId = salesId;
	}
	/**
	 * 创建时间
	 * @return the value of VEND_METER_INITIAL_CREDIT_AMOUNT.CREATE_DATE
	 * @mbggenerated 2018-06-06 20:08:49
	 */
	public java.util.Date getCreateDate() {
		return createDate;
	}

	/**
	 * 创建时间
	 * @param createDate the value for VEND_METER_INITIAL_CREDIT_AMOUNT.CREATE_DATE
	 * @mbggenerated 2018-06-06 20:08:49
	 */
    	public void setCreateDate(java.util.Date createDate) {
		this.createDate = createDate;
	}
	
	/**
	 * 备注
	 * @return the value of VEND_METER_INITIAL_CREDIT_AMOUNT.README
	 * @mbggenerated 2018-06-06 20:08:49
	 */
	public java.lang.String getReadme() {
		return readme;
	}

	/**
	 * 备注
	 * @param readme the value for VEND_METER_INITIAL_CREDIT_AMOUNT.README
	 * @mbggenerated 2018-06-06 20:08:49
	 */
    	public void setReadme(java.lang.String readme) {
		this.readme = readme;
	}

	public VendMeterInitialCreditAmount(java.lang.String meterId 
	,java.math.BigDecimal initCreditAmount 
	,java.util.Date paymentTime 
	,java.lang.Integer state 
	,java.lang.String salesId 
	,java.util.Date createDate 
	,java.lang.String readme ) {
		super();
		this.meterId = meterId;
		this.initCreditAmount = initCreditAmount;
		this.paymentTime = paymentTime;
		this.state = state;
		this.salesId = salesId;
		this.createDate = createDate;
		this.readme = readme;
	}

}