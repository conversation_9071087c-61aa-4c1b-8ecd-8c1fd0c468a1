/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictReport{ } 
 * 
 * 摘    要： dictReport
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-05-22 04:14:25
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.report;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.tag.vo.ZtreeUtil;
import com.clou.esp.hes.app.web.core.util.CreatePdf;
import com.clou.esp.hes.app.web.core.util.DateTimeFormatterUtil;
import com.clou.esp.hes.app.web.core.util.ExcelUtils;
import com.clou.esp.hes.app.web.core.util.Globals;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.model.asset.AssetCalcObj;
import com.clou.esp.hes.app.web.model.asset.AssetCalcObjMap;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroup;
import com.clou.esp.hes.app.web.model.data.DataCalcObj;
import com.clou.esp.hes.app.web.model.data.DataCalcObjDto;
import com.clou.esp.hes.app.web.model.data.DataDeviceLoginLog;
import com.clou.esp.hes.app.web.model.dict.DictCommunicationType;
import com.clou.esp.hes.app.web.model.dict.DictCustomerIndustry;
import com.clou.esp.hes.app.web.model.dict.DictCustomerType;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;
import com.clou.esp.hes.app.web.model.dict.DictDataitemGroup;
import com.clou.esp.hes.app.web.model.dict.DictProfile;
import com.clou.esp.hes.app.web.model.report.BillingReport;
import com.clou.esp.hes.app.web.model.report.DictReport;
import com.clou.esp.hes.app.web.model.report.LineLossDetailReport;
import com.clou.esp.hes.app.web.model.report.LineLossReport;
import com.clou.esp.hes.app.web.model.system.SysOrg;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.model.vee.DictVeeEvent;
import com.clou.esp.hes.app.web.service.asset.AssetCalcObjService;
import com.clou.esp.hes.app.web.service.asset.AssetCustomerService;
import com.clou.esp.hes.app.web.service.data.DataDeviceLoginLogService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemGroupService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemService;
import com.clou.esp.hes.app.web.service.dict.DictProfileService;
import com.clou.esp.hes.app.web.service.report.DictReportService;
import com.clou.esp.hes.app.web.service.system.SysOrgService;
import com.clou.esp.hes.app.web.service.vee.DictVeeEventService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.itextpdf.text.pdf.PdfStructTreeController.returnType;
import com.power7000g.core.excel.ExcelDataFormatter;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;

/**
 * <AUTHOR>
 * @时间：2018-05-22 04:14:25
 * @描述：dictReport类
 */
@Controller
@RequestMapping("/dictReportController")
public class DictReportController extends BaseController{

 	@Resource
    private DictReportService 		  dictReportService;
 	@Resource
 	private SysOrgService     		  sysOrgService;
 	@Resource
 	private AssetCustomerService      assetCustomerService;
 	@Resource
 	private DictProfileService    	  dictProfileService;
 	@Resource
 	private DictDataitemGroupService  dictDataitemGroupService;
 	@Resource
 	private DictDataitemService       dictDataitemService;
 	@Resource
 	private DictVeeEventService       dictVeeEventService;
	@Resource
 	private AssetCalcObjService       assetCalcObjService;
	@Resource
	private DataDeviceLoginLogService dataDeviceLoginLogService;

	public static final String MONTH_FORMAT ="MM/yyyy";
	public static final String DAY_FORMAT =ResourceUtil.getSessionattachmenttitle("local.date.formatter");
	public static final String MIN_FORMAT =ResourceUtil.getSessionattachmenttitle("local.date.time.formatter");

	private SimpleDateFormat sdfMonthly = new SimpleDateFormat(MONTH_FORMAT);
	SimpleDateFormat sdfDay = new SimpleDateFormat(DAY_FORMAT);
	SimpleDateFormat sdfMin = new SimpleDateFormat(MIN_FORMAT);

	/**
	 * 跳转到line loss single object 列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "lineLossSingleObjectList")
    public ModelAndView lineLossSingleObjectList(HttpServletRequest request, Model model) {
		SimpleDateFormat sdf = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.formatter"));
	    Calendar lastDate = Calendar.getInstance();
	    lastDate.add(Calendar.DATE, -1);	//昨天
	    model.addAttribute("lineLossStartTime", sdf.format(lastDate.getTime()));
	    lastDate.add(Calendar.DATE, 1);
	    model.addAttribute("lineLossExpiryTime", sdf.format(lastDate.getTime()));
        return new ModelAndView("/report/jasper/lineLossSingleObjReport");
    }
	
	/**
	 * 跳转到line loss org 列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "lineLossOrgList")
    public ModelAndView lineLossOrgList(HttpServletRequest request, Model model) {
		String orgId = (String) request
				.getAttribute(Globals.REQUESST_DATA_AUTH_ORG_ID);
		SimpleDateFormat sdf = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.formatter"));
	    Calendar lastDate = Calendar.getInstance();
	    lastDate.add(Calendar.DATE, -1);	//昨天
	    model.addAttribute("lineLossStartTime", sdf.format(lastDate.getTime()));
	    lastDate.add(Calendar.DATE, 1);
	    model.addAttribute("lineLossExpiryTime", sdf.format(lastDate.getTime()));
        return new ModelAndView("/report/jasper/lineLossOrgReport");
    }
	
	/**
	 * 跳转到billing报表列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "billingReportList")
    public ModelAndView billingReportList(HttpServletRequest request, Model model) {
		String orgId = (String) request.getAttribute(Globals.REQUESST_DATA_AUTH_ORG_ID);
		SimpleDateFormat sdf = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.formatter"));
	    Calendar cale = Calendar.getInstance();
	    cale = Calendar.getInstance();
		cale.add(Calendar.MONTH, 0);
		cale.set(Calendar.DAY_OF_MONTH, 1);

		Calendar cale1 = Calendar.getInstance();
		cale1 = Calendar.getInstance();
		cale1.add(Calendar.MONTH, -1);
		cale1.set(Calendar.DAY_OF_MONTH, 1);
		
	    model.addAttribute("billingStartTime", sdf.format(cale1.getTime()));
	    model.addAttribute("billingExpiryTime", sdf.format(cale.getTime()));
        return new ModelAndView("/report/jasper/billingOrgReport");
    }
	
	
	 /**
     * 删除dataParameterPlan信息
     * @param id
     * @return
     */
    @RequestMapping(value = "dateType")
    @ResponseBody
    public AjaxJson linkageDateType(HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        Map<String , Object> dateTypeMap = new HashMap();
        try {
    	    Calendar lastDate = Calendar.getInstance();
        	String lineLossDateType = (String) request.getParameter("lineLossDateType");
            if("1".equals(lineLossDateType)){
            	SimpleDateFormat sdf = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.formatter"));
            	dateTypeMap.put("lineLossExpiryTime", sdf.format(lastDate.getTime()));
            	lastDate.add(Calendar.DATE, -7);
            	dateTypeMap.put("lineLossStartTime", sdf.format(lastDate.getTime()));
            }else if("2".equals(lineLossDateType)){
            	SimpleDateFormat sdf = new SimpleDateFormat("MM/yyyy");
            	dateTypeMap.put("lineLossExpiryTime", sdf.format(lastDate.getTime()));
            	lastDate.add(Calendar.MONTH,-6);
            	dateTypeMap.put("lineLossStartTime", sdf.format(lastDate.getTime()));
            	
            	
            }
            j.setAttributes(dateTypeMap);
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg("Abnormal operation");
        }
        return j;
    }
 	
    
    /**
     * 删除dataParameterPlan信息
     * @param id
     * @return
     */
    @RequestMapping(value = "billingDateType")
    @ResponseBody
    public AjaxJson billingDateType(HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        Map<String , Object> dateTypeMap = new HashMap();
        try {
    
        	String lineLossDateType = (String) request
    				.getParameter("billingDateType");
        	
        	  Calendar cale = Calendar.getInstance();
      	    cale = Calendar.getInstance();
      		cale.add(Calendar.MONTH, 0);
      		cale.set(Calendar.DAY_OF_MONTH, 1);

      		Calendar cale1 = Calendar.getInstance();
      		cale1 = Calendar.getInstance();
      		cale1.add(Calendar.MONTH, -1);
      		cale1.set(Calendar.DAY_OF_MONTH, 1);
      		
            if("1".equals(lineLossDateType)){
            	SimpleDateFormat sdf = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.formatter"));
            	Date startDate=DateUtils.getDateAdd(new Date(), Calendar.DATE, -1);
            	Date endDate=DateUtils.getDateAdd(new Date(), Calendar.DATE, 0);
            	dateTypeMap.put("billingStartTime", sdf.format(startDate));
            	dateTypeMap.put("billingExpiryTime", sdf.format(endDate));
            }else if("2".equals(lineLossDateType)){
            	SimpleDateFormat sdf = new SimpleDateFormat("MM/yyyy");
            	dateTypeMap.put("billingStartTime", sdf.format(cale1.getTime()));
            	dateTypeMap.put("billingExpiryTime", sdf.format(cale.getTime()));
            }
            j.setAttributes(dateTypeMap);
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg("Abnormal operation");
        }
        return j;
    }

	/**
	 * 跳转到dictReport列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
		DictReport entityParent=new DictReport();
		entityParent.setParentId("0");
		List<DictReport> dictReportList = dictReportService.getList(entityParent);
		request.setAttribute("dictReportList", dictReportList);
        return new ModelAndView("/report/jasper/reportMainFrame");
    }
	
	/**
	 * 获取选择显示列表树形框数据
	 * @param searchName
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getReportZtree")
	@ResponseBody
	public AjaxJson getChannelZtree(HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		List<Map<String, Object>> list = new ArrayList<>();
		DictReport entityParent=new DictReport();
		entityParent.setParentId("0");
		List<DictReport> dictReportList = dictReportService.getList(entityParent);
		for(DictReport g:dictReportList){
			Map<String, Object> m = new HashMap<String, Object>();
			m.put("id", g.getId());
			m.put("name", g.getReportname());
			m.put("parent", "");
			m.put("expanded", true);
			DictReport entityChildren=new DictReport();
			entityChildren.setParentId(g.getId());
			List<DictReport> entityChildrenList = dictReportService.getList(entityChildren);
			List<Map<String, Object>> listk = new ArrayList<>();
			for(DictReport d:entityChildrenList){
				Map<String, Object> dm = new HashMap<String, Object>();
				dm.put("id", d.getId());
				dm.put("name", d.getReportname());
				dm.put("parent", g.getId());
				dm.put("expanded", false);
				dm.put("functionUrl", d.getFunctionurl());
				listk.add(dm);
			}
			
			if(listk.size()>0){
			m.put("list", listk);
			list.add(m);
			}
			
		}
		ZtreeUtil jtu = new ZtreeUtil();
		jtu.setIdFname("id");
		jtu.setNameFname("name");
		jtu.setpIdFname("parent");
		jtu.setSubsetFieldName("list");
		jtu.setOpenFname("expanded");
		jtu.seteUrl("functionUrl");
		j.setObj(jtu.getZtreeNodeData(list));
		return j;
	}
	

	/**
	 * 跳转到dictReport新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dictReport")
	public ModelAndView dictReport(DictReport dictReport,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dictReport.getId())){
			try {
                dictReport=dictReportService.getEntity(dictReport.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dictReport", dictReport);
		}
		return new ModelAndView("/report/dictReport");
	}


	/**
	 * dictReport查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=dictReportService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除dictReport信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DictReport dictReport,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dictReportService.deleteById(dictReport.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存dictReport信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DictReport dictReport,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DictReport t=new  DictReport();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dictReport.getId())){
        	t=dictReportService.getEntity(dictReport.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dictReport, t);
				dictReportService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            dictReportService.save(dictReport);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
	
    @RequestMapping(value = "findLineLossSingleJqGrid")
    @ResponseBody
    public JqGridResponseTo findLineLossSingleJqGrid(boolean flag,String lineLossObjectType,
    		String timeType,String startTime,String endTime,String timePattern,String orgId,
    		String entity,String nodeType,String entityId,String rangeStart,String rangeEnd,
    		JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
        	if(!flag){
        		j = new JqGridResponseTo();
        		return j;
        	}
        	// 参数赋值
        	if(StringUtils.isEmpty(orgId)) {
    			SysUser su = TokenManager.getToken();
    			orgId=su.getOrgId();
    		}
        	setOrgCode(jqGridSearchTo, orgId);
        	
//        	if(StringUtils.isEmpty(entityId)) {
//        		endTime=startTime;
//        	}
        	jqGridSearchTo.put("lineLossObjectType", lineLossObjectType);
        	jqGridSearchTo.put("timeType", timeType);
        	jqGridSearchTo.put("startTime", startTime);
        	jqGridSearchTo.put("endTime", endTime);
        	jqGridSearchTo.put("timePattern", timePattern);
        	jqGridSearchTo.put("orgId", orgId);
        	jqGridSearchTo.put("entity", entity);
        	jqGridSearchTo.put("entityId", entityId);
        	if(StringUtils.isNotEmpty(rangeStart)) {
        		jqGridSearchTo.put("rangeStart", rangeStart);
        	}
        	if(StringUtils.isNotEmpty(rangeEnd)) {
        		jqGridSearchTo.put("rangeEnd", rangeEnd);
        	}
        	// 用于mysql 时间转换
        	String datePattern = convertDatePattern(timePattern);
        	jqGridSearchTo.put("datePattern", datePattern);
        	// 数据库查询
        	j = dictReportService.findLineLossSingleJqGrid(jqGridSearchTo);
        } catch (Exception e) {
            e.printStackTrace();
        }
		return j;
    }
    
    
    @RequestMapping(value = "getChartData")
    @ResponseBody
    public AjaxJson getChartData(String timeType,String compType,String startTime,String endTime,JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
    	TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
    	String[] calaObjIds = request.getParameterValues("calaObjIds[]");
    	if(StringUtils.isEmpty(startTime)||StringUtils.isEmpty(endTime)||calaObjIds==null||calaObjIds.length==0) {
    		return null;
    	}
    	
    	AjaxJson j=new AjaxJson();
    	try {
    		String pattern =ResourceUtil.getSessionattachmenttitle("local.date.formatter");
    		SimpleDateFormat sdf = new SimpleDateFormat(pattern);
    		SimpleDateFormat sdf1 = new SimpleDateFormat("MM/yyyy");
    		jqGridSearchTo.put("dateType", timeType);
    		jqGridSearchTo.put("calaObjIds", calaObjIds);
    		//获取日期数组
    		List<String> dates=null;
    		
    		if("1".equals(timeType)) {
    			jqGridSearchTo.put("startTime", sdf.parse(startTime));
        		jqGridSearchTo.put("endTime", sdf.parse(endTime));
        		dates=DateTimeFormatterUtil.getDays(startTime, endTime, pattern);
    		}else if("2".equals(timeType)) {
    			jqGridSearchTo.put("startTime", sdf1.parse(startTime));
        		jqGridSearchTo.put("endTime", sdf1.parse(endTime));
        		dates=DateTimeFormatterUtil.getMonthlys(startTime, endTime, "MM/yyyy");
    		}
    		
    		List<DataCalcObj> objs = this.dictReportService.getDataCalcObj(jqGridSearchTo);
    		
    		if("yoy".equals(compType)) {
    			//与去年同期相比
    			if("1".equals(timeType)) {
	    			jqGridSearchTo.put("startTime", getYearDate(sdf.parse(startTime), -1));
	        		jqGridSearchTo.put("endTime", getYearDate(sdf.parse(endTime),-1));
    			}else if("2".equals(timeType)){
    				jqGridSearchTo.put("startTime", getYearDate(sdf1.parse(startTime), -1));
	        		jqGridSearchTo.put("endTime", getYearDate(sdf1.parse(endTime),-1));
    			}
    			List<DataCalcObj> yoyObjs = this.dictReportService.getDataCalcObj(jqGridSearchTo);
    			if(objs==null) {
    				objs=Lists.newArrayList();
    			}
    			calaObjIds=new String[]{calaObjIds[0],calaObjIds[0]+"yoy"};
    			if(yoyObjs!=null) {
    				for(DataCalcObj obj: yoyObjs) {
        				obj.setCalcObjId(obj.getCalcObjId()+"yoy");
        				obj.setTv(getYearDate(obj.getTv(),1));
        				objs.add(obj);
        			}
    			}
    		}else if("qoq".equals(compType)) {
    			//与上个月相比
    			jqGridSearchTo.put("startTime", getMonthDate(sdf.parse(startTime), -1));
        		jqGridSearchTo.put("endTime", getMonthDate(sdf.parse(endTime),-1));
    			List<DataCalcObj> qoqObjs = this.dictReportService.getDataCalcObj(jqGridSearchTo);
    			if(objs==null) {
    				objs=Lists.newArrayList();
    			}
    			calaObjIds=new String[]{calaObjIds[0],calaObjIds[0]+"qoq"};
    			if(qoqObjs!=null) {
    				for(DataCalcObj obj: qoqObjs) {
        				obj.setCalcObjId(obj.getCalcObjId()+"qoq");
        				obj.setTv(getMonthDate(obj.getTv(),1));
        				objs.add(obj);
        			}
    			}
    			
    		}
    		
    		
    		//[calcName:date:calcObj]
    		Map<String,Map<String,DataCalcObj>>  dataMap = Maps.newHashMap();
    		Map<String,Map<String,DataCalcObj>>  restMap = Maps.newHashMap();
    		Map<String,String> calcObjNameMap = Maps.newHashMap();
    		AssetCalcObj ob = new AssetCalcObj();
    		ob.setIds(calaObjIds);
    		
    		//查询计算对象名称
    		List<AssetCalcObj> list = this.assetCalcObjService.getList(ob);   
    		for(AssetCalcObj o:list) {
    			calcObjNameMap.put(o.getId(), o.getName());
    		}
    		
    		if("yoy".equals(compType)) {
    			calcObjNameMap.put(calaObjIds[0]+"yoy",calcObjNameMap.get(calaObjIds[0])+"-"+MutiLangUtil.doMutiLang("lineLossReportList.yoy"));
    		}else if("qoq".equals(compType)) {
    			calcObjNameMap.put(calaObjIds[0]+"qoq",calcObjNameMap.get(calaObjIds[0])+"-"+MutiLangUtil.doMutiLang("lineLossReportList.qoq"));
    		}
    		
    		
    		for(DataCalcObj obj: objs) {
    			String calcObjId = obj.getCalcObjId();
    			String date ="";
    			if("2".equals(timeType)) {
    				date= obj.getTvStr("MM/yyyy");
    			}else {
    				date= obj.getTvStr("");
    			}
    			Map<String,DataCalcObj> tmpMap = dataMap.get(calcObjId);
    			if(tmpMap == null) {
    				tmpMap=Maps.newHashMap();
    			}
    			tmpMap.put(date, obj);
    			dataMap.put(calcObjId, tmpMap);
    		}
    		
    		//组装生成echart的数据
    		for(String objId: calaObjIds) {
    			Map<String,DataCalcObj> tmpMap = dataMap.get(objId);
    			String name =calcObjNameMap.get(objId);
    			restMap.put(name, tmpMap);
    		}
    		
    		String[] array = new String[dates.size()];
    		j.setObj(new DataCalcObjDto(dates.toArray(array), restMap));
    	} catch (Exception e) {
    		e.printStackTrace();
    	}
    	return j;
    }
	
    /**
	 * @Title: convertDatePattern
	 * @Description: TODO
	 * @param timePattern
	 * @return
	 * @return String
	 * @throws
	 */
	private String convertDatePattern(String timePattern) {
		// TODO Auto-generated method stub
		switch(timePattern){
		case "1":return "m%/%d/%Y";
		case "2":return "%Y/%m/%d";
		case "3":return "%d/%m/%Y";
		case "4":return "%d-%m-%Y";
		case "5": return "%Y-%m-%d";
		default: return "%d-%m-%Y";
		}
	}

//	@RequestMapping(value = "findLineLossOrganizationJqGrid")
//    @ResponseBody
//    public JqGridResponseTo findLineLossOrganizationJqGrid(boolean flag,
//    		String orgId,String lineLossObjectType,
//    		String timeType,String startTime,
//    		String endTime,String timePattern,String entity,
//    		JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
//        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
//        JqGridResponseTo j=null;
//        try {
//        	if(!flag){
//        		j = new JqGridResponseTo();
//        		return j;
//        	}
//        	
//        	// 参数赋值
//        	jqGridSearchTo.put("orgId", orgId);
//        	jqGridSearchTo.put("lineLossObjectType", lineLossObjectType);
//        	jqGridSearchTo.put("timeType", timeType);
//        	jqGridSearchTo.put("startTime", startTime);
//        	jqGridSearchTo.put("endTime", endTime);
//        	jqGridSearchTo.put("timePattern", timePattern);
//        	jqGridSearchTo.put("entity", entity);
//        	// 用于mysql 时间转换
//        	String datePattern = convertDatePattern(timePattern);
//        	jqGridSearchTo.put("datePattern", datePattern);
//        	
//        	// 数据库查询
//        	j = dictReportService.findLineLossOrganizationJqGrid(jqGridSearchTo);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//		return j;
//    }
	
	
	@RequestMapping(value = "findBillingOrganizationJqGrid")
    @ResponseBody
    public JqGridResponseTo findBillingOrganizationJqGrid(boolean flag,
    		String orgId,String sn,
    		String timeType,String startTime,
    		String endTime,String timePattern,
    		JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
        	if(!flag){
        		j = new JqGridResponseTo();
        		return j;
        	}
        	
        	this.setOrgCode(jqGridSearchTo,orgId);
        	
        	jqGridSearchTo.put("customerName", sn);//此处为customerName
        	jqGridSearchTo.put("timeType", timeType);
        	jqGridSearchTo.put("startTime", startTime);
        	jqGridSearchTo.put("endTime", endTime);
        	jqGridSearchTo.put("timePattern", timePattern);
        	
        	// 用于mysql 时间转换
        	String datePattern = convertDatePattern(timePattern);
        	jqGridSearchTo.put("datePattern", datePattern);
        	
        	// 数据库查询
        	j = dictReportService.findBillingOrganizationJqGrid(jqGridSearchTo);
        } catch (Exception e) {
            e.printStackTrace();
        }
		return j;
    }
	
    
    @RequestMapping(value = "exportLineLossSingleReport")
	@ResponseBody
	public void exportLineLossSingleReport(HttpServletRequest request, HttpServletResponse response) {
	    
	    try {
	    	List<LineLossReport> lineLossReports = getLineLossSingleReport(request);
	    	ExcelDataFormatter edf = new ExcelDataFormatter();
	        //要日期格式化使用以下方式
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("date", "MM/dd/yyyy HH:mm:ss");
	        edf.set("date", tvs);
			ExcelUtils.writeToFile(lineLossReports, edf, "datalineLossSingleList.xlsx", response);
		} catch (Exception e) {
			e.printStackTrace();
		}
    }
    
    
    /**
	 * @Title: getLineLossSingleReport
	 * @Description: 获取导出、打印对象列表数据
	 * @param request
	 * @return
	 * @return List<LineLossReport>
	 * @throws
	 */
	private List<LineLossReport> getLineLossSingleReport(
			HttpServletRequest request) {
		// TODO Auto-generated method stub
//		String lineLossObject = (String) request.getParameter("lineLossObject");
	    String lineLossObjectType = (String) request.getParameter("lineLossObjectType");
	    String timeType = (String) request.getParameter("timeType");
	    String startTime = request.getParameter("startTime");
	    String endTime = (String) request.getParameter("endTime");
	    String timePattern = request.getParameter("timePattern");
	    String orgId= request.getParameter("orgId");
	    String entity= request.getParameter("entity");
	   
	 // 参数赋值
	    JqGridSearchTo jqGridSearchTo = new JqGridSearchTo();
	    setOrgCode(jqGridSearchTo, orgId);
//    	jqGridSearchTo.put("lineLossObject", lineLossObject);
    	jqGridSearchTo.put("lineLossObjectType", lineLossObjectType);
    	jqGridSearchTo.put("timeType", timeType);
    	jqGridSearchTo.put("startTime", startTime);
    	jqGridSearchTo.put("endTime", endTime);
    	jqGridSearchTo.put("timePattern", timePattern);
    	jqGridSearchTo.put("entity", entity);
    	// 用于mysql 时间转换
    	String datePattern = convertDatePattern(timePattern);
    	jqGridSearchTo.put("datePattern", datePattern);
    	
    	List<LineLossReport> lineLossReports = dictReportService.findLineLossSingleReport(jqGridSearchTo);
    	if(null == lineLossReports || lineLossReports.isEmpty()){
    		lineLossReports = new ArrayList<LineLossReport>();
    		lineLossReports.add(new LineLossReport());
    	}
    	
		return lineLossReports;
	}
    
    
	@RequestMapping(value = "printLineLossSingleReport")
	@ResponseBody
	public void printLineLossSingleReport(HttpServletRequest request, HttpServletResponse response) {
	    try {
	    	List<LineLossReport> lineLossReports = getLineLossSingleReport(request);
	    	ExcelDataFormatter edf = new ExcelDataFormatter();
	        //要日期格式化使用以下方式
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("date", "MM/dd/yyyy HH:mm:ss");
	        edf.set("date", tvs);
	        CreatePdf.printPdf(lineLossReports, edf, request, response);
		} catch (Exception e) {
			e.printStackTrace();
		}
    }
    
    @RequestMapping(value = "exportLineLossOrgReport")
	@ResponseBody
	public void exportLineLossOrgReport(HttpServletRequest request, HttpServletResponse response) {
	    try {
	    	List<LineLossReport> lineLossReports = getLineLossOrgReport(request);
	    	ExcelDataFormatter edf = new ExcelDataFormatter();
	        //要日期格式化使用以下方式
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("date", "MM/dd/yyyy HH:mm:ss");
	        edf.set("date", tvs);
			ExcelUtils.writeToFile(lineLossReports, edf, "dataLineLossOrgReportList.xlsx", response);
		} catch (Exception e) {
			e.printStackTrace();
		}
    }
    
    
    @RequestMapping(value = "exportBillingOrgReport")
	@ResponseBody
	public void exportBillingOrgReport(HttpServletRequest request, HttpServletResponse response) {
	    try {
	    	List<BillingReport> billingReports = getBillingOrgReport(request);
	    	ExcelDataFormatter edf = new ExcelDataFormatter();
	        //要日期格式化使用以下方式
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("date", "MM/dd/yyyy HH:mm:ss");
	        edf.set("date", tvs);
			ExcelUtils.writeToFile(billingReports, edf, "dataBillingOrgReportList.xlsx", response);
		} catch (Exception e) {
			e.printStackTrace();
		}
    }
    
    
    @RequestMapping(value = "printBillingSingleReport")
	@ResponseBody
	public void printBillingSingleReport(HttpServletRequest request, HttpServletResponse response) {
	    try {
	    	List<BillingReport> billingReports = getBillingOrgReport(request);
	    	ExcelDataFormatter edf = new ExcelDataFormatter();
	        //要日期格式化使用以下方式
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("date", "MM/dd/yyyy HH:mm:ss");
	        edf.set("date", tvs);
	        CreatePdf.printPdf(billingReports, edf, request, response);
		} catch (Exception e) {
			e.printStackTrace();
		}
    }
    
    /**
	 * @Title: getLineLossOrgReport
	 * @Description: 导出、打印线损报表(orgId)
	 * @param request
	 * @return
	 * @return List<LineLossReport>
	 * @throws
	 */
	private List<LineLossReport> getLineLossOrgReport(HttpServletRequest request) {
		// TODO Auto-generated method stub
		String orgId = (String) request.getParameter("orgId");
	    String lineLossObjectType = (String) request.getParameter("lineLossObjectType");
	    String timeType = (String) request.getParameter("timeType");
	    String startTime = request.getParameter("startTime");
	    String endTime = (String) request.getParameter("endTime");
	    String timePattern = request.getParameter("timePattern");
	    String entity =request.getParameter("entity");
	 // 参数赋值
	    JqGridSearchTo jqGridSearchTo = new JqGridSearchTo();
    	jqGridSearchTo.put("orgId", orgId);
    	jqGridSearchTo.put("lineLossObjectType", lineLossObjectType);
    	jqGridSearchTo.put("timeType", timeType);
    	jqGridSearchTo.put("startTime", startTime);
    	jqGridSearchTo.put("endTime", endTime);
    	jqGridSearchTo.put("timePattern", timePattern);
    	// 用于mysql 时间转换
    	String datePattern = convertDatePattern(timePattern);
    	jqGridSearchTo.put("datePattern", datePattern);
    	jqGridSearchTo.put("entity", entity);
    	
    	List<LineLossReport> lineLossReports = dictReportService.findLineLossOrgReport(jqGridSearchTo);
    	if(null == lineLossReports || lineLossReports.isEmpty()){
    		lineLossReports = new ArrayList<LineLossReport>();
    		lineLossReports.add(new LineLossReport());
    	}
    	
		return lineLossReports;
	}
	
	private List<BillingReport> getBillingOrgReport(HttpServletRequest request) {
		// TODO Auto-generated method stub
		String orgId = (String) request.getParameter("orgId");
	    String sn = (String) request.getParameter("sn");
	    String timeType = (String) request.getParameter("timeType");
	    String startTime = request.getParameter("startTime");
	    String endTime = (String) request.getParameter("endTime");
	    String timePattern = request.getParameter("timePattern");
	    
	 // 参数赋值
	    JqGridSearchTo jqGridSearchTo = new JqGridSearchTo();
    	jqGridSearchTo.put("orgId", orgId);
    	jqGridSearchTo.put("customerName", sn);
    	jqGridSearchTo.put("timeType", timeType);
    	jqGridSearchTo.put("startTime", startTime);
    	jqGridSearchTo.put("endTime", endTime);
    	jqGridSearchTo.put("timePattern", timePattern);
    	//设置orgCode
    	setOrgCode(jqGridSearchTo, orgId);
    	// 用于mysql 时间转换
    	String datePattern = convertDatePattern(timePattern);
    	jqGridSearchTo.put("datePattern", datePattern);
    	List<BillingReport> billingReports = new ArrayList();
    	billingReports = dictReportService.findBillingOrgReport(jqGridSearchTo);
    	if(null == billingReports || billingReports.isEmpty()){
    		billingReports = new ArrayList<BillingReport>();
    		billingReports.add(new BillingReport());
    	}
    	
		return billingReports;
	}

	@RequestMapping(value = "printLineLossOrgReport")
	@ResponseBody
	public void printLineLossOrgReport(HttpServletRequest request, HttpServletResponse response) {
	    try {
	    	List<LineLossReport> lineLossReports = getLineLossOrgReport(request);
	    	ExcelDataFormatter edf = new ExcelDataFormatter();
	        //要日期格式化使用以下方式
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("date", "MM/dd/yyyy HH:mm:ss");
	        edf.set("date", tvs);
	        CreatePdf.printPdf(lineLossReports, edf, request, response);
		} catch (Exception e) {
			e.printStackTrace();
		}
    }
	
	/**
	 * line loss single detail详情
	 * @param request
	 * @param model
	 * @return				
	 */
	@RequestMapping(value  = "lineLossObjectDetails")
    public ModelAndView lineLossObjectDetails(String timeFlag,String timeType,String calDate,String snName,HttpServletRequest request, Model model) {
		model.addAttribute("timeFlag", timeFlag);
		model.addAttribute("timeType", timeType);
		model.addAttribute("calDate", calDate);
		model.addAttribute("snName", snName);
		
        return new ModelAndView("/report/jasper/lineLossObjDetails");
    }
	
	
	/**
	 * line Loss Object
	 * @param jqGridSearchTo
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "dtDetailsdatagrid")
    @ResponseBody
    public JqGridResponseTo dtDetailsdatagrid(String timePattern,String timeType,String calDate,String snName,String calcObjId,JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {

		TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        Map<String,Object> m=jqGridSearchTo.getMap();
        m.put("timePattern", timePattern);
        m.put("timeType",timeType);
        m.put("calDate",calDate);
        m.put("snName",snName);
        m.put("calcObjId",calcObjId);
        JqGridResponseTo j=null;
        try {
             j=dictReportService.findLineLossDetailReport(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
	
	
	@RequestMapping(value = "getExcelLineLossDetails")
	@ResponseBody
	public void getExcelLineLossDetails(HttpServletRequest request, HttpServletResponse response) {
	    
	    try {
	    	List<LineLossDetailReport> lineLossDetailReports = getLineLossDetailReport(request);
	    	ExcelDataFormatter edf = new ExcelDataFormatter();
	        //要日期格式化使用以下方式
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("date", "MM/dd/yyyy HH:mm:ss");
	        edf.set("date", tvs);
			ExcelUtils.writeToFile(lineLossDetailReports, edf, "datalineLossDetailList.xlsx", response);
		} catch (Exception e) {
			e.printStackTrace();
		}
    }
	
	@RequestMapping(value = "getPrintLineLossDetails")
	@ResponseBody
	public void getPrintLineLossDetails(HttpServletRequest request, HttpServletResponse response) {
	    
	    try {
	    	List<LineLossDetailReport> lineLossDetailReports = getLineLossDetailReport(request);
	    	ExcelDataFormatter edf = new ExcelDataFormatter();
	        //要日期格式化使用以下方式
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("date", "MM/dd/yyyy HH:mm:ss");
	        edf.set("date", tvs);
	        CreatePdf.printPdf(lineLossDetailReports, edf, request, response);
		} catch (Exception e) {
			e.printStackTrace();
		}
    }
    
	/**
	 * @Title: getLineLossDetailReport
	 * @Description: 获取导出、打印对象列表数据
	 * @param request
	 * @return
	 * @return List<LineLossReport>
	 * @throws
	 */
	private List<LineLossDetailReport> getLineLossDetailReport(HttpServletRequest request) {
		// TODO Auto-generated method stub
		String timePattern = (String) request.getParameter("timePattern");
	    String timeType = (String) request.getParameter("timeType");
	    String calDate = (String) request.getParameter("calDate");
	    String snName = request.getParameter("snName");
	    String calcObjId = request.getParameter("calcObjId");

	    
	 // 参数赋值
	    JqGridSearchTo jqGridSearchTo = new JqGridSearchTo();
    	jqGridSearchTo.put("timePattern", timePattern);
    	jqGridSearchTo.put("timeType", timeType);
    	jqGridSearchTo.put("calDate", calDate);
    	jqGridSearchTo.put("snName", snName);
    	jqGridSearchTo.put("calcObjId", calcObjId);

    	
    	List<LineLossDetailReport> lineLossDetailReports = dictReportService.findLineLossDetailExcel(jqGridSearchTo);
    	if(null == lineLossDetailReports || lineLossDetailReports.isEmpty()){
    		lineLossDetailReports = new ArrayList<LineLossDetailReport>();
    		lineLossDetailReports.add(new LineLossDetailReport());
    	}else {
    		//把IN 换成Input，把OUT 换成Output
    		for(LineLossDetailReport report:lineLossDetailReports) {
    			String type=report.getType();
    			if("IN".equals(type)) {
    				type="Input";
    			}else if("OUT".equals(type)) {
    				type="Output";
    			}
    			report.setType(type);
        	}
    	}
		return lineLossDetailReports;
	}

//	新增 Import & Export Report 报表操作 baijun 2018-10-15
	@RequestMapping(value  = "importAndExportReport")
    public ModelAndView importAndExportReport(HttpServletRequest request, Model model) {
		String orgId = (String) request
				.getAttribute(Globals.REQUESST_DATA_AUTH_ORG_ID);
		SimpleDateFormat sdf = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.formatter"));
	    Calendar lastDate = Calendar.getInstance();
	    lastDate.add(Calendar.DATE, -1);	//昨天
	    model.addAttribute("lineLossStartTime", sdf.format(lastDate.getTime()));
	    lastDate.add(Calendar.DATE, 1);
	    model.addAttribute("lineLossExpiryTime", sdf.format(lastDate.getTime()));
        return new ModelAndView("/report/jasper/importAndExportReport");
    }
	
//	新增 Import & Export Report 报表操作 baijun 2018-10-15
	@RequestMapping(value  = "importAndExportReport1")
    public ModelAndView importAndExportReport1(HttpServletRequest request, Model model) {
		String orgId = (String) request
				.getAttribute(Globals.REQUESST_DATA_AUTH_ORG_ID);
		SimpleDateFormat sdf = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.formatter"));
	    Calendar lastDate = Calendar.getInstance();
	    lastDate.add(Calendar.DATE, -1);	//昨天
	    model.addAttribute("lineLossStartTime", sdf.format(lastDate.getTime()));
	    lastDate.add(Calendar.DATE, 1);
	    model.addAttribute("lineLossExpiryTime", sdf.format(lastDate.getTime()));
        return new ModelAndView("/report/jasper/importAndExportReport1");
    }
	
	public  void setOrgCode(JqGridSearchTo jqGridSearchTo,String orgId) {
		if(StringUtils.isEmpty(orgId)) {
			SysUser su = TokenManager.getToken();
			orgId=su.getOrgId();
		}
    	SysOrg sysOrg= sysOrgService.getEntity(orgId);
    	if(sysOrg!=null) {
    		jqGridSearchTo.put("orgCode", sysOrg.getOrgCode());
    	}
	}
	
	@RequestMapping(value  = "salesStatisticsReport")
    public ModelAndView salesStatisticsReport(HttpServletRequest request, Model model) {
		List<DictCustomerType> dictCustomerTypes=assetCustomerService.getDictCustomerType();
		String customerTypeReplace=RoletoJson.listToReplaceStr(dictCustomerTypes, "id", "name", ",");
		List<DictCustomerIndustry> dictCustomerIndustrys=assetCustomerService.getDictCustomerIndustry();
		String customerIndustryReplace=RoletoJson.listToReplaceStr(dictCustomerIndustrys, "id", "name", ",");
		model.addAttribute("customerIndustryReplace",customerIndustryReplace);
		model.addAttribute("customerTypeReplace",customerTypeReplace);
		SimpleDateFormat sdf = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.formatter"));
	    Calendar lastDate = Calendar.getInstance();
	    lastDate.add(Calendar.DATE, -7);	//昨天
	    model.addAttribute("startTime", sdf.format(lastDate.getTime()));
	    lastDate.add(Calendar.DATE, 7);
	    model.addAttribute("endTime", sdf.format(lastDate.getTime()));
        return new ModelAndView("/report/jasper/salesStatisticsReport");
    }
	
	@RequestMapping(value  = "unknowDeviceManageReport")
    public ModelAndView unknowDeviceManageReport(HttpServletRequest request, Model model,String deviceType) {
        return new ModelAndView("/report/jasper/unknowDeviceManageReport");
    }
	
	/**
	 * dataComminicationStatus查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "unknowDeviceManageReportDatagrid")
    @ResponseBody
    public JqGridResponseTo unknowDeviceManageReportDatagrid(String mac,String startDate,String endDate,
    		JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
		TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j = null;
        
        try {
		    jqGridSearchTo.put("mac", mac);
		    try {
	    		if(StringUtil.isNotEmpty(startDate)){
	    			jqGridSearchTo.put("startDate", DateUtils.parseDate(startDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
	    		}
				
				if(StringUtil.isNotEmpty(endDate)){
					jqGridSearchTo.put("endDate", DateUtils.parseDate(endDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
	        	}
			} catch (ParseException e) {
				e.printStackTrace();
			}
			j = dataDeviceLoginLogService.getForJqGrid(jqGridSearchTo);
        } catch (Exception ex) {
        	ex.printStackTrace();
        }
        return j;
	}
	
	/**
	  * 导出
	  * @param deviceId
	  * @param date
	  * @param dataIntegrityDetails
	  * @param request
	  * @param response
	  */
	 @RequestMapping(value = "getExportUnknowDevice")
	 @ResponseBody
	 public void getExportUnknowDevice(HttpServletRequest request, HttpServletResponse response) {
		String mac=(String) request.getParameter("mac");
        String startDate=(String) request.getParameter("startDate");
        String endDate=(String) request.getParameter("endDate");
        
        List<DataDeviceLoginLog> list = new ArrayList<>();
        
        DataDeviceLoginLog entity = new DataDeviceLoginLog();
        entity.setMac(mac);
        
        try {
	        JqGridSearchTo jqGridSearchTo = new JqGridSearchTo();  
	        jqGridSearchTo.put("mac",mac);
	        
	        if(StringUtil.isNotEmpty(startDate)){
	        	entity.getExtData().put("startDate", DateUtils.parseDate(startDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
			}
			
			if(StringUtil.isNotEmpty(endDate)){
				entity.getExtData().put("endDate", DateUtils.parseDate(endDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
	    	}
			
			list = dataDeviceLoginLogService.getList(entity);

			ExcelDataFormatter edf = new ExcelDataFormatter();
	        //要日期格式化使用以下方式
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("date", "MM/dd/yyyy HH:mm:ss");
	        edf.set("date", tvs);
			ExcelUtils.writeToFile(list, edf, "Unknow_Device_List.xlsx", response);
        } catch (Exception ex){
        	ex.printStackTrace();
        }
	 }
	
	/**
	 * 跳转到Vee report 列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "veeReport")
    public ModelAndView veeReport(HttpServletRequest request, Model model) {
		DictProfile dictProfile = new DictProfile();
		dictProfile.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
		dictProfile.setProfileType("1");
		List<DictProfile> list = dictProfileService.getList(dictProfile);
		String ftrs = RoletoJson.listToReplaceStr(list, "id", "name");
		model.addAttribute("dictProfileReplace", ftrs);
		
		List<DictVeeEvent> veeList=dictVeeEventService.getAllList();
		String veeEventReplace = RoletoJson.listToReplaceStr(veeList, "id", "name");
		
		
		String channelIds="";
		String channelNames="";
		DictDataitemGroup entity=new DictDataitemGroup();
		entity.setAppType("1");
		entity.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
		List<DictDataitemGroup> ddgList = dictDataitemGroupService.getList(entity);
		if(ddgList.size()>0){
			DictDataitem dd=new DictDataitem();
			Map<String, Object> qm = new HashMap<String, Object>();
			qm.put("groupId", ddgList.get(0).getId());
			model.addAttribute("groupId", ddgList.get(0).getId());
			qm.put("appType", "1");
			dd.setExtData(qm);
		//	dd.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
			channelIds+=ddgList.get(0).getId();
			List<DictDataitem> ddList=dictDataitemService.getList(dd);
			for(DictDataitem d:ddList){
				channelIds+=","+d.getId();
				if(StringUtil.isNotEmpty(channelNames)){
					channelNames+="&"+d.getName();
				}else{
					channelNames+=d.getName();
				}
			}
		}
		
		
		//设置开始结束时间
		SimpleDateFormat sdf = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.formatter"));
	    SimpleDateFormat sdf1 = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));

	    String date =sdf.format(new Date());
	    model.addAttribute("startDate", date+" 00:00:00");
	    String endDate=sdf1.format(new Date());
	    model.addAttribute("endDate", endDate);
			    
		model.addAttribute("channelNames", channelNames);
		model.addAttribute("veeEventReplace", veeEventReplace);
		model.addAttribute("classReplace", "1:veeList.class1,2:veeList.class2,3:veeList.class3,4:veeList.class4,5:veeList.class5");
		
        return new ModelAndView("/report/veeReport");
    }



	/**
	 * 跳转到自定义报表列表页面
	 *
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "designer")
	public ModelAndView designer(HttpServletRequest request, Model model) {
		//设置开始结束时间

		Calendar cale = Calendar.getInstance();
		cale = Calendar.getInstance();
		cale.add(Calendar.MONTH, 0);
		cale.set(Calendar.DAY_OF_MONTH, 1);

		Calendar cale1 = Calendar.getInstance();
		cale1 = Calendar.getInstance();
		cale1.add(Calendar.MONTH, -1);
		cale1.set(Calendar.DAY_OF_MONTH, 1);

		SimpleDateFormat sdf = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.formatter"));
		Date startDate=DateUtils.getDateAdd(new Date(), Calendar.DATE, -1);
		Date endDate=DateUtils.getDateAdd(new Date(), Calendar.DATE, 0);
		model.addAttribute("billingStartTimeDaily", sdf.format(startDate));
		model.addAttribute("billingExpiryTimeDaily", sdf.format(endDate));

		model.addAttribute("billingStartTimeMonthly", sdfMonthly.format(cale1.getTime()));
		model.addAttribute("billingExpiryTimeMonthly", sdfMonthly.format(cale.getTime()));

		String  tvTypeReplace = "1:Daily,2:Monthly";
		model.addAttribute("reportTypeReplace", tvTypeReplace);
		String  isEnableReplace = "0:Disabled,1:Enabled";
		model.addAttribute("isEnableReplace", isEnableReplace);


		return new ModelAndView("/report/reportDesigner");
	}
	
	
	@RequestMapping(value  = "defineReport")
	public ModelAndView defineReport(AssetMeterGroup assetMeterGroup,HttpServletRequest request, Model model) {
		model.addAttribute("protocolId", ResourceUtil.getSessionattachmenttitle("protocol.id"));
		return new ModelAndView("/report/addDefineReport");
	}
	
	@RequestMapping(value  = "searchCalcObj")
	public ModelAndView searchCalcObj(String objId,String timeType,String entityType,HttpServletRequest request, Model model) {
		model.addAttribute("timeType", timeType);
		model.addAttribute("entityType", entityType);
		model.addAttribute("objId", objId);
		return new ModelAndView("/asset/searchCalcObj");
	}
	
	public Date getMonthDate(Date date,int i) {
		Calendar c = Calendar.getInstance();  
		c.setTime(date);
		c.add(Calendar.MONTH, i);  
		return c.getTime();
	}
	
	public Date getYearDate(Date date,int i) {
		Calendar c = Calendar.getInstance();  
		c.setTime(date);
		c.add(Calendar.YEAR, i);  
		return c.getTime();
	}
	
	
	public Date getDayDate(Date date,int i) {
		Calendar c = Calendar.getInstance();  
		c.setTime(date);
		c.add(Calendar.DATE, i);  
		return c.getTime();
	}
	 
}