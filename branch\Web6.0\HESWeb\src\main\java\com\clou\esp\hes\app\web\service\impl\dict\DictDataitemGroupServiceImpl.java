/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictDataitemGroup{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.dict;

import java.util.List;
import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.dao.dict.DictDataitemGroupDao;
import com.clou.esp.hes.app.web.model.dict.DictDataitemGroup;
import com.clou.esp.hes.app.web.service.dict.DictDataitemGroupService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dictDataitemGroupService")
public class DictDataitemGroupServiceImpl  extends CommonServiceImpl<DictDataitemGroup>  implements DictDataitemGroupService {

	@Resource
	private DictDataitemGroupDao dictDataitemGroupDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dictDataitemGroupDao);
    }
	public DictDataitemGroupServiceImpl() {}
	
	@Override
	public List<DictDataitemGroup> getMeterDataEventExportList(DictDataitemGroup temp) {
		return dictDataitemGroupDao.getMeterDataEventExportList(temp);
	}
	
	@Override
	public String getMeterDataEventType(
			DictDataitemGroup temp) {
		return dictDataitemGroupDao.getMeterDataEventType(temp);
	}
	
	@Override
	public List<DictDataitemGroup> getList(DictDataitemGroup entity){
		return this.dictDataitemGroupDao.getList(entity);
	}
}