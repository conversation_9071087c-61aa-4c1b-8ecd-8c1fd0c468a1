package clouesp.hes.core.uci.soap.custom.asset_refresh;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import javax.xml.ws.Service;

/**
 * This class was generated by Apache CXF 3.1.14
 * 2018-04-08T09:52:33.947+08:00
 * Generated source version: 3.1.14
 * 
 */
@WebServiceClient(name = "AssetRefreshPort", 
                  wsdlLocation = "http://10.8.165.49:8080/UCI-1.0-SNAPSHOT/services/AssetRefresh?wsdl",
                  targetNamespace = "http://asset_refresh.custom.soap.uci.core.hes.clouesp/") 
public class AssetRefreshPort_Service extends Service {

    public final static URL WSDL_LOCATION;

    public final static QName SERVICE = new QName("http://asset_refresh.custom.soap.uci.core.hes.clouesp/", "AssetRefreshPort");
    public final static QName AssetRefreshPortPort = new QName("http://asset_refresh.custom.soap.uci.core.hes.clouesp/", "AssetRefreshPortPort");
    static {
        URL url = null;
        try {
            url = new URL("http://10.8.165.49:8080/UCI-1.0-SNAPSHOT/services/AssetRefresh?wsdl");
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(AssetRefreshPort_Service.class.getName())
                .log(java.util.logging.Level.INFO, 
                     "Can not initialize the default wsdl from {0}", "http://10.8.165.49:8080/UCI-1.0-SNAPSHOT/services/AssetRefresh?wsdl");
        }
        WSDL_LOCATION = url;
    }

    public AssetRefreshPort_Service(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public AssetRefreshPort_Service(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public AssetRefreshPort_Service() {
        super(WSDL_LOCATION, SERVICE);
    }
    
    public AssetRefreshPort_Service(WebServiceFeature ... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }

    public AssetRefreshPort_Service(URL wsdlLocation, WebServiceFeature ... features) {
        super(wsdlLocation, SERVICE, features);
    }

    public AssetRefreshPort_Service(URL wsdlLocation, QName serviceName, WebServiceFeature ... features) {
        super(wsdlLocation, serviceName, features);
    }    




    /**
     *
     * @return
     *     returns AssetRefreshPort
     */
    @WebEndpoint(name = "AssetRefreshPortPort")
    public AssetRefreshPort getAssetRefreshPortPort() {
        return super.getPort(AssetRefreshPortPort, AssetRefreshPort.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns AssetRefreshPort
     */
    @WebEndpoint(name = "AssetRefreshPortPort")
    public AssetRefreshPort getAssetRefreshPortPort(WebServiceFeature... features) {
        return super.getPort(AssetRefreshPortPort, AssetRefreshPort.class, features);
    }

}
