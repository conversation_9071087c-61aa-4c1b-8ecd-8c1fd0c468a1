/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictVeeEventDataitem{ } 
 * 
 * 摘    要： dictVeeEventDataitem
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2019-03-04 09:38:47
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.vee;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DictVeeEventDataitem  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DictVeeEventDataitem() {
	}

	/**dataitemId*/
	private java.lang.String dataitemId;
	/**dataitemName*/
	private java.lang.String dataitemName;
	/**dataitemKey*/
	private java.lang.String dataitemKey;
	/**descr*/
	private java.lang.String descr;
	/**cycleCount*/
	private java.math.BigDecimal cycleCount;
	/**cycleType*/
	private java.lang.String cycleType;

	
	
	
	public java.lang.String getDataitemName() {
		return dataitemName;
	}

	public void setDataitemName(java.lang.String dataitemName) {
		this.dataitemName = dataitemName;
	}

	/**
	 * dataitemId
	 * @return the value of DICT_VEE_EVENT_DATAITEM.DATAITEM_ID
	 * @mbggenerated 2019-03-04 09:38:47
	 */
	public java.lang.String getDataitemId() {
		return dataitemId;
	}

	/**
	 * dataitemId
	 * @param dataitemId the value for DICT_VEE_EVENT_DATAITEM.DATAITEM_ID
	 * @mbggenerated 2019-03-04 09:38:47
	 */
    	public void setDataitemId(java.lang.String dataitemId) {
		this.dataitemId = dataitemId;
	}
	/**
	 * dataitemKey
	 * @return the value of DICT_VEE_EVENT_DATAITEM.DATAITEM_KEY
	 * @mbggenerated 2019-03-04 09:38:47
	 */
	public java.lang.String getDataitemKey() {
		return dataitemKey;
	}

	/**
	 * dataitemKey
	 * @param dataitemKey the value for DICT_VEE_EVENT_DATAITEM.DATAITEM_KEY
	 * @mbggenerated 2019-03-04 09:38:47
	 */
    	public void setDataitemKey(java.lang.String dataitemKey) {
		this.dataitemKey = dataitemKey;
	}
	/**
	 * descr
	 * @return the value of DICT_VEE_EVENT_DATAITEM.DESCR
	 * @mbggenerated 2019-03-04 09:38:47
	 */
	public java.lang.String getDescr() {
		return descr;
	}

	/**
	 * descr
	 * @param descr the value for DICT_VEE_EVENT_DATAITEM.DESCR
	 * @mbggenerated 2019-03-04 09:38:47
	 */
    	public void setDescr(java.lang.String descr) {
		this.descr = descr;
	}
	/**
	 * cycleCount
	 * @return the value of DICT_VEE_EVENT_DATAITEM.CYCLE_COUNT
	 * @mbggenerated 2019-03-04 09:38:47
	 */
	public java.math.BigDecimal getCycleCount() {
		return cycleCount;
	}

	/**
	 * cycleCount
	 * @param cycleCount the value for DICT_VEE_EVENT_DATAITEM.CYCLE_COUNT
	 * @mbggenerated 2019-03-04 09:38:47
	 */
    	public void setCycleCount(java.math.BigDecimal cycleCount) {
		this.cycleCount = cycleCount;
	}
	/**
	 * cycleType
	 * @return the value of DICT_VEE_EVENT_DATAITEM.CYCLE_TYPE
	 * @mbggenerated 2019-03-04 09:38:47
	 */
	public java.lang.String getCycleType() {
		return cycleType;
	}

	/**
	 * cycleType
	 * @param cycleType the value for DICT_VEE_EVENT_DATAITEM.CYCLE_TYPE
	 * @mbggenerated 2019-03-04 09:38:47
	 */
    	public void setCycleType(java.lang.String cycleType) {
		this.cycleType = cycleType;
	}

	public DictVeeEventDataitem(java.lang.String dataitemId 
	,java.lang.String dataitemKey 
	,java.lang.String descr 
	,java.math.BigDecimal cycleCount 
	,java.lang.String cycleType ) {
		super();
		this.dataitemId = dataitemId;
		this.dataitemKey = dataitemKey;
		this.descr = descr;
		this.cycleCount = cycleCount;
		this.cycleType = cycleType;
	}

}