$(document).ready(function() {
	$.jgrid.defaults.styleUI = "Bootstrap";
	var mydata = [{
		id: "1",
		invdate: "11/15/2017",
		name: "test",
		note: "note",
		amount: "300.00",
		tax: "10.00",
		total: "2111.00"
	}, {
		id: "2",
		invdate: "11/15/2017",
		name: "test2",
		note: "note2",
		amount: "300.00",
		tax: "20.00",
		total: "320.00"
	}, {
		id: "3",
		invdate: "11/15/2017",
		name: "test3",
		note: "note3",
		amount: "300.00",
		tax: "30.00",
		total: "430.00"
	}, {
		id: "4",
		invdate: "11/15/2017",
		name: "test",
		note: "note",
		amount: "300.00",
		tax: "10.00",
		total: "210.00"
	}, {
		id: "5",
		invdate: "11/15/2017",
		name: "test2",
		note: "note2",
		amount: "300.00",
		tax: "20.00",
		total: "320.00"
	}, {
		id: "6",
		invdate: "11/15/2017",
		name: "test3",
		note: "note3",
		amount: "300.00",
		tax: "30.00",
		total: "430.00"
	}, {
		id: "7",
		invdate: "11/15/2017",
		name: "test",
		note: "note",
		amount: "300.00",
		tax: "10.00",
		total: "210.00"
	}, {
		id: "8",
		invdate: "11/15/2017",
		name: "test2",
		note: "note2",
		amount: "300.00",
		tax: "21.00",
		total: "320.00"
	}, {
		id: "9",
		invdate: "11/15/2017",
		name: "test3",
		note: "note3",
		amount: "400.00",
		tax: "30.00",
		total: "430.00"
	}, {
		id: "10",
		invdate: "11/15/2017",
		name: "test3",
		note: "note2",
		amount: "500.00",
		tax: "30.00",
		total: "430.00"
	}];
	$("#table_list_2").jqGrid({
		data: mydata,
		datatype: "local",
		autowidth: true,
		gridview: true,
		height: "auto",
		rowNum: 10,
	
		/*direction:"rtl",*/
		rownumbers: true,
		altRows: true, //单双行样式不同  
		altclass: 'differ',
		rowList: [10, 20, 30,40,50],
		colNames: [  "","Serial Number", "Start Time", "<input name=\"\" type=\"checkbox\" class=\"tou_class\" value=\"\" />kWh+", "<input  type=\"checkbox\" class=\"tou_class\"  value=\"\" />kWh-", "<input  type=\"checkbox\" class=\"tou_class\"  value=\"\" />kVarh+", "<input  type=\"checkbox\" class=\"tou_class\"  value=\"\" />Va", "<input  type=\"checkbox\" class=\"tou_class\"  value=\"\" />Ic"],
		colModel: [{
			name: "id",
			index: "id",
			editable: true,
			width: 60,
			frozen: true,
			search: false,
			hidden: true,
			sorttype: "int"

		}, {
			name: "invdate",
			index: "invdate",
			editable: true,
			sortable:true,
			width: 220,
			sorttype: "date",
			stype: "date",
			formatter: "date"
		}, {
			name: "name",
			index: "name",
			editable: true,
			sortable:true,
			width: 220
		}, {
			name: "amount",
			index: "amount",
			editable: true,
			sortable:true,
			width: 220,
			/*align: "right",*/

			sorttype: "float",
			formatter: "number"
		}, {
			name: "tax",
			index: "tax",
			editable: true,
			sortable:true,
			width: 220,
			/*align: "right",*/
			sorttype: "float"
		}, {
			name: "total",
			index: "total",
			editable: true,
			sortable:true,
			width: 190,
			/*align: "right",*/
			sorttype: "float",
			stype: "select"
		}, {
			name: "note",
			index: "note",
			editable: true,
			sortable:true,
			width: 190,
			search: false
		}, {
			name: "act",
			index: "act",
			editable: false,
			sortable:true,
			search: false,
			width: 120
		}],
		pager: "#pager_list_2",
		viewrecords: true,
		caption: ' ',
		gridComplete:touCanCel,
		//multiselect: true,
		/*editurl: "/RowEditing",*/
		shrinkToFit: false,
		autoScroll: true
	});

	/**
	 * 取消头部选择框事件冒泡
	 */
	function touCanCel(){
		$(".tou_class").click(function(event){
		    event.stopPropagation();//阻止事件冒泡即可
		});	
	}
	
	function alarmFormatter(cellvalue, options, rowdata) {
		return '<a href="javascript:;" title="edit" ;<i class=" fa fa-pencil " style="margin:2px"></i> </a><a href="javascript:;" title="detail" ;<i class="fa fa-book" style="margin:2px"></i>  </a><a href="javascript:;" title="delete" ;<i class="fa fa-trash-o"style="margin:2px"></i>  </a>';
	}
	
	
	$("#table_list_2").jqGrid("navGrid", "#pager_list_2", {
			edit: false,
			add: false,
			del: false,
			search: false
		},

		{
			height: 200,
			reloadAfterSubmit: true
		});

	$(window).bind("resize", function() {
		var width = $(".jqGrid_wrapper").width();
		
		$("#table_list_2").setGridWidth(width)
	})
});