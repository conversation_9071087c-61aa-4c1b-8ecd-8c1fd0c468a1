/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataFwuPlan{ } 
 * 
 * 摘    要： dataFwuPlan
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-12-29 08:37:48
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.data;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import ch.iec.tc57._2011.enddevicecontrols.RequestEndDeviceControlsPort;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl.EndDeviceControlType;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl.EndDevices;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl.EndDevices.Names;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl.EndDevices.Names.NameType;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControls;
import ch.iec.tc57._2011.enddevicecontrolsmessage.EndDeviceControlsPayloadType;
import ch.iec.tc57._2011.enddevicecontrolsmessage.EndDeviceControlsRequestMessageType;
import ch.iec.tc57._2011.enddevicecontrolsmessage.EndDeviceControlsResponseMessageType;
import ch.iec.tc57._2011.schema.message.HeaderType;
import ch.iec.tc57._2011.schema.message.UserType;
import clouesp.hes.core.uci.soap.custom.file_upload.CxfFileWrapper;
import clouesp.hes.core.uci.soap.custom.file_upload.FirmwareImageUploadPort;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.CreatePdf;
import com.clou.esp.hes.app.web.core.util.ExcelUtils;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.OrganizationUtils;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.core.util.UciInterfaceUtil;
import com.clou.esp.hes.app.web.core.util.XMLUtil;
import com.clou.esp.hes.app.web.model.asset.AssetCommunicator;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.data.DataFwuJob;
import com.clou.esp.hes.app.web.model.data.DataFwuPlan;
import com.clou.esp.hes.app.web.model.demo.ProgressEntity;
import com.clou.esp.hes.app.web.model.dict.DictDeviceModel;
import com.clou.esp.hes.app.web.model.dict.DictManufacturer;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.asset.AssetCommunicatorService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.data.DataFwuJobService;
import com.clou.esp.hes.app.web.service.data.DataFwuPlanService;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.dict.DictDeviceModelService;
import com.clou.esp.hes.app.web.service.dict.DictManufacturerService;
import com.clou.esp.hes.app.web.service.system.SysOrgService;
import com.clou.esp.hes.app.web.service.system.SysServiceAttributeService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.clou.esp.hes.app.web.validation.ValidGroup2;
import com.power7000g.core.excel.ExcelDataFormatter;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.format.FormatUtil;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;
import com.power7000g.core.util.redis.JedisUtils;

/**
 * <AUTHOR>
 * @时间：2017-12-29 08:37:48
 * @描述：dataFwuPlan类
 */
@Controller
@RequestMapping("/dataFwuPlanController")
public class DataFwuPlanController extends BaseController {
	
	final static String TIME_FLAG =ResourceUtil.getSessionattachmenttitle("local.time.flag");
	
	@Resource
	private DataFwuPlanService dataFwuPlanService;
	@Resource
	private DataFwuJobService dataFwuJobService;
	@Resource
	private DictManufacturerService dictManufacturerService;
	@Resource
	private DictDeviceModelService dictDeviceModelService;
	@Resource
	private AssetMeterService assetMeterService;
	@Resource
	private AssetCommunicatorService assetCommunicatorService;
	 
	@Resource
    private DataUserLogService dataUserLogService;
	@Resource
    private SysOrgService sysOrgService;
	
	@Resource
    private SysServiceAttributeService sysServiceAttributeService;
	
	private static SimpleDateFormat sdf = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
	private static SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	/**
	 * 打印固件升级计划下的job任务列表
	 * 
	 * @param type
	 * @param planId
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@RequestMapping(value = "exportOrPrintPlanReportJobList")
	@ResponseBody
	public void exportOrPrintPlanReportJobList(String type, String planId,String jobState,HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		if (StringUtil.isEmpty(type)) {
			return;
		}
		if (StringUtil.isEmpty(planId)) {
			return;
		}
		DataFwuPlan dfp = dataFwuPlanService.getEntity(planId);
		if (dfp == null) {
			return;
		}
		DataFwuJob dfj = new DataFwuJob();   
		dfj.setPlanId(planId);
		dfj.setState(jobState);
		List<DataFwuJob> list = dataFwuJobService.getList(dfj);
		ExcelDataFormatter edf = new ExcelDataFormatter();
		// 1:Runing,2:Done,3:Cancel,4:Waiting,5:Expired
		Map<String, String> trt = new HashMap<String, String>();
		trt.put("1", "Runing");
		trt.put("2", "Done");
		trt.put("3", "Cancel");
		trt.put("4", "Waiting");
		trt.put("5", "Expired");
		edf.set("state", trt);
		
		Map<String, String> deviceTypeMap = new HashMap<String, String>();
		deviceTypeMap.put("1", "Meter");
		deviceTypeMap.put("2", "Communicator");
		deviceTypeMap.put("3", "GPRS Module of Meter");
		deviceTypeMap.put("4", "Communication Module of Communicatior");
		deviceTypeMap.put("5", "(Broadcast) Meter");
		deviceTypeMap.put("6", "(Broadcast) Communication Module of Meter");
		deviceTypeMap.put("7", "(Broadcast) Communicatior");
		deviceTypeMap.put("8", "PLC Module of DCU");
		deviceTypeMap.put("9", "(Broadcast) PLC Module of Communicatior");
		deviceTypeMap.put("10", "(Broadcast) PLC Module of Meter");
		edf.set("deviceType", deviceTypeMap);   
		
		// 要日期格式化使用以下方式
		Map<String, String> tvs = new HashMap<String, String>();
//		tvs.put("lastExecTime", "MM/dd/yyyy HH:mm:ss");
		tvs.put("lastExecTime", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
		edf.set("lastExecTime", tvs);
		if (type.equals("export")) {// export
			ExcelUtils.writeToFile(list, edf, System.currentTimeMillis()
					+ ".xlsx", response, ValidGroup2.class);
		} else {
			CreatePdf.printPdf(list, edf, ValidGroup2.class, request, response);
		}
	}

	/**
	 * 导出或者打印固件升级计划
	 * 
	 * @param type
	 * @param dfp
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@RequestMapping(value = "exportOrPrintPlanReportList")
	@ResponseBody
	public void exportOrPrintPlanReportList(String type, DataFwuPlan dfp,
			HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		if (StringUtil.isEmpty(type)) {
			return;
		}
		List<DataFwuPlan> list = dataFwuPlanService.getList(dfp);
		ExcelDataFormatter edf = new ExcelDataFormatter();
		List<DictManufacturer> mList = dictManufacturerService.getAllList();
		Map<String, String> dmf = new HashMap<String, String>();
		if (mList != null) {
			for (DictManufacturer m : mList) {
				dmf.put(m.getId(), m.getName());
			}
		}
		edf.set("manufacturerId", dmf);
		// 1:Runing,2:Done,3:Cancel,4:Waiting
		Map<String, String> trt = new HashMap<String, String>();
		trt.put("1", "Runing");
		trt.put("2", "Done");
		trt.put("3", "Cancel");
		trt.put("4", "Waiting");
		edf.set("state", trt);
		// 要日期格式化使用以下方式
		Map<String, String> tvs = new HashMap<String, String>();
		tvs.put("startTime", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
		tvs.put("expiryTime", ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
		edf.set("startTime", tvs);
		edf.set("expiryTime", tvs);
		if (type.equals("export")) {// export
			ExcelUtils.writeToFile(list, edf, System.currentTimeMillis()
					+ ".xlsx", response, ValidGroup1.class);
		} else {
			CreatePdf.printPdf(list, edf, ValidGroup1.class, request, response);
		}
	}

	/**
	 * 打印升级的电表
	 * 
	 * @param deviceType
	 * @param manufacturer
	 * @param deviceModel
	 * @param currentVesion
	 * @param sn
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "printFMUPlanMeterList0")
	@ResponseBody
	public void printFMUPlanMeterList0(String planId, HttpServletRequest request, HttpServletResponse response) {
		if (StringUtil.isEmpty(planId)) {
			return;
		}
		DataFwuPlan dfp = dataFwuPlanService.getEntity(planId);
		if (dfp == null) {
			return;
		}
		SimpleDateFormat datetimeFormat = new SimpleDateFormat(
				ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
		JqGridSearchTo j = new JqGridSearchTo();
		j.getMap().put("planId", planId);
		j.getMap().put("deviceType", dfp.getDeviceType());
		j.getMap().put("startTime", DateUtils.date2Str(dfp.getStartTime(), datetimeFormat));
		j.getMap().put("expiryTime", DateUtils.date2Str(dfp.getExpiryTime(), datetimeFormat));
		List<DataFwuJob> list = dataFwuJobService.getListNoPage(j);
		CreatePdf.printPdf(list, null, ValidGroup1.class, request, response);
	}

	/**
	 * 导出待升级的电表
	 * 
	 * @param m
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@RequestMapping(value = "exportFMUPlanMeterList0")
	@ResponseBody
	public void exportFMUPlanMeterList0(String planId, String manufacturer, String deviceModel, 
			String currentVesion, String sn, HttpServletRequest request, HttpServletResponse response)throws Exception {
		if (StringUtil.isEmpty(planId)) {
			return;
		}
		DataFwuPlan dfp = dataFwuPlanService.getEntity(planId);
		if (dfp == null) {
			return;
		}
		SimpleDateFormat datetimeFormat = new SimpleDateFormat(
				ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
		JqGridSearchTo j = new JqGridSearchTo();
		j.getMap().put("planId", planId);
		j.getMap().put("deviceType", dfp.getDeviceType());
		j.getMap().put("startTime", DateUtils.date2Str(dfp.getStartTime(), datetimeFormat));
		j.getMap().put("expiryTime", DateUtils.date2Str(dfp.getExpiryTime(), datetimeFormat));
		List<DataFwuJob> list = dataFwuJobService.getListNoPage(j);
		ExcelDataFormatter edf = new ExcelDataFormatter();
		ExcelUtils.writeToFile(list, edf, System.currentTimeMillis() + ".xlsx",
				response, ValidGroup1.class);
	}

	/**
	 * 打印待升级的电表
	 * 
	 * @param deviceType
	 * @param manufacturer
	 * @param deviceModel
	 * @param currentVesion
	 * @param sn
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "printFMUPlanMeterList")
	@ResponseBody
	public void printFMUPlanMeterList(String deviceType, String manufacturer, String deviceModel,
			String currentVesion, String sn, HttpServletRequest request, HttpServletResponse response) {
		if (StringUtil.isEmpty(deviceType) || StringUtil.isEmpty(manufacturer)
				|| StringUtil.isEmpty(deviceModel)) {
			return;
		}
		List<AssetMeter> mList = null;
		List<AssetCommunicator> cList = null;
		if (deviceType.equals("1")||deviceType.equals("3")||deviceType.equals("5")||deviceType.equals("6")||deviceType.equals("10")) {// Meter
			AssetMeter m = new AssetMeter();
			m.setManufacturer(manufacturer);
			m.setModel(deviceModel);
			if (StringUtil.isNotEmpty(currentVesion)) {
				m.setCurrentVesionArray(currentVesion.split(","));
			}
			m.setSn(sn);
			mList = assetMeterService.getList(m);
			if (mList == null || mList.size() == 0) {
				AssetMeter temMeter = new AssetMeter();
				temMeter.setSn("");
				temMeter.setManufacturerName("");
				temMeter.setModelName("");
				temMeter.setFwVersion("");
				mList.add(temMeter);
			}
		} else {// Comm
			AssetCommunicator c = new AssetCommunicator();
			c.setManufacturer(manufacturer);
			c.setModel(deviceModel);
			if (StringUtil.isNotEmpty(currentVesion)) {
				c.setCurrentVesionArray(currentVesion.split(","));
			}
			c.setSn(sn);
			cList = assetCommunicatorService.getList(c);
			if (cList == null || cList.size() == 0) {
				AssetCommunicator temComm = new AssetCommunicator();
				temComm.setSn("");
				temComm.setManufacturerName("");
				temComm.setModelName("");
				temComm.setFwVersion("");
				cList.add(temComm);
			}
		}
		if (deviceType.equals("1")||deviceType.equals("3")||deviceType.equals("5")||deviceType.equals("6")||deviceType.equals("10")) {// Meter
			CreatePdf.printPdf(mList, null, ValidGroup1.class, request, response);
		} else {
			CreatePdf.printPdf(cList, null, ValidGroup1.class, request, response);
		}
	}

	/**
	 * 导出待升级的电表
	 * 
	 * @param deviceType
	 * @param manufacturer
	 * @param deviceModel
	 * @param currentVesion
	 * @param sn
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "exportFMUPlanMeterList")
	@ResponseBody
	public void exportFMUPlanMeterList(String deviceType, String manufacturer, String deviceModel,
			String currentVesion, String sn, HttpServletRequest request, HttpServletResponse response) {
		AjaxJson j = new AjaxJson();
		try {
			if (StringUtil.isEmpty(deviceType) || StringUtil.isEmpty(manufacturer) || StringUtil.isEmpty(deviceModel)) {
				return;
			}
			if (deviceType.equals("1")||deviceType.equals("3")||deviceType.equals("5")||deviceType.equals("6")||deviceType.equals("10")) {// Meter
				AssetMeter m = new AssetMeter();
				m.setManufacturer(manufacturer);
				m.setModel(deviceModel);
				if (StringUtil.isNotEmpty(currentVesion)) {
					m.setCurrentVesionArray(currentVesion.split(","));
				}
				m.setSn(sn);
				List<AssetMeter> list = assetMeterService.getList(m);
				ExcelDataFormatter edf = new ExcelDataFormatter();
				ExcelUtils.writeToFile(list, edf, "FMUPlanMeter.xlsx", response, ValidGroup1.class);
			} else {// Comm
				AssetCommunicator c = new AssetCommunicator();
				c.setManufacturer(manufacturer);
				c.setModel(deviceModel);
				if (StringUtil.isNotEmpty(currentVesion)) {
					c.setCurrentVesionArray(currentVesion.split(","));
				}
				c.setSn(sn);
				List<AssetCommunicator> list = assetCommunicatorService.getList(c);
				ExcelDataFormatter edf = new ExcelDataFormatter();
				ExcelUtils.writeToFile(list, edf, System.currentTimeMillis() + ".xlsx", response, ValidGroup1.class);
			}
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
		}
	}

	/**
	 * 上传固件
	 * @param request
	 * @param file
	 * @return
	 */
	@RequestMapping(value = "upload", method = RequestMethod.POST)
	// @ResponseBody
	public void upload(HttpServletRequest request,
			HttpServletResponse response, @RequestParam("lefile") MultipartFile file) {
		AjaxJson j = new AjaxJson();
		String relPathOfSavedDir = "\\upload\\fwu\\";
		SysUser su = TokenManager.getToken();
		try {
			File savedDir = prepareSavedDir(request, relPathOfSavedDir);
			String savedFileName = getSavedFileName(file.getOriginalFilename());
			//复制文件到指定目录下（上传文件）
			File newFile = new File(savedDir, savedFileName);
			file.transferTo(newFile);
			String url = relPathOfSavedDir + savedFileName;
			j.setObj(url.replaceAll("\\\\", "/"));
			
			//上传UCI接口的文件信息存入(文件和文件扩展名)
			JedisUtils.setObject(su.getId() + "file", newFile, 0);
			if(savedFileName.indexOf(".") > 0){
				String fileName = file.getOriginalFilename();
				String fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
				JedisUtils.setObject(su.getId() + "fileExtension", fileExtension, 0);
			}
		} catch (Exception e) {
			e.printStackTrace();
			j.setErrorMsg(e.getMessage());
			j.setSuccess(false);
			try {
				//返回参数到前端界面
				response.getWriter().write(j.toString());
				response.getWriter().flush();
			} catch (IOException e1) {
				e1.printStackTrace();
			}
		}
		response.setContentType("text/html");
		response.setHeader("Cache-Control", "no-store");
		try {
			//返回参数到前端界面
			response.getWriter().write(j.toString());
			response.getWriter().flush();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				response.getWriter().close();
			} catch (Exception e2) {
				e2.printStackTrace();
			}
		}
		// return j;
	}

	private File prepareSavedDir(HttpServletRequest request, String relativePath)
			throws Exception {
		@SuppressWarnings("deprecation")
		String url = request.getRealPath("/") + relativePath;
		File dir = new File(url);
		if (!dir.exists()) {
			dir.mkdirs();
		}
		return dir;
	}

	private String getSavedFileName(String origFileName) {
		return RandomStringUtils.randomNumeric(8) + "."
				+ FilenameUtils.getExtension(origFileName);
	}
	/**
	 * 获取上传文件进度数据
	 * @Description 
	 * @param model
	 * @return String
	 * <AUTHOR> 
	 * @Time 2018年3月12日 下午5:42:42
	 */
	@RequestMapping(value = "getProgress")  
    @ResponseBody  
    public AjaxJson initCreateInfo(HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
        ProgressEntity status = (ProgressEntity) request.getSession().getAttribute("upload_ps");
        try {
        	if(status == null){
        		j.setSuccess(false);
        	}else{
        		float tmp = (float)status.getpBytesRead();  
        		float percentage = tmp/status.getpContentLength() * 100;
        		BigDecimal result = new BigDecimal(percentage).setScale(0, BigDecimal.ROUND_HALF_UP);
        		status.setPercentage(result.intValue());
        		System.out.println("upload_ps is --->" + result.intValue());
        		j.setObj(status);
        		//清除session缓存
        		if(result.intValue() == 100){
        			request.getSession().removeAttribute("upload_ps");
        		}
        	}
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
		}
        return j;
    }
	
	/**
	 * 清除session缓存
	 * @Description 
	 * @param model
	 * @return String
	 * <AUTHOR> 
	 * @Time 2018年3月12日 下午5:42:42
	 */
	@RequestMapping(value = "removeProgressSession")  
    @ResponseBody  
    public AjaxJson removeProgressSession(HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
        try {
        	request.getSession().removeAttribute("upload_ps");
		} catch (Exception e) {
			e.printStackTrace();
		}
        return j;
    }

	/**
	 * 保存生成固件升级计划
	 * 
	 * @param dfp
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "saveFWUPlan")
	@ResponseBody
	public AjaxJson saveFWUPlan(DataFwuPlan dfp, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		SysUser su = TokenManager.getToken();
		dfp.setOperationId(su.getId());
		dfp.setState("0");// 是否取消；0=未取消；1=已取消；
		try {
			j = dataFwuPlanService.saveFWUPlan(dfp, sysServiceAttributeService);
			//添加操作日志
            dataUserLogService.insertDataUserLog(su.getId(), 
            		"Firmware Upgrade", "Add Plan", "Add Plan (Plan Description=" + dfp.getIntroduction() + ")");
			this.uploadFirmwareFile_uci(dfp.getId());
		} catch (Exception e) {
			e.printStackTrace();
			j.setErrorMsg("Invoking exception of job service, please check and create plan again!");
		}
		return j;
	}

	/**
	 * 获取电表和集中器下当前版本集合
	 * 
	 * @param jqGridSearchTo
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getFMUPlanDeviceList")
	@ResponseBody
	public JqGridResponseTo getFMUPlanDeviceList(JqGridSearchTo jqGridSearchTo,
			HttpServletRequest request) {
		TagUtil.setFieldValue(jqGridSearchTo, request); // 取传递的参数到JqGridSearchTo
		JqGridResponseTo j = null;
		String isLoadData = request.getParameter("isLoadData");
		if (isLoadData != null && isLoadData.equals("false")) {
			return j;
		}
		try {
			j = dataFwuJobService.getForJqGrid(jqGridSearchTo);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return j;
	}

	/**
	 * 获取电表和集中器下当前版本集合
	 * @param jqGridSearchTo
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getFMUPlanMeterList")
	@ResponseBody
	public JqGridResponseTo getFMUPlanMeterList(JqGridSearchTo jqGridSearchTo,
			HttpServletRequest request) {
		TagUtil.setFieldValue(jqGridSearchTo, request); // 取传递的参数到JqGridSearchTo
		JqGridResponseTo j = null;
		String isLoadData = request.getParameter("isLoadData");
		if (isLoadData != null && isLoadData.equals("false")) {
			return j;
		}
		String currentVesion = (String) jqGridSearchTo.getMap().get("currentVesion");
		String deviceType = (String) jqGridSearchTo.getMap().get("deviceType");
		String sn = (String) jqGridSearchTo.getMap().get("sn");
		// 传入的sn条件的设备类型（1:meter;2:DCU）
		String condSnDeviceType = (String) jqGridSearchTo.getMap().get("condSnDeviceType");
		jqGridSearchTo.put("sn", sn);
//		jqGridSearchTo.put("fwu_sn", sn);
		if (StringUtil.isNotEmpty(currentVesion)) {
			String currentVesions = "";
			String currentVesionArray[] = currentVesion.split(",");
			if (currentVesionArray != null && currentVesionArray.length > 0) {
				for (String cv : currentVesionArray) {
					currentVesions += ("'" + cv + "',");
				}
			}
			if (StringUtil.isNotEmpty(currentVesions)) {
				currentVesions = currentVesions.substring(0,
						currentVesions.length() - 1);
			}
			jqGridSearchTo.getMap().put("currentVesion", null);
			jqGridSearchTo.getMap().put("fwVersions", currentVesions);
		}
		try {
			if (StringUtil.isNotEmpty(deviceType) && deviceType.equals("1")) {
				// 表计sn
				if ("1".equals(condSnDeviceType)) {
					j = assetMeterService.getForJqGrid(jqGridSearchTo);
				} else { // DCU的sn
					// 查询DCU的id
					AssetCommunicator acEntity = new AssetCommunicator();
					acEntity.setSn(sn);
					AssetCommunicator ac = assetCommunicatorService.get(acEntity);
					jqGridSearchTo.put("sn", null);
					jqGridSearchTo.put("communicatorId", ac.getId());
					
					j = assetMeterService.getForJqGrid(jqGridSearchTo);
				}
			} else {
				jqGridSearchTo.getMap().put("deviceType", null);
				j = assetCommunicatorService.getForJqGrid(jqGridSearchTo);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return j;
	}

	/**
	 * 根据厂商编号获取设备型号(使用redis作为缓存工具)---不使用
	 * @param deviceType
	 * @param request
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "getDeviceModel_redis")
	@ResponseBody
	public AjaxJson getDeviceModel_redis(String manufacturerId, String deviceTypeId,
			HttpServletRequest request) {
		/*JedisUtils.delObject("model");
		JedisUtils.delObject("version");*/
		//首先获取model和version的值，方便判断是否需要重新缓存数据到Redis
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		String dateStr = sdf.format(new Date());
		Map<String, Object> modelMapJudge = (Map<String, Object>) JedisUtils.getObject(dateStr + "model");
		Map<String, Object> versionMapJudge = (Map<String, Object>) JedisUtils.getObject(dateStr + "version");
		//获取所有厂商
		List<DictManufacturer> mList = dictManufacturerService.getAllList();
		List<DictDeviceModel> allModel = new ArrayList<>();
		//获取所有的model，缓存入Redis
		if(modelMapJudge == null || modelMapJudge.size() < 1){
			Map<String, Object> modelMap = new HashMap<String, Object>();
			for (int i = 0; i < mList.size(); i++) {
				DictDeviceModel deviceModelMap = new DictDeviceModel();
				deviceModelMap.setManufacturerId(mList.get(i).getId());
				List<DictDeviceModel> modelListMap = dictDeviceModelService.getList(deviceModelMap);
				for (int j = 0; j < modelListMap.size(); j++) {
					allModel.add(modelListMap.get(j));
				}
				modelMap.put(mList.get(i).getId(), modelListMap);
			}
			JedisUtils.setObject(dateStr + "model", modelMap, 3600*12);
		}
		//获取所有model的版本信息，缓存入Redis
		if(versionMapJudge == null || versionMapJudge.size() < 1){
			Map<String, Object> versionMap = new HashMap<String, Object>();
			for (int i = 0; i < allModel.size(); i++) {
				List<AssetMeter> listVersionMeter = assetMeterService.getFwVersionGroupByModel(allModel.get(i).getId());
				versionMap.put("1" + allModel.get(i).getId(), listVersionMeter);
				List<AssetCommunicator> listVersionCommunicator = assetCommunicatorService.getFwVersionGroupByModel(allModel.get(i).getId());
				versionMap.put("2" + allModel.get(i).getId(), listVersionCommunicator);
			}
			JedisUtils.setObject(dateStr + "version", versionMap, 3600*12);
		}
		
		AjaxJson j = new AjaxJson();
		Map<String, Object> attributes = new HashMap<>();
		if (StringUtil.isEmpty(manufacturerId)) {
			return j;
		}
//		DictDeviceModel ddm = new DictDeviceModel();
//		ddm.setManufacturerId(manufacturerId);
//		List<DictDeviceModel> list = dictDeviceModelService.getList(ddm);
		Map<String, Object> modelMap = (Map<String, Object>) JedisUtils.getObject(dateStr + "model");
		//判断是meter(1)/communicator(2)
		if(deviceTypeId != null && deviceTypeId.equals("1")){
			List<DictDeviceModel> modelListMap = (List<DictDeviceModel>) modelMap.get(manufacturerId);
			List<DictDeviceModel> modelListMap_t = new ArrayList<>();
			for (int i = 0; i < modelListMap.size(); i++) {
				DictDeviceModel temple = modelListMap.get(i);
				//判断是否存在电表的协议范围内，存在则放入新的list中
				if(Integer.parseInt(temple.getProtocolId()) >= 100 && Integer.parseInt(temple.getProtocolId()) <= 199){
					modelListMap_t.add(temple);
				}
			}
			j.setObj(modelListMap_t);
		}else{
			List<DictDeviceModel> modelListMap1 = (List<DictDeviceModel>) modelMap.get(manufacturerId);
			List<DictDeviceModel> modelListMap_t1 = new ArrayList<>();
			for (int i = 0; i < modelListMap1.size(); i++) {
				DictDeviceModel temple1 = modelListMap1.get(i);
				//判断是否存在集中器的协议范围内，不存在则移除
				if(Integer.parseInt(temple1.getProtocolId()) >= 200 && Integer.parseInt(temple1.getProtocolId()) <= 299){
					modelListMap_t1.add(temple1);
				}
			}
			j.setObj(modelListMap_t1);
		}
		//获取展示的model，然后取第一项获取版本信息
		List<DictDeviceModel> modelListMap = (List<DictDeviceModel>) j.getObj();
		//获取第一个model的当前版本展示
		Map<String, Object> versionMap = (Map<String, Object>) JedisUtils.getObject(dateStr + "version");
		if (deviceTypeId != null && deviceTypeId.equals("1")) {
			List<AssetMeter> listVersionMeter = (List<AssetMeter>) versionMap.get("1" + modelListMap.get(0).getId());
			attributes.put("listVersion", listVersionMeter);
		} else {
			List<AssetCommunicator> listVersionCommunicator = (List<AssetCommunicator>) versionMap.get("2" + modelListMap.get(0).getId());
			attributes.put("listVersion", listVersionCommunicator);
		}
		j.setAttributes(attributes);
		return j;
	}
	
	/**
	 * 根据设备种类获取设备当前版本(使用redis作为缓存工具)---不使用
	 * @Description 
	 * @param deviceTypeId, modelTypeId
	 * @param request
	 * @return AjaxJson
	 * <AUTHOR> 
	 * @Time 2018年2月22日 下午3:18:08
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "getFwVersionGroupByModel_redis")
	@ResponseBody
	public AjaxJson getDeviceModelFwVersion_redis(String deviceTypeId, String modelTypeId,
			HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		String dateStr = sdf.format(new Date());
		Map<String, Object> versionMap = (Map<String, Object>) JedisUtils.getObject(dateStr + "version");
		if (deviceTypeId != null && deviceTypeId.equals("1")) {
			List<AssetMeter> listVersionMeter = (List<AssetMeter>) versionMap.get("1" + modelTypeId);
			j.setObj(listVersionMeter);
		} else {
			List<AssetCommunicator> listVersionCommunicator = (List<AssetCommunicator>) versionMap.get("2" + modelTypeId);
			j.setObj(listVersionCommunicator);
		}
		return j;
	}
	
	/**
	 * 根据设备种类获取设备当前版本
	 * @param deviceType
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getDeviceFwVersion")
	@ResponseBody
	public AjaxJson getDeviceFwVersion(String deviceType,
			HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		if (deviceType != null && (deviceType.equals("1")||deviceType.equals("3")||deviceType.equals("5")||deviceType.equals("6")||deviceType.equals("10"))) {
			List<AssetMeter> list = assetMeterService.getListGroupByFwVersion();
			j.setObj(list);
		} else {
			List<AssetCommunicator> list = assetCommunicatorService.getListGroupByFwVersion();
			j.setObj(list);
		}
		return j;
	}
	
	/**
	 * 根据厂商编号获取设备型号
	 * @param deviceType
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getDeviceModel")
	@ResponseBody
	public AjaxJson getDeviceModel(String manufacturerId, String deviceTypeId,
			HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		Map<String, Object> attributes = new HashMap<>();
		//获取选定厂商的下属model
		DictDeviceModel deviceModel = new DictDeviceModel();
		deviceModel.setDeviceType(deviceTypeId);		//1:meter 2:communicator
		deviceModel.setManufacturerId(manufacturerId);
		List<DictDeviceModel> modelList = dictDeviceModelService.getList(deviceModel);
		j.setObj(modelList);
		if (StringUtil.isEmpty(manufacturerId)||StringUtil.isEmpty(deviceTypeId)) {
			return j;
		}
		//获取第一个model的当前版本展示
		if (deviceTypeId != null && (deviceTypeId.equals("1")||deviceTypeId.equals("3")||deviceTypeId.equals("5")||deviceTypeId.equals("6")||deviceTypeId.equals("10"))) {
			List<AssetMeter> listVersionMeter = assetMeterService.getFwVersionGroupByModel(modelList.get(0).getId());
			attributes.put("listVersion", listVersionMeter);
		} else {
			if (CollectionUtils.isNotEmpty(modelList)) {
				List<AssetCommunicator> listVersionCommunicator = assetCommunicatorService.getFwVersionGroupByModel(modelList.get(0).getId());
				attributes.put("listVersion", listVersionCommunicator);
			} else {
				attributes.put("listVersion", new ArrayList<AssetCommunicator>());
			}
		}
		j.setAttributes(attributes);
		return j;
	}
	
	/**
	 * 根据search获取集中器
	 * @param deviceType
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getCommunicator")
	@ResponseBody
	public AjaxJson getCommunicator(String search,
			HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
	
		Map<String, Object> params = new HashMap<String, Object>();
		SysUser su = TokenManager.getToken();
		List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
		params.put("orgIdList", orgIdList);
		params.put("snName", search);
		List<AssetCommunicator> communicatorList = assetCommunicatorService.getListLimitTwenty(params);
		j.setObj(communicatorList);
		
		return j;
	}
	
	/**
	 * 根据search获取meter
	 * @param deviceType
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getMeter")
	@ResponseBody
	public AjaxJson getMeter(String search,
			HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
	
		Map<String, Object> params = new HashMap<String, Object>();
		SysUser su = TokenManager.getToken();
		List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
		params.put("orgIdList", orgIdList);
		params.put("snName", search);
		if(!StringUtils.isEmpty(request.getParameter("comType")) && "GPRS".equals(request.getParameter("comType"))){
			params.put("comType", "GPRS");
		}else if(!StringUtils.isEmpty(request.getParameter("comType")) && "NORMAL".equals(request.getParameter("comType"))){
			params.put("comType", "NORMAL");
		}
			
		List<AssetMeter> meterList = assetMeterService.getListLimitTwenty(params);
		
		j.setObj(meterList);
		
		return j;
	}
	
	/**
     * 获取默认的设备类型列表
     * @param dictManufacturer
     * @param request
     * @return
     */
    @RequestMapping(value = "getDeviceModelList")
    @ResponseBody
    public AjaxJson getDeviceModelList(String manufacturerId, String deviceTypeId, HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        try {
        	//获取选定厂商的下属model
    		DictDeviceModel deviceModel = new DictDeviceModel();
    		deviceModel.setDeviceType(deviceTypeId);		//1:meter 2:communicator
    		deviceModel.setManufacturerId(manufacturerId);
    		List<DictDeviceModel> modelList = dictDeviceModelService.getList(deviceModel);
    		j.setObj(modelList);
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg("系统异常");
        }
        return j;
    }
	
	/**
	 * 根据设备种类获取设备当前版本
	 * @Description 
	 * @param deviceTypeId, modelTypeId
	 * @param request
	 * @return AjaxJson
	 * <AUTHOR> 
	 * @Time 2018年2月22日 下午3:18:08
	 */
	@RequestMapping(value = "getFwVersionGroupByModel")
	@ResponseBody
	public AjaxJson getDeviceModelFwVersion(String deviceTypeId, String modelTypeId,
			HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		//获取第一个model的当前版本展示
		if (deviceTypeId != null && (deviceTypeId.equals("1")||deviceTypeId.equals("3")||deviceTypeId.equals("5")||deviceTypeId.equals("6")||deviceTypeId.equals("10"))) {
			List<AssetMeter> listVersionMeter = assetMeterService.getFwVersionGroupByModel(modelTypeId);
			j.setObj(listVersionMeter);
		} else {
			List<AssetCommunicator> listVersionCommunicator = assetCommunicatorService.getFwVersionGroupByModel(modelTypeId);
			j.setObj(listVersionCommunicator);
		}
		return j;
	}

	/**
	 * 获取job的当前和目标版本
	 * 
	 * @param deviceType
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getFWUJobVersion")
	@ResponseBody
	public AjaxJson getFWUJobVersion(String versionType,
			HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		DataFwuJob dfj = new DataFwuJob();
		List<DataFwuJob> dfjList = null;
		if (versionType != null && versionType.equals("1")) {// currentVersion
			dfj.setCurrentVesion("");
			dfjList = dataFwuJobService.getListGroupByField(dfj);
		} else {// newVersion
			dfj.setNewVersion("");
			dfjList = dataFwuJobService.getListGroupByField(dfj);
		}
		j.setObj(dfjList);
		return j;
	}

	/**
	 * 跳转到dataFwuPlan列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "list")
	public ModelAndView list(HttpServletRequest request, Model model) {
		// 工厂
		List<DictManufacturer> mList = dictManufacturerService.getAllList();
		request.setAttribute("mList", mList);
		//预加载第一个工厂的model
		DictDeviceModel deviceModel = new DictDeviceModel();
		deviceModel.setManufacturerId(mList.get(0).getId());
		deviceModel.setDeviceType("1");		//1:meter 2:communicator	
		List<DictDeviceModel> modelList = dictDeviceModelService.getList(deviceModel);
		request.setAttribute("modelList", modelList);
		request.setAttribute("dmfs", RoletoJson.listToReplaceStr(mList, "id", "name"));
		// Plan Start Time
		// Plan Expiry Time
		Date currentDate = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
		request.setAttribute("planStartTime", DateUtils.date2Str(currentDate, sdf));
		request.setAttribute("planExpiryTime", DateUtils.date2Str(FormatUtil.addDaysToDate(currentDate, 6), sdf));
		
		// broadcast Meter:
		SimpleDateFormat sdf2 = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.formatter"));
		request.setAttribute("broadcastMeterStartTime", DateUtils.date2Str(currentDate, sdf2));
		
		request.setAttribute("broadcastMeterExpiryTime", DateUtils.date2Str(FormatUtil.addMonthsToDate(currentDate, 1), sdf2)); 
		
		return new ModelAndView("/data/firmwareManagementUpgrade");
	}

	/**
	 * 跳转到dataFwuPlan新增界面
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "dataFwuPlan")
	public ModelAndView dataFwuPlan(DataFwuPlan dataFwuPlan,
			HttpServletRequest request, Model model) {
		if (StringUtil.isNotEmpty(dataFwuPlan.getId())) {
			try {
				dataFwuPlan = dataFwuPlanService.getEntity(dataFwuPlan.getId());
			} catch (Exception e) {
				e.printStackTrace();
			}
			model.addAttribute("dataFwuPlan", dataFwuPlan);
		}
		return new ModelAndView("/data/dataFwuPlan");
	}

	/**
	 * dataFwuPlan查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "datagrid")
	@ResponseBody
	public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,
			HttpServletRequest request) {
		TagUtil.setFieldValue(jqGridSearchTo, request); // 取传递的参数到JqGridSearchTo
		String modelName = (String) jqGridSearchTo.getMap().get("modelName");
		if (StringUtil.isNotEmpty(modelName)) {
			jqGridSearchTo.getMap().put("deviceModel", modelName);
		}
		jqGridSearchTo.getMap().put("currentDate", new Date());
		JqGridResponseTo j = null;
		try {
			jqGridSearchTo.setSord("desc");
			jqGridSearchTo.setSidx("startTime");
			//获取固件升级计划的list
			j = dataFwuPlanService.getForJqGrid(jqGridSearchTo);
			List<Map<String, Object>> list = (List<Map<String, Object>>) j.getRows();
			Date current = new Date();
			//当升级计划到期时间小于当前时间时，则计划失效，修改状态为1
			for (Map<String, Object> l : list) {
//				String startTime = (String) l.get("startTime");
//				if (current.before(DateUtils.str2Date(startTime, DateUtils.datetimeFormat)) || 
				String expiryTime = (String) l.get("expiryTime");
				if (current.after(DateUtils.str2Date(expiryTime, DateUtils.datetimeFormat))) {
					l.put("state", "1");
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return j;
	}

	/**
	 * 取消固件升级计划，取消dataFwuPlan信息
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "cancel")
	@ResponseBody
	public AjaxJson cancel(String id, HttpServletRequest req) {
		AjaxJson j = new AjaxJson();
		try {
			if (StringUtil.isEmpty(id)) {
				j.setErrorMsg("Please enter Plan ID!");
				return j;
			}
			DataFwuPlan plan = dataFwuPlanService.getEntity(id);
			if (plan == null) {
				j.setErrorMsg("No plan found!");
				return j;
			}
//			Date startTime = p.getStartTime();
			Date expiryTime = plan.getExpiryTime();
			Date current = new Date();
			String state = plan.getState();
			j.setErrorMsg("Cancel fail!");
//			if (state.equals("0") && !(current.before(startTime) || current.after(expiryTime))) {
			//取消条件：state == 0 /  当前时间在过期时间之前
			if (state.equals("0") && current.before(expiryTime)) {
				/*
				 * 更改plan的状态为已取消，然后再调用接口
				 */
				plan.setState("1");	//取消状态
				int update = dataFwuPlanService.update(plan);
				
				//取消plan
				this.dataFwuJobService.cancelByPlanId(id);
				
				System.out.println("修改固件升级计划状态结果==" + update);
				
//				RequestEndDeviceControlsPort port = (RequestEndDeviceControlsPort) SpringContextUtil.getBean("RequestEndDeviceControlsPort");
				RequestEndDeviceControlsPort port = (RequestEndDeviceControlsPort) UciInterfaceUtil
		        		.getInterface("RequestEndDeviceControlsPort", RequestEndDeviceControlsPort.class, sysServiceAttributeService);
				EndDeviceControlsRequestMessageType reqMsg = new EndDeviceControlsRequestMessageType();
				SysUser su = TokenManager.getToken();
				HeaderType header = new HeaderType();
				Date date = new Date();
				header.setVerb("cancel");
				header.setNoun("EndDeviceControls");
				header.setTimestamp(DateUtils.dateToXmlDate(date));
				header.setSource("ClouESP HES");
				header.setAsyncReplyFlag(false);
				header.setReplyAddress("");
				header.setAckRequired(true);
				UserType u = new UserType();
				u.setOrganization(su.getOrgId());
				u.setUserID(su.getId());
				header.setUser(u);
				header.setMessageID(DateUtils.formatDate("yyyyMMdd") + su.getId());
				header.setCorrelationID(DateUtils.formatDate("yyyyMMdd") + su.getId());
				reqMsg.setHeader(header);
				EndDeviceControlsPayloadType payload = new EndDeviceControlsPayloadType();
				EndDeviceControls endDeviceControls = new EndDeviceControls();
				List<EndDeviceControl> endDeviceControl = endDeviceControls.getEndDeviceControl();
				EndDeviceControl edc = new EndDeviceControl();
				edc.setMRID(id);// 放的固件升级计划主键ID
				edc.setReason("immediately");// immediately 立即开始， periodicity
				EndDeviceControlType edct = new EndDeviceControlType();
				edct.setRef("********");// ??这个是啥
				edc.setEndDeviceControlType(edct);
				List<EndDeviceControl.EndDevices> endDevices = edc.getEndDevices();
				EndDevices ed = new EndDevices();
				List<EndDeviceControl.EndDevices.Names> names = ed.getNames();
				Names name1 = new Names();
				name1.setName(DateUtils.date2Str(plan.getStartTime(), DateUtils.date_sdf));
				NameType nt1 = new NameType();
				nt1.setName("plan start time");
				name1.setNameType(nt1);
				Names name2 = new Names();
				name2.setName(DateUtils.date2Str(plan.getExpiryTime(), DateUtils.date_sdf));
				NameType nt2 = new NameType();
				nt2.setName("plan end time");
				name2.setNameType(nt2);
				Names name3 = new Names();
				name3.setName(DateUtils.date2Str(plan.getTaskStartTime(), DateUtils.HHmmss));
				NameType nt3 = new NameType();
				nt3.setName("job start time");
				name3.setNameType(nt3);
				Names name4 = new Names();
				name4.setName(DateUtils.date2Str(plan.getTaskEndTime(), DateUtils.HHmmss));
				NameType nt4 = new NameType();
				nt4.setName("job end time");
				name4.setNameType(nt4);
				Names name5 = new Names();
				name5.setName(plan.getTaskCycle().toString());
				NameType nt5 = new NameType();
				nt5.setName("task cycle");
				name5.setNameType(nt5);
				names.add(name1);
				names.add(name2);
				names.add(name3);
				names.add(name4);
				names.add(name5);
				endDevices.add(ed);
				endDeviceControl.add(edc);
				payload.setEndDeviceControls(endDeviceControls);
				reqMsg.setPayload(payload);
				System.out.println("XML格式==" + XMLUtil.convertToXml(reqMsg));
				EndDeviceControlsResponseMessageType resMsg = port.cancelEndDeviceControls(reqMsg);// result OK就是成功
				System.out.println("取消固件升级Plan返回结果===" + XMLUtil.convertToXml(resMsg));
				String result = resMsg.getReply().getResult();
				if (result != null && result.toUpperCase().equals("OK")) {
					//添加操作日志
                    dataUserLogService.insertDataUserLog(su.getId(), 
                    		"Firmware Upgrade", "Cancel Plan", "Cancel Plan (Plan Description=" + plan.getIntroduction() + ")");
					j.setSuccess(true);
					j.setMsg(MutiLangUtil.doMutiLang("system.cancelSucc"));
				} else {
					j.setErrorMsg(MutiLangUtil.doMutiLang("system.cancelFail"));
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			j.setErrorMsg("Invoking exception of job service, please check and create plan again!");
		}
		return j;
	}

	/**
	 * 保存dataFwuPlan信息
	 * 
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "save")
	@ResponseBody
	public AjaxJson save(
			@Validated(value = { ValidGroup1.class }) DataFwuPlan dataFwuPlan,
			BindingResult bindingResult, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		DataFwuPlan t = new DataFwuPlan();
		try {
			SysUser su = TokenManager.getToken();
			if (StringUtil.isNotEmpty(dataFwuPlan.getId())) {
				t = dataFwuPlanService.getEntity(dataFwuPlan.getId());
				MyBeanUtils.copyBeanNotNull2Bean(dataFwuPlan, t);
				dataFwuPlanService.update(t);
				j.setMsg("修改成功");

			} else {
				dataFwuPlanService.save(dataFwuPlan);
				j.setMsg("创建成功");

			}
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg("系统异常");
		}
		return j;
	}
	
	/**
	 * 获取升级计划生成job的保存进度
	 * 
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "getSaveFWUPlanProgress")
	@ResponseBody
	public AjaxJson getSaveFWUPlanProgress(String planDescription, String end, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
//			SysUser su = TokenManager.getToken();
			String progress = JedisUtils.get(planDescription + "fwuProgress");
			if("1".equals(end)){
				JedisUtils.del(planDescription + "fwuProgress");
			}
			if(progress == null || "".equals(progress)){
				j.setObj("1");
			}else{
				j.setObj(progress);
			}
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg("System error!");
		}
		return j;
	}
	
	/**
	 * 调用UCI上传固件接口，
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "uploadFirmwareFile_uci")
	@ResponseBody
	public boolean uploadFirmwareFile_uci(String planId) {
		SysUser su = TokenManager.getToken();
		CxfFileWrapper fileWrapper = new CxfFileWrapper();
		try {
//			FirmwareImageUploadPort port = (FirmwareImageUploadPort) SpringContextUtil.getBean("FirmwareImageUploadPort");
			FirmwareImageUploadPort port = (FirmwareImageUploadPort) UciInterfaceUtil
	        		.getInterface("FirmwareImageUploadPort", FirmwareImageUploadPort.class, sysServiceAttributeService);
			File file = (File) JedisUtils.getObject(su.getId() + "file");
			String fileExtension = (String) JedisUtils.getObject(su.getId() + "fileExtension");
			DataSource source = new FileDataSource(file);
			fileWrapper.setFile(new DataHandler(source));
			fileWrapper.setFileName(planId);
			fileWrapper.setFileExtension(fileExtension);
			System.out.println("Upload Firmware File==PlanId--->" + planId + ", fileExtension--->" + fileExtension);
			boolean returnMsg = port.upload(fileWrapper);// result OK就是成功
			System.out.println("Upload Firmware File to UCI Server return result is ===" + returnMsg);
			if (returnMsg) {
				return returnMsg;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}
	
	/**
	 * 跳转到add plan页面
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "toAddNewPlan")
    public ModelAndView list(DataFwuPlan plan, HttpServletRequest request, Model model,String jobState) {
		DataFwuPlan planTemp = new DataFwuPlan();
		if(StringUtil.isNotEmpty(plan.getId())){
			planTemp = dataFwuPlanService.getEntity(plan.getId());
		}
		
	    Calendar lastDate = Calendar.getInstance();
	    model.addAttribute("planStartTime", sdf.format(lastDate.getTime()));
	    lastDate.add(Calendar.DATE, 7);	//日期延后7天
	    model.addAttribute("planExpiryTime", sdf.format(lastDate.getTime()));
	    model.addAttribute("plan", planTemp);
	    model.addAttribute("jobState", jobState);
        return new ModelAndView("/data/addNewPlanFwu");
    }
	
	
	/**
	 * 获取旧升级计划的未完成的job，生成新的plan和job
	 * @param dfp
	 * @param request
	 * @return
	 * @throws ParseException 
	 */
	@RequestMapping(value = "cretePlanAgain")
	@ResponseBody
	public AjaxJson cretePlanAgain(DataFwuPlan plan,String jobState, HttpServletRequest request) throws ParseException {
		AjaxJson j = new AjaxJson();
		SysUser su = TokenManager.getToken();
		//获取旧的升级计划
		DataFwuPlan oldPlan = dataFwuPlanService.getEntity(plan.getId());
		//获取该计划未完成job数量，如果为0，则计划不可创建
		DataFwuJob entity = new DataFwuJob();
		entity.setPlanId(plan.getId());
		//entity.setState("4"); waiting状态
		if(!"4".equals(jobState)&&!"5".equals(jobState)) {
			jobState="8";
		}
		entity.setState(jobState);
		int jobCount = dataFwuJobService.getCount(entity).intValue();
		if(jobCount == 0){
			j.setMsg(MutiLangUtil.doMutiLang("meterGroupUpgrade.jobUpgradeSucc"));
			j.setSuccess(false);
		}else{
			//新的升级计划
			DataFwuPlan newPlan = new DataFwuPlan();
			newPlan.setIntroduction(plan.getIntroduction());
			newPlan.setOperationId(su.getId());
			newPlan.setState("0");		// 是否取消；0=未取消；1=已取消
			newPlan.setStartTime(sdf.parse(plan.getTaskStartTimeStr()));
			newPlan.setExpiryTime(sdf.parse(plan.getTaskEndTimeStr()));
			newPlan.setTaskStartTime(oldPlan.getTaskStartTime());
			newPlan.setTaskEndTime(oldPlan.getTaskEndTime());
			newPlan.setTaskCycle(oldPlan.getTaskCycle());
			newPlan.setDeviceType(oldPlan.getDeviceType());
			newPlan.setDeviceModel(oldPlan.getDeviceModel());
			newPlan.setManufacturerId(oldPlan.getManufacturerId());
			newPlan.setFilePath(oldPlan.getFilePath());
			newPlan.setFirmwareFileName(oldPlan.getFirmwareFileName());
			newPlan.setFilePathServer(oldPlan.getFilePathServer());
			newPlan.setImageIdentifier(oldPlan.getImageIdentifier());
			newPlan.setNewVersion(oldPlan.getNewVersion());
			newPlan.setCurrentVesion(oldPlan.getCurrentVesion());
			newPlan.setJobState(jobState);
			try {
				j = dataFwuPlanService.cretePlanAgain(newPlan, oldPlan.getId(), sysServiceAttributeService);
				//添加操作日志
	            dataUserLogService.insertDataUserLog(su.getId(), "Firmware Upgrade", "Add Plan", "Add Plan (Plan Description=" + plan.getIntroduction() + ")");
//				this.uploadFirmwareFile_uci(newPlan.getId());
				} catch (Exception e) {
					e.printStackTrace();
					j.setErrorMsg(MutiLangUtil.doMutiLang("meterGroupUpgrade.exceptionMsg"));
				}
			}
			return j;
		}
	    
}