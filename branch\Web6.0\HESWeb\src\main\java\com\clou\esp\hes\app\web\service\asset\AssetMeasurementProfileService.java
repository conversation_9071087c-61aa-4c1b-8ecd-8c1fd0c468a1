/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeasurementProfile{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-02-28 02:48:00
 * 最后修改时间：
 * 
*********************************************************************************************************/

package com.clou.esp.hes.app.web.service.asset;

import com.clou.esp.hes.app.web.service.common.CommonService;
import com.clou.esp.hes.app.web.model.asset.AssetMeasurementProfile;

public interface AssetMeasurementProfileService extends CommonService<AssetMeasurementProfile>{
	
	public Integer deleteByEntity(AssetMeasurementProfile entity);
	
	/**
	 * 根据meterGroupId和profileType和dataitemId查询曲线
	 */
	public AssetMeasurementProfile getProfileByMgAndDataItemAndType(String mgId,String dataItemId,String type);
	public AssetMeasurementProfile getProfileByProfileid(String profileId);
}