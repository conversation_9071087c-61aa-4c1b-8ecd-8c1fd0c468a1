/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictServiceAttribute{ } 
 * 
 * 摘    要： dictServiceAttribute
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-03-29 03:19:41
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.dict;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DictServiceAttribute  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DictServiceAttribute() {
	}
	/**类型，每个类型对应多个属性*/
	private java.lang.Integer serviceType;
	/**属性名字*/
	private java.lang.String attributeName;
	/**判断属性的界面输入框类型，是input/select/others*/
	private java.lang.String attributeType;
	/**默认值*/
	private java.lang.String attributeDefault;
	/**属性描述*/
	private java.lang.String attributeDesc;
	/**排序编号*/
	private java.lang.String sortId;
	/**attributeValue*/
	private java.lang.String attributeValue;
	
	private java.lang.String serviceId;
	private java.lang.String attributeMin;	//限定最大值
	private java.lang.String attributeMax;	//限定最小值
	private java.lang.String opt;			//下拉参数

	/**
	 * attributeName
	 * @return the value of DICT_SERVICE_ATTRIBUTE.ATTRIBUTE_NAME
	 * @mbggenerated 2018-03-29 03:19:41
	 */
	public java.lang.String getAttributeName() {
		return attributeName;
	}

	/**
	 * attributeName
	 * @param attributeName the value for DICT_SERVICE_ATTRIBUTE.ATTRIBUTE_NAME
	 * @mbggenerated 2018-03-29 03:19:41
	 */
    	public void setAttributeName(java.lang.String attributeName) {
		this.attributeName = attributeName;
	}

	public java.lang.String getAttributeType() {
		return attributeType;
	}

	public void setAttributeType(java.lang.String attributeType) {
		this.attributeType = attributeType;
	}

	public java.lang.String getAttributeDefault() {
		return attributeDefault;
	}

	public void setAttributeDefault(java.lang.String attributeDefault) {
		this.attributeDefault = attributeDefault;
	}

	public java.lang.String getSortId() {
		return sortId;
	}

	public void setSortId(java.lang.String sortId) {
		this.sortId = sortId;
	}

	public java.lang.Integer getServiceType() {
		return serviceType;
	}

	public void setServiceType(java.lang.Integer serviceType) {
		this.serviceType = serviceType;
	}

	public java.lang.String getAttributeDesc() {
		return attributeDesc;
	}

	public void setAttributeDesc(java.lang.String attributeDesc) {
		this.attributeDesc = attributeDesc;
	}

	public java.lang.String getAttributeValue() {
		return attributeValue;
	}

	public void setAttributeValue(java.lang.String attributeValue) {
		this.attributeValue = attributeValue;
	}

	public java.lang.String getServiceId() {
		return serviceId;
	}

	public void setServiceId(java.lang.String serviceId) {
		this.serviceId = serviceId;
	}

	public java.lang.String getAttributeMin() {
		return attributeMin;
	}

	public void setAttributeMin(java.lang.String attributeMin) {
		this.attributeMin = attributeMin;
	}

	public java.lang.String getAttributeMax() {
		return attributeMax;
	}

	public void setAttributeMax(java.lang.String attributeMax) {
		this.attributeMax = attributeMax;
	}

	public java.lang.String getOpt() {
		return opt;
	}

	public void setOpt(java.lang.String opt) {
		this.opt = opt;
	}

}