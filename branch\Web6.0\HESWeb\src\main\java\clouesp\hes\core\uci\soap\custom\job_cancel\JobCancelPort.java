package clouesp.hes.core.uci.soap.custom.job_cancel;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

/**
 * This class was generated by Apache CXF 3.1.14
 * 2018-05-07T14:23:05.437+08:00
 * Generated source version: 3.1.14
 * 
 */
@WebService(targetNamespace = "http://job_cancel.custom.soap.uci.core.hes.clouesp/", name = "JobCancelPort")
@XmlSeeAlso({ObjectFactory.class})
public interface JobCancelPort {

    @WebResult(name = "return", targetNamespace = "")
    @RequestWrapper(localName = "cancelJob", targetNamespace = "http://job_cancel.custom.soap.uci.core.hes.clouesp/", className = "clouesp.hes.core.uci.soap.custom.job_cancel.CancelJob")
    @WebMethod
    @ResponseWrapper(localName = "cancelJobResponse", targetNamespace = "http://job_cancel.custom.soap.uci.core.hes.clouesp/", className = "clouesp.hes.core.uci.soap.custom.job_cancel.CancelJobResponse")
    public boolean cancelJob(
        @WebParam(name = "messageIds", targetNamespace = "")
        java.util.List<java.lang.String> messageIds
    );
}
