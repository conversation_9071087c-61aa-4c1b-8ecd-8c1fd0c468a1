package com.clou.esp.hes.app.web.model.report;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.google.common.collect.Maps;

public class MeterDataReport extends BaseEntity {
	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    /**自动填充状态*/
    private String manualFilling;//0:自动     1:手动
    /**设备SN*/
	private java.lang.String serizlName;
	private java.lang.String meterName;
	private java.lang.String addr;

	/**时间*/
	private java.util.Date times;
	/**数据值1*/
	private java.lang.String value1;
	/**数据值2*/
	private java.lang.String value2;
	/**数据值3*/
	private java.lang.String value3;
	/**数据值4*/
	private java.lang.String value4;
	/**数据值5*/
	private java.lang.String value5;
	/**数据值6*/
	private java.lang.String value6;
	/**数据值7*/
	private java.lang.String value7;
	/**数据值8*/
	private java.lang.String value8;
	/**数据值9*/
	private java.lang.String value9;
	/**数据值10*/
	private java.lang.String value10;
	/**数据值11*/
	private java.lang.String value11;
	/**数据值12*/
	private java.lang.String value12;
	/**数据值13*/
	private java.lang.String value13;
	/**数据值14*/
	private java.lang.String value14;
	/**数据值15*/
	private java.lang.String value15;
	/**数据值16*/
	private java.lang.String value16;
	/**数据值16*/
	private java.lang.String value17;
	/**数据值16*/
	private java.lang.String value18;
	/**数据值16*/
	private java.lang.String value19;
	/**数据值16*/
	private java.lang.String value20;
	/**数据值16*/
	private java.lang.String value21;
	/**数据值16*/
	private java.lang.String value22;
	/**数据值16*/
	private java.lang.String value23;
	/**数据值16*/
	private java.lang.String value24;
	/**数据值16*/
	private java.lang.String value25;
	/**数据值16*/
	private java.lang.String value26;
	/**数据值16*/
	private java.lang.String value27;
	/**数据值16*/
	private java.lang.String value28;
	/**数据值16*/
	private java.lang.String value29;
	/**数据值16*/
	private java.lang.String value30;
	/**数据值16*/
	private java.lang.String value31;
	/**数据值16*/
	private java.lang.String value32;
	/**数据值16*/
	private java.lang.String value33;
	/**数据值16*/
	private java.lang.String value34;
	/**数据值16*/
	private java.lang.String value35;
	/**数据值16*/
	private java.lang.String value36;
	/**数据值16*/
	private java.lang.String value37;
	/**数据值16*/
	private java.lang.String value38;
	/**数据值16*/
	private java.lang.String value39;
	/**数据值16*/
	private java.lang.String value40;
	/**数据值16*/
	private java.lang.String value41;
	/**数据值16*/
	private java.lang.String value42;
	/**数据值16*/
	private java.lang.String value43;
	/**数据值16*/
	private java.lang.String value44;
	/**数据值16*/
	private java.lang.String value45;


	public String getMeterName() {
		return meterName;
	}

	public void setMeterName(String meterName) {
		this.meterName = meterName;
	}

	public String getAddr() {
		return addr;
	}

	public void setAddr(String addr) {
		this.addr = addr;
	}

	public java.lang.String getSerizlName() {
		return serizlName;
	}
	public void setSerizlName(java.lang.String serizlName) {
		this.serizlName = serizlName;
	}
	public java.util.Date getTimes() {
		return times;
	}
	public void setTimes(java.util.Date times) {
		this.times= times;
	}
	public java.lang.String getValue1() {
		return value1;
	}
	public void setValue1(java.lang.String value1) {
		this.value1 = value1;
	}
	public java.lang.String getValue2() {
		return value2;
	}
	public void setValue2(java.lang.String value2) {
		this.value2 = value2;
	}
	public java.lang.String getValue3() {
		return value3;
	}
	public void setValue3(java.lang.String value3) {
		this.value3 = value3;
	}
	public java.lang.String getValue4() {
		return value4;
	}
	public void setValue4(java.lang.String value4) {
		this.value4 = value4;
	}
	public java.lang.String getValue5() {
		return value5;
	}
	public void setValue5(java.lang.String value5) {
		this.value5 = value5;
	}
	public java.lang.String getValue6() {
		return value6;
	}
	public void setValue6(java.lang.String value6) {
		this.value6 = value6;
	}
	public java.lang.String getValue7() {
		return value7;
	}
	public void setValue7(java.lang.String value7) {
		this.value7 = value7;
	}
	public java.lang.String getValue8() {
		return value8;
	}
	public void setValue8(java.lang.String value8) {
		this.value8 = value8;
	}
	public java.lang.String getValue9() {
		return value9;
	}
	public void setValue9(java.lang.String value9) {
		this.value9 = value9;
	}
	public java.lang.String getValue10() {
		return value10;
	}
	public void setValue10(java.lang.String value10) {
		this.value10 = value10;
	}
	public java.lang.String getValue11() {
		return value11;
	}
	public void setValue11(java.lang.String value11) {
		this.value11 = value11;
	}
	public java.lang.String getValue12() {
		return value12;
	}
	public void setValue12(java.lang.String value12) {
		this.value12 = value12;
	}
	public java.lang.String getValue13() {
		return value13;
	}
	public void setValue13(java.lang.String value13) {
		this.value13 = value13;
	}
	public java.lang.String getValue14() {
		return value14;
	}
	public void setValue14(java.lang.String value14) {
		this.value14 = value14;
	}
	public java.lang.String getValue15() {
		return value15;
	}
	public void setValue15(java.lang.String value15) {
		this.value15 = value15;
	}
	public java.lang.String getValue16() {
		return value16;
	}
	public void setValue16(java.lang.String value16) {
		this.value16 = value16;
	}
	public java.lang.String getValue17() {
		return value17;
	}
	public void setValue17(java.lang.String value17) {
		this.value17 = value17;
	}
	public java.lang.String getValue18() {
		return value18;
	}
	public void setValue18(java.lang.String value18) {
		this.value18 = value18;
	}
	public java.lang.String getValue19() {
		return value19;
	}
	public void setValue19(java.lang.String value19) {
		this.value19 = value19;
	}
	public java.lang.String getValue20() {
		return value20;
	}
	public void setValue20(java.lang.String value20) {
		this.value20 = value20;
	}
	public java.lang.String getValue21() {
		return value21;
	}
	public void setValue21(java.lang.String value21) {
		this.value21 = value21;
	}
	public java.lang.String getValue22() {
		return value22;
	}
	public void setValue22(java.lang.String value22) {
		this.value22 = value22;
	}
	public java.lang.String getValue23() {
		return value23;
	}
	public void setValue23(java.lang.String value23) {
		this.value23 = value23;
	}
	public java.lang.String getValue24() {
		return value24;
	}
	public void setValue24(java.lang.String value24) {
		this.value24 = value24;
	}
	public java.lang.String getValue25() {
		return value25;
	}
	public void setValue25(java.lang.String value25) {
		this.value25 = value25;
	}
	public java.lang.String getValue26() {
		return value26;
	}
	public void setValue26(java.lang.String value26) {
		this.value26 = value26;
	}
	public java.lang.String getValue27() {
		return value27;
	}
	public void setValue27(java.lang.String value27) {
		this.value27 = value27;
	}
	public java.lang.String getValue28() {
		return value28;
	}
	public void setValue28(java.lang.String value28) {
		this.value28 = value28;
	}
	public java.lang.String getValue29() {
		return value29;
	}
	public void setValue29(java.lang.String value29) {
		this.value29 = value29;
	}
	public java.lang.String getValue30() {
		return value30;
	}
	public void setValue30(java.lang.String value30) {
		this.value30 = value30;
	}
	public java.lang.String getValue31() {
		return value31;
	}
	public void setValue31(java.lang.String value31) {
		this.value31 = value31;
	}
	public java.lang.String getValue32() {
		return value32;
	}
	public void setValue32(java.lang.String value32) {
		this.value32 = value32;
	}
	public java.lang.String getValue33() {
		return value33;
	}
	public void setValue33(java.lang.String value33) {
		this.value33 = value33;
	}
	public java.lang.String getValue34() {
		return value34;
	}
	public void setValue34(java.lang.String value34) {
		this.value34 = value34;
	}
	public java.lang.String getValue35() {
		return value35;
	}
	public void setValue35(java.lang.String value35) {
		this.value35 = value35;
	}
	public java.lang.String getValue36() {
		return value36;
	}
	public void setValue36(java.lang.String value36) {
		this.value36 = value36;
	}
	public java.lang.String getValue37() {
		return value37;
	}
	public void setValue37(java.lang.String value37) {
		this.value37 = value37;
	}
	public java.lang.String getValue38() {
		return value38;
	}
	public void setValue38(java.lang.String value38) {
		this.value38 = value38;
	}
	public java.lang.String getValue39() {
		return value39;
	}
	public void setValue39(java.lang.String value39) {
		this.value39 = value39;
	}
	public java.lang.String getValue40() {
		return value40;
	}
	public void setValue40(java.lang.String value40) {
		this.value40 = value40;
	}
	public java.lang.String getValue41() {
		return value41;
	}
	public void setValue41(java.lang.String value41) {
		this.value41 = value41;
	}
	public java.lang.String getValue42() {
		return value42;
	}
	public void setValue42(java.lang.String value42) {
		this.value42 = value42;
	}
	public java.lang.String getValue43() {
		return value43;
	}
	public void setValue43(java.lang.String value43) {
		this.value43 = value43;
	}
	public java.lang.String getValue44() {
		return value44;
	}
	public void setValue44(java.lang.String value44) {
		this.value44 = value44;
	}
	public java.lang.String getValue45() {
		return value45;
	}
	public void setValue45(java.lang.String value45) {
		this.value45 = value45;
	}
	
	public String getManualFilling() {
		return manualFilling;
	}
	public void setManualFilling(String manualFilling) {
		this.manualFilling = manualFilling;
	}
	public MeterDataReport() {};
	
	public MeterDataReport(String serizlName, Date times,String manualFilling) {
		super();
		this.serizlName = serizlName;
		this.times = times;
		this.manualFilling=manualFilling;
	}

	public Map<String,String> getMap(SimpleDateFormat sdf ) {
		Map<String,String> map = Maps.newHashMap();
		map.put("serizlName", serizlName);
		map.put("times", sdf.format(times));
		map.put("manualFilling", manualFilling);
		map.put("value1",value1);
		map.put("value2",value2);
		map.put("value3",value3);
		map.put("value4",value4);
		map.put("value5",value5);
		map.put("value6",value6);
		map.put("value7",value7);
		map.put("value8",value8);
		map.put("value9",value9);
		map.put("value10",value10);
		map.put("value11",value11);
		map.put("value12",value12);
		map.put("value13",value13);
		map.put("value14",value14);
		map.put("value15",value15);
		map.put("value16",value16);
		map.put("value17",value17);
		map.put("value18",value18);
		map.put("value19",value19);
		map.put("value20",value20);
		map.put("value21",value21);
		map.put("value22",value22);
		map.put("value23",value23);
		map.put("value24",value24);
		map.put("value25",value25);
		map.put("value26",value26);
		map.put("value27",value27);
		map.put("value28",value28);
		map.put("value29",value29);
		map.put("value30",value30);
		map.put("value31",value31);
		map.put("value32",value32);
		map.put("value33",value33);
		map.put("value34",value34);
		map.put("value35",value35);
		map.put("value36",value36);
		map.put("value37",value37);
		map.put("value38",value38);
		map.put("value39",value39);
		map.put("value40",value40);
		map.put("value41",value41);
		map.put("value42",value42);
		map.put("value43",value43);
		map.put("value44",value44);
		map.put("value45",value45);
		return map;
	}
	
	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	public boolean isRight(String timeType) {
		String time = sdf.format(times);
		boolean flag=true;
		if("1".equals(timeType)) {//小时  2019-01-01 00:00:00
			String end=time.substring(14, time.length());
			if(!"00:00".equals(end)) {
				flag=false;
			}
		}else if("2".equals(timeType)) {//日
			String end=time.substring(11, time.length());
			if(!"00:00:00".equals(end)) {
				flag=false;
			}
		}
		
		return flag;
	}
}
