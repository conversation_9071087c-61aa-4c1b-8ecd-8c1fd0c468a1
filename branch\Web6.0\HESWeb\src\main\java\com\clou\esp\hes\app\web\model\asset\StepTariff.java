package com.clou.esp.hes.app.web.model.asset;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

/**
 * 阶梯汇率model
 * <AUTHOR>
 * @date 2018/04/17
 */
public class StepTariff extends BaseEntity{
	
	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public StepTariff() {
	}
	
	private String mgId;			//电表组ID
	
	private String stepName;		//阶梯名
	private String startQuantity;	//开始数目
	private String endQuantity;		//结尾数目
	private String price;			//价格

	public String getStepName() {
		return stepName;
	}
	public void setStepName(String stepName) {
		this.stepName = stepName;
	}
	public String getStartQuantity() {
		return startQuantity;
	}
	public void setStartQuantity(String startQuantity) {
		this.startQuantity = startQuantity;
	}
	public String getEndQuantity() {
		return endQuantity;
	}
	public void setEndQuantity(String endQuantity) {
		this.endQuantity = endQuantity;
	}
	public String getPrice() {
		return price;
	}
	public void setPrice(String price) {
		this.price = price;
	}
	public String getMgId() {
		return mgId;
	}
	public void setMgId(String mgId) {
		this.mgId = mgId;
	}
	
	
}
