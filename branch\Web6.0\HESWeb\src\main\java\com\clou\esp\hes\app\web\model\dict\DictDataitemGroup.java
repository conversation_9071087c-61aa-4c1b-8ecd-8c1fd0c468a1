/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictDataitemGroup{ } 
 * 
 * 摘    要： 数据项分组
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.dict;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DictDataitemGroup  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	public DictDataitemGroup() {}

	private String 	name;
	/**1：定炒    2、事件*/
	private String 	appType;
	/** 规约编号 */
	private String 	protocolId;
	
	private Integer	sortId;

	private String  oldId;
	
	
	public String getOldId() {
		return oldId;
	}

	public void setOldId(String oldId) {
		this.oldId = oldId;
	}

	public Integer getSortId() {
		return sortId;
	}

	public void setSortId(Integer sortId) {
		this.sortId = sortId;
	}

	public String getName() {
		return MutiLangUtil.assemblyI18nData(MutiLangUtil.DATAITEM_GROUP_I18N,name);
	}

	public void setName(String name) {
		this.name = name;
	}
	
	public String getAppType() {
		return appType;
	}

	public void setAppType(String appType) {
		this.appType = appType;
	}

	
	public String getProtocolId() {
		return protocolId;
	}

	
	public void setProtocolId(String protocolId) {
		this.protocolId = protocolId;
	}

	public DictDataitemGroup(String name, String appType, String protocolId) {
		super();
		this.name = name;
		this.appType = appType;
		this.protocolId = protocolId;
	}
	
	
	

}