{"login": {"username": "用户名", "pswd": "密码", "usernameNull": "用户不能为空！", "pswdNull": "密码不能为空！", "remeberMe": "记住用户名和密码", "login": "登录", "loginOnOtherPlace": "您已经在其他地方登录，请重新登录！", "accountDisabled": "您的帐户被禁用，将被迫注销!", "world": "世界", "America": "美国", "Nigeria": "尼日利亚", "Australia": "澳大利亚", "ACP": "美国云平台", "notExistUser": "用户不存在！", "LockedUser": "用户已被锁定！", "errorPwd": "密码错误，输入错误超过三次用户将被锁定，剩余尝试次数{0}！", "disabledUser": "用户已被禁用！", "errorUser": "用户异常！", "loginFirst": "请先登录！", "logoutFailed": "退出失败，请重试！", "logout": "退出", "tenantIsStop": "所在租户已停用！", "accLgedElsewhere": "此用户已在其他地方登录!", "userDontExist": "该用户不存在!", "toRevisePwd": "您的密码已过期。 请更改密码", "firstLoginRevisePwd": "请更改首次登录密码", "firstLoginResetPassword": "首次登录以重置密码"}, "system": {"systemName": "ClouESP", "systemVersion": "V5.0", "copyRight": "Copyright © 2018 SHENZHEN CLOU. All rights reserved.", "determine": "确认", "cancel": "取消", "notifications": "通知", "information": "信息", "message": "消息", "aydeltherdate": "确认删除此记录?", "submit": "确认", "pseleData": "请选择一个数据!", "pleaseInterNum": "请输入号码!", "reqexc": "请求异常!", "delete": "删除", "add": "添加", "edit": "编辑", "change": "将RF更改为GPRS", "inquiryTitle": "通知", "delSuccess": "删除成功!", "delFail": "删除失败!", "abnoOpera": "操作异常!", "addSucc": "添加成功!", "updateSucc": "更新成功!", "saveSucc": "保存成功!", "saveFail": "保存失败!", "cancelSucc": "取消成功!", "cancelFail": "取消失败!", "requestError": "请求错误!", "operSucce": "操作成功!", "nameExist": "账户已经存在!", "systemException": "系统异常!", "UCIException": "UCI 异常!", "selectNoData": "找不到数据!", "operation": "操作", "export": "输出", "print": "打印", "start": "开始", "end": "结束", "validSucc": "验证通过.", "refreshTree": "刷新节点."}, "home": {"devicesStatistic": "设备统计", "meters": " 电表", "concentrators": "集中器", "communicators": "集中器", "selectiveCategory": "选择类别", "manufacturer": "厂商", "model": " 型号", "communication": "通讯类型", "name": " 名称", "number": "数量", "rate": " 比率", "date": "日期", "collectionIntegrity": "完整率统计", "completeRate": "完整率", "completedNumber": "数据完整表数", "partialNumber": "数据部分完整表数", "failedNumber": "失败表数", "eventsStatistic": "事件统计", "eventsTotal": "事件总计", "classifiedStatistcOfEvents": "事件分类统计", "overallDaily": "日完整率 (%)", "classifiedStatistcOfMeter": "电表分类统计", "supplyStaticOfSubOrgan": "下级组织机构供电量统计", "supplyStatistic": "本级组织机构供电量统计", "salesStatisticOfCustomer": "本级组织机构售电量统计", "organization": "组织机构", "type": "类型", "powerSupply": "供电量(kWh)", "customerNumber": "数量", "supplyStatisticDaily": "过去7日的供电量统计(kWh)", "supplyStatistic15Daily": "过去15日的供电量统计(kWh)", "supplyStatistic30Daily": "过去30日的供电量统计(kWh)", "supplyStatistic6Month": "过去6个月的供电量统计(kWh)", "supplyStatistic12Month": "过去一年的供电量统计(kWh)", "Last7days": "过去7日", "Last15days": "过去15日", "Last30days": "过去30日", "supplyCount": "下级组织机构供电量统计", "onlineMeter": "在线电表数", "onlineCommunicators": "在线集中器数", "onlineConcentrators": "在线集中器数", "yesterdayPowerSupply": "昨日供电量(kWh)", "other": "其他", "noData": "无数据", "Last6months": "过去6个月", "Last12months": "过去一年"}, "error403": {"title": "网页未找到!", "content": " 抱歉，本页未获授权~"}, "error404": {"title": "网页未找到!", "content": "抱歉，这页好像要转到火星了~"}, "error500": {"title": "内部服务器错误!", "content": "服务器似乎出错了……"}, "noPermissions": {"code": "10002", "title": "没有访问!", "content": "如果您需要访问，请与管理员联系……"}, "index": {"home": " 主页", "logout": "退出", "search": "搜索", "meter": "电表", "commnuicator": "集中器", "serialNumber": "编号", "name": "名称", "mac": "物理地址", "model": "型号", "searchFor": "搜索", "favorites": "收藏夹", "myProfile": "我的资料", "passwordSetting": "密码设置", "confCanFavorite": "确认取消收藏?", "advancedSearch": "高级搜索", "frimwareVersion": "固件版本", "measurementGroup": "曲线参数分组", "collectionSchemeGroup": "采集方案分组", "line": "线路", "transformer": "变压器", "manufacturer": "厂商", "commuicationType": "通讯类型", "more": "其他", "selected": "已选择", "pleaseChoose": "请选择", "communicatorSn": "通讯编号", "organization": "组织机构", "headAlarmTitle": "电表报警事件报表", "supplyStatisticsReport": "供应统计报表", "noAlarm": "无报警", "customer": "客户", "telephoneNum": "电话号码", "tel": "TEL", "voltageLevel": "电压等级", "refreshOrgLineTransformer": "刷新组织结构...", "searchOrgLineTransformer": "搜索节点...", "loadTreeNode": "加载节点..."}, "connectOrDisconnect": {"function": "功能", "system": "系统", "url": "URL", "connectDisconnect": "合闸/拉闸", "power7000": "Power7000"}, "scheduleReadsReport": {"title": "计划报表", "overview": "概述", "manufacturer": "厂商", "model": "型号", "communication": "通讯类型", "ydayInteRate": "昨日数据采集完整率", "ydayCompRate": "昨日电表采集完整率", "completed": "完成", "partial": "部分", "failed": "失败", "details": "详细", "dMeterInteRate": "日电表采集完整率", "start": "开始", "Start": "开始", "end": "结束", "mmeterInteRate": "月电表采集完整率", "dInteRateManu": "日采集完整率", "mInteRateManu": "月采集完整率", "dInteRateSMmanu": "单个/多个通讯日采集完整率", "mInteRateSMmanu": "单个/多个通讯月采集完整率", "dInteRateModels": "日采集完整率", "mInteRateModels": "月采集完整率", "dInteRateSMModel": "单/多个电表类型采集完整率", "mInteRateSMModel": "单/多个电表类型月采集完整率", "dInteRateCommun": "日采集完整率", "mInteRateCommun": "月采集完整率", "dInteRateSMCommun": "单/多个通讯模块日采集完整率", "mInteRateSMCommun": "单/多个通讯模块月采集完整率", "dInteRateOrg": "日采集完整率", "mInteRateOrg": "月采集完整率", "dInteRateSMOrg": "单个/多个组织机构日采集完整率", "mInteRateSMOrg": "单个/多个组织机构月采集完整率", "dailyInteRate": "日采集完整率", "monthlyInteRate": "月采集完整率", "dmeterInteRadeio": "日电表读取采集完整率", "mmeterInteRadeio": "月电表读取采集完整率", "dInteRatioManu": "通讯日采集完整率", "calcIntegrityRate": "计算完整率", "calcLineLoss": "计算线损", "holdManual": "后台已重新计算、请等待"}, "assetScheduleSchemeDetailList": {"title": "任务列表", "taskId": "流水号", "id": "编号", "taskType": "任务类型", "profileId": "曲线名称", "taskCycleType": "任务周期类型", "taskCycle": "任务周期", "startTime": "开始时间", "endTime": "结束时间", "pseleData": "此方案已被引用，无法删除!", "confirmDel": "确认删除此任务?", "addReadsTask": "数据读取任务", "addEvenTask": "事件读取任务", "addTimeTask": "时间同步任务", "collSchemeMage": "采集方案管理", "daily": "日", "hourly": "小时", "minutely": "分钟", "monthly": "月", "pleaseChoose": "---请选择---", "successfullyModified": "成功修改", "addedSuccessfully": "成功添加"}, "assetScheduleSchemeList": {"title": "采集方案列表", "id": "编号", "name": "方案名称", "meterStandard": "采集规约", "protocolId": "规约编号", "meterNumber": "电表数量", "referenceScheme": "参考方案", "description": "描述", "pseleData": "请选择方案列表数据!", "confirmDel": "确认删除方案?", "canBeDeleted": "该方案与无法删除的电表相关联!"}, "dataIntegrityList": {"headTitle": "缺失数据跟踪", "evenExportTitle": "电表数据和事件导出", "id": "No", "serizlNo": "电表编号", "tv": "日期", "mfrId": "厂家名称", "modelId": "电表类型", "communicator": "集中器", "commId": "通讯", "progress": "进度", "lastTask": "任务最后执行时间", "taskResult": "任务结果", "failureCause": "失败原因", "integrity": "比率(%)", "integrityRate": "完整率 (%)", "analysis": "参考分析", "export": "输出", "print": "打印", "taskResultFailed": "失败", "taskResultSuccess": "成功", "all": "全部", "delayDay": "进度延迟（天）", "progressDelayReport": "进度延迟报表", "missDataReport": "数据缺失报表", "integrityRateReport": "完整率报表", "updateTv": "状态更新时间", "comStatus": "网络状态", "offLine": "离线", "onLine": "在线", "abnormalOperation": "操作异常", "yes": "是", "Profile": "曲线", "meterSN": "电表编号", "noData": "无数据"}, "dataIntegrityDetails": {"id": "No", "serizlNo": "编号", "tv": "数据时间", "manufacturer": "厂商", "model": "型号", "communication": "通讯类型", "profile": "曲线", "readStatus": "读取状态"}, "dataMeterEventList": {"headTitle": "电表事件报表", "headAlarmTitle": "电表报警事件报表", "deviceId": "设备编号", "eventId": "事件编号", "sn": "编号", "export": "输出", "print": "打印", "tv": "时间", "eventType": "事件类型", "all": "全选", "event": "事件", "eventDetail": "事件详细信息"}, "dayList": {"id": "编号", "dayName": "日编号", "startTime": "开始时间", "rate": "比率", "add": "添加", "addDay": "添加日期", "hour": "小时", "minute": "分钟", "second": "秒", "delete": "删除", "title": "日列表", "pleaseSelectData": "请在日单中选择一天!", "pleSetRateDayList": "请在日单上设定比率!", "lastOneTimePeriod": "日数据至少一个时间段!", "areBeAtMost": "最多允许创建255天!", "inteAllAtMost": "一天最多允许8次休息!"}, "device_list": {"headTitle": "电表事件报表", "pageTitle": "电表参数设置", "title": "曲线类型", "id": "编号", "sn": "电表编号", "modelName": "电表类型", "communicator": "集中器编号", "comType": "通讯类型", "set": "设置", "get": "读取", "delete": "删除", "parameterType": "参数类型", "operation": "操作", "profiles": "曲线", "channels": "随时抄表", "seleLeastOne": "选择至少一个表数据!", "leastOneChoose": "选择至少一份阅读!", "communicatorList": "集中器列表"}, "device_read_parameter_list": {"title": "结果", "id": "编号", "sn": "电表编号", "parameterType": "参数类型", "parameterItem": "参数项", "meterGroup": "电表组", "requestTime": "请求时间", "responseTime": "响应时间", "status": "状态", "reason": "原因", "delete": "删除", "export": "输出", "print": "打印", "total": "总数", "completed": "完成", "success": "成功", "failed": "失败", "processing": "处理", "tariffType": "费率类型", "active": "激活", "passive": "备选状态", "touGroup": "分时费率组", "limiterGroup": "门限", "pleAddTask": "请添加任务!", "areToExcTask": "你确定要删除这个任务列表吗?", "stepTariffGroup": "阶梯费率组", "prepay": "预付", "friend": "友好时段费率", "touTariff": "分时费率"}, "linkedEntityRelationship": {"meterSN": "电表型号", "logicalName": "物理地址", "customerType": "客户类型", "organization": "组织机构", "linkedMeter": "关联电表", "linkedMeterList": "关联电表列表", "linkedTransformer": "关联变压器", "linkedTransformerList": "关联变压器列表", "communicatorSN": "集中器编号", "communicatorName": "集中器名称", "transformerName": "变压器名称", "transformerSN": "变压器编号", "selectLeastLine": "请先选择一条线路.", "selectLeastTransformer": "请先选择一个变压器."}, "importTempGprsMeter": {"downFileImport": "下载模板文件", "tempFileImport": "导入模板文件", "tempFile": "模板文件", "meterType": "电表类型", "DCU": "DCU", "importStatus": "导入状态", "importLog": "导入日志", "commAddress": "公共地址", "logicalAddr": "逻辑地址", "import": "导入", "finish": "结束"}, "deviceList": {"abnormalRequestFromUCI": "UCI的异常请求", "title": "档案管理", "basicTitle": "基本属性", "communication": "通讯属性", "groupPro": "组属性", "other": "其他", "addGprsMeter": "添加GPRS电表", "addMeterCommun": "向集中器中添加电表", "addCommunicatior": "添加集中器", "id": "编号", "deviceSN": "设备编号", "deviceType": "设备类型", "name": "名称", "mac": "物理地址", "model": "型号", "communicationType": "通讯方式", "manufacturer": "厂商", "limiter": "门限", "tou": "分时费率", "stepTariff": "阶梯费率", "schemeGroup": "采集方案", "meaGroup": "采集曲线", "encrypt": "加密", "ek": "加密密钥", "ak": "认证密钥", "llsPwd": "LLS 密码", "authType": "验证类型", "port": "端口", "ip": "编号", "organation": "组织机构", "channel": "通讯模块", "schedule": "定抄模块", "firmVer": "固件版本", "pleaseChoose": "---请选择---", "meterNullMsg": "电表为空", "commnuicatorNullMsg": "通讯器为空", "pleSelDevice": "请选择曲线列表!", "ipValidErrorMsg": "Ip地址格式不正确", "noDeviceFound": "没有发现设备", "deviceNumExists": "设备号已经存在", "pleSeleCOmmun": "请选择通讯器!", "pleSaveOrDel": "请保存数据或删除数据!", "tCommunHChild": "该集中器下有电表，不能删除！", "pleSelOrganation": "请选择组织机构!", "addr": "地址", "simNum": "SIM卡编号", "meterTitle": "电表", "commTitle": "集中器", "meterInfo": "电表", "commInfo": "集中器", "commSN": "集中器编号", "communicator": "集中器", "ct": "CT", "pt": "PT", "indexDcu": "采集序号", "comPort": "通讯端口", "keyMeter": "关键仪表", "archivedMeter": "归档的表计", "changeSuccess": "更改成功!", "changeTitle": "仪表通信类型更改"}, "divFMUPlanMeterList": {"pleSelDevType": "请选择逻辑设备名!", "pleSelManufac": "请选择厂商!", "pleEntPlanDesc": "请输入计划说明!", "pleSelPlanTime": "请选择计划开始时间!", "pleSelPlanExpTime": "请选择计划有效期!", "planExpPlanTime": "计划到期时间必须在计划开始时间之后!", "pleEntNewVer": "请输入新版本!", "pleEntImgeIden": "请输入图像标识符!", "pleSelFirFile": "请选择电表版本", "pleSelTaskStartTime": "请选择任务开始时间!", "pleSelTaskEndTime": "请选择任务结束时间!", "taskStartLaterSTime": "任务开始时间必须等于或晚于任务结束时间!", "pleSelTaskCyc": "请选择任务周期!", "confCreatePlan": "确认创建此计划?", "pleSelModel": "请选择电表类型!", "plan": "计划", "planReport": "计划报表", "jobReport": "任务报表", "deviceSearch": "设备搜索", "manufacturer": "厂商", "model": "电表型号", "deviceSN": "设备编号", "planCreation": "计划创建", "planDesc": "计划描述", "planStartTime": "计划开始时间", "planExpTime": "计划到期时间", "newVersion": "新版本", "imageIdentifier": "图像标识符", "firmwareFile": "电表版本文件", "taskStartTime": "任务开始时间", "taskEndTime": "任务结束时间", "taskCycle": "任务周期", "hour1": "1 小时", "hour2": "2 小时", "hour3": "3 小时", "hour4": "4 小时", "hour5": "5 小时", "hour6": "6 小时", "hour7": "7 小时", "hour8": "8 小时", "hour9": "9 小时", "hour10": "10 小时", "hour11": "11 小时", "hour12": "12 小时", "deviceType": "物理地址", "currentVersion": "当前版本", "expTimeAfterStaTime": "计划到期时间必须在计划开始时间之后!", "pleaEntNewVer": "请输入新版本!", "pleaEntImaIdent": "请输入图像标识符!", "pleaSelFirmFile": "请选择电表版本文件", "pleaSelStartTime": "请选择任务开始时间!", "pleaSelEndTime": "请选择任务结束时间!", "startTimeLaterThanEndTime": "任务开始时间必须等于或晚于任务结束时间!", "pleaSelTaskCycle": "请选择任务周期!", "confCreaPlan": "确认创建此计划?", "information": "信息"}, "addAssetGPRSMeter": {"sn": "编号", "comSn": "通讯编号", "refCommun": "参考通讯", "referenceMeter": "参考电表", "refSnIsEm": "参考电表编号为空！", "successMatch": "成功匹配", "devTypeMis": "逻辑设备名不匹配", "pleMatchData": "请先匹配数据!"}, "FMUPlanMeterList": {"title": "电表参数列表", "sn": "电表编号", "manufacturerName": "厂商", "modelName": "电表类型", "export": "输出", "print": "打印", "fwVersion": "当前版本"}, "FMUPlanMeterList0": {"title": "电表参数列表", "sn": "电表编号", "manufacturerName": "厂商", "modelName": "电表类型", "currentVesion": "当前版本", "newVersion": "新版本", "startTime": "开始时间", "export": "输出", "print": "打印", "expiryTime": "有效期"}, "jobReportJobList": {"title": "项目", "opt": "操作", "id": "ID", "meterIdDfj": "电表ID", "deviceTypeDfj": "物理地址", "snDfj": "电表编号", "manufacturerIdDfj": "厂商", "modelNameDfj": "电表类型", "currentVesionDfj": "旧版本", "newVersionDfj": "新版本", "startTimeDfj": "开始时间", "expiryTimeDfj": "有效期", "lastExecTimeDfj": "上次操作时间", "stateDfj": "状态", "blockSizeDfj": "Block Size (Byte)", "blockCountDfj": "Transferred Block Count", "fwuProgressDfj": "进度", "export": "输出", "print": "打印", "confToCancJob": "确认取消此工作?", "cancelled": "已取消", "failedReasonDfj": "原因", "runing": "正在运行", "done": "已完成", "cancel": "已取消", "waiting": "等待中", "expired": "到期", "jobNoExcute": "这项工作还没有开始运行!"}, "limiterGroupList": {"id": "编号", "type": "类型", "name": "组名", "protocolId": "电表规约", "meterNumber": "电表数量", "introduction": "描述", "meterCount": "电表计算", "title": "门限列表", "titleDetails": "门限详细", "add": "添加门限", "delete": "删除", "pleaseSelectData": "请在门限列表中选择一个组!"}, "limiterList": {"title": "门限配置", "id": "编号", "name": "门限名称", "value": "门限值", "meterCount": "电表数量", "getProgress": "取得进展", "day": "天", "week": "周", "season": "原因", "upcoming": "即将", "processing": "正在处理", "success": "成功", "failed": "失败", "timeout": "超时", "cancelled": "取消", "meterCanBeEm": "电表不能空！", "choLeastCopRead": "至少选择一份读取！", "seleLeastOneData": "至少选择一个表数据！", "pleLimitCofVal": "请设置门限配置值!", "pleLimitCofValNo": "请设置门限配置值号码!", "calGetPtogress": "日历进程", "speDayGetPtogress": "特殊日进程", "stepTariffGetPtogress": "阶梯费率进程"}, "measurementGroupList": {"title": "采集曲线列表", "titleDetails": "采集曲线详细信息", "add": "添加采集曲线", "delete": "删除", "id": "编号", "type": "类型", "name": "组名", "protocolId": "电表规约", "referenceGroup": "参照组", "meterNumber": "电表的数量", "introduction": "描述", "meterCount": "电表数量", "editProfile": "编辑曲线", "pleaseSelectData": "请选择一组采集曲线列表!", "confirmToDel": "确认删除这个组?", "addProfile": "添加曲线"}, "meterDataReportList": {"frequency": "频率", "headTitle": "电表数据定抄", "intervalEnergy": "供电质量报表", "times": "时间", "groupId": "曲线", "channel": "曲线分组", "all selected": "选择所有", "serizlName": "编号", "export": "输出", "print": "打印", "graphVisualiz": "图形可视化", "noDataChannel": "没有选择数据通道", "tableView": "表视图", "lineChart": "折线图", "columnChart": "柱状图", "reduction": "刷新", "saveAsPicture": "另存为图片", "mustSingTable": "必须是单个表的统计信息吗", "pleSelColumn": "请选择列", "searchFor": "搜索", "selectSn": "请选择编号", "resultOver": "结果集超过200条，请重新筛选条件!", "selectAll": "全选", "allSelected": "全选"}, "planReportJobList": {"title": "工作", "opt": "操作", "id": "编号", "meterId": "电表编号", "sn": "电表编号", "manufacturerName": "厂商", "modelName": "电表类型", "currentVesion": "当前版本", "newVersion": "新版本", "lastExecTime": "上次操作时间", "state": "状态", "blockSize": "数据块大小(字节)", "blockCount": "传输数据块的数量", "fwuProgress": "进度", "failedReason": "原因", "export": "输出", "print": "打印", "cancel": "取消", "confiCanCelJob": "确认取消该任务吗?"}, "planReportList": {"title": "计划", "opt": "操作", "id": "编号", "deviceType": "物理地址", "introduction": "计划描述", "manufacturerId": "厂商", "modelName": "电表类型", "currentVesion": "当前版本", "newVersion": "新版本", "taskCycle": "任务周期", "filePath": "文件名称", "startTime": "开始时间", "expiryTime": "过期时间", "taskStartTimeStr": "任务开始时间", "taskEndTimeStr": "任务结束时间", "state": "是否过期", "done": "已完成", "expired": "过期的", "running": "正在运行", "export": "输出", "print": "打印", "cancelled": "已取消", "cancel": "取消", "conToCanPlan": "确认取消这个计划?", "waiting": "请等待", "meter": "电表", "communicator": "通讯", "valid": "有效的"}, "profileList": {"title": "曲线列表", "add": "添加", "delete": "删除", "id": "编号", "mgId": "采集曲线编号", "profileId": "曲线编号", "profileType": "曲线类型", "profileType_view": "曲线类型", "profileName": "曲线名称", "profileCycleType": "配置间隔类型", "profileCycle": "配置间隔", "protocolCode": "代码", "pleaseSelectData": "请在曲线列表中选择一个曲线!", "pleSelAntOne": "该曲线已添加，请选择另一个!", "confirmToDel": "确认删除这个曲线?"}, "seasonList": {"id": "编号", "seasonName": "季度编号", "startTime": "开始时间", "weekProfile": "周曲线", "title": "季度名单", "add": "添加", "delete": "删除", "month": "月份", "dayofMonth": "Day of Month", "year": "年份", "dayofWeek": "Day of Week", "hour": "小时", "minute": "分钟", "second": "秒", "pleaseSelectData": "请选择季度列表中的一个季度!", "pleSetSeasonList": "请在季度列表中建立档案!", "seasonAreBeAtMost": "最多允许创建255个季度!", "seleLeastOneData": "选择至少一个表数据!", "noMeterGroup": "请检查电表组属性是否已经配置好！ "}, "selectedDataChannelList": {"title": "选择数据通道", "up": "Up", "down": "Down", "delete": "删除", "id": "编号", "mgId": "采集曲线编号", "profileId": "曲线编号", "dataitemId": "曲线分组编号", "dataitemName": "数据通道", "sortId": "编号排序"}, "specialDayList": {"title": "特殊日列表", "id": "编号", "specialDayName": "特殊日编号", "date": "日期", "dayProfile": "日曲线", "month": "月", "dayofMonth": "月日", "year": "年", "dayofWeek": "Day of Week", "pleaseSelectData": "请在特别日列表中选择一天!", "pleSpeDayDprofile": "请设置特殊的日列表日曲线!", "daysBeAtMost": "最多允许创建255天!", "dayAssCanBeDel": "与特殊日列表相关联,不能删除!"}, "sysIntegrationLogList": {"title": "通讯系统运行日志", "id": "编号", "sn": "编号", "fromId": "系统", "tv": "请求时间", "requestType": "请求类型", "requestSubType": "请求子类型", "responseResult": "响应结果", "export": "输出", "print": "打印"}, "timeSynchronizationLog": {"title": "时间同步日志", "id": "编号", "sn": "编号", "tv": "任务时间", "strSynResult": "同步结果", "meterTv": "电表时间", "systemTv": "系统时间", "synTv": "同步时间", "failedReason": "失败原因", "export": "输出", "print": "打印"}, "touGroupList": {"id": "编号", "title": "电表分组管理", "touTitle": "分时费率组列表", "touTitle_": "分时费率组详细", "measurement": "采集曲线参数", "tou": "分时费率", "limiter": "门限", "addTouGroup": "添加分时费率组", "calendar": "日历", "specialDay": "特殊日", "delete": "删除", "type": "类型", "name": "分组名称", "protocolId": "电表规约", "meterNumber": "电表数量", "introduction": "描述", "meterCount": "电表数量", "referenceGroup": "参照组", "pleaseSelectData": "请在分时群组列表中选择一个群组!", "canNotBeDel": "无法删除!", "confirmToDel": "确认删除此组?"}, "tracingLogList": {"headTitle": "电表分组管理", "meterTracingLog": "电表跟踪日志", "timeSynLog": "时间同步日志", "sysInteLog": "系统集成的日志", "userLog": "用户日志", "deviceSn": "设备编号", "service": "服务", "logLevel": "日志级别", "startTime": "开始时间", "endTime": "结束时间", "reqType": "请求类型", "user": "用户", "logType": "日志类型", "logSubType": "日志的子类型", "clikcSearchBut": "单击搜索按钮进行搜索", "sychResult": "同步结果", "title": "跟踪日志", "export": "输出", "print": "打印", "id": "编号", "date": "请求时间", "serviceId": "服务", "type": "日志类型", "level": "日志级别", "content": "内容", "success": "成功", "failed": "失败", "normal": "正常"}, "userLogList": {"title": "用户日志", "userId": "用户编号", "tv": "操作时间", "name": "用户", "logType": "日志类型", "logSubType": "日志子类型", "detail": "内容", "startTimeCanNot": "开始时间和结束时间不能为空!", "logTimeBeWDay": "日志查询时间应在一天之内!"}, "unSelectedDataChannelList": {"title": "可选数据通道", "add": "添加", "id": "编号", "mgId": "采集曲线编号", "profileId": "曲线编号", "dataitemId": "曲线分组编号", "dataitemName": "数据通道", "sortId": "编号排序", "pleaseSelectData": "请在此列表中选择一个数据通道!"}, "weekList": {"id": "编号", "weekName": "周编号", "dayProfile": "日曲线", "add": "添加", "delete": "删除", "title": "周列表", "pleaseSelectData": "请在周表中选择一周!", "pleSetWeekList": "请在周列表中设置日档案!", "youCanSingleDay": "您不能删除单日!", "seasonListCanBeDel": "该星期与不能删除的季度清单关联!", "weeksToBeAtMost": "最多允许创建255周!", "weekCanBeDel": "日期与无法删除的星期列表相关联!"}, "sysUserList": {"headTitle": "用户和角色", "title": "用户列表", "orgPassword": "初始密码", "newPassword": "新密码", "confirm": "确认", "enterNewPasswordAgain": "再次输入新密码", "askYouForgotPwd": "如果您忘记了密码，请向系统管理员求助.", "id": "编号", "username": "账户名", "name": "名称", "email": "邮件", "mobilePhone": "电话号码", "userState": "状态", "userType": "用户类型", "roleId": "角色", "orgId": "组织机构编号", "lastLoginTime": "上次登录时间", "opt": "操作", "user": "用户", "role": "角色", "organization": "组织机构", "eisable": "禁用", "enable": "启用", "addUser": "添加用户", "delUser": "删除用户", "resPwd": "重置密码", "oldPasswordError": "出初始密码错误!", "newPasswordError": "新密码与初始密码不一致!", "resetPassSucc": "重置密码成功!", "confirmDisable": "确认禁用该用户帐户吗?", "confirmEnable": "确认启用该用户帐户吗?", "selectUser": "请选择一个用户!", "myProfile": "我的资料", "passSet": "密码设置", "administrator": "超级管理员", "normal": "普通用户", "password": "密码", "pleaEnterPass": "请输入你的密码", "roleName": "角色", "accountNotExist": "该帐户不存在", "validatePwd": "密码长度为8到15位，必须包含字母和数字，字母区分大小写", "originConfirmRepeat": "原始密码与新密码不同!", "passwordTooLong": "密码长度太长，请输入少于15位数字", "originPasswordError": "原密码错误"}, "sysRoleList": {"title": "角色列表", "id": "编号", "name": "名称", "userCount": "用户数量", "description": "描述", "delete": "删除", "addRole": "添加角色", "rolenameNull": "名称不能为空", "selectRole": "请选择角色!", "deleteRole": "确认删除此角色吗?", "queryResultNull": "查询结果为空!", "unableDel_user": "该角色有无法删除的子用户!", "operaConfig": "操作配置", "roleNameExist": "角色名称已经存在!"}, "meterConfigurationList": {"systemFunction": "系统功能", "title": "操作配置", "id": "编号", "isSelect": "选择电表", "operationname": "操作项", "description": "描述"}, "organizationList": {"title": "组织机构列表", "addOrganization": "添加组织机构", "delete": "删除", "name": "名称", "id": "编号", "userCount": "用户数量", "meterCount": "电表数量", "mobile": "电话号码", "address": "地址", "description": "描述", "contactMan": "联系人"}, "sysOrganizationList": {"id": "编号", "parentOrg": "上级组织机构", "name": "名称", "mobile": "电话号码", "address": "地址", "description": "描述", "unableDel_org": "组织中有无法删除的子组织机构!", "unableDel_comm": "组织机构中有无法删除的子集中器!", "unableDel_meter": "组织机构有无法删除的子电表!", "unableDel_user": "组织机构有无法删除的子用户!", "moveOrg": "行动组织机构", "moveOrgError": "无法选择组织机构本身!", "selectOrg": "请选择一个组织机构!", "organization": "组织机构", "addOrganization": "添加组织机构", "moveSuccess": "移除成功", "admitFourLevelOrg": "最多允许创建4个级别的组织机构!", "deleteOrg": "确认删除该组织机构?", "noMoveToChildOrg": "组织是不允许转移到下属组织的!"}, "device_read_result_list": {"id": "编号", "sn": "编号", "profileId": "曲线编号", "profileName": "曲线名称", "communication": "通讯类型", "requestTime": "请求时间", "dataChannel": "数据通道", "value": "价值", "reponseTime": "数据时间", "startTime": "开始时间", "endTime": "结束时间", "status": "状态", "statusView": "状态", "export": "输出", "print": "打印"}, "device_read_schedule_list": {"id": "编号", "sn": "编号", "model": "型号", "communication": "通讯类型", "profileId": "配置编号", "profile": "配置", "startTime": "开始时间", "endTime": "结束时间"}, "device_read_channel_list": {"title": "结果", "checkReadTitle": "单击读取按钮进行读取", "id": "编号", "sn": "电表编号", "communicator": "集中器编号", "commmunication": "通讯", "dataChannel": "数据项", "value": "数值", "status": "状态", "statusView": "状态", "requestTime": "请求时间", "responseTime": "响应时间", "export": "输出", "print": "打印", "total": "总计", "completed": "完成", "success": "成功", "failed": "失败", "processing": "正在处理", "read": "读取", "cancel": "取消", "cancelFailure": "取消失败"}, "deploymentAndClusterManagement": {"title": "部署和集群管理", "serviceConfig": "服务器和服务配置", "channelService": "配置详细", "name": "名称", "ip": "IP", "hostId": "主机ID", "server": "服务器", "type": "类型", "ipOrHostId": "IP/主机 ID", "status": "状态", "properties": "属性", "value": "价值", "description": "描述", "addServer": "添加服务器", "addService": "添加服务", "deleteServer": "删除服务器", "deleteService": "删除服务", "action": "操作", "save": "保存", "editChannel": "编辑通道", "unableDel_server": "服务器子服务,不能删除!", "unableDel_service": "服务子属性,不能删除!", "serviceExistSystem": "这服务系统中已经存在!", "serviceExistServer": "这个服务已经存在于服务器!", "ipExistSystem": "这个ip系统中已经存在!", "hostIdExistServer": "这主机id已经存在于服务器!", "pleaseSelectService": "请选择服务!", "pleaseAddChannel": "请添加频道服务列表!", "nameExist": "这名字已经存在于系统!", "stop": "停止", "Running": "运行中", "channel": "采集曲线参数", "messageBus": "消息总线", "schedule": "安排", "UCI": "UCI", "calculation": "计算", "application": "应用程序"}, "serviceConfig": {"title": "服务配置", "id": "编号", "parent": "父级ID", "serviceTypeTemp": "服务器类型", "introduction": "名称", "serviceType": "服务器类型", "ip": "IP/主机 ID", "isOnline": "状态", "delete": "删除"}, "meterDataEventExport": {"title": "电表数据和事件导出", "dataChannel": "数据采集曲线参数", "exportProgress": "输出进展", "exportTime": "输出时间", "result": "结果", "unableDel_server": "服务器子服务,不能删除!", "unableDel_service": "服务器子属性,不能删除!", "serviceExistSystem": "这服务在系统中已经存在!", "serviceExistServer": "这个服务已经存在于服务器!", "ipExistSystem": "这个ip系统中已经存在!", "hostIdExistServer": "这主机id已经存在于服务器!", "pleaseSelectService": "请选择服务!", "pleaseAddChannel": "请添加采集曲线参数服务列表!", "nameExist": "这名字已经存在于系统!"}, "stepTariffList": {"title": "阶梯费率列表", "titleDetails": "阶梯费率详细", "id": "数量", "stepTariff": "阶梯费率", "description": "描述", "groupName": "组名", "stepName": "阶梯费率名称", "meterStandard": "电表规约", "startQuantity": "开始电量(kWh)", "meterNumber": "电表数量", "endQuantity": "结束电量(kWh)", "activateTime": "激活时间", "price": "价格", "referenceGroup": "参照组", "addGroup": "添加阶梯费率组", "addStep": "添加阶梯费率", "pleaseSelectData": "请这个列表中选择一个阶梯费率!", "nameExist": "该阶梯费率组名称已存在!", "stepNameExist": "该阶梯费率名称已存在!", "selectStepTariff": "请选择一个阶梯费率组!", "deleteStepTariff": "确认删除该阶梯费率组吗?", "selectStep": "请选择一个阶梯费率!", "delStep": "确认删除该阶梯费率吗?", "endGreaterThanStart": "结束电量必须大于开始电量!", "pleaseAddStep": "请添加阶梯费率列表!", "pleaseCorrectFormat": "请输入正确的格式数据!", "reselectStepName": "请重新选择阶梯费率!", "startQuantityError": "应与上一步的结束电量一致!", "delete": "删除", "save": "保存", "stepTariffGetPtogress": "阶梯费率进程", "friendly": "友好时段", "prepay": "预付费", "touTariff": "分时费率", "friendlyPeriod": "友好时段", "friendlyWeekDay": "友好工作日", "friendlySpecialDay": "友好特殊日", "friendlyTou": "友好分时段", "periodId": "预付费编号", "start": "开始", "end": "结束", "weekDayId": "工作日编号", "weekDay": "工作日", "specialDayId": "特殊日编号", "enable": "启动", "touId": "分时编号", "prepayDetail": "预付款明细", "touAtMost": "最多可以创建4个分时关税", "weekAtMost": "最多可以创建7天", "periodAtMost": "最多允许创建8个时段", "startLaterEnd": "开始时间必须晚于结束时间!", "touExist": "已存在", "periodRepeat": "友好时段不能重叠", "stepExist": "价格已存在，无法添加TOU价格", "cannotOp": "不能超过激活时间"}, "meterConfiguration": {"title": "电表参数设置", "canNotBeDelete": "任务处理状态不能删除!", "pleaseSelectData": "请这个列表中选择一个任务!", "confirmDeleteTask": "你确定删除这个任务?", "stepNameExist": "这一步名字已经添加,请选择另一个!"}, "meterGroupUpgrade": {"plan": "计划", "planReport": "计划报表", "jobReport": "项目报表", "deviceSearch": "设备搜索", "groupType": "组类型", "group": "组", "measurement": "曲线分组", "TOU": "分时费率分组", "limiter": "门限", "stepTariff": "阶梯费率分组", "friendly": "友好", "search": "搜索", "deviceSN": "电表编号", "pleSelGroupType": "请选择组类型!", "pleSelGroup": "请选择组!", "pleEntPlanDesc": "请输入计划描述!", "noDeviceFound": "请检查当前执行的方案和电表配置的一样!", "startEqualExpiry": "计划到期时间必须在计划开始时间!", "taskStartEqualEnd": "任务开始时间必须等于或晚于结束时间!", "ifExried": "如果已过期", "valid": "有效的", "expired": "已过期", "startTime": "开始时间", "endTime": "结束时间", "noFoundPlan": "无计划!", "pleaseSelectAplan": "请选择一个计划!", "createPlanSucc": "创建成功,新创建的计划计划报表中可以查看选项卡.", "exceptionMsg": "工作服务的调用异常,请检查并创建计划!", "jobUpgradeSucc": "这个计划的所有工作已经升级成功!", "all": "全部", "pleaseSelModel": "请选择地电表类型!"}, "divMGU_PlanMeterList": {"sn": "电表编号", "manufacturer": "厂商", "model": "型号", "groupType": "组类型", "group": "组", "startTime": "计划开始时间", "expiryTime": "计划到期时间"}, "MGU_planReportList": {"opt": "操作", "introduction": "计划描述", "groupType": "组类型", "groupName": "组名称", "model": "型号", "state": "状态", "taskCycle": "任务周期", "done": "已完成", "expired": "已过期", "running": "正在运行", "waiting": "等待中", "startTime": "计划开始时间", "expiryTime": "计划期时间", "taskStartTime": "任务开始时间", "taskEndTime": "任务到期时间"}, "MGU_planReportJobList": {"title": "项目", "id": "ID", "meterId": "电表ID", "sn": "电表编号", "manufacturer": "厂商", "model": "电表类型", "status": "状态", "lastExecTime": "上次操作时间", "reason": "理由", "running": "正在运行", "done": "已完成", "cancel": "已取消", "waiting": "正在等待", "expired": "已过期", "export": "输出", "print": "打印", "failedReason": "理由", "addPlan": "添加计划"}, "connOrDisconnList": {"title": "结果", "mainTitle": "合闸/拉闸", "sn": "电表编号", "commSN": "通讯编号", "communication": "通讯类型", "command": "命令", "status": "状态", "requestTime": "请求时间", "responseTime": "响应时间", "connect": "合闸", "disconnect": "拉闸", "pleSelMeter": "请选择电表!", "export": "输出", "print": "打印", "readTimeOut": "超时", "failReason": "原因", "relayOptional": "继电器可选", "internal": "主要继电器", "external": "扩展继电器"}, "billingReportList": {"reports": "报表", "reportList": "报表列表", "startTime": "开始时间", "endTime": "结束时间", "organization": "组织机构", "lineLossObjectType": "类型", "timeType": "时间类型", "lineLossObject": "线损对象", "import": "输入(kWh)", "export": "输出(kWh)", "loss": "线损(kWh)", "rate": "比率(25%)", "date": "日期", "lineLossStartTimeTip": "请选择线损开始时间!", "lineLossEndTimeTip": "请选择线损结束时间!", "timeIssueAlert": "计划结束时间必须在线损开始时间!", "transformer": "变压器", "line": "线路", "meter": "电表", "communicator": "通讯", "daily": "日", "monthly": "月", "title": "客户账单报表", "sn": "电表编号", "time": "数据时间", "energy": "能源消耗(kWh)", "consumption": "能源金额(美元)", "rateOther": "比率(%)", "serach": "搜索", "reportTime": "报表时间", "assetSelectTip": "请先选择档案!"}, "lineLossReportList": {"reportList": "报表列表", "startTime": "开始时间", "endTime": "结束时间", "organization": "组织机构", "lineLossObjectType": "类型", "timeType": "时间类型", "lineLossObject": "线损对象", "import": "输入(kWh)", "export": "输出(kWh)", "loss": "损失(kWh)", "rate": "利率(25%)", "date": "日期", "lineLossStartTimeTip": "请选择线损开始时间!", "lineLossEndTimeTip": "请选择线损结束时间!", "timeIssueAlert": "计划结束时间必须在线损开始时间之后!", "transformer": "变压器", "line": "线", "meter": "电表", "communicator": "通讯", "daily": "日", "monthly": "月", "title": "线损报表", "rateOther": "比率(%)", "type": "类型", "meterName": "电表名称", "tv": "时间值", "name": "数据采集曲线参数", "dataValue": "数据值", "objectName": "线损对象", "entity": "实体", "entityName": "实体名称", "objectComparison": "Object Comparison", "yoyComparison": "<PERSON><PERSON>on", "qoqComparison": "QoQ Comparison", "cancelComparison": "Cancel Comparison", "yoy": "YoY", "qoq": "QoQ"}, "importAndExportReport": {"organization": "组织机构", "timeType": "时间类型", "dataType": "数据类型", "startTime": "开始时间", "endTime": "结束时间", "result": "结果", "time": "时间", "value": "值(kWh)", "meterSn": "电表编号", "import": "输入", "export": "输出", "detail": "详细", "noRecords": "没有记录", "timeNotExist": "时间表不存在!", "title": "供应统计报表"}, "lineManagementList": {"title": "线路", "lineSn": "线路编号", "lineName": "线路名称", "lineList": "线路列表", "name": "名称", "organization": "组织机构", "type": "类型", "voltageLevel": "电压等级", "properties": "属性", "basicInformation": "基本信息", "transformers": "变压器", "calculationObject": "计算对象", "sn": "编号", "transformerList": "变压器列表", "transformerSn": "变压器编号", "calObjList": "计算对象列表", "calculationObjectName": "计算对象名称", "cycle": "周期", "calculationObjectProperties": "计算对象配置", "dataChannelList": "数据采集曲线参数列表", "meterSn": "电表编号", "meterName": "电表名称", "dataChannel": "数据采集曲线参数", "addDataChannel": "添加数据采集曲线参数", "addDataChannelFromTransformers": "在变压器添加数据采集曲线参数", "addLine": "添加线路", "opt": "操作", "busbar": "母线", "feeder": "支线", "unit": "伏特", "meterNotExist": "没有发现电表!", "noCalObjFound": "没找到计算对象!", "nolineFound": "没有发现线路!"}, "transformerManagementList": {"title": "变压器", "transformerSn": "变压器编号", "transformerName": "变压器名称", "transformerList": "变压器列表", "name": "名称", "organization": "组织机构", "ratedCapacity": "额定容量", "properties": "属性", "basicInformation": "基本信息", "calculationObject": "计算对象", "sn": "编号", "address": "地址", "calculationObjectList": "计算对象列表", "calculationObjectName": "计算对象名称", "type": "类型", "cycle": "周期", "calculationObjectProperties": "计算对象属性", "dataChannelList": "数据采集曲线参数列表", "meterSn": "电表编号", "meterName": "电表名称", "dataChannel": "数据采集曲线参数", "addDataChannel": "添加数据采集曲线参数", "noTrFound": "没有发现变压器!"}, "calculationObjectList": {"define": "计算对象定义", "title": "计算对象", "name": "名称", "type": "类型", "cycle": "周期", "entityType": "实体类型", "entityName": "实体名称", "calculationObject": "计算对象", "properties": "属性", "calculationObjectList": "计算对象列表", "meterSn": "电表编号", "dataChannel": "数据项", "addDataChannel": "添加数据项", "lineLoss": "线损", "import": "供电量", "export": "售电量", "daily": "日", "monthly": "月", "line": "线路 ", "transformer": "变压器", "organization": "组织机构", "pleaseChoose": "---请选择---", "all": "全部", "addBySearch": "按搜索添加", "addFromRelationship": "从关联中添加", "addMeteringPoint": "添加电表试点", "defaultType": "默认类型", "defaultDataChannel": "默认数据项", "gridLossReport": "电网损失报表", "rangeOfLossRate": "损失率范围(%)", "reference": "参考"}, "dcuConfiguration": {"title": "集中器参数设置", "meterListUpload": "电表参数列表", "otherParameter": "其他参数", "communicatorSn": "集中器编号", "set": "下发", "meterTitle": "电表列表", "meterTitleHES": "电表列表(系统侧)", "meterTitleDCU": "电表列表(集中器侧)", "meterSn": "电表编号", "pointNum": "点数", "logicalName": "物理地址", "baudRate": "波特率", "comNum": "通信号码", "isVip": "关键用户", "protocol": "协议", "communicatorAddress": "通讯地址", "result": "结果", "communication": "通讯类型", "status": "状态", "requestTime": "请求时间", "responseTime": "响应时间", "reason": "原因", "get": "抄读", "hesCommunicationParameter": "前置机通讯参数", "gprsNetworkParameter": " GPRS网络参数", "clock": "时间", "synchronizationCurrentTime": "同步当前时间", "concentratorVersion": "集中器版本信息", "encryptionMode": "加密模式", "encryptionType": "加密类型", "encryptionKey": "加密密钥", "globalEncryption": "全局加密", "dedicatedEncryption": "专用加密", "unencryted": "未加密", "ipAddress": "IP地址", "port": "端口", "userName": "用户名", "password": "密码", "dialedNumber": "联系电话", "ipAddressBackup": "Ip地址备份", "portBackup": "端口备份", "apn": "接入点名称", "resetCommand": "重置命令", "resetType": "重置类型", "pleaseChooseCommSn": "Please choose comm sn!", "pleaseFillIn": "请填写资料!", "pleaseFillInCorrect": "请填写正确信息!", "pleaseChoose": "---请选择---", "hardwareReset": "硬件重启", "dataBaseInit": "数据库初始化", "parameterResetExceptComm": "参数重置(集中器除外)", "dataReset": "数据重置", "parameterAndDataReset": "参数和数据重置", "parameterExceptHesReset": "参数(除了前置设备通信参数)重置", "importantUserSettingList": "重点用户设置", "meterModel": "电表型号", "importantFlag": "重要标志", "command": "命令", "resetTypeCannotBeRead": "无法读取重置类型", "selectCommunicators": "请选择集中器", "selectImportantUser": "请选择您想要设置为重要用户的电表", "selectResetType": "请选择重置类型", "wiringMethod": "接线方式", "comType": "通讯类型", "versionInfo": "版本信息", "equipmentNumber": "设备号", "softwareVersion": "软件版本", "softwareDate": "软件日期", "softwareItem": "软件项目", "communicationProtocol": "通信规约", "hardwareVersion": "硬件版本", "hardwareDate": "硬件设备", "versionInformationWrite": "不能写入集中器版本信息.", "importUserPleaseSelect": "请选择要设置为重要用户的电表.", "indexDcu": "采集序号", "heartbeatCycle": "波动周期", "heartbeatCycleUnit": "波动周期（单位：秒）", "energyProfileReadingCycle": "电量读取周期", "energyProfileReadingCycleUnit": "电量读取周期（单位：秒）", "plcChannelSendTimes": "PLC通道发送时间", "plcChannelSendTimesUnit": "PLC通道发送时间（单位：秒）", "plcChannelTimeout": "PLC通道超时", "plcChannelTimeoutUnit": "PLC通道超时（单位：秒）", "concentratorAddress": "集中器地址", "managementLogicalDeviceName": "逻辑设备管理名称”，", "dormancyTime": "休眠时间", "dormancyTimeUnit": "休眠时间（单位：秒）", "concentratorIpAddress": "集中器IP地址", "eventConfiguration": "事件配置", "broadcastTimeOrderEnable": "启用广播时间顺序", "broadcastTimeParameter": "广播时间参数", "gprsSignalStrength": "GPRS信号强度", "gprsImeiSerialNumber": "GPRS IMEI序列号", "gprsNetworkStandard": "GPRS网络标准", "wanDhcpEnable": "广域网DHCP启用", "csMode": "C/S模式", "serverPort": "服务器端口", "gprsModemVersion": "GPRS调制解调器版本", "plcModemVersion": "PLC调制解调器版本", "rfModemVersion": "射频调制解调器版本", "blacklistOfMeter": "电表黑名单", "meterModuleSoftwareVersion": "电表模块软件版本", "dcuAsClient": "Dcu作为客户", "dcuAsServer": "Dcu作为服务器", "terminalIp": "终端IP", "subnetMask": "子网掩码", "gatewayMask": "网关掩码", "ItemDontWrite": "该项目不写入", "blackFlag": "黑标志", "pleaseCsMode": "请选择C / S模式！", "broadcastKey": "广播密钥", "firmwareVersionLaterThan": "固件版本晚于1.39"}, "importGprsMeter": {"importFromShipmentFile": "电表批量导入文件", "importGprsMeter": "批量导入文件", "num1": "1", "num2": "2", "num3": "3", "num4": "4", "step1": "第一步", "step2": "第二步", "step3": "第三步", "step4": "第四步", "assetType": "资产类型", "GPRSMeter": "GPRS电表", "assetFile": "资产文件", "manufacturer": "厂商", "model": "型号", "firmwareVersion": "电表版本", "measurementGroup": "曲线参数分组", "collectionSchemeGroup": "Collection Scheme Group", "meterExist1": "具有编号的电表 ", "meterExist2": " 已经存在.", "checkProgress": "检查进度", "importProgress": "导入进度", "selectFile": "选择文件", "checkStatus": "验证Excel文件，请勿切换!", "pleaseSelectFile": "请选择Excel文件!", "noDataInExcel": "Excel文件中没有数据!", "verificationFailed": "验证失败，请检查并重新导入!", "pleaseSelMeaGroup": "请选择曲线!", "pleaseSelColSchGroup": "Please select Collection Scheme Group!", "checkCompleted": "检查完成!", "importStatus": "导入Excel文件，请勿切换!", "deviceTypeIsEmpty": "逻辑设备名为空!", "pleaseSelCommType": "请选择通讯类型!", "shipmentFileType": "批量导入文件类型", "meterSipmentFile": "电表批量导入文件", "shipmentFile": "批量导入文件", "verifySuccess": "验证成功", "verifyFail": "验证失败"}, "customerList": {"customer": "客户", "customerList": "客户列表", "customerSn": "客户编号", "customerType": "客户类型", "customerName": "客户名称", "industryType": "行业类型", "onlyOneError": "您选择该电表或输入的客户编号已经被其他客户指定。 "}, "dataComminicationStatus": {"title": "通信状态报表", "statusUpdateTime": "状态更新时间", "networkAddress": "网络地址", "commComType": "集中器通讯类型", "meterComType": "电表通讯类型", "offlineTimeDays": "离线时间 (天)", "offline": "离线", "online": "在线"}, "saleStatReport": {"missData": "丢失数据"}, "veeList": {"validatingReport": "验证报表", "exception tatal": "异常数量合计", "class1": "类别1", "class2": "类别2", "class3": "类别3", "class4": "类别4", "class5": "类别5", "veeGroup": "数据校验组", "veeGroupList": "数据校验组列表", "groupName": "组名称", "meterCount": "电表数量", "descr": "说明", "veeRuleList": "数据校验规则列表", "ruleName": "校验规则名称", "ruleType": "校验规则类型", "class": "类别", "dataChannel": "数据采集曲线参数", "event": "事件", "method": "方式", "estimationReportProgressDelay": "评估报表 -进度延迟", "estimationReportMissData": "评估报表 -缺失数据", "meterSn": "电表编号", "profile": "曲线名称", "communicatorSn": "集中器编号", "communicationType": "通讯类别", "progress": "进度", "progressDelay": "进度延迟", "time": "时间", "missDataListDetail": "数据缺失列表明细", "missDataList": "数据缺失列表", "progressDelayList": "进度延迟列表", "progressDelayListDetail": "进度延迟列表明细", "serizlNo": "设备编号", "missData": "缺失数据", "deadline": "截止日期", "delayExported": "只能导出延迟数据", "missExported": "只能导出缺失数据", "rowExport": "选择要导出的行", "taskTv": "任务时间", "ruleStatus": "规则状态", "ruleClass": "规则类别", "editVeeRule": "修改", "addVeeRule": "添加", "pleSeleVeeGroup": "请选择数据校验组!", "pleFillName": "名称不能为空!", "pleCycleCount": "循环数量是数字!", "eventRuleDesc": "时间规则描述", "dataItemName": "数据项名称", "dataItemKey": "关键数据项", "cycleCount": "循环数量", "cycleType": "循环周期", "paramKey": "关键参数", "defaultValue": "参数值", "dataItemList": "数据项列表", "veeEventParamList": "参数列表"}, "dict": {"Reports": "报表", "Cusomer Billing Report": "客户账单报表", "Line Loss Report": "线路损耗报表", "Supply Statistics Report": "供电统计报表", "Sales Statistics Report": "销售统计报表", "Interval Data Report": "供电质量报表", "Non-Residential": "非居民", "Residential": "居民", "Production": "生产业", "Farming": "农业", "Animal Husbandry": "畜牧业", "Fishery": "渔业", "Mining": "矿业", "Lodging & Catering": "住宿和餐饮业", "Minutely": "分钟", "Hourly": "小时", "Daily": "日", "Monthly": "月", "Yes": "是", "No": "否", "Data Profile": "数据曲线", "Event Profile": "事件曲线"}, "menu": {"Data Collection": "数据采集", "Tools": "工具", "System": "系统", "Provisioning": "安装配置", "Meter Data Report": "电表数据查询", "Meter Event Report": "电表事件查询", "Schedule Reads Report": "采集完整率报表", "Miss Data Tracing": "缺失数据跟踪", "Collection Scheme Management": "采集方案管理", "Meter Group Management": "电表分组管理", "Meter Configuration": "电表参数设置", "DCU Configuration": "集中器参数设置", "Firmware Upgrade": "电表固件升级", "On Demand Reads": "随时抄表", "Connect / Disconnect": "合闸/拉闸", "Asset Management": "档案管理", "Deployment Management": "部署管理", "Log Explorer": "日志浏览", "Permission": "权限管理", "Data Export Management": "数据推送管理", "Meter Group Upgrade": "电表参数批量更新", "Data Management": "数据管理", "DCU Configuration - SG376": "集中器参数设置-376", "On Demand Reads - CSG": "随时抄表", "Calculation Object Define": "计算对象定义", "VEE Management": "数据校验管理", "Reports": "报表", "Communication Status Report": "通讯状态报表", "Grid Loss Management": "网损管理", "Template Reports": "自定义报表"}, "dictDataItemGroup": {"Energy": "中Energy", "Load Profile Minutely": "中Load Profile Minutely", "TOU": "分时费率", "Standard Event Log": "标准日志文件"}, "dictDataItem": {"All selected": "全选", "selectAll": "全选", "Passive Calendar": "集中器集中", "Active energy import (+A) (interval) (Daily) [Unit: kWh]": "正向有功总电能(日) [单位: kWh]", "Active energy import (+A) (interval) (Monthly) [Unit: kWh]": "正向有功总电能(月) [单位: kWh]", "Active energy export (-A) (interval) (Monthly) [Unit: kWh]": "反向有功总电能(月) [单位: kWh]", "Active energy import (+A) rate1 (interval) (Daily) [Unit: kWh]": "正向有功总电能费率1 (日) [单位: kWh]", "Active energy import (+A) rate2 (interval) (Daily) [Unit: kWh]": "正向有功总电能费率2 (日) [单位: kWh]", "Active energy import (+A) rate3 (interval) (Daily) [Unit: kWh]": "正向有功总电能费率3 (日) [单位: kWh]", "Active energy import (+A) rate4 (interval) (Daily) [Unit: kWh]": "正向有功总电能费率4 (日) [单位: kWh]", "Active energy export (-A) (interval) (Daily) [Unit: kWh]": "反向有功总电能(日) [单位: kWh]", "Active energy export (-A) rate1 (interval) (Daily) [Unit: kWh]": "反向有功总电能费率1 (日) [单位: kWh]", "Active energy export (-A) rate2 (interval) (Daily) [Unit: kWh]": "反向有功总电能费率2 (日) [单位: kWh]", "Active energy export (-A) rate3 (interval) (Daily) [Unit: kWh]": "反向有功总电能费率3 (日) [单位: kWh]", "Active energy export (-A) rate4 (interval) (Daily) [Unit: kWh]": "反向有功总电能费率4 (日) [单位: kWh]", "Reactive energy import (+R)(QI+QII) (interval) (Daily) [Unit: kVarh]": "正向无功总电能 (日) [单位: kVarh]", "Reactive energy import (+R)(QI+QII) rate1 (interval) (Daily) [Unit: kVarh]": "正向无功总电能费率1 (日) [单位: kVarh]", "Reactive energy import (+R)(QI+QII) rate2 (interval) (Daily) [Unit: kVarh]": "正向无功总电能费率2 (日) [单位: kVarh]", "Reactive energy import (+R)(QI+QII) rate3 (interval) (Daily) [Unit: kVarh]": "正向无功总电能费率3 (日) [单位: kVarh]", "Reactive energy import (+R)(QI+QII) rate4 (interval) (Daily) [Unit: kVarh]": "正向无功总电能费率4 (日)[单位: kVarh]", "Reactive energy export (-R)(QIII+QIV) (interval) (Daily) [Unit: kVarh]": "反向无功总电能 (日) [单位: kVarh]", "Reactive energy export (-R)(QIII+QIV) rate1 (interval) (Daily) [Unit: kVarh]": "反向无功总电能费率1 (日) [单位: kVarh]", "Reactive energy export (-R)(QIII+QIV) rate2 (interval) (Daily) [Unit: kVarh]": "反向无功总电能费率2 (日) [单位: kVarh]", "Reactive energy export (-R)(QIII+QIV) rate3 (interval) (Daily) [Unit: kVarh]": "反向无功总电能费率3 (日) [单位: kVarh]", "Reactive energy export (-R)(QIII+QIV) rate4 (interval) (Daily) [Unit: kVarh]": "反向无功总电能费率4 (日) [单位: kVarh]", "Active energy import (+A) [Unit: kWh]": "正向有功总电能 [单位: kWh]", "Active energy export (-A) [Unit: kWh]": "反向有功总电能 [单位: kWh]", "Reactive energy import (+R) (QI+QII) [Unit: kVarh]": "正向无功总电能  [单位: kVarh]", "Reactive energy export (-R) (QIII+QIV) [Unit: kVarh]": "反向无功总电能  [单位: kVarh]", "Reactive energy QI (+Rl) [Unit: kVarh]": "一象限无功 [单位: kVarh]", "Reactive energy QII (+Rc) [Unit: kVarh]": "二象限无功 [单位: kVarh]", "Reactive energy QIII (-Rl) [Unit: kVarh]": "三象限无功 [单位: kVarh]", "Reactive energy QIV (-Rc) [Unit: kVarh]": "四象限无功 [单位: kVarh]", "Watchdog Error": "中中Watchdog Error", "Normal Voltage L1": "中中Normal Voltage L1", "Export active demand [Unit:kW]": "中文Export active demand [Unit:kW]", "Clock": "中文Clock", "Under Limit Threshold of miss Voltage [Unit: V]": "中中Under <PERSON><PERSON> of miss Voltage [Unit: V]"}, "profile": {"Interval Profile": "中文Interval Profile", "Energy Billing Profile Jingxiang (Daily)": "中中Energy Billing Profile <PERSON> (Daily)", "Standard Event Log": "中文Standard Event Log", "Time Synchronization": "中文Time Synchronization"}, "dcuConfiguration300": {"title": "测量点参数管理", "communicatorSn": "集中器资产编号", "measurementPointRange": "测量点范围", "meterTitleHES": "计量点列表", "communication": "communication", "meterSn": "资产编号", "pointNum": "测量点编号", "status": "状态", "meterProperties": "表计性质", "mac": "测量点地址", "meterType": "电能表类型", "totalDivType": "总分类型", "isVip": "重点用户属性", "feeRateCount": "最大费率数", "collectorAddress": "采集器地址", "comNum": "端口号", "taChange": "TA变化", "tvChange": "TV变化", "command": "命令", "reason": "原因", "requestTime": "请求时间", "responseTime": "响应时间", "protocol": "规约", "meterTitleDCU": "表计列表 (从DCU读取)", "seleCommunicator": "请选择集中器！", "seleLeastOne": "至少选择表中的一条数据！", "pleaseEnterPositiveInt": "请输入正整数", "beginGreaterThanEnd": "起始测量点必须大于终止测量点", "baudRate": "波特率", "stopFlag": "停止位"}, "courtsTopology": {"title": "台区拓扑"}, "reportDesigner": {"reportDesigner": "报表设计", "reportManagement": "报表管理", "reportExploer": "报表浏览", "reportManagementList": "报表管理列表", "reportType": "报表类型", "reportName": "报表名称", "orgName": "组织机构名称", "templateFile": "自定义文件"}}