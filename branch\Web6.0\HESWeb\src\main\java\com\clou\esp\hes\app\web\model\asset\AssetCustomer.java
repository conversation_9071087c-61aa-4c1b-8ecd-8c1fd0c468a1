/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetCustomer{ } 
 * 
 * 摘    要： assetCustomer
 * 版    本：1.0
 * 作    者：赵久霆
 * 创建于：2018-11-13 06:49:07
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.asset;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class AssetCustomer  extends BaseEntity {

	private static final long serialVersionUID = 6588523698852737912L;
	public AssetCustomer() {}

	private 	String 		sn;
	private 	String 		name;
	private 	String 		orgId;
	private 	String 		meterId;
	private 	String 		customerType;
	private 	String 		industryType;
	private 	String 		telephoneNum;
	private 	String 		addr;
	private     String      meterSn;
	private     String      orgName;
	private 	String 		customerTypeName;
	private 	String 		industryTypeName;
	
	/**是否收藏*/
	private Boolean isCollect;


	public Boolean getIsCollect() {
		return isCollect;
	}

	public void setIsCollect(Boolean isCollect) {
		this.isCollect = isCollect;
	}
	
	public String getCustomerTypeName() {
		return MutiLangUtil.assemblyI18nData(MutiLangUtil.DICT_I18N,customerTypeName);
	}

	public void setCustomerTypeName(String customerTypeName) {
		this.customerTypeName = customerTypeName;
	}

	public String getIndustryTypeName() {
		return MutiLangUtil.assemblyI18nData(MutiLangUtil.DICT_I18N,industryTypeName);
	}

	public void setIndustryTypeName(String industryTypeName) {
		this.industryTypeName = industryTypeName;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getSn() {
		return sn;
	}

    public void setSn(String sn) {
		this.sn = sn;
	}

    public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}
	
	public String getCustomerType() {
		return customerType;
	}
	
	public void setCustomerType(String customerType) {
		this.customerType = customerType;
	}
	public String getMeterId() {
		return meterId;
	}
	
	
    public void setMeterId(String meterId) {
		this.meterId = meterId;
	}
    
	public String getIndustryType() {
		return industryType;
	}

	public void setIndustryType(String industryType) {
		this.industryType = industryType;
	}
	public String getTelephoneNum() {
		return telephoneNum;
	}

	public void setTelephoneNum(String telephoneNum) {
		this.telephoneNum = telephoneNum;
	}
	
	public String getAddr() {
		return addr;
	}

	public void setAddr(String addr) {
		this.addr = addr;
	}

	public String getMeterSn() {
		return meterSn;
	}

	public void setMeterSn(String meterSn) {
		this.meterSn = meterSn;
	}

	public AssetCustomer(String sn,String name,String orgId,String customerType,String meterId,String industryType,
			String telephoneNum,String addr,String meterSn) {
		super();
		this.sn = sn;
		this.name = name;
		this.orgId = orgId;
		this.customerType = customerType;
		this.meterId = meterId;
		this.industryType = industryType;
		this.telephoneNum = telephoneNum;
		this.addr = addr;
		this.meterSn=meterSn;
	}

}