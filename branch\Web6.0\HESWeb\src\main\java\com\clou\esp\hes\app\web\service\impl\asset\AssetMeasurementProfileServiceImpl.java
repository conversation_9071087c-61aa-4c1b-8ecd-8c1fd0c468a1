/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeasurementProfile{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-02-28 02:48:00
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.asset;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.asset.AssetMeasurementProfileDao;
import com.clou.esp.hes.app.web.model.asset.AssetMeasurementProfile;
import com.clou.esp.hes.app.web.service.asset.AssetMeasurementProfileService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("assetMeasurementProfileService")
public class AssetMeasurementProfileServiceImpl  extends CommonServiceImpl<AssetMeasurementProfile>  implements AssetMeasurementProfileService {

	@Resource
	private AssetMeasurementProfileDao assetMeasurementProfileDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(assetMeasurementProfileDao);
    }
	@SuppressWarnings("rawtypes")
	public AssetMeasurementProfileServiceImpl() {}
	
	@Override
	public Integer deleteByEntity(AssetMeasurementProfile entity) {
		return assetMeasurementProfileDao.deleteByEntity(entity);
	}

	@Override
	public AssetMeasurementProfile getProfileByProfileid(String profileId) {
		return assetMeasurementProfileDao.getProfileByProfileid(profileId);
	}

	@Override
	public AssetMeasurementProfile getProfileByMgAndDataItemAndType(String mgId, String dataItemId, String type) {
		return assetMeasurementProfileDao.getProfileByMgAndDataItemAndType(mgId, dataItemId, type);
	}
	
	
}