/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeter{ } 
 * 
 * 摘    要： assetMeter
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-06 08:04:55
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.asset;

import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.google.common.collect.Maps;

public class AssetMeterDto extends BaseEntity {

	private static final long serialVersionUID = 7736108698525751744L;
	public  AssetMeterDto() {};
	
	private  String   meterSn;
	private  String   commSn;
	private  String   customerSn;
	private  String   customerName;
	private  String   tranformName;
	private  String   lineName;
	
	
	public String getMeterSn() {
		return meterSn;
	}
	public void setMeterSn(String meterSn) {
		this.meterSn = meterSn;
	}
	public String getCommSn() {
		return commSn;
	}
	public void setCommSn(String commSn) {
		this.commSn = commSn;
	}
	public String getCustomerSn() {
		return customerSn;
	}
	public void setCustomerSn(String customerSn) {
		this.customerSn = customerSn;
	}
	public String getCustomerName() {
		return customerName;
	}
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}
	public String getTranformName() {
		return tranformName;
	}
	public void setTranformName(String tranformName) {
		this.tranformName = tranformName;
	}
	public String getLineName() {
		return lineName;
	}
	public void setLineName(String lineName) {
		this.lineName = lineName;
	}
	
	
	//[Edit Meter: Meter SN = liubeitest,DCU sn=123...]
	public String getLog(String operationName) {
		String rest="["+operationName+":"+	"Meter SN="+meterSn;
		if(StringUtils.isNotEmpty(commSn)) {
			rest+=",DCU SN="+commSn;
		}
		if(StringUtils.isNotEmpty(customerName)) {
			rest+=",Customer Name="+customerName;
		}
		if(StringUtils.isNotEmpty(customerSn)) {
			rest+=",Customer SN="+customerSn;
		}
		if(StringUtils.isNotEmpty(tranformName)) {
			rest+=",Transformer Name="+tranformName;
		}
		if(StringUtils.isNotEmpty(lineName)) {
			rest+=",Line Name="+lineName;
		}
		rest+="]";
		return rest;
	}
	
	public String getLogContent() {
		String rest="";
		if(StringUtils.isNotEmpty(commSn)) {
			rest+="DCU SN="+commSn;
		}
		if(StringUtils.isNotEmpty(customerName)) {
			rest+=",Customer Name="+customerName;
		}
		if(StringUtils.isNotEmpty(customerSn)) {
			rest+=",Customer SN="+customerSn;
		}
		if(StringUtils.isNotEmpty(tranformName)) {
			rest+=",Transformer Name="+tranformName;
		}
		if(StringUtils.isNotEmpty(lineName)) {
			rest+=",Line Name="+lineName;
		}
		return rest;
	}
	
	//logic name, model, communication type, IP, Port, Authentication Type
	public static String getCommEditLog(AssetCommunicator commOld, AssetCommunicator comm,
			Map<String,String> modelMap,Map<String,String> commMap) {
		Map<String,String>  authMap = Maps.newHashMap();
		authMap.put("0", "NO_SECURITY");
		authMap.put("1","LLS");
		authMap.put("2","HLS_2");
		authMap.put("3","HLS_MD5");
		authMap.put("4","HLS_SHA1");
		authMap.put("5","HLS_GMAC");
		
		String rest="";
		if(!comm.getMac().equals(commOld.getMac())) {
			rest+="Logic Name:"+comm.getMac()+"("+commOld.getMac()+"),";
		}
		
		if(!comm.getModel().equals(commOld.getModel())) {
			rest+="Model:"+modelMap.get(comm.getModel())+"("+modelMap.get(commOld.getModel())+"),";
		}
		
		
		if(!comm.getComType().equals(commOld.getComType())) {
			rest+="Communication Type:"+commMap.get(comm.getComType())+"("+commMap.get(commOld.getComType())+"),";
		}
		
		String ip = comm.getNetworkIp();
		if(StringUtils.isEmpty(ip)) {
			if(StringUtils.isNotEmpty(commOld.getNetworkIp())) {
				rest+="IP:Null"+"("+commOld.getNetworkIp()+"),";
			}
		}else {
			if(!ip.equals(commOld.getNetworkIp())) {
				rest+="IP:"+ip+"("+commOld.getNetworkIp()+"),";
			}
		}
		
		Integer port = comm.getNetworkPort();
		if(port==null) {
			if(commOld.getNetworkPort()!=null) {
				rest+="Port:Null"+"("+commOld.getNetworkPort()+"),";
			}
		}else {
			if(commOld.getNetworkPort()==null) {
				rest+="Port:"+port+"(Null),";
			}else if(port.intValue()!=commOld.getNetworkPort().intValue()){
				rest+="Port:"+port+"("+commOld.getNetworkPort()+"),";
			}
		}
		
		String authType = comm.getAuthType();
		if(StringUtils.isEmpty(authType)) {
			if(StringUtils.isNotEmpty(commOld.getAuthType())) {
				rest+="Authentication Type:Null"+"("+authMap.get(commOld.getAuthType())+"),";
			}
		}else {
			if(!authType.equals(commOld.getAuthType())) {
				rest+="Authentication Type:"+authMap.get(authType)+"("+authMap.get(commOld.getAuthType())+"),";
			}
		}
		
		
		if(rest.length()>0) {
			return "[Edit History:"+rest.substring(0, rest.length()-1)+"]";
		}else {
			return "";
		}
	}
	
	
	
	
	
	
	
	//logic name, model, key meter, CT, PT
	public static String getMeterEditLog(AssetMeter meterOld, AssetMeter meter,Map<String,String> modelMap) {
		String rest="";
		
		if(!meter.getMac().equals(meterOld.getMac())) {
			rest+="Logic Name:"+meter.getMac()+"("+meterOld.getMac()+"),";
		}
		
		if(!meter.getModel().equals(meterOld.getModel())) {
			rest+="Model:"+modelMap.get(meter.getModel())+"("+modelMap.get(meterOld.getModel())+"),";
		}
		
		
		if(meter.getKeyFlag()==null) {
			if(meterOld.getKeyFlag()!=null&&1==meterOld.getKeyFlag()) {
				rest+="Key Meter:No(Yes)";
			}
		}else{
			if(!meter.getKeyFlag().equals(meterOld.getKeyFlag())) {
				rest+="Key Meter:"+(1==meter.getKeyFlag()?"Yes":"No")+"("+(meterOld.getKeyFlag()!=null&&meterOld.getKeyFlag()==1?"Yes":"No")+"),";
			}
		}
		
		Integer ct = meter.getCt();
		if(ct==null) {
			if(meterOld.getCt()!=null) {
				rest+="CT:Null"+"("+meterOld.getCt()+"),";
			}
		}else {
			if(meterOld.getCt()==null) {
				rest+="CT:"+ct+"(Null),";
			}else if(ct.intValue()!=meterOld.getCt().intValue()){
				rest+="CT:"+ct+"("+meterOld.getCt()+"),";
			}
		}
		
		Integer pt = meter.getPt();
		if(pt==null) {
			if(meterOld.getPt()!=null) {
				rest+="PT:Null"+"("+meterOld.getPt()+"),";
			}
		}else {
			if(meterOld.getPt()==null) {
				rest+="PT:"+pt+"(Null),";
			}else if(pt.intValue()!=meterOld.getPt().intValue()){
				rest+="PT:"+pt+"("+meterOld.getPt()+"),";
			}
		}
		
		if(rest.length()>0) {
			return "[Edit History:"+rest.substring(0, rest.length()-1)+"]";
		}else {
			return "";
		}
//			
//		
//		if(StringUtils.isNotEmpty(meterOld.getMac())) {
//			
//		}
	}

}