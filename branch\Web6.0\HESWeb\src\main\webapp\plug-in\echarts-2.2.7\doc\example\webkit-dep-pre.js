var data = [ ["AbstractWorker",      "EventListener"], ["AbstractWorker",      "Event"], ["AnalyserNode",        "AudioNode"], ["AnalyserNode",        "Uint8Array"], ["AnalyserNode",        "Float32Array"], ["<PERSON><PERSON>yBuffer", "ArrayBuffer"], ["ArrayBufferView",     "ArrayBuffer"], ["Attr",        "Node"], ["Attr",        "Element"], ["AudioBuffer", "Float32Array"], ["AudioBufferCallback", "AudioBuffer"], ["AudioBufferSourceNode",       "AudioSourceNode"], ["AudioBufferSourceNode",       "AudioBuffer"], ["AudioBufferSourceNode",       "AudioGain"], ["AudioBufferSourceNode",       "AudioParam"], ["AudioContext",        "AudioDestinationNode"], ["AudioContext",        "AudioListener"], ["AudioContext",        "EventListener"], ["AudioContext",        "AnalyserNode"], ["AudioContext",        "BiquadFilterNode"], ["AudioContext",        "AudioBuffer"], ["AudioContext",        "AudioBufferSourceNode"], ["AudioContext",        "ChannelMergerNode"], ["AudioContext",        "ChannelSplitterNode"], ["AudioContext",        "ConvolverNode"], ["AudioContext",        "DelayNode"], ["AudioContext",        "DynamicsCompressorNode"], ["AudioContext",        "GainNode"], ["AudioContext",        "MediaElementAudioSourceNode"], ["AudioContext",        "MediaStreamAudioDestinationNode"], ["AudioContext",        "MediaStreamAudioSourceNode"], ["AudioContext",        "OscillatorNode"], ["AudioContext",        "PannerNode"], ["AudioContext",        "ScriptProcessorNode"], ["AudioContext",        "WaveShaperNode"], ["AudioContext",        "WaveTable"], ["AudioContext",        "ArrayBuffer"], ["AudioDestinationNode",        "AudioNode"], ["AudioGain",   "AudioParam"], ["AudioNode",   "AudioContext"], ["AudioNode",   "AudioNode"], ["AudioNode",   "AudioParam"], ["AudioParam",  "Float32Array"], ["AudioProcessingEvent",        "Event"], ["AudioProcessingEvent",        "AudioBuffer"], ["AudioSourceNode",     "AudioNode"], ["BatteryManager",      "EventListener"], ["BatteryManager",      "Event"], ["BeforeLoadEvent",     "Event"], ["BiquadFilterNode",    "AudioNode"], ["BiquadFilterNode",    "AudioParam"], ["BiquadFilterNode",    "Float32Array"], ["Blob",        "Blob"], ["CanvasRenderingContext",      "HTMLCanvasElement"], ["CanvasRenderingContext2D",    "CanvasRenderingContext"], ["CanvasRenderingContext2D",    "ImageData"], ["CanvasRenderingContext2D",    "CanvasGradient"], ["CanvasRenderingContext2D",    "CanvasPattern"], ["CanvasRenderingContext2D",    "HTMLImageElement"], ["CanvasRenderingContext2D",    "HTMLCanvasElement"], ["CanvasRenderingContext2D",    "HTMLVideoElement"], ["CanvasRenderingContext2D",    "TextMetrics"], ["CDATASection",        "Text"], ["ChannelMergerNode",   "AudioNode"], ["ChannelSplitterNode", "AudioNode"], ["CharacterData",       "Node"], ["ClientRectList",      "ClientRect"], ["Clipboard",   "FileList"], ["Clipboard",   "DataTransferItemList"], ["Clipboard",   "HTMLImageElement"], ["CloseEvent",  "Event"], ["Comment",     "CharacterData"], ["CompositionEvent",    "UIEvent"], ["Console",     "MemoryInfo"], ["ConvolverNode",       "AudioNode"], ["ConvolverNode",       "AudioBuffer"], ["Crypto",      "ArrayBufferView"], ["CSSCharsetRule",      "CSSRule"], ["CSSFontFaceRule",     "CSSRule"], ["CSSFontFaceRule",     "CSSStyleDeclaration"], ["CSSImportRule",       "CSSRule"], ["CSSImportRule",       "MediaList"], ["CSSImportRule",       "CSSStyleSheet"], ["CSSMediaRule",        "CSSRule"], ["CSSMediaRule",        "CSSRuleList"], ["CSSMediaRule",        "MediaList"], ["CSSPageRule", "CSSRule"], ["CSSPageRule", "CSSStyleDeclaration"], ["CSSPrimitiveValue",   "CSSValue"], ["CSSPrimitiveValue",   "Counter"], ["CSSPrimitiveValue",   "RGBColor"], ["CSSPrimitiveValue",   "Rect"], ["CSSRule",     "CSSRule"], ["CSSRule",     "CSSStyleSheet"], ["CSSRuleList", "CSSRule"], ["CSSStyleDeclaration", "CSSRule"], ["CSSStyleDeclaration", "CSSValue"], ["CSSStyleRule",        "CSSRule"], ["CSSStyleRule",        "CSSStyleDeclaration"], ["CSSStyleSheet",       "StyleSheet"], ["CSSStyleSheet",       "CSSRuleList"], ["CSSStyleSheet",       "CSSRule"], ["CSSUnknownRule",      "CSSRule"], ["CSSValueList",        "CSSValue"], ["CustomEvent", "Event"], ["Database",    "SQLTransactionCallback"], ["DatabaseCallback",    "Database"], ["DatabaseCallback",    "DatabaseSync"], ["DatabaseSync",        "SQLTransactionSyncCallback"], ["DataTransferItem",    "Blob"], ["DataTransferItem",    "StringCallback"], ["DataTransferItem",    "Entry"], ["DataTransferItemList",        "File"], ["DataTransferItemList",        "DataTransferItem"], ["DataView",    "ArrayBufferView"], ["DedicatedWorkerContext",      "WorkerContext"], ["DedicatedWorkerContext",      "EventListener"], ["DelayNode",   "AudioNode"], ["DelayNode",   "AudioParam"], ["DeviceMotionEvent",   "Event"], ["DeviceOrientationEvent",      "Event"], ["DirectoryEntry",      "Entry"], ["DirectoryEntry",      "DirectoryReader"], ["DirectoryEntry",      "VoidCallback"], ["DirectoryEntrySync",  "EntrySync"], ["DirectoryEntrySync",  "DirectoryReaderSync"], ["DirectoryEntrySync",  "DirectoryEntrySync"], ["DirectoryEntrySync",  "FileEntrySync"], ["DirectoryReader",     "EntriesCallback"], ["DirectoryReaderSync", "EntryArraySync"], ["Document",    "Node"], ["Document",    "HTMLCollection"], ["Document",    "HTMLElement"], ["Document",    "DOMWindow"], ["Document",    "DocumentType"], ["Document",    "Element"], ["Document",    "HTMLHeadElement"], ["Document",    "DOMImplementation"], ["Document",    "Location"], ["Document",    "EventListener"], ["Document",    "StyleSheetList"], ["Document",    "Range"], ["Document",    "Attr"], ["Document",    "CDATASection"], ["Document",    "Comment"], ["Document",    "DocumentFragment"], ["Document",    "EntityReference"], ["Document",    "Event"], ["Document",    "XPathExpression"], ["Document",    "XPathNSResolver"], ["Document",    "NodeIterator"], ["Document",    "ProcessingInstruction"], ["Document",    "Text"], ["Document",    "Touch"], ["Document",    "TouchList"], ["Document",    "TreeWalker"], ["Document",    "XPathResult"], ["Document",    "CanvasRenderingContext"], ["Document",    "NodeList"], ["Document",    "CSSStyleDeclaration"], ["Document",    "DOMSelection"], ["DocumentFragment",    "Node"], ["DocumentFragment",    "Element"], ["DocumentFragment",    "NodeList"], ["DocumentType",        "Node"], ["DocumentType",        "NamedNodeMap"], ["DOMApplicationCache", "EventListener"], ["DOMApplicationCache", "Event"], ["DOMFileSystem",       "DirectoryEntry"], ["DOMFileSystemSync",   "DirectoryEntrySync"], ["DOMImplementation",   "CSSStyleSheet"], ["DOMImplementation",   "Document"], ["DOMImplementation",   "DocumentType"], ["DOMImplementation",   "HTMLDocument"], ["DOMMimeType", "DOMPlugin"], ["DOMMimeTypeArray",    "DOMMimeType"], ["DOMParser",   "Document"], ["DOMPlugin",   "DOMMimeType"], ["DOMPluginArray",      "DOMPlugin"], ["DOMSelection",        "Node"], ["DOMSelection",        "Range"], ["DOMSettableTokenList",        "DOMTokenList"], ["DOMWindow",   "DOMApplicationCache"], ["DOMWindow",   "Navigator"], ["DOMWindow",   "Console"], ["DOMWindow",   "Crypto"], ["DOMWindow",   "Document"], ["DOMWindow",   "Event"], ["DOMWindow",   "Element"], ["DOMWindow",   "DOMWindow"], ["DOMWindow",   "History"], ["DOMWindow",   "IDBFactory"], ["DOMWindow",   "Storage"], ["DOMWindow",   "Location"], ["DOMWindow",   "BarInfo"], ["DOMWindow",   "EventListener"], ["DOMWindow",   "PagePopupController"], ["DOMWindow",   "Performance"], ["DOMWindow",   "Screen"], ["DOMWindow",   "StyleMedia"], ["DOMWindow",   "NotificationCenter"], ["DOMWindow",   "StorageInfo"], ["DOMWindow",   "CSSStyleDeclaration"], ["DOMWindow",   "CSSRuleList"], ["DOMWindow",   "DOMSelection"], ["DOMWindow",   "MediaQueryList"], ["DOMWindow",   "Database"], ["DOMWindow",   "RequestAnimationFrameCallback"], ["DOMWindow",   "WebKitPoint"], ["DOMWindow",   "FileSystemCallback"], ["DynamicsCompressorNode",      "AudioNode"], ["DynamicsCompressorNode",      "AudioParam"], ["Element",     "Node"], ["Element",     "DOMTokenList"], ["Element",     "DOMStringMap"], ["Element",     "Element"], ["Element",     "EventListener"], ["Element",     "CSSStyleDeclaration"], ["Element",     "Attr"], ["Element",     "ClientRect"], ["Element",     "ClientRectList"], ["Element",     "NodeList"], ["Element",     "ShadowRoot"], ["Entity",      "Node"], ["EntityReference",     "Node"], ["EntriesCallback",     "EntryArray"], ["Entry",       "DOMFileSystem"], ["Entry",       "DirectoryEntry"], ["Entry",       "MetadataCallback"], ["Entry",       "EntryCallback"], ["Entry",       "VoidCallback"], ["EntryArray",  "Entry"], ["EntryArraySync",      "EntrySync"], ["EntryCallback",       "Entry"], ["EntrySync",   "DOMFileSystemSync"], ["EntrySync",   "EntrySync"], ["EntrySync",   "Metadata"], ["EntrySync",   "DirectoryEntrySync"], ["ErrorCallback",       "FileError"], ["ErrorEvent",  "Event"], ["Event",       "Clipboard"], ["Event",       "EventTarget"], ["EventListener",       "Event"], ["EventSource", "EventListener"], ["EventSource", "Event"], ["EventTarget", "Event"], ["File",        "Blob"], ["FileCallback",        "File"], ["FileEntry",   "Entry"], ["FileEntry",   "FileWriterCallback"], ["FileEntry",   "FileCallback"], ["FileEntrySync",       "EntrySync"], ["FileEntrySync",       "FileWriterSync"], ["FileEntrySync",       "File"], ["FileList",    "File"], ["FileReader",  "FileError"], ["FileReader",  "EventListener"], ["FileReader",  "Event"], ["FileReader",  "Blob"], ["FileReaderSync",      "ArrayBuffer"], ["FileSystemCallback",  "DOMFileSystem"], ["FileWriter",  "FileError"], ["FileWriter",  "EventListener"], ["FileWriter",  "Event"], ["FileWriter",  "Blob"], ["FileWriterCallback",  "FileWriter"], ["FileWriterSync",      "Blob"], ["Float32Array",        "ArrayBufferView"], ["Float32Array",        "Float32Array"], ["Float64Array",        "ArrayBufferView"], ["Float64Array",        "Float64Array"], ["GainNode",    "AudioNode"], ["GainNode",    "AudioGain"], ["GamepadList", "Gamepad"], ["Geolocation", "PositionCallback"], ["Geoposition", "Coordinates"], ["HashChangeEvent",     "Event"], ["HTMLAllCollection",   "Node"], ["HTMLAllCollection",   "NodeList"], ["HTMLAnchorElement",   "HTMLElement"], ["HTMLAppletElement",   "HTMLElement"], ["HTMLAreaElement",     "HTMLElement"], ["HTMLAudioElement",    "HTMLMediaElement"], ["HTMLBaseElement",     "HTMLElement"], ["HTMLBaseFontElement", "HTMLElement"], ["HTMLBodyElement",     "HTMLElement"], ["HTMLBodyElement",     "EventListener"], ["HTMLBRElement",       "HTMLElement"], ["HTMLButtonElement",   "HTMLElement"], ["HTMLButtonElement",   "HTMLFormElement"], ["HTMLButtonElement",   "NodeList"], ["HTMLButtonElement",   "ValidityState"], ["HTMLCanvasElement",   "HTMLElement"], ["HTMLCollection",      "Node"], ["HTMLContentElement",  "HTMLElement"], ["HTMLContentElement",  "NodeList"], ["HTMLDataListElement", "HTMLElement"], ["HTMLDataListElement", "HTMLCollection"], ["HTMLDetailsElement",  "HTMLElement"], ["HTMLDirectoryElement",        "HTMLElement"], ["HTMLDivElement",      "HTMLElement"], ["HTMLDListElement",    "HTMLElement"], ["HTMLDocument",        "Document"], ["HTMLDocument",        "Element"], ["HTMLDocument",        "HTMLAllCollection"], ["HTMLDocument",        "HTMLCollection"], ["HTMLElement", "Element"], ["HTMLElement", "HTMLCollection"], ["HTMLEmbedElement",    "HTMLElement"], ["HTMLEmbedElement",    "SVGDocument"], ["HTMLFieldSetElement", "HTMLElement"], ["HTMLFieldSetElement", "HTMLCollection"], ["HTMLFieldSetElement", "HTMLFormElement"], ["HTMLFieldSetElement", "ValidityState"], ["HTMLFontElement",     "HTMLElement"], ["HTMLFormControlsCollection",  "HTMLCollection"], ["HTMLFormControlsCollection",  "Node"], ["HTMLFormElement",     "HTMLElement"], ["HTMLFormElement",     "HTMLCollection"], ["HTMLFrameElement",    "HTMLElement"], ["HTMLFrameElement",    "Document"], ["HTMLFrameElement",    "DOMWindow"], ["HTMLFrameElement",    "SVGDocument"], ["HTMLFrameSetElement", "HTMLElement"], ["HTMLFrameSetElement", "EventListener"], ["HTMLHeadElement",     "HTMLElement"], ["HTMLHeadingElement",  "HTMLElement"], ["HTMLHRElement",       "HTMLElement"], ["HTMLHtmlElement",     "HTMLElement"], ["HTMLIFrameElement",   "HTMLElement"], ["HTMLIFrameElement",   "Document"], ["HTMLIFrameElement",   "DOMWindow"], ["HTMLIFrameElement",   "SVGDocument"], ["HTMLImageElement",    "HTMLElement"], ["HTMLInputElement",    "HTMLElement"], ["HTMLInputElement",    "FileList"], ["HTMLInputElement",    "HTMLFormElement"], ["HTMLInputElement",    "NodeList"], ["HTMLInputElement",    "EventListener"], ["HTMLInputElement",    "ValidityState"], ["HTMLInputElement",    "EntryArray"], ["HTMLKeygenElement",   "HTMLElement"], ["HTMLKeygenElement",   "HTMLFormElement"], ["HTMLKeygenElement",   "NodeList"], ["HTMLKeygenElement",   "ValidityState"], ["HTMLLabelElement",    "HTMLElement"], ["HTMLLabelElement",    "HTMLFormElement"], ["HTMLLegendElement",   "HTMLElement"], ["HTMLLegendElement",   "HTMLFormElement"], ["HTMLLIElement",       "HTMLElement"], ["HTMLLinkElement",     "HTMLElement"], ["HTMLLinkElement",     "StyleSheet"], ["HTMLLinkElement",     "DOMSettableTokenList"], ["HTMLMapElement",      "HTMLElement"], ["HTMLMapElement",      "HTMLCollection"], ["HTMLMarqueeElement",  "HTMLElement"], ["HTMLMediaElement",    "HTMLElement"], ["HTMLMediaElement",    "TimeRanges"], ["HTMLMediaElement",    "MediaController"], ["HTMLMediaElement",    "MediaError"], ["HTMLMediaElement",    "EventListener"], ["HTMLMediaElement",    "TextTrackList"], ["HTMLMediaElement",    "TextTrack"], ["HTMLMenuElement",     "HTMLElement"], ["HTMLMetaElement",     "HTMLElement"], ["HTMLMeterElement",    "HTMLElement"], ["HTMLMeterElement",    "NodeList"], ["HTMLModElement",      "HTMLElement"], ["HTMLObjectElement",   "HTMLElement"], ["HTMLObjectElement",   "Document"], ["HTMLObjectElement",   "HTMLFormElement"], ["HTMLObjectElement",   "ValidityState"], ["HTMLObjectElement",   "SVGDocument"], ["HTMLOListElement",    "HTMLElement"], ["HTMLOptGroupElement", "HTMLElement"], ["HTMLOptionElement",   "HTMLElement"], ["HTMLOptionElement",   "HTMLFormElement"], ["HTMLOptionsCollection",       "HTMLCollection"], ["HTMLOptionsCollection",       "HTMLOptionElement"], ["HTMLOptionsCollection",       "Node"], ["HTMLOutputElement",   "HTMLElement"], ["HTMLOutputElement",   "HTMLFormElement"], ["HTMLOutputElement",   "DOMSettableTokenList"], ["HTMLOutputElement",   "NodeList"], ["HTMLOutputElement",   "ValidityState"], ["HTMLParagraphElement",        "HTMLElement"], ["HTMLParamElement",    "HTMLElement"], ["HTMLPreElement",      "HTMLElement"], ["HTMLProgressElement", "HTMLElement"], ["HTMLProgressElement", "NodeList"], ["HTMLQuoteElement",    "HTMLElement"], ["HTMLScriptElement",   "HTMLElement"], ["HTMLSelectElement",   "HTMLElement"], ["HTMLSelectElement",   "HTMLFormElement"], ["HTMLSelectElement",   "NodeList"], ["HTMLSelectElement",   "HTMLOptionsCollection"], ["HTMLSelectElement",   "HTMLCollection"], ["HTMLSelectElement",   "ValidityState"], ["HTMLSelectElement",   "Node"], ["HTMLShadowElement",   "HTMLElement"], ["HTMLSourceElement",   "HTMLElement"], ["HTMLSpanElement",     "HTMLElement"], ["HTMLStyleElement",    "HTMLElement"], ["HTMLStyleElement",    "StyleSheet"], ["HTMLTableCaptionElement",     "HTMLElement"], ["HTMLTableCellElement",        "HTMLElement"], ["HTMLTableColElement", "HTMLElement"], ["HTMLTableElement",    "HTMLElement"], ["HTMLTableElement",    "HTMLTableCaptionElement"], ["HTMLTableElement",    "HTMLCollection"], ["HTMLTableElement",    "HTMLTableSectionElement"], ["HTMLTableRowElement", "HTMLElement"], ["HTMLTableRowElement", "HTMLCollection"], ["HTMLTableSectionElement",     "HTMLElement"], ["HTMLTableSectionElement",     "HTMLCollection"], ["HTMLTextAreaElement", "HTMLElement"], ["HTMLTextAreaElement", "HTMLFormElement"], ["HTMLTextAreaElement", "NodeList"], ["HTMLTextAreaElement", "ValidityState"], ["HTMLTitleElement",    "HTMLElement"], ["HTMLTrackElement",    "HTMLElement"], ["HTMLTrackElement",    "TextTrack"], ["HTMLUListElement",    "HTMLElement"], ["HTMLUnknownElement",  "HTMLElement"], ["HTMLVideoElement",    "HTMLMediaElement"], ["IDBCursor",   "IDBAny"], ["IDBCursor",   "IDBKey"], ["IDBCursor",   "IDBRequest"], ["IDBCursorWithValue",  "IDBCursor"], ["IDBDatabase", "DOMStringList"], ["IDBDatabase", "EventListener"], ["IDBDatabase", "IDBAny"], ["IDBDatabase", "IDBObjectStore"], ["IDBDatabase", "Event"], ["IDBDatabase", "IDBTransaction"], ["IDBFactory",  "IDBKey"], ["IDBFactory",  "IDBVersionChangeRequest"], ["IDBFactory",  "IDBOpenDBRequest"], ["IDBFactory",  "IDBRequest"], ["IDBIndex",    "IDBAny"], ["IDBIndex",    "IDBObjectStore"], ["IDBIndex",    "IDBRequest"], ["IDBKeyRange", "IDBKey"], ["IDBKeyRange", "IDBKeyRange"], ["IDBObjectStore",      "DOMStringList"], ["IDBObjectStore",      "IDBAny"], ["IDBObjectStore",      "IDBTransaction"], ["IDBObjectStore",      "IDBRequest"], ["IDBObjectStore",      "IDBIndex"], ["IDBOpenDBRequest",    "IDBRequest"], ["IDBOpenDBRequest",    "EventListener"], ["IDBRequest",  "DOMError"], ["IDBRequest",  "EventListener"], ["IDBRequest",  "IDBAny"], ["IDBRequest",  "IDBTransaction"], ["IDBRequest",  "Event"], ["IDBTransaction",      "IDBDatabase"], ["IDBTransaction",      "DOMError"], ["IDBTransaction",      "EventListener"], ["IDBTransaction",      "Event"], ["IDBTransaction",      "IDBObjectStore"], ["IDBUpgradeNeededEvent",       "Event"], ["IDBVersionChangeEvent",       "Event"], ["IDBVersionChangeRequest",     "IDBRequest"], ["IDBVersionChangeRequest",     "EventListener"], ["Int16Array",  "ArrayBufferView"], ["Int16Array",  "Int16Array"], ["Int32Array",  "ArrayBufferView"], ["Int32Array",  "Int32Array"], ["Int8Array",   "ArrayBufferView"], ["Int8Array",   "Int8Array"], ["JavaScriptCallFrame", "JavaScriptCallFrame"], ["KeyboardEvent",       "UIEvent"], ["LocalMediaStream",    "MediaStream"], ["Location",    "DOMStringList"], ["MediaController",     "TimeRanges"], ["MediaController",     "Event"], ["MediaElementAudioSourceNode", "AudioSourceNode"], ["MediaElementAudioSourceNode", "HTMLMediaElement"], ["MediaKeyEvent",       "Event"], ["MediaKeyEvent",       "MediaKeyError"], ["MediaKeyEvent",       "Uint8Array"], ["MediaQueryList",      "MediaQueryListListener"], ["MediaQueryListListener",      "MediaQueryList"], ["MediaSource", "SourceBufferList"], ["MediaSource", "SourceBuffer"], ["MediaSource", "Event"], ["MediaStream", "MediaStreamTrackList"], ["MediaStream", "EventListener"], ["MediaStream", "Event"], ["MediaStreamAudioDestinationNode",     "AudioSourceNode"], ["MediaStreamAudioDestinationNode",     "MediaStream"], ["MediaStreamAudioSourceNode",  "AudioSourceNode"], ["MediaStreamAudioSourceNode",  "MediaStream"], ["MediaStreamEvent",    "Event"], ["MediaStreamEvent",    "MediaStream"], ["MediaStreamList",     "MediaStream"], ["MediaStreamTrack",    "EventListener"], ["MediaStreamTrack",    "Event"], ["MediaStreamTrackEvent",       "Event"], ["MediaStreamTrackEvent",       "MediaStreamTrack"], ["MediaStreamTrackList",        "EventListener"], ["MediaStreamTrackList",        "MediaStreamTrack"], ["MediaStreamTrackList",        "Event"], ["MessageChannel",      "MessagePort"], ["MessageEvent",        "Event"], ["MessageEvent",        "DOMWindow"], ["MessagePort", "EventListener"], ["MessagePort", "Event"], ["MetadataCallback",    "Metadata"], ["MouseEvent",  "UIEvent"], ["MouseEvent",  "Clipboard"], ["MouseEvent",  "Node"], ["MouseEvent",  "EventTarget"], ["MutationEvent",       "Event"], ["MutationEvent",       "Node"], ["MutationObserver",    "Node"], ["MutationRecord",      "NodeList"], ["MutationRecord",      "Node"], ["NamedNodeMap",        "Node"], ["Navigator",   "Geolocation"], ["Navigator",   "DOMMimeTypeArray"], ["Navigator",   "DOMPluginArray"], ["Navigator",   "BatteryManager"], ["Navigator",   "GamepadList"], ["NavigatorUserMediaErrorCallback",     "NavigatorUserMediaError"], ["NavigatorUserMediaSuccessCallback",   "LocalMediaStream"], ["Node",        "NamedNodeMap"], ["Node",        "NodeList"], ["Node",        "Node"], ["Node",        "Document"], ["Node",        "Element"], ["Node",        "Event"], ["NodeFilter",  "Node"], ["NodeIterator",        "NodeFilter"], ["NodeIterator",        "Node"], ["NodeList",    "Node"], ["Notation",    "Node"], ["Notification",        "EventListener"], ["Notification",        "Event"], ["Notification",        "NotificationPermissionCallback"], ["NotificationCenter",  "Notification"], ["NotificationCenter",  "VoidCallback"], ["OESVertexArrayObject",        "WebGLVertexArrayObjectOES"], ["OfflineAudioCompletionEvent", "Event"], ["OfflineAudioCompletionEvent", "AudioBuffer"], ["OscillatorNode",      "AudioSourceNode"], ["OscillatorNode",      "AudioParam"], ["OscillatorNode",      "WaveTable"], ["OverflowEvent",       "Event"], ["PageTransitionEvent", "Event"], ["PannerNode",  "AudioNode"], ["Performance", "MemoryInfo"], ["Performance", "PerformanceNavigation"], ["Performance", "PerformanceTiming"], ["PopStateEvent",       "Event"], ["PositionCallback",    "Geoposition"], ["PositionErrorCallback",       "PositionError"], ["ProcessingInstruction",       "Node"], ["ProcessingInstruction",       "StyleSheet"], ["ProgressEvent",       "Event"], ["RadioNodeList",       "NodeList"], ["Range",       "Node"], ["Range",       "DocumentFragment"], ["Range",       "Range"], ["Range",       "ClientRect"], ["Range",       "ClientRectList"], ["Rect",        "CSSPrimitiveValue"], ["RGBColor",    "CSSPrimitiveValue"], ["RTCDataChannel",      "EventListener"], ["RTCDataChannel",      "Event"], ["RTCDataChannel",      "ArrayBuffer"], ["RTCDataChannel",      "ArrayBufferView"], ["RTCDataChannel",      "Blob"], ["RTCDataChannelEvent", "Event"], ["RTCDataChannelEvent", "RTCDataChannel"], ["RTCIceCandidateEvent",        "Event"], ["RTCIceCandidateEvent",        "RTCIceCandidate"], ["RTCPeerConnection",   "RTCSessionDescription"], ["RTCPeerConnection",   "MediaStreamList"], ["RTCPeerConnection",   "EventListener"], ["RTCPeerConnection",   "RTCIceCandidate"], ["RTCPeerConnection",   "MediaStream"], ["RTCPeerConnection",   "RTCSessionDescriptionCallback"], ["RTCPeerConnection",   "RTCDataChannel"], ["RTCPeerConnection",   "Event"], ["RTCPeerConnection",   "RTCStatsCallback"], ["RTCSessionDescriptionCallback",       "RTCSessionDescription"], ["RTCStatsCallback",    "RTCStatsResponse"], ["RTCStatsReport",      "RTCStatsElement"], ["ScriptProcessorNode", "AudioNode"], ["ScriptProcessorNode", "EventListener"], ["ScriptProfile",       "ScriptProfileNode"], ["ShadowRoot",  "DocumentFragment"], ["ShadowRoot",  "Element"], ["ShadowRoot",  "Node"], ["ShadowRoot",  "NodeList"], ["ShadowRoot",  "DOMSelection"], ["SharedWorker",        "AbstractWorker"], ["SharedWorker",        "MessagePort"], ["SharedWorkerContext", "WorkerContext"], ["SharedWorkerContext", "EventListener"], ["SourceBuffer",        "TimeRanges"], ["SourceBuffer",        "Uint8Array"], ["SourceBufferList",    "Event"], ["SourceBufferList",    "SourceBuffer"], ["SpeechGrammarList",   "SpeechGrammar"], ["SpeechInputEvent",    "Event"], ["SpeechInputEvent",    "SpeechInputResultList"], ["SpeechInputResultList",       "SpeechInputResult"], ["SpeechRecognition",   "SpeechGrammarList"], ["SpeechRecognition",   "EventListener"], ["SpeechRecognition",   "Event"], ["SpeechRecognitionError",      "Event"], ["SpeechRecognitionEvent",      "Event"], ["SpeechRecognitionEvent",      "Document"], ["SpeechRecognitionEvent",      "SpeechRecognitionResult"], ["SpeechRecognitionEvent",      "SpeechRecognitionResultList"], ["SpeechRecognitionResult",     "SpeechRecognitionAlternative"], ["SpeechRecognitionResultList", "SpeechRecognitionResult"], ["SQLResultSet",        "SQLResultSetRowList"], ["SQLStatementCallback",        "SQLTransaction"], ["SQLStatementErrorCallback",   "SQLTransaction"], ["SQLTransactionCallback",      "SQLTransaction"], ["SQLTransactionErrorCallback", "SQLError"], ["SQLTransactionSync",  "SQLResultSet"], ["SQLTransactionSyncCallback",  "SQLTransactionSync"], ["StorageEvent",        "Event"], ["StorageEvent",        "Storage"], ["StorageInfo", "StorageInfoUsageCallback"], ["StorageInfo", "StorageInfoQuotaCallback"], ["StorageInfoErrorCallback",    "DOMCoreException"], ["StyleSheet",  "MediaList"], ["StyleSheet",  "Node"], ["StyleSheet",  "StyleSheet"], ["StyleSheetList",      "StyleSheet"], ["SVGAElement", "SVGTransformable"], ["SVGAElement", "SVGAnimatedString"], ["SVGAltGlyphDefElement",       "SVGElement"], ["SVGAltGlyphElement",  "SVGURIReference"], ["SVGAltGlyphItemElement",      "SVGElement"], ["SVGAnimateColorElement",      "SVGAnimationElement"], ["SVGAnimatedAngle",    "SVGAngle"], ["SVGAnimatedLength",   "SVGLength"], ["SVGAnimatedLengthList",       "SVGLengthList"], ["SVGAnimatedNumberList",       "SVGNumberList"], ["SVGAnimatedPreserveAspectRatio",      "SVGPreserveAspectRatio"], ["SVGAnimatedRect",     "SVGRect"], ["SVGAnimatedTransformList",    "SVGTransformList"], ["SVGAnimateElement",   "SVGAnimationElement"], ["SVGAnimateMotionElement",     "SVGAnimationElement"], ["SVGAnimateTransformElement",  "SVGAnimationElement"], ["SVGAnimationElement", "ElementTimeControl"], ["SVGAnimationElement", "SVGElement"], ["SVGCircleElement",    "SVGTransformable"], ["SVGCircleElement",    "SVGAnimatedLength"], ["SVGClipPathElement",  "SVGTransformable"], ["SVGClipPathElement",  "SVGAnimatedEnumeration"], ["SVGColor",    "CSSValue"], ["SVGColor",    "RGBColor"], ["SVGComponentTransferFunctionElement", "SVGElement"], ["SVGComponentTransferFunctionElement", "SVGAnimatedNumber"], ["SVGComponentTransferFunctionElement", "SVGAnimatedNumberList"], ["SVGComponentTransferFunctionElement", "SVGAnimatedEnumeration"], ["SVGCursorElement",    "SVGExternalResourcesRequired"], ["SVGCursorElement",    "SVGAnimatedLength"], ["SVGDefsElement",      "SVGTransformable"], ["SVGDescElement",      "SVGStylable"], ["SVGDocument", "Document"], ["SVGDocument", "SVGSVGElement"], ["SVGDocument", "Event"], ["SVGElement",  "Element"], ["SVGElement",  "SVGSVGElement"], ["SVGElement",  "SVGElement"], ["SVGElementInstance",  "EventTarget"], ["SVGElementInstance",  "SVGElementInstanceList"], ["SVGElementInstance",  "SVGElement"], ["SVGElementInstance",  "SVGUseElement"], ["SVGElementInstance",  "SVGElementInstance"], ["SVGElementInstance",  "EventListener"], ["SVGElementInstanceList",      "SVGElementInstance"], ["SVGEllipseElement",   "SVGTransformable"], ["SVGEllipseElement",   "SVGAnimatedLength"], ["SVGExternalResourcesRequired",        "SVGAnimatedBoolean"], ["SVGFEBlendElement",   "SVGFilterPrimitiveStandardAttributes"], ["SVGFEBlendElement",   "SVGAnimatedString"], ["SVGFEBlendElement",   "SVGAnimatedEnumeration"], ["SVGFEColorMatrixElement",     "SVGFilterPrimitiveStandardAttributes"], ["SVGFEColorMatrixElement",     "SVGAnimatedString"], ["SVGFEColorMatrixElement",     "SVGAnimatedEnumeration"], ["SVGFEColorMatrixElement",     "SVGAnimatedNumberList"], ["SVGFEComponentTransferElement",       "SVGFilterPrimitiveStandardAttributes"], ["SVGFEComponentTransferElement",       "SVGAnimatedString"], ["SVGFECompositeElement",       "SVGFilterPrimitiveStandardAttributes"], ["SVGFECompositeElement",       "SVGAnimatedString"], ["SVGFECompositeElement",       "SVGAnimatedNumber"], ["SVGFECompositeElement",       "SVGAnimatedEnumeration"], ["SVGFEConvolveMatrixElement",  "SVGFilterPrimitiveStandardAttributes"], ["SVGFEConvolveMatrixElement",  "SVGAnimatedNumber"], ["SVGFEConvolveMatrixElement",  "SVGAnimatedEnumeration"], ["SVGFEConvolveMatrixElement",  "SVGAnimatedString"], ["SVGFEConvolveMatrixElement",  "SVGAnimatedNumberList"], ["SVGFEConvolveMatrixElement",  "SVGAnimatedInteger"], ["SVGFEConvolveMatrixElement",  "SVGAnimatedBoolean"], ["SVGFEDiffuseLightingElement", "SVGFilterPrimitiveStandardAttributes"], ["SVGFEDiffuseLightingElement", "SVGAnimatedNumber"], ["SVGFEDiffuseLightingElement", "SVGAnimatedString"], ["SVGFEDisplacementMapElement", "SVGFilterPrimitiveStandardAttributes"], ["SVGFEDisplacementMapElement", "SVGAnimatedString"], ["SVGFEDisplacementMapElement", "SVGAnimatedNumber"], ["SVGFEDisplacementMapElement", "SVGAnimatedEnumeration"], ["SVGFEDistantLightElement",    "SVGElement"], ["SVGFEDistantLightElement",    "SVGAnimatedNumber"], ["SVGFEDropShadowElement",      "SVGFilterPrimitiveStandardAttributes"], ["SVGFEDropShadowElement",      "SVGAnimatedNumber"], ["SVGFEDropShadowElement",      "SVGAnimatedString"], ["SVGFEFloodElement",   "SVGFilterPrimitiveStandardAttributes"], ["SVGFEFuncAElement",   "SVGComponentTransferFunctionElement"], ["SVGFEFuncBElement",   "SVGComponentTransferFunctionElement"], ["SVGFEFuncGElement",   "SVGComponentTransferFunctionElement"], ["SVGFEFuncRElement",   "SVGComponentTransferFunctionElement"], ["SVGFEGaussianBlurElement",    "SVGFilterPrimitiveStandardAttributes"], ["SVGFEGaussianBlurElement",    "SVGAnimatedString"], ["SVGFEGaussianBlurElement",    "SVGAnimatedNumber"], ["SVGFEImageElement",   "SVGFilterPrimitiveStandardAttributes"], ["SVGFEImageElement",   "SVGAnimatedPreserveAspectRatio"], ["SVGFEMergeElement",   "SVGFilterPrimitiveStandardAttributes"], ["SVGFEMergeNodeElement",       "SVGElement"], ["SVGFEMergeNodeElement",       "SVGAnimatedString"], ["SVGFEMorphologyElement",      "SVGFilterPrimitiveStandardAttributes"], ["SVGFEMorphologyElement",      "SVGAnimatedString"], ["SVGFEMorphologyElement",      "SVGAnimatedEnumeration"], ["SVGFEMorphologyElement",      "SVGAnimatedNumber"], ["SVGFEOffsetElement",  "SVGFilterPrimitiveStandardAttributes"], ["SVGFEOffsetElement",  "SVGAnimatedNumber"], ["SVGFEOffsetElement",  "SVGAnimatedString"], ["SVGFEPointLightElement",      "SVGElement"], ["SVGFEPointLightElement",      "SVGAnimatedNumber"], ["SVGFESpecularLightingElement",        "SVGFilterPrimitiveStandardAttributes"], ["SVGFESpecularLightingElement",        "SVGAnimatedString"], ["SVGFESpecularLightingElement",        "SVGAnimatedNumber"], ["SVGFESpotLightElement",       "SVGElement"], ["SVGFESpotLightElement",       "SVGAnimatedNumber"], ["SVGFETileElement",    "SVGFilterPrimitiveStandardAttributes"], ["SVGFETileElement",    "SVGAnimatedString"], ["SVGFETurbulenceElement",      "SVGFilterPrimitiveStandardAttributes"], ["SVGFETurbulenceElement",      "SVGAnimatedNumber"], ["SVGFETurbulenceElement",      "SVGAnimatedInteger"], ["SVGFETurbulenceElement",      "SVGAnimatedEnumeration"], ["SVGFilterElement",    "SVGStylable"], ["SVGFilterElement",    "SVGAnimatedInteger"], ["SVGFilterElement",    "SVGAnimatedEnumeration"], ["SVGFilterElement",    "SVGAnimatedLength"], ["SVGFilterPrimitiveStandardAttributes",        "SVGStylable"], ["SVGFilterPrimitiveStandardAttributes",        "SVGAnimatedLength"], ["SVGFilterPrimitiveStandardAttributes",        "SVGAnimatedString"], ["SVGFitToViewBox",     "SVGAnimatedPreserveAspectRatio"], ["SVGFitToViewBox",     "SVGAnimatedRect"], ["SVGFontElement",      "SVGElement"], ["SVGFontFaceElement",  "SVGElement"], ["SVGFontFaceFormatElement",    "SVGElement"], ["SVGFontFaceNameElement",      "SVGElement"], ["SVGFontFaceSrcElement",       "SVGElement"], ["SVGFontFaceUriElement",       "SVGElement"], ["SVGForeignObjectElement",     "SVGTransformable"], ["SVGForeignObjectElement",     "SVGAnimatedLength"], ["SVGGElement", "SVGTransformable"], ["SVGGlyphElement",     "SVGElement"], ["SVGGlyphRefElement",  "SVGStylable"], ["SVGGradientElement",  "SVGStylable"], ["SVGGradientElement",  "SVGAnimatedTransformList"], ["SVGGradientElement",  "SVGAnimatedEnumeration"], ["SVGHKernElement",     "SVGElement"], ["SVGImageElement",     "SVGTransformable"], ["SVGImageElement",     "SVGAnimatedLength"], ["SVGImageElement",     "SVGAnimatedPreserveAspectRatio"], ["SVGLengthList",       "SVGLength"], ["SVGLinearGradientElement",    "SVGGradientElement"], ["SVGLinearGradientElement",    "SVGAnimatedLength"], ["SVGLineElement",      "SVGTransformable"], ["SVGLineElement",      "SVGAnimatedLength"], ["SVGLocatable",        "SVGElement"], ["SVGLocatable",        "SVGRect"], ["SVGLocatable",        "SVGMatrix"], ["SVGMarkerElement",    "SVGFitToViewBox"], ["SVGMarkerElement",    "SVGAnimatedLength"], ["SVGMarkerElement",    "SVGAnimatedEnumeration"], ["SVGMarkerElement",    "SVGAnimatedAngle"], ["SVGMarkerElement",    "SVGAngle"], ["SVGMaskElement",      "SVGStylable"], ["SVGMaskElement",      "SVGAnimatedLength"], ["SVGMaskElement",      "SVGAnimatedEnumeration"], ["SVGMatrix",   "SVGMatrix"], ["SVGMetadataElement",  "SVGElement"], ["SVGMissingGlyphElement",      "SVGElement"], ["SVGMPathElement",     "SVGExternalResourcesRequired"], ["SVGNumberList",       "SVGNumber"], ["SVGPaint",    "SVGColor"], ["SVGPathElement",      "SVGTransformable"], ["SVGPathElement",      "SVGPathSegList"], ["SVGPathElement",      "SVGAnimatedNumber"], ["SVGPathElement",      "SVGPathSegArcAbs"], ["SVGPathElement",      "SVGPathSegArcRel"], ["SVGPathElement",      "SVGPathSegClosePath"], ["SVGPathElement",      "SVGPathSegCurvetoCubicAbs"], ["SVGPathElement",      "SVGPathSegCurvetoCubicRel"], ["SVGPathElement",      "SVGPathSegCurvetoCubicSmoothAbs"], ["SVGPathElement",      "SVGPathSegCurvetoCubicSmoothRel"], ["SVGPathElement",      "SVGPathSegCurvetoQuadraticAbs"], ["SVGPathElement",      "SVGPathSegCurvetoQuadraticRel"], ["SVGPathElement",      "SVGPathSegCurvetoQuadraticSmoothAbs"], ["SVGPathElement",      "SVGPathSegCurvetoQuadraticSmoothRel"], ["SVGPathElement",      "SVGPathSegLinetoAbs"], ["SVGPathElement",      "SVGPathSegLinetoHorizontalAbs"], ["SVGPathElement",      "SVGPathSegLinetoHorizontalRel"], ["SVGPathElement",      "SVGPathSegLinetoRel"], ["SVGPathElement",      "SVGPathSegLinetoVerticalAbs"], ["SVGPathElement",      "SVGPathSegLinetoVerticalRel"], ["SVGPathElement",      "SVGPathSegMovetoAbs"], ["SVGPathElement",      "SVGPathSegMovetoRel"], ["SVGPathElement",      "SVGPoint"], ["SVGPathSegArcAbs",    "SVGPathSeg"], ["SVGPathSegArcRel",    "SVGPathSeg"], ["SVGPathSegClosePath", "SVGPathSeg"], ["SVGPathSegCurvetoCubicAbs",   "SVGPathSeg"], ["SVGPathSegCurvetoCubicRel",   "SVGPathSeg"], ["SVGPathSegCurvetoCubicSmoothAbs",     "SVGPathSeg"], ["SVGPathSegCurvetoCubicSmoothRel",     "SVGPathSeg"], ["SVGPathSegCurvetoQuadraticAbs",       "SVGPathSeg"], ["SVGPathSegCurvetoQuadraticRel",       "SVGPathSeg"], ["SVGPathSegCurvetoQuadraticSmoothAbs", "SVGPathSeg"], ["SVGPathSegCurvetoQuadraticSmoothRel", "SVGPathSeg"], ["SVGPathSegLinetoAbs", "SVGPathSeg"], ["SVGPathSegLinetoHorizontalAbs",       "SVGPathSeg"], ["SVGPathSegLinetoHorizontalRel",       "SVGPathSeg"], ["SVGPathSegLinetoRel", "SVGPathSeg"], ["SVGPathSegLinetoVerticalAbs", "SVGPathSeg"], ["SVGPathSegLinetoVerticalRel", "SVGPathSeg"], ["SVGPathSegList",      "SVGPathSeg"], ["SVGPathSegMovetoAbs", "SVGPathSeg"], ["SVGPathSegMovetoRel", "SVGPathSeg"], ["SVGPatternElement",   "SVGFitToViewBox"], ["SVGPatternElement",   "SVGAnimatedLength"], ["SVGPatternElement",   "SVGAnimatedEnumeration"], ["SVGPatternElement",   "SVGAnimatedTransformList"], ["SVGPoint",    "SVGPoint"], ["SVGPointList",        "SVGPoint"], ["SVGPolygonElement",   "SVGTransformable"], ["SVGPolygonElement",   "SVGPointList"], ["SVGPolylineElement",  "SVGTransformable"], ["SVGPolylineElement",  "SVGPointList"], ["SVGRadialGradientElement",    "SVGGradientElement"], ["SVGRadialGradientElement",    "SVGAnimatedLength"], ["SVGRectElement",      "SVGTransformable"], ["SVGRectElement",      "SVGAnimatedLength"], ["SVGScriptElement",    "SVGExternalResourcesRequired"], ["SVGSetElement",       "SVGAnimationElement"], ["SVGStopElement",      "SVGStylable"], ["SVGStopElement",      "SVGAnimatedNumber"], ["SVGStylable", "SVGAnimatedString"], ["SVGStylable", "CSSStyleDeclaration"], ["SVGStylable", "CSSValue"], ["SVGStyleElement",     "SVGLangSpace"], ["SVGSVGElement",       "SVGZoomAndPan"], ["SVGSVGElement",       "SVGPoint"], ["SVGSVGElement",       "SVGViewSpec"], ["SVGSVGElement",       "SVGAnimatedLength"], ["SVGSVGElement",       "SVGRect"], ["SVGSVGElement",       "SVGElement"], ["SVGSVGElement",       "SVGAngle"], ["SVGSVGElement",       "SVGLength"], ["SVGSVGElement",       "SVGMatrix"], ["SVGSVGElement",       "SVGNumber"], ["SVGSVGElement",       "SVGTransform"], ["SVGSVGElement",       "Element"], ["SVGSVGElement",       "NodeList"], ["SVGSwitchElement",    "SVGTransformable"], ["SVGSymbolElement",    "SVGFitToViewBox"], ["SVGTests",    "SVGStringList"], ["SVGTextContentElement",       "SVGStylable"], ["SVGTextContentElement",       "SVGAnimatedEnumeration"], ["SVGTextContentElement",       "SVGAnimatedLength"], ["SVGTextContentElement",       "SVGPoint"], ["SVGTextContentElement",       "SVGRect"], ["SVGTextElement",      "SVGTransformable"], ["SVGTextPathElement",  "SVGURIReference"], ["SVGTextPathElement",  "SVGAnimatedEnumeration"], ["SVGTextPathElement",  "SVGAnimatedLength"], ["SVGTextPositioningElement",   "SVGTextContentElement"], ["SVGTextPositioningElement",   "SVGAnimatedLengthList"], ["SVGTextPositioningElement",   "SVGAnimatedNumberList"], ["SVGTitleElement",     "SVGStylable"], ["SVGTransform",        "SVGMatrix"], ["SVGTransformable",    "SVGLocatable"], ["SVGTransformable",    "SVGAnimatedTransformList"], ["SVGTransformList",    "SVGTransform"], ["SVGTRefElement",      "SVGURIReference"], ["SVGTSpanElement",     "SVGTextPositioningElement"], ["SVGURIReference",     "SVGAnimatedString"], ["SVGUseElement",       "SVGTransformable"], ["SVGUseElement",       "SVGElementInstance"], ["SVGUseElement",       "SVGAnimatedLength"], ["SVGViewElement",      "SVGZoomAndPan"], ["SVGViewElement",      "SVGStringList"], ["SVGViewSpec", "SVGAnimatedPreserveAspectRatio"], ["SVGViewSpec", "SVGTransformList"], ["SVGViewSpec", "SVGAnimatedRect"], ["SVGViewSpec", "SVGElement"], ["SVGVKernElement",     "SVGElement"], ["SVGZoomEvent",        "UIEvent"], ["SVGZoomEvent",        "SVGPoint"], ["SVGZoomEvent",        "SVGRect"], ["Text",        "CharacterData"], ["Text",        "Text"], ["TextEvent",   "UIEvent"], ["TextTrack",   "TextTrackCueList"], ["TextTrack",   "EventListener"], ["TextTrack",   "TextTrackCue"], ["TextTrack",   "Event"], ["TextTrackCue",        "EventListener"], ["TextTrackCue",        "TextTrack"], ["TextTrackCue",        "Event"], ["TextTrackCue",        "DocumentFragment"], ["TextTrackCueList",    "TextTrackCue"], ["TextTrackList",       "EventListener"], ["TextTrackList",       "Event"], ["TextTrackList",       "TextTrack"], ["Touch",       "EventTarget"], ["TouchEvent",  "UIEvent"], ["TouchEvent",  "TouchList"], ["TouchList",   "Touch"], ["TrackEvent",  "Event"], ["TreeWalker",  "Node"], ["TreeWalker",  "NodeFilter"], ["UIEvent",     "Event"], ["UIEvent",     "DOMWindow"], ["Uint16Array", "ArrayBufferView"], ["Uint16Array", "Uint16Array"], ["Uint32Array", "ArrayBufferView"], ["Uint32Array", "Uint32Array"], ["Uint8Array",  "ArrayBufferView"], ["Uint8Array",  "Uint8Array"], ["Uint8ClampedArray",   "Uint8Array"], ["Uint8ClampedArray",   "Uint8ClampedArray"], ["WaveShaperNode",      "AudioNode"], ["WaveShaperNode",      "Float32Array"], ["WebGLContextEvent",   "Event"], ["WebGLRenderingContext",       "CanvasRenderingContext"], ["WebGLRenderingContext",       "WebGLProgram"], ["WebGLRenderingContext",       "WebGLBuffer"], ["WebGLRenderingContext",       "WebGLFramebuffer"], ["WebGLRenderingContext",       "WebGLRenderbuffer"], ["WebGLRenderingContext",       "WebGLTexture"], ["WebGLRenderingContext",       "ArrayBuffer"], ["WebGLRenderingContext",       "ArrayBufferView"], ["WebGLRenderingContext",       "WebGLShader"], ["WebGLRenderingContext",       "WebGLActiveInfo"], ["WebGLRenderingContext",       "WebGLContextAttributes"], ["WebGLRenderingContext",       "WebGLShaderPrecisionFormat"], ["WebGLRenderingContext",       "WebGLUniformLocation"], ["WebGLRenderingContext",       "ImageData"], ["WebGLRenderingContext",       "HTMLImageElement"], ["WebGLRenderingContext",       "HTMLCanvasElement"], ["WebGLRenderingContext",       "HTMLVideoElement"], ["WebGLRenderingContext",       "Float32Array"], ["WebKitAnimationEvent",        "Event"], ["WebKitAnimationList", "WebKitAnimation"], ["WebKitCSSFilterValue",        "CSSValueList"], ["WebKitCSSKeyframeRule",       "CSSRule"], ["WebKitCSSKeyframeRule",       "CSSStyleDeclaration"], ["WebKitCSSKeyframesRule",      "CSSRule"], ["WebKitCSSKeyframesRule",      "CSSRuleList"], ["WebKitCSSKeyframesRule",      "WebKitCSSKeyframeRule"], ["WebKitCSSMatrix",     "WebKitCSSMatrix"], ["WebKitCSSMixFunctionValue",   "CSSValueList"], ["WebKitCSSTransformValue",     "CSSValueList"], ["WebKitNamedFlow",     "Event"], ["WebKitNamedFlow",     "NodeList"], ["WebKitTransitionEvent",       "Event"], ["WebSocket",   "EventListener"], ["WebSocket",   "Event"], ["WebSocket",   "ArrayBuffer"], ["WebSocket",   "ArrayBufferView"], ["WebSocket",   "Blob"], ["WheelEvent",  "MouseEvent"], ["WheelEvent",  "DOMWindow"], ["Worker",      "AbstractWorker"], ["Worker",      "EventListener"], ["WorkerContext",       "IDBFactory"], ["WorkerContext",       "WorkerLocation"], ["WorkerContext",       "WorkerNavigator"], ["WorkerContext",       "EventListener"], ["WorkerContext",       "WorkerContext"], ["WorkerContext",       "NotificationCenter"], ["WorkerContext",       "Event"], ["WorkerContext",       "Database"], ["WorkerContext",       "DatabaseSync"], ["WorkerContext",       "FileSystemCallback"], ["WorkerContext",       "DOMFileSystemSync"], [   "WorkerContext",    "EntrySync"], [   "XMLHttpRequest",   "EventListener"], [   "XMLHttpRequest",   "Document"], [   "XMLHttpRequest",   "XMLHttpRequestUpload"], [   "XMLHttpRequest",   "Event"], [   "XMLHttpRequest",   "ArrayBuffer"], [   "XMLHttpRequest",   "ArrayBufferView"], [   "XMLHttpRequest",   "Blob"], [   "XMLHttpRequest",   "DOMFormData"], [   "XMLHttpRequestProgressEvent",      "ProgressEvent"], [   "XMLHttpRequestUpload",     "EventListener"], [   "XMLHttpRequestUpload",     "Event"], [   "XPathEvaluator",   "XPathExpression"], [   "XPathEvaluator",   "XPathNSResolver"], [   "XPathEvaluator",   "XPathResult"], [   "XPathExpression",  "XPathResult"], [   "XPathResult",      "Node"], [   "XSLTProcessor",    "Node"], [   "XSLTProcessor",    "Document"], [   "XSLTProcessor",    "DocumentFragment"], ];

var fs = require("fs");

var nodes = [];
var links = [];
var categories = [{
    name : 'HTMLElement',
    keyword : /^HTML/,
    base : 'HTMLElement',
    itemStyle : {
        normal: {
            "brushType": "both",
            "color": "#D0D102",
            "strokeColor": "#5182ab",
            "lineWidth": 2
        }
    }
}, {
    name : 'WebGL',
    keyword : /^WebGL/,
    base : 'WebGLRenderingContext',
    itemStyle : {
        normal: {
            "brushType": "both",
            "color": "#00A1CB",
            "strokeColor": "#5182ab",
            "lineWidth": 2
        }
    }
}, {
    name : 'SVG',
    keyword : /^SVG/,
    base : 'SVGElement',
    itemStyle : {
        normal: {
            "brushType": "both",
             "color": "#dda0dd",
             "strokeColor": "#5182ab",
             "lineWidth": 2
        }
    }
}, {
    name : 'CSS',
    keyword : /^CSS/,
    base : 'CSSRule',
    itemStyle : {
        normal: {
            "brushType": "both",
            "color": "#61AE24",
            "strokeColor": "#5182ab",
            "lineWidth": 2
        }
    }
}, {
    name : 'Other',
    keyword : /.*/,
    itemStyle : {
        normal: {
            "brushType": "both",
             "strokeColor": "#5182ab",
             "lineWidth": 2
        }
    }
}];

var nodesIdxMap = {};

data.forEach(function (item){
    
    if (item[0] == "Event" || item[0] == "EventListener" ) return;
    if (item[1] == "Event" || item[1] == "EventListener" ) return;
    if (item[0] == "DOMWindow" || item[1] == "DOMWindow" ) return;
    if (item[0] == "Document" || item[1] == "Document" ) return;
    if (item[0] == "Blob" || item[1] == "Blob" ) return;
    if (item[0].match("Event$") || item[1].match("Event$") ) return;

    links.push({
        source : getNodeIdx(item[0]),
        target : getNodeIdx(item[1])
    })
});

function getNodeIdx(name){
    if(typeof(nodesIdxMap[name]) === "undefined"){
        nodesIdxMap[name] = nodes.length;

        nodes.push({
            name : name,
            value : calculateValue(name),
            category : findCategory(name)
        });
    }
    return nodesIdxMap[name];
}

function findCategory(name){
    for (var i = 0; i < categories.length; i++) {
        if (name.match(categories[i].keyword) ){
            return i;
        }
    }
}

function calculateValue(name){
    for (var i = 0; i < categories.length; i++) {
        if (name === categories[i].base){
            return 3;
        }
    }
    return 1;
}

var res = {
    type : "force",
    categories : categories,
    nodes : nodes,
    links : links
}

fs.writeFile("webkit-dep.js", 'define(' + JSON.stringify(res, null, 4) + ')');