package com.clou.esp.hes.app.web.service.demo;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.jws.soap.SOAPBinding.Style;

import com.clou.esp.hes.app.web.model.demo.req.Header;
import com.clou.esp.hes.app.web.model.demo.req.Payload;
import com.clou.esp.hes.app.web.model.demo.res.Reply;


/**
 * 孟加拉系统接口
 * 
 * <AUTHOR>
 * 
 */
@WebService
@SOAPBinding(style = Style.RPC)
public interface PARTWebService {

	/**
	 * 孟加拉项目接口
	 * 
	 * @param params
	 * @return
	 */
	@WebMethod
	public Reply doCommand(@WebParam Header header, @WebParam Payload payload);

}
