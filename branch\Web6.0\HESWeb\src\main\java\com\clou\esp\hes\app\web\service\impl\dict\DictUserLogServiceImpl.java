package com.clou.esp.hes.app.web.service.impl.dict;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.dict.DictUserLogDao;
import com.clou.esp.hes.app.web.model.dict.DictUserLog;
import com.clou.esp.hes.app.web.service.dict.DictUserLogService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
@Service("dictUserLogService")
public class DictUserLogServiceImpl extends CommonServiceImpl<DictUserLog>  implements DictUserLogService{

	@Resource
	private DictUserLogDao dictUserLogDao;
	
	@Autowired
	public void setCommonService() {
		// TODO Auto-generated method stub
		super.setCommonService(dictUserLogDao);
	}

	@SuppressWarnings("rawtypes")
	public DictUserLogServiceImpl(){
		
	}
}
