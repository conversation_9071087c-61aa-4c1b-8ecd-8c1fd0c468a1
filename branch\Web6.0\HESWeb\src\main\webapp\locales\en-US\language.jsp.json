{"login": {"username": "User Name", "pswd": "Password", "usernameNull": "User name cannot be empty!", "pswdNull": "Password cannot be empty!", "AccountNull": "Account  cannot be empty!", "MailNull": "Mail  cannot be empty!", "TelephoneNull": "Telephone cannot be empty!", "RoleNull": "Role cannot be empty!", "OrgIdNull": "rgId cannot be empty!", "remeberMe": "Remember Me", "login": "<PERSON><PERSON>", "loginOnOtherPlace": "You have signed in somewhere else, please login again!", "accountDisabled": "Account disabled, you are forced to log off!", "world": "World", "America": "America", "Nigeria": "Nigeria", "Australia": "Australia", "ACP": "American Cloud Platform", "notExistUser": "User Not Exist!", "LockedUser": "User Locked!", "errorPwd": "Password Incorrect!", "disabledUser": "User Disabled!", "errorUser": "User Exception!", "loginFirst": "Please login first!", "logoutFailed": "<PERSON><PERSON><PERSON> failed, please try again!", "logout": "Logout", "tenantIsStop": "The tenant has been disabled!", "accLgedElsewhere": "This user account is logged elsewhere!", "userDontExist": "The user account doesn't exist!", "toRevisePwd": "Your password has expired. Please change it.", "firstLoginRevisePwd": "Please change your password for the first login.", "firstLoginResetPassword": "First login to reset password", "incorrectPcCode": "Incorrect PC code, If you have any questions, please contact the system administrator", "startTimeNotEffect": "The start time has not yet taken effect, If you have any questions, please contact the system administrator", "pcCodeOverdue": "The PC code was Overdue, If you have any questions, please contact the system administrator", "pcCodeUnavailable": "The PC code is unavailable, If you have any questions, please contact the system administrator", "meterNumUnavailable": "The Meter Number is unavailable, If you have any questions, please contact the system administrator"}, "system": {"systemName": "ClouESP", "systemVersion": "V6.0", "copyRight": "Copyright @ 2020 SHENZHEN CLOU. All rights reserved.", "determine": "OK", "cancel": "Cancel", "notifications": "Notifications", "information": "Information", "message": "Message", "aydeltherdate": "Confirm to delete this record?", "submit": "OK", "pseleData": "Please select a data!", "pleaseInterNum": "Please enter a number!", "reqexc": "Abnormal request!", "delete": "Del", "add": "Add", "edit": "Edit", "change": "Change RF To GPRS", "inquiryTitle": "Notifications", "delSuccess": "Delete success!", "delFail": "Delete fail!", "abnoOpera": "Abnormal operation!", "addSucc": "Add Success!", "updateSucc": "Update Success!", "saveSucc": "Save Success!", "saveFail": "Save fail!", "cancelSucc": "Cancel success!", "cancelFail": "Cancel fail!", "requestError": "Request error!", "operSucce": "Operation succeeded!", "nameExist": "The account already exists!", "systemException": "System Exception!", "UCIException": "UCI Exception!", "selectNoData": "Can't find data!", "operation": "Operation", "export": "Export", "print": "Print", "start": "Start", "end": "End", "startdate": "Select Date", "validSucc": "Verification passed", "refreshTree": "Refresh Tree Node.", "checkLicense": "Please check that the license is correct"}, "home": {"devicesStatistic": "Devices Statistic", "meters": " Meters", "concentrators": "Concentrators", "communicators": "Communicators", "selectiveCategory": " Selective Category", "manufacturer": "Manufacturer", "model": " Model", "communication": "Communication", "name": " Name", "number": "Number", "rate": " Rate", "date": "Date", "collectionIntegrity": "Collection Integrity", "completeRate": " Complete Rate", "completedNumber": "Completed Number", "partialNumber": "Partial Number", "failedNumber": "Failed Number", "eventsStatistic": "Events Statistic", "eventsTotal": "Events Total", "classifiedStatistcOfEvents": "Classified Statistic of Events", "overallDaily": "Overall Daily Integrity Ratio (%)", "classifiedStatistcOfMeter": "Classified Statistic of Meter", "supplyStaticOfSubOrgan": "Supply Static of Sub Organization", "supplyStatistic": "Supply Statistic", "salesStatisticOfCustomer": "Sales Statistic of Customer", "organization": "Organization", "type": "Type", "powerSupply": "Supply(kWh)", "customerNumber": "Number", "supplyStatisticDaily": "The Past 7 Days Of Supply Statistic(kWh)", "supplyStatistic15Daily": "The Past 15 Days Of Supply Statistic(kWh)", "supplyStatistic30Daily": "The Past 30 Days Of Supply Statistic(kWh)", "supplyStatistic6Month": "The Past 6 Months Of Supply Statistic(kWh)", "supplyStatistic12Month": "The Past 12 Months Of Supply Statistic(kWh)", "Last7days": "7", "Last15days": "15", "Last30days": "30", "supplyCount": "Supply Statistic Of Sub Organization", "onlineMeter": "Online Meters", "onlineCommunicator": "Online Communicators", "onlineConcentrators": "Online Concentrators", "yesterdayPowerSupply": "Yesterday Power Supply(kWh)", "other": "Other", "noData": "No Data", "Last6months": "6", "Last12months": "12"}, "error403": {"title": "Page not found!", "content": " Sorry, the page is not authorized~"}, "error404": {"title": "Page not found!", "content": "Sorry, the page seems to go to Mars~"}, "error500": {"title": "Internal server error!", "content": "The server seems to be wrong..."}, "noPermissions": {"code": "10002", "title": "No access!", "content": "If you need access, please contact the administrator..."}, "index": {"home": " Home", "logout": "Logout", "search": "Search", "meter": "<PERSON>er", "commnuicator": "Communicator", "serialNumber": "Serial Number", "name": "Name", "mac": "LDN", "model": "Model", "searchFor": "Search for...", "favorites": "Favorites", "myProfile": "My Profile", "passwordSetting": "Password Setting", "licenseSetting": "License Setting", "confCanFavorite": "Confirm to cancel the favorite?", "advancedSearch": "Advanced Search", "frimwareVersion": "Firmware Version", "measurementGroup": "Measurement Group", "collectionSchemeGroup": "Collection Scheme Group", "line": "Line", "transformer": "Transformer", "manufacturer": "Manufacturer", "commuicationType": "Communication Type", "more": "more", "selected": "Selected", "pleaseChoose": "Please Choose", "communicatorSn": "Communicator SN", "organization": "Organization", "headAlarmTitle": "Meter Alarm Event Report", "supplyStatisticsReport": "Supply Statistics Report", "noAlarm": "Pls try again later, No event Sub are now.", "customer": "Customer", "telephoneNum": "Telephone No", "tel": "TEL", "voltageLevel": "Voltage Level", "refreshOrgLineTransformer": "Refreshing the Organizational Tree...", "searchOrgLineTransformer": "Searching Node ...", "loadTreeNode": "Loading Tree Node ...", "dailyReport": "Daily Report", "monthlyReport": "Monthly Report", "refreshReportTree": "Refreshing the Report Tree..."}, "connectOrDisconnect": {"function": "Function", "system": "System", "url": "URL", "connectDisconnect": "Connect / Disconnect", "power7000": "Power7000"}, "scheduleReadsReport": {"title": "Schedule Reads Report", "overview": "Overview", "manufacturer": "Manufacturer", "model": "Model", "communication": "Communication", "ydayInteRate": "Yesterday Data Reads Integrity Rate", "ydayCompRate": "Yesterday Meter Reads Complete Ratio", "completed": "Completed", "partial": "Partial", "failed": "Failed", "details": "Details", "dMeterInteRate": "Daily Meter Reads Integrity Rate", "start": "Start", "end": "End", "mmeterInteRate": "Monthly Meter Reads Integrity Rate", "dInteRateManu": "Daily Integrity Rate by Manufacturers", "mInteRateManu": "Monthly Integrity Rate by Manufacturers", "dInteRateSMmanu": "Daily Integrity Rate by Single/Multiple Manufacturer(s)", "mInteRateSMmanu": "Monthly Integrity Rate by Single/Multiple Manufacturer(s)", "dInteRateModels": "Daily Integrity Rate by Models", "mInteRateModels": "Monthly Integrity Rate by Models", "dInteRateSMModel": "Daily Integrity Rate by Single/Multiple Model(s)", "mInteRateSMModel": "Monthly Integrity Rate by Single/Multiple Model(s)", "dInteRateCommun": "Daily Integrity Rate by Communications", "mInteRateCommun": "Monthly Integrity Rate by Communications", "dInteRateSMCommun": "Daily Integrity Rate by Single/Multiple Communication(s)", "mInteRateSMCommun": "Monthly Integrity Rate by Single/Multiple Communication(s)", "dInteRateOrg": "Daily Integrity Rate by Organization", "mInteRateOrg": "Monthly Integrity Rate by Organization", "dInteRateSMOrg": "Daily Integrity Rate by Single/Multiple Organization(s)", "mInteRateSMOrg": "Monthly Integrity Rate by Single/Multiple Organization(s)", "dailyInteRate": "Daily Integrity Rate", "monthlyInteRate": "Monthly Integrity Rate", "dmeterInteRadeio": "Daily Meter Reads Integrity Ratio", "mmeterInteRadeio": "Monthly Meter Reads Integrity Ratio", "dInteRatioManu": "Daily Integrity Ratio by Manufacturers", "calcIntegrityRate": "Calculate Integrity Rate", "calcLineLoss": "Calculate Line Loss", "holdManual": "Background recalculated,please wait"}, "assetScheduleSchemeDetailList": {"title": "Task List", "taskId": "Task Id", "id": "Id", "taskType": "Task Type", "profileId": "Profile Name", "taskCycleType": "Task Cycle Type", "taskCycle": "Task Cycle", "startTime": "Start Time", "endTime": "End Time", "pseleData": "This scheme is referenced and cannot be deleted!", "confirmDel": "Confirm to delete this task?", "addReadsTask": "Data Reads Task", "addEvenTask": "Event Reads Task", "addTimeTask": "Time Synchronization Task", "addOnline": "Online Status Reads Task", "dcuSyncTime": "DCU Sync Time", "collSchemeMage": "Collection Scheme Management", "daily": "Daily", "hourly": "Hourly", "minutely": "Minutely", "monthly": "Monthly", "pleaseChoose": "---Please choose---", "successfullyModified": "Successfully modified", "addedSuccessfully": "Added successfully"}, "assetScheduleSchemeList": {"title": "Scheme List", "id": "Id", "name": "Scheme Name", "meterStandard": "Meter Standard", "protocolId": "ProtocolId", "meterNumber": "Meter Count", "referenceScheme": "Reference Scheme", "description": "Description", "pseleData": "Please select Scheme List data!", "confirmDel": "Confirm to delete Scheme?", "canBeDeleted": "The scheme is associated with meters that cannot be deleted!"}, "dataIntegrityList": {"headTitle": "Miss Data Tracing", "evenExportTitle": "Meter Data & Event Export", "id": "No", "serizlNo": "Meter SN", "tv": " Date", "mfrId": "Manufacturer", "modelId": "Model", "communicator": "Communicator SN", "commId": "Communication", "progress": "Progress", "lastTask": "Last Task", "taskResult": "Task Result", "failureCause": "Failure Cause", "integrity": "Rate(%)", "integrityRate": "Integrity Rate (%)", "analysis": "Reference Analysis", "export": "Export", "print": "Print", "taskResultFailed": "Failed", "taskResultSuccess": "Success", "all": "All", "delayDay": "Progress Delay (Days)", "progressDelayReport": "Progress Delay Report", "missDataReport": "Missing Data Report", "integrityRateReport": "Integrity Rate Report", "updateTv": "Status Update Time", "comStatus": "Com Status"}, "dataIntegrityDetails": {"id": "No", "serizlNo": "SN", "tv": "Time", "manufacturer": "Manufacturer", "model": "Model", "communication": "Communication", "profile": "Profile", "readStatus": "Read Status", "noMeasurementGroup": "No measurement group added."}, "dataMeterEventList": {"headTitle": "Meter Event Report", "headAlarmTitle": "Meter Alarm Event Report", "deviceId": "Device No", "eventId": "Event No", "sn": "SN", "export": "Export", "print": "Print", "tv": " Time", "eventType": "Event Type", "event": "Event", "eventDetail": "Event Details"}, "dayList": {"id": "Id", "dayName": "Day ID", "startTime": "Start Time", "rate": "Rate", "add": "Add", "addDay": "Add Day", "hour": "Hour", "minute": "Minute", "second": "Second", "delete": "Delete", "title": "Day List", "pleaseSelectData": "Please select a day in Day List!", "pleSetRateDayList": "Please set up Rate in Day List!", "lastOneTimePeriod": "Day data at least one time period!", "areBeAtMost": "255 days are allowed to be created at most!", "inteAllAtMost": "8 intervals are allowed at most in a day!"}, "device_list": {"headTitle": "Meter Event Report", "pageTitle": "Meter Configuration", "title": "Meter List", "id": "Id", "sn": "Meter SN", "modelName": "Model", "communicator": "Communicator SN", "comType": "Commmunication", "set": "Write", "get": "Read", "delete": "Delete", "parameterType": "Parameter Type", "operation": "Operation", "profiles": "Profiles", "channels": "On Demand Reads", "seleLeastOne": "Select at least one table data", "seleCommunicator": "Please Select a communicator", "leastOneChoose": "Choose at least one copy of reading", "communicatorList": "Communicator List", "donotRepeat": "Please do not repeat to operation."}, "device_read_parameter_list": {"title": "Results", "id": "Id", "sn": "Meter SN", "parameterType": "Parameter Type", "parameterItem": "Parameter Item", "meterGroup": "Meter Group", "requestTime": "Request Time", "responseTime": "Response Time", "status": "Status", "reason": "Reason", "delete": "Delete", "export": "Export", "print": "Print", "total": "Total", "completed": "Completed", "success": "Success", "failed": "Failed", "processing": "Processing", "tariffType": "Tariff Type", "active": "Active", "passive": "Passive", "touGroup": "TOU Group", "limiterGroup": "Limiter Group", "measurementGroup": "Measurement Group", "pleAddTask": "Please add task!", "areToExcTask": "You are sure to excute this task list?", "stepTariffGroup": "Step Tariff Group", "prepay": "Prepay", "friend": "Friend", "touTariff": "Tou Tariff"}, "linkedEntityRelationship": {"meterSN": "Meter SN", "logicalName": "Logical Name", "customerType": "Customer Type", "organization": "Organization", "linkedMeter": "<PERSON>ed Meter", "linkedMeterList": "Linked Meter List", "linkedTransformer": "Linked Transformer", "linkedTransformerList": "Linked Transformer List", "communicatorSN": "Communicator SN", "communicatorName": "Communicator Name", "transformerName": "Transformer Name", "transformerSN": "Transformer SN", "selectLeastLine": "Please select a line first.", "selectLeastTransformer": "Please select a line Transformer."}, "importTempGprsMeter": {"downFileImport": "Download Template File", "tempFileImport": "Import Template File", "tempFile": "Template File", "meterType": "Meter Type", "DCU": "DCU", "importStatus": "Import Status", "importLog": "Import Log", "commAddress": "Common Address", "logicalAddr": "Logical Address", "import": "Import", "finish": "Close"}, "deviceList": {"abnormalRequestFromUCI": "Abnormal request from UCI", "title": "Asset Management", "basicTitle": "Basic Properties", "communication": "COM Properties", "groupPro": "Group Properties", "other": "Other", "addGprsMeter": "Add GPRS Meter", "addMeterCommun": "Add Meter to Communicator", "relateMeterCommun": "Relate Meter to Communicator", "addCommunicatior": "Add Communicator", "id": "Id", "deviceSN": "Meter SN", "deviceType": "Device Type", "name": "Name", "mac": "Logical Name", "model": "Model", "communicationType": "Communication Type", "manufacturer": "Manufacturer", "limiter": "Limiter", "tou": "TOU", "stepTariff": "Step Tariff", "schemeGroup": "Collection Scheme Group", "meaGroup": "Measurement Group", "encrypt": "Encrypt", "ek": "EK", "ak": "AK", "llsPwd": "LLS Password", "authType": "Authentication Type", "port": "Port", "ip": "IP", "organation": "Organization", "channel": "Channel", "schedule": "Schedule", "firmVer": "Firmware Version", "pleaseChoose": "---Please choose---", "meterNullMsg": "Meter is Null", "commnuicatorNullMsg": "Communicator is Null", "pleSelDevice": "Please select Device List!", "ipValidErrorMsg": "Ip address format is incorrect", "noDeviceFound": "No device found", "deviceNumExists": "Device number already exists", "pleSeleCOmmun": "Please select Communicator!", "pleSaveOrDel": "Please save data or delete data!", "tCommunHChild": "The Communication has a child data!", "pleSelOrganation": "Please select Organation!", "addr": "Address", "simNum": "SIM Num", "meterTitle": "<PERSON>er", "commTitle": "Communicator", "meterInfo": "<PERSON>er", "commInfo": "Communicator", "commSN": "Communicator SN", "communicator": "Communicator", "ct": "CT", "pt": "PT", "indexDcu": "Index In DCU", "comPort": "COM Port", "keyMeter": "VIP Meter", "archivedMeter": "Archived Meter", "changeSuccess": "Change success!", "changeTitle": "Meter Communication Type Change", "longitude": "Longitude", "latitude": "Latitude", "setLongLat": "Set latitude and longitude", "server": "Server", "pleaseSelectedProfile": "Please select Profile!", "over100": "Error! The meter amount in HES now reaches 100% percent of the meter amount in your license.", "over90": "Warn! The meter amount in HES now reaches 90% percent of the meter amount in your license.", "createTime": "Registration Time"}, "divFMUPlanMeterList": {"pleSelDevType": "Please select Device Type!", "pleSelManufac": "Please select Manufacturer!", "pleEntPlanDesc": "Please enter Plan Description!", "pleSelPlanTime": "Please select Plan Start Time!", "pleSelPlanExpTime": "Please select Plan Expiry Time!", "planExpPlanTime": "Plan Expiry Time must be after Plan Start Time!", "pleEntNewVer": "Please enter New Version!", "pleEntImgeIden": "Please enter Image Identifier!", "pleSelFirFile": "Please select Firmware File!", "pleSelTaskStartTime": "Please select Task Start Time!", "pleSelTaskEndTime": "Please select Task End Time!", "taskStartLaterSTime": "Task start time must be equal or later than Task End Time!", "pleSelTaskCyc": "Please select Task Cycle!", "confCreatePlan": "Confirm to create this plan?", "pleSelModel": "Please select Model!", "plan": "Plan", "planReport": "Plan Report", "jobReport": "Job Report", "deviceSearch": "Device Search", "manufacturer": "Manufacturer", "model": "Model", "deviceSN": "Device SN", "planCreation": "Plan Creation", "planDesc": "Plan Description", "planStartTime": "Plan Start Time", "planExpTime": "Plan Expiry Time", "newVersion": "New Version", "imageIdentifier": "Image Identifier", "firmwareFile": "Firmware File", "taskStartTime": "Task Start Time", "taskEndTime": "Task End Time", "taskCycle": "Task Cycle", "hour1": "1 hour", "hour2": "2 hours", "hour3": "3 hours", "hour4": "4 hours", "hour5": "5 hours", "hour6": "6 hours", "hour7": "7 hours", "hour8": "8 hours", "hour9": "9 hours", "hour10": "10 hours", "hour11": "11 hours", "hour12": "12 hours", "hour24": "24 hours", "deviceType": "Plan Type", "versionType": "Version Type", "currentVersion": "Current Version", "expTimeAfterStaTime": "Plan Expiry Time must be after Plan Start Time!", "curTimeAfterExpTime": "Plan Expiry Time must be greater than current Time!", "pleaEntNewVer": "Please enter New Version!", "pleaEntImaIdent": "Please enter Image Identifier!", "pleaSelFirmFile": "Please select Firmware File!", "pleaSelStartTime": "Please select Task Start Time!", "pleaSelEndTime": "Please select Task End Time!", "startTimeLaterThanEndTime": "Task start time must be equal or later than Task End Time!", "pleaSelTaskCycle": "Please select Task Cycle!", "pleaSelVersionType": "Please select Version Type!", "midFV": "MID Version", "appFV": "APP Version", "confCreaPlan": "Confirm to create this plan?", "information": "Information", "commMeter": "GPRS Module of Meter", "commCommunicatior": "Communication Module of Communicatior", "broadcastMeter": "(Broadcast) Meter", "broadcastComm": "(Broadcast) Communicatior", "broadcastCommMeter": "(Broadcast) Communication Module of Meter", "broadcastCommCommunicatior": "PLC Module of DCU", "broadcastPlcComm": "(Broadcast) PLC Module of Communicatior", "broadcastHplcComm": "(Broadcast) PLC Module of Meter"}, "addAssetGPRSMeter": {"sn": "SN", "comSn": "Communicator SN", "refCommun": "Reference Communicator", "referenceMeter": "Reference Meter", "refSnIsEm": "Reference Meter SN is Empty!", "successMatch": "Successful match", "devTypeMis": "Device type mismatch", "pleMatchData": "Please match data first!"}, "FMUPlanMeterList": {"title": "Devices List", "sn": "Meter SN", "manufacturerName": "Manufacturer", "modelName": "Model", "export": "Export", "print": "Print", "fwVersion": "Current Version"}, "FMUPlanMeterList0": {"title": "Devices List", "sn": "Meter SN", "manufacturerName": "Manufacturer", "modelName": "Model", "currentVesion": "Current Version", "newVersion": "New Version", "startTime": "Start Time", "export": "Export", "print": "Print", "expiryTime": "Expiry Time"}, "jobReportJobList": {"title": "Jobs", "opt": "Action", "id": "ID", "meterIdDfj": "Meter ID", "deviceTypeDfj": "Device Type", "snDfj": "Meter SN", "manufacturerIdDfj": "Manufacturer", "modelNameDfj": "Model", "currentVesionDfj": "Old Version", "newVersionDfj": "New Version", "startTimeDfj": "Start Time", "expiryTimeDfj": "Expiry Time", "lastExecTimeDfj": "Last Execute Time", "stateDfj": "Status", "blockSizeDfj": "Block Size (Byte)", "blockCountDfj": "Transferred Block Count", "fwuProgressDfj": "Progress", "export": "Export", "print": "Print", "confToCancJob": "Confirm to cancel this job?", "cancelled": "Cancelled", "failedReasonDfj": "Reason", "runing": "Running", "done": "Done", "cancel": "Cancelled", "waiting": "Waiting", "expired": "Expired", "jobNoExcute": "The job hasn't begun to run!"}, "limiterGroupList": {"id": "Id", "type": "Type", "name": "Group Name", "protocolId": "Meter Standard", "meterNumber": "Number of Meters", "introduction": "Description", "meterCount": "Meter Count", "title": "Limiter Group List", "titleDetails": "Limiter Group Details", "add": "Add Limiter Group", "delete": "Delete", "pleaseSelectData": "Please select a group in Limiter Group List!", "dateError": "Please enter the correct date format"}, "limiterList": {"title": "Limiter Configuration", "id": "Id", "name": "<PERSON><PERSON>", "value": "Value", "meterCount": "Meter Count", "getProgress": "GET PROGRESS", "day": "Day", "week": "Week", "season": "Season", "upcoming": "Upcoming", "processing": "Processing", "success": "Success", "failed": "Failed", "timeout": "Timeout", "cancelled": "Cancelled", "meterCanBeEm": "Meter can not be empty", "choLeastCopRead": "Choose at least one copy of reading", "seleLeastOneData": "Select at least one table data", "pleLimitCofVal": "Please set Limiter Configuration Value!", "pleLimitCofValNo": "Please set Limiter Configuration Value is Number!", "calGetPtogress": "Calendar Get Progress", "speDayGetPtogress": "Special Day Get Progress", "stepTariffGetPtogress": "Step Tariff Get Progress"}, "measurementGroupList": {"title": "Measurement Group List", "titleDetails": "Measurement Group Details", "add": "Add Measurement Group", "delete": "Delete", "id": "Id", "type": "Type", "name": "Group Name", "protocolId": "Meter Standard", "referenceGroup": "Reference Group", "meterNumber": "Number of Meters", "introduction": "Description", "meterCount": "Meter Count", "editProfile": "Edit Profile", "pleaseSelectData": "Please select a group in Measurement Group List!", "confirmToDel": "Confirm to delete this group?", "addProfile": "Add Profile"}, "meterDataReportList": {"frequency": "Frequency", "headTitle": "Meter Data Report", "intervalEnergy": "Power Quality Report", "times": " Time", "groupId": "Group", "channel": "Data Channel", "serizlName": "SN", "export": "Export", "print": "Print", "graphVisualiz": "Graph Visualization", "noDataChannel": "No Data Channel selected", "tableView": "Table view", "lineChart": "Line chart", "columnChart": "Column chart", "reduction": "Refresh", "saveAsPicture": "Save as picture", "mustSingTable": "Must be a single table statistics", "pleSelColumn": "Please select a column", "searchFor": "Search for...", "selectSn": "Please select SN", "resultOver": "More than 200 result sets, please re-select the criteria!", "selectAll": "Select All", "allSelected": "All selected"}, "planReportJobList": {"title": "Jobs", "opt": "Action", "id": "ID", "meterId": "Meter ID", "sn": "Meter SN", "manufacturerName": "Manufacturer", "modelName": "Model", "currentVesion": "Current Version", "newVersion": "New Version", "lastExecTime": "Last Execute Time", "state": "Status", "blockSize": "Block Size (Byte)", "blockCount": "Transferred Block Count", "fwuProgress": "Progress", "failedReason": "Reason", "export": "Export", "print": "Print", "cancel": "Cancel", "confiCanCelJob": "Confirm to cancel this job?"}, "planReportList": {"title": "Plans", "opt": "Action", "id": "id", "deviceType": "Device Type", "introduction": "Plan Description", "manufacturerId": "Manufacturer", "modelName": "Model", "currentVesion": "Current Version", "newVersion": "New Version", "taskCycle": "Task Cycle", "filePath": "File Name", "startTime": "Start Time", "expiryTime": "Expiry Time", "taskStartTimeStr": "Task Start Time", "taskEndTimeStr": "Task End Time", "state": "If Expired", "done": "Done", "expired": "Expired", "running": "Running", "export": "Export", "print": "Print", "cancelled": "Cancelled", "cancel": "Cancel", "conToCanPlan": "Confirm to cancel this plan?", "waiting": "Waiting", "meter": "<PERSON>er", "communicator": "Communicator", "valid": "<PERSON><PERSON>"}, "profileList": {"title": "Profile List", "add": "Add", "delete": "Delete", "id": "Id", "mgId": "Measurement Group Id", "profileId": "Profile ID", "profileType": "Profile Type", "profileType_view": "Profile Type", "profileName": "Profile Name", "profileCycleType": "Profile Interval Type", "profileCycle": "Profile Interval", "protocolCode": "OBIS Code", "pleaseSelectData": "Please select a profile in Profile List!", "pleSelAntOne": "This profile has been added, please select another one!", "confirmToDel": "Confirm to delete this profile?"}, "seasonList": {"id": "Id", "seasonName": "Season ID", "startTime": "Start Time", "weekProfile": "Week Profile", "title": "Season List", "add": "Add", "delete": "Delete", "month": "Month", "dayofMonth": "Day of Month", "year": "Year", "dayofWeek": "Day of Week", "hour": "Hour", "minute": "Minute", "second": "Second", "pleaseSelectData": "Please select a season in Season List!", "pleSetSeasonList": "Please set up Week Profile in Season List!", "seasonAreBeAtMost": "255 seasons are allowed to be created at most!", "seleLeastOneData": "Select at least one table data !", "noMeterGroup": "Please check <PERSON><PERSON>`s Group properties has been configured or not! "}, "selectedDataChannelList": {"title": "Selected Data Channels", "up": "Up", "down": "Down", "delete": "Delete", "id": "Id", "mgId": "Measurement Group Id", "profileId": "Profile Id", "dataitemId": "Dataitem Id", "dataitemName": "Data Channel", "sortId": "Sort Id"}, "specialDayList": {"title": "Special Day List", "id": "Id", "specialDayName": "Special Day ID", "date": "Date", "dayProfile": "Day Profile", "month": "Month", "dayofMonth": "Day of Month", "year": "Year", "dayofWeek": "Day of Week", "pleaseSelectData": "Please select a day in Sppecial Day List!", "pleSpeDayDprofile": "Please set Special Day List Day Profile!", "daysBeAtMost": "255 days are allowed to be created at most!", "dayAssCanBeDel": "The day is associated with Special Day List that cannot be deleted!"}, "sysIntegrationLogList": {"title": "System Integration Log", "id": "ID", "sn": "SN", "fromId": "System", "tv": "Request Time", "requestType": "Request Type", "requestSubType": "Request Sub Type", "responseResult": "Response Result", "requestDetail": "Request Detail", "responseDetail": "Response Detail", "export": "Export", "print": "Print"}, "timeSynchronizationLog": {"title": "Time Synchronization Log", "id": "ID", "sn": "SN", "tv": "Task Time", "strSynResult": "Synchronization Result", "meterTv": "Meter Time", "systemTv": "System Time", "synTv": "Synchronization Time", "failedReason": "Failed Cause", "export": "Export", "print": "Print"}, "touGroupList": {"id": "Id", "title": "Meter Group Mgmt", "touTitle": "TOU Group List", "touTitle_": "TOU Group Details", "measurement": "Measurement", "tou": "TOU", "limiter": "Limiter", "addTouGroup": "Add TOU Group", "calendar": "Calendar", "specialDay": "Special Day", "delete": "Delete", "type": "Type", "name": "Group Name", "protocolId": "Meter Standard", "meterNumber": "Number of Meters", "introduction": "Description", "meterCount": "Meter Count", "referenceGroup": "Reference Group", "pleaseSelectData": "Please select a group in TOU Group List!", "canNotBeDel": "Can not be deleted!", "confirmToDel": "Confirm to delete this group?"}, "tracingLogList": {"headTitle": "Meter Group Mgmt", "meterTracingLog": "Metering Tracing Log", "timeSynLog": "Time Synchronization Log", "sysInteLog": "System Integration Log", "userLog": "User Log", "deviceSn": "Device SN", "service": "Service", "logLevel": "Log Level", "startTime": "Start Time", "endTime": "End Time", "reqType": "Request Type", "user": "User", "logType": "Log Type", "logSubType": "Log Sub Type", "clikcSearchBut": "Click the search button for searching", "sychResult": "Synchronization Result", "title": "Tracing Log", "export": "Export", "print": "Print", "id": "ID", "date": "Request Time", "serviceId": "Service", "type": "Log Type", "level": "Log Level", "content": "Content", "success": "Success", "failed": "Failed", "normal": "Normal"}, "userLogList": {"title": "User Log", "userId": "User Id", "tv": "Operation Time", "name": "User", "logType": "Log Type", "logSubType": "Log Sub Type", "detail": "Content", "startTimeCanNot": "Start time or end time can not be empty!", "logTimeBeWDay": "The log query time shall be within a day!"}, "unSelectedDataChannelList": {"title": "Optional Data Channels", "title1": "Reading Data Channels", "profileInterval": "Profile Interval", "add": "Add", "id": "Id", "mgId": "Measurement Group Id", "profileId": "Profile Id", "dataitemId": "Dataitem Id", "dataitemName": "Data Channel", "sortId": "Sort Id", "pleaseSelectData": "Please select a data channel in this list!", "hasMovedToTheTop": "Has moved to the top!", "hasMovedToTheFoot": "Has moved to the foot!"}, "weekList": {"id": "Id", "weekName": "Week ID", "dayProfile": "Day Profile", "add": "Add", "delete": "Delete", "title": "Week List", "pleaseSelectData": "Please select a week in Week List!", "pleSetWeekList": "Please set up Day Profile in Week List!", "youCanSingleDay": "You can not delete a single day!", "seasonListCanBeDel": "The week is associated with Season List that cannot be deleted!", "weeksToBeAtMost": "255 weeks are allowed to be created at most!", "weekCanBeDel": "The day is associated with Week List that cannot be deleted!"}, "sysUserList": {"headTitle": "User & Role", "title": "User List", "orgPassword": "Original Password", "newPassword": "New Password", "confirm": "Confirm", "enterNewPasswordAgain": "Enter new password again", "askYouForgotPwd": "Ask your system administrator for help if you forgot password.", "id": "Id", "username": "Account", "name": "Name", "email": "Mail", "mobilePhone": "Telephone No.", "userState": "Status", "userType": "User Type", "roleId": "Role", "orgId": "Organization", "lastLoginTime": "Last Login Time", "opt": "Action", "user": "User", "role": "Role", "organization": "Organization", "disable": "Disable", "enable": "Enable", "addUser": "Add User", "delUser": "Delete User", "resPwd": "Reset Password", "oldPasswordError": "Original password error!", "newPasswordError": "New Password and Confirm Password inconsistent!", "resetPassSucc": "Reset Password Success!", "confirmDisable": "Confirm to disable this user account?", "confirmEnable": "Confirm to enable this user account?", "selectUser": "Please select a user!", "myProfile": "My Profile", "passSet": "Password Setting", "licenseSet": "License Setting", "administrator": "Admininistrator", "normal": "Normal", "password": "Password", "pleaEnterPass": "Please enter your password", "roleName": "Role", "accountNotExist": "Account Not Exist", "validatePwd": "Password length is 8 to 15 bits, must contain letters and numbers, letters case-sensitive.", "originConfirmRepeat": "The original password is not the same as the new password.!", "passwordTooLong": "Password length is too long, please input less than 15 digits", "originPasswordError": "Origin Password Error."}, "sysRoleList": {"title": "Role List", "id": "Id", "name": "Name", "userCount": "User Count", "description": "Description", "delete": "Delete", "addRole": "Add Role", "rolenameNull": "Name cannot be empty!", "selectRole": "Please select a role!", "deleteRole": "Confirm to delete this role?", "queryResultNull": "The query result is null!", "unableDel_user": "The role has sub users that cannot be deleted!", "operaConfig": "Operation Configuration", "roleNameExist": "The role name already exists!"}, "meterConfigurationList": {"systemFunction": "System Function", "title": "Operation Configuration", "id": "Id", "isSelect": "Is Select", "operationname": "Operation Item", "description": "Description"}, "organizationList": {"title": "Organization List", "addOrganization": "Add Organization", "delete": "Delete", "name": "Name", "id": "Id", "userCount": "User Count", "meterCount": "Meter Count", "mobile": "Telephone No.", "address": "Address", "description": "Description", "contactMan": "Contact Man"}, "sysOrganizationList": {"id": "Id", "parentOrg": "Parent Orgnization", "name": "Name", "mobile": "Telephone No.", "address": "Address", "description": "Description", "unableDel_org": "The organization has sub organizations that cannot be deleted!", "unableDel_comm": "The organization has sub communicators that cannot be deleted!", "unableDel_meter": "The organization has sub meters that cannot be deleted!", "unableDel_user": "The organization has sub users that cannot be deleted!", "moveOrg": "Move Organization", "moveOrgError": "Unable to select the organization itself!", "selectOrg": "Please select a organization!", "organization": "Organization", "addOrganization": "Add Organization", "moveSuccess": "Moved Success", "admitFourLevelOrg": "4 levels of organizations are allowed to be created at most!", "deleteOrg": "Confirm to delete this organization?", "noMoveToChildOrg": "Organization is not allowed to move to its subordinate organization!"}, "device_read_result_list": {"id": "Id", "sn": "<PERSON><PERSON>", "profileId": "Profile Id", "profileName": "Profile", "communication": "Communication", "requestTime": "Request Time", "dataChannel": "Data Channel", "value": "Value", "reponseTime": "Data Time", "startTime": "Start Time", "endTime": "End Time", "status": "Status", "statusView": "Status", "export": "Export", "print": "Print"}, "device_read_schedule_list": {"id": "Id", "sn": "Serial Number", "model": "Model", "communication": "Communication", "profileId": "profileId", "profile": "Profile", "startTime": "Start Time", "endTime": "End Time"}, "device_read_channel_list": {"title": "Results", "checkReadTitle": "Click the read button for reading", "id": "Id", "sn": "Meter SN", "communicator": "Communicator SN", "commmunication": "Commmunication", "dataChannel": "Data Channel", "value": "Value", "status": "Status", "statusView": "Status", "requestTime": "Request Time", "responseTime": "Response Time", "export": "Export", "print": "Print", "total": "Total", "completed": "Completed", "success": "Success", "failed": "Failed", "processing": "Processing", "read": "Read", "cancel": "Cancel", "cancelFailure": "Cancel the failure"}, "deploymentAndClusterManagement": {"title": "Deployment & Cluster Management", "serviceConfig": "Server & Service Configuration", "channelService": "Configuration Details", "name": "Name", "ip": "IP", "hostId": "Host ID", "server": "Server", "type": "Type", "ipOrHostId": "IP/Host ID", "status": "Status", "properties": "Properties", "value": "Value", "description": "Description", "addServer": "Add Server", "addService": "Add Service", "deleteServer": "Delete Server", "deleteService": "Delete Service", "action": "Action", "save": "Save", "editChannel": "Edit Channel", "unableDel_server": "The server has sub services that cannot be deleted!", "unableDel_service": "The service has sub properties that cannot be deleted!", "serviceExistSystem": "This service already exists in the system!", "serviceExistServer": "This service already exists in the server!", "ipExistSystem": "This ip already exist in the system!", "hostIdExistServer": "This host id already exist in the server!", "pleaseSelectService": "Please select a service!", "pleaseAddChannel": "Please add channel service list!", "nameExist": "This name already exist in the system!", "stop": "Stop", "running": "Running", "channel": "Channel", "messageBus": "Message Bus", "schedule": "Schedule", "UCI": "UCI", "calculation": "Calculation", "application": "Application"}, "serviceConfig": {"title": "Service Configuration", "id": "Id", "parent": "Parent ID", "serviceTypeTemp": "Service Type", "introduction": "Name", "serviceType": "Type", "ip": "IP/Host ID", "isOnline": "Status", "delete": "Delete"}, "meterDataEventExport": {"title": "Meter Data & Event Export", "dataChannel": "Data Channel", "exportProgress": "Export Progress", "exportTime": "Export Time", "result": "Result", "unableDel_server": "The server has sub services that cannot be deleted!", "unableDel_service": "The service has sub properties that cannot be deleted!", "serviceExistSystem": "This service already exists in the system!", "serviceExistServer": "This service already exists in the server!", "ipExistSystem": "This ip already exist in the system!", "hostIdExistServer": "This host id already exist in the server!", "pleaseSelectService": "Please select a service!", "pleaseAddChannel": "Please add channel service list!", "nameExist": "This name already exist in the system!"}, "stepTariffList": {"title": "Step Tariff Group List", "titleDetails": "Step Tariff Group Details", "id": "Number", "stepTariff": "Step Tariff", "description": "Description", "groupName": "Group Name", "stepName": "Step Name", "meterStandard": "Meter Standard", "startQuantity": "Start Quantity (kWh)", "meterNumber": "Meter Number", "endQuantity": "End Quantity (kWh)", "activateTime": "Activate Time", "price": "Price", "referenceGroup": "Reference Group", "addGroup": "Add Step Tariff Group", "addStep": "Add Step", "pleaseSelectData": "Please select the item first", "nameExist": "The Step tariff Group Name already exists!", "stepNameExist": "The Step tariff Name already exists!", "selectStepTariff": "Please select a Step Tariff Group!", "deleteStepTariff": "Confirm to delete this Step Tariff Group?", "selectStep": "Please select a step!", "delStep": "Confirm to delete this step?", "endGreaterThanStart": "End Quantity must be greater than Start Quantity!", "pleaseAddStep": "Please add step list!", "pleaseCorrectFormat": "Please enter the correct format data!", "reselectStepName": "Please select Step Name again!", "startQuantityError": " shall be consistent with End Quantity of the previous step!", "delete": "Delete", "save": "Save", "stepTariffGetPtogress": "Step Tariff Get Progress", "friendly": "Friendly", "prepay": "Prepaid", "touTariff": "TOU Tariff", "friendlyPeriod": "Friendly Period", "friendlyWeekDay": "Friendly Week Day", "friendlySpecialDay": "Friendly Special Day", "friendlyTou": "Friendly TOU", "periodId": "Period ID", "start": "Start", "end": "End", "weekDayId": "Week Day ID", "weekDay": "Week Day", "specialDayId": "Special Day ID", "enable": "Enable", "touId": "TOU ID", "prepayDetail": "Prepay Details", "touAtMost": "4 TOU Tariff are allowed to be created at most", "weekAtMost": "7 days are allowed to be created at most", "periodAtMost": "8 periods are allowed to be created at most", "startLaterEnd": "Start Time must be later than End Time!", "touExist": "has been exist", "periodRepeat": "Friendly periods should not overlap", "stepExist": "Step price has exist cannot add TOU price", "cannotOp": "Can't operate more than activation time"}, "meterConfiguration": {"title": "Meter Configuration", "canNotBeDelete": "The task at processing status cannot be deleted!", "pleaseSelectData": "Please select a task in this list!", "confirmDeleteTask": "You are sure to delete this task?", "stepNameExist": "This step name has been added, please select another one!"}, "commGroupUpgrade": {"paramTitle": "Device Parameter Option", "paramFile": "Parameter File", "set": "Setting", "dcuSn": "DCU SN", "setFirst": "Please Set Dcu Parameter First", "setParam": "Set DCU Parameter", "viewParam": "View DCU Paramete XML"}, "meterGroupUpgrade": {"plan": "Plan", "planReport": "Plan Report", "jobReport": "Job Report", "deviceSearch": "Device Search", "groupType": "Group Type", "group": "Group", "measurement": "Measurement", "TOU": "TOU", "limiter": "Limiter", "stepTariff": "Step tariff", "friendly": "Friendly", "search": "Search", "deviceSN": "Meter SN", "pleSelGroupType": "Please select Group Type!", "pleSelGroup": "Please select Group!", "pleEntPlanDesc": "Please enter Plan Description!", "noDeviceFound": "Please check if the meter file you selected is associated with the scheme or check if the selected scheme matches with the asset file information!", "startEqualExpiry": "Plan Expiry Time must be after Plan Start Time!", "taskStartEqualEnd": "Task start time must be equal or later than Task End Time!", "ifExpried": "If Expired", "valid": "<PERSON><PERSON>", "expired": "Expired", "startTime": "Start Time", "endTime": "End Time", "noFoundPlan": "No plans found!", "pleaseSelectAplan": "Please select a plan!", "pleaseSelectState": "Please select expried plan and waitting or expried job!", "createPlanSucc": "Created success, The newly created plan can be viewed in Plan Report tab.", "exceptionMsg": "Invoking exception of job service, please check and create plan again!", "jobUpgradeSucc": "All jobs of this plan have been upgraded successfully!", "all": "ALL", "pleaseSelModel": "Please select Model!", "hasMeter": "This Group has Meters can not delete"}, "divMGU_PlanMeterList": {"sn": "Meter SN", "manufacturer": "Manufacturer", "model": "Model", "groupType": "Group Type", "group": "Group", "startTime": "Plan Start Time", "expiryTime": "Plan Expiry Time"}, "MGU_planReportList": {"opt": "Action", "introduction": "Plan Description", "groupType": "Group Type", "groupName": "Group Name", "model": "Model", "state": "State", "taskCycle": "Task Cycle(Hour)", "done": "Done", "expired": "Expired", "running": "Running", "waiting": "Waiting", "startTime": "Plan Start Time", "expiryTime": "Plan Expiry Time", "taskStartTime": "Task Start Time", "taskEndTime": "Task Expiry Time"}, "MGU_planReportJobList": {"title": "Jobs", "id": "ID", "meterId": "Meter ID", "sn": "Meter SN", "manufacturer": "Manufacturer", "model": "Model", "status": "Status", "lastExecTime": "Last Execute Time", "reason": "Reason", "running": "Running", "done": "Done", "cancel": "Cancelled", "waiting": "Waiting", "expired": "Expired", "export": "Export", "print": "Print", "failedReason": "Reason", "addPlan": "Add Plan"}, "connOrDisconnList": {"title": "Results", "mainTitle": "Connect / Disconnect", "sn": "Meter SN", "commSN": "Communicator SN", "communication": "Communication", "command": "Command", "status": "Status", "requestTime": "Request Time", "responseTime": "Response Time", "connect": "Connect", "disconnect": "Disconnect", "pleSelMeter": "Please select a meter!", "export": "Export", "print": "Print", "readTimeOut": "Read timed out", "failReason": "Reason", "relayOptional": "Relay Optional", "internal": "Main Relay", "external": "Extend Relay1"}, "billingReportList": {"reports": "Reports", "reportList": "Report List", "startTime": "Start Time", "endTime": "End Time", "organization": "Organization", "lineLossObjectType": "Type", "timeType": "Time Type", "lineLossObject": "Line Loss Object", "import": "Import(KWh)", "export": "Export(KWh)", "loss": "Loss(KWh)", "rate": "Rate(%25)", "date": "Date", "lineLossStartTimeTip": "Please select Start Time!", "lineLossEndTimeTip": "Please select End Time!", "timeIssueAlert": "Plan End Time must be after Start Time!", "transformer": "Transformer", "line": "Line", "meter": "<PERSON>er", "communicator": "Communicator", "daily": "Daily", "monthly": "Monthly", "title": "Customer Billing Report", "sn": "Meter SN", "time": "Time", "energy": "Energy Consumption(KWh)", "consumption": "Energy Amount(USD)", "rateOther": "Rate(%)", "serach": "Search", "reportTime": "Report Time", "assetSelectTip": "Please select The Asset First!"}, "unknowDeviceManageReport": {"unknowDeviceManageStartTimeTip": "Please select unknow device Start Time!", "unknowDeviceManageEndTimeTip": "Please select unknow device End Time!", "timeIssueAlert": "Plan End Time must be after Line Loss Start Time!", "title": "Unknow Device Manage Report"}, "lineLossReportList": {"reportList": "Report List", "startTime": "Start Time", "endTime": "End Time", "organization": "Organization", "lineLossObjectType": "Type", "timeType": "Time Type", "lineLossObject": "Line Loss Object", "import": "Import(KWh)", "export": "Export(KWh)", "loss": "Loss(KWh)", "rate": "Rate(%25)", "date": "Date", "lineLossStartTimeTip": "Please select Line Loss Start Time!", "lineLossEndTimeTip": "Please select Line Loss End Time!", "timeIssueAlert": "Plan End Time must be after Line Loss Start Time!", "transformer": "Transformer", "line": "Line", "meter": "<PERSON>er", "communicator": "Communicator", "daily": "Daily", "monthly": "Monthly", "title": "Line Loss Report", "rateOther": "Rate(%)", "type": "Type", "meterName": "Meter Name", "tv": "Time", "name": "Data Channel", "dataValue": "Data Value", "objectName": "Line Loss Object", "entity": "Entity", "entityName": "Entity Name", "objectComparison": "Object Comparison", "yoyComparison": "<PERSON><PERSON>on", "qoqComparison": "QoQ Comparison", "cancelComparison": "Cancel Comparison", "yoy": "YoY", "qoq": "QoQ"}, "importAndExportReport": {"organization": "Organization", "timeType": "Time Type", "dataType": "Data Type", "startTime": "Start Time", "endTime": "End Time", "result": "Result", "time": "Time", "value": "Value(kWh)", "meterSn": "Meter SN", "import": "Import", "export": "Export", "detail": "Detail", "noRecords": "No Records", "timeNotExist": "Timescale Not Exist!", "title": "Supply Statistics Report", "curveType": "Curve Type"}, "lineManagementList": {"title": "Line", "lineSn": "Line SN", "lineName": "Line Name", "lineList": "Line List", "name": "Name", "organization": "Organization", "type": "Type", "voltageLevel": "Voltage Level", "properties": "Properties", "basicInformation": "Basic Information", "transformers": "Transformers", "calculationObject": "Calculation Object", "sn": "SN", "transformerList": "Transformer List", "transformerSn": "Transformer SN", "calObjList": "Calculation Object List", "calculationObjectName": "Calculation Object Name", "cycle": "Cycle", "calculationObjectProperties": "Calculation Object Properties", "dataChannelList": "Data Channel List", "meterSn": "Meter SN", "meterName": "Meter Name", "dataChannel": "Data Channel", "addDataChannel": "Add Data Channel", "addDataChannelFromTransformers": "Add Data Channel from Transformers", "addLine": "Add Line", "opt": "Action", "busbar": "Busbar", "feeder": "<PERSON>ede<PERSON>", "unit": "kV", "meterNotExist": "No meter found!", "noCalObjFound": "No calculation object found!", "nolineFound": "No line found!"}, "transformerManagementList": {"title": "Transformer", "transformerSn": "Transformer SN", "transformerName": "Transformer Name", "transformerList": "Transformer List", "name": "Name", "organization": "Organization", "ratedCapacity": "Rated Capacity", "properties": "Properties", "basicInformation": "Basic Information", "calculationObject": "Calculation Object", "sn": "SN", "address": "Address", "calculationObjectList": "Calculation Object List", "calculationObjectName": "Calculation Object Name", "type": "Type", "cycle": "Cycle", "calculationObjectProperties": "Calculation Object Properties", "dataChannelList": "Data Channel List", "meterSn": "Meter SN", "meterName": "Meter Name", "dataChannel": "Data Channel", "addDataChannel": "Add Data Channel", "noTrFound": "No Transformer Found!"}, "calculationObjectList": {"define": "Calculation Object Define", "title": "Calculation Object", "name": "Name", "type": "Type", "cycle": "Cycle", "entityType": "Entity Type", "entityName": "Entity Name", "calculationObject": "Calculation Object", "properties": "Properties", "calculationObjectList": "Calculation Object List", "meterSn": "Meter SN", "dataChannel": "Data Channel", "addDataChannel": "Add Data Channel", "lineLoss": "Line Loss", "import": "Supply", "export": "Sales", "daily": "Daily", "monthly": "Monthly", "line": "Line", "transformer": "Transformer", "organization": "Organization", "pleaseChoose": "---Please choose---", "all": "All", "addBySearch": "Add By Search", "addFromRelationship": "Add From Relationship", "addMeteringPoint": "Add Metering Point", "defaultType": "Default Type", "defaultDataChannel": "Default Data Channel", "gridLossReport": "Grid Loss Report", "rangeOfLossRate": "Range Of Loss Rate (%)", "reference": "Reference"}, "dcuConfiguration": {"activeCollect": "Can Read Meter", "passiveCollect": "Can Read Meter Directly", "startedTime": "Started Time", "delayTime": "Delayed Time", "switch": "Switch", "searchMeterSwitch": "Search Meter Switch", "rs485LogicalAddress": "RS485 Logical Address", "title": "DCU Configuration", "meterListUpload": "Meter List Parameter", "otherParameter": "Other Parameter", "communicatorSn": "Communicator SN", "set": "Write", "meterTitle": "Meter List", "meterTitleHES": "Meter List (HES)", "meterTitleDCU": "Meter List (Read From DCU)", "meterSn": "Meter SN", "pointNum": "Point Num", "logicalName": "Logical Name", "baudRate": "Baud <PERSON>", "comNum": "COM Num", "isVip": "Key User", "protocol": "Protocol", "communicatorAddress": "Communicator Address", "result": "Result", "communication": "Communication", "status": "Status", "requestTime": "Request Time", "responseTime": "Response Time", "reason": "Reason", "get": "Read", "hesCommunicationParameter": "HES Communication Parameter", "gprsNetworkParameter": " GPRS Network Parameter", "clock": "Clock", "concentratorVersion": "Concentrator Version Information", "encryptionMode": "Encryption Mode", "encryptionType": "Encryption Type", "encryptionKey": "Broadcast Key", "globalEncryption": "Global Encryption", "dedicatedEncryption": "Dedicated Encryption", "unencryted": "Unencryted", "ipAddress": "IP Address", "port": "Port", "userName": "User Name", "password": "Password", "dialedNumber": "Dialed Number", "ipAddressBackup": "Ip Address-Backup", "portBackup": "Port - Backup", "apn": "APN", "resetCommand": "Reset Command", "resetType": "Reset Type", "pleaseChooseCommSn": "Please choose comm sn!", "pleaseFillIn": "Please fill in information!", "pleaseFillInCorrect": "Please fill in correct information!", "pleaseChoose": "---Please choose---", "hardwareReset": "Hardware Reboot", "dataBaseInit": "Data Base Initialization", "parameterResetExceptComm": "Parameter Reset(except communication)", "dataReset": "Data Reset", "parameterAndDataReset": "Parameter and Data Reset", "parameterExceptHesReset": "Parameter(Except HES Communication Parameter) Reset", "importantUserSettingList": "VIP Setting", "meterModel": "Meter Model", "importantFlag": "VIP Meter Flag", "concentratorimportantFlag": "VIP Meter Flag -Concentrator", "command": "Command", "resetTypeCannotBeRead": "Reset Type Cannot be read", "itemCannotBeRead": "The Item Cannot be read", "selectCommunicators": "Please select Communicators.", "selectImportantUser": "Please select the meter that you want to set as a important user.", "selectResetType": "Please Select Reset Type.", "wiringMethod": "Wiring Method", "comType": "COM Type", "versionInfo": "Version Information", "equipmentNumber": "Equipment Number", "softwareVersion": "Software Version", "softwareDate": "Software Date", "SoftwareItem": "Software Item", "communicationProtocol": "Communication Protocol", "hardwareVersion": "Hardware Version", "hardwareDate": "Hardware Date", "versionInformationWrite": "Concentrator Version Information don't write.", "importUserPleaseSelect": " Please select the meter that you want to set as a important user.", "blackListPleaseSelect": " Please select the meter that you want to set as a Black meter.", "indexDcu": "Index Dcu", "heartbeatCycle": "Heartbeat Cycle", "heartbeatCycleUnit": "Heartbeat Cycle(Unit: Sec)", "energyProfileReadingCycle": "Energy Profile Reading Cycle", "energyProfileReadingCycleUnit": "Energy Profile Reading Cycle(Unit: Sec)", "plcChannelSendTimes": "PLC Channel Send Times", "plcChannelSendTimesUnit": "PLC Channel Send Times(Unit: Sec)", "plcChannelTimeout": "PLC Channel Timeout", "plcChannelTimeoutUnit": "PLC Channel Timeout(Unit: Sec)", "concentratorAddress": "Concentrator Address", "managementLogicalDeviceName": "Management LogicalDevice Name", "dormancyTime": "Dormancy Time", "dormancyTimeUnit": "Dormancy Time(Unit: Sec)", "concentratorIpAddress": "Concentrator IP Address", "eventConfiguration": "Event Configuration", "broadcastTimeOrderEnable": "Broadcast Time Order Enable", "broadcastTimeParameter": "Broadcast Time Parameter", "gprsSignalStrength": "GPRS Signal Strength", "gprsImeiSerialNumber": "GPRS IMEI Serial Number", "gprsNetworkStandard": "GPRS Network Standard", "wanDhcpEnable": "WAN DHCP Enable", "csMode": "C/S Mode", "serverPort": "Server Port", "gprsModemVersion": "GPRS Modem Version", "plcModemVersion": "PLC Modem Version", "rfModemVersion": "RF Modem Version", "blacklistOfMeter": "Blacklist of meter", "meterModuleSoftwareVersion": "Meter Module Software Version", "dcuAsClient": "Dcu as a Client", "dcuAsServer": "<PERSON><PERSON> as a Server", "terminalIp": "Terminal Ip", "subnetMask": "Subnet Mask", "gatewayMask": "Gateway Mask", "ItemDontWrite": "The Item don't write.", "blackFlag": "Black Flag", "pleaseCsMode": "Please choose C/S Mode!", "broadcastKey": "Broadcast Key", "firmwareVersionLaterThan": "the firmware version is later than 1.39", "vpnDial": "VPN Dial", "vpnNet": "Vpn Net", "connectedState": "Connected State", "topoInfo": "Topology Info", "shortAddress": "Short Address", "signalIntensity": "Signal Intensity", "netStatus": "Network Status", "phaseInfo": "Phase information", "networkAccess": "Network access", "networkIng": "Entering the network", "beaconForwarding": "Beacon forwarding", "waitingConf": "Waiting configuration", "searchNode": "Search Node", "delMeter": "Delete Meter", "selectLogicName": "Please select a LogicName!", "relayControlMode": "Relay Control Mode", "searchLevel": "Topology Level", "lossedInfo": "Lossed Topology Info", "total": "Total", "lossed": "Lossed", "level": "Level", "dayCurves": "Number Of Reading Meter Day Profiles (Event Profiles)", "monthCurves": "Number Of Reading Meter Month Profiles", "loadCurves": "Number Of Reading Meter Load Profiles", "eventLogCurves": "Reading Event Profiles", "writeEvent": "Write :", "readEvent": "Read :", "eventLogName": "Event Profile Name", "obisCode": "Obis Code", "selectEventToWrite": "Please Selected Event Profiles To Write", "enterValueTimes": "Enter a value between 30 and 120 seconds", "timeZone": "Time Zone", "batteryTime": "Battery charging delay time", "batteryTimeUnit": "Battery charging delay Time: Valid value range:0~129600(Unit: min)"}, "dcuModuleConfiguration": {"title": "Module Configuration", "moduleParameter": "Module Parameter", "pleaseChooseMeterSn": "Please choose meter sn!", "meterSn": "Meter SN", "APN": "APN", "userAndPassword": "User And Password", "pinCode": "PIN Code", "hesIpAndPort": "HES ip and port", "ftpIpAndPort": "FTP ip and port", "autoConnectMode": "Auto Connect Mode", "autoAnswerMode": "Auto Answer Mode", "inactivityTimeOut": "Inactivity TimeOut", "noNetworkCommunicationTimeoutReset": "No Network Communication Timeout Reset", "user": "UserName", "password": "Password", "inactiveConnection": "Inactive Connection", "activeConnection": "Active Connection", "activeConnectionNotInWindowTime": "Active Connection(Can't Rouse in windows time)", "activeConnectionInWindowTime": "Active Connection(Can Rouse in windows time)", "inactiveConnectionRouseHesConnect": "Inactive Connection(Can Rouse HES Connection)", "alwaysConnectedGprs": "Always Connected GPRS", "notNeedToAnswer": "Connection(There is no need to answer rouse)", "needToAnswer": "Connection(There is need to answer rouse)", "closedGprs": "Closed GRPS(Can't Rouse)", "selectConnectType": "Please Select Connect Type.", "selectModeType": "Please Select Mode Type."}, "importGprsMeter": {"importFromShipmentFile": "Import Meter Shipment File", "importGprsMeter": "Shipment File Import", "num1": "1", "num2": "2", "num3": "3", "num4": "4", "step1": "Step One", "step2": "Step Two", "step3": "Step Three", "step4": "Step Four", "assetType": "Asset Type", "GPRSMeter": "GPRS Meter", "assetFile": "Asset File", "manufacturer": "Manufacturer", "model": "Model", "firmwareVersion": "Firmware Version", "measurementGroup": "Measurement Group", "collectionSchemeGroup": "Collection Scheme Group", "meterExist1": "The meter with SN number ", "meterExist2": " already exists.", "checkProgress": "Check progress", "importProgress": "Import progress", "selectFile": "Select File", "checkStatus": "Verifying Excel files, do not switch!", "pleaseSelectFile": "Please select an Excel file!", "noDataInExcel": "There is no data in the Excel file!", "verificationFailed": "Verification failed, please check and re-import!", "pleaseSelMeaGroup": "Please select Measurement Group!", "pleaseSelColSchGroup": "Please select Collection Scheme Group!", "checkCompleted": "Check completed!", "importStatus": "Import Excel files, do not switch!", "deviceTypeIsEmpty": "Device Type is Empty!", "pleaseSelCommType": "Please select Communication Type!", "shipmentFileType": "Shipment File Type", "meterSipmentFile": "Meter Shipment File", "shipmentFile": "Shipment File", "verifySuccess": "Verify Success", "verifyFail": "Verify F<PERSON>"}, "customerList": {"customer": "Customer", "customerList": "Customer List", "customerSn": "Customer SN", "customerType": "Customer Type", "customerName": "Customer Name", "industryType": "Industry Type", "onlyOneError": "You selected this meter or inputted Customer SN is referrenced by another customer. "}, "dataComminicationStatus": {"title": "Communication Status Report", "statusUpdateTime": "Status Update Time", "networkAddress": "Network Address", "commComType": "Communicator COM Type", "meterComType": "Meter COM Type", "offlineTimeDays": "Offline Time (Days)", "offline": "Offline", "online": "Online"}, "saleStatReport": {"missData": "Miss Data"}, "veeList": {"validatingReport": "Validating Report", "exception tatal": "Exception Tatal", "class1": "Class1", "class2": "Class2", "class3": "Class3", "class4": "Class4", "class5": "Class5", "veeGroup": "VEE Group", "veeGroupList": "VEE Group List", "groupName": "Group Name", "meterCount": "Meter Count", "descr": "Description", "veeRuleList": "VEE Rule List", "ruleName": "Rule Name", "ruleType": "Rule Type", "class": "Class", "dataChannel": "Data Channel", "event": "Event", "method": "Method", "estimationReportProgressDelay": "Estimation Report -Progress Delay", "estimationReportMissData": "Estimation Report -Miss Data", "meterSn": "Meter SN", "profile": "Profile", "communicatorSn": "Communicator SN", "communicationType": "Communication Type", "progress": "Progress", "progressDelay": "Progress Delay", "time": "Time", "missDataListDetail": "Miss Data List Detail", "missDataList": "Miss Data List", "progressDelayList": "Progress Delay List", "progressDelayListDetail": "Progress Delay List Detail", "serizlNo": "Device SN", "missData": "Miss Data", "deadline": "Deadline", "delayExported": "Only delay data can be exported", "missExported": "Only miss data can be exported", "rowExport": "Select the row to export", "taskTv": "Task Time", "ruleStatus": "Rule Status", "ruleClass": "Rule Class", "editVeeRule": "<PERSON> <PERSON><PERSON>", "addVeeRule": "Add <PERSON> Rule", "pleSeleVeeGroup": "Please select Vee Group!", "pleFillName": "Name cannot be empty !", "pleCycleCount": "Cycle Count Must be numbers !", "pleDefaultValue": "Parameter Value Must be numbers !", "eventRuleDesc": "Event Rule Description", "dataItemName": "Data Channel Name", "dataItemKey": "Data Channel Key", "cycleCount": "Cycle Count", "cycleType": "Cycle Type", "paramKey": "Parameter Key", "defaultValue": "Parameter Value", "dataItemList": "Data Channel List", "veeEventParamList": "Parameter List"}, "dict": {"Reports": "Reports", "Cusomer Billing Report": "Customer Billing Report", "Line Loss Report": "Line Loss Report", "Supply Statistics Report": "Supply Statistics Report", "Sales Statistics Report": "Sales Statistics Report", "Non-Residential": "Non-Residential", "Residential": "Residential", "Production": "Production", "Farming": "Farming", "Animal Husbandry": "Animal Husbandry", "Fishery": "Fishery", "Mining": "Mining", "Lodging & Catering": "Lodging & Catering", "Minutely": "Minutely", "Hourly": "Hourly", "Daily": "Daily", "Monthly": "Monthly", "Yes": "Yes", "No": "No", "Data Profile": "Data Profile", "Event Profile": "Event Profile"}, "dcuConfiguration300": {"title": "娴嬮噺鐐瑰弬鏁扮鐞�", "communicatorSn": "闆嗕腑鍣ㄨ祫浜х紪鍙�", "measurementPointRange": "娴嬮噺鐐硅寖鍥�", "meterTitleHES": "璁￠噺鐐瑰垪琛�", "communication": "communication", "meterSn": "璧勪骇缂栧彿", "pointNum": "娴嬮噺鐐圭紪鍙�", "status": "鐘舵��", "meterProperties": "琛ㄨ鎬ц川", "mac": "娴嬮噺鐐瑰湴鍧�", "meterType": "鐢佃兘琛ㄧ被鍨�", "totalDivType": "鎬诲垎绫诲瀷", "isVip": "閲嶇偣鐢ㄦ埛灞炴��", "feeRateCount": "鏈�澶ц垂鐜囨暟", "collectorAddress": "閲囬泦鍣ㄥ湴鍧�", "comNum": "绔彛鍙�", "taChange": "TA鍙樻瘮", "tvChange": "TV鍙樻瘮", "command": "鍛戒护", "reason": "鍘熷洜", "requestTime": "璇锋眰鏃堕棿", "responseTime": "鍝嶅簲鏃堕棿", "protocol": "瑙勭害", "meterTitleDCU": "琛ㄨ鍒楄〃 (浠嶥CU璇诲彇)", "seleCommunicator": "璇烽�夋嫨闆嗕腑鍣紒", "seleLeastOne": "鑷冲皯閫夋嫨琛ㄤ腑鐨勪竴鏉℃暟鎹紒", "pleaseEnterPositiveInt": "璇疯緭鍏ユ鏁存暟", "beginGreaterThanEnd": "璧峰娴嬮噺鐐瑰繀椤诲ぇ浜庣粓姝㈡祴閲忕偣", "baudRate": "娉㈢壒鐜�", "stopFlag": "鍋滄浣�"}, "courtsTopology": {"title": "Courts Topology"}, "reportDesigner": {"reportDesigner": "Report Designer", "reportManagement": "Report Management", "reportExploer": "Reports", "reportManagementList": "Report Management List", "reportType": "Report Type", "reportName": "Report Name", "orgName": "Org Name", "templateFile": "Template File"}, "dictManagement": {"title": "Dictionary Management", "dictId": "Dict ID", "dictName": "Dict Name", "dictDesp": "Dict Description", "dictGroupList": "Dict Group List", "dictDetailList": "Dict Detail List", "dictDetailId": "Dict Detail ID", "dictGroupId": "Dict Group ID", "dictDetailName": "Dict Detail Name", "dictDetailDesp": "Dict Detail Description", "isDefault": "<PERSON>", "displayOrder": "Display Order", "dictIdOneTips": "Dict ID Already Exists!", "dictDetailIdOneTips": "Dict Detail ID Already Exists!", "dictGroupFirst": "Please Selected Dict Group First!"}, "dictDataitem": {"name": "Channel Name", "title": "Channel Management", "list": "Channel List", "id": "Channel ID", "protocolCode": "OBIS Code", "protocolName": "Protocol Name", "unit": "Unit", "dataitemType": "Channel Type", "showUnit": "Show Unit", "opType": "Operation Type", "idExist": "Channel ID Already Exists!", "nameExist": "Channel ID Already Exists!", "add": "Add Channel", "import": "Import Channel File", "parseType": "Parse Type", "parseLen": "Parse Length", "scale": "Scale"}, "dictDataitemGroup": {"title": "Show Group Management", "list": "Show Group List", "name": "Group Name", "id": "Group ID", "appType": "Group Type", "protocolName": "Protocol Name", "idExist": "Group ID Already Exists!", "nameExist": "Group Name Already Exists!", "selectGroup": "Please Selected Group First!", "sortId": "Sort ID", "deleteItem": "Please Delete Linked Channels First"}, "dictProfile": {"id": "Profile ID", "name": "Profile Name", "type": "Profile Type", "title": "Profile Template Management", "list": "Profile Template List", "idExist": "Profile ID  Already Exists!", "nameExist": "Profile Name  Already Exists!", "itemList": "Linked Channel List", "linkChannel": "Link Channel", "sortChannel": "Sort Channel", "selectProfile": "Please Selected Profile First!"}, "storeTable": {"title": "Store Management", "list": "Store Table List", "columnCount": "Column Count", "id": "Table ID", "name": "Table Name", "columnList": "Column Detail List", "columnId": "Column ID", "selectTable": "Please Selected Store Table First!", "idExist": "Table ID Already Exists!", "nameExist": "Table Name Already Exists!", "delHasData": "Table has data can not delete", "setProfile": "Profile Name Is Empty Cannot Link Channel!"}, "sysUtility": {"licenseInfo": "License Information", "newLicense": "New License", "companyName": "Company Name", "licenseStartDate": "Current License Start Date", "licenseEndDate": "Current License End Date", "meterNum": "Current Support Meter Number"}, "afterAdd": {"addTask": "Add Task", "execute": "Execute", "content": "Content"}}