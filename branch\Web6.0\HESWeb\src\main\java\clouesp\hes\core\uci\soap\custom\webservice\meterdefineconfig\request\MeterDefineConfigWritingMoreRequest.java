package clouesp.hes.core.uci.soap.custom.webservice.meterdefineconfig.request;

import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import clouesp.hes.core.uci.soap.custom.webservice.common.WebservTask;

import com.clou.esp.hes.app.web.core.pushlet.PushletData;
import com.clou.esp.hes.app.web.enums.system.TaskState;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.redis.JedisUtils;

import ch.iec.tc57._2011.meterdefineconfig.MeterDefineConfigPort;
import ch.iec.tc57._2011.meterdefineconfig_.Arrays;
import ch.iec.tc57._2011.meterdefineconfig_.Meter;
import ch.iec.tc57._2011.meterdefineconfig_.MeterDefineConfig;
import ch.iec.tc57._2011.meterdefineconfigmessage.MeterDefineConfigPayloadType;


/**
 * @ClassName: MeterDefineConfigWritingMoreRequest
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年8月3日 下午5:09:30
 *
 */
public class MeterDefineConfigWritingMoreRequest extends
		MeterDefineConfigRequestAbstract{

	
	Arrays arrays; // 参数
	List<String> ids; // 每一条数据的唯一 id (一次可向 uci 发送多条数据，但是 web 需要分开标识)
	List<String> meters; // 每一个id,对应一个meter
	
	public MeterDefineConfigWritingMoreRequest(String id,List<String> ids,List<String> meters,Arrays arrays,String sn,String dataItemId,
			String userId,String verb,String noun,String bathPath,MeterDefineConfigPort port,String revision,List<String> pns){
		this.id = id;
		this.ids = ids;
		this.meters = meters;
		this.arrays = arrays;
		
		this.sn = sn;
		this.dataItemId = dataItemId;
		this.messageId = UUID.randomUUID().toString().replace("-", "");
		this.verb = verb;
		this.noun = noun;
		this.source = "ClouESP HES";
		this.timestamp = DateUtils.dateToXmlDate(new Date());
		this.replyAddress = bathPath+"/interfaces/ReplyMeterDefineConfigPort?wsdl";
		this.asyncReplyFlag = true;
		this.ackRequired = true;
		
		this.userId = userId;
		
		this.port = port;
		
		this.revision = revision;
		this.pns = pns;
		
		saveToRedis(id, dataItemId,TaskState.Processing, null);
	}
	
	@Override
	public void saveToRedis(){
		// TODO Auto-generated method stub
		for (int i = 0; i < ids.size(); i++) {
			WebservTask task = new WebservTask(ids.get(i), dataItemId, TaskState.Processing, null, meters.get(i));
			String messegekey = messageId + meters.get(i);
			JedisUtils.setObject(messegekey, task, 0);
		}
	}
	
	@Override
	public Object createPayload() {
		// TODO Auto-generated method stub
		MeterDefineConfig config = new MeterDefineConfig();
		
		Meter meter = new Meter();
		meter.setMRID(sn);
		if(null != pns){
			meter.getPns().addAll(pns);
		}
		config.getMeters().add(meter);
		
		MeterDefineConfig.ReadingType type = new MeterDefineConfig.ReadingType();
		type.setRef(dataItemId);
		config.setReadingType(type);
		
		config.getArrays().add(arrays);
		
		MeterDefineConfigPayloadType payLoadType = new MeterDefineConfigPayloadType();
		payLoadType.setMeterDefineConfig(config);
		
		return payLoadType;
	}
	
/*	@Override
	public void pushlet(AjaxJson json) {
		// TODO Auto-generated method stub
		if(json.isSuccess()) // 正常状况无需推送 pushlet
			return;
		json.put("messageId", messageId);
		json.put("responseTime", DateUtils.formatDate(new Date(), "dd-MM-yyyy HH:mm:ss"));
		json.put("dataItemId", dataItemId);
		
		for (String string : meters) {
			JedisUtils.delObject(messageId + string);
		}
		
		// 处理err
		WebservTask servTask = new WebservTask(null, dataItemId, TaskState.Failed, ids);
		json.put("webservTask", servTask);
		try {
			PushletData.pushlet("dcuConfigPushletChannel", json, userId);
		} catch (UnsupportedEncodingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}*/
}
