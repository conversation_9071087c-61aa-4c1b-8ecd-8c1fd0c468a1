/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataStatisticsEvent{ } 
 * 
 * 摘    要： dataStatisticsEvent
 * 版    本：1.0
 * 作    者：赵久霆
 * 创建于：2018-09-19 07:26:51
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.data;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;

import com.clou.esp.hes.app.web.model.data.DataStatisticsEvent;
import com.clou.esp.hes.app.web.service.data.DataStatisticsEventService;

/**
 * <AUTHOR>
 * @时间：2018-09-19 07:26:51
 * @描述：dataStatisticsEvent类
 */
@Controller
@RequestMapping("/dataStatisticsEventController")
public class DataStatisticsEventController extends BaseController{

 	@Resource
    private DataStatisticsEventService dataStatisticsEventService;

	/**
	 * 跳转到dataStatisticsEvent列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/data/dataStatisticsEventList");
    }

	/**
	 * 跳转到dataStatisticsEvent新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dataStatisticsEvent")
	public ModelAndView dataStatisticsEvent(DataStatisticsEvent dataStatisticsEvent,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dataStatisticsEvent.getId())){
			try {
                dataStatisticsEvent=dataStatisticsEventService.getEntity(dataStatisticsEvent.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dataStatisticsEvent", dataStatisticsEvent);
		}
		return new ModelAndView("/data/dataStatisticsEvent");
	}


	/**
	 * dataStatisticsEvent查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=dataStatisticsEventService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除dataStatisticsEvent信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DataStatisticsEvent dataStatisticsEvent,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dataStatisticsEventService.deleteById(dataStatisticsEvent.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存dataStatisticsEvent信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DataStatisticsEvent dataStatisticsEvent,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DataStatisticsEvent t=new  DataStatisticsEvent();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dataStatisticsEvent.getId())){
        	t=dataStatisticsEventService.getEntity(dataStatisticsEvent.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dataStatisticsEvent, t);
				dataStatisticsEventService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            dataStatisticsEventService.save(dataStatisticsEvent);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
	
	
	
}