/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetVeeRuleParam{ } 
 * 
 * 摘    要： assetVeeRuleParam
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2019-03-04 09:38:24
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.vee;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class AssetVeeRuleParam  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public AssetVeeRuleParam() {
	}
	/**paramKey*/
	private java.lang.String ruleId;
	/**paramKey*/
	private java.lang.String paramKey;
	/**paramValue*/
	private java.lang.String paramValue;

	
	
	public java.lang.String getRuleId() {
		return ruleId;
	}

	public void setRuleId(java.lang.String ruleId) {
		this.ruleId = ruleId;
	}

	/**
	 * paramKey
	 * @return the value of ASSET_VEE_RULE_PARAM.PARAM_KEY
	 * @mbggenerated 2019-03-04 09:38:24
	 */
	public java.lang.String getParamKey() {
		return paramKey;
	}

	/**
	 * paramKey
	 * @param paramKey the value for ASSET_VEE_RULE_PARAM.PARAM_KEY
	 * @mbggenerated 2019-03-04 09:38:24
	 */
    	public void setParamKey(java.lang.String paramKey) {
		this.paramKey = paramKey;
	}
	/**
	 * paramValue
	 * @return the value of ASSET_VEE_RULE_PARAM.PARAM_VALUE
	 * @mbggenerated 2019-03-04 09:38:24
	 */
	public java.lang.String getParamValue() {
		return paramValue;
	}

	/**
	 * paramValue
	 * @param paramValue the value for ASSET_VEE_RULE_PARAM.PARAM_VALUE
	 * @mbggenerated 2019-03-04 09:38:24
	 */
    	public void setParamValue(java.lang.String paramValue) {
		this.paramValue = paramValue;
	}

	public AssetVeeRuleParam(java.lang.String paramKey 
	,java.lang.String paramValue ) {
		super();
		this.paramKey = paramKey;
		this.paramValue = paramValue;
	}

}