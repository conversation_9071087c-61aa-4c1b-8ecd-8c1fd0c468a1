/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysService{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-01-29 08:53:20
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.system;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.system.SysServiceDao;
import com.clou.esp.hes.app.web.model.system.SysService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.clou.esp.hes.app.web.service.system.SysServiceService;

@Component
@Service("sysServiceService")
public class SysServiceServiceImpl  extends CommonServiceImpl<SysService>  implements SysServiceService {

	@Resource
	private SysServiceDao sysServiceDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(sysServiceDao);
    }
	public SysServiceServiceImpl() {}
	
	@Override
	public List<SysService> getListByEntity(SysService service) {
		return sysServiceDao.getListByEntity(service);
	}
	
	@Override
	public List<SysService> vaildServiceName(String name) {
		return sysServiceDao.vaildServiceName(name);
	}
	
	
}