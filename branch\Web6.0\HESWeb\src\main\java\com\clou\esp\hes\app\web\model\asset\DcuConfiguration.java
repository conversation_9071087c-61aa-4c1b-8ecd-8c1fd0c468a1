package com.clou.esp.hes.app.web.model.asset;

import java.util.Date;

import com.clou.esp.hes.app.web.model.common.BaseEntity;


/**
 * @ClassName: DcuConfiguration
 * @Description: DCU 参数配置实体
 * <AUTHOR>
 * @date 2018年7月10日 下午5:06:08
 *
 */
public class DcuConfiguration extends BaseEntity implements
		Comparable<DcuConfiguration> {

	private static final long serialVersionUID = 1L;

	@Override
	public int compareTo(DcuConfiguration o) {
		// TODO Auto-generated method stub
		return this.meterSn.compareTo(o.getMeterSn());
	}
	
	/**
	 * 租户Id
	 */
	private String utilityId;
	
	/**
	 * 电表Id
	 */
	private String id;
	private String encryptionMode;
	/**
	 * 电表序列号
	 */
	private String meterSn;
	
	/**
	 * 测量点
	 */
	private int pointNum;
	/**
	 * 关键用户
	 */
	private int keyFlag;
	
	/**
	 * 0-普通用户 1-重点用户
	 */
	private int isVip;
	/**
	 * 0-普通用户 1-重点用户
	 */
	private String isVipTxt;
	
	/**
	 * 逻辑名
	 */
	private String logicalName;
	

	/**
	 * auth type
	 */
	private String authType;
	/**
	 * 波特率
	 */
	private int baudRate;
	
	/**
	 * 通讯口
	 */
	private int comNum;
	/**
	 * 通讯类型
	 */
	private int comType;
	/**
	 * 配线方法
	 */
	private int wiringMethod;
	
	/**
	 * meter blacklist
	 */
	private int blackFlag;
	/**
	 * 协议类型
	 */
	private String protocol;
	
	/**
	 * 通讯地址
	 */
//	private String communicatorAddress = "FFFFFFFFF";
	private String communicatorAddress;
	
	/**
	 * 通讯方式
	 */
	private String communication;
	
	/**
	 * 通讯方式ID
	 */
	private String communicationId;
	
	/**
	 * 集中器SN
	 */
	private String communicatorSn;

	/**
	 * 集中器Id
	 */
	private String communicatorId;
	
	/**
	 * 运行状态
	 */
	private String status;
	
	/**
	 * 请求时间
	 */
	private Date requestTime;
	
	/**
	 * 响应时间
	 */
	private Date responseTime;
	
	/**
	 * 执行结果
	 */
	private String reason;
	
	/**
	 * meter model
	 */
	private String meterModel;
	
	private String broadcastKey;
	private String password;
	private String hlsAk;
	private String hlsEk;
	
	private String rs485LogicalAddress;
	
	
	
	public String getAuthType() {
		return authType;
	}

	public void setAuthType(String authType) {
		this.authType = authType;
	}

	public String getRs485LogicalAddress() {
		return rs485LogicalAddress;
	}

	public void setRs485LogicalAddress(String rs485LogicalAddress) {
		this.rs485LogicalAddress = rs485LogicalAddress;
	}

	public String getEncryptionMode() {
		return encryptionMode;
	}

	public void setEncryptionMode(String encryptionMode) {
		this.encryptionMode = encryptionMode;
	}

	public String getBroadcastKey() {
		return broadcastKey;
	}

	public void setBroadcastKey(String broadcastKey) {
		this.broadcastKey = broadcastKey;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getHlsAk() {
		return hlsAk;
	}

	public void setHlsAk(String hlsAk) {
		this.hlsAk = hlsAk;
	}

	public String getHlsEk() {
		return hlsEk;
	}

	public void setHlsEk(String hlsEk) {
		this.hlsEk = hlsEk;
	}

	public int getBlackFlag() {
		return blackFlag;
	}

	public void setBlackFlag(int blackFlag) {
		this.blackFlag = blackFlag;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getMeterSn() {
		return meterSn;
	}

	public void setMeterSn(String meterSn) {
		this.meterSn = meterSn;
	}

	public int getPointNum() {
		return pointNum;
	}

	public void setPointNum(int pointNum) {
		this.pointNum = pointNum;
	}

	public String getLogicalName() {
		return logicalName;
	}

	public void setLogicalName(String logicalName) {
		this.logicalName = logicalName;
	}

	public int getBaudRate() {
		return baudRate;
	}

	public void setBaudRate(int baudRate) {
		this.baudRate = baudRate;
	}

	public int getComNum() {
		return comNum;
	}

	public void setComNum(int comNum) {
		this.comNum = comNum;
	}

	public String getProtocol() {
		return protocol;
	}

	public void setProtocol(String protocol) {
		this.protocol = protocol;
	}

	public String getCommunicatorAddress() {
		return communicatorAddress;
	}

	public void setCommunicatorAddress(String communicatorAddress) {
		this.communicatorAddress = communicatorAddress;
	}

	public String getCommunication() {
		return communication;
	}

	public void setCommunication(String communication) {
		this.communication = communication;
	}

	public String getCommunicatorSn() {
		return communicatorSn;
	}

	public void setCommunicatorSn(String communicatorSn) {
		this.communicatorSn = communicatorSn;
	}

	public String getCommunicatorId() {
		return communicatorId;
	}

	public void setCommunicatorId(String communicatorId) {
		this.communicatorId = communicatorId;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Date getRequestTime() {
		return requestTime;
	}

	public void setRequestTime(Date requestTime) {
		this.requestTime = requestTime;
	}

	public Date getResponseTime() {
		return responseTime;
	}

	public void setResponseTime(Date responseTime) {
		this.responseTime = responseTime;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public String getUtilityId() {
		return utilityId;
	}

	public void setUtilityId(String utilityId) {
		this.utilityId = utilityId;
	}

	public String getCommunicationId() {
		return communicationId;
	}

	public void setCommunicationId(String communicationId) {
		this.communicationId = communicationId;
	}

	/**
	 * @return the isVip
	 */
	public int getIsVip() {
		return isVip;
	}

	/**
	 * @param isVip the isVip to set
	 */
	public void setIsVip(int isVip) {
		this.isVip = isVip;
	}

	public String getMeterModel() {
		return meterModel;
	}

	public void setMeterModel(String meterModel) {
		this.meterModel = meterModel;
	}

	public String getIsVipTxt() {
		return isVipTxt;
	}

	public void setIsVipTxt(String isVipTxt) {
		this.isVipTxt = isVipTxt;
	}

	public int getComType() {
		return comType;
	}

	public void setComType(int comType) {
		this.comType = comType;
	}

	public int getWiringMethod() {
		return wiringMethod;
	}

	public void setWiringMethod(int wiringMethod) {
		this.wiringMethod = wiringMethod;
	}

	public int getKeyFlag() {
		return keyFlag;
	}

	public void setKeyFlag(int keyFlag) {
		this.keyFlag = keyFlag;
	}
	
	
	
}
