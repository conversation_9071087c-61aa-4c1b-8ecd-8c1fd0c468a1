/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class onDemandRead{ } 
 * 
 * 摘    要： onDemandRead
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-1-2 09:41:41
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.asset;

import java.util.Date;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.excel.Excel;

public class OnDemandRead extends  BaseEntity implements Comparable<OnDemandRead> {

	private static final long serialVersionUID = 1L;
	
	/** sn */
	@Excel(name = "Serial Number", width = 30, groups = ValidGroup1.class)
	private java.lang.String sn;
	/** sn name */
	@Excel(name = "Meter Name", width = 30, groups = ValidGroup1.class)
	private java.lang.String meterName;
	/** model */
	@Excel(name = "Meter Model", width = 30, groups = ValidGroup1.class)
	private java.lang.String model;
	/** addr */
	@Excel(name = "Meter ADDR", width = 30, groups = ValidGroup1.class)
	private java.lang.String addr;

	/** communicator */
	@Excel(name = "Communicator", width = 30, groups = ValidGroup1.class)
	private java.lang.String communicator;
	/**Commmunication**/
	@Excel(name = "Commmunication", width = 30, groups = ValidGroup1.class)
	private java.lang.String commmunication;
	/** dataChannel */
	@Excel(name = "Data Channel", width = 30, groups = ValidGroup1.class)
	private java.lang.String dataChannel;
	/** value */
	@Excel(name = "Value", width = 30, groups = ValidGroup1.class)
	private String value;
	/** status */
	@Excel(name = "Status", width = 30, groups = ValidGroup1.class)
	private java.lang.String status;
	/** requestTime */
	@Excel(name = "Request Time", width = 30, groups = ValidGroup1.class)
	private java.util.Date requestTime;
	/** reponseTime */
	@Excel(name = "Reponse Time", width = 30, groups = ValidGroup1.class)
	private java.util.Date reponseTime;

	/** profile */
	private java.lang.String profile;
	
	
	

	public OnDemandRead(String sn,String meterName, String communicator, String commmunication,
			String dataChannel, String value, String status, Date requestTime,
			Date reponseTime, String model, String profile) {
		super();
		this.sn = sn;
		this.meterName = meterName;
		this.communicator = communicator;
		this.commmunication = commmunication;
		this.dataChannel = dataChannel;
		this.value = value;
		this.status = status;
		this.requestTime = requestTime;
		this.reponseTime = reponseTime;
		this.model = model;
		this.profile = profile;
	}

	public String getAddr() {
		return addr;
	}

	public void setAddr(String addr) {
		this.addr = addr;
	}

	public String getMeterName() {
		return meterName;
	}

	public void setMeterName(String meterName) {
		this.meterName = meterName;
	}

	public OnDemandRead() {
		super();
	}


	public java.lang.String getSn() {
		return sn;
	}


	public void setSn(java.lang.String sn) {
		this.sn = sn;
	}


	public java.lang.String getCommunicator() {
		return communicator;
	}


	public void setCommunicator(java.lang.String communicator) {
		this.communicator = communicator;
	}


	public java.util.Date getRequestTime() {
		return requestTime;
	}


	public void setRequestTime(java.util.Date requestTime) {
		this.requestTime = requestTime;
	}


	public java.lang.String getStatus() {
		return status;
	}


	public void setStatus(java.lang.String status) {
		this.status = status;
	}



	public java.util.Date getReponseTime() {
		return reponseTime;
	}


	public void setReponseTime(java.util.Date reponseTime) {
		this.reponseTime = reponseTime;
	}


	public java.lang.String getDataChannel() {
		return dataChannel;
	}


	public void setDataChannel(java.lang.String dataChannel) {
		this.dataChannel = dataChannel;
	}


	public String getValue() {
		return value;
	}


	public void setValue(String value) {
		this.value = value;
	}


	public java.lang.String getModel() {
		return model;
	}


	public void setModel(java.lang.String model) {
		this.model = model;
	}


	public java.lang.String getProfile() {
		return profile;
	}


	public void setProfile(java.lang.String profile) {
		this.profile = profile;
	}


	public java.lang.String getCommmunication() {
		return commmunication;
	}


	public void setCommmunication(java.lang.String commmunication) {
		this.commmunication = commmunication;
	}


	@Override
	public int compareTo(OnDemandRead o) {
		return this.getSn().compareTo(o.getSn());
	}


	
}
