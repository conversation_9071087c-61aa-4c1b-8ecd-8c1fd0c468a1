$(document).ready(function() {
	$.jgrid.defaults.styleUI = "Bootstrap";
	var mydata = [{
		id: "1",
		Special: "Spacial Day1",
		Week: "Week1",
		Season: "Season1",
		invdate: "Day1",
		StartTime: "00:00:00",
		Rate: "Rate1",
		Rate2: "Week1",
		note: "19.0(kWh)",
		amount: "Done",
		tax: "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "30.00"
	}, {
		id: "2",
		Special: "Spacial Day2",
		Week: "Week2",
		Season: "Season2",
		invdate: "Day2",
		StartTime: "02:00:00",
		Rate: "Rate2",
		Rate2: "Week1",
		note: "17.0(kWh)",
		amount: "Done",
		tax: "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total1]",
		totals: "30"
	}, {
		id: "3",
		Special: "Spacial Day3",
		Week: "Week3",
		Season: "Season3",
		invdate: "Day3",
		StartTime: "04:00:00",
		Rate: "Rate3",
		Rate2: "Week1",
		note: "19.0(kWh)",
		amount: "Done",
		tax: "12/20/2017 15:45:04",
		total: "Active Energy -Import [Register] [Total2]",
		totals: "100"

	}, {
		id: "4",
		Special: "Spacial Day4",
		Week: "Week4",
		Season: "Season4",
		invdate: "Day4",
		StartTime: "06:00:00",
		Rate: "Rate4",
		Rate2: "Week1",
		note: "23.0(kWh)",
		amount: "Done",
		tax: "12/20/2017 15:45:05",
		total: "Active Energy -Import [Register] [Total3]",
		totals: "3600"
	}, {
		id: "5",
		Special: "Spacial Day5",
		Week: "Week5",
		Season: "Season5",
		invdate: "Day5",
		tax: "12/20/2017 15:45:03",
		StartTime: "08:00:00",
		Rate: "Rate5",
		Rate2: "Week1",
		amount: "Failed",
		totals: "60"
	}, {
		id: "6",
		Special: "Spacial Day6",
		Week: "Week6",
		Season: "Season6",
		invdate: "Day6",
		StartTime: "10:00:00",
		Rate: "Rate6",
		Rate2: "Week1",
		note: "18.0(kWh)",
		amount: "Done",
		tax: "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total1]",
		totals: "430.00"
	}, {
		id: "7",
		Special: "Spacial Day7",
		Week: "Week7",
		Season: "Season7",
		invdate: "Day7",
		StartTime: "12:00:00",
		Rate: "Rate7",
		Rate2: "Week1",
		note: "19.0(kWh)",
		amount: "Done",
		tax: "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total2]",
		totals: "430.00"
	}, {
		id: "8",
		Special: "Spacial Day8",
		Week: "Week8",
		Season: "Season8",
		invdate: "Day8",
		StartTime: "14:00:00",
		Rate: "Rate8",
		Rate2: "Week1",
		note: "17.0(kWh)",
		amount: "Done",
		tax: "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total3]",
		totals: "430.00",
	}, {
		id: "9",
		Special: "Spacial Day9",
		Week: "Week9",
		Season: "Season9",
		invdate: "Day9",
		StartTime: "16:00:00",
		Rate: "Rate",
		Rate2: "Week1",
		note: "18.0(kWh)",
		amount: "Done",
		tax: "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		Special: "Spacial Day10",
		Week: "Week10",
		Season: "Season10",
		invdate: "Day10",
		totals: "430.00",
		tax: "12/20/2017 15:45:03",
		StartTime: "18:00:00",
		Rate: "Rate",
		Rate2: "Week1",
		amount: "Failed",
	}, {
		id: "11",
		Special: "Spacial Day11",
		Week: "Week11",
		Season: "Season11",
		invdate: "Day11",
		tax: "12/20/2017 15:45:03",
		StartTime: "20:00:00",
		Rate: "Rate",
		Rate2: "Week1",
		amount: "Failed",
		totals: "430.00"

	}, {
		id: "12",
		Special: "Spacial Day12",
		Week: "Week12",
		Season: "Season12",
		invdate: "Day12",
		StartTime: "22:00:00",
		Rate: "Rate",
		Rate2: "Week1",
		note: "19.0(kWh)",
		amount: "Done",
		tax: "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total2]",
		totals: "430.00"
	}, {
		id: "13",
		Special: "Spacial Day13",
		Week: "Week13",
		Season: "Season13",
		invdate: "Day13",
		tax: "12/20/2017 15:45:03",
		StartTime: "23:00:00",
		Rate: "Rate",
		Rate2: "Week1",
		amount: "Failed",
		totals: "430.00"
	}, {
		id: "14",
		Special: "Spacial Day14",
		Week: "Week14",
		Season: "Season14",
		invdate: "Day14",
		StartTime: "12:30:00",
		Rate: "Rate",
		Rate2: "Week1",
		note: "17.0(kWh)",
		amount: "Done",
		tax: "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}];
	var lastsel;
	$("#table").jqGrid({
		data: mydata,
		datatype: "local",
		autowidth: true,
		gridview: true,
		height: "200px",
		caption: 'Day List',
		/*direction:"rtl",*/
		rownumbers: true,
		  
		colNames: ["", "Day Name", "Start Time", "Rate"],
		colModel: [{
				name: "id",
				index: "id",
				editable: true,
				width: 60,
				frozen: true,
				search: false,
				align: "right",
				hidden: true,
				sorttype: "int"

			}, {
				name: "invdate",
				index: "invdate",
				editable: false,
				width: 80,
				editable: false,
			}, {
				name: "StartTime",
				index: "StartTime",
				editable: true,
				width: 120,
				editable : true,sorttype : "date",editoptions:{
                dataInit:function(e){
                    $(e).WdatePicker({isShowClear:false,lang:'en',dateFmt:'MM/dd/yyyy'});
                    $(this).click(function(e){//选中时间后隐藏
                        $(e).parent().WdatePicker('hide');
                    });
                }
            },sortable : false}
			, {
				name: "Rate",
				index: "Rate",
				editable: true,
				width: 100,
				editable: false,
				editable: true,
				editable : true,edittype : "select",editoptions : {value : "Rate1:Rate1;Rate2:Rate2;Rate3:Rate3;Rate4:Rate4"}
				/*formatter: alarmFormatter_rate,*/
			},
			
		],
		viewrecords: true,
		multiselect: false,
		onSelectRow : function(id) {
          if (id && id !== lastsel) {
            jQuery('#table').jqGrid('restoreRow', lastsel);
            jQuery('#table').jqGrid('editRow', id,true);
            lastsel = id;
          }
        },
		shrinkToFit: true,
		autoScroll: true
	});
	/*time*/
	function alarmFormatter_daytime(cellvalue, options, rowdata) {
		return '<input value="00:00:00" class=" f_right m-bottom_5 m-top_5 m_date_W jqgrid_tree_time" id="start" type="text" onClick="WdatePicker({isShowClear:false,lang:\'en\',dateFmt:\'HH:mm:ss\'})">';

	}

	function alarmFormatter_rate(cellvalue, options, rowdata) {
		return '<span class="selsct_contant"><select class="jqgrid_tree_select" onchange="func()"><option value ="Rate1">Rate1</option><option value ="Rate2">Rate2</option><option value="Rate3">Rate3</option><option value="Rate4">Rate4</option></select></span>';

	}

	/*week*/
var lastsel1;
	$("#table1").jqGrid({
		treeGrid: true, //是否树形
		treeGridModel: 'adjacency', //treeGrid模式，跟json元数据有关 ,adjacency/nested  
		ExpandColumn: 'name',
		url: getRootPathWeb()+'/modules/report/Meter_Group_Mgmt/jqgrid_ztree1.json',
		datatype: "json",
		width: "30%",
		autowidth: true,
		rowNum: 10,
		rowList: [10, 20, 30],
		colNames: ["Week Name", "Day Profile"],
		colModel: [{
			name: "name",
			index: "name",
			editable: false,
			search: true,
			align: "left",
			width: 80,
			frozen: true,
		}, {
			name: "sex",
			index: "sex",
			align: "left",
			editable: true,
			width: 80,
			editable: true,
			cellattr: addCellAttr,
			edittype : "select",editoptions : {value : "Day1:Day1;Day2:Day2;Day3:Day3;Day4:Day4"}
			/*formatter: alarmFormatter,*/
		}],
		/*	pager: "#pager",*/
		//					rownumbers:true,
		viewrecords: true,
		onSelectRow : function(id) {
          if (id && id !== lastsel1) {
            jQuery('#table1').jqGrid('restoreRow', lastsel1);
            jQuery('#table1').jqGrid('editRow', id, true);
            lastsel1= id;
          }
        },
		/*shrinkToFit:true,  */
		autoScroll: true,
		treeIcons: {
			leaf: ' '
		},
		caption: "Week List",
		jsonReader: {
			repeatitems: false //不需要再去后台刷新，否则可能有问题，所以最好第一次就加载所有数据 
		},
		height: "200px",
		postData: {
			'field': 'id,name,sex'
		}
	});
  function addCellAttr(rowId, val, rawObject, cm, rdata) {
        if (rawObject.name.indexOf("Week")>=0) {
        	console.log(val.sex);
           /*return  rawObject.sex.style.display='none";*/
        }else{
        
        };
    }
	function alarmFormatter(cellvalue, options, rowdata) {
		if(rowdata.level == 1) {
			return '';

		} else if(rowdata.level == 2) {

			return '<span class="selsct_contant"><select class="jqgrid_tree_select"><option value =">Day1">Day1</option><option value =">Day2">Day2</option><option value=">Day3">Day3</option><option value=">Day4">Day4</option><option value =">Day5">Day5</option><option value =">Day6">Day6</option><option value =">Day7">Day7</option></select></span>';
		}
	}
	/*season*/
 var lastsel2;
	$("#table2").jqGrid({
		data: mydata,
		datatype: "local",
		autowidth: true,
		gridview: true,
		height: "200px",
		caption: 'Season List',
		/*direction:"rtl",*/
		rownumbers: true,
		  
		colNames: ["", "Season Name", "Start Time", "Week Profile"],
		colModel: [{
				name: "id",
				index: "id",
				editable: true,
				width: 60,
				frozen: true,
				search: false,
				align: "right",
				hidden: true,
				sorttype: "int"

			}, {
				name: "Season",
				index: "Season",
				editable: true,
				width: 80,
				editable: false,
			},
			{
				name: "tax",
				index: "tax",
				editable: true,
				width: 120,
				editable: false,
				editable: true,
				/*formatter: alarmFormatter_seasontime,*/
			}, {
				name: "Rate2",
				index: "Rate2",
				editable: true,
				width: 100,
				editable: false,
				editable: true,
				edittype : "select",editoptions : {value : "Week1:Week1;Week2:Week2;Week3:Week3;Week4:Week4"}
				/*formatter: alarmFormatter_season_week,*/
			}
		],
		viewrecords: true,
		onSelectRow : function(id) {
          if (id && id !== lastsel2) {
            jQuery('#table2').jqGrid('restoreRow', lastsel2);
            jQuery('#table2').jqGrid('editRow', id, true);
            lastsel2 = id;
          }
        },
		multiselect: false,
		shrinkToFit: true,
		autoScroll: true
	});

	function alarmFormatter_seasontime() {
		return '<input value="01/04/2018 00:00:00" class=" f_right m-bottom_5 m-top_5 m_date_W jqgrid_tree_time" id="start" type="text" onClick="WdatePicker({isShowClear:false,lang:\'en\',dateFmt:\'MM/dd/yyyy HH:mm:ss\'})">';

	}

	function alarmFormatter_season_week() {
		return '<span class="selsct_contant"><select class="jqgrid_tree_select"><option value ="Week1">Week1</option><option value ="Week2">Week2</option><option value="Week3">Week3</option><option value="Week4">Week4</option><option value ="Week5">Week5</option><option value ="Week6">Week6</option><option value ="Week7">Week7</option></select></span>';

	}
	/*Special Day List*/
	var lastsel3;
	$("#table3").jqGrid({
		data: mydata,
		datatype: "local",
		autowidth: true,
		gridview: true,
		height: "auto",
		caption: 'Special Day List',
		/*direction:"rtl",*/
		rownumbers: true,
		colNames: ["", "Special Day Name", "Date", "Day Profile"],
		colModel: [{
				name: "id",
				index: "id",
				editable: true,
				width: 60,
				frozen: true,
				search: false,
				align: "right",
				hidden: true,
				sorttype: "int"

			}, {
				name: "Special",
				index: "Special",
				editable: true,
				width: 80,
				editable: false,
			},
			{
				name: "tax",
				index: "tax",
				editable: true,
				width: 120,
				editable: false,
				editable: true,
				/*formatter: alarmFormatter_Specialtime,*/
			}, {
				name: "Rate2",
				index: "Rate2",
				editable: true,
				width: 100,
				editable: true,
				edittype : "select",
				editoptions : {value : "Day1:Day1;Day2:Day2;Day3:Day3;Day4:Day4"}
				/*formatter: alarmFormatter_Special_day,*/
			}
		],
		viewrecords: true,
		multiselect: false,
		onSelectRow : function(id) {
          if (id && id !== lastsel3) {
            jQuery('#table3').jqGrid('restoreRow', lastsel3);
            jQuery('#table3').jqGrid('editRow', id, true);
            lastsel3 = id;
          }
        },
		shrinkToFit: true,
		autoScroll: true
	});

	function alarmFormatter_Specialtime() {
		return '<input value="01/04/2018" class=" f_right m-bottom_5 m-top_5 m_date_W jqgrid_tree_time" id="start" type="text" onClick="WdatePicker({isShowClear:false,lang:\'en\',dateFmt:\'MM/dd/yyyy\'})">';

	}

	function alarmFormatter_Special_day() {
		return '<span class="selsct_contant"><select class="jqgrid_tree_select"><option value =">Day1">Day1</option><option value =">Day2">Day2</option><option value=">Day3">Day3</option><option value=">Day4">Day4</option><option value =">Day5">Day5</option><option value =">Day6">Day6</option><option value =">Day7">Day7</option></select></span>';

	}

	$(window).bind("resize", function() {
		var width = $("#tab_1_1 .col-md-4 .jqGrid_wrapper").width();

		var widths = $("#tab_1_1 .col-md-12 .jqGrid_wrapper").width() - 15;
		$("#table").setGridWidth(width);
		$("#table1").setGridWidth(width);
		$("#table2").setGridWidth(width);
		$("#table3").setGridWidth(widths);
	});
	$('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
		var width = $("#tab_1_1 .col-md-4 .jqGrid_wrapper").width();

		var widths = $("#tab_1_1 .col-md-12 .jqGrid_wrapper").width() - 15;
		$("#table").setGridWidth(width);
		$("#table1").setGridWidth(width);
		$("#table2").setGridWidth(width);
		$("#table3").setGridWidth(widths);
	});
	/*添加操作按钮*/
	$("#tab_1_1 .ui-jqgrid-titlebar").append(" <div class=' titleBtnItem '><div class='btn_wrapper' title='Add' onclick='add_group_layer()'><div class='ui-title-btn'><span class='glyphicon glyphicon-plus'></span></div></div><div class='btn_wrapper padding-top8' title='Delete' onclick='del_group_layer()'><div class='ui-title-btn'><span class='glyphicon glyphicon-trash'></span></div></div></div>");

});