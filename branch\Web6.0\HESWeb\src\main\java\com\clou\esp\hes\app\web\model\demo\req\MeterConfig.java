package com.clou.esp.hes.app.web.model.demo.req;

import java.io.Serializable;

import javax.jws.soap.SOAPBinding;
import javax.jws.soap.SOAPBinding.Style;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * 孟加拉接口项目请求数据头数据对象
 * 
 * <AUTHOR>
 * 
 */
@SOAPBinding(style = Style.RPC)
@XmlRootElement(name = "MeterConfig")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = { "meterData"})
public class MeterConfig implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	public MeterConfig() {
		super();
	}

	public MeterConfig(MeterData meterData) {
		this.meterData = meterData;
	}

	public MeterData meterData;

	public MeterData getmeterData() {
		return meterData;
	}

	public void setmeterData(MeterData meterData) {
		this.meterData = meterData;
	}

	@Override
	public String toString() {
		return "MeterConfig [meterData=" + meterData + "]";
	}
	
	

}
