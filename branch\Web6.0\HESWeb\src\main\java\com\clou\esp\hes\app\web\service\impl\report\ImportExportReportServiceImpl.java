package com.clou.esp.hes.app.web.service.impl.report;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.report.ImportExportReportDao;
import com.clou.esp.hes.app.web.model.report.ImportExportReport;
import com.clou.esp.hes.app.web.model.report.LineLossReport;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.clou.esp.hes.app.web.service.report.ImportExportReportService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;


/**
 * @ClassName: ImportExportReportServiceImpl
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年10月18日 上午10:18:13
 *
 */
@Component
@Service("importExportReportService")
public class ImportExportReportServiceImpl extends
		CommonServiceImpl<ImportExportReport> implements ImportExportReportService{

	@Resource
	private ImportExportReportDao importExportReportDao;
	
	
	@Override
	public void setCommonService() {
		// TODO Auto-generated method stub
		super.setCommonService(importExportReportDao);
	}

	@Override
	public JqGridResponseTo findImportExportReport(JqGridSearchTo jqGridSearchTo) {
		// TODO Auto-generated method stub
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		PageInfo<ImportExportReport> pageInfo = new PageInfo<ImportExportReport>(importExportReportDao.findImportExportReport(jqGridSearchTo));
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}

	@Override
	public JqGridResponseTo findImportExportReportDetail(
			JqGridSearchTo jqGridSearchTo) {
		// TODO Auto-generated method stub
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		PageInfo<ImportExportReport> pageInfo = new PageInfo<ImportExportReport>(importExportReportDao.findImportExportReportDetail(jqGridSearchTo));
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}

	@Override
	public List<ImportExportReport> findImportExportReportList(
			JqGridSearchTo jqGridSearchTo) {
		// TODO Auto-generated method stub
		return importExportReportDao.findImportExportReport(jqGridSearchTo);
	}

	@Override
	public List<ImportExportReport> findImportExportReportDetailList(
			JqGridSearchTo jqGridSearchTo) {
		// TODO Auto-generated method stub
		return importExportReportDao.findImportExportReportDetail(jqGridSearchTo);
	}

	@Override
	public List<ImportExportReport> findImportExportParamList(
			Map<String, Object> p) {
		// TODO Auto-generated method stub
		return importExportReportDao.findImportExportParamList(p);
	}

}
