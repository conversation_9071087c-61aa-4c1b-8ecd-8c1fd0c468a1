package com.clou.esp.hes.app.web.model.report;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.google.common.collect.Maps;
import com.power7000g.core.util.base.DateUtils;

public class ProgressDelayDataReport extends BaseEntity {
	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    /**设备SN*/
	private java.lang.String serizlName;
	/**时间*/
	private java.util.Date times;
	/**时间*/
	private java.lang.String timeStr;
	/**数据值1*/
	private java.lang.String delay_value1;
	/**数据值2*/
	private java.lang.String delay_value2;
	/**数据值3*/
	private java.lang.String delay_value3;
	/**数据值4*/
	private java.lang.String delay_value4;
	/**数据值5*/
	private java.lang.String delay_value5;
	/**数据值6*/
	private java.lang.String delay_value6;
	/**数据值7*/
	private java.lang.String delay_value7;
	/**数据值8*/
	private java.lang.String delay_value8;
	/**数据值9*/
	private java.lang.String delay_value9;
	/**数据值10*/
	private java.lang.String delay_value10;
	/**数据值11*/
	private java.lang.String delay_value11;
	/**数据值12*/
	private java.lang.String delay_value12;
	/**数据值13*/
	private java.lang.String delay_value13;
	/**数据值14*/
	private java.lang.String delay_value14;
	/**数据值15*/
	private java.lang.String delay_value15;
	/**数据值16*/
	private java.lang.String delay_value16;
	/**数据值16*/
	private java.lang.String delay_value17;
	/**数据值16*/
	private java.lang.String delay_value18;
	/**数据值16*/
	private java.lang.String delay_value19;
	/**数据值16*/
	private java.lang.String delay_value20;
	/**数据值16*/
	private java.lang.String delay_value21;
	/**数据值16*/
	private java.lang.String delay_value22;
	/**数据值16*/
	private java.lang.String delay_value23;
	/**数据值16*/
	private java.lang.String delay_value24;
	/**数据值16*/
	private java.lang.String delay_value25;
	/**数据值16*/
	private java.lang.String delay_value26;
	/**数据值16*/
	private java.lang.String delay_value27;
	/**数据值16*/
	private java.lang.String delay_value28;
	/**数据值16*/
	private java.lang.String delay_value29;
	/**数据值16*/
	private java.lang.String delay_value30;
	/**数据值16*/
	private java.lang.String delay_value31;
	/**数据值16*/
	private java.lang.String delay_value32;
	/**数据值16*/
	private java.lang.String delay_value33;
	/**数据值16*/
	private java.lang.String delay_value34;
	/**数据值16*/
	private java.lang.String delay_value35;
	/**数据值16*/
	private java.lang.String delay_value36;
	/**数据值16*/
	private java.lang.String delay_value37;
	/**数据值16*/
	private java.lang.String delay_value38;
	/**数据值16*/
	private java.lang.String delay_value39;
	/**数据值16*/
	private java.lang.String delay_value40;
	/**数据值16*/
	private java.lang.String delay_value41;
	/**数据值16*/
	private java.lang.String delay_value42;
	/**数据值16*/
	private java.lang.String delay_value43;
	/**数据值16*/
	private java.lang.String delay_value44;
	/**数据值16*/
	private java.lang.String delay_value45;
	
	
	
	public java.lang.String getSerizlName() {
		return serizlName;
	}
	public void setSerizlName(java.lang.String serizlName) {
		this.serizlName = serizlName;
	}
	public java.util.Date getTimes() {
		return times;
	}
	public void setTimes(java.util.Date times) {
		this.times= times;
	}
	
	
	
	public java.lang.String getDelay_value1() {
		return delay_value1;
	}
	public void setDelay_value1(java.lang.String delay_value1) {
		this.delay_value1 = delay_value1;
	}
	public java.lang.String getDelay_value2() {
		return delay_value2;
	}
	public void setDelay_value2(java.lang.String delay_value2) {
		this.delay_value2 = delay_value2;
	}
	public java.lang.String getDelay_value3() {
		return delay_value3;
	}
	public void setDelay_value3(java.lang.String delay_value3) {
		this.delay_value3 = delay_value3;
	}
	public java.lang.String getDelay_value4() {
		return delay_value4;
	}
	public void setDelay_value4(java.lang.String delay_value4) {
		this.delay_value4 = delay_value4;
	}
	public java.lang.String getDelay_value5() {
		return delay_value5;
	}
	public void setDelay_value5(java.lang.String delay_value5) {
		this.delay_value5 = delay_value5;
	}
	public java.lang.String getDelay_value6() {
		return delay_value6;
	}
	public void setDelay_value6(java.lang.String delay_value6) {
		this.delay_value6 = delay_value6;
	}
	public java.lang.String getDelay_value7() {
		return delay_value7;
	}
	public void setDelay_value7(java.lang.String delay_value7) {
		this.delay_value7 = delay_value7;
	}
	public java.lang.String getDelay_value8() {
		return delay_value8;
	}
	public void setDelay_value8(java.lang.String delay_value8) {
		this.delay_value8 = delay_value8;
	}
	public java.lang.String getDelay_value9() {
		return delay_value9;
	}
	public void setDelay_value9(java.lang.String delay_value9) {
		this.delay_value9 = delay_value9;
	}
	public java.lang.String getDelay_value10() {
		return delay_value10;
	}
	public void setDelay_value10(java.lang.String delay_value10) {
		this.delay_value10 = delay_value10;
	}
	public java.lang.String getDelay_value11() {
		return delay_value11;
	}
	public void setDelay_value11(java.lang.String delay_value11) {
		this.delay_value11 = delay_value11;
	}
	public java.lang.String getDelay_value12() {
		return delay_value12;
	}
	public void setDelay_value12(java.lang.String delay_value12) {
		this.delay_value12 = delay_value12;
	}
	public java.lang.String getDelay_value13() {
		return delay_value13;
	}
	public void setDelay_value13(java.lang.String delay_value13) {
		this.delay_value13 = delay_value13;
	}
	public java.lang.String getDelay_value14() {
		return delay_value14;
	}
	public void setDelay_value14(java.lang.String delay_value14) {
		this.delay_value14 = delay_value14;
	}
	public java.lang.String getDelay_value15() {
		return delay_value15;
	}
	public void setDelay_value15(java.lang.String delay_value15) {
		this.delay_value15 = delay_value15;
	}
	public java.lang.String getDelay_value16() {
		return delay_value16;
	}
	public void setDelay_value16(java.lang.String delay_value16) {
		this.delay_value16 = delay_value16;
	}
	public java.lang.String getDelay_value17() {
		return delay_value17;
	}
	public void setDelay_value17(java.lang.String delay_value17) {
		this.delay_value17 = delay_value17;
	}
	public java.lang.String getDelay_value18() {
		return delay_value18;
	}
	public void setDelay_value18(java.lang.String delay_value18) {
		this.delay_value18 = delay_value18;
	}
	public java.lang.String getDelay_value19() {
		return delay_value19;
	}
	public void setDelay_value19(java.lang.String delay_value19) {
		this.delay_value19 = delay_value19;
	}
	public java.lang.String getDelay_value20() {
		return delay_value20;
	}
	public void setDelay_value20(java.lang.String delay_value20) {
		this.delay_value20 = delay_value20;
	}
	public java.lang.String getDelay_value21() {
		return delay_value21;
	}
	public void setDelay_value21(java.lang.String delay_value21) {
		this.delay_value21 = delay_value21;
	}
	public java.lang.String getDelay_value22() {
		return delay_value22;
	}
	public void setDelay_value22(java.lang.String delay_value22) {
		this.delay_value22 = delay_value22;
	}
	public java.lang.String getDelay_value23() {
		return delay_value23;
	}
	public void setDelay_value23(java.lang.String delay_value23) {
		this.delay_value23 = delay_value23;
	}
	public java.lang.String getDelay_value24() {
		return delay_value24;
	}
	public void setDelay_value24(java.lang.String delay_value24) {
		this.delay_value24 = delay_value24;
	}
	public java.lang.String getDelay_value25() {
		return delay_value25;
	}
	public void setDelay_value25(java.lang.String delay_value25) {
		this.delay_value25 = delay_value25;
	}
	public java.lang.String getDelay_value26() {
		return delay_value26;
	}
	public void setDelay_value26(java.lang.String delay_value26) {
		this.delay_value26 = delay_value26;
	}
	public java.lang.String getDelay_value27() {
		return delay_value27;
	}
	public void setDelay_value27(java.lang.String delay_value27) {
		this.delay_value27 = delay_value27;
	}
	public java.lang.String getDelay_value28() {
		return delay_value28;
	}
	public void setDelay_value28(java.lang.String delay_value28) {
		this.delay_value28 = delay_value28;
	}
	public java.lang.String getDelay_value29() {
		return delay_value29;
	}
	public void setDelay_value29(java.lang.String delay_value29) {
		this.delay_value29 = delay_value29;
	}
	public java.lang.String getDelay_value30() {
		return delay_value30;
	}
	public void setDelay_value30(java.lang.String delay_value30) {
		this.delay_value30 = delay_value30;
	}
	public java.lang.String getDelay_value31() {
		return delay_value31;
	}
	public void setDelay_value31(java.lang.String delay_value31) {
		this.delay_value31 = delay_value31;
	}
	public java.lang.String getDelay_value32() {
		return delay_value32;
	}
	public void setDelay_value32(java.lang.String delay_value32) {
		this.delay_value32 = delay_value32;
	}
	public java.lang.String getDelay_value33() {
		return delay_value33;
	}
	public void setDelay_value33(java.lang.String delay_value33) {
		this.delay_value33 = delay_value33;
	}
	public java.lang.String getDelay_value34() {
		return delay_value34;
	}
	public void setDelay_value34(java.lang.String delay_value34) {
		this.delay_value34 = delay_value34;
	}
	public java.lang.String getDelay_value35() {
		return delay_value35;
	}
	public void setDelay_value35(java.lang.String delay_value35) {
		this.delay_value35 = delay_value35;
	}
	public java.lang.String getDelay_value36() {
		return delay_value36;
	}
	public void setDelay_value36(java.lang.String delay_value36) {
		this.delay_value36 = delay_value36;
	}
	public java.lang.String getDelay_value37() {
		return delay_value37;
	}
	public void setDelay_value37(java.lang.String delay_value37) {
		this.delay_value37 = delay_value37;
	}
	public java.lang.String getDelay_value38() {
		return delay_value38;
	}
	public void setDelay_value38(java.lang.String delay_value38) {
		this.delay_value38 = delay_value38;
	}
	public java.lang.String getDelay_value39() {
		return delay_value39;
	}
	public void setDelay_value39(java.lang.String delay_value39) {
		this.delay_value39 = delay_value39;
	}
	public java.lang.String getDelay_value40() {
		return delay_value40;
	}
	public void setDelay_value40(java.lang.String delay_value40) {
		this.delay_value40 = delay_value40;
	}
	public java.lang.String getDelay_value41() {
		return delay_value41;
	}
	public void setDelay_value41(java.lang.String delay_value41) {
		this.delay_value41 = delay_value41;
	}
	public java.lang.String getDelay_value42() {
		return delay_value42;
	}
	public void setDelay_value42(java.lang.String delay_value42) {
		this.delay_value42 = delay_value42;
	}
	public java.lang.String getDelay_value43() {
		return delay_value43;
	}
	public void setDelay_value43(java.lang.String delay_value43) {
		this.delay_value43 = delay_value43;
	}
	public java.lang.String getDelay_value44() {
		return delay_value44;
	}
	public void setDelay_value44(java.lang.String delay_value44) {
		this.delay_value44 = delay_value44;
	}
	public java.lang.String getDelay_value45() {
		return delay_value45;
	}
	public void setDelay_value45(java.lang.String delay_value45) {
		this.delay_value45 = delay_value45;
	}
	public ProgressDelayDataReport() {};
	
	public ProgressDelayDataReport(String serizlName, Date times) {
		super();
		this.serizlName = serizlName;
		this.times = times;
		this.timeStr = DateUtils.date2Str(
				times,
				new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")));
	}
	
	
	
	

	public java.lang.String getTimeStr() {
		return timeStr;
	}
	public void setTimeStr(java.lang.String timeStr) {
		this.timeStr = timeStr;
	}
	public Map<String,String> getMap(SimpleDateFormat sdf ) {
		Map<String,String> map = Maps.newHashMap();
		map.put("serizlName", serizlName);
		map.put("times", sdf.format(times));
		map.put("delay_value1",delay_value1);
		map.put("delay_value2",delay_value2);
		map.put("delay_value3",delay_value3);
		map.put("delay_value4",delay_value4);
		map.put("delay_value5",delay_value5);
		map.put("delay_value6",delay_value6);
		map.put("delay_value7",delay_value7);
		map.put("delay_value8",delay_value8);
		map.put("delay_value9",delay_value9);
		map.put("delay_value10",delay_value10);
		map.put("delay_value11",delay_value11);
		map.put("delay_value12",delay_value12);
		map.put("delay_value13",delay_value13);
		map.put("delay_value14",delay_value14);
		map.put("delay_value15",delay_value15);
		map.put("delay_value16",delay_value16);
		map.put("delay_value17",delay_value17);
		map.put("delay_value18",delay_value18);
		map.put("delay_value19",delay_value19);
		map.put("delay_value20",delay_value20);
		map.put("delay_value21",delay_value21);
		map.put("delay_value22",delay_value22);
		map.put("delay_value23",delay_value23);
		map.put("delay_value24",delay_value24);
		map.put("delay_value25",delay_value25);
		map.put("delay_value26",delay_value26);
		map.put("delay_value27",delay_value27);
		map.put("delay_value28",delay_value28);
		map.put("delay_value29",delay_value29);
		map.put("delay_value30",delay_value30);
		map.put("delay_value31",delay_value31);
		map.put("delay_value32",delay_value32);
		map.put("delay_value33",delay_value33);
		map.put("delay_value34",delay_value34);
		map.put("delay_value35",delay_value35);
		map.put("delay_value36",delay_value36);
		map.put("delay_value37",delay_value37);
		map.put("delay_value38",delay_value38);
		map.put("delay_value39",delay_value39);
		map.put("delay_value40",delay_value40);
		map.put("delay_value41",delay_value41);
		map.put("delay_value42",delay_value42);
		map.put("delay_value43",delay_value43);
		map.put("delay_value44",delay_value44);
		map.put("delay_value45",delay_value45);
		return map;
	}
}
