/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictDeviceModel{ } 
 * 
 * 摘    要： 设备类型型号
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-18 02:59:36
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.dict;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;

import com.clou.esp.hes.app.web.model.dict.DictDeviceModel;
import com.clou.esp.hes.app.web.service.dict.DictDeviceModelService;

/**
 * <AUTHOR>
 * @时间：2017-11-18 02:59:36
 * @描述：设备类型型号类
 */
@Controller
@RequestMapping("/dictDeviceModelController")
public class DictDeviceModelController extends BaseController{

 	@Resource
    private DictDeviceModelService dictDeviceModelService;

	/**
	 * 跳转到设备类型型号列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/dict/dictDeviceModelList");
    }

	/**
	 * 跳转到设备类型型号新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dictDeviceModel")
	public ModelAndView dictDeviceModel(DictDeviceModel dictDeviceModel,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dictDeviceModel.getId())){
			try {
                dictDeviceModel=dictDeviceModelService.getEntity(dictDeviceModel.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dictDeviceModel", dictDeviceModel);
		}
		return new ModelAndView("/dict/dictDeviceModel");
	}


	/**
	 * 设备类型型号查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=dictDeviceModelService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除设备类型型号信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DictDeviceModel dictDeviceModel,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dictDeviceModelService.deleteById(dictDeviceModel.getId())>0){
                j.setMsg("删除成功");
            }else{
                j.setSuccess(false);
                j.setMsg("删除失败");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
    
    /**
     * 保存设备类型型号信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DictDeviceModel dictDeviceModel,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DictDeviceModel t=new  DictDeviceModel();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dictDeviceModel.getId())){
        	t=dictDeviceModelService.getEntity(dictDeviceModel.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dictDeviceModel, t);
				dictDeviceModelService.update(t);
				j.setMsg("修改成功");
				
			}else{
	            dictDeviceModelService.save(dictDeviceModel);
	            j.setMsg("创建成功");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
	
	
	
}