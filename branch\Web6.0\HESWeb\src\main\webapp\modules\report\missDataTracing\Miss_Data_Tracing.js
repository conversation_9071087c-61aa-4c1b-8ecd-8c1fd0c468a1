/*以下代码是实现自动显示当前日期的功能函数*/
function today(t) {
	var today = new Date();
	var h = today.getFullYear();
	var m = today.getMonth() + 1;
	var d = today.getDate();
	var y = d - 1;
	m = m < 10 ? "0" + m : m; //  这里判断月份是否<10,如果是在月份前面加'0'
	d = d < 10 ? "0" + d : d; //  这里判断日期是否<10,如果是在日期前面加'0'
	if(t == 0) {
		return m + "/" + d + "/" + h;
	} else {
		var day1 = new Date();
		day1.setTime(day1.getTime() - 24 * 60 * 60 * 1000);
		return(day1.getMonth() + 1) + "/" + day1.getDate() + "/" + day1.getFullYear();
	}
}
document.getElementById("start").value = today(1);
document.getElementById("end").value = today(0); //获取文本id并且传入当前日期