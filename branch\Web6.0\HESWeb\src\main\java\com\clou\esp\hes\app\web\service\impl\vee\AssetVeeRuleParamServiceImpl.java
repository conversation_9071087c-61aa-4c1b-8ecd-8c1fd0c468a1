/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetVeeRuleParam{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2019-03-04 09:38:24
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.vee;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.vee.AssetVeeRuleParamDao;
import com.clou.esp.hes.app.web.model.vee.AssetVeeRuleParam;
import com.clou.esp.hes.app.web.service.vee.AssetVeeRuleParamService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("assetVeeRuleParamService")
public class AssetVeeRuleParamServiceImpl  extends CommonServiceImpl<AssetVeeRuleParam>  implements AssetVeeRuleParamService {

	@Resource
	private AssetVeeRuleParamDao assetVeeRuleParamDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(assetVeeRuleParamDao);
    }
	@SuppressWarnings("rawtypes")
	public AssetVeeRuleParamServiceImpl() {}
	
	
}