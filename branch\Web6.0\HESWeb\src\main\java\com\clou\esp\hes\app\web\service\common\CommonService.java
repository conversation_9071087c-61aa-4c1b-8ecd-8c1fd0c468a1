package com.clou.esp.hes.app.web.service.common;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

/**
 * 
 * <AUTHOR>
 * 
 */
public interface CommonService<T extends Serializable> {
	/**
	 * 
	 * @see
	 */
	void setCommonService();

	/**
	 * 保存
	 * 
	 * @param entity
	 * @return
	 */
	Serializable save(T entity);

	/**
	 * 批量保存
	 * 
	 * @param entitys
	 */
	void batchSave(List<T> entitys);

	/**
	 * 获取总数
	 * 
	 * @param entity
	 * @return
	 */
	Long getCount(T entity);

	/**
	 * 删除
	 * 
	 * @param entity
	 * @return
	 */
	Integer delete(T entity);

	/**
	 * 根据主键删除
	 * 
	 * @param id
	 * @return
	 */
	Integer deleteById(String id);

	/**
	 * 删除实体集合
	 * 
	 * @param
	 * @param entities
	 */
	void deleteAllEntitie(Collection<T> entities);

	/**
	 * 更新指定的实体
	 * 
	 * @param
	 * @param pojo
	 */
	Integer update(T pojo);

	/**
	 * 保存或修改
	 * 
	 * @param entity
	 */
	void saveOrUpdate(T entity);

	/**
	 * 批量修改
	 * 
	 * @param entitys
	 */
	void batchUpdate(List<T> entitys);

	/**
	 * 批量保存或修改
	 * 
	 * @param entitys
	 */
	void batchSaveOrUpdate(List<T> entitys);

	/**
	 * 根据实体名称和主键获取实体
	 * 
	 * @param
	 * @param id
	 * @return
	 */
	T getEntity(String id);

	/**
	 * 获取实体
	 * 
	 * @param entity
	 * @return
	 */
	T get(T entity);

	/**
	 * 获取查询实体列表
	 * 
	 * @param entity
	 * @return
	 */
	List<T> getList(T entity);

	/**
	 * 获取全部实体列表
	 * 
	 * @return
	 */
	List<T> getAllList();

	/**
	 * 分页
	 * 
	 * @param JqGridSearchTo
	 * @return String
	 */
	JqGridResponseTo getForJqGrid(JqGridSearchTo jqGridSearchTo);
	
	/**
	 * 新增data user log
	 * @Description 
	 * @param userId
	 * @param logType
	 * @param logSubType
	 * @param detail void
	 * <AUTHOR> 
	 * @Time 2018年4月4日 上午11:17:48
	 */
	void insertDataUserLog(String userId, String logType, String logSubType, String detail);
}
