package com.clou.esp.hes.app.web.model.report;

import java.math.BigDecimal;
import java.util.Date;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.clou.esp.hes.app.web.validation.ValidGroup2;
import com.power7000g.core.excel.Excel;


/**
 * @ClassName: LineLossReport
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年8月13日 上午9:35:35
 *
 */
public class LineLossReport extends BaseEntity{

	private static final long serialVersionUID = 1L;
	
	@Excel(name = "Line Loss Object", width = 30)
	private String lineLossObject; // sn
	@Excel(name = "Organization", width = 30)
	private String organization; // 组织机构
	@Excel(name = "Entity Name", width = 30)
	private String entityName;
	@Excel(name = "Line Loss Object Type", width = 30)
	private String lineLossObjectType; // 线损对象类型
	@Excel(name = "Time Type", width = 30)
	private String timeType; // 时间类型
	@Excel(name = "Date", width = 30)
	private String calDate;
	@Excel(name = "Import(KWh)", width = 30)
	private BigDecimal importValue;
	@Excel(name = "Export(KWh)", width = 30)
	private BigDecimal exportValue;
//	@Excel(name = "Loss", width = 30)
	private BigDecimal loss;
	@Excel(name = "Rate(%)", width = 30)
	private BigDecimal rate;
	@Excel(name = "Miss Data", width = 30,groups = {ValidGroup1.class,ValidGroup2.class})
	private String missData; // 值
//	private double rate;
	private String calcObjId;
	
	public LineLossReport(){
		
	}
	
	public String getEntityName() {
		return entityName;
	}

	public void setEntityName(String entityName) {
		this.entityName = entityName;
	}

	public String getLineLossObject() {
		return lineLossObject;
	}
	public void setLineLossObject(String lineLossObject) {
		this.lineLossObject = lineLossObject;
	}
	public String getOrganization() {
		return organization;
	}
	public void setOrganization(String organization) {
		this.organization = organization;
	}
	public String getLineLossObjectType() {
		return lineLossObjectType;
	}
	public void setLineLossObjectType(String lineLossObjectType) {
		this.lineLossObjectType = lineLossObjectType;
	}
	public String getTimeType() {
		return timeType;
	}
	public void setTimeType(String timeType) {
		this.timeType = timeType;
	}
	public String getCalDate() {
		return calDate;
	}

	public void setCalDate(String calDate) {
		this.calDate = calDate;
	}

	public BigDecimal getImportValue() {
		return importValue;
	}
	public void setImportValue(BigDecimal importValue) {
		this.importValue = importValue;
	}
	public BigDecimal getExportValue() {
		return exportValue;
	}
	public void setExportValue(BigDecimal exportValue) {
		this.exportValue = exportValue;
	}
	

	public BigDecimal getLoss() {
		return loss;
	}

	public void setLoss(BigDecimal loss) {
		this.loss = loss;
	}

	public String getCalcObjId() {
		return calcObjId;
	}

	public void setCalcObjId(String calcObjId) {
		this.calcObjId = calcObjId;
	}

	public BigDecimal getRate() {
		return rate;
	}

	public void setRate(BigDecimal rate) {
		this.rate = rate;
	}

	public String getMissData() {
		return missData;
	}

	public void setMissData(String missData) {
		this.missData = missData;
	}

}