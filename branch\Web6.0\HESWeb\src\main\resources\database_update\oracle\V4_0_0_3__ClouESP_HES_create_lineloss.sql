/*----线路档案表--*/
CREATE TABLE ASSET_LINE
(	
	"ID" VARCHAR2(32 BYTE) not null, 
	"SN" VARCHAR2(32 BYTE) not null,
	"ORG_ID" VARCHAR2(32 BYTE) not null,	
	"NAME" VARCHAR2(32 BYTE), 
	"LINE_TYPE" NUMBER(10,0), 
	"VOLTAGE_LEVEL" NUMBER(10,0),
	primary key (id,sn,org_id)
);

/*----变压器档案表--*/
CREATE TABLE ASSET_TRANSFORMER
(	
	"ID" VARCHAR2(32 BYTE) not null,
	"SN" VARCHAR2(32 BYTE), 
	"NAME" VARCHAR2(32 BYTE), 
	"ORG_ID" VARCHAR2(32 BYTE), 
	"RATED_CAPACITY" NUMBER(10,2), 
	"ADDR" VARCHAR2(64 BYTE),
	primary key (id)
);
alter table ASSET_COMMUNICATOR add (SIM_NUM VARCHAR2(32 BYTE));
alter table ASSET_METER add (ADDR VARCHAR2(32 BYTE));
alter table ASSET_METER add (LONGITUDE NUMBER(10,4));
alter table ASSET_METER add (LATITUDE NUMBER(10,4));

CREATE TABLE DATA_STATISTICS_DEVICE
(	
	"ID" VARCHAR2(32) not null,
	"ID_TYPE" VARCHAR2(32) not null,
	"TV" DATE not null,
	"TV_TYPE" NUMBER not null,	
	"PERCENT" NUMBER(10,2),  
	"COUNT_CURRENT" NUMBER(10,2),
	primary key (ID,ID_TYPE,TV,TV_TYPE)
);

CREATE TABLE DATA_STATISTICS_EVENT
(	
	"EVENT_ID" VARCHAR2(64 BYTE) not null, 
	"TV" DATE not null, 
	"TV_TYPE" NUMBER not null,		
	"EVENT_NAME" VARCHAR2(128 BYTE) not null, 
	"PERCENT" NUMBER(10,2), 
	"COUNT_CURRENT" NUMBER(10,2),
	primary key (EVENT_ID,TV,TV_TYPE)
);

insert into dict_service_attribute values (4, 'UCI.DittPort', 'Ditt proxy listen port', 'Integer', '9999', null,null,null,5);
insert into sys_service_attribute values ('20010003', 'UCI.DittPort', '9902');
insert into dict_service_attribute values (1, 'Channel.SGPort', 'Sg protocol server listen port', 'Integer', '9999', null,null,null,4);
insert into sys_service_attribute values ('20010001', 'Channel.SGPort', '9904');