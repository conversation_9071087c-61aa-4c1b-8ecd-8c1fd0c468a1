$(document).ready(function() {
	$.jgrid.defaults.styleUI = "Bootstrap";
	var mydata = [{
		id: "1",
		invdate: "SN0001",
		name: "<PERSON><PERSON><PERSON>22",
		note: "13/20/2017  00:00:00",
		resaon: "Upload File], Communication failed.",
		status: "Failed",
		resaon1: "13/20/2017 15:45:00",
		resaon2: "0",
		act: "12/20/2017 15:45:00",
		amount: "Clou",
		tax: "Energy Profile",
		total: "12/20/2017 00:00:00",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "2",
		invdate: "SN0001",
		name: "<PERSON><PERSON><PERSON><PERSON>",
		note: "13/20/2017  00:00:00",
		resaon: "  ",
		status: "Running",
		resaon1: "13/20/2017 16:45:00",
		resaon2: "30",
		act: "12/20/2017 15:45:00",
		amount: "Clou",
		tax: "Energy Profile",
		total: "12/20/2017 00:00:00",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "3",
		invdate: "SN0001",
		name: "<PERSON><PERSON><PERSON><PERSON>",
		note: "13/20/2017  00:00:00",
		resaon: "[Request Version], Communication failed.",
		status: "Failed",
		resaon1: "13/20/2017 15:45:00",
		resaon2: "0",
		act: "12/20/2017 15:45:00",
		amount: "Clou",
		tax: "Energy Profile",
		total: "12/20/2017 00:00:00",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "4",
		invdate: "SN0001",
		name: "CLK22",
		note: "13/20/2017  00:00:00",
		resaon: "  ",
		status: "Running",
		resaon1: "13/20/2017 16:45:00",
		resaon2: "30",
		act: "12/20/2017 15:45:00",
		amount: "Clou",
		tax: "Energy Profile",
		total: "12/20/2017 00:00:00",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "5",
		invdate: "SN0001",
		name: "CLK22",
		note: "13/20/2017  00:00:00",
		resaon: "  ",
		status: "Running",
		resaon1: "13/20/2017 16:45:00",
		resaon2: "30",
		act: "12/20/2017 15:45:00",
		amount: "Clou",
		tax: "Energy Profile",
		total: "12/20/2017 00:00:00",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "6",
		invdate: "SN0001",
		name: "CLK22",
		note: "13/20/2017  00:00:00",
		resaon: "  ",
		status: "Running",
		resaon1: "13/20/2017 16:45:00",
		resaon2: "30",
		act: "12/20/2017 15:45:00",
		amount: "Clou",
		tax: "Energy Profile",
		total: "12/20/2017 00:00:00",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "7",
		invdate: "SN0001",
		name: "CLK22",
		note: "13/20/2017  00:00:00",
		resaon: "Upload File], Communication failed.",
		status: "Failed",
		resaon1: "13/20/2017 15:45:00",
		resaon2: "0",
		act: "12/20/2017 15:45:00",
		amount: "Clou",
		tax: "Energy Profile",
		total: "12/20/2017 00:00:00",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "8",
		invdate: "SN0001",
		name: "CLK22",
		note: "13/20/2017  00:00:00",
		resaon: "  ",
		status: "Running",
		resaon1: "13/20/2017 16:45:00",
		resaon2: "30",
		act: "12/20/2017 15:45:00",
		amount: "Clou",
		tax: "Energy Profile",
		total: "12/20/2017 00:00:00",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "9",
		invdate: "SN0001",
		name: "CLK22",
		note: "13/20/2017  00:00:00",
		resaon: "  ",
		status: "Running",
		resaon1: "13/20/2017 16:45:00",
		resaon2: "30",
		act: "12/20/2017 15:45:00",
		amount: "Clou",
		tax: "Energy Profile",
		total: "12/20/2017 00:00:00",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "10",
		invdate: "SN0001",
		name: "CLK22",
		note: "13/20/2017  00:00:00",
		resaon: "Upload File], Communication failed.",
		status: "Failed",
		resaon1: "13/20/2017 15:45:00",
		resaon2: "0",
		act: "12/20/2017 15:45:00",
		amount: "Clou",
		tax: "Energy Profile",
		total: "12/20/2017 00:00:00",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "10",
		invdate: "SN0001",
		name: "CLK22",
		note: "13/20/2017  00:00:00",
		resaon: "  ",
		status: "Running",
		resaon1: "13/20/2017 16:45:00",
		resaon2: "30",
		act: "12/20/2017 15:45:00",
		amount: "Clou",
		tax: "Energy Profile",
		total: "12/20/2017 00:00:00",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "10",
		invdate: "SN0001",
		name: "CLK22",
		note: "13/20/2017  00:00:00",
		resaon: "  ",
		status: "Running",
		resaon1: "13/20/2017 16:45:00",
		resaon2: "30",
		act: "12/20/2017 15:45:00",
		amount: "Clou",
		tax: "Energy Profile",
		total: "12/20/2017 00:00:00",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "10",
		invdate: "SN0001",
		name: "CLK22",
		note: "13/20/2017  00:00:00",
		resaon: "  ",
		status: "Running",
		resaon1: "13/20/2017 16:45:00",
		resaon2: "30",
		act: "12/20/2017 15:45:00",
		amount: "Clou",
		tax: "Energy Profile",
		total: "12/20/2017 00:00:00",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "10",
		invdate: "SN0001",
		name: "CLK22",
		note: "13/20/2017  00:00:00",
		resaon: "  ",
		status: "Running",
		resaon1: "13/20/2017 16:45:00",
		resaon2: "30",
		act: "12/20/2017 15:45:00",
		amount: "Clou",
		tax: "Energy Profile",
		total: "12/20/2017 00:00:00",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "10",
		invdate: "SN0001",
		name: "CLK22",
		note: "13/20/2017  00:00:00",
		resaon: "  ",
		status: "Running",
		resaon1: "13/20/2017 16:45:00",
		resaon2: "30",
		act: "12/20/2017 15:45:00",
		amount: "Clou",
		tax: "Energy Profile",
		total: "12/20/2017 00:00:00",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "10",
		invdate: "SN0001",
		name: "CLK22",
		note: "13/20/2017  00:00:00",
		resaon: "  ",
		status: "Running",
		resaon1: "13/20/2017 16:45:00",
		resaon2: "30",
		act: "12/20/2017 15:45:00",
		amount: "Clou",
		tax: "Energy Profile",
		total: "12/20/2017 00:00:00",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "10",
		invdate: "SN0001",
		name: "CLK22",
		note: "13/20/2017  00:00:00",
		resaon: "  ",
		status: "Running",
		resaon1: "13/20/2017 16:45:00",
		resaon2: "30",
		act: "12/20/2017 15:45:00",
		amount: "Clou",
		tax: "Energy Profile",
		total: "12/20/2017 00:00:00",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "10",
		invdate: "SN0001",
		name: "CLK22",
		note: "13/20/2017  00:00:00",
		resaon: "  ",
		status: "Running",
		resaon1: "13/20/2017 16:45:00",
		resaon2: "30",
		act: "12/20/2017 15:45:00",
		amount: "Clou",
		tax: "Energy Profile",
		total: "12/20/2017 00:00:00",
		totals: "12/20/2017 15:45:00"
	}, {
		id: "10",
		invdate: "SN0020",
		name: "CLK22",
		note: "13/20/2017  00:00:00",
		resaon: "  ",
		status: "Running",
		resaon1: "13/20/2017 16:45:00",
		resaon2: "30",
		act: "12/20/2017 15:45:00",
		amount: "Clou",
		tax: "Energy Profile",
		total: "12/20/2017 00:00:00",
		totals: "12/20/2017 15:45:00"
	}, ];
	$("#table_list_3").jqGrid({
		data: mydata,
		datatype: "local",
		autowidth: false,
		gridview: true,
		height: "114px",
       caption: ' ',
		/*direction:"rtl",*/
		rownumbers: true,
		altRows: true, //单双行样式不同  
		altclass: 'differ',

		colNames: ["", "Serial Number", "Model", "Communicator", "Profile", "Start Time", "End Time"],
		colModel: [{
			name: "id",
			index: "id",
			editable: true,
			width: 60,
			frozen: true,
			search: false,
			hidden: true,
			sorttype: "int"

		}, {
			name: "invdate",
			index: "invdate",
			editable: true,
			width: 120,
		}, {
			name: "name",
			index: "name",
			editable: true,
			width: 120
		}, {
			name: "amount",
			index: "amount",
			editable: true,
			width: 120,
		}, {
			name: "tax",
			index: "tax",
			editable: true,
			width: 120,
			sorttype: "float"
		}, {
			name: "total",
			index: "total",
			editable: true,
			width: 170,
			stype: "select"
		}, {
			name: "note",
			index: "note",
			editable: true,
			width: 120,
			search: false,
			sortable: false
		}],
		viewrecords: true,
		multiselect: false,
		/*editurl: "/RowEditing",*/
		shrinkToFit: true,
		autoScroll: true
	});

	$("#table_list_3").jqGrid("navGrid", "#pager_list_3", {
			edit: false,
			add: false,
			del: false,
			search: false
		},

		{
			height: 200,
			reloadAfterSubmit: true
		});

	$("#table_list4").jqGrid({
		data: mydata,
		datatype: "local",
		autowidth: false,
		gridview: true,
		height: "auto",

		/*direction:"rtl",*/
		rownumbers: true,
		altRows: true, //单双行样式不同  
		altclass: 'differ',
		
		colNames: ["", "Serial Number", "Status", "Profile", "Time", "Data Channel", "Value"],
		colModel: [{
			name: "id",
			index: "id",
			editable: true,
			width: 60,
			align: "right",
			frozen: true,
			search: false,
			hidden: true,
		}, {
			name: "invdate",
			index: "invdate",
			editable: true,
			width: 120,
		}, {
			name: "status",
			index: "status",
			editable: true,
			width: 120,
			sorttype: "float",
			stype: "select",
			cellattr: addCellAttr//Result=Failed 的时候改变字体的颜色为红色
		}, {
			name: "tax",
			index: "tax",
			editable: true,
			width: 120,
		}, {
			name: "resaon1",
			index: "resaon1",
			editable: true,
			width: 120,
			sorttype: "float"
		}, {
			name: "note",
			index: "note",
			editable: true,
			width: 120,
			search: false,
			sortable: false
		}, {
			name: "resaon2",
			index: "resaon2",
			editable: true,
			width: 170,
			align: "right",
			sorttype: "float",
			stype: "select"
		}],

		viewrecords: true,
		multiselect: false,
		/*editurl: "/RowEditing",*/
		shrinkToFit: true,
		autoScroll: true
	});
//Result=Failed 的时候改变字体的颜色为红色
  function addCellAttr(rowId, val, rawObject, cm, rdata) {
        if (rawObject.status == 'Failed') {
            return "style='color:red'";
        }
    }
	$("#table_list4").jqGrid("navGrid", "#pager_list4", {
			edit: false,
			add: false,
			del: false,
			search: false
		},

		{
			height: 200,
			reloadAfterSubmit: true
		});

	
	});