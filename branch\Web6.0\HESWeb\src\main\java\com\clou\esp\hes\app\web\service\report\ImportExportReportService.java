package com.clou.esp.hes.app.web.service.report;

import java.util.List;
import java.util.Map;

import com.clou.esp.hes.app.web.model.report.ImportExportReport;
import com.clou.esp.hes.app.web.service.common.CommonService;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;


/**
 * @ClassName: ImportExportReportService
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年10月18日 上午10:15:32
 *
 */
public interface ImportExportReportService extends
		CommonService<ImportExportReport> {
	
	List<ImportExportReport> findImportExportParamList(Map<String, Object> p);
	
	JqGridResponseTo findImportExportReport(JqGridSearchTo jqGridSearchTo);
	JqGridResponseTo findImportExportReportDetail(JqGridSearchTo jqGridSearchTo);
	List<ImportExportReport> findImportExportReportList(JqGridSearchTo jqGridSearchTo);
	List<ImportExportReport> findImportExportReportDetailList(JqGridSearchTo jqGridSearchTo);
}
