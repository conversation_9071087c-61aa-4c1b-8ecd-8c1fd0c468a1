package com.clou.esp.hes.app.web.model.asset;

import java.math.BigDecimal;

import com.clou.esp.hes.app.web.model.common.BaseEntity;


/**
 * @ClassName: AssetTransformer
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年9月20日 上午9:05:23
 *
 */
public class AssetTransformer extends BaseEntity {

	private static final long serialVersionUID = 1L;
	private String sn;
	private String name;
	private String orgId;
	private String orgName;
	private BigDecimal ratedCapacity;
	private String addr;
	/**是否收藏*/
	private Boolean isCollect;


	public Boolean getIsCollect() {
		return isCollect;
	}

	public void setIsCollect(Boolean isCollect) {
		this.isCollect = isCollect;
	}
	
	public String getSn() {
		return sn;
	}
	public void setSn(String sn) {
		this.sn = sn;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getOrgId() {
		return orgId;
	}
	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public BigDecimal getRatedCapacity() {
		return ratedCapacity;
	}
	public void setRatedCapacity(BigDecimal ratedCapacity) {
		this.ratedCapacity = ratedCapacity;
	}
	public String getAddr() {
		return addr;
	}
	public void setAddr(String addr) {
		this.addr = addr;
	}
	
}