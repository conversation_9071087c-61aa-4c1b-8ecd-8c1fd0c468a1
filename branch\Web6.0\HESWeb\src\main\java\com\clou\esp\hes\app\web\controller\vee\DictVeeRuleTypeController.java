/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictVeeRuleType{ } 
 * 
 * 摘    要： dictVeeRuleType
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-12-19 07:31:50
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.vee;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;

import com.clou.esp.hes.app.web.model.vee.DictVeeRuleType;
import com.clou.esp.hes.app.web.service.vee.DictVeeRuleTypeService;

/**
 * <AUTHOR>
 * @时间：2018-12-19 07:31:50
 * @描述：dictVeeRuleType类
 */
@Controller
@RequestMapping("/dictVeeRuleTypeController")
public class DictVeeRuleTypeController extends BaseController{

 	@Resource
    private DictVeeRuleTypeService dictVeeRuleTypeService;

	/**
	 * 跳转到dictVeeRuleType列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/vee/dictVeeRuleTypeList");
    }

	/**
	 * 跳转到dictVeeRuleType新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dictVeeRuleType")
	public ModelAndView dictVeeRuleType(DictVeeRuleType dictVeeRuleType,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dictVeeRuleType.getId())){
			try {
                dictVeeRuleType=dictVeeRuleTypeService.getEntity(dictVeeRuleType.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dictVeeRuleType", dictVeeRuleType);
		}
		return new ModelAndView("/vee/dictVeeRuleType");
	}


	/**
	 * dictVeeRuleType查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=dictVeeRuleTypeService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除dictVeeRuleType信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DictVeeRuleType dictVeeRuleType,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dictVeeRuleTypeService.deleteById(dictVeeRuleType.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存dictVeeRuleType信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DictVeeRuleType dictVeeRuleType,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DictVeeRuleType t=new  DictVeeRuleType();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dictVeeRuleType.getId())){
        	t=dictVeeRuleTypeService.getEntity(dictVeeRuleType.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dictVeeRuleType, t);
				dictVeeRuleTypeService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            dictVeeRuleTypeService.save(dictVeeRuleType);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
	
	
	
}