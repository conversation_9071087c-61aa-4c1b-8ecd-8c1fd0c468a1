package com.clou.esp.hes.app.web.service.impl.data;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.data.DataIntegrationRequestDao;
import com.clou.esp.hes.app.web.model.data.DataIntegrationRequest;
import com.clou.esp.hes.app.web.service.data.DataIntegrationRequestService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dataIntegrationRequestService")
public class DataIntegrationRequestServiceImpl extends
		CommonServiceImpl<DataIntegrationRequest> implements
		DataIntegrationRequestService {
	
	private static final Logger LOG = Logger.getLogger(DataIntegrationRequestServiceImpl.class);
	
	@Resource
	private DataIntegrationRequestDao dataIntegrationRequestDao;
	
	@Autowired
	public void setCommonService() {
		// TODO Auto-generated method stub
		super.setCommonService(dataIntegrationRequestDao);
	}
	
	public DataIntegrationRequestServiceImpl() {
		// TODO Auto-generated constructor stub
	}

//	@Override
//	public Serializable save(DataIntegrationRequest entity) {
//		// TODO Auto-generated method stub
//		return null;
//	}
//
//	@Override
//	public void batchSave(List<DataIntegrationRequest> entitys) {
//		// TODO Auto-generated method stub
//
//	}
//
//	@Override
//	public Long getCount(DataIntegrationRequest entity) {
//		// TODO Auto-generated method stub
//		return null;
//	}
//
//	@Override
//	public Integer delete(DataIntegrationRequest entity) {
//		// TODO Auto-generated method stub
//		return null;
//	}
//
//	@Override
//	public Integer deleteById(String id) {
//		// TODO Auto-generated method stub
//		return null;
//	}
//
//	@Override
//	public void deleteAllEntitie(Collection<DataIntegrationRequest> entities) {
//		// TODO Auto-generated method stub
//
//	}
//
//	@Override
//	public Integer update(DataIntegrationRequest pojo) {
//		// TODO Auto-generated method stub
//		return null;
//	}
//
//	@Override
//	public void saveOrUpdate(DataIntegrationRequest entity) {
//		// TODO Auto-generated method stub
//
//	}
//
//	@Override
//	public void batchUpdate(List<DataIntegrationRequest> entitys) {
//		// TODO Auto-generated method stub
//
//	}
//
//	@Override
//	public void batchSaveOrUpdate(List<DataIntegrationRequest> entitys) {
//		// TODO Auto-generated method stub
//
//	}
//
//	@Override
//	public DataIntegrationRequest getEntity(String id) {
//		// TODO Auto-generated method stub
//		return null;
//	}
//
//	@Override
//	public DataIntegrationRequest get(DataIntegrationRequest entity) {
//		// TODO Auto-generated method stub
//		return null;
//	}
//
//	@Override
//	public List<DataIntegrationRequest> getList(DataIntegrationRequest entity) {
//		// TODO Auto-generated method stub
//		return null;
//	}
//
//	@Override
//	public List<DataIntegrationRequest> getAllList() {
//		// TODO Auto-generated method stub
//		return null;
//	}

//	@Override
//	public JqGridResponseTo getForJqGrid(JqGridSearchTo jqGridSearchTo) {
//		// TODO Auto-generated method stub
//		return (JqGridResponseTo) dataIntegrationRequestDao.getForJqGrid(jqGridSearchTo);
//	}

}
