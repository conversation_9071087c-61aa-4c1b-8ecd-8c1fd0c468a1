package com.clou.esp.hes.app.web.service.asset;

import java.util.List;

import com.clou.esp.hes.app.web.model.asset.AssetCalcObj;
import com.clou.esp.hes.app.web.model.asset.AssetCalcObjMap;
import com.clou.esp.hes.app.web.model.asset.AssetLine;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.service.common.CommonService;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;


/**
 * @ClassName: AssetLineManagement
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年9月3日 下午2:54:16
 *
 */
public interface AssetLineManagementService extends CommonService<AssetLine> {
	/**
	 * 
	 * @Title: findLinesForJqGrid
	 * @Description: 查询线路档案
	 * @param jqGridSearchTo
	 * @return
	 * @return JqGridResponseTo
	 * @throws
	 */
	JqGridResponseTo findLinesForJqGrid(JqGridSearchTo jqGridSearchTo);
	
	/**
	 * 
	 * @Title: findCalObjectsForJqGrid
	 * @Description: 查询计算对象
	 * @param jqGridSearchTo
	 * @return
	 * @return JqGridResponseTo
	 * @throws
	 */
	JqGridResponseTo findCalObjectsForJqGrid(JqGridSearchTo jqGridSearchTo);
	List<AssetCalcObj> getListForCalObj(AssetCalcObj assetCalcObj);
	Integer updateCalcObj(AssetCalcObj pojo);
	Integer saveCalcObj(AssetCalcObj pojo);
	Integer deleteCalcObj(AssetCalcObj entity);
	AssetCalcObj getCalcObj(AssetCalcObj entity);
	
	JqGridResponseTo findCalObjectMapsForJqGrid(JqGridSearchTo jqGridSearchTo);
	Integer updateCalcObjMap(AssetCalcObjMap pojo);
	Integer saveCalcObjMap(AssetCalcObjMap pojo);
	Integer deleteCalcObjMap(AssetCalcObjMap entity);
	AssetCalcObjMap getCalcObjMap(AssetCalcObjMap entity);
	
	// 级联删除
	Integer deleteCalcObjByEntityId(AssetCalcObj entity);
	List<AssetCalcObj> findCalObjectsForList(JqGridSearchTo jqGridSearchTo);
	
	List<AssetMeter> queryMeterSn(AssetMeter meter);
}
