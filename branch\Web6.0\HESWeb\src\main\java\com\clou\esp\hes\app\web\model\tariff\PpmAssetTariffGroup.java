package com.clou.esp.hes.app.web.model.tariff;

import com.clou.esp.hes.app.web.model.common.BaseEntity;



/*********************************************************************************************************
 * @see Copyright© 2019 Shenzhen Clou Electronics CO., LTD.  All rights reserved. 
 * @see 
 * @see File Name:public class PpmAssetTariffGroup{ } 
 * @see 
 * @see Description： ppmAssetTariffGroup
 * @version *******
 * <AUTHOR>
 * @see Create Time：2019-09-17 10:04:36
 * @see Last modification Time：2019-09-17 10:04:36
 * 
*********************************************************************************************************/
public class PpmAssetTariffGroup  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public PpmAssetTariffGroup() {
	}

	/**name*/
	private java.lang.String name;
	/**type*/
	private java.lang.Integer type;
	/**activateTv*/
	private java.util.Date activateTv;
	
	/**descr*/
	private java.lang.String activateTvStr;
	
	/**descr*/
	private java.lang.String descr;

	/**activeTariffCustomerNums*/
	private java.lang.Integer activeTariffCustomerNums;

	/**passiveTariffCustomerNums*/
	private java.lang.Integer passiveTariffCustomerNums;

	/**historyTariffCustomerNums*/
	private java.lang.Integer historyTariffCustomerNums;
	/**tariffSolutionReference*/
	private java.lang.String tariffSolutionReference;
	
	
	public java.lang.String getTariffSolutionReference() {
		return tariffSolutionReference;
	}

	public void setTariffSolutionReference(java.lang.String tariffSolutionReference) {
		this.tariffSolutionReference = tariffSolutionReference;
	}

	public java.lang.String getActivateTvStr() {
		return activateTvStr;
	}

	public void setActivateTvStr(java.lang.String activateTvStr) {
		this.activateTvStr = activateTvStr;
	}

	public java.lang.Integer getActiveTariffCustomerNums() {
		return activeTariffCustomerNums;
	}

	public void setActiveTariffCustomerNums(
			java.lang.Integer activeTariffCustomerNums) {
		this.activeTariffCustomerNums = activeTariffCustomerNums;
	}

	public java.lang.Integer getPassiveTariffCustomerNums() {
		return passiveTariffCustomerNums;
	}

	public void setPassiveTariffCustomerNums(
			java.lang.Integer passiveTariffCustomerNums) {
		this.passiveTariffCustomerNums = passiveTariffCustomerNums;
	}

	public java.lang.Integer getHistoryTariffCustomerNums() {
		return historyTariffCustomerNums;
	}

	public void setHistoryTariffCustomerNums(
			java.lang.Integer historyTariffCustomerNums) {
		this.historyTariffCustomerNums = historyTariffCustomerNums;
	}

	/**
	 * name
	 * @return the value of PPM_ASSET_TARIFF_GROUP.NAME
	 * @mbggenerated 2019-09-17 10:04:36
	 */
	public java.lang.String getName() {
		return name;
	}

	/**
	 * name
	 * @param name the value for PPM_ASSET_TARIFF_GROUP.NAME
	 * @mbggenerated 2019-09-17 10:04:36
	 */
    	public void setName(java.lang.String name) {
		this.name = name;
	}
	
	public java.lang.Integer getType() {
		return type;
	}

	public void setType(java.lang.Integer type) {
		this.type = type;
	}

	/**
	 * activateTv
	 * @return the value of PPM_ASSET_TARIFF_GROUP.ACTIVATE_TV
	 * @mbggenerated 2019-09-17 10:04:36
	 */
	public java.util.Date getActivateTv() {
		return activateTv;
	}

	/**
	 * activateTv
	 * @param activateTv the value for PPM_ASSET_TARIFF_GROUP.ACTIVATE_TV
	 * @mbggenerated 2019-09-17 10:04:36
	 */
    	public void setActivateTv(java.util.Date activateTv) {
		this.activateTv = activateTv;
	}
	/**
	 * descr
	 * @return the value of PPM_ASSET_TARIFF_GROUP.DESCR
	 * @mbggenerated 2019-09-17 10:04:36
	 */
	public java.lang.String getDescr() {
		return descr;
	}

	/**
	 * descr
	 * @param descr the value for PPM_ASSET_TARIFF_GROUP.DESCR
	 * @mbggenerated 2019-09-17 10:04:36
	 */
    	public void setDescr(java.lang.String descr) {
		this.descr = descr;
	}

	public PpmAssetTariffGroup(java.lang.String name 
	,java.lang.Integer type 
	,java.util.Date activateTv 
	,java.lang.String descr ) {
		super();
		this.name = name;
		this.type = type;
		this.activateTv = activateTv;
		this.descr = descr;
	}

}