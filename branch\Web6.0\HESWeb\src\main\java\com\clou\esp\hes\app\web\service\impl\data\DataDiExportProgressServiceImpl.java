/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataDiExportProgress{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-04-04 06:12:00
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.data;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.data.DataDiExportProgressDao;
import com.clou.esp.hes.app.web.model.data.DataDiExportProgress;
import com.clou.esp.hes.app.web.service.data.DataDiExportProgressService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dataDiExportProgressService")
public class DataDiExportProgressServiceImpl  extends CommonServiceImpl<DataDiExportProgress>  implements DataDiExportProgressService {

	@Resource
	private DataDiExportProgressDao dataDiExportProgressDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dataDiExportProgressDao);
    }
	@SuppressWarnings("rawtypes")
	public DataDiExportProgressServiceImpl() {}
	
	
}