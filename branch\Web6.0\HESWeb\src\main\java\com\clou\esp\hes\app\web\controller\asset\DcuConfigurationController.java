/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetCommunicator{ }
 *
 * 摘    要： GPRS Module
 DCU
 Gateway
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-06 08:04:54
 * 最后修改时间：
 *
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.asset;


import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.clou.esp.hes.app.web.core.util.*;
import com.clou.esp.hes.app.web.model.asset.DcuConfigModel;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.model.asset.AssetMeterDto;
import com.clou.esp.hes.app.web.model.asset.ConnOrDisconn;
import com.clou.esp.hes.app.web.model.asset.DcuConfiguration;
import com.clou.esp.hes.app.web.model.dict.DictProfile;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.asset.AssetDcuConfigurationService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.dict.DictProfileService;
import com.clou.esp.hes.app.web.service.system.SysServiceAttributeService;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.redis.JedisUtils;

import ch.iec.tc57._2011.meterdefineconfig.MeterDefineConfigPort;
import ch.iec.tc57._2011.meterdefineconfig_.Arrays;
import ch.iec.tc57._2011.meterdefineconfig_.Meter;
import ch.iec.tc57._2011.meterdefineconfig_.MeterDefineConfig;
import ch.iec.tc57._2011.meterdefineconfig_.MeterDefineConfig.ReadingType;
import ch.iec.tc57._2011.meterdefineconfig_.Structures;
import ch.iec.tc57._2011.meterdefineconfig_.Values;
import ch.iec.tc57._2011.meterdefineconfigmessage.MeterDefineConfigPayloadType;
import ch.iec.tc57._2011.meterdefineconfigmessage.MeterDefineConfigRequestMessageType;
import ch.iec.tc57._2011.meterdefineconfigmessage.MeterDefineConfigResponseMessageType;
import ch.iec.tc57._2011.schema.message.HeaderType;
import ch.iec.tc57._2011.schema.message.UserType;
import clouesp.hes.core.uci.soap.custom.webservice.WebserviceBuilder;
import clouesp.hes.core.uci.soap.custom.webservice.common.DcuDlmsIdEnum;
import clouesp.hes.core.uci.soap.custom.webservice.common.ResetType;
import clouesp.hes.core.uci.soap.custom.webservice.common.ResetTypeDlms;

/**
 *
 * @ClassName: DcuConfigurationController
 * @Description: DCU 参数设置
 * <AUTHOR>
 * @date 2018年7月10日 上午11:02:00
 *
 */
@Controller
@RequestMapping("/dcuConfigurationController")
public class DcuConfigurationController extends BaseController{

	final static String TIME_FLAG =ResourceUtil.getSessionattachmenttitle("local.time.flag");

	@Resource
	private AssetDcuConfigurationService assetDcuConfigurationService;
	@Resource
	private DataUserLogService 			 dataUserLogService;
	@Resource
	private SysServiceAttributeService   sysServiceAttributeService;
	@Resource
	private AssetMeterService 			 assetMeterService;
	@Resource
	private DictProfileService 			 dictProfileService;


	/**
	 * to selfDefineToUci
	 *
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "selfDefineToUci")
	public ModelAndView selfDefineToUci(HttpServletRequest request,
										Model model) {
		return new ModelAndView("/asset/selfDefineToUci");
	}


	@RequestMapping(value = "validateInputBuffer")
	@ResponseBody
	public AjaxJson validateInputBuffer(String sn, String communicatorId,String requestInfo, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		String requestInfoValidate = requestInfo.replace(" ", "");
		if(!ValidateFormat.validateInputBueffer(requestInfoValidate)){
			j.setSuccess(false);
			j.setErrorMsg("Input Buffer Error,Please Revise .");
			return j;
		}
		return j;
	}

	@RequestMapping(value = "sendInputBuffer")
	@ResponseBody
	public AjaxJson sendInputBuffer(String sn, String communicatorId,String requestInfo, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();

		Map<String, Object> attributes = new HashMap<>();

		attributes.put("requestTime", DateUtils.date2Str(new Date(), DateTimeFormatterUtil.getSimpleDateFormat_HHmmss(TIME_FLAG)));

		j.setAttributes(attributes);

		ConnOrDisconn entity = new ConnOrDisconn();
		entity.setStatus("Proccessing");
		entity.setCommSN(sn);
		entity.setCommand("1");
		entity.setRequestTime(attributes.get("requestTime").toString());

		j.setObj(entity);

		SysUser su=TokenManager.getToken();
		//String commSn = request.getParameter("commSn");
		List<String> ids = new ArrayList<>();
		ids.add(communicatorId);
		List<String> meters = new ArrayList<>();
		meters.add(sn);
		//	List<String> pns = Tool.Instance.createMeterUploadSetPns(requestInfo);

		Arrays uploadSetArray = Tool.Instance.createMeterUploadSet(requestInfo);

		String userId = TokenManager.getToken().getId();
		String verb = "create";
		String noun = "MeterConfig";
		//String basePath = "http://j25676u579.zicp.vip:29727/HESWeb";
		//http://***********:8080/HESWeb
		String basePath = ResourceUtil.getUciBasePath(request);
		MeterDefineConfigPort defineConfigPort = (MeterDefineConfigPort)UciInterfaceUtil.getInterface("MeterDefineConfigPort", MeterDefineConfigPort.class,sysServiceAttributeService);
		try {
			dataUserLogService.insertDataUserLog(su.getId(), "Send Message", "Send Message","[Request Info:"+requestInfo+"];[SN:"+sn+"];[Data Channel=***************]");
		}catch(Exception ex) {
			ex.printStackTrace();
		}
		// 电表档案下发
		//WebserviceBuilder.buildMeterDefineConfigWritingRequest(ids, uploadSetArray, commSn, "**********", userId, verb, noun, s, defineConfigPort);
		WebserviceBuilder.buildMeterDefineConfigWritingMoreRequest(communicatorId,ids,meters, uploadSetArray, sn, "***************", userId, verb, noun, basePath, defineConfigPort,"100",null);


		return j;
	}

	@RequestMapping(value  = "toDcuConfiguration")
	public ModelAndView toMeterConfiguration(HttpServletRequest request, Model model) {
		return new ModelAndView("/asset/dcuConfiguration");
	}

	@RequestMapping(value  = "toDcuDlmsConfiguration")
	public ModelAndView toMeterConfigurationDlms(HttpServletRequest request, Model model) {
		String baudRateReplace   = "0:300,";
		baudRateReplace += "1:600,";
		baudRateReplace += "2:1200,";
		baudRateReplace += "3:2400,";
		baudRateReplace += "4:4800,";
		baudRateReplace += "5:9600,";
		baudRateReplace += "6:19200,";
		baudRateReplace += "7:38400,";
		baudRateReplace += "8:57600,";
		baudRateReplace += "9:115200";
		String comTypeReplace   = "1:RS485-1,";
		comTypeReplace += "2:RS485-2,";
		comTypeReplace += "30:RF,";
		comTypeReplace += "31:PLC";

		DictProfile dictProfile = new DictProfile();

		//获取所有的profile类型
		dictProfile.setProtocolId(ResourceUtil.getSessionattachmenttitle("protocol.id"));
		dictProfile.setProfileType("5");
		List<DictProfile> dictProfilelist = dictProfileService.getList(dictProfile);

		if(dictProfilelist != null && dictProfilelist.size() > 0){
			for(DictProfile dictProfile1 : dictProfilelist){
				if(dictProfile1.getProtocolCode() != null){
					if(dictProfile1.getProtocolCode().split("#").length == 3){
						dictProfile1.setProtocolCode(dictProfile1.getProtocolCode().split("#")[1]);
					}
				}
			}
		}

		model.addAttribute("dictProfilelist",dictProfilelist);
		model.addAttribute("baudRateReplace",baudRateReplace);
		model.addAttribute("comTypeReplace",comTypeReplace);
		model.addAttribute("dmlsDefaultDate",DateUtils.date2Str(new Date(), DateTimeFormatterUtil.getSimpleDateFormat_HHmmss(TIME_FLAG)));
		return new ModelAndView("/asset/dcuDlmsConfiguration");
	}


	@RequestMapping(value  = "toDcuModuleConfiguration")
	public ModelAndView toMeterConfigurationModule(HttpServletRequest request, Model model) {
		String baudRateReplace   = "0:300,";
		baudRateReplace += "1:600,";
		baudRateReplace += "2:1200,";
		baudRateReplace += "3:2400,";
		baudRateReplace += "4:4800,";
		baudRateReplace += "5:9600,";
		baudRateReplace += "6:19200,";
		baudRateReplace += "7:38400,";
		baudRateReplace += "8:57600,";
		baudRateReplace += "9:115200";
		String comTypeReplace   = "1:RS485-1,";
		comTypeReplace += "2:RS485-2,";
		comTypeReplace += "30:RF,";
		comTypeReplace += "31:PLC";

		model.addAttribute("baudRateReplace",baudRateReplace);
		model.addAttribute("comTypeReplace",comTypeReplace);
		model.addAttribute("dmlsDefaultDate",DateUtils.date2Str(new Date(), DateTimeFormatterUtil.getSimpleDateFormat_HHmmss(TIME_FLAG)));
		return new ModelAndView("/asset/dcuModuleConfiguration");
	}

	@RequestMapping(value  = "toDcuConfiguration300")
	public ModelAndView toMeterConfiguration300(HttpServletRequest request, Model model) {
		String baudRateReplace   = "0:300,";
		baudRateReplace += "1:600,";
		baudRateReplace += "2:1200,";
		baudRateReplace += "3:2400,";
		baudRateReplace += "4:4800,";
		baudRateReplace += "5:9600,";
		baudRateReplace += "6:19200,";
		baudRateReplace += "7:38400,";
		baudRateReplace += "8:57600,";
		baudRateReplace += "9:115200";
		String comTypeReplace   = "1:RS485-1,";
		comTypeReplace += "2:RS485-2,";
		comTypeReplace += "30:RF,";
		comTypeReplace += "31:PLC";

		model.addAttribute("baudRateReplace",baudRateReplace);
		model.addAttribute("comTypeReplace",comTypeReplace);
		model.addAttribute("dmlsDefaultDate",DateUtils.date2Str(new Date(), DateTimeFormatterUtil.getSimpleDateFormat_HHmmss(TIME_FLAG)));
		return new ModelAndView("/asset/dcuConfiguration300");
	}

	/**
	 *
	 * @Title: datagrid
	 * @Description: 分页查询电表信息(传入communicatorId)
	 * @param jqGridSearchTo
	 * @param request
	 * @return
	 * @return JqGridResponseTo
	 * @throws
	 */
	@RequestMapping(value = "datagrid")
	@ResponseBody
	public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
		JqGridResponseTo response = new JqGridResponseTo();
		TagUtil.setFieldValue(jqGridSearchTo, request);		//取传递的参数到JqGridSearchTo
		String communicatorId = (String) request.getParameter("communicatorId");
		if(StringUtil.isEmpty(communicatorId)){
			return response;
		}

		jqGridSearchTo.put("communicatorId", communicatorId);
		response = assetDcuConfigurationService.findMetersForJqGrid(jqGridSearchTo);

		return response;
	}


	/**
	 *
	 * @Title: datagrid300
	 * @Description: 分页查询电表信息(传入communicatorId)
	 * @param jqGridSearchTo
	 * @param request
	 * @return
	 * @return JqGridResponseTo
	 * @throws
	 */
	@RequestMapping(value = "datagrid300")
	@ResponseBody
	public JqGridResponseTo datagrid300(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
		JqGridResponseTo response = new JqGridResponseTo();
		TagUtil.setFieldValue(jqGridSearchTo, request);		//取传递的参数到JqGridSearchTo
		String communicatorId = (String) request.getParameter("communicatorId");
		if(StringUtil.isEmpty(communicatorId)){
			return response;
		}

		jqGridSearchTo.put("communicatorId", communicatorId);
		response = assetDcuConfigurationService.findMetersForJqGrid300(jqGridSearchTo);

		return response;

	}

	/**
	 *
	 * @Title: datagrid
	 * @Description: 分页查询电表信息(传入communicatorId)
	 * @param jqGridSearchTo
	 * @param request
	 * @return
	 * @return JqGridResponseTo
	 * @throws
	 */
	@RequestMapping(value = "datagridImportant")
	@ResponseBody
	public JqGridResponseTo datagridImportant(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
		JqGridResponseTo response = new JqGridResponseTo();
		TagUtil.setFieldValue(jqGridSearchTo, request);		//取传递的参数到JqGridSearchTo
		String communicatorId = (String) request.getParameter("communicatorId");
		if(StringUtil.isEmpty(communicatorId)){
			return response;
		}

		jqGridSearchTo.put("communicatorId", communicatorId);
		response = assetDcuConfigurationService.findMetersForJqGrid(jqGridSearchTo);

		return response;
	}


	/*@RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(BindingResult bindingResult,HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		return j;
	}*/

	/**
	 *
	 * @Title: ajaxGetDcuMeterParameters
	 * @Description: 集中器读取参数 dlms
	 * @param commSn
	 * @param commId
	 * @return
	 * @return AjaxJson
	 * @throws
	 */
	@RequestMapping(value = "ajaxGetDcuMeterParametersDlms")
	@ResponseBody
	public AjaxJson ajaxGetDcuMeterParametersDlms(String commId,String commSn,boolean newMeterFlag, HttpServletRequest request) {
		AjaxJson result = new AjaxJson();
//		String[] params = request.getParameterValues("params[]");
//		List<String> ids = Tool.Instance.createMeterUploadSetIds(params);
//		List<String> meters = Tool.Instance.createMeterUploadSetMeters(params);
//		//pns 即海外的sns
//		List<String> pns = Tool.Instance.createMeterUploadSetSnsDlms(params);
//
//		Arrays uploadSetArray = Tool.Instance.createMeterUploadSetSnsDlmsRead(params);

		String userId = TokenManager.getToken().getId();
		String verb = "get";
		String noun = "MeterConfig";
		//"http://linan1607.f3322.net:20000/HESWeb"
		//String basePath = "http://linan1607.f3322.net:20000/HESWeb"	//URL地址

		//String basePath = "http://2481h646b9.wicp.vip/HESWeb";

		String basePath = ResourceUtil.getUciBasePath(request);
		MeterDefineConfigPort defineConfigPort = (MeterDefineConfigPort)UciInterfaceUtil
				.getInterface("MeterDefineConfigPort", MeterDefineConfigPort.class,sysServiceAttributeService);

		// 电表档案下发
		//WebserviceBuilder.buildMeterDefineConfigWritingRequest(ids, uploadSetArray, commSn, "**********", userId, verb, noun, basePath, defineConfigPort);
		if(newMeterFlag){
			WebserviceBuilder.buildMeterDefineConfigReadingMoreRequest(commId, null, null, null, commSn, DcuDlmsIdEnum.AddNewMeterList.getDataItemId(), userId, verb, noun, basePath, defineConfigPort,"100", null);
		}else{
			WebserviceBuilder.buildMeterDefineConfigReadingMoreRequest(commId, null, null,  null, commSn, DcuDlmsIdEnum.ReadMeterList.getDataItemId(), userId, verb, noun, basePath, defineConfigPort,"100", null);
		}

		return result;
	}


	/**
	 *
	 * @Title: ajaxGetDcuMeterParameters 376
	 * @Description: 集中器读取参数
	 * @param commSn
	 * @param commId
	 * @return
	 * @return AjaxJson
	 * @throws
	 */
	@RequestMapping(value = "ajaxGetDcuMeterParameters")
	@ResponseBody
	public AjaxJson ajaxGetDcuMeterParameters(String commId,String commSn,HttpServletRequest request) {
		AjaxJson result = new AjaxJson();
		String[] params = request.getParameterValues("params[]");
		List<String> ids = Tool.Instance.createMeterUploadSetIds(params);
		List<String> meters = Tool.Instance.createMeterUploadSetMeters(params);
		List<String> pns = Tool.Instance.createMeterUploadSetPns(params);

		Arrays uploadSetArray = Tool.Instance.createMeterUploadSetPnsRead(params);

		String userId = TokenManager.getToken().getId();
		String verb = "get";
		String noun = "MeterConfig";
		//String basePath = "http://linan1607.f3322.net:20000/HESWeb"	//URL地址
		String basePath = ResourceUtil.getUciBasePath(request);
		MeterDefineConfigPort defineConfigPort = (MeterDefineConfigPort)UciInterfaceUtil
				.getInterface("MeterDefineConfigPort", MeterDefineConfigPort.class,sysServiceAttributeService);

		// 电表档案下发
		//WebserviceBuilder.buildMeterDefineConfigWritingRequest(ids, uploadSetArray, commSn, "**********", userId, verb, noun, basePath, defineConfigPort);
		WebserviceBuilder.buildMeterDefineConfigReadingMoreRequest(commId, ids,meters, uploadSetArray, commSn, "***********", userId, verb, noun, basePath, defineConfigPort,"200",pns);

		return result;
	}


	/**
	 *
	 * @Title: ajaxGetDcuMeterParameters 376
	 * @Description: 集中器读取参数
	 * @param commSn
	 * @param commId
	 * @return
	 * @return AjaxJson
	 * @throws
	 */
	@RequestMapping(value = "ajaxGetDcuMeterParameters300")
	@ResponseBody
	public AjaxJson ajaxGetDcuMeterParameters300(String commId,String commSn,HttpServletRequest request) {
		AjaxJson result = new AjaxJson();
		String[] params = request.getParameterValues("params[]");
		List<String> ids = new ArrayList<String>();
		List<String> meters = new ArrayList<String>();
		List<String> pns = Tool.Instance.getMeterUploadSetPns300(params);
		Arrays uploadSetArray = new Arrays();
		//	Arrays uploadSetArray = Tool.Instance.createMeterUploadSetPnsRead300(params);

		String userId = TokenManager.getToken().getId();
		String verb = "get";
		String noun = "MeterConfig";
		//String basePath = "http://linan1607.f3322.net:20000/HESWeb"	//URL地址
		String basePath =  ResourceUtil.getUciBasePath(request);
		MeterDefineConfigPort defineConfigPort = (MeterDefineConfigPort)UciInterfaceUtil
				.getInterface("MeterDefineConfigPort", MeterDefineConfigPort.class,sysServiceAttributeService);

		// 电表档案下发
		//WebserviceBuilder.buildMeterDefineConfigWritingRequest(ids, uploadSetArray, commSn, "**********", userId, verb, noun, basePath, defineConfigPort);
		WebserviceBuilder.buildMeterDefineConfigReadingMoreRequest(commId, ids,meters, uploadSetArray, commSn, "300.E0.80.00.0F", userId, verb, noun, basePath, defineConfigPort,"300",pns);

		return result;
	}


	/**
	 *
	 * @Title: ajaxSetMeterParameters dlms
	 * @Description: 电表档案下发
	 * @param request
	 * @param response
	 * @return void
	 * @throws
	 */
	@RequestMapping(value = "ajaxSetDcuMeterParametersDlms")
	@ResponseBody
	public AjaxJson ajaxSetDcuMeterParametersDlms(String commId,String commSn,boolean newMeterFlag,HttpServletRequest request) {
		SysUser su=TokenManager.getToken();
		AjaxJson result = new AjaxJson();
		String[] params = request.getParameterValues("params[]");
		//String commSn = request.getParameter("commSn");
		List<String> ids = Tool.Instance.createMeterUploadSetIds(params);
		List<String> meters = Tool.Instance.createMeterUploadSetMeters(params);
		//List<String> pns = Tool.Instance.createMeterUploadSetPns(params);
		//todo edison 是否转了uci所需要的码
		Arrays uploadSetArray = Tool.Instance.createMeterUploadDlmsSet(params,newMeterFlag);

		String userId = TokenManager.getToken().getId();
		String verb = "create";
		String noun = "MeterConfig";
		//String basePath = "http://linan1607.f3322.net:20000/HESWeb"	//URL地址
		//http://***********:8080/HESWeb
		String basePath =  ResourceUtil.getUciBasePath(request);
		MeterDefineConfigPort defineConfigPort = (MeterDefineConfigPort)UciInterfaceUtil.getInterface("MeterDefineConfigPort", MeterDefineConfigPort.class,sysServiceAttributeService);

		try {
			String meterId=ids.get(0);
			AssetMeterDto dto = this.assetMeterService.getMeterDtoInfo(meterId);
			dataUserLogService.insertDataUserLog(su.getId(), "DCU Configuration", "Set Parameter","[Set Parameter:"+dto.getLogContent()+",Meters=("+StringUtils.join(meters.toArray(), ",")+");[Data Channel="+DcuDlmsIdEnum.AddMeterList.getDataItemId()+"]");
		}catch(Exception ex) {
			ex.printStackTrace();
		}
//		dataUserLogService.insertDataUserLog(su.getId(), "DCU Configuration", "Set Parameter","Set Parameter(CommSN="+ commSn+",Meters=["+StringUtils.join(meters.toArray(), ",")+"],Data Channel="+DcuDlmsIdEnum.AddMeterList.getDataItemId()+")");
		// 电表档案下发
		//WebserviceBuilder.buildMeterDefineConfigWritingRequest(ids, uploadSetArray, commSn, "**********", userId, verb, noun, basePath, defineConfigPort);
		if(newMeterFlag){
			WebserviceBuilder.buildMeterDefineConfigWritingMoreRequest(commId,ids,meters, uploadSetArray, commSn, DcuDlmsIdEnum.AddNewMeterList.getDataItemId(), userId, verb, noun, basePath, defineConfigPort,"100",null);
		}else{
			WebserviceBuilder.buildMeterDefineConfigWritingMoreRequest(commId,ids,meters, uploadSetArray, commSn, DcuDlmsIdEnum.AddMeterList.getDataItemId(), userId, verb, noun, basePath, defineConfigPort,"100",null);
		}

		return result;
	}


	/**
	 *
	 * @Title: ajaxSetMeterParameters dlms
	 * @Description: 电表档案下发
	 * @param request
	 * @param response
	 * @return void
	 * @throws
	 */
	@RequestMapping(value = "ajaxDeleteSetDcuMeterParametersDlms")
	@ResponseBody
	public AjaxJson ajaxDeleteSetDcuMeterParametersDlms(HttpServletRequest request,Model model) {
		SysUser su=TokenManager.getToken();
		AjaxJson result = new AjaxJson();
		String logicName = request.getParameter("logicalName");
		List<String> ids = new ArrayList<>();
		ids.add(logicName);
		String dcuSn = request.getParameter("dcuSn");
		List<String> meters = new ArrayList<>();
		meters.add(logicName);
		Arrays uploadSetArray = Tool.Instance.deleteMeterUploadDlmsSet(logicName);

		String userId = TokenManager.getToken().getId();
		String verb = "create";
		String noun = "MeterConfig";
		//String basePath = "http://linan1607.f3322.net:20000/HESWeb"	//URL地址
		//http://***********:8080/HESWeb
		String basePath = ResourceUtil.getUciBasePath(request);
		MeterDefineConfigPort defineConfigPort = (MeterDefineConfigPort)UciInterfaceUtil.getInterface("MeterDefineConfigPort", MeterDefineConfigPort.class,sysServiceAttributeService);

		try {
			dataUserLogService.insertDataUserLog(su.getId(), "DCU Configuration", "Delete Parameter","[Delete Parameter:"+logicName+",Meters=("+logicName+");[Data Channel="+DcuDlmsIdEnum.DelMeterList.getDataItemId()+"]");
		}catch(Exception ex) {
			ex.printStackTrace();
		}

		WebserviceBuilder.buildMeterDefineConfigWritingMoreRequest(logicName,ids,meters, uploadSetArray, dcuSn, DcuDlmsIdEnum.DelMeterList.getDataItemId(), userId, verb, noun, basePath, defineConfigPort,"100",null);


		return result;
	}

	/**
	 *
	 * @Title: ajaxSetMeterParameters dlms
	 * @Description: 电表档案下发
	 * @param request
	 * @param response
	 * @return void
	 * @throws
	 */
	@RequestMapping(value = "ajaxSetDcuMeterParameters300")
	@ResponseBody
	public AjaxJson ajaxSetDcuMeterParameters300(String commId,String commSn,HttpServletRequest request) {
		SysUser su=TokenManager.getToken();
		AjaxJson result = new AjaxJson();
		String[] params = request.getParameterValues("params[]");
		//String commSn = request.getParameter("commSn");
		List<String> ids = Tool.Instance.createMeterUploadSetIds(params);
		List<String> meters = Tool.Instance.createMeterUploadSetMeters(params);
		List<String> pns = Tool.Instance.createMeterUploadSetPns300(params);
		//todo edison 是否转了uci所需要的码
		Arrays uploadSetArray = Tool.Instance.createMeterUploadSet300(params);

		String userId = TokenManager.getToken().getId();
		String verb = "create";
		String noun = "MeterConfig";
		//String basePath = "http://linan1607.f3322.net:20000/HESWeb"	//URL地址
		//http://***********:8080/HESWeb
		String basePath =  ResourceUtil.getUciBasePath(request);
		MeterDefineConfigPort defineConfigPort = (MeterDefineConfigPort)UciInterfaceUtil.getInterface("MeterDefineConfigPort", MeterDefineConfigPort.class,sysServiceAttributeService);
		try {
			String meterId=ids.get(0);
			AssetMeterDto dto = this.assetMeterService.getMeterDtoInfo(meterId);
			dataUserLogService.insertDataUserLog(su.getId(), "DCU Configuration", "Set Parameter","[Set Parameter:"+dto.getLogContent()+",Meters=("+StringUtils.join(meters.toArray(), ",")+");[Data Channel=300.E0.80.00.0F]");
		}catch(Exception ex) {
			ex.printStackTrace();
		}
//		dataUserLogService.insertDataUserLog(su.getId(), "DCU Configuration", "Set Parameter","Set Parameter(CommSN="+ commSn+",Meters=["+StringUtils.join(meters.toArray(), ",")+"],Data Channel="+"300.E0.80.00.0F"+")");
		// 电表档案下发
		//WebserviceBuilder.buildMeterDefineConfigWritingRequest(ids, uploadSetArray, commSn, "**********", userId, verb, noun, basePath, defineConfigPort);
		WebserviceBuilder.buildMeterDefineConfigWritingMoreRequest(commId,ids,meters, uploadSetArray, commSn, "300.E0.80.00.0F", userId, verb, noun, basePath, defineConfigPort,"300",pns);

		return result;
	}

	/**
	 *
	 * @Title: ajaxSetMeterParameters 376
	 * @Description: 电表档案下发
	 * @param request
	 * @param response
	 * @return void
	 * @throws
	 */
	@RequestMapping(value = "ajaxSetDcuMeterParameters")
	@ResponseBody
	public AjaxJson ajaxSetDcuMeterParameters(String commId,String commSn,HttpServletRequest request) {
		SysUser su=TokenManager.getToken();
		AjaxJson result = new AjaxJson();
		String[] params = request.getParameterValues("params[]");
		//String commSn = request.getParameter("commSn");
		List<String> ids = Tool.Instance.createMeterUploadSetIds(params);
		List<String> meters = Tool.Instance.createMeterUploadSetMeters(params);
		List<String> pns = Tool.Instance.createMeterUploadSetPns(params);

		Arrays uploadSetArray = Tool.Instance.createMeterUploadSet(params);

		String userId = TokenManager.getToken().getId();
		String verb = "create";
		String noun = "MeterConfig";
		//String basePath = "http://linan1607.f3322.net:20000/HESWeb"	//URL地址
		//http://***********:8080/HESWeb
		String basePath =  ResourceUtil.getUciBasePath(request);
		MeterDefineConfigPort defineConfigPort = (MeterDefineConfigPort)UciInterfaceUtil.getInterface("MeterDefineConfigPort", MeterDefineConfigPort.class,sysServiceAttributeService);
		try {
			String meterId=ids.get(0);
			AssetMeterDto dto = this.assetMeterService.getMeterDtoInfo(meterId);
			dataUserLogService.insertDataUserLog(su.getId(), "DCU Configuration", "Set Parameter","[Set Parameter:"+dto.getLogContent()+",Meters=("+StringUtils.join(meters.toArray(), ",")+");[Data Channel=**********]");
		}catch(Exception ex) {
			ex.printStackTrace();
		}
		// 电表档案下发
		//WebserviceBuilder.buildMeterDefineConfigWritingRequest(ids, uploadSetArray, commSn, "**********", userId, verb, noun, s, defineConfigPort);
		WebserviceBuilder.buildMeterDefineConfigWritingMoreRequest(commId,ids,meters, uploadSetArray, commSn, "**********", userId, verb, noun, basePath, defineConfigPort,"200",null);

		return result;
	}

	/**
	 *
	 * @Title: queryComType
	 * @Description: 查询通讯方式
	 * @param comType
	 * @param request
	 * @return
	 * @return AjaxJson
	 * @throws
	 */
	@RequestMapping(value = "queryComType")
	@ResponseBody
	public AjaxJson queryComType(String comType,HttpServletRequest request){
		AjaxJson result = new AjaxJson();
		if(StringUtil.isEmpty(comType)){
			result.setSuccess(false);
			return result;
		}

		DcuConfiguration config = assetDcuConfigurationService.queryComType(comType);
		if(null == config){
			result.setSuccess(false);
			return result;
		}

		result.setObj(config);

		return result;
	}


	/**
	 *
	 * @Title: ajaxGetOtherParameters Dlms
	 * @Description: 集中器读取参数
	 * @param commSn
	 * @param commId
	 * @return
	 * @return AjaxJson
	 * @throws
	 */
	@RequestMapping(value = "ajaxGetOtherParametersDlms")
	@ResponseBody
	public AjaxJson ajaxGetOtherParametersDlms(String commSn,String commId,int sendType,HttpServletRequest request) {
		AjaxJson result = new AjaxJson();

		String userId = TokenManager.getToken().getId();
		String verb = "get";
		String noun = "MeterConfig";

		String basePath =  ResourceUtil.getUciBasePath(request);
		MeterDefineConfigPort port = (MeterDefineConfigPort)UciInterfaceUtil
				.getInterface("MeterDefineConfigPort", MeterDefineConfigPort.class,sysServiceAttributeService);
		//KeyMeterParam("********","Key Meter parameter RW"), --
		// 读取集中器 ip / port


		if(sendType == 1){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#1", commSn, "********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 2){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#2", commSn, "********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 3){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#3", commSn, "********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 4){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#4", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 6){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#6", commSn, "********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 7){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#7", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 8){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#8", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 9){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#9", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 10){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#10", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 11){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#11", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 12){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#12", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 13){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#13", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 14){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#14", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 15){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#15", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 16){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#16", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 17){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#17", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 18){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#18", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 19){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#19", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 20){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#20", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 21){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#21", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 22){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#22", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 23){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#23", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 24){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#24", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 25){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#25", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 26){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#26", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 27){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#27", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 28){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#28", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 29){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#29", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 30){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#30", commSn, "********0", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 31){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#31", commSn, "********1", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 32){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#32", commSn, "********3", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 33){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#33", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 34){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#34", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 35){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#35", commSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 36){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#36", commSn, "********1", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 37){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#37", commSn, "********8", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 38){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#38", commSn, "********9", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 39){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+
					"#39", commSn, "********9", userId, verb, noun, basePath, port,"100");
		}

		//TODO topo begin
		else if(sendType == 100){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#100", commSn, "********2", userId, verb, noun, basePath, port,"100");
		}
		//top end

		return result;
	}



	/**
	 *
	 * @Title: ajaxGetOtherParameters Dlms
	 * @Description: 集中器读取参数
	 * @param meterSn
	 * @param meterId
	 * @return
	 * @return AjaxJson
	 * @throws
	 */
	@RequestMapping(value = "ajaxGetOtherParametersModule")
	@ResponseBody
	public AjaxJson ajaxGetOtherParametersModule(String meterSn,String meterId,int sendType,HttpServletRequest request) {
		AjaxJson result = new AjaxJson();

		String userId = TokenManager.getToken().getId();
		String verb = "get";
		String noun = "MeterConfig";
		//String basePath = "http://j25676u579.zicp.vip:29727/HESWeb"	 ;//URL地址
		String basePath = ResourceUtil.getUciBasePath(request);
		MeterDefineConfigPort port = (MeterDefineConfigPort)UciInterfaceUtil
				.getInterface("MeterDefineConfigPort", MeterDefineConfigPort.class,sysServiceAttributeService);

		//hes ip and port *********
		//ftp ip and port  *********
		//apn  *********
		//user password  *********
		//connectMode *********
		//answerMode *********
		//pincode *********
		//inactivityTimeout *********
		//noNetworkCommunicationTimeoutReset *********

		if(sendType == 1){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(meterId+"#1", meterSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 2){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(meterId+"#2", meterSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 3){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(meterId+"#3", meterSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 4){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(meterId+"#4", meterSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 5){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(meterId+"#5", meterSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 6){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(meterId+"#6", meterSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 7){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(meterId+"#7", meterSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 8){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(meterId+"#8", meterSn, "*********", userId, verb, noun, basePath, port,"100");
		}else if(sendType == 9){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(meterId+"#9", meterSn, "*********", userId, verb, noun, basePath, port,"100");
		}

		return result;
	}


	/**
	 *
	 * @Title: ajaxGetOtherParameters 376
	 * @Description: 集中器读取参数
	 * @param commSn
	 * @param commId
	 * @return
	 * @return AjaxJson
	 * @throws
	 */
	@RequestMapping(value = "ajaxGetOtherParameters")
	@ResponseBody
	public AjaxJson ajaxGetOtherParameters(String commSn,String commId,int sendType,HttpServletRequest request) {
		AjaxJson result = new AjaxJson();

		String userId = TokenManager.getToken().getId();
		String verb = "get";
		String noun = "MeterConfig";
		//String basePath = "http://linan1607.f3322.net:20000/HESWeb"	//URL地址
		String basePath = ResourceUtil.getUciBasePath(request);
		MeterDefineConfigPort port = (MeterDefineConfigPort)UciInterfaceUtil
				.getInterface("MeterDefineConfigPort", MeterDefineConfigPort.class,sysServiceAttributeService);

		// 读取集中器 ip / port
		if(sendType == 1){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#1", commSn, "**********", userId, verb, noun, basePath, port,"200");
		}else if(sendType == 3){
			WebserviceBuilder.buildMeterDefineConfigReadingRequest(commId+"#2", commSn, "**********5", userId, verb, noun, basePath, port,"200");
		}

		return result;
	}


	/**
	 * @throws InterruptedException
	 *
	 * @Title: ajaxSetOtherParameters Dlms
	 * @Description: 参数设置
	 * @param commSn 集中器SN
	 * @param ip
	 * @param port
	 * @param ipBak 备用IP
	 * @param portBak 备用端口
	 * @param apn
	 * @param resetType 重置类型
	 * @return
	 * @return AjaxJson
	 * @throws
	 */
	@RequestMapping(value = "ajaxSetOtherParametersDlms")
	@ResponseBody
	public AjaxJson ajaxSetOtherParametersDlms(String commSn,String commId,String ip,int port,String ipBak,int portBak,String apn,
											   String userName,String password,String  dialedNumber,String clock,String versionInfo,String encryptionMode,String encryptionKey
			,String heartbeatCycle,String energyProfileReadingCycle,String plcChannelSendTimes,String plcChannelTimeout
			,boolean isChecked,boolean existKeyUser,String meterSn
			,int resetType,int sendType
			,String dormancyTime,String terminalIp,String subnetMask,String gatewayMask,String eventConfiguration,
											   String broadcastTimeParameter,
											   String broadcastTimeParameter2,String timePeriod,
											   String csMode,String serverPort,
											   boolean isCheckedBroadcast,boolean isCheckedWan,
											   boolean isCheckedConnected,
											   String ipAddressVpn,
											   String vpnPort,
											   String vpnUserName,
											   String vpnPassword,
											   boolean isSwitched,
											   String txtStartedTime32,
											   String txtDelayTime32,
											   boolean isActiveCollect,
											   boolean isPassiveCollect,String txtDayCurves,String txtMonthCurves,String txtLoadCurves,String timeZone,String batteryTime,
											   HttpServletRequest request) throws InterruptedException{
		AjaxJson result = new AjaxJson();

		String [] params = request.getParameterValues("params[]");
		String [] serviceIds = request.getParameterValues("serviceIds[]");

		SysUser su = TokenManager.getToken();
		String userId = su.getId();

		String verb = "create";
		String noun = "MeterConfig";
		//String basePath = "http://linan1607.f3322.net:20000/HESWeb"	//URL地址
		String basePath = ResourceUtil.getUciBasePath(request);
		MeterDefineConfigPort defineConfigPort = (MeterDefineConfigPort)UciInterfaceUtil
				.getInterface("MeterDefineConfigPort", MeterDefineConfigPort.class,sysServiceAttributeService);

		// 设置集中器 ip port apn

		if(sendType == 1){
			Arrays ipaArrays = Tool.Instance.createTerminalIpPortDlms(ip, port, ipBak, portBak);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#1", ipaArrays, commSn, "********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 2){
			Arrays ipaArrays = Tool.Instance.createGprsDlms(apn, userName, password, dialedNumber);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#2", ipaArrays, commSn, "********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 3){
			if(isChecked){
				clock = DateUtils.date2Str(new Date(), new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
			}else{
				clock = DateUtils.date2Str(
						DateUtils.str2Date(clock, DateTimeFormatterUtil.getSimpleDateFormat_HHmmss(TIME_FLAG)),
						DateUtils.datetimeFormat);
			}

			Arrays ipaArrays = Tool.Instance.createClockDlms(clock);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#3", ipaArrays, commSn, "********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 4){
			Arrays ipaArrays = Tool.Instance.createVersionInfoDlms(versionInfo);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#4", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 5){
			//todo edison 重置枚举
			Arrays resetArrays = Tool.Instance.createResetType(resetType);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#5", resetArrays, commSn, ResetTypeDlms.parse(resetType).getDataItemId(), userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 6){
			//more

			Arrays resetArrays = Tool.Instance.createResetType(resetType);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#66", resetArrays, commSn, "********", userId, verb, noun, basePath, defineConfigPort,"100");
			Thread.sleep(1000);
			if(existKeyUser){
				List<String> idsList = Tool.Instance.createMeterUploadSetIds(params);
				List<String> meters = Tool.Instance.createMeterUploadSetMetersDlms(params);
				//todo edison index_dcu 还有先删后减没做
				List<String> logicNames = Tool.Instance.createMeterUploadSetLogicDlms(params);

				Arrays uploadSetArray = Tool.Instance.createImportantUploadSetDlms(params);
				WebserviceBuilder.buildMeterDefineConfigWritingMoreRequest(commId+"#6",idsList,meters, uploadSetArray, commSn, "********", userId, verb, noun, basePath, defineConfigPort,"100",null);
			}

		}else if(sendType == 7){
			Arrays ipaArrays = Tool.Instance.createEncryptionModeDlms(encryptionMode);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#7", ipaArrays, meterSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 8){
			Arrays ipaArrays = Tool.Instance.createEncryptionKeyDlms(encryptionKey);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#8", ipaArrays, meterSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 9){
			Arrays ipaArrays = Tool.Instance.createHeartbeatCycleDlms(heartbeatCycle);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#9", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 10){
			Arrays ipaArrays = Tool.Instance.createEnergyProfileReadingCycleDlms(energyProfileReadingCycle);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#10", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 11){
			Arrays ipaArrays = Tool.Instance.createPLCChannelSendTimesDlms(plcChannelSendTimes);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#11", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 12){
			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(plcChannelTimeout);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#12", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 13){
//			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(plcChannelTimeout);
//			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#13", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 14){
//			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(plcChannelTimeout);
//			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#14", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 15){
			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(dormancyTime);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#15", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 16){
			Arrays ipaArrays = Tool.Instance.createConcentratorIpAddress(terminalIp, subnetMask, gatewayMask);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#16", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 17){
			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(eventConfiguration);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#17", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 18){
			String broadCast = "0";
			if(isCheckedBroadcast){
				broadCast = "1";
			}

			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(broadCast);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#18", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 19){
			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(broadcastTimeParameter);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#19", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 20){
//			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(plcChannelTimeout);
//			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#20", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 21){
//			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(plcChannelTimeout);
//			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#21", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 22){

			//	WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#22", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 23){

			String wan = "0";
			if(isCheckedWan){
				wan = "1";
			}
			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(wan);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#23", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 24){

			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(csMode);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#24", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 25){

			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(serverPort);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#25", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 26){
//			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(plcChannelTimeout);
//			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#26", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 27){
//			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(plcChannelTimeout);
//			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#27", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 28){
//			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(plcChannelTimeout);
//			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#28", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 29){

			List<String> idsList = Tool.Instance.createMeterUploadSetIds(params);
			List<String> meters = Tool.Instance.createMeterUploadSetMetersDlms(params);
			//todo edison index_dcu 还有先删后减没做
			List<String> logicNames = Tool.Instance.createMeterUploadSetLogicDlms(params);

			Arrays uploadSetArray = Tool.Instance.createImportantUploadSetDlms(params);
			WebserviceBuilder.buildMeterDefineConfigWritingMoreRequest(commId+"#29",idsList,meters, uploadSetArray, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100",null);

//			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(plcChannelTimeout);
//			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#29", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 30){

			Arrays ipaArrays = Tool.Instance.createVpnDial(isCheckedConnected, ipAddressVpn, vpnPort, vpnUserName, vpnPassword);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#30", ipaArrays, commSn, "********0", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 32){
			Arrays ipaArrays = Tool.Instance.createSearchMeterSwitch(isSwitched, txtStartedTime32, txtDelayTime32, isActiveCollect, isPassiveCollect);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#32", ipaArrays, commSn, "********3", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 33){
			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(txtDayCurves);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#33", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 34){
			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(txtMonthCurves);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#34", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 35){
			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(txtLoadCurves);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#35", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 36){

			Arrays ipaArrays = Tool.Instance.createEventLogDlms(serviceIds);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#36", ipaArrays, commSn, "********1", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 37){
			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms2(broadcastTimeParameter2,timePeriod);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#37", ipaArrays, commSn, "********8", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 38){
			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(timeZone);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#38", ipaArrays, commSn, "********9", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 39){
			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(batteryTime);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#39", ipaArrays, commSn, "********9", userId, verb, noun, basePath, defineConfigPort,"100");
		}

		return result;
	}
	/**
	 * @throws InterruptedException
	 *
	 * @Title: ajaxSetOtherParameters Dlms
	 * @Description: 参数设置
	 * @param commSn 集中器SN
	 * @param ip
	 * @param port
	 * @param ipBak 备用IP
	 * @param portBak 备用端口
	 * @param apn
	 * @param resetType 重置类型
	 * @return
	 * @return AjaxJson
	 * @throws
	 */
	@RequestMapping(value = "ajaxSetOtherParametersDlmsSaveXml")
	@ResponseBody
	public AjaxJson ajaxSetOtherParametersDlmsSaveXml(String commSn,String commId,String ip,int port,String ipBak,int portBak,String apn,
													  String userName,String password,String  dialedNumber,String clock,String versionInfo,String encryptionMode,String encryptionKey
			,String heartbeatCycle,String energyProfileReadingCycle,String plcChannelSendTimes,String plcChannelTimeout
			,boolean isChecked,boolean existKeyUser,String meterSn
			,int resetType,int sendType
			,String dormancyTime,String terminalIp,String subnetMask,String gatewayMask,String eventConfiguration,
													  String broadcastTimeParameter,String csMode,String serverPort,
													  boolean isCheckedBroadcast,boolean isCheckedWan,
													  boolean isCheckedConnected,
													  String ipAddressVpn,
													  String vpnPort,
													  String vpnUserName,
													  String vpnPassword,
													  boolean isSwitched,
													  String txtStartedTime32,
													  String txtDelayTime32,
													  boolean isActiveCollect,
													  boolean isPassiveCollect,String txtDayCurves,String txtMonthCurves,String txtLoadCurves,
													  HttpServletRequest request) throws InterruptedException{

		AjaxJson result = new AjaxJson();

		String [] params = request.getParameterValues("params[]");
		String [] serviceIds = request.getParameterValues("serviceIds[]");

		String verb = "create";
		String noun = "MeterConfig";
		// 设置集中器 ip port apn
		String uuid=UUID.randomUUID().toString();
		if(sendType == 9){
			Arrays ipaArrays = Tool.Instance.createHeartbeatCycleDlms(heartbeatCycle);
			saveXmlToRedis(verb, noun, "*********", ipaArrays,uuid);
		}else if(sendType == 10){
			Arrays ipaArrays = Tool.Instance.createEnergyProfileReadingCycleDlms(energyProfileReadingCycle);
			saveXmlToRedis(verb, noun, "*********", ipaArrays,uuid);
		}else if(sendType == 11){
			Arrays ipaArrays = Tool.Instance.createPLCChannelSendTimesDlms(plcChannelSendTimes);
			saveXmlToRedis(verb, noun, "*********", ipaArrays,uuid);
		}else if(sendType == 12){
			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(plcChannelTimeout);
			saveXmlToRedis(verb, noun, "*********", ipaArrays,uuid);
		}else if(sendType == 15){
			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(dormancyTime);
			saveXmlToRedis(verb, noun, "*********", ipaArrays,uuid);
		}else if(sendType == 17){
			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(eventConfiguration);
			saveXmlToRedis(verb, noun, "*********", ipaArrays,uuid);
		}else if(sendType == 18){
			String broadCast = "0";
			if(isCheckedBroadcast){
				broadCast = "1";
			}
			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(broadCast);
			saveXmlToRedis(verb, noun, "*********", ipaArrays,uuid);
		}else if(sendType == 19){
			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(broadcastTimeParameter);
			saveXmlToRedis(verb, noun, "*********", ipaArrays,uuid);
		}else if(sendType == 32){
			Arrays ipaArrays = Tool.Instance.createSearchMeterSwitch(isSwitched, txtStartedTime32, txtDelayTime32, isActiveCollect, isPassiveCollect);
			saveXmlToRedis(verb, noun, "********3", ipaArrays,uuid);
		}else if(sendType == 33){
			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(txtDayCurves);
			saveXmlToRedis(verb, noun, "*********", ipaArrays,uuid);
		}else if(sendType == 34){
			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(txtMonthCurves);
			saveXmlToRedis(verb, noun, "*********", ipaArrays,uuid);
		}else if(sendType == 35){
			Arrays ipaArrays = Tool.Instance.createPlcChannelTimeoutDlms(txtLoadCurves);
			saveXmlToRedis(verb, noun, "*********", ipaArrays,uuid);
		}else if(sendType == 36){
			Arrays ipaArrays = Tool.Instance.createEventLogDlms(serviceIds);
			saveXmlToRedis(verb, noun, "********1", ipaArrays,uuid);
		}

		result.put("fileIndexId", uuid);
		return result;
	}



	/**
	 * @throws InterruptedException
	 *
	 * @Title: ajaxSetOtherParameters Dlms
	 * @Description: 参数设置
	 * @param commSn 集中器SN
	 * @param ip
	 * @param port
	 * @param ipBak 备用IP
	 * @param portBak 备用端口
	 * @param apn
	 * @param resetType 重置类型
	 * @return
	 * @return AjaxJson
	 * @throws
	 */
	@RequestMapping(value = "ajaxSetOtherParametersModule")
	@ResponseBody
	public AjaxJson ajaxSetOtherParametersModule(String meterSn ,String meterId ,String ip,int port,String ipBak,int portBak,
												 String ip2,int port2,String userName1,String password1,String apn ,String userName ,String password ,String connectMode ,String answerMode ,String pinCode  ,int inactivityTimeout  ,int noNetworkCommunicationTimeoutReset  ,int sendType ,
												 HttpServletRequest request) throws InterruptedException{

		AjaxJson result = new AjaxJson();

		SysUser su = TokenManager.getToken();
		String userId = su.getId();

		String verb = "create";
		String noun = "MeterConfig";
		//String basePath = "http://linan1607.f3322.net:20000/HESWeb"	//URL地址
		String basePath = ResourceUtil.getUciBasePath(request);
		MeterDefineConfigPort defineConfigPort = (MeterDefineConfigPort)UciInterfaceUtil
				.getInterface("MeterDefineConfigPort", MeterDefineConfigPort.class,sysServiceAttributeService);

		if(sendType == 1){
			//hes ip and port
			Arrays hesIpAndPortArrays = Tool.Instance.createHesIpAndPortModule(ip, port, ipBak, portBak);

			WebserviceBuilder.buildMeterDefineConfigWritingRequest(meterId+"#1", hesIpAndPortArrays, meterSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 2){
			//ftp ip and port
			Arrays hesIpAndPortArrays = Tool.Instance.createFtpIpAndPortModule(ip2, port2, userName1, password1);

			WebserviceBuilder.buildMeterDefineConfigWritingRequest(meterId+"#2", hesIpAndPortArrays, meterSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 3){
			//apn
			Arrays apnArrays = Tool.Instance.createApnModule(apn);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(meterId+"#3", apnArrays, meterSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 4){
			//user password
			Arrays userAndPasswordArrays = Tool.Instance.createUserAndPasswordModule(userName, password);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(meterId+"#4", userAndPasswordArrays, meterSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 5){

			//connectMode

			Arrays apnArrays = Tool.Instance.createApnModule(connectMode);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(meterId+"#5", apnArrays, meterSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 6){

			//answerMode
			Arrays apnArrays = Tool.Instance.createApnModule(answerMode);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(meterId+"#6", apnArrays, meterSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");

		}else if(sendType == 7){
			//pincode
			Arrays apnArrays = Tool.Instance.createApnModule(pinCode);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(meterId+"#7", apnArrays, meterSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 8){

			//inactivityTimeout
			Arrays apnArrays = Tool.Instance.createModule(inactivityTimeout);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(meterId+"#8", apnArrays, meterSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}else if(sendType == 9){
			//noNetworkCommunicationTimeoutReset
			Arrays apnArrays = Tool.Instance.createModule(noNetworkCommunicationTimeoutReset);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(meterId+"#9", apnArrays, meterSn, "*********", userId, verb, noun, basePath, defineConfigPort,"100");
		}

		return result;
	}


	/**
	 *
	 * @Title: ajaxSetOtherParameters 376
	 * @Description: 参数设置
	 * @param commSn 集中器SN
	 * @param ip
	 * @param port
	 * @param ipBak 备用IP
	 * @param portBak 备用端口
	 * @param apn
	 * @param resetType 重置类型
	 * @return
	 * @return AjaxJson
	 * @throws
	 */
	@RequestMapping(value = "ajaxSetOtherParameters")
	@ResponseBody
	public AjaxJson ajaxSetOtherParameters(String commSn,String commId,String ip,int port,String ipBak,int portBak,String apn,int resetType,int sendType,HttpServletRequest request){
		AjaxJson result = new AjaxJson();

		String [] params = request.getParameterValues("params[]");

		SysUser su = TokenManager.getToken();
		String userId = su.getId();

		String verb = "create";
		String noun = "MeterConfig";
		//String basePath = "http://linan1607.f3322.net:20000/HESWeb"	//URL地址
		String basePath = ResourceUtil.getUciBasePath(request);
		MeterDefineConfigPort defineConfigPort = (MeterDefineConfigPort)UciInterfaceUtil
				.getInterface("MeterDefineConfigPort", MeterDefineConfigPort.class,sysServiceAttributeService);

		// 设置集中器 ip port apn

		if(sendType == 1){
			Arrays ipaArrays = Tool.Instance.createTerminalIpPortApn(ip, port, ipBak, portBak, apn);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#1", ipaArrays, commSn, "*********", userId, verb, noun, basePath, defineConfigPort,"200");
		}else if(sendType == 2){

			Arrays resetArrays = Tool.Instance.createResetType(resetType);
			WebserviceBuilder.buildMeterDefineConfigWritingRequest(commId+"#2", resetArrays, commSn, ResetType.parse(resetType).getDataItemId(), userId, verb, noun, basePath, defineConfigPort,"200");
		}else if(sendType == 3){
			//more

			//String commSn = request.getParameter("commSn");
			List<String> idsList = Tool.Instance.createMeterUploadSetIds(params);
			List<String> meters = Tool.Instance.createMeterUploadSetMeters(params);
			List<String> pns = Tool.Instance.createMeterUploadSetPns(params);

			Arrays uploadSetArray = Tool.Instance.createImportantUploadSet(params);
			WebserviceBuilder.buildMeterDefineConfigWritingMoreRequest(commId,idsList,meters, uploadSetArray, commSn, "**********", userId, verb, noun, basePath, defineConfigPort,"200",null);

		}

		return result;
	}



	/**
	 * 手工对时下发
	 * @param meterId
	 * @param clockDate
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "ajaxSetMeterClock")
	@ResponseBody
	public AjaxJson ajaxSetMeterClock(String sn,String meterId,String dcuClock,HttpServletRequest request) {
		// 针对电表手动对时
		AjaxJson result = new AjaxJson();
		try {
			// 将日期格式转换成uci日期格式
			SysUser su = TokenManager.getToken();
			String userId = su.getId();

			String basePath = ResourceUtil.getUciBasePath(request);
			MeterDefineConfigPort port = (MeterDefineConfigPort)UciInterfaceUtil
					.getInterface("MeterDefineConfigPort", MeterDefineConfigPort.class,sysServiceAttributeService);

			/* Header */
			MeterDefineConfigRequestMessageType requestMessageType = new MeterDefineConfigRequestMessageType();
			HeaderType ht = new HeaderType();
			Date date = new Date();
			ht.setVerb("create");
			ht.setNoun("MeterConfig");
			ht.setTimestamp(DateUtils.dateToXmlDate(date));
			ht.setSource("ClouESP HES");
			ht.setMessageID(DateUtils.formatDate("yyyyMMddHHmmss")+su.getId());
			ht.setAsyncReplyFlag(true);
			ht.setReplyAddress(basePath+"/interfaces/ReplyMeterDefineConfigPort?wsdl");
			ht.setAckRequired(true);
			UserType u = new UserType();
			u.setOrganization(su.getOrgId());
			u.setUserID(userId);
			ht.setUser(u);
			requestMessageType.setHeader(ht);


			MeterDefineConfigPayloadType mdfpt = new MeterDefineConfigPayloadType();
			MeterDefineConfig mdc=new MeterDefineConfig();
			/* MeterDefineConfigPayloadType.MeterDefineConfig.Meters */
			Meter meter=new Meter();
			meter.setMRID(sn);
			mdc.getMeters().add(meter);

			/* MeterDefineConfigPayloadType.MeterDefineConfig.Arrays */
			Arrays arrays = new Arrays();

			// 设置参数
			Values values1 = new Values();
			values1.setValue(DateUtils.dateformat(dcuClock,
					ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"), "yyyy-MM-dd HH:mm:ss"));
			arrays.getValues().add(values1);

			mdc.getArrays().add(arrays);

			/* MeterDefineConfigPayloadType.MeterDefineConfig.ReadingType */
			ReadingType rt=new ReadingType();
			rt.setRef("39.0.0.5");
			mdc.setReadingType(rt);

			/* MeterDefineConfigPayloadType.MeterDefineConfig */
			mdfpt.setMeterDefineConfig(mdc);

			/* MeterDefineConfigPayloadType */
			requestMessageType.setPayload(mdfpt);

			// System.out.println(XMLUtil.convertToXml(requestMessageType));

			MeterDefineConfigResponseMessageType responseMessageType = port.meterDefineConfig(requestMessageType);

			if(!responseMessageType.getReply().getResult().toUpperCase().equals("OK")){
				Map<String,Object> m=new HashMap<String, Object>();
				m.put("rateId", rt.getRef());
				m.put("msg", responseMessageType.getReply().getResult().toUpperCase());
				result.put(requestMessageType.getHeader().getMessageID(), m);
			}

			// PushletData.pushlet("meterConfigurationHY", result, su.getId());
		} catch (Exception e) {
			e.printStackTrace();
		}

		return result;
	}


	/**
	 * 手工继电器模式下发
	 * @param meterId
	 * @param clockDate
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "ajaxSetMeterRelayControMode")
	@ResponseBody
	public AjaxJson ajaxSetMeterRelayControMode(String sn,String meterId,String relayControlMode,HttpServletRequest request) {
		// 针对电表手动对时
		AjaxJson result = new AjaxJson();
		try {
			// 将日期格式转换成uci日期格式
			SysUser su = TokenManager.getToken();
			String userId = su.getId();

			String basePath = ResourceUtil.getUciBasePath(request);
			MeterDefineConfigPort port = (MeterDefineConfigPort)UciInterfaceUtil
					.getInterface("MeterDefineConfigPort", MeterDefineConfigPort.class,sysServiceAttributeService);

			/* Header */
			MeterDefineConfigRequestMessageType requestMessageType = new MeterDefineConfigRequestMessageType();
			HeaderType ht = new HeaderType();
			Date date = new Date();
			ht.setVerb("create");
			ht.setNoun("MeterConfig");
			ht.setTimestamp(DateUtils.dateToXmlDate(date));
			ht.setSource("ClouESP HES");
			ht.setMessageID(DateUtils.formatDate("yyyyMMddHHmmss")+su.getId());
			ht.setAsyncReplyFlag(true);
			ht.setReplyAddress(basePath+"/interfaces/ReplyMeterDefineConfigPort?wsdl");
			ht.setAckRequired(true);
			UserType u = new UserType();
			u.setOrganization(su.getOrgId());
			u.setUserID(userId);
			ht.setUser(u);
			requestMessageType.setHeader(ht);


			MeterDefineConfigPayloadType mdfpt = new MeterDefineConfigPayloadType();
			MeterDefineConfig mdc=new MeterDefineConfig();
			/* MeterDefineConfigPayloadType.MeterDefineConfig.Meters */
			Meter meter=new Meter();
			meter.setMRID(sn);
			mdc.getMeters().add(meter);

			/* MeterDefineConfigPayloadType.MeterDefineConfig.Arrays */
			Arrays arrays = new Arrays();

			// 设置参数
			Values values1 = new Values();
			values1.setValue(relayControlMode);
			arrays.getValues().add(values1);

			mdc.getArrays().add(arrays);

			/* MeterDefineConfigPayloadType.MeterDefineConfig.ReadingType */
			ReadingType rt=new ReadingType();
			rt.setRef("********");
			mdc.setReadingType(rt);

			/* MeterDefineConfigPayloadType.MeterDefineConfig */
			mdfpt.setMeterDefineConfig(mdc);

			/* MeterDefineConfigPayloadType */
			requestMessageType.setPayload(mdfpt);

			// System.out.println(XMLUtil.convertToXml(requestMessageType));

			MeterDefineConfigResponseMessageType responseMessageType = port.meterDefineConfig(requestMessageType);

			if(!responseMessageType.getReply().getResult().toUpperCase().equals("OK")){
				Map<String,Object> m=new HashMap<String, Object>();
				m.put("rateId", rt.getRef());
				m.put("msg", responseMessageType.getReply().getResult().toUpperCase());
				result.put(requestMessageType.getHeader().getMessageID(), m);
			}

			// PushletData.pushlet("meterConfigurationHY", result, su.getId());
		} catch (Exception e) {
			e.printStackTrace();
		}

		return result;
	}


	/**
	 * LDC配置下发
	 * @param meterId
	 * @param clockDate
	 * @param request
	 * @return
	 */
//	@RequestMapping(value = "ajaxSetMeterLDCConfig")
//	@ResponseBody
//	public AjaxJson ajaxSetMeterLDCConfig(String sn,String meterId,HttpServletRequest request) {
//		AjaxJson result = new AjaxJson();
//		try {
//			String[] itemIds = request.getParameterValues("itemIds[]");
//
//			SysUser su = TokenManager.getToken();
//			String userId = su.getId();
//
//			String basePath = ResourceUtil.getUciBasePath(request);
//			MeterDefineConfigPort port = (MeterDefineConfigPort)UciInterfaceUtil
//	         		.getInterface("MeterDefineConfigPort", MeterDefineConfigPort.class,sysServiceAttributeService);
//
//			/* Header */
//			MeterDefineConfigRequestMessageType requestMessageType = new MeterDefineConfigRequestMessageType();
//			HeaderType ht = new HeaderType();
//	        Date date = new Date();
//	        ht.setVerb("create");
//	        ht.setNoun("MeterConfig");
//	        ht.setTimestamp(DateUtils.dateToXmlDate(date));
//	        ht.setSource("Clou_Esp");
//	        ht.setMessageID(DateUtils.formatDate("yyyyMMddHHmmss")+su.getId());
//	        ht.setAsyncReplyFlag(true);
//	        ht.setReplyAddress(basePath+"/interfaces/ReplyMeterDefineConfigPort?wsdl");
//	        ht.setAckRequired(true);
//	        UserType u = new UserType();
//	        u.setOrganization(su.getOrgId());
//	        u.setUserID(userId);
//	        ht.setUser(u);
//	        requestMessageType.setHeader(ht);
//
//
//	    	MeterDefineConfigPayloadType mdfpt = new MeterDefineConfigPayloadType();
//	        MeterDefineConfig mdc=new MeterDefineConfig();
//	        /* MeterDefineConfigPayloadType.MeterDefineConfig.Meters */
//	        Meter meter=new Meter();
//	        meter.setMRID(sn);
//	        mdc.getMeters().add(meter);
//
//	        /* MeterDefineConfigPayloadType.MeterDefineConfig.Arrays */
//	        Arrays arrays = new Arrays();
//
//	        for (String id : itemIds) {
//	        	Structures structures = new Structures();
//	        	Values values1 = new Values();
//	        	values1.setValue(id);
//	        	structures.getValues().add(values1);
//	        	arrays.getStructures().add(structures);
//			}
//			mdc.getArrays().add(arrays);
//
//			/* MeterDefineConfigPayloadType.MeterDefineConfig.ReadingType */
//	        ReadingType rt=new ReadingType();
//	    	rt.setRef("********");
//	    	mdc.setReadingType(rt);
//
//	    	/* MeterDefineConfigPayloadType.MeterDefineConfig */
//	    	mdfpt.setMeterDefineConfig(mdc);
//
//	    	/* MeterDefineConfigPayloadType */
//	    	requestMessageType.setPayload(mdfpt);
//
//	    	// System.out.println(XMLUtil.convertToXml(requestMessageType));
//
//			MeterDefineConfigResponseMessageType responseMessageType = port.meterDefineConfig(requestMessageType);
//
//			if(!responseMessageType.getReply().getResult().toUpperCase().equals("OK")){
//				Map<String,Object> m=new HashMap<String, Object>();
//				m.put("rateId", rt.getRef());
//				m.put("msg", responseMessageType.getReply().getResult().toUpperCase());
//				result.put(requestMessageType.getHeader().getMessageID(), m);
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return result;
//	}

	/**
	 * LCD配置下发轮显模式
	 * @param meterId
	 * @param clockDate
	 * @param request
	 * @parm mode 0:自动轮显 1：手动轮显
	 * @return
	 */
	@RequestMapping(value = "ajaxSetMeterLCDPeriodConfig")
	@ResponseBody
	public AjaxJson ajaxSetMeterLCDPeriodConfig(String sn,String meterId,String mode,String period,HttpServletRequest request) {
		AjaxJson result = new AjaxJson();
		try {
			SysUser su = TokenManager.getToken();
			String userId = su.getId();

			String basePath = ResourceUtil.getUciBasePath(request);
			MeterDefineConfigPort port = (MeterDefineConfigPort)UciInterfaceUtil
					.getInterface("MeterDefineConfigPort", MeterDefineConfigPort.class,sysServiceAttributeService);

			Integer periodSec = Integer.parseInt(period);

			/* Header */
			MeterDefineConfigRequestMessageType requestMessageType = new MeterDefineConfigRequestMessageType();
			HeaderType ht = new HeaderType();
			Date date = new Date();
			ht.setVerb("create");
			ht.setNoun("MeterConfig");
			ht.setTimestamp(DateUtils.dateToXmlDate(date));
			ht.setSource("ClouESP HES");
			ht.setMessageID(DateUtils.formatDate("yyyyMMddHHmmss")+su.getId());
			ht.setAsyncReplyFlag(true);
			ht.setReplyAddress(basePath+"/interfaces/ReplyMeterDefineConfigPort?wsdl");
			ht.setAckRequired(true);
			UserType u = new UserType();
			u.setOrganization(su.getOrgId());
			u.setUserID(userId);
			ht.setUser(u);
			requestMessageType.setHeader(ht);


			MeterDefineConfigPayloadType mdfpt = new MeterDefineConfigPayloadType();
			MeterDefineConfig mdc=new MeterDefineConfig();
			/* MeterDefineConfigPayloadType.MeterDefineConfig.Meters */
			Meter meter=new Meter();
			meter.setMRID(sn);
			mdc.getMeters().add(meter);

			/* MeterDefineConfigPayloadType.MeterDefineConfig.Arrays */
			Arrays arrays = new Arrays();

			Values values1 = new Values();
			values1.setValue(periodSec.toString());
			values1.setType("string");
			arrays.getValues().add(values1);

			mdc.getArrays().add(arrays);

			/* MeterDefineConfigPayloadType.MeterDefineConfig.ReadingType */
			ReadingType rt=new ReadingType();

			if ("0".equals(mode)) {
				rt.setRef("********");
			} else if ("1".equals(mode)) {
				rt.setRef("********");
			}

			mdc.setReadingType(rt);

			/* MeterDefineConfigPayloadType.MeterDefineConfig */
			mdfpt.setMeterDefineConfig(mdc);

			/* MeterDefineConfigPayloadType */
			requestMessageType.setPayload(mdfpt);

			System.out.println(XMLUtil.convertToXml(requestMessageType));

			MeterDefineConfigResponseMessageType responseMessageType = port.meterDefineConfig(requestMessageType);

			if(!responseMessageType.getReply().getResult().toUpperCase().equals("OK")){
				Map<String,Object> m=new HashMap<String, Object>();
				m.put("rateId", rt.getRef());
				m.put("msg", responseMessageType.getReply().getResult().toUpperCase());
				result.put(requestMessageType.getHeader().getMessageID(), m);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}


	/**
	 * LCD配置下发轮显模式
	 * @param meterId
	 * @param clockDate
	 * @param request
	 * @parm mode 0:自动轮显 1：手动轮显
	 * @return
	 */
	@RequestMapping(value = "ajaxGetMeterLCDPeriodConfig")
	@ResponseBody
	public AjaxJson ajaxGetMeterLCDPeriodConfig(String sn,String meterId,String mode,HttpServletRequest request) {
		AjaxJson result = new AjaxJson();
		try {
			SysUser su = TokenManager.getToken();
			String userId = su.getId();

			String basePath = ResourceUtil.getUciBasePath(request);
			MeterDefineConfigPort port = (MeterDefineConfigPort)UciInterfaceUtil
					.getInterface("MeterDefineConfigPort", MeterDefineConfigPort.class,sysServiceAttributeService);

			/* Header */
			MeterDefineConfigRequestMessageType requestMessageType = new MeterDefineConfigRequestMessageType();
			HeaderType ht = new HeaderType();
			Date date = new Date();
			ht.setVerb("get");
			ht.setNoun("MeterConfig");
			ht.setTimestamp(DateUtils.dateToXmlDate(date));
			ht.setSource("ClouESP HES");
			ht.setMessageID(DateUtils.formatDate("yyyyMMddHHmmss")+su.getId());
			ht.setAsyncReplyFlag(true);
			ht.setReplyAddress(basePath+"/interfaces/ReplyMeterDefineConfigPort?wsdl");
			ht.setAckRequired(true);
			UserType u = new UserType();
			u.setOrganization(su.getOrgId());
			u.setUserID(userId);
			ht.setUser(u);
			requestMessageType.setHeader(ht);


			MeterDefineConfigPayloadType mdfpt = new MeterDefineConfigPayloadType();
			MeterDefineConfig mdc=new MeterDefineConfig();
			/* MeterDefineConfigPayloadType.MeterDefineConfig.Meters */
			Meter meter=new Meter();
			meter.setMRID(sn);
			mdc.getMeters().add(meter);

			/* MeterDefineConfigPayloadType.MeterDefineConfig.ReadingType */
			ReadingType rt=new ReadingType();

			if ("0".equals(mode)) {
				rt.setRef("********");
			} else if ("1".equals(mode)) {
				rt.setRef("********");
			}

			mdc.setReadingType(rt);

			/* MeterDefineConfigPayloadType.MeterDefineConfig */
			mdfpt.setMeterDefineConfig(mdc);

			/* MeterDefineConfigPayloadType */
			requestMessageType.setPayload(mdfpt);

			System.out.println(XMLUtil.convertToXml(requestMessageType));

			MeterDefineConfigResponseMessageType responseMessageType = port.meterDefineConfig(requestMessageType);

			if(!responseMessageType.getReply().getResult().toUpperCase().equals("OK")){
				Map<String,Object> m=new HashMap<String, Object>();
				m.put("rateId", rt.getRef());
				m.put("msg", responseMessageType.getReply().getResult().toUpperCase());
				result.put(requestMessageType.getHeader().getMessageID(), m);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	/**
	 * LCD配置下发轮显项
	 * @param meterId
	 * @param clockDate
	 * @param request
	 * @parm mode 0:自动轮显 1：手动轮显
	 * @return
	 */
	@RequestMapping(value = "ajaxSetMeterLCDChannelConfig")
	@ResponseBody
	public AjaxJson ajaxSetMeterLCDChannelConfig(String sn,String meterId,String mode,HttpServletRequest request) {
		AjaxJson result = new AjaxJson();
		try {
			List<String> channelIds = new ArrayList<String>();
			SysUser su = TokenManager.getToken();
			String userId = su.getId();
			//获取Redis中保存的channel list
			Map<String, Object> redisValues = JedisUtils.getObjectMap(su.getId() + "autoLCDList");
			if(redisValues!=null) {
				List<Map<String, Object>> redisList = (List<Map<String, Object>>) redisValues.get("listDi");
				for (Map<String, Object> map : redisList) {
					channelIds.add((String) map.get("channelId"));
				}
			}

			String basePath = ResourceUtil.getUciBasePath(request);
			MeterDefineConfigPort port = (MeterDefineConfigPort)UciInterfaceUtil
					.getInterface("MeterDefineConfigPort", MeterDefineConfigPort.class,sysServiceAttributeService);

			/* Header */
			MeterDefineConfigRequestMessageType requestMessageType = new MeterDefineConfigRequestMessageType();
			HeaderType ht = new HeaderType();
			Date date = new Date();
			ht.setVerb("create");
			ht.setNoun("MeterConfig");
			ht.setTimestamp(DateUtils.dateToXmlDate(date));
			ht.setSource("Clou_Esp");
			ht.setMessageID(DateUtils.formatDate("yyyyMMddHHmmss")+su.getId());
			ht.setAsyncReplyFlag(true);
			ht.setReplyAddress(basePath+"/interfaces/ReplyMeterDefineConfigPort?wsdl");
			ht.setAckRequired(true);
			UserType u = new UserType();
			u.setOrganization(su.getOrgId());
			u.setUserID(userId);
			ht.setUser(u);
			requestMessageType.setHeader(ht);


			MeterDefineConfigPayloadType mdfpt = new MeterDefineConfigPayloadType();
			MeterDefineConfig mdc=new MeterDefineConfig();
			/* MeterDefineConfigPayloadType.MeterDefineConfig.Meters */
			Meter meter=new Meter();
			meter.setMRID(sn);
			mdc.getMeters().add(meter);

			/* MeterDefineConfigPayloadType.MeterDefineConfig.Arrays */
			Arrays arrays = new Arrays();

			for (String id : channelIds) {
				Structures structures = new Structures();
				Values values1 = new Values();
				values1.setValue(id);
				structures.getValues().add(values1);
				arrays.getStructures().add(structures);
			}
			mdc.getArrays().add(arrays);

			/* MeterDefineConfigPayloadType.MeterDefineConfig.ReadingType */
			ReadingType rt=new ReadingType();

			if ("0".equals(mode)) {
				rt.setRef("********");
			} else if ("1".equals(mode)) {
				rt.setRef("********");
			}
			mdc.setReadingType(rt);

			/* MeterDefineConfigPayloadType.MeterDefineConfig */
			mdfpt.setMeterDefineConfig(mdc);

			/* MeterDefineConfigPayloadType */
			requestMessageType.setPayload(mdfpt);

			System.out.println(XMLUtil.convertToXml(requestMessageType));

			MeterDefineConfigResponseMessageType responseMessageType = port.meterDefineConfig(requestMessageType);

			if(!responseMessageType.getReply().getResult().toUpperCase().equals("OK")){
				Map<String,Object> m=new HashMap<String, Object>();
				m.put("rateId", rt.getRef());
				m.put("msg", responseMessageType.getReply().getResult().toUpperCase());
				result.put(requestMessageType.getHeader().getMessageID(), m);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	/**
	 * LCD配置下发轮显项
	 * @param meterId
	 * @param clockDate
	 * @param request
	 * @parm mode 0:自动轮显 1：手动轮显
	 * @return
	 */
	@RequestMapping(value = "ajaxGetMeterLCDChannelConfig")
	@ResponseBody
	public AjaxJson ajaxGetMeterLCDChannelConfig(String sn,String meterId,String mode,HttpServletRequest request) {
		AjaxJson result = new AjaxJson();
		try {
			List<String> channelIds = new ArrayList<String>();
			SysUser su = TokenManager.getToken();
			String userId = su.getId();
			//获取Redis中保存的channel list
			Map<String, Object> redisValues = JedisUtils.getObjectMap(su.getId() + "autoLCDList");
			if(redisValues!=null) {
				List<Map<String, Object>> redisList = (List<Map<String, Object>>) redisValues.get("listDi");
				for (Map<String, Object> map : redisList) {
					channelIds.add((String) map.get("channelId"));
				}
			}

			String basePath = ResourceUtil.getUciBasePath(request);
			MeterDefineConfigPort port = (MeterDefineConfigPort)UciInterfaceUtil
					.getInterface("MeterDefineConfigPort", MeterDefineConfigPort.class,sysServiceAttributeService);

			/* Header */
			MeterDefineConfigRequestMessageType requestMessageType = new MeterDefineConfigRequestMessageType();
			HeaderType ht = new HeaderType();
			Date date = new Date();
			ht.setVerb("get");
			ht.setNoun("MeterConfig");
			ht.setTimestamp(DateUtils.dateToXmlDate(date));
			ht.setSource("ClouESP HES");
			ht.setMessageID(DateUtils.formatDate("yyyyMMddHHmmss")+su.getId());
			ht.setAsyncReplyFlag(true);
			ht.setReplyAddress(basePath+"/interfaces/ReplyMeterDefineConfigPort?wsdl");
			ht.setAckRequired(true);
			UserType u = new UserType();
			u.setOrganization(su.getOrgId());
			u.setUserID(userId);
			ht.setUser(u);
			requestMessageType.setHeader(ht);


			MeterDefineConfigPayloadType mdfpt = new MeterDefineConfigPayloadType();
			MeterDefineConfig mdc=new MeterDefineConfig();
			/* MeterDefineConfigPayloadType.MeterDefineConfig.Meters */
			Meter meter=new Meter();
			meter.setMRID(sn);
			mdc.getMeters().add(meter);

			/* MeterDefineConfigPayloadType.MeterDefineConfig.ReadingType */
			ReadingType rt=new ReadingType();

			if ("0".equals(mode)) {
				rt.setRef("********");
			} else if ("1".equals(mode)) {
				rt.setRef("********");
			}
			mdc.setReadingType(rt);

			/* MeterDefineConfigPayloadType.MeterDefineConfig */
			mdfpt.setMeterDefineConfig(mdc);

			/* MeterDefineConfigPayloadType */
			requestMessageType.setPayload(mdfpt);

			System.out.println(XMLUtil.convertToXml(requestMessageType));

			MeterDefineConfigResponseMessageType responseMessageType = port.meterDefineConfig(requestMessageType);

			if(!responseMessageType.getReply().getResult().toUpperCase().equals("OK")){
				Map<String,Object> m=new HashMap<String, Object>();
				m.put("rateId", rt.getRef());
				m.put("msg", responseMessageType.getReply().getResult().toUpperCase());
				result.put(requestMessageType.getHeader().getMessageID(), m);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	/**
	 * Limit Monitor Value配置下发
	 * @param meterId
	 * @param clockDate
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "ajaxSetLimitMonitorValue")
	@ResponseBody
	public AjaxJson ajaxSetLimitMonitorValue(String sn,String meterId,HttpServletRequest request) {
		AjaxJson result = new AjaxJson();
		try {
			String[] itemIds = request.getParameterValues("itemIds[]");

			SysUser su = TokenManager.getToken();
			String userId = su.getId();

			String basePath = ResourceUtil.getUciBasePath(request);
			MeterDefineConfigPort port = (MeterDefineConfigPort)UciInterfaceUtil
					.getInterface("MeterDefineConfigPort", MeterDefineConfigPort.class,sysServiceAttributeService);

			/* Header */
			MeterDefineConfigRequestMessageType requestMessageType = new MeterDefineConfigRequestMessageType();
			HeaderType ht = new HeaderType();
			Date date = new Date();
			ht.setVerb("create");
			ht.setNoun("MeterConfig");
			ht.setTimestamp(DateUtils.dateToXmlDate(date));
			ht.setSource("Clou_Esp");
			ht.setMessageID(DateUtils.formatDate("yyyyMMddHHmmss")+su.getId());
			ht.setAsyncReplyFlag(true);
			ht.setReplyAddress(basePath+"/interfaces/ReplyMeterDefineConfigPort?wsdl");
			ht.setAckRequired(true);
			UserType u = new UserType();
			u.setOrganization(su.getOrgId());
			u.setUserID(userId);
			ht.setUser(u);
			requestMessageType.setHeader(ht);


			MeterDefineConfigPayloadType mdfpt = new MeterDefineConfigPayloadType();
			MeterDefineConfig mdc=new MeterDefineConfig();
			/* MeterDefineConfigPayloadType.MeterDefineConfig.Meters */
			Meter meter=new Meter();
			meter.setMRID(sn);
			mdc.getMeters().add(meter);

			/* MeterDefineConfigPayloadType.MeterDefineConfig.Arrays */
			Arrays arrays = new Arrays();
			arrays.setType("string");
			for (String id : itemIds) {
				Values values1 = new Values();
				values1.setValue(id);
				values1.setType("string");
				arrays.getValues().add(values1);
			}
			mdc.getArrays().add(arrays);

			/* MeterDefineConfigPayloadType.MeterDefineConfig.ReadingType */
			ReadingType rt=new ReadingType();
			rt.setRef("*********");
			mdc.setReadingType(rt);

			/* MeterDefineConfigPayloadType.MeterDefineConfig */
			mdfpt.setMeterDefineConfig(mdc);

			/* MeterDefineConfigPayloadType */
			requestMessageType.setPayload(mdfpt);

			System.out.println(XMLUtil.convertToXml(requestMessageType));

			MeterDefineConfigResponseMessageType responseMessageType = port.meterDefineConfig(requestMessageType);

			if(!responseMessageType.getReply().getResult().toUpperCase().equals("OK")){
				Map<String,Object> m=new HashMap<String, Object>();
				m.put("rateId", rt.getRef());
				m.put("msg", responseMessageType.getReply().getResult().toUpperCase());
				result.put(requestMessageType.getHeader().getMessageID(), m);
			}

			// PushletData.pushlet("meterConfigurationHY", result, su.getId());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	public void saveXmlToRedis(String verb, String noun,String dataitemId,Arrays ipaArrays,String uuid) {
		MeterDefineConfigRequestMessageType requestMessage = new MeterDefineConfigRequestMessageType();
		requestMessage.setHeader(createHeader(verb, noun));
		requestMessage.setPayload((MeterDefineConfigPayloadType) createPayload(dataitemId,ipaArrays));
		JedisUtils.set(uuid, XMLUtil.convertToXml(requestMessage), 24*60*60);
	}

	public HeaderType createHeader(String verb, String noun) {
		HeaderType header = new HeaderType();
		header.setVerb(verb);
		header.setNoun(noun);
		header.setTimestamp(DateUtils.dateToXmlDate(new Date()));
		header.setSource("HES Batch");
		header.setAsyncReplyFlag(true);
		header.setAckRequired(true);
		header.setReplyAddress("");
		header.setMessageID(UUID.randomUUID().toString().replace("-", ""));
		header.setRevision("100");

		UserType userType = new UserType();
		userType.setOrganization("");
		userType.setUserID("");
		header.setUser(userType);
		header.setCorrelationID("");

		return header;
	}

	public Object createPayload(String dataItemId,Arrays arrays) {
		MeterDefineConfig config = new MeterDefineConfig();

		Meter meter = new Meter();
		meter.setMRID("dcuParamBatch");
		config.getMeters().add(meter);

		MeterDefineConfig.ReadingType type = new MeterDefineConfig.ReadingType();
		type.setRef(dataItemId);
		config.setReadingType(type);
		config.getArrays().add(arrays);

		MeterDefineConfigPayloadType payLoadType = new MeterDefineConfigPayloadType();
		payLoadType.setMeterDefineConfig(config);
		return payLoadType;
	}

	@RequestMapping(value = "reportExcelData")
	@ResponseBody
	public AjaxJson reportExcelData(@RequestBody DcuConfigModel data, HttpServletResponse response){
		AjaxJson result = new AjaxJson();
		List<DcuConfigModel> array = new ArrayList<>();
		DcuConfigModel m = new DcuConfigModel();
		m.setLevel(data.getLevel());
		m.setMac(data.getMac());
		m.setName(data.getName());
		m.setNetStatus(data.getNetStatus());
		m.setParentName(data.getParentName());
		m.setPhaseInfo(data.getPhaseInfo());
		m.setShortAddress(data.getShortAddress());
		m.setSignalIntensity(data.getSignalIntensity());
		m.setValue(data.getValue());
		array.add(m);
		List<DcuConfigModel> arr = this.getDataList(data.getChildren());
		result.put("arr",arr);
		return result;
	}

	/**
	 * 取消递归
	 * @param arr
	 * @return
	 */
	public List<DcuConfigModel> getDataList(List<DcuConfigModel> arr){
		List<DcuConfigModel> array = new ArrayList<>();
		for (DcuConfigModel temp : arr) {
			DcuConfigModel m = new DcuConfigModel();
			m.setLevel(temp.getLevel());
			m.setMac(temp.getMac());
			m.setName(temp.getName());
			m.setNetStatus(temp.getNetStatus());
			m.setParentName(temp.getParentName());
			m.setPhaseInfo(temp.getPhaseInfo());
			m.setShortAddress(temp.getShortAddress());
			m.setSignalIntensity(temp.getSignalIntensity());
			m.setValue(temp.getValue());
			array.add(m);
			if(!temp.getChildren().isEmpty()){
				array.addAll(getDataList(temp.getChildren()));
			}
		}
		return  array;
	}


}



