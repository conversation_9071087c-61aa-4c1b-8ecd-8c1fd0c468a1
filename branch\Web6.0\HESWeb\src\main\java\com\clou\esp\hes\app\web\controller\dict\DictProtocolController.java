/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictProtocol{ } 
 * 
 * 摘    要： 通讯规约
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-01-16 02:01:08
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.dict;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;

import com.clou.esp.hes.app.web.model.dict.DictProtocol;
import com.clou.esp.hes.app.web.service.dict.DictProtocolService;

/**
 * <AUTHOR>
 * @时间：2018-01-16 02:01:08
 * @描述：通讯规约类
 */
@Controller
@RequestMapping("/dictProtocolController")
public class DictProtocolController extends BaseController{

 	@Resource
    private DictProtocolService dictProtocolService;

	/**
	 * 跳转到通讯规约列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/dict/dictProtocolList");
    }

	/**
	 * 跳转到通讯规约新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dictProtocol")
	public ModelAndView dictProtocol(DictProtocol dictProtocol,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dictProtocol.getId())){
			try {
                dictProtocol=dictProtocolService.getEntity(dictProtocol.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dictProtocol", dictProtocol);
		}
		return new ModelAndView("/dict/dictProtocol");
	}


	/**
	 * 通讯规约查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=dictProtocolService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除通讯规约信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DictProtocol dictProtocol,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dictProtocolService.deleteById(dictProtocol.getId())>0){
                j.setMsg("删除成功");
            }else{
                j.setSuccess(false);
                j.setMsg("删除失败");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
    
    /**
     * 保存通讯规约信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DictProtocol dictProtocol,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DictProtocol t=new  DictProtocol();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dictProtocol.getId())){
        	t=dictProtocolService.getEntity(dictProtocol.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dictProtocol, t);
				dictProtocolService.update(t);
				j.setMsg("修改成功");
				
			}else{
	            dictProtocolService.save(dictProtocol);
	            j.setMsg("创建成功");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
	
	
	
}