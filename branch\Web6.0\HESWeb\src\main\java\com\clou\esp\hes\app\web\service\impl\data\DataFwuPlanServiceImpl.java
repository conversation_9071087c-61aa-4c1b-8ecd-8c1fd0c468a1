/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataFwuPlan{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-12-29 08:37:48
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.data;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.UciInterfaceUtil;
import com.clou.esp.hes.app.web.core.util.XMLUtil;
import com.clou.esp.hes.app.web.dao.data.DataFwuJobDao;
import com.clou.esp.hes.app.web.dao.data.DataFwuPlanDao;
import com.clou.esp.hes.app.web.model.asset.AssetCommunicator;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.data.DataFwuJob;
import com.clou.esp.hes.app.web.model.data.DataFwuPlan;
import com.clou.esp.hes.app.web.model.data.TempJob;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.asset.AssetCommunicatorService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.data.DataFwuPlanService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.clou.esp.hes.app.web.service.system.SysServiceAttributeService;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.uuid.UUIDGenerator;
import com.power7000g.core.util.verify.ValidationUtils;

import ch.iec.tc57._2011.enddevicecontrols.FaultMessage;
import ch.iec.tc57._2011.enddevicecontrols.RequestEndDeviceControlsPort;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl.EndDeviceControlType;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl.EndDevices;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl.EndDevices.Names;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl.EndDevices.Names.NameType;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControls;
import ch.iec.tc57._2011.enddevicecontrolsmessage.EndDeviceControlsPayloadType;
import ch.iec.tc57._2011.enddevicecontrolsmessage.EndDeviceControlsRequestMessageType;
import ch.iec.tc57._2011.enddevicecontrolsmessage.EndDeviceControlsResponseMessageType;
import ch.iec.tc57._2011.schema.message.HeaderType;
import ch.iec.tc57._2011.schema.message.UserType;

@Component
@Service("dataFwuPlanService")
public class DataFwuPlanServiceImpl extends CommonServiceImpl<DataFwuPlan>	implements DataFwuPlanService {

	@Resource
	private DataFwuPlanDao dataFwuPlanDao;
	@Resource
	private DataFwuJobDao dataFwuJobDao;
	@Resource
	private AssetMeterService assetMeterService;
	@Resource
	private AssetCommunicatorService assetCommunicatorService;

	@Autowired
	public void setCommonService() {
		super.setCommonService(dataFwuPlanDao);
	}

	public DataFwuPlanServiceImpl() {
	}
	
	/**
	 * 保存固件升级计划
	 */
	@Override
	@Transactional
	public AjaxJson saveFWUPlan(DataFwuPlan dfp, SysServiceAttributeService sysServiceAttributeService) throws FaultMessage {
		SysUser su = TokenManager.getToken();
		AjaxJson j = new AjaxJson();
		String valityMsg = ValidationUtils.validateObj(dfp);
		if (StringUtil.isNotEmpty(valityMsg)) {
			j.setErrorMsg(valityMsg);
			return j;
		}
		// 比对日期大小
		Date startTime = dfp.getStartTime();
		Date expiryTime = dfp.getExpiryTime();
		if (!startTime.before(expiryTime)) {
			j.setErrorMsg(MutiLangUtil.doMutiLang("meterGroupUpgrade.startEqualExpiry"));
			return j;
		}
		Date d = new Date();
		Date taskStartTime = dfp.getTaskStartTime();
		Date taskEndtime = dfp.getTaskEndTime();
		taskStartTime = DateUtils.str2Date(DateUtils.date2Str(d, DateUtils.date_sdf) 
				+ " " + DateUtils.date2Str(taskStartTime, DateUtils.HHmmss), DateUtils.datetimeFormat);
		taskEndtime = DateUtils.str2Date(DateUtils.date2Str(d, DateUtils.date_sdf) 
				+ " " + DateUtils.date2Str(taskEndtime, DateUtils.HHmmss), DateUtils.datetimeFormat);
//		if (taskEndtime.before(taskStartTime)) {
//			j.setErrorMsg("Task start time must be equal or later than Task End Time!");
//			return j;
//		}
		// 处理Tast Start/End Time 这两个字段
		String deviceType = dfp.getDeviceType();
		String manufacturerId = dfp.getManufacturerId();
		String deviceModel = dfp.getDeviceModel();
		String currentVesion = dfp.getCurrentVesion();
		String currentVesionArray[] = null;
		if (StringUtil.isNotEmpty(currentVesion)) {
			currentVesionArray = currentVesion.split(",");
		}
		String newVersion = dfp.getNewVersion();
		String sn = dfp.getSn();
		String planId = UUIDGenerator.generate();
		dfp.setId(planId);
		dfp.setFilePath(dfp.getFirmwareFileName());	//存放文件名称
		
		if (deviceType.equals("1")||deviceType.equals("3")||deviceType.equals("5")||deviceType.equals("6")||deviceType.equals("10")) {
			// 查询电表集合
			
			// 判断查询的sn类型
			String condSnDeviceType = dfp.getCondSnDeviceType();
			if ("2".equals(condSnDeviceType)) {
				// 名称加成sn信息
				dfp.setIntroduction(dfp.getIntroduction() + "[" + dfp.getSn() + "]");
				
				// 查询对应集中器
				AssetCommunicator ac = new AssetCommunicator();
				ac.setSn(dfp.getSn());
				ac = assetCommunicatorService.get(ac);
				if (ac == null) {
					j.setErrorMsg("No devices found!");
					return j;
				}
				
				AssetMeter meter = new AssetMeter();
				meter.setCommunicatorId(ac.getId());
				meter.setManufacturer(manufacturerId);
				meter.setModel(deviceModel);
				meter.setCurrentVesionArray(currentVesionArray);
				List<AssetMeter> mList = assetMeterService.getList(meter);
				if (mList == null || mList.size() == 0) {
					j.setErrorMsg("No devices found!");
					return j;
				}
				dataFwuPlanDao.insert(dfp); //保存升级计划
				j.setObj(planId);
				
				List<DataFwuJob> dfjList = new ArrayList<DataFwuJob>();
				int size = mList.size();
				
				for (int i = 1; i <= size; i++) {
					AssetMeter am = mList.get(i - 1);
					DataFwuJob dfj = new DataFwuJob();
					dfj.setId(UUIDGenerator.generate());
					dfj.setPlanId(planId);
					dfj.setDeviceId(am.getId());
					dfj.setDeviceType(deviceType);
					dfj.setCurrentVesion(am.getFwVersion());
					dfj.setNewVersion(newVersion);
					dfj.setState("4");// 4: Waiting - apps
					dfj.setLastExecTime(null);
					dfj.setFailedReason("");
					dfj.setFwuStep("");
					dfj.setBlockSize(null);
					dfj.setBlockCount(null);
					dfj.setFwuStep(null);
					dfj.setSn(am.getSn());
					dfjList.add(dfj);
					if (i == size) {
						int ret = dataFwuJobDao.batchInsert(dfjList);
						System.out.println("Asset Communicator Batch Insert Firmfile Job DATA(number) :: " + ret);
						dfjList.clear();
						continue;
					}
					if (i % 20000 == 0) {
						int ret = dataFwuJobDao.batchInsert(dfjList);
						System.out.println("Asset Communicator Batch Insert Firmfile Job DATA(number) :: " + ret);
						dfjList.clear();
						continue;
					}
				}
			} else {
				AssetMeter meter = new AssetMeter();
				meter.setManufacturer(manufacturerId);
				meter.setModel(deviceModel);
				meter.setCurrentVesionArray(currentVesionArray);
				meter.setSn(sn);
				List<AssetMeter> mList = assetMeterService.getList(meter);
				if (mList == null || mList.size() == 0) {
					j.setErrorMsg("No devices found!");
					return j;
				}
				dataFwuPlanDao.insert(dfp); //保存升级计划
				j.setObj(planId);
				List<DataFwuJob> dfjList = new ArrayList<DataFwuJob>();
				int size = mList.size();
				int k = 1;	//用于计算固件升级进度条的数据
				/**
				 * 创建插入job的临时实体
				 */
				TempJob tempJob = new TempJob();
				tempJob.setPlanId(planId);
				tempJob.setDeviceType(deviceType);
				tempJob.setNewVersion(newVersion);
				tempJob.setManufacturerId(manufacturerId);
				tempJob.setDeviceModel(deviceModel);
				tempJob.setCurrentVesionArray(currentVesionArray);
				tempJob.setSn(sn);
				int ret = dataFwuJobDao.batchInsertJob(tempJob);
			}
			// 在这里下发创建固件升级的执行
			try {
				EndDeviceControlsResponseMessageType resMsg = createEndDeviceControls(dfp, sysServiceAttributeService);
				System.out.println("Create firmware upgrade Plan return result === > " + XMLUtil.convertToXml(resMsg));
			} catch (Exception e) {
				e.printStackTrace();
			}
		} else if (deviceType.equals("2")||deviceType.equals("4")||deviceType.equals("7")||deviceType.equals("8")||deviceType.equals("9")) {
			// 查询集中器集合
			AssetCommunicator c = new AssetCommunicator();
			c.setManufacturer(manufacturerId);
			c.setModel(deviceModel);
			c.setCurrentVesionArray(currentVesionArray);
			c.setSn(sn);
			List<AssetCommunicator> cList = assetCommunicatorService.getList(c);
			if (cList == null || cList.size() == 0) {
				j.setErrorMsg("No devices found!");
				return j;
			}
			
			dataFwuPlanDao.insert(dfp);
			j.setObj(planId);
			List<DataFwuJob> dfjList = new ArrayList<DataFwuJob>();
			int size = cList.size();
			for (int i = 1; i <= size; i++) {
				AssetCommunicator ac = cList.get(i - 1);
				DataFwuJob dfj = new DataFwuJob();
				dfj.setId(UUIDGenerator.generate());
				dfj.setPlanId(planId);
				dfj.setDeviceId(ac.getId());
				dfj.setDeviceType(deviceType);
				dfj.setCurrentVesion(ac.getFwVersion());
				dfj.setNewVersion(newVersion);
				dfj.setState("4");// 4: Waiting - apps
				dfj.setLastExecTime(null);
				dfj.setFailedReason("");
				dfj.setFwuStep("");
				dfj.setBlockSize(null);
				dfj.setBlockCount(null);
				dfj.setFwuStep(null); 
				dfjList.add(dfj);
				if (i == size) {
					int ret = dataFwuJobDao.batchInsert(dfjList);
					System.out.println("Asset Communicator Batch Insert Firmfile Job DATA(number) :: " + ret);
					dfjList.clear();
					continue;
				}
				if (i % 20000 == 0) {
					int ret = dataFwuJobDao.batchInsert(dfjList);
					System.out.println("Asset Communicator Batch Insert Firmfile Job DATA(number) :: " + ret);
					dfjList.clear();
					continue;
				}
			}
			// 在这里下发创建固件升级的执行
			try {
				EndDeviceControlsResponseMessageType resMsg = createEndDeviceControls(dfp, sysServiceAttributeService);
				System.out.println("创建固件升级Plan返回结果===" + XMLUtil.convertToXml(resMsg));
			} catch (Exception e) {
				e.printStackTrace();
			}
		} else {
			j.setErrorMsg("Device Type param error!");
			return j;
		}
		j.setMsg("Created success! The newly created plan can be viewed in Plan Report tab.");
		return j;
	}

	private EndDeviceControlsResponseMessageType createEndDeviceControls(
			DataFwuPlan p, SysServiceAttributeService sysServiceAttributeService) throws FaultMessage {
		//接口实例化
		RequestEndDeviceControlsPort port = (RequestEndDeviceControlsPort) UciInterfaceUtil
        		.getInterface("RequestEndDeviceControlsPort", RequestEndDeviceControlsPort.class, sysServiceAttributeService);
		EndDeviceControlsRequestMessageType reqMsg = new EndDeviceControlsRequestMessageType();
		SysUser su = TokenManager.getToken();
		HeaderType header = new HeaderType();
		Date date = new Date();
		header.setVerb("create");
		header.setNoun("EndDeviceControls");
		header.setTimestamp(DateUtils.dateToXmlDate(date));
		header.setSource("ClouESP HES");
		header.setAsyncReplyFlag(false);
		header.setReplyAddress("");
		header.setAckRequired(true);
		UserType u = new UserType();
		u.setOrganization(su.getOrgId());
		u.setUserID(su.getId());
		header.setUser(u);
		header.setMessageID(DateUtils.formatDate("yyyyMMdd") + su.getId());
		header.setCorrelationID(DateUtils.formatDate("yyyyMMdd") + su.getId());
		reqMsg.setHeader(header);
		EndDeviceControlsPayloadType payload = new EndDeviceControlsPayloadType();
		EndDeviceControls endDeviceControls = new EndDeviceControls();
		List<EndDeviceControl> endDeviceControl = endDeviceControls.getEndDeviceControl();
		EndDeviceControl edc = new EndDeviceControl();
		edc.setMRID(p.getId());			// 放的固件升级计划主键ID
		edc.setReason("periodicity");	// immediately 立即开始， periodicity
		EndDeviceControlType edct = new EndDeviceControlType();
		edct.setRef("********");
		edc.setEndDeviceControlType(edct);
		List<EndDeviceControl.EndDevices> endDevices = edc.getEndDevices();
		EndDevices ed = new EndDevices();
		List<EndDeviceControl.EndDevices.Names> names = ed.getNames();
		Names name1 = new Names();
		name1.setName(DateUtils.date2Str(p.getStartTime(), DateUtils.date_sdf));
		NameType nt1 = new NameType();
		nt1.setName("plan start time");
		name1.setNameType(nt1);
		Names name2 = new Names();
		name2.setName(DateUtils.date2Str(p.getExpiryTime(), DateUtils.date_sdf));
		NameType nt2 = new NameType();
		nt2.setName("plan end time");
		name2.setNameType(nt2);
		Names name3 = new Names();
		name3.setName(DateUtils.date2Str(p.getTaskStartTime(), DateUtils.HHmmss));
		NameType nt3 = new NameType();
		nt3.setName("job start time");
		name3.setNameType(nt3);
		Names name4 = new Names();
		name4.setName(DateUtils.date2Str(p.getTaskEndTime(), DateUtils.HHmmss));
		NameType nt4 = new NameType();
		nt4.setName("job end time");
		name4.setNameType(nt4);
		Names name5 = new Names();
		name5.setName(p.getTaskCycle().toString());
		NameType nt5 = new NameType();
		nt5.setName("task cycle");
		name5.setNameType(nt5);
		names.add(name1);
		names.add(name2);
		names.add(name3);
		names.add(name4);
		names.add(name5);
		endDevices.add(ed);
		endDeviceControl.add(edc);
		payload.setEndDeviceControls(endDeviceControls);
		reqMsg.setPayload(payload);
		System.out.println("XML格式==" + XMLUtil.convertToXml(reqMsg));
//		EndDeviceControlsResponseMessageType resMsg = port.cancelEndDeviceControls(reqMsg);// result OK就是成功
		EndDeviceControlsResponseMessageType resMsg = port.createEndDeviceControls(reqMsg);// result OK就是成功
		return resMsg;
	}

	@Override
	public AjaxJson cretePlanAgain(DataFwuPlan plan, String oldPlanId,	SysServiceAttributeService sysServiceAttributeService) {
		AjaxJson j = new AjaxJson();
		String valityMsg = ValidationUtils.validateObj(plan);
		if (StringUtil.isNotEmpty(valityMsg)) {
			j.setErrorMsg(valityMsg);
			return j;
		}
		// 比对日期大小
		Date startTime = plan.getStartTime();
		Date expiryTime = plan.getExpiryTime();
		if (!startTime.before(expiryTime)) {
			j.setErrorMsg(MutiLangUtil.doMutiLang("meterGroupUpgrade.startEqualExpiry"));
			return j;
		}
		Date d = new Date();
		Date taskStartTime = plan.getTaskStartTime();
		Date taskEndtime = plan.getTaskEndTime();
		taskStartTime = DateUtils.str2Date(DateUtils.date2Str(d, DateUtils.date_sdf) 
				+ " " + DateUtils.date2Str(taskStartTime, DateUtils.HHmmss), DateUtils.datetimeFormat);
		taskEndtime = DateUtils.str2Date(DateUtils.date2Str(d, DateUtils.date_sdf) 
				+ " " + DateUtils.date2Str(taskEndtime, DateUtils.HHmmss), DateUtils.datetimeFormat);
//		if (taskEndtime.before(taskStartTime)) {
//			j.setErrorMsg(MutiLangUtil.doMutiLang("meterGroupUpgrade.taskStartEqualEnd"));
//			return j;
//		}
		String planId = UUIDGenerator.generate();//升级计划ID
		plan.setId(planId);
		try {
			dataFwuPlanDao.insert(plan);					//保存升级计划
			j.setObj(planId);
			Map<String, Object> extData = new HashMap<String, Object>();
			extData.put("oldPlanId", oldPlanId);	//设置旧的升级计划ID
			plan.setExtData(extData);
			dataFwuJobDao.batchInsert_cretePlanAgainJob(plan);			//批量插入job
			// 在这里下发创建固件升级的执行
			try {
				EndDeviceControlsResponseMessageType resMsg = createEndDeviceControls(plan, sysServiceAttributeService);
				System.out.println("Create firmware upgrade Plan return result === > " + XMLUtil.convertToXml(resMsg));
			} catch (Exception e) {
				e.printStackTrace();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		j.setMsg(MutiLangUtil.doMutiLang("meterGroupUpgrade.createPlanSucc"));
		return j;
	}
	
	
	
}