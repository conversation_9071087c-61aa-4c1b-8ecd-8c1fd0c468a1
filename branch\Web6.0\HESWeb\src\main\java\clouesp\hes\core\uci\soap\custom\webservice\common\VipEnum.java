package clouesp.hes.core.uci.soap.custom.webservice.common;


/**
 * @ClassName: BaudType
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年8月7日 上午10:39:41
 *
 */
public enum VipEnum {
	
	VIPTYPE_0(0,"普通用户"),
	VIPTYPE_1(1,"重点用户");

	
	private VipEnum(int index,String vipType) {
		this.index = index;
		this.vipType = vipType;
	}
	
	public static VipEnum parseVip(String vipType) {
		for (VipEnum type : values()) {
			if(type.vipType.equals(vipType))
				return type;
		}
		return null;
	}
	
	private int index;
	private String vipType;
	
	public int getIndex() {
		return index;
	}
	public String getVipType() {
		return vipType;
	}
	
	
}
