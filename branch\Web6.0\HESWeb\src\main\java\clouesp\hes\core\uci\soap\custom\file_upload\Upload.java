
package clouesp.hes.core.uci.soap.custom.file_upload;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>upload complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="upload"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="file" type="{http://file_upload.custom.soap.uci.core.hes.clouesp/}cxfFileWrapper" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "upload", propOrder = {
    "file"
})
public class Upload {

    protected CxfFileWrapper file;

    /**
     * 获取file属性的值。
     * 
     * @return
     *     possible object is
     *     {@link CxfFileWrapper }
     *     
     */
    public CxfFileWrapper getFile() {
        return file;
    }

    /**
     * 设置file属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link CxfFileWrapper }
     *     
     */
    public void setFile(CxfFileWrapper value) {
        this.file = value;
    }

}
