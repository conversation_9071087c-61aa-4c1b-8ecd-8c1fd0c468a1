/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class PpmAssetTariffGroup{ } 
 * 
 * 摘    要： ppmAssetTariffGroup
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2019-09-25 03:56:32
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.tariff;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;

import com.clou.esp.hes.app.web.model.tariff.PpmAssetTariffGroup;
import com.clou.esp.hes.app.web.service.tariff.PpmAssetTariffGroupService;

/**
 * <AUTHOR>
 * @时间：2019-09-25 03:56:32
 * @描述：ppmAssetTariffGroup类
 */
@Controller
@RequestMapping("/ppmAssetTariffGroupController")
public class PpmAssetTariffGroupController extends BaseController{

 	@Resource
    private PpmAssetTariffGroupService ppmAssetTariffGroupService;

	/**
	 * 跳转到ppmAssetTariffGroup列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/tariff/ppmAssetTariffGroupList");
    }

	/**
	 * 跳转到ppmAssetTariffGroup新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "ppmAssetTariffGroup")
	public ModelAndView ppmAssetTariffGroup(PpmAssetTariffGroup ppmAssetTariffGroup,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(ppmAssetTariffGroup.getId())){
			try {
                ppmAssetTariffGroup=ppmAssetTariffGroupService.getEntity(ppmAssetTariffGroup.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("ppmAssetTariffGroup", ppmAssetTariffGroup);
		}
		return new ModelAndView("/tariff/ppmAssetTariffGroup");
	}


	/**
	 * ppmAssetTariffGroup查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=ppmAssetTariffGroupService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除ppmAssetTariffGroup信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(PpmAssetTariffGroup ppmAssetTariffGroup,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(ppmAssetTariffGroupService.deleteById(ppmAssetTariffGroup.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存ppmAssetTariffGroup信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })PpmAssetTariffGroup ppmAssetTariffGroup,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        PpmAssetTariffGroup t=new  PpmAssetTariffGroup();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(ppmAssetTariffGroup.getId())){
        	t=ppmAssetTariffGroupService.getEntity(ppmAssetTariffGroup.getId());
			MyBeanUtils.copyBeanNotNull2Bean(ppmAssetTariffGroup, t);
				ppmAssetTariffGroupService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            ppmAssetTariffGroupService.save(ppmAssetTariffGroup);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
	
	
	
}