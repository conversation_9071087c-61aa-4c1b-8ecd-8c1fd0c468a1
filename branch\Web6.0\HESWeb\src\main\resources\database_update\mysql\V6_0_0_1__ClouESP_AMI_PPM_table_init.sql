  CREATE TABLE PPM_ASSET_METER 
   (	
    ID VARCHAR(32) NOT NULL, 
	STS_MAC VARCHAR(20), 
	SGC VARCHAR(20), 
	DEPLOY_TV datetime, 
	PAYMENT_TYPE DECIMAL(4,0) DEFAULT 1,
	PRIMARY KEY (ID)
   ) ;

  CREATE TABLE PPM_ASSET_METER_CHANGE 
   (	
    CUSTOMER_ID VARCHAR(32) NOT NULL, 
	TIME_TAG datetime COMMENT '换表时间' NOT NULL, 
	OLD_METER_ID VARCHAR(32) COMMENT '旧表ID' NOT NULL, 
	NEW_METER_ID VARCHAR(32) COMMENT '新表ID' NOT NULL, 
	OLD_SERIAL_NUMBER VARCHAR(32) COMMENT '旧表资产编号' NOT NULL, 
	NEW_SERIAL_NUMBER VARCHAR(32) COMMENT '新表资产编号' NOT NULL, 
	DATA_SOURCE VARCHAR(20) COMMENT '数据来源: 默认为 CLouESP PPM' NOT NULL, 
	OLD_COMBINED_ACTIVE_ENERGY DECIMAL(18,4) DEFAULT '0.0000' COMMENT '旧表_正向有功总示度' NOT NULL, 
	OLD_TOTAL_IMPORT_ACTIVE DECIMAL(18,4) DEFAULT NULL COMMENT '旧表_反向有功总示度', 
	OLD_TOTAL_EXPORT_ACTIVE DECIMAL(18,4) DEFAULT NULL COMMENT '旧表_正向无功总示度', 
	OLD_TOTAL_IMPORT_REACTIVE DECIMAL(18,4) DEFAULT NULL, 
	OLD_TOTAL_EXPORT_REACTIVE DECIMAL(18,4) DEFAULT NULL, 
	OLD_IMPORT_ACTIVE_TARIFF1 DECIMAL(18,4) DEFAULT NULL, 
	OLD_IMPORT_ACTIVE_TARIFF2 DECIMAL(18,4) DEFAULT NULL, 
	OLD_IMPORT_ACTIVE_TARIFF3 DECIMAL(18,4) DEFAULT NULL, 
	OLD_IMPORT_ACTIVE_TARIFF4 DECIMAL(18,4) DEFAULT NULL, 
	OLD_EXPORT_ACTIVE_TARIFF1 DECIMAL(18,4) DEFAULT NULL, 
	OLD_EXPORT_ACTIVE_TARIFF2 DECIMAL(18,4) DEFAULT NULL, 
	OLD_EXPORT_ACTIVE_TARIFF3 DECIMAL(18,4) DEFAULT NULL, 
	OLD_EXPORT_ACTIVE_TARIFF4 DECIMAL(18,4) DEFAULT NULL, 
	OLD_IMPORT_REACTIVE_TARIFF1 DECIMAL(18,4) DEFAULT NULL, 
	OLD_IMPORT_REACTIVE_TARIFF2 DECIMAL(18,4) DEFAULT NULL, 
	OLD_IMPORT_REACTIVE_TARIFF3 DECIMAL(18,4) DEFAULT NULL, 
	OLD_IMPORT_REACTIVE_TARIFF4 DECIMAL(18,4) DEFAULT NULL, 
	OLD_EXPORT_REACTIVE_TARIFF1 DECIMAL(18,4) DEFAULT NULL, 
	OLD_EXPORT_REACTIVE_TARIFF2 DECIMAL(18,4) DEFAULT NULL, 
	OLD_EXPORT_REACTIVE_TARIFF3 DECIMAL(18,4) DEFAULT NULL, 
	OLD_EXPORT_REACTIVE_TARIFF4 DECIMAL(18,4) DEFAULT NULL, 
	NEW_COMBINED_ACTIVE_ENERGY DECIMAL(18,4) DEFAULT '0.0000' COMMENT '新表_组合有功用电量' NOT NULL, 
	NEW_TOTAL_IMPORT_ACTIVE DECIMAL(18,4) DEFAULT NULL COMMENT '新表_正向有功总示度', 
	NEW_TOTAL_EXPORT_ACTIVE DECIMAL(18,4) DEFAULT NULL  COMMENT '新表_反向有功总示度', 
	NEW_TOTAL_IMPORT_REACTIVE DECIMAL(18,4) DEFAULT NULL, 
	NEW_TOTAL_EXPORT_REACTIVE DECIMAL(18,4) DEFAULT NULL, 
	NEW_IMPORT_ACTIVE_TARIFF1 DECIMAL(18,4) DEFAULT NULL, 
	NEW_IMPORT_ACTIVE_TARIFF2 DECIMAL(18,4) DEFAULT NULL, 
	NEW_IMPORT_ACTIVE_TARIFF3 DECIMAL(18,4) DEFAULT NULL, 
	NEW_IMPORT_ACTIVE_TARIFF4 DECIMAL(18,4) DEFAULT NULL, 
	NEW_EXPORT_ACTIVE_TARIFF1 DECIMAL(18,4) DEFAULT NULL, 
	NEW_EXPORT_ACTIVE_TARIFF2 DECIMAL(18,4) DEFAULT NULL, 
	NEW_EXPORT_ACTIVE_TARIFF3 DECIMAL(18,4) DEFAULT NULL, 
	NEW_EXPORT_ACTIVE_TARIFF4 DECIMAL(18,4) DEFAULT NULL, 
	NEW_IMPORT_REACTIVE_TARIFF1 DECIMAL(18,4) DEFAULT NULL, 
	NEW_IMPORT_REACTIVE_TARIFF2 DECIMAL(18,4) DEFAULT NULL, 
	NEW_IMPORT_REACTIVE_TARIFF3 DECIMAL(18,4) DEFAULT NULL, 
	NEW_IMPORT_REACTIVE_TARIFF4 DECIMAL(18,4) DEFAULT NULL, 
	NEW_EXPORT_REACTIVE_TARIFF1 DECIMAL(18,4) DEFAULT NULL, 
	NEW_EXPORT_REACTIVE_TARIFF2 DECIMAL(18,4) DEFAULT NULL, 
	NEW_EXPORT_REACTIVE_TARIFF3 DECIMAL(18,4) DEFAULT NULL, 
	NEW_EXPORT_REACTIVE_TARIFF4 DECIMAL(18,4) DEFAULT NULL, 
	ABNORMAL_PERIOD_COMBINED_ACTIVE_ENERGY DECIMAL(18,4)  NOT NULL DEFAULT '0.0000' COMMENT '表异常期间组合有功用电量--(电表故障开始到发现故障换新表这段时间客户使用的组合有功电量)', 
	OLD_LAST_SURPLUS_MONEY DECIMAL(18,2) DEFAULT NULL COMMENT '旧表剩余金额', 
	PROCESSING_TYPE DECIMAL(4,0) DEFAULT '2'  NOT NULL COMMENT '处理类别: 字典表90 1=已成功处理,2=未处理,3=处理失败, 4=作废', 
	CREATE_DATE datetime DEFAULT CURRENT_TIMESTAMP COMMENT '建立时间', 
	SAVE_DB_DATE datetime DEFAULT CURRENT_TIMESTAMP COMMENT '数据存入数据库时间', 
	README VARCHAR(256) DEFAULT NULL COMMENT '备注', 
	LAST_SURPLUS_MONEY DECIMAL(10,2) DEFAULT NULL, 
	CHANGE_TYPE DECIMAL(4,0) DEFAULT NULL,
	PRIMARY KEY (CUSTOMER_ID, TIME_TAG)
   ) ;
   
  CREATE TABLE PPM_ASSET_TARIFF_GROUP 
   (	
    ID VARCHAR(32) NOT NULL, 
	NAME VARCHAR(64), 
	TYPE DECIMAL(4,0) COMMENT '1=单费率  2=阶梯费率  3=分时电价', 
	ACTIVATE_TV datetime, 
	DESCR VARCHAR(256),
	PRIMARY KEY (ID)
   ) ;

  CREATE TABLE PPM_ASSET_TARIFF_STEP 
   (	
    GROUP_ID VARCHAR(32) NOT NULL, 
	STEP_INDEX DECIMAL(4,0) NOT NULL COMMENT '1-10', 
	START_QUANTITY DECIMAL(10,4), 
	END_QUANTITY DECIMAL(10,4), 
	PRICE DECIMAL(10,4),
	PRIMARY KEY (GROUP_ID, STEP_INDEX)
   ) ;

  CREATE TABLE PPM_ASSET_TARIFF_STEP_DETAIL 
   (	
    GROUP_ID VARCHAR(32) NOT NULL, 
	STEP_INDEX DECIMAL(4,0) NOT NULL, 
	TAX_TYPE DECIMAL(4,0) NOT NULL COMMENT '1=街道照明税费   2=政府税费  3=政府津贴  4=补贴  5=增值税  6=国家保险计划征税  7=增值税作补贴  8=服务费  9=补助1  10=补助2    11=税费%*用电量', 
	TARIFF_NAME VARCHAR(256), 
	THE_FORMULA VARCHAR(256), 
	VARIABLE_VALUE DECIMAL(10,4), 
	CHARGE_MODE DECIMAL(4,0) COMMENT '1=按月收取  2=按次收取', 
	DESCR VARCHAR(256), 
	CALCULATED VARCHAR(20) COMMENT '1=是(参与计算)   2=否(不参与计算) 特殊情况',
	PRIMARY KEY (GROUP_ID, STEP_INDEX, TAX_TYPE)
   ) ;

  CREATE TABLE PPM_CUSTOMER_HISTORY_TARIFF 
   (	
    ID VARCHAR(32) NOT NULL, 
	TYPE DECIMAL(4,0) NOT NULL, 
	GROUP_ID VARCHAR(32), 
	ACTIVE_TV datetime NOT NULL,
	PRIMARY KEY (ID, ACTIVE_TV)
   ) ;

  CREATE TABLE PPM_CUSTOMER_TARIFF_MAP 
   (	
    ID VARCHAR(32) NOT NULL, 
	TYPE DECIMAL(4,0) NOT NULL COMMENT '1=单费率  2=阶梯费率  3=分时电价', 
	GROUP_ID VARCHAR(32) COMMENT '当前使用费率',
	PRIMARY KEY (ID)
   ) ;

  CREATE TABLE PPM_DATA_PREPAID_PROGRESS 
   (	
    CUSTOMER_ID VARCHAR(32) NOT NULL, 
	LAST_PAYMENT_TV datetime NOT NULL COMMENT '上次充值时间', 
	METER_COMMISSIONING_DATE datetime NOT NULL COMMENT '客户与电表的关联时间',
	PRIMARY KEY (CUSTOMER_ID)
   ) ;

  CREATE TABLE PPM_DATA_STATISTICS_CUSTOMER 
   (	
    ORG_ID VARCHAR(32) NOT NULL, 
	TV datetime NOT NULL, 
	TV_TYPE DECIMAL(4,0) NOT NULL COMMENT '1:日 2：月', 
	DATA_TYPE DECIMAL(10,2) NOT NULL COMMENT '1:Sale 2 Consumption', 
	TOTAL_VALUE DECIMAL(12,2),
	PRIMARY KEY (ORG_ID, TV, TV_TYPE, DATA_TYPE)
   ) ;

  CREATE TABLE PPM_DATA_USER_LOG 
   (	
    TV datetime NOT NULL, 
	USER_ID VARCHAR(32) NOT NULL, 
	LOG_TYPE VARCHAR(64) NOT NULL, 
	LOG_SUB_TYPE VARCHAR(64) NOT NULL, 
	DETAIL VARCHAR(256),
	PRIMARY KEY (TV, USER_ID, LOG_TYPE, LOG_SUB_TYPE)
   );

  CREATE TABLE PPM_DICT_DETAIL 
   (	
    DICT_ID VARCHAR(32) NOT NULL, 
	INNER_VALUE DECIMAL(11,0) NOT NULL COMMENT '内部值(程序引用序号)', 
	GUI_DISPLAY_NAME VARCHAR(256) COMMENT '字典明细名称', 
	README VARCHAR(256),
	PRIMARY KEY (DICT_ID, INNER_VALUE)
   ) COMMENT 'zzz_dict_detail 字典明细表';

  CREATE TABLE PPM_DICT_FUNCTION 
   (	
    ID VARCHAR(32) NOT NULL, 
	FUNCTIONNAME VARCHAR(64) NOT NULL, 
	FUNCTIONURL VARCHAR(128), 
	FUNCTION_INTRO VARCHAR(128),
	PRIMARY KEY (ID)
   ) ;

  CREATE TABLE PPM_DICT_OPERATION 
   (	
    ID VARCHAR(32) NOT NULL, 
	OPERATIONNAME VARCHAR(50), 
	FUNCTION_ID VARCHAR(32), 
	OPERATIONURL VARCHAR(128), 
	ISHIDE DECIMAL(4,0), 
	DESCRIPTION VARCHAR(255),
	PRIMARY KEY (ID)
   ) ;

  CREATE TABLE PPM_DICT_USER_LOG 
   (	
    LOG_TYPE VARCHAR(64) NOT NULL, 
	LOG_SUB_TYPE VARCHAR(1024) NOT NULL, 
	SORT_ID DECIMAL
   ) ;

  CREATE TABLE PPM_INTEGRATION_LOG 
   (	
    ID VARCHAR(32) NOT NULL, 
	METER_ID VARCHAR(32), 
	SERIAL_NUMBER VARCHAR(32), 
	TV datetime, 
	DATA_SOURCE VARCHAR(32) COMMENT '数据来源(这里记录的是 数据源系统的名称 例如：ClouESP_HES 或 ClouESP_PPM )', 
	MESSAGE_TYPE VARCHAR(64) COMMENT '消息类型 (这里记录的是 IEC61968-9接口执行的功能名称)', 
	LOG_TYPE VARCHAR(10) COMMENT '日志类型（固定数据类型：Tx 为 发送的数据  Rx 为接收的数据）', 
	MESSAGE_SUB_TYPE VARCHAR(64) COMMENT '消息子类型（这里记录的是 IEC61968-9接口执行的功能名称）', 
	MESSAGE_ID VARCHAR(128), 
	MESSAGE_RESULT VARCHAR(64), 
	MESSAGE_DETAIL VARCHAR(4000), 
	SAVE_DB_DATE datetime DEFAULT CURRENT_TIMESTAMP, 
	README VARCHAR(256),
	PRIMARY KEY (ID)
   ) COMMENT 'PPM_INTEGRATION_LOG 接口通讯日志';

  CREATE TABLE PPM_SGC_KEYFILE 
   (	
    DEVICE_ID VARCHAR(32) NOT NULL, 
	SUPPLY_GROUP_CODE VARCHAR(32) NOT NULL, 
	SUPPLY_GROUP_NAME VARCHAR(256), 
	IS_DEFAULT DECIMAL(2,0) DEFAULT 0, 
	KEY_REGISTER_NUMBER VARCHAR(20), 
	KEY_REVISION_NUMBER VARCHAR(1), 
	KEY_EXPIRY_NUMBER VARCHAR(4),
	PRIMARY KEY (DEVICE_ID, SUPPLY_GROUP_CODE)
   ) ;

  CREATE TABLE PPM_SYS_MENU 
   (	
    ID VARCHAR(32) NOT NULL, 
	UTILITY_IDS VARCHAR(600), 
	MENULEVEL DECIMAL(6,0), 
	MENUNAME VARCHAR(50) NOT NULL, 
	MENUORDER DECIMAL(11,0), 
	FUNCTIONURL VARCHAR(100), 
	PARENTMENUID VARCHAR(32), 
	MENU_INTRO VARCHAR(300), 
	FUNCTIONID VARCHAR(32), 
	HIDE_TAB VARCHAR(64),
	PRIMARY KEY (ID)
   ) ;

  CREATE TABLE PPM_SYS_ROLE 
   (	
    ID VARCHAR(32) NOT NULL, 
	UTILITY_ID VARCHAR(32), 
	NAME VARCHAR(64) NOT NULL, 
	DESCRIPTION VARCHAR(256),
	PRIMARY KEY (ID)
   ) ;

  CREATE TABLE PPM_SYS_ROLE_MENU 
   (	
    ROLE_ID VARCHAR(32) NOT NULL, 
	MENU_ID VARCHAR(32) NOT NULL, 
	OPERATION VARCHAR(1000),
	PRIMARY KEY (ROLE_ID, MENU_ID)
   ) ;

  CREATE TABLE PPM_SYS_USER 
   (	
    ID VARCHAR(32) NOT NULL, 
	UTILITY_ID VARCHAR(32), 
	ORG_ID VARCHAR(500), 
	ROLE_ID VARCHAR(32), 
	NAME VARCHAR(64) NOT NULL, 
	USERNAME VARCHAR(32) NOT NULL, 
	PASSWORD VARCHAR(32) NOT NULL, 
	EMAIL VARCHAR(64), 
	MOBILE_PHONE VARCHAR(32), 
	PROFILE_FILE VARCHAR(32), 
	USER_TYPE DECIMAL(4,0), 
	SIGNATURE longblob, 
	USERKEY VARCHAR(200), 
	DELETE_FLAG DECIMAL(6,0), 
	USER_STATE DECIMAL(4,0), 
	LAST_LOGIN_TIME datetime, 
	STATION_ID VARCHAR(32),
	PRIMARY KEY (ID)
   ) ;

  CREATE TABLE PPM_TOTAL_MONTH_ELECTRICITY_CONSUMPTION 
   (	
    ID VARCHAR(32) NOT NULL, 
	ORG_ID VARCHAR(32) NOT NULL, 
	MONTHS VARCHAR(32), 
	MONTH_ELECTRICITY VARCHAR(32), 
	CREATE_DATE datetime, 
	SAVE_DB_DATE datetime, 
	README VARCHAR(200),
	PRIMARY KEY (ID)
   ) ;

  CREATE TABLE PPM_TOTAL_MONTH_SALES_AMOUNT 
   (	
    ID VARCHAR(32) NOT NULL, 
	ORG_ID VARCHAR(32), 
	MONTHS VARCHAR(32), 
	MONTH_AMOUNT VARCHAR(32), 
	CREATE_DATE datetime, 
	SAVE_DB_DATE datetime DEFAULT CURRENT_TIMESTAMP, 
	README VARCHAR(200),
	PRIMARY KEY (ID)
   ) ;

  CREATE TABLE PPM_VEND_CUSTOMER_DEBT 
   (	
    CUSTOMER_ID VARCHAR(32) NOT NULL COMMENT '客户ID', 
	ID VARCHAR(32)  NOT NULL COMMENT '债务ID', 
	DEBT_DATE datetime NOT NULL COMMENT '日期', 
	DEBT_TOTAL DECIMAL(14,2) DEFAULT NULL COMMENT '债务总额', 
	DEBT_PERIOD DECIMAL(3,0) DEFAULT NULL COMMENT '分期期数', 
	PAYED_PERIOD DECIMAL(3,0) DEFAULT NULL COMMENT '已还期数', 
	LEFT_PERIOD DECIMAL(3,0) DEFAULT NULL COMMENT '剩余期数', 
	MONEY_PERIOD DECIMAL(14,2) DEFAULT NULL COMMENT '每期还款金额', 
	DEBT_MONEY DECIMAL(14,2) DEFAULT NULL COMMENT '剩余欠款', 
	PAYED_TOTAL DECIMAL(14,2) DEFAULT NULL COMMENT '已还总额', 
	DIVIDE_REMAINDER DECIMAL(14,2) DEFAULT '0.00' COMMENT '债务零头，总额除分期期数的债务余数---债务尾款', 
	DEBT_TYPE DECIMAL(2,0) COMMENT '债务类型.字典表 74：1=初始债务 2=换表债务 3=其它债务', 
	RECORD_STATE DECIMAL(2,0) NOT NULL COMMENT '记录状态：字典表 75： 1= 正常.2=作废', 
	DEBT_COLLECT_MONTH DECIMAL(2,0) NOT NULL COMMENT '债务收取开始时间(字典表88 :1=当月开始收取,2=下月开始收取)', 
	DEBT_COLLECT_DATE datetime NOT NULL COMMENT '债务开始收取时间 当售电的时候扣取按这个时间的月份开始进行扣取', 
	USER_ID VARCHAR(32) DEFAULT NULL COMMENT '操作员ID', 
	README VARCHAR(256) DEFAULT NULL,
	PRIMARY KEY (ID)
   ) COMMENT 'ppm_vend_meter_debt 电表债务历史记录表';

  CREATE TABLE PPM_VEND_CUSTOMER_DEBT_RESTORE 
   (	
    CUSTOMER_ID VARCHAR(32) NOT NULL, 
	ID VARCHAR(32) NOT NULL, 
	METER_DEBT_ID VARCHAR(32) NOT NULL, 
	DEBT_PAYED_PERIOD DECIMAL(3,0) DEFAULT NULL, 
	DEBT_PAYED_TIME datetime DEFAULT NULL, 
	DEBT_PAYED_PERIOD_MONEY DECIMAL(14,2) DEFAULT NULL, 
	DEBT_PAYED_MONEY DECIMAL(14,2) DEFAULT NULL, 
	RECORD_STATE DECIMAL(2,0) NOT NULL, 
	SALES_ID VARCHAR(32) DEFAULT NULL, 
	README VARCHAR(256) DEFAULT NULL,
	PRIMARY KEY (ID)
   ) ;

  CREATE TABLE PPM_VEND_FREE_TOKEN_MANAGE 
   (	
    ID VARCHAR(32) NOT NULL, 
	RECEIPT_NO VARCHAR(32) DEFAULT NULL COMMENT '流水号，来源自时间戳', 
	ORG_ID VARCHAR(100) DEFAULT NULL, 
	STATION_ID VARCHAR(32) DEFAULT NULL, 
	CUSTOMER_NAME VARCHAR(256) NOT NULL, 
	METER_ID VARCHAR(32) DEFAULT NULL, 
	SERIAL_NUMBER VARCHAR(30) NOT NULL, 
	COMM_ADDRESS VARCHAR(24) NOT NULL, 
	USER_ID VARCHAR(32) DEFAULT NULL, 
	USER_NAME VARCHAR(30) NOT NULL, 
	TOKEN_TYPE VARCHAR(4) DEFAULT NULL COMMENT 'Token类型(字典类型54： 0=设置最大负荷Token 1=清除电量Token  3=充值Token 4=变更密钥Token 5=清除窃电状态Token 15=设置卸载电量Token）', 
	TOKEN VARCHAR(20) DEFAULT NULL, 
	RECEIPT_STATE DECIMAL(2,0) DEFAULT NULL COMMENT '单据状态 字典表 75： 1= 正常  2=作废', 
	TOTAL_AMOUNT DECIMAL(10,4) DEFAULT '0.0000' COMMENT '总金额', 
	FREE_TOKEN_DESCRIPTION VARCHAR(500),
	PRIMARY KEY (ID)
   ) COMMENT 'ppm_vend_free_token_manage 免费Token管理';

  CREATE TABLE PPM_VEND_HISTORICAL_INFO 
   (	
    ID VARCHAR(32) NOT NULL, 
	ORG_ID VARCHAR(100) DEFAULT NULL, 
	STATION_ID VARCHAR(32) DEFAULT NULL, 
	CUSTOMER_ID VARCHAR(32) DEFAULT NULL, 
	METER_ID VARCHAR(32) NOT NULL, 
	TARIFF_SOLUTION_ID VARCHAR(32) DEFAULT NULL, 
	USER_ID VARCHAR(32) NOT NULL, 
	USER_NAME VARCHAR(50) DEFAULT NULL, 
	SALES_DATE datetime DEFAULT NULL, 
	PAYMENT_TYPE VARCHAR(20) DEFAULT NULL COMMENT '支付类型 (字典表 73：1=现金 2=支票 3=信用卡 4=借记卡) ', 
	CUSTOMER_PAYMENT_AMOUNT DECIMAL(10,4) DEFAULT '0.0000' COMMENT '客户付款', 
	DEBT_ID VARCHAR(32) DEFAULT NULL COMMENT '收取债务记录ID', 
	PAYMENT_DEBT_AMOUNT DECIMAL(10,4) DEFAULT '0.0000' COMMENT '收取债务金额', 
	TAXES DECIMAL(10,4) DEFAULT '0.0000' COMMENT '税金', 
	NET_AMOUNT DECIMAL(10,4) DEFAULT '0.0000' COMMENT '净金额', 
	OTHER_EXPENSES_TOTAL_AMOUNT DECIMAL(10,4) DEFAULT NULL COMMENT '其它费用总金额(包括手续费等等其他费用的总和)', 
	REATER_ID VARCHAR(32) DEFAULT NULL COMMENT '费率切换--退款编号', 
	TOTAL_RATE_MONEY DECIMAL(10,4) DEFAULT NULL COMMENT '费率切换--退款总金额', 
	LAST_PAYMENT_DATE datetime DEFAULT NULL COMMENT '上次售电缴费时间', 
	IS_EMERGENCY_SELL_ELECTRICITY VARCHAR(2) DEFAULT '0' COMMENT '是否紧急售电 0：否 1：是', 
	IS_MONTH_FIRST DECIMAL(1,0) DEFAULT '0'  COMMENT '是否为本月第一次售电 0：否 1：是', 
	IS_FULFILL VARCHAR(1) DEFAULT NULL COMMENT '充值记录是否完成 (0=未完成 1=已完成)', 
	RECEIPT_STATE DECIMAL(2,0) DEFAULT NULL COMMENT '单据状态：0 正常数据  1 过期数据（被卸载的记录）', 
	RECHARGE_TYPE DECIMAL(2,0) DEFAULT NULL COMMENT '充值类型，0表示正常充值，1表示卸载。', 
	UNINSTALL_AMOUNT DECIMAL(10,4) DEFAULT '0.0000' COMMENT '卸载金额', 
	CORRECT_README VARCHAR(512) DEFAULT NULL COMMENT '卸载电量的备注', 
	ORIGINAL_SALES_ID VARCHAR(32) DEFAULT NULL COMMENT '原始售电ID', 
	UPDATE_DATE datetime DEFAULT NULL COMMENT '最后一次修改时间', 
	UPDATE_USER_ID VARCHAR(20) DEFAULT NULL COMMENT '最后修改用户ID', 
	UPDATE_USER_NAME VARCHAR(50) DEFAULT NULL, 
	SAVE_DB_DATE datetime DEFAULT CURRENT_TIMESTAMP, 
	README VARCHAR(256) DEFAULT NULL, 
	CHEQUE_NO VARCHAR(100) DEFAULT NULL COMMENT '支票号码',
	PRIMARY KEY (ID)
   ) COMMENT 'ppm_vend_historical_info 售电历史记录';

  CREATE TABLE PPM_VEND_INITIAL_CREDIT_AMOUNT 
   (	
    ID VARCHAR(32) NOT NULL, 
	METER_ID VARCHAR(32) DEFAULT NULL, 
	INIT_CREDIT_AMOUNT DECIMAL(10,0) DEFAULT '0' COMMENT '预付费表预置金额', 
	PAYMENT_TIME datetime DEFAULT NULL COMMENT '缴费时间', 
	STATE DECIMAL(2,0) NOT NULL COMMENT '状态：0表示未处理、1表示已经缴费处理完毕', 
	SALES_ID VARCHAR(32) DEFAULT NULL COMMENT '售电历史ID：此ID 记录的是对应收取该费用的售电记录，便于检索。', 
	CREATE_DATE datetime DEFAULT NULL COMMENT '创建时间', 
	README VARCHAR(256) DEFAULT NULL,
	PRIMARY KEY (ID)
   ) ;

  CREATE TABLE PPM_VEND_INVOICE 
   (	
    ID VARCHAR(32) NOT NULL COMMENT '售电单据打印ID', 
	RECEIPT_NO VARCHAR(32) DEFAULT NULL COMMENT '售电单据流水号', 
	SALES_ID VARCHAR(32) DEFAULT NULL COMMENT '售电ID', 
	SALES_DATE datetime DEFAULT NULL COMMENT '售电时间', 
	STATION_ID VARCHAR(32) DEFAULT NULL COMMENT '售电站ID', 
	CUSTOMER_ID VARCHAR(32) DEFAULT NULL COMMENT '客户ID', 
	CUSTOMER_NAME VARCHAR(256) DEFAULT NULL COMMENT '客户名称', 
	METER_ID VARCHAR(32) DEFAULT NULL COMMENT '电表ID', 
	SERIAL_NUMBER VARCHAR(30) DEFAULT NULL COMMENT '电表资产编号', 
	COMM_ADDRESS VARCHAR(24) DEFAULT NULL COMMENT '电表通讯地址', 
	CUSTOMER_PAYMENT_AMOUNT DECIMAL(14,2) NOT NULL COMMENT '已付款金额', 
	TOTAL_FEES DECIMAL(14,2) DEFAULT NULL COMMENT '总综合收费(包含要扣取的所有费用)', 
	RECHARGE_AMOUNT DECIMAL(14,2) NOT NULL COMMENT '充值金额(充值到电表的实际金额)', 
	VENDING_TOKEN VARCHAR(20) DEFAULT NULL COMMENT '售电Token', 
	PAYMENT_TYPE VARCHAR(50) DEFAULT NULL COMMENT '支付类型 (字典表 73：1=现金 2=支票 3=信用卡 4=借记卡) ', 
	DAYS_FROM_LAST_CHARGE DECIMAL(10,0) DEFAULT NULL COMMENT '距最后一次售电间隔天数', 
	UPDATE_DATE datetime DEFAULT NULL,
	PRIMARY KEY (ID)
   ) COMMENT 'ppm_vend_invoice 售电单据打印';

  CREATE TABLE PPM_VEND_INVOICE_DETAILS 
   (	
    ID VARCHAR(32) NOT NULL COMMENT '售电明细ID', 
	INVOICE_PRINT_ID VARCHAR(32) DEFAULT NULL COMMENT '售电单据打印ID', 
	SALES_ID VARCHAR(32) DEFAULT NULL COMMENT '售电ID', 
	DETAILS_FEES_NAME VARCHAR(256) DEFAULT NULL COMMENT '费用名称', 
	DETAILS_VALUE DECIMAL(14,2) NOT NULL COMMENT '收费金额', 
	DETAILS_DATE datetime NOT NULL COMMENT '日期', 
	TAX_TYPE DECIMAL(2,0) DEFAULT NULL COMMENT '税费类型 （字典表60： 1=街道照明税费   2=政府税费  3=政府津贴  4=补贴  5=增值税  6=国家保险计划征税  7=增值税作补贴  8=服务费  9=补助1  10=补助2    11=税费%*用电量    等等）', 
	COMPUTE_ENABLE DECIMAL(2,0) DEFAULT '1' COMMENT '是否参与计算(字典表 6：1=是(参与计算) 2=否(不参与计算)特殊情况，如用电量为0的时候，可能不需要计算电费 3 =计算仅作为小票显示使用,不参与扣费',
	PRIMARY KEY (ID)
   ) COMMENT 'vend_invoice_details 售电单据打印-收费详情';

  CREATE TABLE PPM_VEND_INVOICE_FREE_TOKEN 
   (	
    ID VARCHAR(32) NOT NULL COMMENT '售电单据打印ID', 
	RECEIPT_NO VARCHAR(32) DEFAULT NULL COMMENT '售电单据流水号，来源自时间戳', 
	FREE_TOKEN_ID VARCHAR(32) DEFAULT NULL COMMENT '免费TokenID', 
	STATION_ID VARCHAR(32) NOT NULL COMMENT '售电站ID', 
	CUSTOMER_ID VARCHAR(32) DEFAULT NULL COMMENT '客户ID', 
	CUSTOMER_NAME VARCHAR(256) NOT NULL COMMENT '客户名称', 
	METER_ID VARCHAR(32) DEFAULT NULL, 
	SERIAL_NUMBER VARCHAR(30) NOT NULL, 
	COMM_ADDRESS VARCHAR(24) NOT NULL, 
	FREE_RECHARGE_AMOUNT DECIMAL(14,2) NOT NULL COMMENT '充值金额(充值到电表的实际金额)', 
	FREE_TOKEN VARCHAR(20) DEFAULT NULL COMMENT '售电Token', 
	PAYMENT_TYPE VARCHAR(50) DEFAULT NULL COMMENT '支付类型 (字典表 73：1=现金 2=支票 3=信用卡 4=借记卡 5=免费) ', 
	USER_ID VARCHAR(32) DEFAULT NULL COMMENT '用户ID', 
	USER_NAME VARCHAR(50) NOT NULL COMMENT '用户姓名', 
	CREATE_DATE datetime DEFAULT CURRENT_TIMESTAMP COMMENT '建立时间', 
	SAVE_DB_DATE datetime DEFAULT CURRENT_TIMESTAMP,
	PRIMARY KEY (ID)
   ) COMMENT 'ppm_vend_invoice_free_token 免费Token单据打印';

  CREATE TABLE PPM_VEND_STATION_RECHARGE 
   (	
    ID VARCHAR(32) NOT NULL, 
	STATION_ID VARCHAR(32) DEFAULT NULL, 
	DEALING_TIME datetime DEFAULT NULL, 
	RECHARGE_BY DECIMAL(2,0) DEFAULT NULL COMMENT '充值方式(字典表73：1=现金 2=支票 3=信用卡 4=借记卡)', 
	RECHARGE_TYPE DECIMAL(2,0) DEFAULT '1' COMMENT '充值类型（字典表72: 固定为 1 售电站充值)', 
	RECEIPT_NO VARCHAR(32) DEFAULT NULL COMMENT '充值小票号码', 
	RECHARGE_AMOUNT DECIMAL(14,4) DEFAULT NULL COMMENT '充值金额：客户缴费实际金额', 
	COMMISSION DECIMAL(14,4) DEFAULT NULL COMMENT '佣金（劳务费）：充值金额 * 佣金百分比 =佣金（佣金百分比在售电站中获取)', 
	TAX DECIMAL(14,4) DEFAULT NULL COMMENT '税费(佣金 乘以 税费百分比 --税费百分比在售电站中获取)', 
	TOTAL_AMOUNT DECIMAL(14,4) DEFAULT NULL COMMENT '充值总金额(加上佣金 减去 税费)', 
	RECHARGE_AMOUNT_INPUT DECIMAL(14,4) DEFAULT NULL COMMENT '充值前账户金额', 
	ACCOUNT_BALANCE DECIMAL(14,4) DEFAULT NULL COMMENT '充值后账户金额', 
	OPERATOR_ID VARCHAR(32) DEFAULT NULL, 
	README VARCHAR(256) DEFAULT NULL, 
	CHEQUE_NO VARCHAR(100) DEFAULT NULL, 
	RECEIPT_STATE DECIMAL(2,0) COMMENT '单据状态：0 正常数据  1 过期数据（被卸载的记录）', 
	CREATE_DATE datetime DEFAULT CURRENT_TIMESTAMP, 
	SAVE_DB_DATE datetime DEFAULT CURRENT_TIMESTAMP,
	PRIMARY KEY (ID)
   ) COMMENT 'ppm_vend_station_recharge 售电站充值记录';

  CREATE TABLE PPM_VEND_STATION_STREAM 
   (	ID VARCHAR(32), 
	VEND_STATION_ID VARCHAR(32) DEFAULT NULL, 
	VEND_STATION_RECHARGE_ID VARCHAR(32) DEFAULT NULL, 
	SALES_ID VARCHAR(32) DEFAULT NULL, 
	BEFORE_AMOUNT DECIMAL(14,4) DEFAULT NULL COMMENT '变动前总额', 
	AFTER_AMOUNT DECIMAL(14,4) DEFAULT NULL COMMENT '变动后总额', 
	CHANGE_TYPE DECIMAL(4,0) DEFAULT NULL COMMENT '金额变动类型 （字典表 72： 1=售电站充值，2=用户充值，3=免费token，4=用户充值撤销COMMENT ON COLUMN ppm_vend_station_stream. 5=售电站充值撤销)', 
	CHANGE_AMOUNT DECIMAL(14,4) DEFAULT NULL COMMENT '变动金额', 
	USER_ID VARCHAR(32) DEFAULT NULL, 
	CREATE_DATE datetime DEFAULT CURRENT_TIMESTAMP, 
	SAVE_DB_DATE datetime DEFAULT CURRENT_TIMESTAMP,
	PRIMARY KEY (ID)
   ) COMMENT 'ppm_vend_station_stream 售电站金额变动流水';

  CREATE TABLE PPM_VEND_STATION_STREAM_EXCEPTION 
   (	
    ID VARCHAR(32) NOT NULL, 
	VEND_STATION_ID VARCHAR(32) DEFAULT NULL, 
	VEND_STATION_RECHARGE_ID VARCHAR(32) DEFAULT NULL, 
	SALES_ID VARCHAR(32) DEFAULT NULL, 
	BEFORE_AMOUNT DECIMAL(14,4) DEFAULT NULL COMMENT '变动前总额', 
	AFTER_AMOUNT DECIMAL(14,4) DEFAULT NULL COMMENT '变动后总额', 
	CHANGE_TYPE DECIMAL(4,0) DEFAULT NULL COMMENT '金额变动类型 （字典表 72： 1=售电站充值，2=用户充值，3=免费token，4=用户充值撤销. 5=售电站充值撤销)', 
	CHANGE_AMOUNT DECIMAL(14,4) DEFAULT NULL COMMENT '变动金额', 
	USER_ID VARCHAR(32) DEFAULT NULL, 
	CREATE_DATE datetime DEFAULT CURRENT_TIMESTAMP, 
	SAVE_DB_DATE datetime DEFAULT CURRENT_TIMESTAMP, 
	README VARCHAR(500) DEFAULT NULL,
	PRIMARY KEY (ID)
   ) COMMENT 'ppm_vend_station_stream_exception 售电站金额变动流水异常记录';

  CREATE TABLE PPM_VEND_SWITCH_PAYMENT 
   (	
    ID VARCHAR(32) NOT NULL, 
	CUSTOMER_ID VARCHAR(32) DEFAULT NULL, 
	METER_ID VARCHAR(32) DEFAULT NULL, 
	SERIAL_NUMBER VARCHAR(24) DEFAULT NULL, 
	TOKEN_TYPE VARCHAR(4) DEFAULT NULL COMMENT 'Token类型(字典类型：54COMMENT ON COLUMN ppm_vend_switch_payment.21=后付费转预付费Token 22=预付费转后付费Token ）', 
	TOKEN VARCHAR(45) DEFAULT NULL COMMENT '20位Token值', 
	TOKEN_EXECUTION_STATUS VARCHAR(45) DEFAULT NULL COMMENT 'Tokne执行状态（字典表93 0= invaild无效 1= valid 有效 2=to be confirmed 待确认）', 
	COMBINED_ACTIVE_ENERGY DECIMAL(18,4) DEFAULT NULL COMMENT '组合有功用电量 （此值仅在后付费转预付费有用），此字段是后付费转预付费时，读取电表中的组合有功，再根据当月的费率计算出电费，以便生产债务，下月收取。', 
	DEBT_ID VARCHAR(32) DEFAULT NULL COMMENT '债务ID--后付费模式转预付费模式，所生成的债务编号', 
	METER_PREPAIDBALANCE DECIMAL(18,4) DEFAULT NULL COMMENT '电表预付费余额 （此值仅在预付费转后付费有用），此字段是预付费转后付费时，读取电表中的剩余电费，在下个月的后付费账单中进行抵扣。', 
	BILL_ID VARCHAR(32) DEFAULT NULL COMMENT '账单编号 --预付费转后付费，进行抵扣的后付费账单ID', 
	USER_ID VARCHAR(32) DEFAULT NULL, 
	CREATE_DATE datetime DEFAULT CURRENT_TIMESTAMP, 
	SAVE_DB_DATE datetime DEFAULT CURRENT_TIMESTAMP, 
	README VARCHAR(256) DEFAULT NULL,
	PRIMARY KEY (ID)
   ) COMMENT 'ppm_vend_switch_payment 电表付费切换记录表';


  CREATE TABLE PPM_VEND_TOKEN_MANAGE 
   (	
    UTILITY_ID VARCHAR(32), 
	ID VARCHAR(32) NOT NULL, 
	ORG_ID VARCHAR(100) DEFAULT NULL, 
	STATION_ID VARCHAR(32) DEFAULT NULL, 
	METER_ID VARCHAR(32) DEFAULT NULL, 
	USER_ID VARCHAR(32) DEFAULT NULL, 
	SALES_ID VARCHAR(32) DEFAULT NULL, 
	TOKEN_TYPE VARCHAR(4) DEFAULT NULL, 
	TOKEN VARCHAR(254) DEFAULT NULL, 
	CREATE_TOKEN_DATE datetime DEFAULT NULL, 
	CONSUMER_PAYMENT_MONEY DECIMAL(10,2) DEFAULT NULL, 
	DEDUCTIONS_OTHER_MONEY DECIMAL(10,2) DEFAULT NULL, 
	ACTUAL_RECHARGE_AMOUNT DECIMAL(10,2) DEFAULT NULL, 
	DEDUCTIONS_OTHER_MONEY_DETAILS VARCHAR(1000) DEFAULT NULL, 
	SET_LOAD_THRESHOLD DECIMAL(10,2) DEFAULT '0.00', 
	SAVE_DB_DATE datetime DEFAULT CURRENT_TIMESTAMP, 
	README VARCHAR(256) DEFAULT NULL,
	PRIMARY KEY (ID)
   ) ;

  CREATE TABLE PPM_VENDING_STATION 
   (	
    ID VARCHAR(32) NOT NULL, 
	ORG_ID VARCHAR(32), 
	STATION_CODE VARCHAR(32), 
	STATION_NAME VARCHAR(256), 
	STATION_TYPE DECIMAL(4,0) DEFAULT '0' COMMENT '1=营业大厅 2=直属售电点 3=代售点', 
	STATION_ADDRESS VARCHAR(256) COMMENT '1=启用  2=失效 3=作废', 
	LONGITUGE VARCHAR(20), 
	LATITUDE VARCHAR(20), 
	TELEPHONE_NO VARCHAR(20), 
	STATION_STATUS DECIMAL(2,0), 
	COMMISSION DECIMAL(14,4) DEFAULT '0.0000' COMMENT '佣金比例', 
	TAX DECIMAL(14,4) DEFAULT '0.0000' COMMENT '税费比例', 
	MAX_LIMT_AMOUNT DECIMAL(14,4) COMMENT '最大限额', 
	AVAILABLE_ELECTRICITY DECIMAL(14,4) DEFAULT '0.0000' COMMENT '剩余金额', 
	VERSION_OPTIMISTIC_LOCKING VARCHAR(32),
	PRIMARY KEY (ID)
   ) ;
