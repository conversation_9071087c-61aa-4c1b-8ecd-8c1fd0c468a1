<?xml version="1.0" encoding="UTF-8"?><ureport><cell expand="None" name="A1" row="1" col="1"><cell-style font-size="14" forecolor="0,0,0" font-family="Times New Roman" bold="true" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="B1" row="1" col="2" col-span="5"><cell-style font-size="16" forecolor="0,0,0" font-family="Times New Roman" bold="true" align="center" valign="middle"></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="reportTitle" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="G1" row="1" col="7"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="H1" row="1" col="8"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="I1" row="1" col="9"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="J1" row="1" col="10"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="K1" row="1" col="11"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="L1" row="1" col="12"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="M1" row="1" col="13"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="N1" row="1" col="14"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><image-value source="text" width="135" height="35"><text><![CDATA[classpath:nedco.png]]></text></image-value></cell><cell expand="None" name="A2" row="2" col="1"><cell-style font-size="9" forecolor="35,60,12" font-family="宋体" bgcolor="84,101,128" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="B2" row="2" col="2"><cell-style font-size="9" forecolor="35,60,12" font-family="宋体" bgcolor="84,101,128" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="C2" row="2" col="3"><cell-style font-size="9" forecolor="35,60,12" font-family="宋体" bgcolor="84,101,128" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="D2" row="2" col="4"><cell-style font-size="9" forecolor="35,60,12" font-family="宋体" bgcolor="84,101,128" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="E2" row="2" col="5"><cell-style font-size="9" forecolor="35,60,12" font-family="宋体" bgcolor="84,101,128" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="F2" row="2" col="6"><cell-style font-size="9" forecolor="35,60,12" font-family="宋体" bgcolor="84,101,128" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="G2" row="2" col="7"><cell-style font-size="9" forecolor="35,60,12" font-family="宋体" bgcolor="84,101,128" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="H2" row="2" col="8"><cell-style font-size="9" forecolor="255,255,255" font-family="Times New Roman" bgcolor="84,101,128" align="right" valign="middle"></cell-style><simple-value><![CDATA[Area:]]></simple-value></cell><cell expand="None" name="I2" row="2" col="9"><cell-style font-size="9" forecolor="255,255,255" font-family="Times New Roman" bgcolor="84,101,128" align="center" valign="middle"></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="orgName" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="J2" row="2" col="10" col-span="2"><cell-style font-size="9" forecolor="255,255,255" font-family="Times New Roman" bgcolor="84,101,128" align="right" valign="middle"></cell-style><simple-value><![CDATA[Staticstic Date:]]></simple-value></cell><cell expand="None" name="L2" row="2" col="12"><cell-style font-size="9" forecolor="255,255,255" font-family="Times New Roman" bgcolor="84,101,128" align="center" valign="middle"></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="statDate" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="M2" row="2" col="13"><cell-style font-size="9" forecolor="255,255,255" font-family="Times New Roman" bgcolor="84,101,128" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="N2" row="2" col="14"><cell-style font-size="9" forecolor="255,255,255" font-family="Times New Roman" bgcolor="84,101,128" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="A3" row="3" col="1"><cell-style font-size="10" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Tariff Type]]></simple-value></cell><cell expand="None" name="B3" row="3" col="2"><cell-style font-size="10" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[No.of customers]]></simple-value></cell><cell expand="None" name="C3" row="3" col="3"><cell-style font-size="10" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Energy Cost]]></simple-value></cell><cell expand="None" name="D3" row="3" col="4"><cell-style font-size="10" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[NEL]]></simple-value></cell><cell expand="None" name="E3" row="3" col="5"><cell-style font-size="10" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[STL]]></simple-value></cell><cell expand="None" name="F3" row="3" col="6"><cell-style font-size="10" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Service Charge]]></simple-value></cell><cell expand="None" name="G3" row="3" col="7"><cell-style font-size="10" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[NHIL]]></simple-value></cell><cell expand="None" name="H3" row="3" col="8"><cell-style font-size="10" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[GETFL]]></simple-value></cell><cell expand="None" name="I3" row="3" col="9"><cell-style font-size="10" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[VAT]]></simple-value></cell><cell expand="None" name="J3" row="3" col="10"><cell-style font-size="10" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[MD Charge]]></simple-value></cell><cell expand="None" name="K3" row="3" col="11"><cell-style font-size="10" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[PF Charge]]></simple-value></cell><cell expand="None" name="L3" row="3" col="12"><cell-style font-size="10" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Debt Collected]]></simple-value></cell><cell expand="None" name="M3" row="3" col="13"><cell-style font-size="10" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Total Cost]]></simple-value></cell><cell expand="None" name="N3" row="3" col="14"><cell-style font-size="10" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Subsidy]]></simple-value></cell><cell expand="Down" name="A4" row="4" col="1"><cell-style font-size="9" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="tariffType" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="B4" row="4" col="2"><cell-style font-size="9" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="customerNumber" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="C4" row="4" col="3"><cell-style font-size="9" font-family="Times New Roman" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="energyCost" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="D4" row="4" col="4"><cell-style font-size="9" font-family="Times New Roman" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="nel" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="E4" row="4" col="5"><cell-style font-size="9" font-family="Times New Roman" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="stl" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="F4" row="4" col="6"><cell-style font-size="9" font-family="Times New Roman" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="serviceCharge" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="G4" row="4" col="7"><cell-style font-size="9" font-family="Times New Roman" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="nhil" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="H4" row="4" col="8"><cell-style font-size="9" font-family="Times New Roman" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="getfl" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="I4" row="4" col="9"><cell-style font-size="9" font-family="Times New Roman" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="vat" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="J4" row="4" col="10"><cell-style font-size="9" font-family="Times New Roman" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="mdCharge" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="K4" row="4" col="11"><cell-style font-size="9" font-family="Times New Roman" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="pfCharge" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="L4" row="4" col="12"><cell-style font-size="9" font-family="Times New Roman" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="debtCollected" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="M4" row="4" col="13"><cell-style font-size="9" font-family="Times New Roman" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="totalCost" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="N4" row="4" col="14"><cell-style font-size="9" font-family="Times New Roman" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="subsidy" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="A5" row="5" col="1"><cell-style font-size="9" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Total:]]></simple-value></cell><cell expand="None" name="B5" row="5" col="2" top-cell="B3"><cell-style font-size="9" font-family="Times New Roman" format="#" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="customerNumber" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="C5" row="5" col="3" top-cell="C3"><cell-style font-size="9" font-family="Times New Roman" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="energyCost" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="D5" row="5" col="4" top-cell="D3"><cell-style font-size="9" font-family="Times New Roman" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="nel" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="E5" row="5" col="5" top-cell="E3"><cell-style font-size="9" font-family="Times New Roman" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="stl" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="F5" row="5" col="6" top-cell="F3"><cell-style font-size="9" font-family="Times New Roman" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="serviceCharge" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="G5" row="5" col="7" top-cell="G3"><cell-style font-size="9" font-family="Times New Roman" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="nhil" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="H5" row="5" col="8" top-cell="H3"><cell-style font-size="9" font-family="Times New Roman" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="getfl" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="I5" row="5" col="9" top-cell="I3"><cell-style font-size="9" font-family="Times New Roman" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="vat" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="J5" row="5" col="10" top-cell="I3"><cell-style font-size="9" font-family="Times New Roman" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="mdCharge" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="K5" row="5" col="11" top-cell="J3"><cell-style font-size="9" font-family="Times New Roman" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="pfCharge" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="L5" row="5" col="12" top-cell="K3"><cell-style font-size="9" font-family="Times New Roman" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="debtCollected" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="M5" row="5" col="13" top-cell="L3"><cell-style font-size="9" font-family="Times New Roman" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="totalCost" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="N5" row="5" col="14" top-cell="M3"><cell-style font-size="9" font-family="Times New Roman" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="subsidy" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="A6" row="6" col="1"><cell-style font-size="9" forecolor="255,255,255" font-family="Times New Roman" bgcolor="84,101,128" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="B6" row="6" col="2"><cell-style font-size="9" forecolor="255,255,255" font-family="Times New Roman" bgcolor="84,101,128" align="center" valign="middle"></cell-style><simple-value><![CDATA[Print Date:]]></simple-value></cell><cell expand="None" name="C6" row="6" col="3" col-span="2"><cell-style font-size="9" forecolor="255,255,255" font-family="Times New Roman" bgcolor="84,101,128" align="center" valign="middle"></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="printDate" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="E6" row="6" col="5"><cell-style font-size="9" forecolor="255,255,255" font-family="Times New Roman" bgcolor="84,101,128" align="center" valign="middle"></cell-style><simple-value><![CDATA[Print Operator:]]></simple-value></cell><cell expand="None" name="F6" row="6" col="6" col-span="2"><cell-style font-size="9" forecolor="255,255,255" font-family="Times New Roman" bgcolor="84,101,128" align="center" valign="middle"></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="printOpertor" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="H6" row="6" col="8"><cell-style font-size="9" forecolor="255,255,255" font-family="Times New Roman" bgcolor="84,101,128" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="I6" row="6" col="9"><cell-style font-size="9" forecolor="255,255,255" font-family="Times New Roman" bgcolor="84,101,128" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="J6" row="6" col="10"><cell-style font-size="9" forecolor="255,255,255" font-family="Times New Roman" bgcolor="84,101,128" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="K6" row="6" col="11"><cell-style font-size="9" forecolor="255,255,255" font-family="Times New Roman" bgcolor="84,101,128" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="L6" row="6" col="12"><cell-style font-size="9" forecolor="255,255,255" font-family="Times New Roman" bgcolor="84,101,128" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="M6" row="6" col="13"><cell-style font-size="9" forecolor="255,255,255" font-family="Times New Roman" bgcolor="84,101,128" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="N6" row="6" col="14"><cell-style font-size="9" forecolor="255,255,255" font-family="Times New Roman" bgcolor="84,101,128" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><row row-number="1" height="38"/><row row-number="2" height="25"/><row row-number="3" height="18"/><row row-number="4" height="18"/><row row-number="5" height="18"/><row row-number="6" height="27"/><column col-number="1" width="80"/><column col-number="2" width="80"/><column col-number="3" width="80"/><column col-number="4" width="80"/><column col-number="5" width="74"/><column col-number="6" width="74"/><column col-number="7" width="74"/><column col-number="8" width="74"/><column col-number="9" width="74"/><column col-number="10" width="74"/><column col-number="11" width="74"/><column col-number="12" width="74"/><column col-number="13" width="74"/><column col-number="14" width="74"/><datasource name="summaryDailySalesDataSource" type="spring" bean="summaryDailySalesDataSource"><dataset name="reportData" type="bean" method="loadReportData" clazz="com.clou.esp.ppm.app.web.ureport.dataset.SummaryDailySalesDataSet"><field name="customerNumber"/><field name="debtCollected"/><field name="energyCost"/><field name="getfl"/><field name="mdCharge"/><field name="nel"/><field name="nhil"/><field name="orgName"/><field name="pfCharge"/><field name="printDate"/><field name="printOpertor"/><field name="reportTitle"/><field name="serviceCharge"/><field name="statDate"/><field name="stl"/><field name="subsidy"/><field name="tariffType"/><field name="totalCost"/><field name="vat"/></dataset></datasource><paper type="A4" left-margin="90" right-margin="90"
    top-margin="72" bottom-margin="72" paging-mode="fitpage" fixrows="0"
    width="595" height="842" orientation="portrait" html-report-align="left" bg-image="" html-interval-refresh-value="0" column-enabled="false"></paper></ureport>