
package ch.iec.tc57._2011.schema.message;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * From CIM
 * 
 * <p>NameType complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="NameType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}anyType"/&gt;
 *         &lt;element name="description" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
 *         &lt;element name="NameTypeAuthority" type="{http://www.iec.ch/TC57/2011/schema/message}NameTypeAuthority" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "NameType", propOrder = {
    "name",
    "description",
    "nameTypeAuthority"
})
public class NameType {

    @XmlElement(required = true)
    protected Object name;
    protected Object description;
    @XmlElement(name = "NameTypeAuthority")
    protected NameTypeAuthority nameTypeAuthority;

    /**
     * 获取name属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Object }
     *     
     */
    public Object getName() {
        return name;
    }

    /**
     * 设置name属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Object }
     *     
     */
    public void setName(Object value) {
        this.name = value;
    }

    /**
     * 获取description属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Object }
     *     
     */
    public Object getDescription() {
        return description;
    }

    /**
     * 设置description属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Object }
     *     
     */
    public void setDescription(Object value) {
        this.description = value;
    }

    /**
     * 获取nameTypeAuthority属性的值。
     * 
     * @return
     *     possible object is
     *     {@link NameTypeAuthority }
     *     
     */
    public NameTypeAuthority getNameTypeAuthority() {
        return nameTypeAuthority;
    }

    /**
     * 设置nameTypeAuthority属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link NameTypeAuthority }
     *     
     */
    public void setNameTypeAuthority(NameTypeAuthority value) {
        this.nameTypeAuthority = value;
    }

}
