/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictMeterDataStorageTable{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.dict;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.clou.esp.hes.app.web.dao.dict.DictMeterDataStorageTableDao;
import com.clou.esp.hes.app.web.model.dict.DictMeterDataStorageTable;
import com.clou.esp.hes.app.web.model.report.MeterDataReport;
import com.clou.esp.hes.app.web.model.report.MissDataReport;
import com.clou.esp.hes.app.web.model.report.MissDataReport1;
import com.clou.esp.hes.app.web.service.dict.DictMeterDataStorageTableService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@Component
@Service("dictMeterDataStorageTableService")
public class DictMeterDataStorageTableServiceImpl  extends CommonServiceImpl<DictMeterDataStorageTable>  implements DictMeterDataStorageTableService {

	@Resource
	private DictMeterDataStorageTableDao dictMeterDataStorageTableDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dictMeterDataStorageTableDao);
    }
	@SuppressWarnings("rawtypes")
	public DictMeterDataStorageTableServiceImpl() {}
	
	
	@Override
	public JqGridResponseTo getForJqGridMeter(JqGridSearchTo jqGridSearchTo,String searchTimeType) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
//		Map<String,Object> m=jqGridSearchTo.getMap();
//		String sn =(String) m.get("serizlName");
//		Map<Date,MeterDataReport> map = Maps.newHashMap();
		
		if(!StringUtils.isEmpty(searchTimeType)) {
			List<MeterDataReport>	list = dictMeterDataStorageTableDao.getForJqGridMeterOnlySn(jqGridSearchTo);
			JqGridResponseTo j= JqGridHandler.GetJqGridPageJsonData(new PageInfo<MeterDataReport>(), jqGridSearchTo);

//			
			List<Map<String,String>>	tmplist=Lists.newArrayList();
			//如果不为分钟类型,判断是否符合格式
			if(!"0".equals(searchTimeType)) {
				for (Iterator<MeterDataReport> iterator = list.iterator(); iterator.hasNext();) {
					MeterDataReport meterDataReport = (MeterDataReport) iterator.next();
					//如果不符合格式就删除
					if(!meterDataReport.isRight(searchTimeType)) {
						iterator.remove();
					}
				}
			}
			
			for(MeterDataReport meterDataReport:list) {
				tmplist.add(meterDataReport.getMap(sdf));
			}
			
			j.setRows(tmplist);
			return j;
		}else {
			PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
			List<MeterDataReport>	list = dictMeterDataStorageTableDao.getForJqGridMeterOnlySn(jqGridSearchTo);
			PageInfo<MeterDataReport> pageInfo = new PageInfo<MeterDataReport>(list);
			return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
		}
	}

	@Override
	public List<MeterDataReport> getForJqGridMeterExcel(JqGridSearchTo jqGridSearchTo) {
			List<MeterDataReport>	list = dictMeterDataStorageTableDao.getForJqGridMeterOnlySn(jqGridSearchTo);
			return list;
	}

	@Override
	public JqGridResponseTo getForJqGridMeterOrg(JqGridSearchTo jqGridSearchTo,String searchTimeType) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
//		Map<String,Object> m=jqGridSearchTo.getMap();
//		String sn =(String) m.get("serizlName");
//		Map<Date,MeterDataReport> map = Maps.newHashMap();

		if(!StringUtils.isEmpty(searchTimeType)) {
			List<MeterDataReport>	list = dictMeterDataStorageTableDao.getForJqGridMeterOnlySn(jqGridSearchTo);
			JqGridResponseTo j= JqGridHandler.GetJqGridPageJsonData(new PageInfo<MeterDataReport>(), jqGridSearchTo);

//			if(list!=null) {
//				for(MeterDataReport report:list) {
//					map.put(report.getTimes(), report);
//				}
//			}
//
			List<Map<String,String>>	tmplist=Lists.newArrayList();
//			for(Date date:timeList) {
//				MeterDataReport report =map.get(date);
//
//				if(report==null) {
//					Date tmpDate=null;
//					try {
//						tmpDate = sdf.parse(sdf.format(date));
//					} catch (ParseException e) {
//						e.printStackTrace();
//					}
//					report = new MeterDataReport(sn,tmpDate,"1");
//				}
//				tmplist.add(report.getMap(sdf));
//			}
			//如果不为分钟类型,判断是否符合格式
			if(!"0".equals(searchTimeType)) {
				for (Iterator<MeterDataReport> iterator = list.iterator(); iterator.hasNext();) {
					MeterDataReport meterDataReport = (MeterDataReport) iterator.next();
					//如果不符合格式就删除
					if(!meterDataReport.isRight(searchTimeType)) {
						iterator.remove();
					}
				}
			}

			for(MeterDataReport meterDataReport:list) {
				tmplist.add(meterDataReport.getMap(sdf));
			}

			j.setRows(tmplist);
			return j;
		}else {
			PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
			List<MeterDataReport>	list = dictMeterDataStorageTableDao.getForJqGridMeter(jqGridSearchTo);
			PageInfo<MeterDataReport> pageInfo = new PageInfo<MeterDataReport>(list);
			return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
		}
	}


	@Override
	public JqGridResponseTo getForJqGridMeterOnlyShenyu(JqGridSearchTo jqGridSearchTo) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		List<MeterDataReport>	list = dictMeterDataStorageTableDao.getForJqGridMeterOnlyShenyu(jqGridSearchTo);
		PageInfo<MeterDataReport> pageInfo = new PageInfo<MeterDataReport>(list);
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}
	@Override
	public List<MeterDataReport> getForJqGridMeterOnlyShenyuExcel(JqGridSearchTo jqGridSearchTo) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		List<MeterDataReport>	list = dictMeterDataStorageTableDao.getForJqGridMeterOnlyShenyu(jqGridSearchTo);
	 	return list;
	}

	@Override
	public JqGridResponseTo getForJqGridMeterMonthShenyu(JqGridSearchTo jqGridSearchTo) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		List<MeterDataReport>	list = dictMeterDataStorageTableDao.getForJqGridMeterMonthShenyu(jqGridSearchTo);
		PageInfo<MeterDataReport> pageInfo = new PageInfo<MeterDataReport>(list);
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}
	@Override
	public List<MeterDataReport> getForJqGridMeterMonthShenyuExcel(JqGridSearchTo jqGridSearchTo) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		List<MeterDataReport>	list = dictMeterDataStorageTableDao.getForJqGridMeterMonthShenyu(jqGridSearchTo);
		return list;
	}

	
	@Override
	public List<MeterDataReport> getMeterDataList(
			MeterDataReport meterDataReport) {
		return dictMeterDataStorageTableDao.getMeterDataList(meterDataReport);
	}
	@Override
	public List<MeterDataReport> getMeterDataListOrg(
			MeterDataReport meterDataReport) {
		return dictMeterDataStorageTableDao.getMeterDataListOrg(meterDataReport);
	}
	@Override
	public List<MeterDataReport> getMeterDataListByCommnuicator(MeterDataReport meterDataReport) {
		return dictMeterDataStorageTableDao.getMeterDataListByCommnuicator(meterDataReport);
	}
	
	@Override
	public JqGridResponseTo getForJqGridCommnuicator(JqGridSearchTo jqGridSearchTo, Map<String,String> meterMap) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		List<MeterDataReport> list = dictMeterDataStorageTableDao.getForJqGridCommnuicator(jqGridSearchTo); 
		List<Map<String,String>> listTmp = Lists.newArrayList();
		Map<String,MeterDataReport> map = Maps.newHashMap();
		if(list!=null) {
			for(MeterDataReport re:list) {
				map.put(re.getId(), re);
				listTmp.add(re.getMap(sdf));
			}
		}
		
		
		Date startDate =(Date) jqGridSearchTo.getMap().get("times_start");
		
		
		for(String key:meterMap.keySet()) {
			MeterDataReport re = map.get(key);
			if(re==null) {
				String sn = meterMap.get(key);
				re = new MeterDataReport(sn,startDate,"1");
				listTmp.add(re.getMap(sdf));
			}
		}
		JqGridResponseTo j= JqGridHandler.GetJqGridPageJsonData(new PageInfo<MeterDataReport>(), jqGridSearchTo);
		j.setRows(listTmp);
		return j;
	}

	@Override
	public List<MeterDataReport> getForJqGridCommnuicatorExcel(JqGridSearchTo jqGridSearchTo, Map<String,String> meterMap) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		List<MeterDataReport> list = dictMeterDataStorageTableDao.getForJqGridCommnuicator(jqGridSearchTo);
		List<Map<String,String>> listTmp = Lists.newArrayList();
		Map<String,MeterDataReport> map = Maps.newHashMap();
		if(list!=null) {
			for(MeterDataReport re:list) {
				map.put(re.getId(), re);
				listTmp.add(re.getMap(sdf));
			}
		}


		Date startDate =(Date) jqGridSearchTo.getMap().get("times_start");


		for(String key:meterMap.keySet()) {
			MeterDataReport re = map.get(key);
			if(re==null) {
				String sn = meterMap.get(key);
				re = new MeterDataReport(sn,startDate,"1");
				listTmp.add(re.getMap(sdf));
			}
		}
		return list;
	}


	@Override
	public List<MissDataReport> getMissDataList(MissDataReport missDataReport) {
		return dictMeterDataStorageTableDao.getMissDataList(missDataReport);
	}
	@Override
	public List<MissDataReport1> getMissDataList1(MissDataReport missDataReport) {
		return dictMeterDataStorageTableDao.getMissDataList1(missDataReport);
	}
	@Override
	public List<Map<String, Object>> getEnergyDataDailyByMaps(
			Map<String, Object> params) {
		return dictMeterDataStorageTableDao.getEnergyDataDailyByMaps(params);
	}
	@Override
	public List<Map<String, Object>> getMeterDateList(Map<String, Object> params) {
		return dictMeterDataStorageTableDao.getMeterDateList(params);
	}
	@Override
	public List<MeterDataReport> getMeterDateListExcel(Map<String, Object> params) {
		return dictMeterDataStorageTableDao.getMeterDateListExcel(params);
	}
	@Override
	public Long getCountTableName(String tableName) {
		return this.dictMeterDataStorageTableDao.getCountTableName(tableName);
	}
	@Override
	public void createTable(String tableName) {
		this.dictMeterDataStorageTableDao.createTable(tableName);
	}
	@Override
	public void deleteTable(String tableName) {
		this.dictMeterDataStorageTableDao.deleteTable(tableName);
	}
	@Override
	public void addTableColumn(String tableName,String columns) {
		this.dictMeterDataStorageTableDao.addTableColumn(tableName,columns);
	}
	@Override
	public void deleteTableColumn(String tableName, String columns) {
		this.dictMeterDataStorageTableDao.deleteTableColumn(tableName, columns);
	}

}