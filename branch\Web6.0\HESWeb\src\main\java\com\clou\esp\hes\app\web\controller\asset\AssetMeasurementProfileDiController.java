/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeasurementProfileDi{ } 
 * 
 * 摘    要： assetMeasurementProfileDi
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-02-28 02:48:00
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.asset;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.asset.AssetMeasurementProfileDi;
import com.clou.esp.hes.app.web.model.dict.DictProfile;
import com.clou.esp.hes.app.web.model.dict.DictProfileDataItem;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.asset.AssetMeasurementProfileDiService;
import com.clou.esp.hes.app.web.service.dict.DictProfileDataItemService;
import com.clou.esp.hes.app.web.service.dict.DictProfileService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.redis.JedisUtils;

/**
 * <AUTHOR>
 * @时间：2018-02-28 02:48:00
 * @描述：assetMeasurementProfileDi类
 */
@Controller
@RequestMapping("/assetMeasurementProfileDiController")
public class AssetMeasurementProfileDiController extends BaseController{

 	@Resource
    private AssetMeasurementProfileDiService assetMeasurementProfileDiService;
 	@Resource
 	private DictProfileDataItemService dictProfileDataItemService;
 	@Resource
 	private DictProfileService dictProfileService;
	/**
	 * 跳转到assetMeasurementProfileDi列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/asset/assetMeasurementProfileDiList");
    }

	/**
	 * 跳转到assetMeasurementProfileDi新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "assetMeasurementProfileDi")
	public ModelAndView assetMeasurementProfileDi(AssetMeasurementProfileDi assetMeasurementProfileDi,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(assetMeasurementProfileDi.getId())){
			try {
                assetMeasurementProfileDi=assetMeasurementProfileDiService.getEntity(assetMeasurementProfileDi.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("assetMeasurementProfileDi", assetMeasurementProfileDi);
		}
		return new ModelAndView("/asset/assetMeasurementProfileDi");
	}


	/**
	 * assetMeasurementProfileDi查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=assetMeasurementProfileDiService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除assetMeasurementProfileDi信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(AssetMeasurementProfileDi assetMeasurementProfileDi,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(assetMeasurementProfileDiService.deleteById(assetMeasurementProfileDi.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存assetMeasurementProfileDi信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })AssetMeasurementProfileDi assetMeasurementProfileDi,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        AssetMeasurementProfileDi t=new  AssetMeasurementProfileDi();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(assetMeasurementProfileDi.getId())){
        	t=assetMeasurementProfileDiService.getEntity(assetMeasurementProfileDi.getId());
			MyBeanUtils.copyBeanNotNull2Bean(assetMeasurementProfileDi, t);
				assetMeasurementProfileDiService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            assetMeasurementProfileDiService.save(assetMeasurementProfileDi);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
	
    /**
     * 获取profile的dataitem的列表信息
     * @param id
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "getProfileDataitemList")
    public AjaxJson getProfileDataitemList(AssetMeasurementProfileDi entity, HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DictProfileDataItem obj = new DictProfileDataItem();
        Map<String, Object> attributes = new HashMap<>();
        try {
//            SysUser su=TokenManager.getToken();
        	DictProfile dictProfile= dictProfileService.getEntity(entity.getProfileId());
        	List<AssetMeasurementProfileDi> list = assetMeasurementProfileDiService.getList(entity);
        	obj.setProfileId(entity.getProfileId());
        	obj.setProtocolId(dictProfile.getProtocolId());
        	List<DictProfileDataItem> listDataitem = dictProfileDataItemService.getProfileDataitemList(obj);
        	/*
        	 * 设置数据唯一主键，否则，jqgrid将因为主键冲突而不能被选中和编辑
        	 */
        	List<String> strList = new ArrayList<>();
        	for (int i = 0; i < list.size(); i++) {
        		list.get(i).setId(list.get(i).getDataitemId());
        		strList.add(list.get(i).getDataitemId());
			}
        	//去除dataitem list已选的数据, 选中数据到另一个list中
        	List<DictProfileDataItem> listDataitemTemp = new ArrayList<>();
        	for (int i = 0; i < listDataitem.size(); i++) {
        		listDataitem.get(i).setId(listDataitem.get(i).getDataitemId());
        		if(strList.contains(listDataitem.get(i).getDataitemId())){
        			listDataitemTemp.add(listDataitem.get(i));
        		}
        	}
        	//删除
        	for (int i = 0; i < listDataitemTemp.size(); i++) {
        		listDataitem.remove(listDataitemTemp.get(i));
        	}
            if(list.size() > 0 || listDataitem.size() > 0){
            	j.setObj(list);
            	attributes.put("list", listDataitem);
            	j.setAttributes(attributes);
            }else{
                j.setSuccess(false);
                j.setMsg("No data found!");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 从左表选中dataitem添加到右表中
     * 过滤条件：
     * 			不能添加重复数据；
     * 			以profileId为主键，添加数据保存到Redis中，最后统一存入数据库
     * @param id
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "addProfileDataitemList")
    public AjaxJson addProfileDataitemList(String mgId, 
    		String profileId, String dataitemId, String selectedDataChannelList, HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        List<AssetMeasurementProfileDi> list = new ArrayList<>();
        int judge = 0;
        try {
            JSONArray jsonArray = JSONArray.fromObject(selectedDataChannelList);
            if(jsonArray.size() > 0){
            	/*
            	 * 判断是否存在重复数据
            	 */
            	for (int i = 0; i < jsonArray.size(); i++) {
            		JSONObject o = jsonArray.getJSONObject(i);
            		if(o.get("dataitemId").equals(dataitemId)){
            			judge += 1;
            		}
            		AssetMeasurementProfileDi di = new AssetMeasurementProfileDi();
            		di.setId(o.get("id").toString());
            		di.setMgId(mgId);
            		di.setProfileId(o.get("profileId").toString());
            		di.setDataitemId(o.get("dataitemId").toString());
            		di.setDataitemName(o.get("dataitemName").toString());
//            		di.setSortId(o.get("sortId").toString());
            		di.setSortId(String.valueOf(i+1));
            		list.add(di);
            	}
            	if(judge > 1){
            		j.setSuccess(false);
                    j.setMsg("Repeat the data, please rechoose!");
                    return j;
            	}
            	//保存增加后的data channel list 到Redis
            	Map<String, Object> value = new HashMap<>();
				value.put("listProfileDi", list);
				JedisUtils.setObjectMap(mgId + profileId, value, 0);
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 删除Profile Dataitem List，保存到Redis，最后统一存入数据库
     * 过滤条件：
     * 			以profileId为主键，添加数据保存到Redis中
     * @param id
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "deleteProfileDataitemList")
    public AjaxJson deleteProfileDataitemList(String mgId, 
    		String profileId, String dataitemId, String selectedDataChannelList, HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        List<AssetMeasurementProfileDi> list = new ArrayList<>();
        try {
            JSONArray jsonArray = JSONArray.fromObject(selectedDataChannelList);
            if(jsonArray.size() > 0){
            	/*
            	 * 判断
            	 */
            	for (int i = 0; i < jsonArray.size(); i++) {
            		JSONObject o = jsonArray.getJSONObject(i);
            		//不保存需要删除的数据
            		if(dataitemId.equals(o.get("dataitemId").toString())){
            			continue;
            		}
            		AssetMeasurementProfileDi di = new AssetMeasurementProfileDi();
            		di.setId(o.get("id").toString());
            		di.setMgId(mgId);
            		di.setProfileId(o.get("profileId").toString());
            		di.setDataitemId(o.get("dataitemId").toString());
            		di.setDataitemName(o.get("dataitemName").toString());
//            		di.setSortId(o.get("sortId").toString());
            		di.setSortId(String.valueOf(i+1));
            		list.add(di);
            	}
            }
            //保存增加后的data channel list 到Redis
            Map<String, Object> value = new HashMap<>();
            value.put("listProfileDi", list);
            JedisUtils.setObjectMap(mgId + profileId, value, 0);
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg("Abnormal operation");
        }
        return j;
    }
    

    @ResponseBody
    @RequestMapping(value = "upOrDownChannels")
    public AjaxJson upOrDownChannels(String mgId, String profileId, String dataitemId, 
    		String type, String rowNum, String selectedDataChannelList, HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        List<AssetMeasurementProfileDi> list = new ArrayList<>();
        int rowNumber = Integer.parseInt(rowNum);
        try {
            JSONArray jsonArray = JSONArray.fromObject(selectedDataChannelList);
            if(jsonArray.size() > 0){
            	for (int i = 0; i < jsonArray.size(); i++) {
            		JSONObject o = jsonArray.getJSONObject(i);
            		AssetMeasurementProfileDi di = new AssetMeasurementProfileDi();
            		di.setId(o.get("id").toString());
            		di.setMgId(mgId);
            		di.setProfileId(o.get("profileId").toString());
            		di.setDataitemId(o.get("dataitemId").toString());
            		di.setDataitemName(o.get("dataitemName").toString());
//            		di.setSortId(o.get("sortId").toString());
                    di.setProtocolCode(o.get("protocolCode").toString());
            		di.setSortId(String.valueOf(i+1));
            		list.add(di);
            	}
            	if("up".equals(type)){
            		int upRowNumber = rowNumber - 1;
            		Collections.swap(list, rowNumber, upRowNumber);		//数据调换
            		list.get(upRowNumber).setSortId(String.valueOf(rowNumber));		//更改sortId
            		list.get(rowNumber).setSortId(String.valueOf(rowNumber + 1));
            		j.setObj(list);
            	}else{
            		int downRowNumber = rowNumber + 1;
            		Collections.swap(list, rowNumber, downRowNumber);	//数据调换
            		list.get(rowNumber).setSortId(String.valueOf(rowNumber + 1));	//更改sortId
            		list.get(downRowNumber).setSortId(String.valueOf(downRowNumber + 1));
            		j.setObj(list);
            	}
            	
            	//保存增加后的data channel list 到Redis
                Map<String, Object> value = new HashMap<>();
                value.put("listProfileDi", list);
                JedisUtils.setObjectMap(mgId + profileId, value, 0);
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg("Abnormal operation");
        }
        return j;
    }
}