<%@ taglib prefix="t" uri="/HESWebGui-tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jstl/fmt_rt"%>
<%@taglib prefix="shiro" uri="http://shiro.apache.org/tags" %>  
<%@ page import="com.clou.esp.hes.app.web.core.util.ResourceUtil" %>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://"
			+ request.getServerName() + ":" + request.getServerPort()
			+ path;
	String webVer="ver="+ResourceUtil.getSessionattachmenttitle("hesweb.system.pageVer");
    String jasperPath= "http://"+ResourceUtil.getSessionattachmenttitle("report_server_ip")+":"+ResourceUtil.getSessionattachmenttitle("report_server_port");
    String dateFormatter=  ResourceUtil.getSessionattachmenttitle("local.date.formatter");
    String dateAndTimeFormatter=  ResourceUtil.getSessionattachmenttitle("local.date.time.formatter");
    String timeFormatter=  ResourceUtil.getSessionattachmenttitle("local.time.formatter");
    String fgColDateFormatter=  ResourceUtil.getSessionattachmenttitle("fgCol.date.formatter");
    String fgColDateTimeFormatter=  ResourceUtil.getSessionattachmenttitle("fgCol.date.time.formatter");
    String timeFlag=  ResourceUtil.getSessionattachmenttitle("local.time.flag");
    String jsDateFormatter=  ResourceUtil.getSessionattachmenttitle("local.js.date.formatter"); 
    String jsTimeFormatter=  ResourceUtil.getSessionattachmenttitle("local.js.time.formatter"); 
    String jsDateAndTimeFormatter=  ResourceUtil.getSessionattachmenttitle("local.js.date.time.formatter"); 
    String jdbcType=  ResourceUtil.getSessionattachmenttitle("jdbc.type"); 
    String reportLanguage=  ResourceUtil.getSessionattachmenttitle("report_language"); 
    String HESWEB_SYSTEM_NAME =  ResourceUtil.getSessionattachmenttitle("hesweb.system.name"); 
    String HESWEB_SYSTEM_VERSION =  ResourceUtil.getSessionattachmenttitle("hesweb.system.version"); 
    String HESWEB_PROJECT_NAME =  ResourceUtil.getSessionattachmenttitle("hesweb.project.name") == null ? "" : ResourceUtil.getSessionattachmenttitle("hesweb.project.name"); 
    
%>
<c:set var="webRoot" value="<%=basePath%>" />
<c:set var="webVer" value="<%=webVer%>" />
<c:set var="jasperPath" value="<%=jasperPath%>" />
<c:set var="dateFormatter" value="<%=dateFormatter%>" />
<c:set var="dateAndTimeFormatter" value="<%=dateAndTimeFormatter%>" />
<c:set var="timeFormatter" value="<%=timeFormatter%>" />
<c:set var="fgColDateFormatter" value="<%=fgColDateFormatter%>" />
<c:set var="fgColDateTimeFormatter" value="<%=fgColDateTimeFormatter%>" />
<c:set var="timeFlag" value="<%=timeFlag%>" />
<c:set var="jsDateFormatter" value="<%=jsDateFormatter%>" />
<c:set var="jsTimeFormatter" value="<%=jsTimeFormatter%>" />
<c:set var="jsDateAndTimeFormatter" value="<%=jsDateAndTimeFormatter%>" />
<c:set var="jdbcType" value="<%=jdbcType%>" />
<c:set var="reportLanguage" value="<%=reportLanguage%>" />
<c:set var="HESWEB_SYSTEM_NAME" value="<%=HESWEB_SYSTEM_NAME%>" />
<c:set var="HESWEB_VERSION" value="<%=HESWEB_SYSTEM_VERSION%>" />
<c:set var="HESWEB_PROJECT_NAME" value="<%=HESWEB_PROJECT_NAME%>" />


