
package ch.iec.tc57._2011.meterreadings_;

import java.math.BigInteger;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * When present, a scalar conversion that needs to be
 * 				applied to every IntervalReading.value contained in IntervalBlock.
 * 				This conversion results in a new associated ReadingType, reflecting
 * 				the true dimensions of IntervalReading values after the conversion.
 * 			
 * 
 * <p>PendingCalculation complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="PendingCalculation"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="multiplyBeforeAdd" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="offset" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
 *         &lt;element name="scalarDenominator" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
 *         &lt;element name="scalarFloat" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
 *         &lt;element name="scalarNumerator" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
 *         &lt;element name="ReadingType"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="ref" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PendingCalculation", propOrder = {
    "multiplyBeforeAdd",
    "offset",
    "scalarDenominator",
    "scalarFloat",
    "scalarNumerator",
    "readingType"
})
public class PendingCalculation {

    protected Boolean multiplyBeforeAdd;
    protected BigInteger offset;
    protected BigInteger scalarDenominator;
    protected Float scalarFloat;
    protected BigInteger scalarNumerator;
    @XmlElement(name = "ReadingType", required = true)
    protected PendingCalculation.ReadingType readingType;

    /**
     * 获取multiplyBeforeAdd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isMultiplyBeforeAdd() {
        return multiplyBeforeAdd;
    }

    /**
     * 设置multiplyBeforeAdd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setMultiplyBeforeAdd(Boolean value) {
        this.multiplyBeforeAdd = value;
    }

    /**
     * 获取offset属性的值。
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getOffset() {
        return offset;
    }

    /**
     * 设置offset属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setOffset(BigInteger value) {
        this.offset = value;
    }

    /**
     * 获取scalarDenominator属性的值。
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getScalarDenominator() {
        return scalarDenominator;
    }

    /**
     * 设置scalarDenominator属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setScalarDenominator(BigInteger value) {
        this.scalarDenominator = value;
    }

    /**
     * 获取scalarFloat属性的值。
     * 
     * @return
     *     possible object is
     *     {@link Float }
     *     
     */
    public Float getScalarFloat() {
        return scalarFloat;
    }

    /**
     * 设置scalarFloat属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link Float }
     *     
     */
    public void setScalarFloat(Float value) {
        this.scalarFloat = value;
    }

    /**
     * 获取scalarNumerator属性的值。
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getScalarNumerator() {
        return scalarNumerator;
    }

    /**
     * 设置scalarNumerator属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setScalarNumerator(BigInteger value) {
        this.scalarNumerator = value;
    }

    /**
     * 获取readingType属性的值。
     * 
     * @return
     *     possible object is
     *     {@link PendingCalculation.ReadingType }
     *     
     */
    public PendingCalculation.ReadingType getReadingType() {
        return readingType;
    }

    /**
     * 设置readingType属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link PendingCalculation.ReadingType }
     *     
     */
    public void setReadingType(PendingCalculation.ReadingType value) {
        this.readingType = value;
    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType&gt;
     *   &lt;complexContent&gt;
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
     *       &lt;attribute name="ref" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
     *     &lt;/restriction&gt;
     *   &lt;/complexContent&gt;
     * &lt;/complexType&gt;
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "")
    public static class ReadingType {

        @XmlAttribute(name = "ref")
        protected String ref;

        /**
         * 获取ref属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getRef() {
            return ref;
        }

        /**
         * 设置ref属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setRef(String value) {
            this.ref = value;
        }

    }

}
