/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysMenu{ } 
 * 
 * 摘    要： 系统菜单
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-10-27 09:28:59
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.dict;

import java.util.ArrayList;
import java.util.List;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DictMenu  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DictMenu() {
	}

	/**utilityIds*/
	private java.lang.String utilityIds;
	/**菜单等级*/
	private java.lang.Integer menulevel;
	/**菜单名称*/
	private java.lang.String menuname;
	/**菜单排序*/
	private java.lang.String menuorder;
	/**功能ID*/
	private java.lang.String functionId;
	/**菜单地址*/
	private java.lang.String functionurl;
	/**父菜单编号 0=一级菜单；*/
	private java.lang.String parentmenuid;
	/**菜单简介*/
	private java.lang.String menuIntro;
	/**隐藏项*/
	private java.lang.String hideTab;
	
	/**
	 * 菜单ID
	 */
	private java.lang.String menuId;
	/**
	 * 角色ID
	 */
	private java.lang.String roleId;

	/**操作权限*/
	private java.lang.String operation;
	
	/**是否被角色选中**/
	private int isSelect = 0;
	/**只菜单列表*/
	private List<DictMenu> childList=new ArrayList<DictMenu>();

	/**
	 * utilityIds
	 * @return the value of SYS_MENU.VENDOR_IDS
	 * @mbggenerated 2017-10-27 09:28:59
	 */
	public java.lang.String getUtilityIds() {
		return utilityIds;
	}

	/**
	 * utilityIds
	 * @param utilityIds the value for SYS_MENU.VENDOR_IDS
	 * @mbggenerated 2017-10-27 09:28:59
	 */
    	public void setUtilityIds(java.lang.String utilityIds) {
		this.utilityIds = utilityIds;
	}
	/**
	 * 菜单等级
	 * @return the value of SYS_MENU.MENULEVEL
	 * @mbggenerated 2017-10-27 09:28:59
	 */
	public java.lang.Integer getMenulevel() {
		return menulevel;
	}

	/**
	 * 菜单等级
	 * @param menulevel the value for SYS_MENU.MENULEVEL
	 * @mbggenerated 2017-10-27 09:28:59
	 */
    	public void setMenulevel(java.lang.Integer menulevel) {
		this.menulevel = menulevel;
	}
	/**
	 * 菜单名称
	 * @return the value of SYS_MENU.MENUNAME
	 * @mbggenerated 2017-10-27 09:28:59
	 */
	public java.lang.String getMenuname() {
		return MutiLangUtil.assemblyI18nData(MutiLangUtil.MENU_I18N, menuname);
	}

	/**
	 * 菜单名称
	 * @param menuname the value for SYS_MENU.MENUNAME
	 * @mbggenerated 2017-10-27 09:28:59
	 */
    	public void setMenuname(java.lang.String menuname) {
		this.menuname = menuname;
	}
	/**
	 * 菜单排序
	 * @return the value of SYS_MENU.MENUORDER
	 * @mbggenerated 2017-10-27 09:28:59
	 */
	public java.lang.String getMenuorder() {
		return menuorder;
	}

	/**
	 * 菜单排序
	 * @param menuorder the value for SYS_MENU.MENUORDER
	 * @mbggenerated 2017-10-27 09:28:59
	 */
    	public void setMenuorder(java.lang.String menuorder) {
		this.menuorder = menuorder;
	}
	/**
	 * 菜单地址
	 * @return the value of SYS_MENU.FUNCTIONURL
	 * @mbggenerated 2017-10-27 09:28:59
	 */
	public java.lang.String getFunctionurl() {
		return functionurl;
	}

	/**
	 * 菜单地址
	 * @param functionurl the value for SYS_MENU.FUNCTIONURL
	 * @mbggenerated 2017-10-27 09:28:59
	 */
    	public void setFunctionurl(java.lang.String functionurl) {
		this.functionurl = functionurl;
	}
	/**
	 * 父菜单编号 0=一级菜单；
	 * @return the value of SYS_MENU.PARENTMENUID
	 * @mbggenerated 2017-10-27 09:28:59
	 */
	public java.lang.String getParentmenuid() {
		return parentmenuid;
	}

	/**
	 * 父菜单编号 0=一级菜单；
	 * @param parentmenuid the value for SYS_MENU.PARENTMENUID
	 * @mbggenerated 2017-10-27 09:28:59
	 */
    	public void setParentmenuid(java.lang.String parentmenuid) {
		this.parentmenuid = parentmenuid;
	}
	/**
	 * 菜单简介
	 * @return the value of SYS_MENU.MENU_INTRO
	 * @mbggenerated 2017-10-27 09:28:59
	 */
	public java.lang.String getMenuIntro() {
		return menuIntro;
	}

	/**
	 * 菜单简介
	 * @param menuIntro the value for SYS_MENU.MENU_INTRO
	 * @mbggenerated 2017-10-27 09:28:59
	 */
    	public void setMenuIntro(java.lang.String menuIntro) {
		this.menuIntro = menuIntro;
	}
    	
    	

	public List<DictMenu> getChildList() {
		return childList;
	}

	public void setChildList(List<DictMenu> childList) {
		this.childList = childList;
	}
	
	public java.lang.String getFunctionId() {
		return functionId;
	}

	public void setFunctionId(java.lang.String functionId) {
		this.functionId = functionId;
	}

	public DictMenu(java.lang.String utilityIds 
	,java.lang.Integer menulevel 
	,java.lang.String menuname 
	,java.lang.String menuorder 
	,java.lang.String functionurl 
	,java.lang.String parentmenuid 
	,java.lang.String menuIntro ) {
		super();
		this.utilityIds = utilityIds;
		this.menulevel = menulevel;
		this.menuname = menuname;
		this.menuorder = menuorder;
		this.functionurl = functionurl;
		this.parentmenuid = parentmenuid;
		this.menuIntro = menuIntro;
	}

	public int getIsSelect() {
		return isSelect;
	}

	public void setIsSelect(int isSelect) {
		this.isSelect = isSelect;
	}

	public java.lang.String getMenuId() {
		return menuId;
	}

	public void setMenuId(java.lang.String menuId) {
		this.menuId = menuId;
	}

	public java.lang.String getRoleId() {
		return roleId;
	}

	public void setRoleId(java.lang.String roleId) {
		this.roleId = roleId;
	}

	public java.lang.String getOperation() {
		return operation;
	}

	public void setOperation(java.lang.String operation) {
		this.operation = operation;
	}

	public java.lang.String getHideTab() {
		return hideTab;
	}

	public void setHideTab(java.lang.String hideTab) {
		this.hideTab = hideTab;
	}
	
	

}