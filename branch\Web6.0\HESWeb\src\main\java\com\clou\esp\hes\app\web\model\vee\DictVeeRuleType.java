/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictVeeRuleType{ } 
 * 
 * 摘    要： dictVeeRuleType
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-12-19 07:31:50
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.vee;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DictVeeRuleType  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DictVeeRuleType() {
	}

	/**name*/
	private java.lang.String name;
	/**sortId*/
	private java.math.BigDecimal sortId;
	/**type*/
	private java.math.BigDecimal type;
	/**descr*/
	private java.lang.String descr;

	/**
	 * name
	 * @return the value of DICT_VEE_RULE_TYPE.NAME
	 * @mbggenerated 2018-12-19 07:31:50
	 */
	public java.lang.String getName() {
		return name;
	}

	/**
	 * name
	 * @param name the value for DICT_VEE_RULE_TYPE.NAME
	 * @mbggenerated 2018-12-19 07:31:50
	 */
    	public void setName(java.lang.String name) {
		this.name = name;
	}
	/**
	 * sortId
	 * @return the value of DICT_VEE_RULE_TYPE.SORT_ID
	 * @mbggenerated 2018-12-19 07:31:50
	 */
	public java.math.BigDecimal getSortId() {
		return sortId;
	}

	/**
	 * sortId
	 * @param sortId the value for DICT_VEE_RULE_TYPE.SORT_ID
	 * @mbggenerated 2018-12-19 07:31:50
	 */
    	public void setSortId(java.math.BigDecimal sortId) {
		this.sortId = sortId;
	}
	/**
	 * type
	 * @return the value of DICT_VEE_RULE_TYPE.TYPE
	 * @mbggenerated 2018-12-19 07:31:50
	 */
	public java.math.BigDecimal getType() {
		return type;
	}

	/**
	 * type
	 * @param type the value for DICT_VEE_RULE_TYPE.TYPE
	 * @mbggenerated 2018-12-19 07:31:50
	 */
    	public void setType(java.math.BigDecimal type) {
		this.type = type;
	}
	/**
	 * descr
	 * @return the value of DICT_VEE_RULE_TYPE.DESCR
	 * @mbggenerated 2018-12-19 07:31:50
	 */
	public java.lang.String getDescr() {
		return descr;
	}

	/**
	 * descr
	 * @param descr the value for DICT_VEE_RULE_TYPE.DESCR
	 * @mbggenerated 2018-12-19 07:31:50
	 */
    	public void setDescr(java.lang.String descr) {
		this.descr = descr;
	}

	public DictVeeRuleType(java.lang.String name 
	,java.math.BigDecimal sortId 
	,java.math.BigDecimal type 
	,java.lang.String descr ) {
		super();
		this.name = name;
		this.sortId = sortId;
		this.type = type;
		this.descr = descr;
	}

}