/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeasurementProfileDi{ } 
 * 
 * 摘    要： assetMeasurementProfileDi
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-02-28 02:48:00
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.asset;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class AssetMeasurementProfileDi  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public AssetMeasurementProfileDi() {
	}
	/**mgId*/
	private java.lang.String mgId;
	/**profileId*/
	private java.lang.String profileId;
	/**dataitemId*/
	private java.lang.String dataitemId;
	/**sortId*/
	private java.lang.String sortId;
	
	/**dataitemName*/
	private java.lang.String dataitemName;
	
	private java.lang.String protocolCode;

	/**
	 * sortId
	 * @return the value of ASSET_MEASUREMENT_PROFILE_DI.SORT_ID
	 * @mbggenerated 2018-02-28 02:48:00
	 */
	public java.lang.String getSortId() {
		return sortId;
	}

	/**
	 * sortId
	 * @param sortId the value for ASSET_MEASUREMENT_PROFILE_DI.SORT_ID
	 * @mbggenerated 2018-02-28 02:48:00
	 */
    public void setSortId(java.lang.String sortId) {
		this.sortId = sortId;
	}

	public AssetMeasurementProfileDi(java.lang.String sortId ) {
		super();
		this.sortId = sortId;
	}

	public java.lang.String getMgId() {
		return mgId;
	}

	public void setMgId(java.lang.String mgId) {
		this.mgId = mgId;
	}

	public java.lang.String getProfileId() {
		return profileId;
	}

	public void setProfileId(java.lang.String profileId) {
		this.profileId = profileId;
	}

	public java.lang.String getDataitemId() {
		return dataitemId;
	}

	public void setDataitemId(java.lang.String dataitemId) {
		this.dataitemId = dataitemId;
	}

	public java.lang.String getProtocolCode() {
		return protocolCode;
	}

	public void setProtocolCode(java.lang.String protocolCode) {
		this.protocolCode = protocolCode;
	}

	public java.lang.String getDataitemName() {
		return MutiLangUtil.assemblyI18nData(MutiLangUtil.DATAITEM_I18N,dataitemName);
	}

	public void setDataitemName(java.lang.String dataitemName) {
		this.dataitemName = dataitemName;
	}
	
}