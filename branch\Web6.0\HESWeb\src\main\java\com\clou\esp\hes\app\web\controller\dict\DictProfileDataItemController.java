/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictProfileDataItem{ } 
 * 
 * 摘    要： dictProfileDataItem
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-02-26 09:36:52
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.dict;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.transaction.Transactional;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.dict.DictProfileDataItem;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.dict.DictProfileDataItemService;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;

/**
 * <AUTHOR>
 * @时间：2018-02-26 09:36:52
 * @描述：dictProfileDataItem类
 */
@Controller
@RequestMapping("/dictProfileDataItemController")
public class DictProfileDataItemController extends BaseController{

 	@Resource
    private DictProfileDataItemService dictProfileDataItemService;

	/**
	 * 跳转到dictProfileDataItem列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/dict/dictProfileDataItemList");
    }

	/**
	 * 跳转到dictProfileDataItem新增界面
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dictProfileDataItem")
	public ModelAndView dictProfileDataItem(String profileId,String protocolId,HttpServletRequest request, Model model) {
			model.addAttribute("profileId", profileId);
			model.addAttribute("protocolId", protocolId);
			model.addAttribute("channelTypeReplace", DictDataitemController.channelTypeReplace);
		return new ModelAndView("/dict/dictProfileDataItem");
	}


	/**
	 * dictProfileDataItem查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request
    		,String profileId,String dataitemId,String dataitemName,String protocolCode,String protocolId,String dataitemType,Boolean select) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        if(select==null||!select) {
        	return j;
        }
        try {
        	jqGridSearchTo.put("profileId", profileId);
        	jqGridSearchTo.put("dataitemId", dataitemId);
        	jqGridSearchTo.put("dataitemName", dataitemName);
        	jqGridSearchTo.put("protocolCode", protocolCode);
        	jqGridSearchTo.put("protocolId", protocolId);
        	jqGridSearchTo.put("dataitemType", dataitemType);
            j = dictProfileDataItemService.getForJqGrid(jqGridSearchTo);
        }catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
	
	/**
	 * dictProfileDataItem查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "unBindDatagrid")
	@ResponseBody
	public JqGridResponseTo unBindDatagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request
			,String profileId,String id,String name,String protocolCode,String protocolId,String dataitemType,Boolean select) {
		TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
		JqGridResponseTo j=null;
		if(select==null||!select) {
			return j;
		}
		try {
			jqGridSearchTo.put("profileId", profileId);
			jqGridSearchTo.put("dataitemId", id);
			jqGridSearchTo.put("dataitemName", name);
			jqGridSearchTo.put("protocolCode", protocolCode);
			jqGridSearchTo.put("protocolId", protocolId);
			jqGridSearchTo.put("dataitemType", dataitemType);
			j = dictProfileDataItemService.unBindForJqGrid(jqGridSearchTo);
		}catch (Exception e) {
			e.printStackTrace();
		}
		return j;
	}
	
    
    /**
     * 删除dictProfileDataItem信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DictProfileDataItem dictProfileDataItem,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
        	if(StringUtils.isNotEmpty(dictProfileDataItem.getProfileId())&&StringUtils.isNotEmpty(dictProfileDataItem.getDataitemId()))        		{
        		dictProfileDataItemService.delete(dictProfileDataItem);
        	}
	        j.setMsg(MutiLangUtil.doMutiLang("system.delSuccess"));
	    } catch (Exception e) {
	    	e.printStackTrace();
	    	j.setSuccess(false);
	    	j.setMsg(MutiLangUtil.doMutiLang("system.delFail"));
	    }
	    return j;
    }
    
 

	@Transactional
	@RequestMapping(value = "save")
	@ResponseBody
	public AjaxJson save( HttpServletRequest request,String profileId,String items) {
		AjaxJson j = new AjaxJson();
		try {
			JSONArray curArray = null;
			SysUser su=TokenManager.getToken();
			if(StringUtils.isNotEmpty(items)) {
				curArray= JSON.parseArray(items);
				DictProfileDataItem item = new DictProfileDataItem();
				if(curArray!=null) {
					for(int i=0;i<curArray.size();i++) {
						JSONObject jsonRestTemp = curArray.getJSONObject(i);
						String dataItemId = jsonRestTemp.getString("id");
						item.setDataitemId(dataItemId);
						item.setProfileId(profileId);
						item.setSortId(1);
						this.dictProfileDataItemService.save(item);//添加曲线数据项
					}
					
					j.setMsg(MutiLangUtil.doMutiLang("system.addSucc"));
				}
			}
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			j.setSuccess(false);
			j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
		}
		
		return j;
	}
	
	
	@RequestMapping(value  = "sort")
	public ModelAndView sort(String profileId,HttpServletRequest request, Model model) {
			model.addAttribute("profileId", profileId);
			model.addAttribute("channelTypeReplace", DictDataitemController.channelTypeReplace);
		return new ModelAndView("/dict/dictProfileDataitemSort");
	}
	
	
	 @RequestMapping(value = "saveSort")
	    @ResponseBody
	    public AjaxJson saveSort( HttpServletRequest request,String profileId,String items) {
	    	AjaxJson j = new AjaxJson();
	    	try {
	    		JSONArray curArray = null;
	    		if(StringUtils.isNotEmpty(items)) {
	    			curArray= JSON.parseArray(items);
	    			DictProfileDataItem item = new DictProfileDataItem();
	    			if(curArray!=null) {
	    				for(int i=0;i<curArray.size();i++) {
	    					JSONObject jsonRestTemp = curArray.getJSONObject(i);
	    					String dataItemId = jsonRestTemp.getString("id");
	    					String sortId = jsonRestTemp.getString("sortId");
	    					item.setDataitemId(dataItemId);
	    					item.setProfileId(profileId);   
	    					item.setSortId(Integer.parseInt(sortId));
	    					this.dictProfileDataItemService.update(item);
	    				}
	    				j.setMsg(MutiLangUtil.doMutiLang("system.updateSucc"));
	    			}
	    		}
	    	} catch (Exception e) {
	    		e.printStackTrace();
	    		j.setSuccess(false);
	    		j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
	    	}
	    	
	    	return j;
	    }
	
}