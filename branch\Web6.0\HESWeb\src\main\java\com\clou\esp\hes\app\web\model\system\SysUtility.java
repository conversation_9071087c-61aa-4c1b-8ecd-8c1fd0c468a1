/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysVendor{ } 
 * 
 * 摘    要： 租户表
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-10-27 09:29:00
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.system;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class SysUtility  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public SysUtility() {
	}

	/**1=使用中，2=过期，0=停用*/
	private java.lang.Integer state;
	/**name*/
	private java.lang.String name;
	/**description*/
	private java.lang.String description;
	/**readme*/
	private java.lang.String readme;

	/**租户开始时间*/
	private java.util.Date startDate;
	
	/**租户结束时间*/
	private java.util.Date endDate;
	
	/**租户开始时间*/
	private String startDateStr;
	
	/**租户结束时间*/
	private String endDateStr;
	
	/**mac*/
	private String mac;
	
	/**maxNum*/
	private String maxNum;
	
	
	
	
	public String getStartDateStr() {
		return startDateStr;
	}

	public void setStartDateStr(String startDateStr) {
		this.startDateStr = startDateStr;
	}

	public String getEndDateStr() {
		return endDateStr;
	}

	public void setEndDateStr(String endDateStr) {
		this.endDateStr = endDateStr;
	}

	public java.lang.String getReadme() {
		return readme;
	}

	public void setReadme(java.lang.String readme) {
		this.readme = readme;
	}

	public java.util.Date getStartDate() {
		return startDate;
	}

	public void setStartDate(java.util.Date startDate) {
		this.startDate = startDate;
	}

	public java.util.Date getEndDate() {
		return endDate;
	}

	public void setEndDate(java.util.Date endDate) {
		this.endDate = endDate;
	}

	public String getMac() {
		return mac;
	}

	public void setMac(String mac) {
		this.mac = mac;
	}

	public String getMaxNum() {
		return maxNum;
	}

	public void setMaxNum(String maxNum) {
		this.maxNum = maxNum;
	}

	/**
	 * 1=使用中，2=过期，0=停用
	 * @return the value of SYS_VENDOR.STATE
	 * @mbggenerated 2017-10-27 09:29:00
	 */
	public java.lang.Integer getState() {
		return state;
	}

	/**
	 * 1=使用中，2=过期，0=停用
	 * @param state the value for SYS_VENDOR.STATE
	 * @mbggenerated 2017-10-27 09:29:00
	 */
    	public void setState(java.lang.Integer state) {
		this.state = state;
	}
	/**
	 * name
	 * @return the value of SYS_VENDOR.NAME
	 * @mbggenerated 2017-10-27 09:29:00
	 */
	public java.lang.String getName() {
		return name;
	}

	/**
	 * name
	 * @param name the value for SYS_VENDOR.NAME
	 * @mbggenerated 2017-10-27 09:29:00
	 */
    	public void setName(java.lang.String name) {
		this.name = name;
	}
	/**
	 * description
	 * @return the value of SYS_VENDOR.DESCRIPTION
	 * @mbggenerated 2017-10-27 09:29:00
	 */
	public java.lang.String getDescription() {
		return description;
	}

	/**
	 * description
	 * @param description the value for SYS_VENDOR.DESCRIPTION
	 * @mbggenerated 2017-10-27 09:29:00
	 */
    	public void setDescription(java.lang.String description) {
		this.description = description;
	}

	public SysUtility(java.lang.Integer state 
	,java.lang.String name 
	,java.lang.String description ) {
		super();
		this.state = state;
		this.name = name;
		this.description = description;
	}

}