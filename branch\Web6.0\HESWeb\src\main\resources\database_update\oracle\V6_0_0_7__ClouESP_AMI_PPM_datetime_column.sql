truncate table PPM_DICT_USER_LOG;
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Vending Recharge','Recharge',1);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('vending Cancellation','Cancellation',2);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Engineering Token','Clear Tamper',1);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Engineering Token','Clear Credit',2);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Engineering Token','Change Secret Key',3);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Engineering Token','Change Max Power',4);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Customer Management','Add Customer',1);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Customer Management','Modify Customer',2);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Customer Management','Delete Customer',3);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Debt Management','Add Debt',1);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Debt Management','Delete Debt',2);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Vending Station Recharge','Recharge',1);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Vending Station Recharge','Cancellation',2);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Tariff Group Management','Add Tariff Group',1);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Tariff Group Management','Modify Tariff Group',2);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Tariff Group Management','Delete Tariff Group',3);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Tariff Group Management','Set Passvie Tariff Group',4);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Tariff Group Management','Activate Tariff Group',5);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Vending Station Management','Add Vending Station',1);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Vending Station Management','Delete Vending Station',2);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Vending Station Management','Modify Vending Station',3);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission Management','Add User',1);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission Management','Modify User',2);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission Management','Delete User',3);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission Management','Set Password',4);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('login/logout','Login',1);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('login/logout','Logout',2);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission Management','Add Role',1);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission Management','Modify Role',2);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission Management','Delete Role',3);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission Management','Add Organization',4);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission Management','Modify Organization',5);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Permission Management','Delete Organization',6);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Security Module Management','Recharge',1);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Security Module Management','Upload Key File',2);
Insert into PPM_DICT_USER_LOG (LOG_TYPE,LOG_SUB_TYPE,SORT_ID) values ('Security Module Management','Set SGC Default',3);

alter table ppm_dict_detail add obis_code varchar(256);
delete from ppm_dict_detail where dict_id in ('80','91','92');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '1', 'Power down', 'Power Down', '3.26.0.285');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '2', 'Power up', 'Power Up', '3.26.0.216');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '3', 'Daylight Saving Time Enabled/Disabled', 'Daylight Saving Time Enabled/Disable', '3.36.56.76');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '4', 'Clock Adjust (Old Date/Time)', 'Clock Adjust (old date/time)', '3.36.116.58');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '5', 'Clock Adjust (New Date/Time)', 'Clock Adjust (new date/time)', '3.36.116.59');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '6', 'Clock Invalid', 'Clock Invalid', '3.36.0.35');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '7', 'Replace Battery', 'Replace Battery', '3.2.0.284');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '8', 'Low Battery Voltage', 'Battery voltage low', '3.2.22.150');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '9', 'TOU Activated', 'TOU activated', '3.20.121.4');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '10', 'Error Register Cleared', 'Error Register Cleared', '3.0.89.28');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '11', 'Alarm Register Cleared', 'Alarm Register Cleared', '3.17.285.28');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '12', 'Meter Program Memory Error', 'Meter Program Memory Error', '3.18.83.79');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '13', 'RAM Error', 'RAM Error', '3.18.85.79');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '14', 'NV Memory Error', 'NV Memory Error', '3.18.72.79');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '15', 'Watchdog Error', 'Watchdog Error', '3.37.0.79');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '16', 'Measurement System Error', 'Measurement System Error', '3.21.67.79');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '17', 'Firmware Ready for Activation', 'Firmware ready for activation', '3.11.17.280');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '18', 'Firmware Activated', 'Firmware activated', '3.11.17.4');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '19', 'Passive TOU  programmed', 'Passive ToU programmed', '3.20.121.213');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '20', 'Parameter Changed', 'Parameter(s) Changed', '3.7.75.24');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '21', 'Security Key Changed', 'Security key(s) changed', '3.12.32.24');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '22', 'Firmware Verification Failed', 'Firmware verification failed', '3.11.46.35');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '23', 'Unexpected Consumption', 'Unexpected consumption', '3.8.0.40');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '24', 'Phase Sequence Reversal', 'Phase sequence reversal', '3.7.25.91');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '25', 'Missing Neutral', 'Missing neutral', '3.26.137.85');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '26', 'Disconnection Timeout', 'No connection timeout', '3.19.212.85');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '27', 'Modem Initialization Failure', 'Modem Initialization Failure', '3.19.298.85');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '28', 'SIM Card Failure', 'SIM Card Failure', '3.19.69.85');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '29', 'SIM Card OK', 'SIM Card Ok', '3.19.69.37');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '30', 'GSM Registration Failure', 'GSM Registration Failure', '3.19.90.85');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '31', 'GPRS Registration Failure', 'GPRS Registration Failure', '3.19.91.85');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '32', 'Modem SW Reset', 'Modem SW Reset', '3.19.0.214');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '33', 'GSM Outgoing Connection', 'GSM Outgoing Connection', '3.19.0.42');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '34', 'GSM Incoming Connection', 'GSM Incoming Connection', '3.19.1.42');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '35', 'GSM Hang-up', 'GSM Hang-up', '3.19.0.68');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '36', 'User Initialization Failure', 'User Initialization Failure', '3.19.299.85');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '37', 'Low Signal Quality', 'Signal Quality Low', '3.23.0.47');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '38', 'Terminal Cover Opened', 'Terminal Cover opened', '3.12.141.39');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '39', 'Terminal Cover Closed', 'Terminal Cover closed', '3.12.141.16');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '40', 'Strong DC Field Detected', 'Strong DC field detected', '3.12.66.257');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '41', 'Strong DC Field Removed', 'Strong DC field removed', '3.12.66.212');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '42', 'Meter Cover Opened', 'Meter cover opened', '3.12.29.39');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '43', 'Meter Cover Closed', 'Meter cover closed', '3.12.29.16');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '44', 'Association Authentication failures', 'Association authentication failure after n times', '3.23.74.85');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '45', 'Decryption or Message Authentication failure', 'Decryption or Message Authentication failure', '3.12.36.85');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '46', 'Active Power Reversal', 'Active Power Reversal', '3.0.48.219');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '47', 'Disconnector in \"Ready for Reconnection\"', 'Disconnector in \"Ready for Reconnection\"', '3.31.0.280');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '48', 'Manual Disconnection', 'Manual Disconnection', '3.31.0.68');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '49', 'Manual Connection', 'Manual Connection', '3.31.0.42');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '50', 'Remote Disconnection', 'Remote Disconnection', '3.31.211.68');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '51', 'Remote Connection', 'Remote Connection', '3.31.211.42');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '52', 'Local Disconnection', 'Local Disconnection', '3.31.1.68');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '53', 'Limiter Threshold Exceed', 'Limiter threshold exceed', '3.8.261.139');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '54', 'Limiter Threshold OK', 'Limiter threshold OK', '3.8.261.37');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '55', 'Limiter Threshold Changed', 'Limiter threshold changed', '3.8.0.295');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '56', 'Disconnect/Reconnect Failure', 'Disconnect /Reconnect failure', '3.31.0.67');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '57', 'Local Reconnection', 'Local Reconnection', '3.31.0.49');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '58', 'Fuse supervision L1, threshold exceeded', 'Fuse supervision L1, threshold exceeded', '3.8.287.139');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '59', 'Fuse supervision L1, threshold OK', 'Fuse supervision L1, threshold OK', '3.8.287.37');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '60', 'Fuse supervision L2, threshold exceeded', 'Fuse supervision L2, threshold exceeded', '3.8.288.139');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '61', 'Fuse supervision L2, threshold OK', 'Fuse supervision L2, threshold OK', '3.8.288.37');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '62', 'Fuse supervision L3, threshold exceeded', 'Fuse supervision L3, threshold exceeded', '3.8.289.139');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '63', 'Fuse supervision L3, threshold OK', 'Fuse supervision L3, threshold OK', '3.8.289.37');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '64', 'Under Voltage (voltage SAG) L1', 'Under voltage (voltage SAG) L1', '3.26.131.223');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '65', 'Under Voltage (voltage SAG) L2', 'Under voltage (voltage SAG) L2', '3.26.132.223');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '66', 'Under Voltage (voltage SAG) L3', 'Under voltage (voltage SAG) L3', '3.26.133.223');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '67', 'Over Voltage (voltage SWELL) L1', 'Over voltage (voltage SWELL) L1', '3.26.131.248');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '68', 'Over Voltage (voltage SWELL) L2', 'Overvoltage (voltage SWELL) L2', '3.26.132.248');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '69', 'Over Voltage (voltage SWELL) L3', 'Overvoltage (voltage SWELL) L3', '3.26.133.248');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '70', 'Missing Voltage (Voltage Cut) L1', 'Missing Voltage (Voltage Cut) L1', '3.26.131.285');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '71', 'Missing Voltage (Voltage Cut) L2', 'Missing Voltage (Voltage Cut) L2', '3.26.132.285');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '72', 'Missing Voltage (Voltage Cut) L3', 'Missing Voltage (Voltage Cut) L3', '3.26.133.285');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '73', 'Normal Voltage L1', 'Normal Voltage L1', '3.26.131.37');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '74', 'Normal Voltage L2', 'Normal Voltage L2', '3.26.132.37');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '75', 'Normal Voltage L3', 'Normal Voltage L3', '3.26.133.37');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '76', 'Phase Asymmetry', 'Phase Asymmetry', '3.26.0.98');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '77', 'Bad Voltage Quality L1', 'Bad Voltage Quality L1', '3.26.131.40');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '78', 'Bad Voltage Quality L2', 'Bad Voltage Quality L2', '3.26.132.40');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '79', 'Bad Voltage Quality L3', 'Bad Voltage Quality L3', '3.26.133.40');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '80', 'Over Current L1 started', 'Over current L1 started', '3.26.6.152');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '81', 'Over Current L1 stopped', 'Over current L1 stopped', '3.26.6.153');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '82', 'Over Current L2 started', 'Over current L2 started', '3.26.7.152');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '83', 'Over Current L2 stopped', 'Over current L2 stopped', '3.26.7.153');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '84', 'Over Current L3 started', 'Over current L3 started', '3.26.8.152');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '85', 'Over Current L3 stopped', 'Over current L3 stopped', '3.26.8.153');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '86', 'Loss Current L1 started', 'Loss current L1 started', '3.26.6.285');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '87', 'Loss Current L1 stopped', 'Loss current L1 stopped', '3.26.6.286');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '88', 'Loss Current L2 started', 'Loss current L2 started', '3.26.7.285');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '89', 'Loss Current L2 stopped', 'Loss current L2 stopped', '3.26.7.286');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '90', 'Loss Current L3 started', 'Loss current L3 started', '3.26.8.285');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '91', 'Loss Current L3 stopped', 'Loss current L3 stopped', '3.26.8.286');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '92', 'Active Power Reverse L1 started', 'Active Power Reverse L1 started', '3.26.26.285');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '93', 'Active Power Reverse L1 stopped', 'Active Power Reverse L1 stopped', '3.26.26.286');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '94', 'Active Power Reverse L2 started', 'Active Power Reverse L2 started', '3.26.27.285');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '95', 'Active Power Reverse L2 stopped', 'Active Power Reverse L2 stopped', '3.26.27.286');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '96', 'Active Power Reverse L3 started', 'Active Power Reverse L3 started', '3.26.28.285');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '97', 'Active Power Reverse L3 stopped', 'Active Power Reverse L3 stopped', '3.26.28.286');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '98', 'Power Overload L1 started', 'Power Overload L1 started', '3.26.26.152');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '99', 'Power Overload L1 stopped', 'Power Overload L1 stopped', '3.26.26.153');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '100', 'Power Overload L2 started', 'Power Overload L2 started', '3.26.27.152');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '101', 'Power Overload L2 stopped', 'Power Overload L2 stopped', '3.26.27.153');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '102', 'Power Overload L3 started', 'Power Overload L3 started', '3.26.28.152');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '103', 'Power Overload L3 stopped', 'Power Overload L3 stopped', '3.26.28.153');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '104', 'Voltage Unbalance started', 'Voltage unbalance started', '3.26.33.98');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '105', 'Voltage Unbalance stopped', 'Voltage unbalance stopped', '3.26.33.99');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '106', 'Phase Failure L1 started', 'Phase Failure L1 started', '3.26.131.152');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '107', 'Phase Failure L1 stopped', 'Phase Failure L1 stopped', '3.26.131.153');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '108', 'Phase Failure L2 started', 'Phase Failure L2 started', '3.26.132.152');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '109', 'Phase Failure L2 stopped', 'Phase Failure L2 stopped', '3.26.132.153');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '110', 'Phase Failure L3 started', 'Phase Failure L3 started', '3.26.133.152');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '111', 'Phase Failure L3 stopped', 'Phase Failure L3 stopped', '3.26.133.153');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '112', 'Low Power Factor started', 'Low Power Factor started', '3.26.27.219');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '113', 'Low Power Factor stopped', 'Low Power Factor stopped', '3.26.27.220');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '114', 'High Temperature startted', 'High Temperature startted', '3.1.1.219');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '115', 'High Temperature stopped', 'High Temperature stopped', '3.1.1.220');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '116', 'Module Cover removed', 'Module Cover removed', '3.1.2.219');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '117', 'Module Cover closed', 'Module Cover closed', '3.1.2.220');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '118', 'CT Bypass startted', 'CT bypass startted', '3.1.3.219');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '119', 'CT Bypass stopped', 'CT bypass stopped', '3.1.3.220');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '120', 'Low Frequency startted', 'Low frequency startted', '3.1.4.219');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '121', 'Low Frequency stopped', 'Low frequency stopped', '3.1.4.220');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '122', 'High Frequency startted', 'High frequency startted', '3.1.5.219');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '123', 'High Frequency stopped', 'High frequency stopped', '3.1.5.220');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '124', 'External Alert detected', 'External alert detected', '3.7.75.25');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '125', 'Meter Data cleared', 'Meter data cleared', '3.40.0.1');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '126', 'Load Profile cleared', 'Load profile cleared', '3.40.0.2');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '127', 'Event Log cleared', 'Event log cleared', '3.40.0.3');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '128', 'Event Log cleared', 'Event log cleared', '3.40.0.4');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '129', 'Relay Attack', 'Relay attack', '3.12.38.1');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '130', 'Event Log cleared', 'Event log cleared', '3.41.0.1');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '131', 'Event Log cleared', 'Event log cleared', '3.42.0.1');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '132', 'Event Log cleared', 'Event log cleared', '3.41.4.1');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '133', 'PDP Context established', 'PDP context established', '3.19.92.85');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '134', 'PDP Context destoryed', 'PDP context destoryed', '3.19.93.85');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '135', 'PDP Context Failure', 'PDP context failure', '3.19.94.85');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '136', 'Modem HW Reset', 'Modem HW Reset', '3.19.0.215');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '137', 'Diagnostic Failure', 'Diagnostic failure', '3.19.0.72');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '138', 'Auto answer number of calls exceeded', 'Auto answer number of calls exceeded', '3.23.0.48');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '139', 'Local Communication Attempt', 'Local communication attempt', '3.23.0.49');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '140', 'Meter cover opened', '开面盖发生', '50.0.5.1');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '141', 'Terminal cover opened', '开端盖发生', '50.0.5.2');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '142', 'Battery voltage low started', '电池低电压', '50.0.7.1');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '143', 'Battery voltage low stopped', '电池恢复正常', '50.0.7.2');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '144', 'Battery down started', '电池耗尽发生', '50.0.7.3');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('80', '145', 'Battery down stopped', '电池耗尽结束', '50.0.7.4');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '1', 'Success / No errors', 'OK', '0.0');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '2', 'Partial result (additional results conveyed in separate messages)', '部分结果(在单独消息中传递的其他结果)', '0.1');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '3', 'Partial result (no further results to follow)', '部分结果(没有后续结果)', '0.2');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '4', 'IEC61968-9 ACK OK', 'IEC61968-9 ACK OK', '0.3');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '5', 'Mandatory Header elements missing', '缺少强制头元素', '1.5');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '6', 'Mandatory Request elements missing', '缺少强制请求元素', '1.6');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '7', 'Mandatory Payload elements missing', '缺少强制有效载荷元素', '1.7');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '8', 'Format of request does not validate against schema', '请求的格式不能根据模式进行验证', '1.8');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '9', 'Unsupported message revision in Header', '标题中不支持的消息修订', '1.9');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '10', 'Invalid Meter(s)(This meter does not exist in the HES system)', '无效的电表(HES系统中不存在此电表)', '2.4');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '11', 'Invalid Noun', '无效的名词', '2.5');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '12', 'Invalid ReadingType(s)', '无效的读类型', '2.6');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '13', 'Invalid Verb', '无效的动词', '2.9');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '14', 'Unsupported ReadingType(s)', '不支持的读类型', '2.10');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '15', 'Invalid UsagePoint(s)', '无效的使用点', '2.12');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '16', 'Meter / UsagePoint mismatch', '表/使用点不匹配', '2.13');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '17', 'Invalid Source', '无效的来源', '2.14');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '18', 'Invalid Request ID(s)', '无效的请求ID', '2.15');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '19', 'Invalid ServiceLocation(s)', '无效的服务点位置', '2.16');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '20', 'Meter / ServiceLocation mismatch*', '表/服务点位置不匹配*', '2.17');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '21', 'ComModule / Meter mismatch*', '通讯模块/表不匹配*', '2.18');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '22', 'Invalid CustomerAccount(s)', '无效的客户账户', '2.19');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '23', 'Invalid ServiceSupplier(s)', '无效的服务供应商', '2.20');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '24', 'CustomerAccount / ServiceSupplier mismatch', '客户帐户/服务供应商不匹配', '2.21');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '25', 'Invalid Customer(s)', '无效的客户', '2.22');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '26', 'Customer / CustomerAccount mismatch', '客户/客户帐户不匹配', '2.23');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '27', 'Invalid CustomerAgreement(s)', '无效的客户协议', '2.24');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '28', 'CustomerAccount / CustomerAgreement mismatch', '客户帐户/客户协议不匹配', '2.25');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '29', 'CustomerAgreement / UsagePoint mismatch', '客户协议/使用点不匹配', '2.26');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '30', 'CustomerAccount / UsagePoint mismatch', '客户帐户/使用点不匹配', '2.27');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '31', 'ServiceSupplier / UsagePoint mismatch', '服务供应商/使用点不匹配', '2.28');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '32', 'Object relationship mismatch', '对象关系不匹配', '2.29');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '33', 'Invalid ComModule(s)', '无效的通讯模块', '2.30');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '34', 'Invalid ServiceCategory(ies)', '无效的服务类别(ies)', '2.31');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '35', 'Invalid UsagePointLocation(s)', '无效使用点位置', '2.32');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '36', 'Invalid PricingStructure(s)', '无效的定价结构', '2.33');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '37', 'Too many items in request', '请求的项目太多', '3.1');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '38', 'Too many pending requests', '等待的请求太多', '3.2');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '39', 'Request timed out', '请求超时', '4.1');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '40', 'Unable to process the request - high system activity level', '无法处理请求高的系统活动级别', '5.1');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '41', 'Unable to process request -transaction not attempted', '无法处理请求-未尝试事务', '5.2');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '42', 'Unable to process the request - transaction attempted and failed ', '无法处理请求事务——尝试并失败', '5.3');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '43', 'Unable to process the request - multiple error types encountered', '无法处理遇到的请求-多个错误类型', '5.4');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '44', 'Some or all of the requested ReadingTypes are unavailable in MDMS', '某些或所有请求的读取类型在MDMS中不可用', '5.5');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '45', 'Some or all of the requested ReadingTypes are unavailable in AMI', '在AMI中，一些或所有请求的读取类型都不可用', '5.6');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '46', 'Some or all of the requested data is unavailable', '请求的部分或全部数据不可用', '5.7');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '47', 'Unable to process the request – mandatory field(s) missing', '无法处理缺少的请求强制字段', '5.8');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '48', 'Transaction aborted to maintain transactional integrity', '事务中止以维护事务完整性', '5.9');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '49', 'Request canceled per business rule', '根据业务规则取消请求', '6.1');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '50', 'Request placed on hold per business rule', '根据业务规则搁置的请求', '6.2');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '51', 'Request released from business rule hold', '从业务规则保持中释放的请求', '6.3');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '52', 'Request rescheduled per business rule', '请求按业务规则重新调度', '6.4');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '53', 'Request canceled by user', '用户取消的请求', '6.5');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '54', 'Temporary authentication failure', '临时身份验证失败', '7.1');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '55', 'Authentication required', '需要认证', '7.2');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '56', 'Authentication mechanism insufficient', '认证机制不足', '7.3');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '57', 'Authentication failure', '认证失败', '7.4');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '58', 'Action not authorized for user', '未经用户授权的操作', '7.5');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '59', 'Authentication mechanism requires encryption', '认证机制需要加密', '7.6');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '60', 'Policy violation', '违反安全策略', '7.7');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '61', 'Request time out', '请求超时', '100.2');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '62', 'Local error in processing', '程序本地处理错误', '100.3');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '63', 'Meter unidentified responds', '表计回复报文无法识别', '100.4');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '64', 'Channel is busy', '通道繁忙', '100.5');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '65', '(Meter) Device offline', '(电表)设备离线', '100.6');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '66', 'Cosem associate failed', 'cosem层建立连接失败', '100.7');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '67', 'Packet decrypt failed', '包解密失败', '100.8');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '68', 'Response invalid data', '响应无效数据', '100.9');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('91', '69', 'DCU denies the request', 'DCU拒绝请求', '100.10');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('92', '-1', 'N/A (Not applicable)', 'Token不适用', '2');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('92', '0', '(0)Token format result OK', '(0)Token格式正确', '1');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('92', '1', '(1)Authentication result OK', '(1)身份认证成功', '1');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('92', '2', '(2)Validation result OK', '(2)验证成功', '1');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('92', '3', '(3)Token execution result OK', '(3)Token执行成功', '1');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('92', '4', '(4)Token format failure', '(4)Token格式失败', '2');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('92', '5', '(5)Authentication failure', '(5)身份认证失败', '2');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('92', '6', '(6)Validation result failure', '(6)验证结果失败', '2');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('92', '7', '(7)Token execution result failure', '(7)Token 执行结果失败', '2');
INSERT INTO PPM_DICT_DETAIL (DICT_ID, INNER_VALUE, GUI_DISPLAY_NAME,readme, OBIS_Code) VALUES ('92', '8', '(8)Token received and not yet processed', '(8)Token已收到，尚未处理', '2');
