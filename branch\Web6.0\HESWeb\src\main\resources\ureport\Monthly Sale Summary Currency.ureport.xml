<?xml version="1.0" encoding="UTF-8"?><ureport><cell expand="None" name="A1" row="1" col="1"><cell-style font-size="10" forecolor="0,0,0" font-family="Times New Roman" bold="false" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Tariff Class]]></simple-value></cell><cell expand="None" name="B1" row="1" col="2"><cell-style font-size="10" forecolor="0,0,0" font-family="Times New Roman" bold="false" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[No. of Accounts]]></simple-value></cell><cell expand="None" name="C1" row="1" col="3"><cell-style font-size="10" forecolor="0,0,0" font-family="Times New Roman" bold="false" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Consumption]]></simple-value></cell><cell expand="None" name="D1" row="1" col="4"><cell-style font-size="10" forecolor="0,0,0" font-family="Times New Roman" bold="false" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Demand]]></simple-value></cell><cell expand="None" name="E1" row="1" col="5"><cell-style font-size="10" forecolor="0,0,0" font-family="Times New Roman" bold="false" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Service Charge]]></simple-value></cell><cell expand="None" name="F1" row="1" col="6"><cell-style font-size="10" forecolor="0,0,0" font-family="Times New Roman" bold="false" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[NEL]]></simple-value></cell><cell expand="None" name="G1" row="1" col="7"><cell-style font-size="10" forecolor="0,0,0" font-family="Times New Roman" bold="false" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Street L]]></simple-value></cell><cell expand="None" name="H1" row="1" col="8"><cell-style font-size="10" forecolor="0,0,0" font-family="Times New Roman" bold="false" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[PF]]></simple-value></cell><cell expand="None" name="I1" row="1" col="9"><cell-style font-size="10" forecolor="0,0,0" font-family="Times New Roman" bold="false" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[NHIS]]></simple-value></cell><cell expand="None" name="J1" row="1" col="10"><cell-style font-size="10" forecolor="0,0,0" font-family="Times New Roman" bold="false" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[GETFUND]]></simple-value></cell><cell expand="None" name="K1" row="1" col="11"><cell-style font-size="10" forecolor="0,0,0" font-family="Times New Roman" bold="false" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[VAT]]></simple-value></cell><cell expand="None" name="L1" row="1" col="12"><cell-style font-size="10" forecolor="0,0,0" font-family="Times New Roman" bold="false" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Total Due]]></simple-value></cell><cell expand="None" name="M1" row="1" col="13"><cell-style font-size="10" forecolor="0,0,0" font-family="Times New Roman" bold="false" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Govt Subsidy]]></simple-value></cell><cell expand="Down" name="A2" row="2" col="1"><cell-style font-size="9" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="tariffType" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="B2" row="2" col="2"><cell-style font-size="9" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="customerNumber" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="C2" row="2" col="3"><cell-style font-size="9" font-family="Times New Roman" format="#.####" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="energyCost" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="D2" row="2" col="4"><cell-style font-size="9" font-family="Times New Roman" format="#.####" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="demand" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="E2" row="2" col="5"><cell-style font-size="9" font-family="Times New Roman" format="#.####" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="serviceCharge" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="F2" row="2" col="6"><cell-style font-size="9" font-family="Times New Roman" format="#.####" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="nel" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="G2" row="2" col="7"><cell-style font-size="9" font-family="Times New Roman" format="#.####" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="stl" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="H2" row="2" col="8"><cell-style font-size="9" font-family="Times New Roman" format="#.####" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="pfCharge" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="I2" row="2" col="9"><cell-style font-size="9" font-family="Times New Roman" format="#.####" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="nhis" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="J2" row="2" col="10"><cell-style font-size="9" font-family="Times New Roman" format="#.####" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="getfund" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="K2" row="2" col="11"><cell-style font-size="9" font-family="Times New Roman" format="#.####" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="vat" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="L2" row="2" col="12"><cell-style font-size="9" font-family="Times New Roman" format="#.####" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="debtCollected" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="M2" row="2" col="13"><cell-style font-size="9" font-family="Times New Roman" format="#.####" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="govtSubsidy" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="A3" row="3" col="1"><cell-style font-size="9" font-family="Times New Roman" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Total]]></simple-value></cell><cell expand="None" name="B3" row="3" col="2" top-cell="B1"><cell-style font-size="9" font-family="Times New Roman" format="#" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="customerNumber" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="C3" row="3" col="3" top-cell="C1"><cell-style font-size="9" font-family="Times New Roman" format="#.####" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="energyCost" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="D3" row="3" col="4" top-cell="D1"><cell-style font-size="9" font-family="Times New Roman" format="#.####" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="demand" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="E3" row="3" col="5" top-cell="E1"><cell-style font-size="9" font-family="Times New Roman" format="#.####" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="serviceCharge" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="F3" row="3" col="6" top-cell="F1"><cell-style font-size="9" font-family="Times New Roman" format="#.####" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="nel" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="G3" row="3" col="7" top-cell="G1"><cell-style font-size="9" font-family="Times New Roman" format="#.####" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="stl" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="H3" row="3" col="8" top-cell="G1"><cell-style font-size="9" font-family="Times New Roman" format="#.####" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="pfCharge" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="I3" row="3" col="9" top-cell="J1"><cell-style font-size="9" font-family="Times New Roman" format="#.####" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="nhis" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="J3" row="3" col="10" top-cell="K1"><cell-style font-size="9" font-family="Times New Roman" format="#.####" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="getfund" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="K3" row="3" col="11" top-cell="K1"><cell-style font-size="9" font-family="Times New Roman" format="#.####" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="vat" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="L3" row="3" col="12" top-cell="L1"><cell-style font-size="9" font-family="Times New Roman" format="#.####" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="debtCollected" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="M3" row="3" col="13" top-cell="M1"><cell-style font-size="9" font-family="Times New Roman" format="#.####" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="govtSubsidy" order="none" mapping-type="simple"></dataset-value></cell><row row-number="1" height="18" band="title"/><row row-number="2" height="18"/><row row-number="3" height="18"/><column col-number="1" width="80"/><column col-number="2" width="80"/><column col-number="3" width="80"/><column col-number="4" width="74"/><column col-number="5" width="80"/><column col-number="6" width="74"/><column col-number="7" width="74"/><column col-number="8" width="74"/><column col-number="9" width="74"/><column col-number="10" width="74"/><column col-number="11" width="74"/><column col-number="12" width="74"/><column col-number="13" width="74"/><datasource name="monthlySalesSummaryCurrencyDataSource" type="spring" bean="monthlySalesSummaryCurrencyDataSource"><dataset name="reportData" type="bean" method="loadReportData" clazz="com.clou.esp.ppm.app.web.ureport.dataset.MonthlySalesSummaryCurrencyDataSet"><field name="customerNumber"/><field name="debtCollected"/><field name="demand"/><field name="energyCost"/><field name="getfund"/><field name="govtSubsidy"/><field name="mdCharge"/><field name="nel"/><field name="nhis"/><field name="pfCharge"/><field name="serviceCharge"/><field name="stl"/><field name="subsidy"/><field name="tariffType"/><field name="totalCost"/><field name="vat"/></dataset></datasource><paper type="A4" left-margin="90" right-margin="90"
    top-margin="72" bottom-margin="72" paging-mode="fitpage" fixrows="0"
    width="595" height="842" orientation="portrait" html-report-align="left" bg-image="" html-interval-refresh-value="0" column-enabled="false"></paper></ureport>