
package clouesp.hes.core.uci.soap.custom.asset_refresh;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>refresh complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="refresh"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="msgs" type="{http://asset_refresh.custom.soap.uci.core.hes.clouesp/}RefreshMessage" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "refresh", propOrder = {
    "msgs"
})
public class Refresh {

    protected List<RefreshMessage> msgs;

    /**
     * Gets the value of the msgs property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the msgs property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getMsgs().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link RefreshMessage }
     * 
     * 
     */
    public List<RefreshMessage> getMsgs() {
        if (msgs == null) {
            msgs = new ArrayList<RefreshMessage>();
        }
        return this.msgs;
    }

}
