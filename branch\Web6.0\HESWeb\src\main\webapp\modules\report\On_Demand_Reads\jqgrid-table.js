$(document).ready(function() {
	$.jgrid.defaults.styleUI = "Bootstrap";
	var mydata = [{
		id: "1",
		invdate:"SN546100037221",
		name:  "12/20/2017 15:45:00",
		note: "19.0(kWh)",
		amount: "Done",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "2",
		invdate:"SN546100037221",
		name:  "12/20/2017 15:45:00",
		note: "17.0(kWh)",
		amount: "Done",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total1]",
		totals: "430.00"
	}, {
		id: "3",
		invdate: "SN546100037221",
		name:  "12/20/2017 15:45:00",	
		note: "19.0(kWh)",
		amount: "Done",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total2]",
		totals: "430.00"
		
	}, {
		id: "4",
		invdate: "SN546100037221",
		name:  "12/20/2017 15:45:00",
		note: "19.0(kWh)",
		amount: "Done",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total3]",
		totals: "430.00"
	}, {
		id: "5",
		invdate: "SN546100037222",
		name:  "12/20/2017 15:45:00",
		amount: "Timeout",
	}, {
		id: "6",
		invdate: "SN546100037222",
		name:  "12/20/2017 15:45:00",
		note: "18.0(kWh)",
		amount: "Done",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total1]",
		totals: "430.00"
	}, {
		id: "7",
		invdate: "SN546100037222",
		name:  "12/20/2017 15:45:00",
		note: "19.0(kWh)",
		amount: "Done",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total2]",
		totals: "430.00"
	}, {
		id: "8",
		invdate: "SN546100037222",
		name:  "12/20/2017 15:45:00",
		note: "17.0(kWh)",
		amount: "Done",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total3]",
		totals: "430.00",
	}, {
		id: "9",
		invdate: "SN546100037223",
		name:  "12/20/2017 15:45:00",
		note: "18.0(kWh)",
		amount: "Done",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "SN546100037223",
		name:  "12/20/2017 15:45:00",
		amount: "Timeout",
	}, {
		id: "10",
		invdate: "SN546100037224",
		name:  "12/20/2017 15:45:00",
		note: "17.0(kWh)",
		amount: "Timeout",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "SN546100037221",
		name:  "12/20/2017 15:45:00",
		note: "17.0(kWh)",
		amount: "Timeout",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "SN546100037221",
		name:  "12/20/2017 15:45:00",
		note: "17.0(kWh)",
		amount: "Timeout",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "SN546100037221",
		name:  "12/20/2017 15:45:00",
		note: "17.0(kWh)",
		amount: "Timeout",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "SN546100037221",
		name:  "12/20/2017 15:45:00",
		note: "17.0(kWh)",
		amount: "Timeout",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "SN546100037221",
		name:  "12/20/2017 15:45:00",
		note: "17.0(kWh)",
		amount: "Timeout",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "SN546100037221",
		name:  "12/20/2017 15:45:00",
		note: "17.0(kWh)",
		amount: "Timeout",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "SN546100037221",
		name:  "12/20/2017 15:45:00",
		note: "17.0(kWh)",
		amount: "Timeout",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "SN546100037221",
		name:  "12/20/2017 15:45:00",
		note: "17.0(kWh)",
		amount: "Timeout",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "SN546100037221",
		name:  "12/20/2017 15:45:00",
		note: "17.0(kWh)",
		amount: "Timeout",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "SN546100037221",
		name:  "12/20/2017 15:45:00",
		note: "17.0(kWh)",
		amount: "Timeout",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "SN546100037221",
		name:  "12/20/2017 15:45:00",
		note: "17.0(kWh)",
		amount: "Timeout",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "SN546100037221",
		name:  "12/20/2017 15:45:00",
		note: "17.0(kWh)",
		amount: "Timeout",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "SN546100037221",
		name:  "12/20/2017 15:45:00",
		note: "17.0(kWh)",
		amount: "Timeout",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "SN546100037221",
		name:  "12/20/2017 15:45:00",
		note: "17.0(kWh)",
		amount: "Timeout",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "SN546100037221",
		name:  "12/20/2017 15:45:00",
		note: "17.0(kWh)",
		amount: "Timeout",
		
	}, {
		id: "10",
		invdate: "SN546100037221",
		name:  "12/20/2017 15:45:00",
		note: "17.0(kWh)",
		amount: "Timeout",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}, {
		id: "10",
		invdate: "SN546100037221",
		name:  "12/20/2017 15:45:00",
		note: "17.0(kWh)",
		amount: "Timeout",
		tax:  "12/20/2017 15:45:03",
		total: "Active Energy -Import [Register] [Total]",
		totals: "430.00"
	}];
	$("#table_list").jqGrid({
		data: mydata,
		datatype: "local",
		autowidth: true,
		gridview: true,
		height: "300px",
	
        /*direction:"rtl",*/
		rownumbers: true,
		altRows: true, //单双行样式不同  
		altclass: 'differ',
	
		colNames: ["","Serial Number",  "Request Time", "Status", "Response Time", "Data Channel", "Value"],
		colModel: [{
			name: "id",
			index: "id",
			editable: true,
			width: 60,
			frozen: true,
			search: false,
			align: "right",
			hidden: true,
			sorttype: "int"

		}, {
			name: "invdate",
			index: "invdate",
			editable: true,
			width: 100,
		}, {
			name: "name",
			index: "name",
			editable: true,
			width: 80,
		}, {
			name: "amount",
			index: "amount",
			editable: true,
			width: 110,
			sorttype: "float",
			stype: "select",
			cellattr: addCellAttr//Result=Failed 的时候改变字体的颜色为红色
		
		}, {
			name: "tax",
			index: "tax",
			editable: true,
			width: 120,
			sorttype: "float"
		}, {
			name: "total",
			index: "total",
			editable: true,
			width: 140,
			sorttype: "float",
			stype: "select"
		}, {
			name: "note",
			index: "note",
			editable: true,
			align: "right",
			width: 120,
			search: false,
			sortable: false
		}],
		viewrecords: true,
		multiselect: false,
		/*editurl: "/RowEditing",*/
		shrinkToFit: true,
		autoScroll: true
	});
	//Result=Failed 的时候改变字体的颜色为红色
  function addCellAttr(rowId, val, rawObject, cm, rdata) {
        if (rawObject.amount == 'Timeout') {
            return "style='color:red'";
        }
    }

	$("#table_list").jqGrid("navGrid", "#pager_list", {
			edit: false,
			add: false,
			del: false,
			search: false
		},

		{
			height: 200,
			reloadAfterSubmit: true
		});
			var width = $("#table_list").parents(".jqGrid_wrapper").width();
        $("#table_list").setGridWidth(width);
 $(window).bind("resize", function() {
		var width = $("#table_list").parents(".jqGrid_wrapper").width();
        $("#table_list").setGridWidth(width);
	});
	
});