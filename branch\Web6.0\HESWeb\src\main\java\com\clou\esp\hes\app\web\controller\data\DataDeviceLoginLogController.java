/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataDeviceLoginLog{ } 
 * 
 * 摘    要： dataDeviceLoginLog
 * 版    本：1.0
 * 作    者：刘义柯
 * 创建于：2020-10-30 07:36:03
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.data;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;

import com.clou.esp.hes.app.web.model.data.DataDeviceLoginLog;
import com.clou.esp.hes.app.web.service.data.DataDeviceLoginLogService;

/**
 * <AUTHOR>
 * @时间：2020-10-30 07:36:03
 * @描述：dataDeviceLoginLog类
 */
@Controller
@RequestMapping("/dataDeviceLoginLogController")
public class DataDeviceLoginLogController extends BaseController{

 	@Resource
    private DataDeviceLoginLogService dataDeviceLoginLogService;

	/**
	 * 跳转到dataDeviceLoginLog列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/data/dataDeviceLoginLogList");
    }

	/**
	 * 跳转到dataDeviceLoginLog新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dataDeviceLoginLog")
	public ModelAndView dataDeviceLoginLog(DataDeviceLoginLog dataDeviceLoginLog,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dataDeviceLoginLog.getId())){
			try {
                dataDeviceLoginLog=dataDeviceLoginLogService.getEntity(dataDeviceLoginLog.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dataDeviceLoginLog", dataDeviceLoginLog);
		}
		return new ModelAndView("/data/dataDeviceLoginLog");
	}


	/**
	 * dataDeviceLoginLog查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=dataDeviceLoginLogService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除dataDeviceLoginLog信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DataDeviceLoginLog dataDeviceLoginLog,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dataDeviceLoginLogService.deleteById(dataDeviceLoginLog.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存dataDeviceLoginLog信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DataDeviceLoginLog dataDeviceLoginLog,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DataDeviceLoginLog t=new  DataDeviceLoginLog();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dataDeviceLoginLog.getId())){
        	t=dataDeviceLoginLogService.getEntity(dataDeviceLoginLog.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dataDeviceLoginLog, t);
				dataDeviceLoginLogService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            dataDeviceLoginLogService.save(dataDeviceLoginLog);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
	
	
	
}