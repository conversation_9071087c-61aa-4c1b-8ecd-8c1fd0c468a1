/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataStatisticsDevice{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：赵久霆
 * 创建于：2018-09-19 07:28:24
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.data;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.data.DataStatisticsDeviceDao;
import com.clou.esp.hes.app.web.model.data.DataStatisticsDevice;
import com.clou.esp.hes.app.web.service.data.DataStatisticsDeviceService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dataStatisticsDeviceService")
public class DataStatisticsDeviceServiceImpl  extends CommonServiceImpl<DataStatisticsDevice>  implements DataStatisticsDeviceService {

	@Resource
	private DataStatisticsDeviceDao dataStatisticsDeviceDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dataStatisticsDeviceDao);
    }
	@SuppressWarnings("rawtypes")
	public DataStatisticsDeviceServiceImpl() {}
	
	public List<Map<String, Object>> getDataStatisticsDeviceByMaps(Map<String, Object> params){
		
		return dataStatisticsDeviceDao.getDataStatisticsDeviceByMaps(params);
		 
	}
}