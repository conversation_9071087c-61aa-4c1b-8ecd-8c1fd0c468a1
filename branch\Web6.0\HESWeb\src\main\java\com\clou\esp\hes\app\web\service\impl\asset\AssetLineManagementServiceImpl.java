/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeter{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-06 08:04:55
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.asset;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.asset.AssetLineManagementDao;
import com.clou.esp.hes.app.web.model.asset.AssetCalcObj;
import com.clou.esp.hes.app.web.model.asset.AssetCalcObjMap;
import com.clou.esp.hes.app.web.model.asset.AssetLine;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.service.asset.AssetLineManagementService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@Component
@Service("assetLineManagementService")
public class AssetLineManagementServiceImpl extends CommonServiceImpl<AssetLine>
		implements AssetLineManagementService {

	@Resource
	private AssetLineManagementDao assetLineManagementDao;

	@Autowired
	public void setCommonService() {
		super.setCommonService(assetLineManagementDao);
	}

	public AssetLineManagementServiceImpl() {
	}

	@Override
	public JqGridResponseTo findLinesForJqGrid(JqGridSearchTo jqGridSearchTo) {
		// TODO Auto-generated method stub
		PageHelper
		.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		PageInfo<AssetLine> pageInfo = new PageInfo<AssetLine>(assetLineManagementDao.findLinesForJqGrid(jqGridSearchTo));
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}

	
	@Override
	public JqGridResponseTo findCalObjectsForJqGrid(
			JqGridSearchTo jqGridSearchTo) {
		// TODO Auto-generated method stub
		PageHelper
		.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		PageInfo<AssetCalcObj> pageInfo = new PageInfo<AssetCalcObj>(assetLineManagementDao.findCalObjectsForJqGrid(jqGridSearchTo));
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}

	@Override
	public Integer updateCalcObj(AssetCalcObj pojo) {
		// TODO Auto-generated method stub
		return assetLineManagementDao.updateCalcObj(pojo);
	}

	@Override
	public Integer saveCalcObj(AssetCalcObj pojo) {
		// TODO Auto-generated method stub
		return assetLineManagementDao.saveCalcObj(pojo);
	}

	@Override
	public Integer deleteCalcObj(AssetCalcObj entity) {
		// TODO Auto-generated method stub
		return assetLineManagementDao.deleteCalcObj(entity);
	}

	@Override
	public AssetCalcObj getCalcObj(AssetCalcObj entity) {
		return assetLineManagementDao.getCalcObj(entity);
	}

	@Override
	public JqGridResponseTo findCalObjectMapsForJqGrid(
			JqGridSearchTo jqGridSearchTo) {
		// TODO Auto-generated method stub
		PageHelper
		.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows());
		PageInfo<AssetCalcObjMap> pageInfo = new PageInfo<AssetCalcObjMap>(assetLineManagementDao.findCalObjectMapsForJqGrid(jqGridSearchTo));
		return JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
	}

	@Override
	public Integer updateCalcObjMap(AssetCalcObjMap pojo) {
		// TODO Auto-generated method stub
		return assetLineManagementDao.updateCalcObjMap(pojo);
	}

	@Override
	public Integer saveCalcObjMap(AssetCalcObjMap pojo) {
		// TODO Auto-generated method stub
		return assetLineManagementDao.saveCalcObjMap(pojo);
	}

	@Override
	public Integer deleteCalcObjMap(AssetCalcObjMap entity) {
		// TODO Auto-generated method stub
		return assetLineManagementDao.deleteCalcObjMap(entity);
	}

	@Override
	public AssetCalcObjMap getCalcObjMap(AssetCalcObjMap entity) {
		// TODO Auto-generated method stub
		return assetLineManagementDao.getCalcObjMap(entity);
	}

	@Override
	public Integer deleteCalcObjByEntityId(AssetCalcObj entity) {
		// TODO Auto-generated method stub
		return assetLineManagementDao.deleteCalcObjByEntityId(entity);
	}

	@Override
	public List<AssetCalcObj> findCalObjectsForList(
			JqGridSearchTo jqGridSearchTo) {
		// TODO Auto-generated method stub
		return assetLineManagementDao.findCalObjectsForJqGrid(jqGridSearchTo);
	}

	@Override
	public List<AssetMeter> queryMeterSn(AssetMeter meter) {
		// TODO Auto-generated method stub
		return assetLineManagementDao.queryMeterSn(meter);
	}

	@Override
	public List<AssetCalcObj> getListForCalObj(AssetCalcObj assetCalcObj) {
		// TODO Auto-generated method stub
		return assetLineManagementDao.getListForCalObj(assetCalcObj);
	}

}