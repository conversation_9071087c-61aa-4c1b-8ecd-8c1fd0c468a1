/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysService{ } 
 * 
 * 摘    要： sysService
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-01-29 08:53:20
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.system;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.CreatePdf;
import com.clou.esp.hes.app.web.core.util.ExcelUtils;
import com.clou.esp.hes.app.web.core.util.OrganizationUtils;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.enums.system.LoggerInfoType;
import com.clou.esp.hes.app.web.model.asset.AssetCommunicator;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.data.DataIntegrationRequest;
import com.clou.esp.hes.app.web.model.data.DataReadsLog;
import com.clou.esp.hes.app.web.model.data.DataTimeSyncRequest;
import com.clou.esp.hes.app.web.model.data.DataUserLog;
import com.clou.esp.hes.app.web.model.dict.DictUserLog;
import com.clou.esp.hes.app.web.model.system.SysService;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.asset.AssetCommunicatorService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.data.DataIntegrationRequestService;
import com.clou.esp.hes.app.web.service.data.DataTimeSyncRequestService;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.dict.DictUserLogService;
import com.clou.esp.hes.app.web.service.system.SysOrgService;
import com.clou.esp.hes.app.web.service.system.SysServiceService;
import com.clou.esp.hes.app.web.service.system.SysUserService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.power7000g.core.excel.ExcelDataFormatter;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridHandler;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;
import com.power7000g.core.util.redis.JedisUtils;

import clouesp.hes.common.logger.loggerquery.LoggerLevel;
import clouesp.hes.common.logger.loggerquery.LoggerQuery;
import clouesp.hes.common.logger.loggerquery.QueryResult;

/**
 * <AUTHOR>
 * @时间：2018-01-29 08:53:20
 * @描述：sysLog类
 */
@Controller
@RequestMapping("/sysLogController")
public class SysLogController extends BaseController{

 	@Resource
    private SysServiceService sysServiceService;
	@Resource
    private AssetMeterService assetMeterService;
	@Resource
    private AssetCommunicatorService assetCommunicatorService;
	@Resource
	private DataIntegrationRequestService dataIntegrationRequestService;
	@Resource
    private DataTimeSyncRequestService dataTimeSyncRequestService;	

	@Resource
	private SysUserService sysUserService;
	@Resource
	private SysOrgService sysOrgService;
	@Resource
	private DictUserLogService dictUserLogService;
	@Resource
	private DataUserLogService dataUserLogService;
	
	/**
	 * 跳转到sysService列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(String type,HttpServletRequest request, Model model) {
		List<SysService> list= sysServiceService.getAllList();
		String serviceReplac =RoletoJson.listToReplaceStr(list, "id", "introduction");
		model.addAttribute("serviceReplac", serviceReplac);
		model.addAttribute("serviceList", list);
		List<Map<String,Object>> levelList=new ArrayList<Map<String,Object>>();
		LoggerLevel[] levels=LoggerLevel.values();
		for(LoggerLevel ll:levels){
			if((""+ll).equals("OFF")||(""+ll).equals("TRACE")||(""+ll).equals("FATAL")){
				continue;
			}
			Map<String,Object> m=new HashMap<String, Object>();
			m.put("id", ll);
			m.put("name", ll);
			levelList.add(m);
		}
		model.addAttribute("levelList", levelList);
		List<Map<String,Object>> infoTypeList=new ArrayList<Map<String,Object>>();
		LoggerInfoType[] types=LoggerInfoType.values();
		for(LoggerInfoType ll:types){
			Map<String,Object> m=new HashMap<String, Object>();
			m.put("id", (""+ll).replace("_", " "));
			m.put("name", (""+ll).replace("_", " "));
			infoTypeList.add(m);
		}
		model.addAttribute("infoTypeList", infoTypeList);
		model.addAttribute("type", type);
		//初始化,查询所有用户数据 baijun 2018-04-03
		List<SysUser> userList = sysUserService.getAllList();
    	model.addAttribute("userList", userList);
		model = setDictUserLogModel(model);
		
        return new ModelAndView("/system/sysLogList");
    }

	/**
	 * 跳转到sysService新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "sysService")
	public ModelAndView sysService(SysService sysService,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(sysService.getId())){
			try {
                sysService=sysServiceService.getEntity(sysService.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("sysService", sysService);
		}
		return new ModelAndView("/system/sysService");
	}


	/**
	 * sysService查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
		TagUtil.setFieldValue(jqGridSearchTo, request);		//取传递的参数到JqGridSearchTo
        String deviceId = (String) request.getParameter("deviceId");
        String startDate = (String) request.getParameter("startDate");
        String endDate = (String) request.getParameter("endDate");
        String content = (String) request.getParameter("content");
        // 0:all 1:Meter 2:Communicator
        String deviceType = (String) request.getParameter("deviceType");
        String[] serviceId = request.getParameterValues("serviceId[]");
        String[] typeId = request.getParameterValues("typeId[]");
        String[] levelId = request.getParameterValues("levelId[]");
        JqGridResponseTo j = null;
        try {
        	List<DataReadsLog> list = new ArrayList<DataReadsLog>();
        	int count = 0;
        	if(StringUtil.isNotEmpty(deviceId)){
        		AssetMeter meter = assetMeterService.getEntity(deviceId);
	        	Date d = new Date();
        		Date sDate = StringUtil.isNotEmpty(startDate) ? DateUtils.parseDate(startDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")) 
        				: DateUtils.parseDate(DateUtils.formatDate(d, "yyyy-MM-dd")+" 00:00:00", "yyyy-MM-dd HH:mm:ss");
        		Date eDate = StringUtil.isNotEmpty(endDate) ? DateUtils.parseDate(endDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")) 
        				: DateUtils.parseDate(DateUtils.formatAddDate(DateUtils.formatDate(d, "yyyy-MM-dd"), "yyyy-MM-dd", 1)+" 00:00:00", "yyyy-MM-dd HH:mm:ss");
        		List<String> service=new ArrayList<String>();
        		List<String> type=new ArrayList<String>();
        		List<String> device=new ArrayList<String>();
        		if(meter != null){				//查询电表日志
        			if ("0".equals(deviceType)) {
        				device.add(meter.getId());
            			device.add(meter.getCommunicatorId());
        			} else if ("1".equals(deviceType)) {
        				device.add(meter.getId());
        			} else if ("2".equals(deviceType)) {
            			device.add(meter.getCommunicatorId());
        			}
        		}else if(deviceId.equals("0")){
        			device.add(deviceId);
        		}else{							//查询集中器日志数据
        			AssetCommunicator communicator = assetCommunicatorService.getEntity(deviceId);
        			JedisUtils.setObject(communicator.getId(), communicator.getSn(), 0);	//SN缓存入Redis
        			if(communicator != null){
        				// All
        				if ("0".equals(deviceType)) {
        					device.add(communicator.getId());
        					
        					//查询集中器下的电表集合
            				AssetMeter entity = new AssetMeter();
            				entity.setCommunicatorId(communicator.getId());
            				List<AssetMeter> meterList = assetMeterService.getList(entity);
            				if(meterList.size() > 0){
            					for (int i = 0; i < meterList.size(); i++) {
            						device.add(meterList.get(i).getId());
            						JedisUtils.setObject(meterList.get(i).getId(), meterList.get(i).getSn(), 0);	//SN缓存入Redis
    							}
            				}
            			} else if ("1".equals(deviceType)) { // Meter
            				//查询集中器下的电表集合
            				AssetMeter entity = new AssetMeter();
            				entity.setCommunicatorId(communicator.getId());
            				List<AssetMeter> meterList = assetMeterService.getList(entity);
            				if(meterList.size() > 0){
            					for (int i = 0; i < meterList.size(); i++) {
            						device.add(meterList.get(i).getId());
            						JedisUtils.setObject(meterList.get(i).getId(), meterList.get(i).getSn(), 0);	//SN缓存入Redis
    							}
            				}
            			} else if ("2".equals(deviceType)) { // Communicator
            				device.add(communicator.getId());
            			}
        			}
        		}
        		if(serviceId != null){
        			service = Arrays.asList(serviceId);
        		}
        		if(typeId != null){
        			type = Arrays.asList(typeId);
        		}
        		//日志等级，Log Level
        		List<LoggerLevel> level = new ArrayList<LoggerLevel>();
        		if(levelId != null){
        			for(String l:levelId){
        				if(StringUtil.isNotEmpty(l)){
        					level.add(LoggerLevel.valueOf(l));
        				}
        			}
        		}
        		// 获取LoggerQuery实例，查询日志总条数，协助分页
        		if (StringUtils.isEmpty(content)) {
                	content = null;
                }
        		
        		count = LoggerQuery.getInstance().getQueryCount(sDate, eDate,level, service, device,type, content);
        		// 根据查询条件，获取日志
        		Vector<QueryResult> queryResults = LoggerQuery.getInstance().query(jqGridSearchTo.getPage(),PageHelper.startPage(jqGridSearchTo.getPage(), 
        				jqGridSearchTo.getRows()).getPageSize(), sDate, eDate, level, service, device, type, content);
        		if(!StringUtils.isEmpty(queryResults)){
        			for(int i=0; i<queryResults.size(); i++){
            			QueryResult qr = queryResults.get(i);
            			DataReadsLog drl = new DataReadsLog();
            			drl.setId(""+(i+1));
            			//获取电表或者集中器的SN号, 判断是否查询电表日志
            			if(meter != null){
            				drl.setSn(meter.getSn());
            			}else if("0".equals(qr.getdeviceId())){		//初始化界面时，数据过滤
            				drl.setSn("System");
            			}else{
            				String sn = (String) JedisUtils.getObject(qr.getdeviceId());	//获取Redis的数据
            				if(StringUtil.isNotEmpty(sn)){
            					drl.setSn(sn);
            				}else{
            					//当在Redis缓存中没有查询到数据时，再查询数据库
            					AssetMeter meterSn = assetMeterService.getEntity(qr.getdeviceId());
        	        			if(meterSn != null){
        	        				drl.setSn(meterSn.getSn());
        	        			}else{
        	        				AssetCommunicator comm = assetCommunicatorService.getEntity(qr.getdeviceId());
        	        				if(comm != null){
        	        					drl.setSn(comm.getSn());
        	        				}else{
        	        					drl.setSn("");
        	        				}
        	        			}
            				}
            			}
            			drl.setDate(qr.gettime());
            			drl.setServiceId(qr.getserviceId());
            			drl.setType(qr.getinfoType());
            			drl.setContent(qr.getinfo().replace("\r\n", ""));
            			drl.setLevel(qr.getlevel());
            			list.add(drl);
            		}
        		}
    		}
    		PageInfo<DataReadsLog> pageInfo = new PageInfo<DataReadsLog>(list);
    		pageInfo.setList(list);
    		//页数
    		int pages = count / PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows()).getPageSize();
    		if(count % PageHelper.startPage(jqGridSearchTo.getPage(), jqGridSearchTo.getRows()).getPageSize() > 0){
    			pages += 1;
    		}
    		j = JqGridHandler.GetJqGridPageJsonData(pageInfo, jqGridSearchTo);
    		j.setRecords(count);
    		j.setTotal(pages);
    		j.setPage(jqGridSearchTo.getPage());
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
	
	 /**
	  * 导出
	  * @param deviceId
	  * @param date
	  * @param dataIntegrityDetails
	  * @param request
	  * @param response
	  */
	 @RequestMapping(value = "getExportDataLog")
	 @ResponseBody
	 public void getExportDataLog(HttpServletRequest request, HttpServletResponse response) {
			 String deviceId=(String) request.getParameter("deviceId");
		        String startDate=(String) request.getParameter("startDate");
		        String endDate=(String) request.getParameter("endDate");
		        String serviceIds=request.getParameter("serviceId");
		        String typeIds=request.getParameter("typeId");
		        String levelIds=request.getParameter("levelId");
		        String[] serviceId=StringUtil.isNotEmpty(serviceIds)?serviceIds.split(","):null;
		        String[] typeId=StringUtil.isNotEmpty(serviceIds)?typeIds.split(","):null;
		        String[] levelId=StringUtil.isNotEmpty(serviceIds)?levelIds.split(","):null;
		        try {
		        	List<DataReadsLog> list = new ArrayList<DataReadsLog>();
		        	int count=0;
		        	if(StringUtil.isNotEmpty(deviceId)){
			     		AssetMeter meter=assetMeterService.getEntity(deviceId);
			        	Date d=new Date();
		        		Date sDate = StringUtil.isNotEmpty(startDate)?DateUtils.parseDate(startDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")):DateUtils.parseDate(DateUtils.formatDate(d, "yyyy-MM-dd")+" 00:00:00", "yyyy-MM-dd HH:mm:ss");
		        		Date eDate = StringUtil.isNotEmpty(endDate)?DateUtils.parseDate(endDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")):DateUtils.parseDate(DateUtils.formatAddDate(DateUtils.formatDate(d, "yyyy-MM-dd"), "yyyy-MM-dd", 1)+" 00:00:00", "yyyy-MM-dd HH:mm:ss");
		        		List<String> service=new ArrayList<String>();
		        		List<String> type=new ArrayList<String>();
		        		List<String> device=new ArrayList<String>();
		        		if(meter!=null){
		        			device.add(meter.getId());
		        			device.add(meter.getCommunicatorId());
		        		}else if(deviceId.equals("0")){
		        			device.add(deviceId);
		        		}else{							//查询集中器日志数据
		        			AssetCommunicator communicator = assetCommunicatorService.getEntity(deviceId);
		        			JedisUtils.setObject(communicator.getId(), communicator.getSn(), 0);	//SN缓存入Redis
		        			if(communicator != null){
		        				//查询集中器下的电表集合
		        				AssetMeter entity = new AssetMeter();
		        				entity.setCommunicatorId(communicator.getId());
		        				List<AssetMeter> meterList = assetMeterService.getList(entity);
		        				if(meterList.size() > 0){
		        					for (int i = 0; i < meterList.size(); i++) {
		        						device.add(meterList.get(i).getId());
		        						JedisUtils.setObject(meterList.get(i).getId(), meterList.get(i).getSn(), 0);	//SN缓存入Redis
									}
		        					device.add(communicator.getId());
		        				}
		        			}
		        		}
		        		if(serviceId!=null){
		        			service=Arrays.asList(serviceId);
		        		}
		        		if(typeId!=null){
		        			type=Arrays.asList(typeId);
		        		}
		        		List<LoggerLevel> level=new ArrayList<LoggerLevel>();
		        		if(levelId!=null){
		        			for(String l:levelId){
		        				if(StringUtil.isNotEmpty(l)){
		        					level.add(LoggerLevel.valueOf(l));
		        				}
		        			}
		        		}
		        		
		        		if(device != null && device.size() > 0){
		        		
			        		count = LoggerQuery.getInstance().getQueryCount(sDate, eDate,level, service, device,type, null);
			        		Vector<QueryResult> queryResults = LoggerQuery.getInstance().query(1, count, 
			        				sDate, eDate, level, service, device,type, null);
			        		if(!StringUtils.isEmpty(queryResults)){
			        			for(int i=0;i<queryResults.size();i++){
				        			QueryResult qr=queryResults.get(i);
				        			DataReadsLog drl=new DataReadsLog();		
				        			//获取电表或者集中器的SN号, 判断是否查询电表日志
				        			if(meter != null){
				        				drl.setSn(meter.getSn());
				        			}else if("0".equals(qr.getdeviceId())){		//初始化界面时，数据过滤
				        				drl.setSn("System");
				        			}else{
				        				String sn = (String) JedisUtils.getObject(qr.getdeviceId());	//获取Redis的数据
				        				if(StringUtil.isNotEmpty(sn)){
				        					drl.setSn(sn);
				        				}else{
				        					//当在Redis缓存中没有查询到数据时，再查询数据库
				        					AssetMeter meterSn = assetMeterService.getEntity(qr.getdeviceId());
				    	        			if(meterSn != null){
				    	        				drl.setSn(meterSn.getSn());
				    	        			}else{
				    	        				AssetCommunicator comm = assetCommunicatorService.getEntity(qr.getdeviceId());
				    	        				if(comm != null){
				    	        					drl.setSn(comm.getSn());
				    	        				}else{
				    	        					drl.setSn("");
				    	        				}
				    	        			}
				        				}
				        			}
	
				        			drl.setId(""+(i+1));
				        			drl.setDate(qr.gettime());
				        			drl.setServiceId(qr.getserviceId());
				        			drl.setType(qr.getinfoType());
				        			drl.setContent(qr.getinfo().replace("\r\n", ""));
				        			drl.setLevel(qr.getlevel());
				        			list.add(drl);
				        		}
			        		}
		        		
		        		}
		        		
	        		}
        	if(list.size()<=0){
        		DataReadsLog drl=new DataReadsLog();
        		list.add(drl);
        	}
	        ExcelDataFormatter edf = new ExcelDataFormatter();
	        //要日期格式化使用以下方式
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("date", "MM/dd/yyyy HH:mm:ss");
	        edf.set("date", tvs);
			ExcelUtils.writeToFile(list, edf, "dataLogList.xlsx", response,
						ValidGroup1.class);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
	 }
	 
	 /**
	  * 打印
	  * @param deviceId
	  * @param date
	  * @param dataIntegrityDetails
	  * @param request
	  * @param response
	  */
	 @RequestMapping(value = "getPringDataLog")
	 @ResponseBody
	 public void getPringDataLog(HttpServletRequest request, HttpServletResponse response) {
		    String deviceId=(String) request.getParameter("deviceId");
	        String startDate=(String) request.getParameter("startDate");
	        String endDate=(String) request.getParameter("endDate");
	        String serviceIds=request.getParameter("serviceId");
	        String typeIds=request.getParameter("typeId");
	        String levelIds=request.getParameter("levelId");
	        String[] serviceId=StringUtil.isNotEmpty(serviceIds)?serviceIds.split(","):null;
	        String[] typeId=StringUtil.isNotEmpty(serviceIds)?typeIds.split(","):null;
	        String[] levelId=StringUtil.isNotEmpty(serviceIds)?levelIds.split(","):null;
		 try {
			 List<DataReadsLog> list = new ArrayList<DataReadsLog>();
			 int count=0;
			 if(StringUtil.isNotEmpty(deviceId)){
				 AssetMeter meter=assetMeterService.getEntity(deviceId);
				 Date d=new Date();
				 Date sDate = StringUtil.isNotEmpty(startDate)?DateUtils.parseDate(startDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")):DateUtils.parseDate(DateUtils.formatDate(d, "yyyy-MM-dd")+" 00:00:00", "yyyy-MM-dd HH:mm:ss");
				 Date eDate = StringUtil.isNotEmpty(endDate)?DateUtils.parseDate(endDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")):DateUtils.parseDate(DateUtils.formatAddDate(DateUtils.formatDate(d, "yyyy-MM-dd"), "yyyy-MM-dd", 1)+" 00:00:00", "yyyy-MM-dd HH:mm:ss");
				 List<String> service=new ArrayList<String>();
				 List<String> type=new ArrayList<String>();
				 List<String> device=new ArrayList<String>();
				 if(meter!=null){
					 device.add(meter.getId());
					 device.add(meter.getCommunicatorId());
				 }else if(deviceId.equals("0")){
					 device.add(deviceId);
				 }else{							//查询集中器日志数据
					 AssetCommunicator communicator = assetCommunicatorService.getEntity(deviceId);
					 JedisUtils.setObject(communicator.getId(), communicator.getSn(), 0);	//SN缓存入Redis
					 if(communicator != null){
						 //查询集中器下的电表集合
						 AssetMeter entity = new AssetMeter();
						 entity.setCommunicatorId(communicator.getId());
						 List<AssetMeter> meterList = assetMeterService.getList(entity);
						 if(meterList.size() > 0){
							 for (int i = 0; i < meterList.size(); i++) {
								 device.add(meterList.get(i).getId());
								 JedisUtils.setObject(meterList.get(i).getId(), meterList.get(i).getSn(), 0);	//SN缓存入Redis
							 }
							 device.add(communicator.getId());
						 }
					 }
				 }
				 if(serviceId!=null){
					 service=Arrays.asList(serviceId);
				 }
				 if(typeId!=null){
					 type=Arrays.asList(typeId);
				 }
				 List<LoggerLevel> level=new ArrayList<LoggerLevel>();
				 if(levelId!=null){
					 for(String l:levelId){
						 if(StringUtil.isNotEmpty(l)){
							 level.add(LoggerLevel.valueOf(l));
						 }
					 }
				 }

				 if(device != null && device.size() > 0){

					 count = LoggerQuery.getInstance().getQueryCount(sDate, eDate,level, service, device,type, null);
					 Vector<QueryResult> queryResults = LoggerQuery.getInstance().query(1, count,
							 sDate, eDate, level, service, device,type, null);
					 if(!StringUtils.isEmpty(queryResults)){
						 for(int i=0;i<queryResults.size();i++){
							 QueryResult qr=queryResults.get(i);
							 DataReadsLog drl=new DataReadsLog();
							 //获取电表或者集中器的SN号, 判断是否查询电表日志
							 if(meter != null){
								 drl.setSn(meter.getSn());
							 }else if("0".equals(qr.getdeviceId())){		//初始化界面时，数据过滤
								 drl.setSn("System");
							 }else{
								 String sn = (String) JedisUtils.getObject(qr.getdeviceId());	//获取Redis的数据
								 if(StringUtil.isNotEmpty(sn)){
									 drl.setSn(sn);
								 }else{
									 //当在Redis缓存中没有查询到数据时，再查询数据库
									 AssetMeter meterSn = assetMeterService.getEntity(qr.getdeviceId());
									 if(meterSn != null){
										 drl.setSn(meterSn.getSn());
									 }else{
										 AssetCommunicator comm = assetCommunicatorService.getEntity(qr.getdeviceId());
										 if(comm != null){
											 drl.setSn(comm.getSn());
										 }else{
											 drl.setSn("");
										 }
									 }
								 }
							 }

							 drl.setId(""+(i+1));
							 drl.setDate(qr.gettime());
							 drl.setServiceId(qr.getserviceId());
							 drl.setType(qr.getinfoType());
							 drl.setContent(qr.getinfo().replace("\r\n", ""));
							 drl.setLevel(qr.getlevel());
							 list.add(drl);
						 }
					 }

				 }

			 }
			 if(list.size()<=0){
				 DataReadsLog drl=new DataReadsLog();
				 list.add(drl);
			 }
			 ExcelDataFormatter edf = new ExcelDataFormatter();
			 //要日期格式化使用以下方式
			 Map<String,String> tvs=new HashMap<String, String>();
			 tvs.put("date", "MM/dd/yyyy HH:mm:ss");
			 edf.set("date", tvs);
			 CreatePdf.printPdf(list, edf, ValidGroup1.class, request, response);
		 } catch (Exception e) {
			 // TODO Auto-generated catch block
			 e.printStackTrace();
		 }

	 }
    
    /**
     * 删除sysService信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(SysService sysService,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(sysServiceService.deleteById(sysService.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存sysService信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })SysService sysService,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        SysService t=new  SysService();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(sysService.getId())){
        	t=sysServiceService.getEntity(sysService.getId());
			MyBeanUtils.copyBeanNotNull2Bean(sysService, t);
				sysServiceService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            sysServiceService.save(sysService);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * ------------------------------------------------------
     * Time Synchronization 操作函数定义 , 2018-03-09 , baijianyun
     * ------------------------------------------------------
     */
    
    private String[] synResultIds2StrSynResults(String[] synResultIds){
        if(synResultIds == null)
            return null;
        if(synResultIds.length == 0)
            return null;        
        
        String[] strSynResults = new String[synResultIds.length];
//        int i = 0;
        for (int i = 0; i < strSynResults.length; i++) {
        	if(synResultIds[i].equalsIgnoreCase("Success"))
                strSynResults[i] = "1";
            else if(synResultIds[i].equalsIgnoreCase("Failed"))
                strSynResults[i] = "0";
            else if(synResultIds[i].equalsIgnoreCase("Normal"))
                strSynResults[i] = "2";      
		}
//        for(String synResultId : synResultIds){
//            if(synResultId.equalsIgnoreCase("Success"))
//                strSynResults[i] = "1";
//            else if(synResultId.equalsIgnoreCase("Failed"))
//                strSynResults[i] = "0";
//            else if(synResultId.equalsIgnoreCase("Normal"))
//                strSynResults[i] = "2";            
//        }
        return strSynResults;
    }
    
    private int[] synResultIds2IntSynResults(String[] synResultIds){
        if(synResultIds == null)
            return null;
        if(synResultIds.length == 0)
            return null;
        
        int[] intSynResults = new int[synResultIds.length];
        int i = 0;
        for(String synResultId : synResultIds){
            if(synResultId.equalsIgnoreCase("Success"))
                intSynResults[i] = 1;
            else if(synResultId.equalsIgnoreCase("Failed"))
                intSynResults[i] = 0;
            else if(synResultId.equalsIgnoreCase("Normal"))
                intSynResults[i] = 2;
            i++;
        }
        return intSynResults;
    }   
    
    
    @RequestMapping(value = "timeSyncLogDataGrid")
    @ResponseBody
    public JqGridResponseTo timeSyncLogDataGrid(JqGridSearchTo jqGridSearchTo,String leftSearchType,String deviceType,HttpServletRequest request) {
         String deviceId = (String) request.getParameter("deviceId");
         String startDate = (String) request.getParameter("startDate");
         String endDate = (String) request.getParameter("endDate");
         String[] synResultIds = request.getParameterValues("synResultIds[]");
         
         jqGridSearchTo.getMap().put("deviceId", deviceId);
         //如果左边是集中器sn，右边是电表，查询集中器下面所有的电表   leftSarchType:1,Meter   2,Comm
         if("2".equals(leftSearchType)&&"1".equals(deviceType)) {//meter
        	List<String> list=assetMeterService.getMeterIdsByCommunicatorId(deviceId);   
        	if(list!=null) {
        		jqGridSearchTo.put("deviceIds", list);
        	}
         }else {
        	 if(deviceId!=null&&!"".equals(deviceId)) {
        		 List<String> list =Lists.newArrayList();
        		 list.add(deviceId);
        		 jqGridSearchTo.put("deviceIds", list);
        	 }
         }
         jqGridSearchTo.put("deviceType", deviceType);
         String[] strSynResults = synResultIds2StrSynResults(synResultIds);
         
         
         jqGridSearchTo.getMap().put("synResults", strSynResults);
         
         JqGridResponseTo jqGridResponseTo = null;
         try {
            Date d = new Date();
            Date sDate = StringUtil.isNotEmpty(startDate)?DateUtils.parseDate(startDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")):DateUtils.parseDate(DateUtils.formatDate(d, "yyyy-MM-dd")+" 00:00:00", "yyyy-MM-dd HH:mm:ss");
            Date eDate = StringUtil.isNotEmpty(endDate)?DateUtils.parseDate(endDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")):DateUtils.parseDate(DateUtils.formatAddDate(DateUtils.formatDate(d, "yyyy-MM-dd"), "yyyy-MM-dd", 1)+" 00:00:00", "yyyy-MM-dd HH:mm:ss");
            jqGridSearchTo.put("startDate", sDate);
            jqGridSearchTo.put("endDate", eDate);
            
    		List<String> orgIdList = OrganizationUtils.getOrgIds(sysOrgService,"");
    		jqGridSearchTo.put("orgIdList", orgIdList);
    		
            jqGridResponseTo = dataTimeSyncRequestService.getForJqGrid(jqGridSearchTo);
        } catch (Exception e) {
            // TODO: handle exception
            e.printStackTrace();
        }
        return jqGridResponseTo;
    }
    
    
    /**
     * 导出Time Synchronization Log 日志，以excel格式
     * @param request
     * @param response
     */
    @RequestMapping(value = "exportTimeSynchronizationLog")
    @ResponseBody
    public void exportTimeSynchronizationLog(HttpServletRequest request, HttpServletResponse response) {
    
        String deviceId = (String) request.getParameter("deviceId");
        String startDate = (String) request.getParameter("startDate");
        String endDate = (String) request.getParameter("endDate");
        String[] synResultIds = request.getParameterValues("synResultIds[]");
        
        int[] intSynResults = synResultIds2IntSynResults(synResultIds);
        try {
            List<DataTimeSyncRequest> dataTimeSyncReqs = findDataTimeSyncRequests(deviceId,startDate,endDate,intSynResults);
            ExcelDataFormatter edf = new ExcelDataFormatter();
            //要日期格式化使用以下方式
            Map<String,String> tvs=new HashMap<String, String>();
            tvs.put("date", "MM/dd/yyyy HH:mm:ss");
            edf.set("date", tvs);
            ExcelUtils.writeToFile(dataTimeSyncReqs, edf, "dataTimeSynchronizationLogList.xlsx", response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 打印Time Synchronization Log日志 , pdf格式
     * @param request
     * @param response
     */
    @RequestMapping(value = "printTimeSynchronizationLog")
    @ResponseBody
    public void printTimeSynchronizationLog(HttpServletRequest request, HttpServletResponse response) {
        String deviceId = (String) request.getParameter("deviceId");
        String startDate = (String) request.getParameter("startDate");
        String endDate = (String) request.getParameter("endDate");
        String[] synResultIds = request.getParameterValues("synResultIds[]");
        
        int[] intSynResults = synResultIds2IntSynResults(synResultIds);
        
        try {
            List<DataTimeSyncRequest> dataTimeSyncReqs = findDataTimeSyncRequests(deviceId,startDate,endDate,intSynResults);
            ExcelDataFormatter edf = new ExcelDataFormatter();
            //要日期格式化使用以下方式
            Map<String,String> tvs=new HashMap<String, String>();
            tvs.put("date", "MM/dd/yyyy HH:mm:ss");
            edf.set("date", tvs);
            CreatePdf.printPdf(dataTimeSyncReqs, edf, request, response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 查询 Time Synchronization Log 日志信息
     * @param deviceId
     * @param startDate
     * @param endDate
     * @param synResults
     * @return
     * @throws ParseException 
     */
    private List<DataTimeSyncRequest> findDataTimeSyncRequests(String deviceId,String startDate,String endDate,int[] synResults) throws ParseException {
        
        List<DataTimeSyncRequest> dataTimeSyncReqs = new ArrayList<>();
      if(StringUtil.isNotEmpty(deviceId)){//检查ID是否为空
    	    DataTimeSyncRequest param = new DataTimeSyncRequest();
            param.setId(deviceId);
            param.setSynResultArrays(synResults);
            
            if(null != startDate && !"".equals(startDate)) {
                 Date formatStartDate = DateUtils.parseDate(startDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
                 param.setStartDate(formatStartDate);
             }
             
             if(null != endDate && !"".equals(endDate)) {
                 Date formatEndDate = DateUtils.parseDate(endDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
                 param.setEndDate(formatEndDate);
             }
             List<DataTimeSyncRequest> results = dataTimeSyncRequestService.getList(param);
             if(null != results && !results.isEmpty()) 
                 dataTimeSyncReqs.addAll(results);
      }else{
    	  DataTimeSyncRequest param = new DataTimeSyncRequest();
    	  param.setSynResultArrays(synResults);
          if(null != startDate && !"".equals(startDate)) {
               Date formatStartDate = DateUtils.parseDate(startDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
               param.setStartDate(formatStartDate);
           }
           
           if(null != endDate && !"".equals(endDate)) {
               Date formatEndDate = DateUtils.parseDate(endDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
               param.setEndDate(formatEndDate);
           }
           List<DataTimeSyncRequest> results = dataTimeSyncRequestService.getList(param);
           if(null != results && !results.isEmpty()) 
               dataTimeSyncReqs.addAll(results);
      }
        
        if(dataTimeSyncReqs.isEmpty()) {
            dataTimeSyncReqs.add(new DataTimeSyncRequest());
        }
        
        return dataTimeSyncReqs;
    }    
	
	/**
	 * ------------------------------------------------------
	 * System Integration Log 操作函数定义 , 2018-02-27 , baijun
	 * ------------------------------------------------------
	 */
    @RequestMapping(value = "sysInteLogDataGrid")
    @ResponseBody
    public JqGridResponseTo sysInteLogDataGrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
		 String deviceId = (String) request.getParameter("deviceId");
	     String startDate = (String) request.getParameter("startDate");
	     String endDate = (String) request.getParameter("endDate");
	     String[] requestType = request.getParameterValues("requestType[]");
	     
	     jqGridSearchTo.getMap().put("deviceId", deviceId);
	     jqGridSearchTo.getMap().put("requestType", requestType);
	     
		 JqGridResponseTo jqGridResponseTo = null;
		 try {
			Date d = new Date();
			Date sDate = StringUtil.isNotEmpty(startDate)?DateUtils.parseDate(startDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")):DateUtils.parseDate(DateUtils.formatDate(d, "yyyy-MM-dd")+" 00:00:00", "yyyy-MM-dd HH:mm:ss");
     		Date eDate = StringUtil.isNotEmpty(endDate)?DateUtils.parseDate(endDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")):DateUtils.parseDate(DateUtils.formatAddDate(DateUtils.formatDate(d, "yyyy-MM-dd"), "yyyy-MM-dd", 1)+" 00:00:00", "yyyy-MM-dd HH:mm:ss");
     		jqGridSearchTo.getMap().put("startDate", sDate);
     		jqGridSearchTo.getMap().put("endDate", eDate);
     		
     		List<String> orgIdList = OrganizationUtils.getOrgIds(sysOrgService,"");
    		jqGridSearchTo.put("orgIdList", orgIdList);
			jqGridResponseTo = dataIntegrationRequestService.getForJqGrid(jqGridSearchTo);
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
		}
		return jqGridResponseTo;
    }
    
    
    /**
     * 导出System Integration Log 日志，以excel格式
     * @param request
     * @param response
     */
    @RequestMapping(value = "exportSystemIntegrationLog")
	@ResponseBody
	public void exportSystemIntegrationLog(HttpServletRequest request, HttpServletResponse response) {
    	String deviceId = (String) request.getParameter("deviceId");
	    String startDate = (String) request.getParameter("startDate");
	    String endDate = (String) request.getParameter("endDate");
	    String requestTypes = request.getParameter("requestType");
	    
	    String[] reqTypes = StringUtil.isNotEmpty(requestTypes) ? requestTypes.split(",") : null;
	    try {
	    	List<DataIntegrationRequest> dataIntegrationReqs = findDataIntegrationRequests(deviceId,startDate,endDate,reqTypes);
	    	ExcelDataFormatter edf = new ExcelDataFormatter();
	        //要日期格式化使用以下方式
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("date", "MM/dd/yyyy HH:mm:ss");
	        edf.set("date", tvs);
			ExcelUtils.writeToFile(dataIntegrationReqs, edf, "dataSystemIntegrationLogList.xlsx", response);
		} catch (Exception e) {
			e.printStackTrace();
		}
    }
    
    /**
     * 打印System Integration Log日志 , pdf格式
     * @param request
     * @param response
     */
    @RequestMapping(value = "pringDataIntegrationLog")
	@ResponseBody
	public void pringDataIntegrationLog(HttpServletRequest request, HttpServletResponse response) {
    	String deviceId = (String) request.getParameter("deviceId");
	    String startDate = (String) request.getParameter("startDate");
	    String endDate = (String) request.getParameter("endDate");
	    String requestTypes = request.getParameter("requestType");
	    
	    String[] reqTypes = StringUtil.isNotEmpty(requestTypes) ? requestTypes.split(",") : null;
	    
	    try {
	    	List<DataIntegrationRequest> dataIntegrationReqs = findDataIntegrationRequests(deviceId,startDate,endDate,reqTypes);
	    	ExcelDataFormatter edf = new ExcelDataFormatter();
	        //要日期格式化使用以下方式
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("date", "MM/dd/yyyy HH:mm:ss");
	        edf.set("date", tvs);
	        CreatePdf.printPdf(dataIntegrationReqs, edf, request, response);
		} catch (Exception e) {
			e.printStackTrace();
		}
    }
    
    /**
     * 查询 System Integration Log 日志信息
     * @param deviceId
     * @param startDate
     * @param endDate
     * @param requestTypes
     * @return
     * @throws ParseException 
     */
    private List<DataIntegrationRequest> findDataIntegrationRequests(String deviceId,String startDate,String endDate,String[] requestTypes) throws ParseException {
    	
    	List<DataIntegrationRequest> dataIntegrationReqs = new ArrayList<>();
//    	if(StringUtil.isNotEmpty(deviceId)){//检查ID是否为空
    		DataIntegrationRequest param = new DataIntegrationRequest();
    		param.setId(deviceId);
    		param.setRequestTypeArrays(requestTypes);
    		
    		if(null != startDate && !"".equals(startDate)) {
		    	 Date formatStartDate = DateUtils.parseDate(startDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
		    	 param.setStartDate(formatStartDate);
		     }
			 
			 if(null != endDate && !"".equals(endDate)) {
		    	 Date formatEndDate = DateUtils.parseDate(endDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
		    	 param.setEndDate(formatEndDate);
		     }
			 List<DataIntegrationRequest> results = dataIntegrationRequestService.getList(param);
			 if(null != results && !results.isEmpty()) 
				 dataIntegrationReqs.addAll(results);
//    	}
    	
    	if(dataIntegrationReqs.isEmpty()) {
    		dataIntegrationReqs.add(new DataIntegrationRequest());
    	}
    	
    	return dataIntegrationReqs;
    }
    
    /**
	 * ------------------------------------------------------
	 * User Log 操作函数定义 , 2018-04-03 , baijun
	 * ------------------------------------------------------
	 */
    @RequestMapping(value = "sysUserLogDataGrid")
    @ResponseBody
    public JqGridResponseTo sysUserLogDataGrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
    	 String[] userId = request.getParameterValues("userId[]");
		 String[] logType = request.getParameterValues("logType[]");
		 String[] logSubType = request.getParameterValues("logSubType[]");
		 
	     String startDate = (String) request.getParameter("startDate");
	     String endDate = (String) request.getParameter("endDate");
	     
	     jqGridSearchTo.getMap().put("userId", userId);
	     jqGridSearchTo.getMap().put("logType", logType);
	     jqGridSearchTo.getMap().put("logSubType", logSubType);
	     
		 JqGridResponseTo jqGridResponseTo = null;
		 try {
			Date d = new Date();
			Date sDate = StringUtil.isNotEmpty(startDate)?DateUtils.parseDate(startDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")):DateUtils.parseDate(DateUtils.formatDate(d, "yyyy-MM-dd")+" 00:00:00", "yyyy-MM-dd HH:mm:ss");
     		Date eDate = StringUtil.isNotEmpty(endDate)?DateUtils.parseDate(endDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")):DateUtils.parseDate(DateUtils.formatAddDate(DateUtils.formatDate(d, "yyyy-MM-dd"), "yyyy-MM-dd", 1)+" 00:00:00", "yyyy-MM-dd HH:mm:ss");
     		jqGridSearchTo.getMap().put("startDate", sDate);
     		jqGridSearchTo.getMap().put("endDate", eDate);
     		
     		jqGridResponseTo = dataUserLogService.getForJqGrid(jqGridSearchTo);
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
		}
		return jqGridResponseTo;
    }
    
    static Map<String,List<DictUserLog>> mapDictUserLogs = new HashMap<String,List<DictUserLog>>();
    private Model setDictUserLogModel(Model model){
    	mapDictUserLogs.clear();
    	List<DictUserLog> dictUserLogs = dictUserLogService.getAllList();
    	if(dictUserLogs == null || dictUserLogs.isEmpty())
    		return model;
    	
    	for (DictUserLog dictUserLog : dictUserLogs) {
    		if(mapDictUserLogs.containsKey(dictUserLog.getLogType())){
    			mapDictUserLogs.get(dictUserLog.getLogType()).add(dictUserLog);
    		} else {
    			List<DictUserLog> listDictUserLogs = new ArrayList<DictUserLog>();
    			listDictUserLogs.add(dictUserLog);
    			mapDictUserLogs.put(dictUserLog.getLogType(), listDictUserLogs);
    		}
		}
    	
    	model.addAttribute("mapDictUserLogs", mapDictUserLogs);
    	return model;
    }
   
    @SuppressWarnings("unused")
	@RequestMapping(value = "getLogSubType")
	@ResponseBody
	public AjaxJson getLogSubType(String appType,HttpServletRequest request) {
    	AjaxJson json = new AjaxJson();
    	if(null == appType && "null" == appType){
    		return json;
    	}
    	String[] appTypes = appType.replace("[", "").replace("]", "").split(",");
    		
    	List<DictUserLog> tempList = new ArrayList<>();
		for (String string : appTypes) {
			if(mapDictUserLogs.get(string.replace("\"", "")) == null)
				continue;
			tempList.addAll(mapDictUserLogs.get(string.replace("\"", "")));
		}
		json.setObj(tempList);
		return json;
    }
    
    @RequestMapping(value = "exportUserLog")
	@ResponseBody
    public void exportUserLog(HttpServletRequest request, HttpServletResponse response){
    	String userId = (String) request.getParameter("userId");
    	String logType = request.getParameter("logType").replace("$", "&");;
    	String logSubType = request.getParameter("logSubType");
    	
	    String startDate = (String) request.getParameter("startDate");
	    String endDate = (String) request.getParameter("endDate");
	    
	    String[] userIds = StringUtil.isNotEmpty(userId) ? userId.split(",") : null;
	    String[] logSubTypes = StringUtil.isNotEmpty(logSubType) ? logSubType.split(",") : null;
	    String[] logTypes = StringUtil.isNotEmpty(logType) ? logType.split(",") : null;
	    
	    try {
	    	List<DataUserLog> dataUserLogs = getDataUserLogs(userIds,logTypes,logSubTypes,startDate,endDate);
	    	ExcelDataFormatter edf = new ExcelDataFormatter();
	        //要日期格式化使用以下方式
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("date", "MM/dd/yyyy HH:mm:ss");
	        edf.set("date", tvs);
			ExcelUtils.writeToFile(dataUserLogs, edf, "dataUserLogList.xlsx", response);
		} catch (Exception e) {
			e.printStackTrace();
		}
    }
    
    @RequestMapping(value = "printUserLog")
	@ResponseBody
    public void printUserLog(HttpServletRequest request, HttpServletResponse response){
    	String userId = (String) request.getParameter("userId");
    	String logType = request.getParameter("logType").replace("$", "&");
    	String logSubType = request.getParameter("logSubType");
    	
	    String startDate = (String) request.getParameter("startDate");
	    String endDate = (String) request.getParameter("endDate");
	    
	    String[] userIds = StringUtil.isNotEmpty(userId) ? userId.split(",") : null;
	    String[] logSubTypes = StringUtil.isNotEmpty(logSubType) ? logSubType.split(",") : null;
	    String[] logTypes = StringUtil.isNotEmpty(logType) ? logType.split(",") : null;
	    
	    try {
	    	List<DataUserLog> dataUserLogs = getDataUserLogs(userIds,logTypes,logSubTypes,startDate,endDate);
	    	ExcelDataFormatter edf = new ExcelDataFormatter();
	        //要日期格式化使用以下方式
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("date", "MM/dd/yyyy HH:mm:ss");
	        edf.set("date", tvs);
	        CreatePdf.printPdf(dataUserLogs, edf, request, response);
//	        CreatePdf.printPdf(dataUserLogs, edf, ValidGroup1.class, request, response);
		} catch (Exception e) {
			e.printStackTrace();
		}
    }

	private List<DataUserLog> getDataUserLogs(String[] userIds, String[] logTypes,
			String[] logSubTypes, String startDate, String endDate) throws ParseException {
		// TODO Auto-generated method stub
		List<DataUserLog> dataUserLogs = new ArrayList<DataUserLog>();
		DataUserLog dataUserLog = new DataUserLog();
		dataUserLog.setUserIds(userIds);
		dataUserLog.setLogTypes(logTypes);
		dataUserLog.setLogSubTypes(logSubTypes);
		
		if(null != startDate && !"".equals(startDate)) {
	    	 Date formatStartDate = DateUtils.parseDate(startDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
	    	 dataUserLog.setStartDate(formatStartDate);
	     }
		 
		 if(null != endDate && !"".equals(endDate)) {
	    	 Date formatEndDate = DateUtils.parseDate(endDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
	    	 dataUserLog.setEndDate(formatEndDate);
	     }
		 List<DataUserLog> results = dataUserLogService.getList(dataUserLog);
		 if(null != results && !results.isEmpty()) 
			 dataUserLogs.addAll(results);
    		
    	
    	if(dataUserLogs.isEmpty()) {
    		dataUserLogs.add(new DataUserLog());
    	}
    	
		return dataUserLogs;
	}
}