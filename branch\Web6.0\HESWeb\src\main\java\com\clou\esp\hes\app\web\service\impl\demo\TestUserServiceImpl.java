/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class TestUser{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-03-29 08:30:58
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.demo;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.demo.TestUserDao;
import com.clou.esp.hes.app.web.model.demo.TestUser;
import com.clou.esp.hes.app.web.service.demo.TestUserService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;


@Component
@Service("testUserService")
public class TestUserServiceImpl extends CommonServiceImpl<TestUser> implements
		TestUserService {

	@Resource
	private TestUserDao testUserDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(testUserDao);
    }

	@SuppressWarnings("rawtypes")
	public TestUserServiceImpl() {
	}

	

}