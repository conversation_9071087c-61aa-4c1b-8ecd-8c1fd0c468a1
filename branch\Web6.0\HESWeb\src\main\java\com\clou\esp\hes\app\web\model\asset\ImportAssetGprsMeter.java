package com.clou.esp.hes.app.web.model.asset;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.power7000g.core.excel.Excel;

public class ImportAssetGprsMeter extends BaseEntity{

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
    /**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public ImportAssetGprsMeter() {
		
	}
	
	@Excel(name = "Serial_Number", width = 30)
	private String Serial_Number;
	@Excel(name = "Comm_Address", width = 30)
	private String Comm_Address;
	@Excel(name = "Logical_address", width = 30)
	private String Logical_address;
	@Excel(name = "Key Meter(0:No,1:Yes)", width = 30)
	private String keyMeter;

	public String getSerial_Number() {
		return Serial_Number;
	}
	public void setSerial_Number(String serial_Number) {
		Serial_Number = serial_Number;
	}
	public String getComm_Address() {
		return Comm_Address;
	}
	public void setComm_Address(String comm_Address) {
		Comm_Address = comm_Address;
	}
	public String getLogical_address() {
		return Logical_address;
	}
	public void setLogical_address(String logical_address) {
		Logical_address = logical_address;
	}
	public String getKeyMeter() {
		return keyMeter;
	}
	public void setKeyMeter(String keyMeter) {
		this.keyMeter = keyMeter;
	}
}
