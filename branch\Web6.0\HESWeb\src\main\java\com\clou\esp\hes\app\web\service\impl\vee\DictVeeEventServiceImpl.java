/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictVeeEvent{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-12-21 07:44:42
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.vee;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.vee.DictVeeEventDao;
import com.clou.esp.hes.app.web.model.vee.DictVeeEvent;
import com.clou.esp.hes.app.web.service.vee.DictVeeEventService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dictVeeEventService")
public class DictVeeEventServiceImpl  extends CommonServiceImpl<DictVeeEvent>  implements DictVeeEventService {

	@Resource
	private DictVeeEventDao dictVeeEventDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(dictVeeEventDao);
    }
	@SuppressWarnings("rawtypes")
	public DictVeeEventServiceImpl() {}
	
	
}