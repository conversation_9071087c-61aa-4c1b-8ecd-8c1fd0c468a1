INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.03.0B.00.00', '300', '三相失流次数及时间', '030B0000', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.23.*********.0.1', '100', 'Phase 1 import reactive energy T1', '3#1.0.23.8.1.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.7.31.*******.0.71.7.124.255.0.0.1', '100', 'Total harmonic of phase C current', '3#1.0.71.7.124.255#2', '1', 'RW', '0.01', NULL, '1', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.22.1.12.0.0.0.0.3.*********.72.0', '100', 'Active energy import (+A) L2 rate 3 [Unit: kWh]', '3#********.3.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.46', '100', 'Phase 1 import reactive energy', '3#1.0.23.8.0.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.1.0.63.27.0.*********.0.0.*******.38.0', '100', 'Average positive reactive power of phase C', '3#1.0.63.27.0.255#2', 'Wh', 'R', '0.001', '1000', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.53', '100', 'Phase 2 import reactive energy T2', '3#********.2.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.*********.0.0.0.4.*********.73.0', '100', 'Reactive energy import (+R) L2 rate 4 [Unit: kVarh]', '3#********.4.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.0.1.12.0.0.1.2.3.*********.72.6', '100', 'Daily Active energy export (+A) [Unit: kWh]', '3#1.0.2.19.0.255#2', 'Wh', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.4F.01', '300', '费率参数表编程次数更改发生', 'E200004F', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.41.*********.0.1', '100', 'Phase 2 import active energy T2', '3#********.2.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.3.********.0.0.0.0.0.224.0.0.9', '100', 'average power factor of inteval rcd', '3#1.0.***********#2', 'Wh', 'R', '0.01', '2001', '1', 'long-unsigned', '2', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.54', '100', 'Phase 2 import reactive energy T3', '3#********.3.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.8.1.6.0.1.8.0.0.0.0.0.*********.38.0', '100', 'Cumulative active maximum demand', '3#1.0.15.2.0.255#2', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.42.8.4.255.0.0.1', '100', 'Phase 2 export active energy T4', '3#1.0.42.8.4.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.55', '100', 'Phase 2 import reactive energy T4', '3#********.4.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.44.*********.0.1', '100', 'Phase 2 export reactive energy T2', '3#1.0.44.8.2.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.12.34', '200', 'Current Reverse Active/Reactive Energy Data', '0C#34', NULL, 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.18.*******.0.52.53.0.255.0.0.1', '100', 'Maximum L1 Voltage', '3#1.0.52.53.0.255#2', '1', 'RW', '0.001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.*********.13.0.0.0.*********.6.255.1.0.1', '100', 'Credit', '3#*********.6.255#2', '1', 'RW', '0.01', NULL, '0', 'double-long', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.44.8.0.255.0.0.1', '100', 'Phase 2 export reactive energy', '3#1.0.44.8.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.96.50.3', '100', 'Normal over load reply open count', '1#*********.3.255#2', NULL, 'R', '1', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.2D.00', '300', '电能表飞走恢复', 'E200002D', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.0.********.16.255.0', '100', 'last 3 times techincian ID', '1#********.16.255#2', '1', 'RW', '1', NULL, '0', 'octet-string', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.61.*********.0.1', '100', 'Phase 3 import active energy T2', '3#********.2.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.51.53.0.*********.0.*********.72.0', '100', 'L2 max current', '3#1.0.51.53.0.255#2', 'Wh', 'R', '0.001', '2001', '1', 'double-long-unsigned', '4', '0');

INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.5', '100', 'Key meter parameter delete', '1#0.0.96.51.8.255#1', NULL, 'A', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.0C.00', '300', 'C相TA二次侧开路恢复', 'E200000C', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.24.1.12.0.0.0.0.3.*********.72.0', '100', 'Active energy export (-A) L1 rate 3 [Unit: kWh]', '3#1.0.22.8.3.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('38.0.0.59', '100', 'overCurrent', '21#1.0.31.4.0.255#2', NULL, 'RW', '1', NULL, NULL, 'special', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.2C.00', '300', '示度下降恢复', 'E200002C', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.8.7.79', '100', 'Over voltage start', '7#*********.7.255#2#79', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.62.*********.0.1', '100', 'Phase 3 export active energy T1', '3#********.1.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.*******.0.15.8.0.101.0', '100', 'Cumulative active energy of last month', '3#1.0.15.8.0.101#2', '1', 'RW', '0.01', NULL, '0', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.1D.01', '300', 'A相电压断相发生', 'E200001D', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.16', '100', 'Phase 3 import active energy', '3#********.0.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.172', '200', 'Reverse Reactive Energy Data (Total/Tariff 1-M)(Monthly Frozen)', '0D#172', NULL, 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.42.00', '300', '电能表A、B、C相潮流反向总次数恢复', 'E2000042', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.31.1.12.0.0.0.0.2.*********.73.0', '100', 'Reactive energy export (-R) L2 rate 2 [Unit: kVarh]', '3#1.0.44.8.2.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.7.31.*******.0.32.7.124.255.0.0.1', '100', 'Total harmonic of phase A voltage', '3#1.0.32.7.124.255#2', '1', 'RW', '0.01', NULL, '1', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.26.*********.128.*********.0.*********.72.0', '100', 'L1-L2 Average voltage', '3#*********.128.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.35', '100', 'Phase 2 export active energy T4', '3#1.0.42.8.4.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.22.1.12.0.0.0.0.4.*********.72.0', '100', 'Active energy import (+A) L2 rate 4 [Unit: kWh]', '3#********.4.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.1E.01', '300', 'B相电压断相发生', 'E200001E', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.18.*******.0.72.**********.0.1', '100', 'L3 average voltage', '3#1.0.72.27.0.255#2', '1', 'RW', '0.1', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.2********.0.0.0.0.*********.73.0', '100', 'Reactive energy import (+R) L3 [Unit: kVarh]', '3#1.0.63.8.0.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('38.0.0.97', '100', 'Ftp destination', '29#*******.0.255#6', NULL, 'RW', '1', NULL, NULL, 'special', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.1.0.0', '100', 'Clock invalid restoration', '0.4.25.9.0.255#1#00', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.********.**********.*********.72.4', '100', 'Average current Neutral', '3#1.0.91.27.0.255#2', 'A', 'R', '0.01', '1000', '1', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.43.8.4.255.0.0.1', '100', 'Phase 2 import reactive energy T4', '3#********.4.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.69', '100', 'No network communication timeout reset time', '1#0.1.96.50.3.255#2', NULL, 'RW', '1', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.32.1.12.0.0.0.0.4.*********.73.0', '100', 'Reactive energy export (-R) L3 rate 4 [Unit: kVarh]', '3#********.4.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('20.8.7.1', '100', 'Number of terminal cover open', '1#0.0.96.20.5.255#2', NULL, 'R', '1', NULL, NULL, 'long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.**********.22.0.0.0.0.0.96.9.0.255.0.0.1', '100', 'Temperature', '3#0.0.96.9.0.255#2', '1', 'RW', '0.1', NULL, '0', 'long', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.**********.13.0.0.*******9.130.7.255.0.0.1', '100', 'Last recharge amount in EGP', '1#**********.7.255#2', '1', 'RW', '1', NULL, '0', 'float32', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.1.0.41.27.0.*********.0.0.*******.38.0', '100', 'Average positive active power of phase B', '3#1.0.41.27.0.255#2', 'Wh', 'R', '0.001', '1000', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.44.8.0.255.0.0.1', '100', 'Phase 2 export reactive energy', '3#1.0.44.8.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.4.0.0.0.0.0.0.3.128.0.5.0', '300', 'Instantaneous phase angle current L1 [Unit: A]', 'A', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.1.1.20.20.7.6.0.0.*********.72.2', '100', 'Cumulative power on time of the month(Monthly)(BL)', '3#0.0.96.91.128.255#2', 'min', 'R', '1', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.86', '200', 'Phase A Reactive Power Load Profile', '0D#86', NULL, 'R', '0.001', '2001', NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.12', '100', 'Phase 2 import active energy T1', '3#********.1.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.0.9.8.7.*********.73.1', '100', 'Cumulative import apparent energy,+E,T7', '3#*******.7.255#2', 'VAh', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.74', '100', 'Phase 2 export reactive energy T3', '3#1.0.44.8.3.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.2.53.0.*********.0.*********.72.0', '100', 'max export active power', '3#1.0.2.53.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.10.1.12.0.0.13.0.0.*********.73.1', '100', 'Average power factor of billing period', '3#1.0.13.0.0.255#2', NULL, 'R', '1', NULL, '1', 'long-unsigned', '2', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.61.*********.0.1', '100', 'Phase 3 import active energy T2', '3#********.2.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.25.01', '300', 'C相电流畸变发生', 'E2000025', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.26.1.12.0.0.0.0.4.*********.72.0', '100', 'Active energy export (-A) L3 rate 4 [Unit: kWh]', '3#********.4.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.20.20.10.15.14.2.29.0.0.0.0.0.0.0.0.0.22', '100', 'Cumulative import reactive energy, +R - rate 6', '3#1.0.3.8.6.255#2', NULL, 'RW', '0.01', NULL, NULL, 'double-long-unsigned', '12', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.37.01', '300', '电能表脉冲常数更改发生', 'E2000037', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.44.8.3.255.0.0.1', '100', 'Phase 2 export reactive energy T3', '3#1.0.44.8.3.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.3F.00', '300', '电能表校时失败恢复', 'E200003F', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E1.00.40.18', '300', '当月有功最大需量及发生时间数据块', 'E1004018', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.48.00', '300', 'A相闪变越限恢复', 'E2000048', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.28.01', '300', '功率超定值发生', 'E2000028', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.2********.0.0.0.2.*********.73.0', '100', 'Reactive energy import (+R) L3 rate 2 [Unit: kVarh]', '3#1.0.63.8.2.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.**********.13.*******.0.11.129.3.255.0.0.1', '100', 'ReverseCurrent', '3#1.0.11.129.3.255#2', '1', 'RW', '1', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.2E.00', '300', '电能表停走恢复', 'E200002E', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.20.20.10.15.14.2.29.0.0.0.0.0.0.0.0.0.21', '100', 'Cumulative import reactive energy, +R - rate 5', '3#1.0.3.8.5.255#2', NULL, 'RW', '0.01', NULL, NULL, 'double-long-unsigned', '12', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.3.1.0.0.4', '300', 'The time of voltage lose L2 [Unit: Min]', 'Min', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.1.0.41.7.0.255.0.*******28.0.29.1', '100', 'Instant import active power L2 sy', '3#1.0.41.7.0.255#2', NULL, 'R', '1', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.24.0.*********.0.*********.72.0', '100', 'average import active power', '3#1.0.1.24.0.255#2', 'Wh', 'R', '1', '2001', '1', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.05.01', '300', '电压不平衡发生', 'E2000005', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******.********.*********.**********.0.0', '100', 'Phase 2 import active energy', '3#1.********.255#2', NULL, NULL, '1', NULL, '1', 'double-long-unsigned', NULL, NULL);

INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********', '100', 'Concentrator IP Address', '1#********.129.255#2', NULL, 'RW', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('************', '200', 'Reverse Active Energy Data (Total/Tariff 1-M)(Monthlyly Frozen)', '0D#171', NULL, 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('111133', '100', 'xxx333', '1', 'Wh', 'R', '0.1', '2000', NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.***********', '300', '零序电流偏大发生', 'E2000006', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.0A.01', '300', 'A相TA二次侧开路发生', 'E200000A', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.85.0', '100', 'Last Gasp - Occurrence', '851', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.4C.01', '300', '发现未知电表发生', 'E200004C', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.64.8.3.255.0.0.1', '100', 'Phase 3 export reactive energy T3', '3#********.3.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.26.*******.12.0.0.0.0.0.*********.72.1', '100', 'Monthly increase active energy', '3#1.0.15.9.0.255#2', NULL, 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.22.1.12.0.0.0.0.2.*********.72.0', '100', 'Active energy import (+A) L2 rate 2 [Unit: kWh]', '3#********.2.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.**********.13.0.0.0.0.0.96.5.10.255.0.0.1', '100', 'Relay status', '1#0.0.96.5.10.255#2', '1', 'RW', '0.001', NULL, '0', 'octet-string', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.82', '200', 'Phase A Active Power Load Profile', '0D#82', NULL, 'R', '0.001', '2001', NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.06.00', '300', '零序电流偏大恢复', 'E2000006', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.4.0.0.0.0.0.0.4.0.0.5.0', '100', 'Instantaneous average current neutral', '3#1.0.91.27.0.255#2', 'A', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.0D.00', '300', 'A相潮流反向恢复', 'E200000D', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.0.0.0.96.5.10.255.0', '100', 'relay status', '1#0.0.96.5.10.255#2', '1', 'RW', '1', NULL, '0', 'octet-string', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.1.20.20.7.6.0.0.*********.72.17', '100', 'Instantaneous reactive power (|+R|+|-R|)', '3#1.0.128.7.0.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('************.0.0.96.7.30.255.2', '100', 'Power on time (Hrs)', '3#0.0.96.7.30.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long-unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.42.8.3.255.0.0.1', '100', 'Phase 2 export active energy T3', '3#1.0.42.8.3.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.99.97.244', '100', 'Phase B power down - end', '7#1.0.99.97.0.255#2#244', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.0F.01', '300', 'C相潮流反向发生', 'E200000F', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.21.27.0.*********.0.*********.72.0', '100', 'L1 Average import active power', '3#1.0.21.27.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');

INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.6', '100', 'Phase 1 import active energy', '3#********.0.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.1B.00', '300', 'B相电压过压恢复', 'E200001B', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.3.14.0.0.4', '300', 'The time of load flow reverse L2 [Unit: Min]', 'Min', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.1.301', '100', 'Module cover close', '7#*********.1.255#2#301', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);

INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.44.8.4.255.0.0.1', '100', 'Phase 2 export reactive energy T4', '3#1.0.44.8.4.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.0.73.6.1.1.8.0.0.*******.0.224.3.38.0', '100', 'Import active demand occurred time', '5#1.0.1.4.0.255#5', NULL, 'R', '1', NULL, NULL, 'date-time', '12', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.9', '100', 'Phase 1 import active energy T3', '3#********.3.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.74', '100', 'Phase 2 export reactive energy T3', '3#1.0.44.8.3.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.2.1.8.0.0.9.6.2.1.0.224.3.38.1', '100', 'Import apparent maximum demand,t2,occured time', '4#1.0.9.6.2.255#5', NULL, 'R', '1', NULL, '1', 'date-time', '12', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.20.20.10.15.14.2.29.0.0.0.0.0.0.0.0.0.28', '100', 'Cumulative import reactive energy, -R - rate 5', '3#1.0.4.8.5.255#2', NULL, 'RW', '0.01', NULL, NULL, 'double-long-unsigned', '12', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.3.14.0.0.3', '300', 'The number of load flow reverse L2', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.13.00', '300', 'A相电流失流恢复', 'E2000013', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.0.0.61.0.0.7.0.0.0.0.*******28.3.63.9', '100', 'L3 instant active power import', '3#1.0.61.7.0.255#2', 'W', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '44', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.5.11.0.0.0.0.5.*********.38.0', '100', 'Instantaneous active power (|+A|+|-A|) [Unit: kW]', '3#********.0.255#2', 'KW', 'R', '1', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.59', '100', 'Phase 3 import reactive energy T3', '3#1.0.63.8.3.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.32.1.12.0.0.0.0.3.*********.73.0', '100', 'Reactive energy export (-R) L3 rate 3 [Unit: kVarh]', '3#********.3.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.23.8.4.255.0.0.1', '100', 'Phase 1 import reactive energy T4', '3#1.0.23.8.4.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.63.*********.0.1', '100', 'Phase 3 import reactive energy T2', '3#1.0.63.8.2.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.52.27.128.*********.0.*********.72.0', '100', 'L2-L3 Average voltage', '3#1.0.52.27.128.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.60', '100', 'Phase 3 import reactive energy T4', '3#1.0.63.8.4.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.69', '100', 'Phase 1 export reactive energy T3', '3#1.0.24.8.3.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.22.*********.0.1', '100', 'Phase 1 export active energy T2', '3#1.0.22.8.2.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.24.*********.0.1', '100', 'Phase 1 export reactive energy T2', '3#1.0.24.8.2.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.1.4', '200', 'Parameter( Except for communication parameter)and Data Initialization', '01#4', NULL, 'W', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.24.1.12.0.0.0.0.3.*********.72.0', '100', 'Active energy export (-A) L1 rate 3 [Unit: kWh]', '3#1.0.22.8.3.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40*******1.1', '100', 'Over current in any phase3 restoration', '111', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.32.38.1', '100', 'L1 maximum value during over voltage', '3#1.0.32.38.0.255#2', 'V', 'R', '0.1', NULL, NULL, 'long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.5.2', '200', 'Permit to Switch On', '05#2', NULL, 'W', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.0.1.0.4.30.0.*********.0.*********.72.0', '100', 'Block import reactive energy,-R', '3#1.0.4.30.0.255#2', 'kWh', NULL, '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.41.53.0.*********.0.*********.72.0', '100', 'L2 max import active power', '3#1.0.41.53.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.84', '200', 'Phase C Active Power Load Profile', '0D#84', NULL, 'R', '0.001', '2001', NULL, NULL, NULL, NULL);

INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.8.0.0.0.4.11.15.29.0.*********.224.3.38.9', '100', 'Monthly increase active energy', '3#1.0.15.9.0.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.0.2.9.2********.0.0.0.0.*********.72.0', '100', 'Block import apparent energy,+E', '3#1.0.9.29.0.255#2', 'kWh', NULL, '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.0.0.0.96.54.67.255.0', '100', 'Install date', '1#0.0.96.54.67.255#2', '1', 'RW', '1', NULL, '0', 'date-time', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.26.1.12.0.0.0.0.0.*********.72.0', '100', 'Active energy export (-A) L3 [Unit: kWh]', '3#********.0.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.51.01', '300', '异常插卡告警发生', 'E2000051', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.56', '100', 'Phase 3 import reactive energy', '3#1.0.63.8.0.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.64.53.0.*********.0.*********.72.0', '100', 'L3 max export reactive power', '3#1.0.64.53.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.35', '100', 'GPRS Modem Version', '1#*********.22.255#2', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.32.1.12.0.0.0.0.0.*********.73.0', '100', 'Reactive energy export (-R) L3 [Unit: kVarh]', '3#********.0.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.37.0.0.0.0.0.*********.63.0', '100', 'Instantaneous reactive power (|+R|+|-R|) [Unit: kW]', '3#1.0.133.7.0.255#2', 'W', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.21.1.12.0.0.0.0.4.*********.72.0', '100', 'Active energy import (+A) L1 rate 4 [Unit: kWh]', '3#********.4.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.89', '200', 'Phase A Voltage Profile', '0D#89', NULL, 'R', '0.001', '2002', NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.*********.0.*********.0.0.*******.38.0', '100', 'Average reverse active power of phase C', '3#1.0.***********#2', 'Wh', 'R', '0.001', '1000', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.3.14.0.0.5', '300', 'The number of load flow reverse L3', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.44.53.0.*********.0.*********.72.0', '100', 'L2 max export reactive power', '3#1.0.44.53.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.47', '100', 'Phase 1 import reactive energy T1', '3#1.0.23.8.1.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.02.02.FF.00', '300', '三相电流数据块', '0202FF00', 'A', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.6.0', '100', 'R Phase current reverse occurrence', '60', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.17', '100', 'Phase 3 import active energy T1', '3#********.1.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.1.8.0.0.0.0.6.1.0.224.3.38.1', '100', 'Maximum Demand Register 1 -Active energy import(+A) rate 6 occurred time (Monthly)', '4#1.0.1.6.6.255#5', NULL, 'R', '1', NULL, '1', 'date-time', '12', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.31.53.0.*********.0.*********.72.0', '100', 'L1 max current', '3#1.0.31.53.0.255#2', 'Wh', 'R', '0.001', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.72.51.128.*********.0.*********.72.0', '100', 'L3-L1 Min voltage', '3#1.0.72.51.128.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.37.0.0.0.0.0.0.0.64.3.38.0', '100', 'Instantaneous active power L2 (|+A|+|-A|) [Unit: kW]', '3#1.0.55.7.0.255#2', 'W', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.48', '100', 'Phase 1 import reactive energy T2', '3#1.0.23.8.2.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.18', '100', 'Phase 3 import active energy T2', '3#********.2.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.*******.0.15.9.0.110.0', '100', 'last 10 month active energy |+A| + |-A| consumption', '3#1.0.15.9.0.110#2', '1', 'RW', '0.01', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.16.01', '300', 'A相电压失压发生', 'E2000016', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.32.1.12.0.0.0.0.2.*********.73.0', '100', 'Reactive energy export (-R) L3 rate 2 [Unit: kVarh]', '3#********.2.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.32.24.0.*********.0.*********.72.0', '100', 'L1 average voltage', '3#1.0.32.24.0.255#2', 'Wh', 'R', '0.1', '2001', '1', 'long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.**********.13.0.0.0.0.0.96.54.72.255.0.0.1', '100', 'Non cleared tampers', '1#0.0.96.54.72.255#2', '1', 'RW', '1', NULL, '0', 'long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.26.*******.0.21.**********.0.1', '100', 'L1 Average import active power', '3#1.0.21.27.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.3.11.0.0.3', '300', 'The number of current lose L2', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.2C.01', '300', '示度下降发生', 'E200002C', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.0C.01', '300', 'C相TA二次侧开路发生', 'E200000C', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.2.12.0.0.0.0.4.*********.72.0', '100', 'Combined active energy import (+A) [Unit: kWh]', 'Wh', NULL, NULL, '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.1.20.20.7.6.0.0.*********.72.15', '100', 'Instantaneous active power L2 (|+A|+|-A|) [Unit: kW]', '3#1.0.55.7.0.255#2', 'W', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.1.20.20.7.6.0.0.*********.72.13', '100', 'Instantaneous active power (|+A|+|-A|) [Unit: kW]', '3#********.0.255#2', 'W', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.15.38.0', '100', 'Maximum value during normal overload', '3#1.0.15.38.0.255#2', NULL, 'R', '1', NULL, NULL, 'double-long', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.31', '100', 'WAN DHCP Enable', '1#*********.19.255#1', NULL, 'A', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.12.01.0.0.*******9.13*********.0.1', '100', 'Residual Credit_New', '3#**********.0.255#2', '1', 'R', '0.01', NULL, '0', 'double-long', '3', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.**********.13.0.0.0.0.0.96.54.73.255.0.0.1', '100', 'Date of last charge', '1#0.0.96.54.73.255#2', '1', 'RW', '1', NULL, '0', 'date-time', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.44.*********.0.1', '100', 'Phase 2 export reactive energy T2', '3#1.0.44.8.2.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.14', '100', 'Phase 2 import active energy T3', '3#********.3.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.22.8.4.255.0.0.1', '100', 'Phase 1 export active energy T4', '3#1.0.22.8.4.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.12.161', '200', 'Remote Switch On/Switch Off Status and Log', '0C#161', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.25.1.12.0.*******.*********.72.0', '100', 'Active energy export (-A) L2 rate 1 [Unit: kWh]', '3#1.0.42.8.1.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('44.4.4.3', '100', 'Actively type', '1#0.0.96.54.29.255#2', NULL, 'RW', '1', NULL, NULL, 'unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.1.20.20.7.6.0.0.*********.72.20', '100', 'L3 Instant reactive power(import + export)', '3#1.0.158.7.0.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.51', '100', 'Phase 2 import reactive energy', '3#********.0.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.13.53.0.*********.0.*********.72.0', '100', 'Max power factor of inteval rcd', '3#1.0.1**********#2', 'Wh', 'R', '0.001', '2001', '1', 'long-unsigned', '2', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.22.8.3.255.0.0.1', '100', 'Phase 1 export active energy T3', '3#1.0.22.8.3.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.6', '100', 'Phase 1 import active energy', '3#********.0.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.19', '100', 'Phase 3 import active energy T3', '3#********.3.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('44.4.5.2', '100', 'Event Count', '44.4.5.2', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******.0.61.8.0.*********.**********.0.0', '100', 'Phase 3 import active energy', '3#********.0.255#2', NULL, NULL, '1', NULL, '1', 'double-long-unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.47', '200', 'Meter Reading goes fast occurrence', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.2.12.0.0.0.0.3.*********.72.0', '100', 'Combined active energy import (+A) [Unit: kWh]', 'Wh', NULL, NULL, '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.2.12.0.*******.*********.72.0', '100', 'Combined active energy import (+A) [Unit: kWh]', 'Wh', NULL, NULL, '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.20.14.88', '100', 'Load Profile1 Interval', '7#1.0.99.1.0.255#4', NULL, 'RW', '1', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.********.**********.*********.72.6', '100', 'Average voltage L2', '3#1.0.52.27.0.255#2', 'V', 'R', '0.1', NULL, '1', 'double-long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******.0.128.8.0.*********.**********.0.0', '100', 'Total reactive energy', '3#1.0.128.8.0.255#2', NULL, NULL, '1', NULL, '1', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.20.14.91', '100', 'CurrentRatioNumerator', '1#1.0.0.4.2.255#2', NULL, 'RW', '1', NULL, NULL, 'long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.71.24.0.*********.0.*********.72.0', '100', 'L3 average current', '3#1.0.71.24.0.255#2', 'Wh', 'R', '0.001', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.28', '100', 'GPRS Signal Strength', '1#*********.12.255#2', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.26.00', '300', '无功过补偿恢复', 'E2000026', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.2.1.8.0.0.9.6.5.*********.38.1', '100', 'Import apparent maximum demand,t5', '4#1.0.9.6.5.255#2', 'VA', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.29', '100', 'Phase 1 export active energy T3', '3#1.0.22.8.3.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.4.318', '100', 'Current unbalance end', '7#*********.4.255#2#318', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.2A.00', '300', '超合同容量用电恢复', 'E200002A', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.*********.13.0.0.0.********0.1.255.0.0.1', '100', 'Status', '7#********0.1.255#2', '1', 'RW', '1', NULL, '0', 'unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.102', '200', 'Total Forward Reactive Energy Data', '0D#102', NULL, 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('30*******2', '200', 'Current circuit recovery', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('30*******0', '200', 'Meter parameter changed', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.27.1.12.0.0.0.0.0.*********.73.0', '100', 'Reactive energy import (+R) L1 [Unit: kVarh]', '3#1.0.23.8.0.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.21.8.3.255.0.0.1', '100', 'Phase 1 import active energy T3', '3#********.3.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.0.9.8.1.*********.73.1', '100', 'Cumulative import apparent energy,+E,T1', '3#*******.1.255#2', 'VAh', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.31.1.12.0.0.0.0.0.*********.73.0', '100', 'Reactive energy export (-R) L2 [Unit: kVarh]', '3#1.0.44.8.0.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.31.00', '300', '最大需量手动复零恢复', 'E2000031', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.0.0.12.1.0.61.7.0.255.0.*******28.0.29.1', '100', 'Instant import active power L3 sy', '3#1.0.61.7.0.255#2', NULL, 'R', '1', NULL, NULL, 'double-long-unsigned', '44', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.39.01', '300', '遥信变位发生', 'E2000039', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.122.127.3.1.0.13.23.0.255.2', '100', 'Last minimum value of power factor', '3#1.0.13.23.0.255#2', NULL, 'R', '0.01', NULL, NULL, 'long-unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.69', '100', 'Phase 1 export reactive energy T3', '3#1.0.24.8.3.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.4.0.0.0.0.0.0.7.64.0.5.0', '100', 'Average harmonic in L2 Instant current', '3#1.0.51.27.124.255#2', '%', 'R', '0.01', NULL, NULL, 'long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.24.51.0.*********.0.*********.72.0', '100', 'L1 min export reactive power', '3#1.0.24.51.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.12', '100', 'Phase 2 import active energy T1', '3#********.1.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('xx.xx', '200', 'ttest', 'xx#。xx', 'w', 'R', '1', '2010', NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('30*******7', '200', 'Meter time deviation over threshold occurrence', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.5.313', '100', 'Register successful', '7#*********.5.255#2#313', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.25.1.12.0.0.0.0.3.*********.72.0', '100', 'Active energy export (-A) L2 rate 3 [Unit: kWh]', '3#1.0.42.8.3.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.68', '100', 'Phase 1 export reactive energy T2', '3#1.0.24.8.2.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.32.01', '300', '时钟电池电压过低发生', 'E2000032', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.********.**********.0.1.224.3.72.5', '100', 'Average voltage L1', '3#*********.0.255#2', 'V', 'R', '1', NULL, '1', 'double-long-unsigned', '4', '1');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.41.8.4.255.0.0.1', '100', 'Phase 2 import active energy T4', '3#********.4.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.6', '200', 'State variables changed', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.5.319', '100', 'Module in', '7#*********.5.255#2#319', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.101', '200', 'Total Forward Active Energy Data', '0D#101', NULL, 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.93', '200', 'Phase B Current Profile', '0D#93', NULL, 'R', '0.001', '2003', NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.52.00', '300', '非法插卡告警恢复', 'E2000052', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.2.1.8.0.0.9.6.7.1.0.224.3.38.1', '100', 'Import apparent maximum demand,t7,occured time', '4#1.0.9.6.7.255#5', NULL, 'R', '1', NULL, '1', 'date-time', '12', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.7', '200', 'Remote disconnected', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.3B.00', '300', '继电器变位恢复', 'E200003B', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('38.0.0.105', '100', 'Ftp connet(data)', '29#*******.0.255#1', NULL, 'A', '1', NULL, NULL, 'integer', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.21.8.0.255.0.0.1', '100', 'Phase 1 import active energy', '3#********.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.15', '100', 'Phase 2 import active energy T4', '3#********.4.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.39.00', '300', '遥信变位恢复', 'E2000039', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.63.8.4.255.0.0.1', '100', 'Phase 3 import reactive energy T4', '3#1.0.63.8.4.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.43.51.0.*********.0.*********.72.0', '100', 'L2 min import reactive power', '3#1.0.43.51.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('41.96.11.3', '100', 'Other fraud related events', '1#********1.3.255#2', NULL, 'R', '1', NULL, NULL, 'long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.20.20.10.15.14.2.29.0.0.0.0.0.0.0.0.0.7', '100', 'Cumulative import active energy, +A - rate 5', '3#1.0.1.8.5.255#2', NULL, 'RW', '0.01', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.0.0.12.0.1.1.2.3.0.0.*******.0.65.12', '100', 'Current maximum demand capture time', '3#1.0.96.54.61.255#2', NULL, 'R', '1', NULL, NULL, 'date-time', '12', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.0.0', '100', 'R Phase - Voltage missing occurrence', '00', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.*******9.133.0.111.0', '100', 'last 11 month credit consumption ', '1#0.**********.111#2', '0.01', 'RW', '1', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.02.07.FF.00', '300', '相位角数据块', '0207FF00', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.02.03.FF.00', '300', '有功功率数据块', '0203FF00', 'kW', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.76', '100', 'Phase 3 export reactive energy', '3#********.0.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.********.255.0.0.1', '100', 'Phase 2 import active energy', '3#1.********.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.23.51.0.*********.0.*********.72.0', '100', 'L1 min import reactive power', '3#1.0.23.51.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.37', '200', 'Voltage over threshold occurrence', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.33.00', '300', '终端全部掉电恢复', 'E2000033', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.20', '100', 'PLC channel timeout', '1#*********.27.255#3', NULL, 'RW', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.********.0.0.0.0.0.7.32.0.29.0', '100', 'Average harmonic in L3 Instant voltage', '3#1.0.72.27.124.255#2', '%', 'R', '0.01', NULL, NULL, 'long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.00.03.FF.01', '300', '月冻结正向无功电能数据块', '0003FF01', 'kvarh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.2', '200', 'Dcu version changed', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.71.53.0.*********.0.*********.72.0', '100', 'L3  max current', '3#1.0.71.53.0.255#2', 'Wh', 'R', '0.001', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.1.300', '100', 'Module cover open', '7#*********.1.255#2#300', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.0.0.12.0.1.38.0.0.0.0.*******.0.65.0', '100', 'Average power factor of billing period', '3#1.0.13.0.0.255#2', NULL, 'R', '0.001', NULL, NULL, 'long-unsigned', '2', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.26.1.12.0.0.0.0.4.*********.72.0', '100', 'Active energy export (-A) L3 rate 4 [Unit: kWh]', '3#********.4.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.5.0', '100', 'Voltage unbalance occurrence', '50', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.41.00', '300', '电能表A、B、C相失流总次数恢复', 'E2000041', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.37.0.0.0.0.0.0.0.32.3.63.0', '100', 'Instantaneous reactive power L3 (|+R|+|-R|) [Unit: kW]', '3#1.0.133.7.0.255#2', 'W', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.16.00', '300', 'A相电压失压恢复', 'E2000016', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.7.31.*******.0.31.7.124.255.0.0.1', '100', 'Total harmonic of phase A current', '3#1.0.31.7.124.255#2', '1', 'RW', '0.01', NULL, '1', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.20', '100', 'Phase 3 import active energy T4', '3#********.4.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.8', '200', 'Power control disconnected', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.3.********.0.0.0.0.0.224.0.0.6', '100', 'L3 average current', '3#1.0.71.24.0.255#2', 'Wh', 'R', '0.1', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.30.1.12.0.0.0.0.2.*********.73.0', '100', 'Reactive energy export (-R) L1 rate 2 [Unit: kVarh]', '3#1.0.24.8.2.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.23.01', '300', 'A相电流畸变发生', 'E2000023', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.*********.0.0.0.2.*********.73.0', '100', 'Reactive energy import (+R) L2 rate 2 [Unit: kVarh]', '3#********.2.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.*******9.133.0.110.0', '100', 'last 10 month credit consumption ', '1#0.**********.110#2', '0.01', 'RW', '1', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.1.1.1', '100', 'Battery replace occurrence', '0.4.25.9.0.255#1#11', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.32.1.12.0.0.0.0.4.*********.73.0', '100', 'Reactive energy export (-R) L3 rate 4 [Unit: kVarh]', '3#********.4.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.01.01.FF.01', '300', '月冻结正向有功最大需量及发生时间数据块', '0101FF01', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.18.*******.0.21.8.0.255.0.0.1', '100', 'Phase 1 import active energy', '3#********.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.30.1.12.0.0.0.0.3.*********.73.0', '100', 'Reactive energy export (-R) L1 rate 3 [Unit: kVarh]', '3#1.0.24.8.3.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.53', '200', 'Communication flows over thresholed with Dcu and masterstation', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.22.1.12.0.0.0.0.3.*********.72.0', '100', 'Active energy import (+A) L2 rate 3 [Unit: kWh]', '3#********.3.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.22.51.0.*********.0.*********.72.0', '100', 'L1 min export active power', '3#1.0.22.51.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.4E.01', '300', '表盖开启告警发生', 'E200004E', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.51', '200', 'Dcu reading meter failed by RS485 occurrence', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.37.0.0.0.0.*******28.3.63.0', '100', 'Instantaneous reactive power L1 (|+R|+|-R|) [Unit: kW]', '3#1.0.133.7.0.255#2', 'W', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.41.9.1', '100', 'Purchase event', '7#*********.9.255#2#252', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.12.129', '200', 'Current Forward Active Energy Data (Total/Tariff 1-M)', '0C#129', NULL, 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.3.14.0.0.1', '300', 'The number of load flow reverse L1', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.21.*********.0.1', '100', 'Phase 1 import active energy T1', '3#********.1.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.*******9.133.0.103.0', '100', 'last 3 month credit consumption ', '1#0.**********.103#2', '0.01', 'RW', '1', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.20.20.10.15.14.2.29.0.0.0.0.0.0.*******4', '100', 'Cumulative export active energy, -A - rate 5', '3#1.0.2.8.5.255#2', NULL, 'RW', '0.01', NULL, NULL, 'double-long-unsigned', '12', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.44.8.3.255.0.0.1', '100', 'Phase 2 export reactive energy T3', '3#1.0.44.8.3.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.44.*********.0.1', '100', 'Phase 2 export reactive energy T1', '3#1.0.44.8.1.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.43', '100', 'Search meter list', '1#0.0.96.81.0.255#6', NULL, 'RW', '1', NULL, NULL, 'special', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.63.51.0.*********.0.*********.72.0', '100', 'L3 min import reactive power', '3#1.0.63.51.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.1.23', '100', 'Last purchase credit', '1#**********.1.255#2', NULL, 'R', '1', NULL, NULL, 'float', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.53.01', '300', '恒定磁场干扰告警发生', 'E2000053', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.0A.00', '300', 'A相TA二次侧开路恢复', 'E200000A', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.23.*********.0.1', '100', 'Phase 1 import reactive energy T2', '3#1.0.23.8.2.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.**********.13.0.0.0.********.16.255.0.0.1', '100', 'Last 3 techincian IDs', '1#********.16.255#2', '1', 'RW', '0.001', NULL, '0', 'octet-string', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.24.1.12.0.0.0.0.4.*********.72.0', '100', 'Active energy export (-A) L1 rate 4 [Unit: kWh]', '3#1.0.22.8.4.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.1.8.0.0.0.0.5.*********.38.1', '100', 'Maximum Demand Register 1 -Active energy import(+A) rate 5 (Monthly) [Unit: kW]', '4#1.0.1.6.5.255#2', 'W', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.41.8.3.255.0.0.1', '100', 'Phase 2 import active energy T3', '3#********.3.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.1.0.73.27.0.*********.0.0.*******.38.0', '100', 'Average phase C power factor', '3#1.0.73.27.0.255#2', 'Wh', 'R', '0.001', '1000', '1', 'double-long', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.34', '100', 'Server Port', '1#*********.20.255#3', NULL, 'RW', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.33.01', '300', '终端全部掉电发生', 'E2000033', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.1.247', '100', 'Zero line interference start', '7#*********.1.255#2#247', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.99.97.383', '100', 'Long power failure event', '7#1.0.99.97.0.255#2#383', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.**********.13.0.0.0.********.15.255.0.0.1', '100', 'Last 2 techincian IDs', '1#********.15.255#2', '1', 'RW', '0.001', NULL, '0', 'octet-string', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.08.01', '300', 'B相TA二次侧短路发生', 'E2000008', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.50', '100', 'Phase 1 import reactive energy T4', '3#1.0.23.8.4.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.*******.0.11.129.4.255.0', '100', 'UnbalancedCurrentAmp', '3#1.0.11.129.4.255#2', '1', 'RW', '0.01', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.86.0', '100', 'First Breath - Restoration', '861', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.63', '200', 'Magnetic field exception occurrence', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.00.02.FF.01', '300', '月冻结反向有功电能数据块', '0002FF01', 'kWh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.8.0.6.2.1.8.0.0.9.6.0.*********.38.1', '100', 'Import apparent maximum demand,total', '4#1.0.9.6.0.255#2', 'VA', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.1.1.12.0.0.0.0.0.0.0.224.4.19.0', '100', 'Daily increase export reactive energy\r\n', '3#1.0.4.19.0.255#2', 'Wh', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.0B.01', '300', 'B相TA二次侧开路发生', 'E200000B', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.22.00', '300', 'C相电压畸变恢复', 'E2000022', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.30', '100', 'Phase 1 export active energy T4', '3#1.0.22.8.4.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.42.53.0.*********.0.*********.72.0', '100', 'L2 max export active power', '3#1.0.42.53.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.21', '200', 'Dcu power down', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.30', '100', 'GPRS Network Standard', '1#*********.23.255#2', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.23.1.12.0.0.0.0.3.*********.72.0', '100', 'Active energy import (+A) L3 rate 3 [Unit: kWh]', '3#********.3.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.1.0.61.27.0.*********.0.0.*******.38.0', '100', 'Average positive active power of phase C', '3#1.0.61.27.0.255#2', 'Wh', 'R', '0.001', '1000', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.4.326', '100', 'Current unbalanced by Neutral line end', '7#*********.4.255#2#326', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.1', '100', 'Add meter list', '1#0.0.96.81.0.255#1', NULL, 'A', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.31.1.12.0.*******.*********.73.0', '100', 'Reactive energy export (-R) L2 rate 1 [Unit: kVarh]', '3#1.0.44.8.1.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40*******2.1', '100', 'Very low PF restoration', '121', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.1.0.72.27.0.*********.0.0.*******.38.0', '100', 'Average C-phase voltage', '3#1.0.72.27.0.255#2', 'V', 'R', '0.1', NULL, '1', 'double-long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.24.8.4.255.0.0.1', '100', 'Phase 1 export reactive energy T4', '3#1.0.24.8.4.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E0.80.00.0F', '300', '终端电表档案', 'E080000F', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.1A.01', '300', 'A相电压过压发生', 'E200001A', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.163', '200', 'Reverse Active Energy Data (Total/Tariff 1-M)(Daily Frozen)', '0D#163', NULL, 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.51.0.*********.0.*********.72.0', '100', 'min import active power', '3#1.0.1.51.0.255#2', 'Wh', 'R', '1', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.63.53.0.*********.0.*********.72.0', '100', 'L3 max import reactive power', '3#1.0.6**********#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.********.0.0.0.0.0.3.64.0.29.0', '100', 'Instantaneous phase angle wave voltage L2 [Unit: V]', 'V', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.3D.00', '300', '抄表失败恢复', 'E200003D', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.61.8.4.255.0.0.1', '100', 'Phase 3 import active energy T4', '3#********.4.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.37.0.0.0.0.0.*********.38.0', '100', 'Instantaneous active power (|+A|+|-A|) [Unit: kW]', '3#********.0.255#2', 'W', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.26.01', '300', '无功过补偿发生', 'E2000026', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.*******.0.15.9.0.103.0', '100', 'last 3 month active energy |+A| + |-A| consumption', '3#1.0.15.9.0.103#2', '1', 'RW', '0.01', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.0.1.12.0.0.0.0.0.*********.72.1', '100', 'Daily increase active energy', '3#1.0.15.19.0.255#2', NULL, 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.162', '200', 'Forward Reactive Energy Data (Total/Tariff 1-M)(Daily Frozen)', '0D#162', NULL, 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.27', '200', 'Voltage/Current unblance over threshold occurrence', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.15.38.1', '100', 'Maximum value during emergency overload', '100', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.51.51.128.*********.0.*********.72.0', '100', 'L1-L2 Min voltage', '3#1.0.32.51.128.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.62.8.3.255.0.0.1', '100', 'Phase 3 export active energy T3', '3#********.3.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.**********.13.0.0.0.********.14.255.0.0.1', '100', 'Last 1 techincian IDs', '1#********.14.255#2', '1', 'RW', '0.001', NULL, '0', 'octet-string', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.3.********.0.0.0.0.0.224.0.0.5', '100', 'L2 average current', '3#1.0.51.24.0.255#2', 'Wh', 'R', '0.1', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.2.20.20.09.08.18.14.2.0.0.0.0.0.0.0.0.8', '100', 'Instantaneous reactive import power (+R) L1', '3#1.0.23.7.0.255#2', NULL, 'RW', '0.001', NULL, NULL, 'double-long-unsigned', '12', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.27', '100', 'Phase 1 export active energy T1', '3#1.0.22.8.1.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.*******.0.11.129.3.255.0', '100', 'ReverseCurrent', '3#1.0.11.129.3.255#2', '1', 'RW', '0.01', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.62.8.4.255.0.0.1', '100', 'Phase 3 export active energy T4', '3#********.4.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.0.1.12.0.0.1.2.3.*********.72.8', '100', 'Daily increase import reactive energy', '3#1.0.3.19.0.255#2', 'Wh', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.20.20.10.15.14.2.29.0.0.0.0.0.0.0.0.0.29', '100', 'Cumulative import reactive energy, -R - rate 6', '3#1.0.4.8.6.255#2', NULL, 'RW', '0.01', NULL, NULL, 'double-long-unsigned', '12', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.0.0.12.0.1.1.2.3.0.0.*******.0.65.1', '100', 'Average power factor of billing period', '3#1.0.13.0.0.255#2', NULL, 'R', '0.001', NULL, NULL, 'long-unsigned', '2', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('44.4.5.1', '100', 'Event Code', '44.4.5.1', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.59', '200', 'Meter cover open', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.24.8.4.255.0.0.1', '100', 'Phase 1 export reactive energy T4', '3#1.0.24.8.4.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('43.0.0.3', '100', 'Auto LCD Period', '3#0.0.96.58.1.255#2', NULL, 'RW', '1', NULL, NULL, 'long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.0.1.0.2.30.0.*********.0.*********.72.0', '100', 'Active energy import (-A) (interval) [Unit: kWh]', '3#1.0.2.30.0.255#2', 'kWh', NULL, '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.*********.13.0.0.0.*********.6.255.0.0.1', '100', 'Credit', '3#*********.6.255#2', '1', 'RW', '0.01', NULL, '0', 'double-long', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.*********.13.0.0.0.*********.6.255.0.0.1', '100', 'Credit', '3#*********.6.255#2', '1', 'RW', '0.01', NULL, '0', 'double-long', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.24.8.0.255.0.0.1', '100', 'Phase 1 export reactive energy', '3#1.0.24.8.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.4.0.0.0.0.0.71.7.32.0.5.0', '100', 'Total harmonic of phase C current', '3#1.0.71.7.124.255#2', '%', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.45.00', '300', 'B相电压偏差越限恢复', 'E2000045', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.21.*********.0.1', '100', 'Phase 1 import active energy T2', '3#********.2.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40*******.0', '100', 'Y Phase - Voltage missing occurrence', '10', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.**********.13.0.0.0.0.0.96.54.70.255.0.0.1', '100', 'Total number of charge transactions', '1#0.0.96.54.70.255#2', '1', 'RW', '1', NULL, '0', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.07.00', '300', 'A相TA二次侧短路恢复', 'E2000007', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.1.1.1.1.12.0.0.0.0.0.*********.72.0', '100', 'Daily increase import active energy', '3#1.0.1.19.0.255#2', 'Wh', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.2********.0.0.0.3.*********.73.0', '100', 'Reactive energy import (+R) L3 rate 3 [Unit: kVarh]', '3#1.0.63.8.3.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.26.*******.0.41.**********.0.1', '100', 'L2 Average import active power', '3#1.0.41.27.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.8.*******.8.1.0.128.8.0.255.0.224.3.38.2', '100', 'Total reactive energy', '3#1.0.128.8.0.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.0.0.12.0.1.1.2.3.0.0.*******.0.65.11', '100', 'Current maximum demand', '3#1.********.255#2', NULL, 'R', '1', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.01.01', '300', '计量装置门开闭发生', 'E2000001', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.43.8.3.255.0.0.1', '100', 'Phase 2 import reactive energy T3', '3#********.3.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.41.27.0.*********.0.*********.72.0', '100', 'L2 Average import active power', '3#1.0.41.27.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.3.24.0.*********.0.*********.72.0', '100', 'Total Average Reactive Power', '3#1.0.3.24.0.255#2', 'Wh', 'R', '1', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.3.********.0.0.0.0.0.224.0.0.2', '100', 'L2 average voltage', '3#1.0.52.24.0.255#2', 'Wh', 'R', '0.1', '2001', '1', 'long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.55', '200', 'CT exception occurrence', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('4.8.15.244', '100', 'B Intersect End', '7#*********.4.255#2#244', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('44.4.4.2', '100', 'Debits', '1#0.0.96.54.8.255#2', NULL, 'RW', '1', NULL, NULL, 'double-long', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.7', '100', 'Phase 1 import active energy T1', '3#********.1.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.3B.01', '300', '继电器变位发生', 'E200003B', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.164', '200', 'Reverse Reactive Energy Data (Total/Tariff 1-M)(Daily Frozen)', '0D#164', NULL, 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.43.*********.0.1', '100', 'Phase 2 import reactive energy T2', '3#********.2.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.4C.00', '300', '发现未知电表恢复', 'E200004C', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.*********.0.0.0.0.*********.73.0', '100', 'Reactive energy import (+R) L2 [Unit: kVarh]', '3#********.0.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.2.12.0.0.0.0.0.*********.72.0', '100', 'Combined active energy import (+A) [Unit: kWh]', 'Wh', NULL, NULL, '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.7.29.*******.0.***********.0.0.1', '100', 'Unnamed', '3#1.0.***********#2', '1', 'RW', '0.001', NULL, '1', 'long-unsigned', '2', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.2.1.8.0.0.9.6.3.*********.38.1', '100', 'Import apparent maximum demand,t3', '4#1.0.9.6.3.255#2', 'VA', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('6.37.0.2', '100', 'Current average reactive power\r\n\r\n', '3#1.0.3.5.0.255#2', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.10', '100', 'Concentrator version information', '1#0.0.96.57.0.255#2', NULL, 'R', '1', NULL, NULL, 'special', NULL, '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.43', '200', 'Meter reading drop occurrence', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.34.01', '300', '终端上电发生', 'E2000034', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.0.9.8.5.*********.73.1', '100', 'Cumulative import apparent energy,+E,T5', '3#*******.5.255#2', 'VAh', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.**********.22.*******.0.15.**********.0.1', '100', 'Average active power(import + export)', '3#1.0.15.24.0.255#2', '1', 'RW', '0.001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.3.14.0.0.6', '300', 'The time of load flow reverse L3 [Unit: Min]', 'Min', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.21.53.0.*********.0.*********.72.0', '100', 'L1 max import active power', '3#1.0.21.53.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.32.1.12.0.0.0.0.2.*********.73.0', '100', 'Reactive energy export (-R) L3 rate 2 [Unit: kVarh]', '3#********.2.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.67', '100', 'Phase 1 export reactive energy T1', '3#1.0.24.8.1.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.9', '100', 'Phase 1 import active energy T3', '3#********.3.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.22.8.3.255.0.0.1', '100', 'Phase 1 export active energy T3', '3#1.0.22.8.3.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.21.1.12.0.*******.*********.72.0', '100', 'Active energy import (+A) L1 rate 1 [Unit: kWh]', '3#********.1.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.62.51.0.*********.0.*********.72.0', '100', 'L3 min export active power', '3#1.0.62.51.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.*******9.133.0.104.0', '100', 'last 4 month credit consumption ', '1#0.**********.104#2', '0.01', 'RW', '1', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.2.1.8.0.0.9.6.1.*********.38.1', '100', 'Import apparent maximum demand,t1', '4#1.0.9.6.1.255#2', 'VA', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('41.96.11.8', '100', 'Normal over load event code', '1#********1.8.255#2', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.12.131', '200', 'Current Reverse Active Energy Data (Total/Tariff 1-M)', '0C#131', NULL, 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.75', '100', 'Phase 2 export reactive energy T4', '3#1.0.44.8.4.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.3.5.1', '100', 'Voltage Sag Phase L1 occurrence', '0.4.25.9.0.255#3#51', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.*********.13.0.0.0.********0.1.255.2.0.1', '100', 'Status', '7#********0.2.255#2', '1', 'RW', '1', NULL, '0', 'unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.00.02.FF.00', '300', '反向有功电能数据块', '0002FF00', 'kWh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.32.34.1', '100', 'L1 minimum value during over voltage', '3#1.0.32.34.0.255#2', 'V', 'R', '0.1', NULL, NULL, 'long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.32', '100', 'Phase 2 export active energy T1', '3#1.0.42.8.1.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.122.127.3.*********.6.255.2', '100', 'Available credit', '3#*********.6.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.31.1.12.0.0.0.0.3.*********.73.0', '100', 'Reactive energy export (-R) L2 rate 3 [Unit: kVarh]', '3#1.0.44.8.3.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('************.*********.6.255.2', '100', 'Available credit', '3#*********.6.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.12.19.12', '100', 'Missing neutral end', '7#*********.1.255#2#236', '1', 'RW', '1', NULL, '0', NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('44.0.0.255', '100', 'Image Status', '18#0.0.44.0.0.255#6', NULL, 'R', '1', NULL, NULL, 'enum', '1', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.43.8.4.255.0.0.1', '100', 'Phase 2 import reactive energy T4', '3#********.4.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.8.7.203', '100', 'Over voltage end', '7#*********.7.255#2#203', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.0.73.6.19.1.8.0.0.*******.0.224.3.63.0', '100', 'Export reactive demand occurred time', '5#1.0.4.4.0.255#5', NULL, 'R', '1', NULL, NULL, 'date-time', '12', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.80', '100', 'Phase 3 export reactive energy T4', '3#********.4.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('38.0.0.99', '100', 'Mask word', '1#0.0.96.4.0.255#2', NULL, 'RW', '1', NULL, NULL, 'bit-string', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.25.1.12.0.0.0.0.0.*********.72.0', '100', 'Active energy export (-A) L2 [Unit: kWh]', '3#1.0.42.8.0.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.1.0.2.27.0.*********.0.0.*******.38.0', '100', 'Average combined phase reverse active power', '3#1.0.2.27.0.255#2', 'Wh', 'R', '0.001', '1000', '1', 'double-long', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('43.0.0.2', '100', 'Manual LCD List', '7#0.0.21.0.2.255#3', NULL, 'RW', '1', NULL, NULL, 'special', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.02.02.02.00', '300', 'B相电流', '02020200', 'A', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.4.0.0.0.0.0.0.4.32.0.5.0', '100', 'Instantaneous average current L3', '3#1.0.71.27.0.255#2', 'W', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.28', '100', 'Phase 1 export active energy T2', '3#1.0.22.8.2.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.25.1.12.0.0.0.0.2.*********.72.0', '100', 'Active energy export (-A) L2 rate 2 [Unit: kWh]', '3#1.0.42.8.2.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.83', '200', 'Phase B Active Power Load Profile', '0D#83', NULL, 'R', '0.001', '2001', NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.52.51.0.*********.0.*********.72.0', '100', 'L2 min voltage', '3#1.0.52.51.0.255#2', 'Wh', 'R', '0.1', '2001', '1', 'long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.********.0.0.0.0.52.7.64.0.29.0', '100', 'Total harmonic of phase B voltage', '3#1.0.52.7.124.255#2', '%', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.27.1.12.0.0.0.0.3.*********.73.0', '100', 'Reactive energy import (+R) L1 rate 3 [Unit: kVarh]', '3#1.0.23.8.3.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.*******.0.15.9.0.108.0', '100', 'last 8 month active energy |+A| + |-A| consumption', '3#1.0.15.9.0.108#2', '1', 'RW', '0.01', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.1.1.20.20.7.6.0.0.*********.72.3', '100', 'Accumulated consumption credit(BL)', '3#0.0.19.131.0.255#2', NULL, 'R', '1', NULL, NULL, 'double-long', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.17.01', '300', 'B相电压失压发生', 'E2000017', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.32.51.0.*********.0.*********.72.0', '100', 'L1 min voltage', '3#1.0.32.51.0.255#2', 'Wh', 'R', '0.1', '2001', '1', 'long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.2********.0.0.0.0.*********.73.0', '100', 'Reactive energy import (+R) L3 [Unit: kVarh]', '3#1.0.63.8.0.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.18.*******.0.32.53.0.255.0.0.1', '100', 'Maximum L1 Voltage', '3#1.0.32.53.0.255#2', '1', 'RW', '0.001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.33.03', '300', '模块掉电', 'E2000033', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.61.01', '300', '漏电监测告警发生', 'E2000061', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.37.0.0.0.0.0.0.0.64.3.63.0', '100', 'Instantaneous reactive power L2 (|+R|+|-R|) [Unit: kW]', '3#1.0.133.7.0.255#2', 'W', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.1.55', '100', 'Terminal cover open log', '7#*********.6.255#2#2', '1', 'RW', '1', NULL, '0', NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.27.1.12.0.*******.*********.73.0', '100', 'Reactive energy import (+R) L1 rate 1 [Unit: kVarh]', '3#1.0.23.8.1.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.1.37', '100', 'Module cover close', '7#*********.1.255#2#37', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.26', '100', 'Phase 1 export active energy', '3#1.0.22.8.0.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.23.53.0.*********.0.*********.72.0', '100', 'L1 max import reactive power', '3#1.0.2**********#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.24.1.12.0.*******.*********.72.0', '100', 'Active energy export (-A) L1 rate 1 [Unit: kWh]', '3#1.0.22.8.1.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.64.*********.0.1', '100', 'Phase 3 export reactive energy T1', '3#********.1.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.21.1.12.0.0.0.0.3.*********.72.0', '100', 'Active energy import (+A) L1 rate 3 [Unit: kWh]', '3#********.3.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.*******.0.128.8.0.101.0', '100', 'Cumulative reactive energy of last month', '3#1.0.128.8.0.101#2', '1', 'RW', '0.01', NULL, '0', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.42.0.0.0.0.0.*******.38.0', '100', 'Instantaneous average power import (+A)', '3#1.0.1.27.0.255#2', 'W', 'R', '1', NULL, NULL, 'double-long', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.4.0', '100', 'Low voltage in any phase occurrence', '40', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.30.1.12.0.0.0.0.0.*********.73.0', '100', 'Reactive energy export (-R) L1 [Unit: kVarh]', '3#1.0.24.8.0.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.52.34.1', '100', 'L2 minimum value during over voltage', '3#1.0.52.34.0.255#2', 'V', 'R', '0.1', NULL, NULL, 'long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.64.8.0.255.0.0.1', '100', 'Phase 3 export reactive energy', '3#********.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.22.1.12.0.0.0.0.2.*********.72.0', '100', 'Active energy import (+A) L2 rate 2 [Unit: kWh]', '3#********.2.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.24.1.12.0.0.0.0.2.*********.72.0', '100', 'Active energy export (-A) L1 rate 2 [Unit: kWh]', '3#1.0.22.8.2.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.4A.01', '300', 'C相闪变越限发生', 'E200004A', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.**********.13.0.0.0.0.0.96.54.66.255.0.0.1', '100', 'Install techincian ID', '1#0.0.96.54.66.255#2', '1', 'RW', '0.001', NULL, '0', 'octet-string', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.52', '100', 'Phase 2 import reactive energy T1', '3#********.1.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.18.*******.0.72.51.0.255.0.0.1', '100', 'Minimum L3 Voltage', '3#1.0.72.51.0.255#2', '1', 'RW', '0.001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('41.96.11.9', '100', 'Event code of emergency over load event log', '1#********1.9.255#2', NULL, 'R', '1', NULL, NULL, 'long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.51.51.0.*********.0.*********.72.0', '100', 'L2 min current', '3#1.0.51.51.0.255#2', 'Wh', 'R', '0.001', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.37.0.0.0.0.0.*********.38.1', '100', 'Instantaneous active power wz(|+A|+|-A|) [Unit: kW]', '3#********.0.255#2', NULL, 'R', '1', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.**********.13.0.0.0.********.7.255.0.0.1', '100', 'Customer ID', '1#********.7.255#2', '1', 'RW', '0.001', NULL, '0', 'octet-string', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.1.20.20.7.6.0.0.*********.72.14', '100', 'Instantaneous active power L1 (|+A|+|-A|) [Unit: kW]', '3#1.0.35.7.0.255#2', 'W', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.25.1.12.0.0.0.0.4.*********.72.0', '100', 'Active energy export (-A) L2 rate 4 [Unit: kWh]', '3#1.0.42.8.4.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.0.0.0.96.56.12.255.0', '100', 'last 1 current unblance record', '1#0.0.96.56.12.255#2', '1', 'RW', '1', NULL, '0', 'special', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.2.12.0.0.0.0.2.*********.72.0', '100', 'Combined active energy import (+A) [Unit: kWh]', 'Wh', NULL, NULL, '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.40.01', '300', '电能表A、B、C相失压总次数发生', 'E2000040', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.8.0.0.0.4.11.15.29.0.*********.224.3.38.6', '100', 'T1 cumulative- reactive Energy Kvarh.', '3#1.0.128.8.1.255#2', NULL, 'R', '1', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.40', '100', 'VPN Dial', '1#0.0.96.51.29.255#2', NULL, 'RW', '1', NULL, NULL, 'special', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.5.2.12.*******.0.*********.73.0', '100', 'Combined reactive energy import 1 (+A) [Unit: kVarh]', 'Varh', NULL, NULL, '0.01', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.8.1.37.0.0.0.0.*******28.3.38.0', '100', 'Instantaneous apparent power L1 [Unit: kW]', NULL, 'W', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.1.1.12.0.0.0.0.8.*********.72.1', '100', 'Active energy import (+A) rate 8 (Monthly) [Unit: kWh]', '3#1.0.1.8.8.255#2', 'Wh', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.*******9.133.0.102.0', '100', 'last 2 month credit consumption ', '1#0.**********.102#2', '0.01', 'RW', '1', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.2.1.8.0.0.9.6.5.1.0.224.3.38.1', '100', 'Import apparent maximum demand,t5,occured time', '4#1.0.9.6.5.255#5', NULL, 'R', '1', NULL, '1', 'date-time', '12', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.1B.01', '300', 'B相电压过压发生', 'E200001B', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.21.1.12.0.0.0.0.0.*********.72.0', '100', 'Active energy import (+A) L1 [Unit: kWh]', '3#********.0.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.1.0.51.27.0.*********.0.0.*******.38.0', '100', 'Average B-phase current', '3#1.0.51.27.0.255#2', 'A', 'R', '0.001', '1000', '1', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.1.0.33.27.0.*********.0.0.*******.38.0', '100', 'Average phase A power factor', '3#1.0.33.27.0.255#2', 'Wh', 'R', '0.001', '1000', '1', 'double-long', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.********.*******.*********.73.0', '100', 'Combined reactive energy import 2 (+A) [Unit: kVarh]', 'Varh', NULL, NULL, '0.01', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.**********.13.*******.0.11.129.4.255.0.0.1', '100', 'UnbalancedCurrentAmp', '3#1.0.11.129.4.255#2', '1', 'RW', '0.01', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.1.315', '100', 'Neutral disturbance end', '7#*********.1.255#2#315', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.41.9.322', '100', 'emergency over load end', '7#*********.9.255#2#322', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.7.1', '100', 'Y Phase current reverse restoration', '71', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.13.24.0.*********.0.*********.72.0', '100', 'average power factor of inteval rcd', '3#1.0.***********#2', 'Wh', 'R', '0.001', '2001', '1', 'long-unsigned', '2', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('***********', '200', 'Vip user config', '0A#35', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.*******9.133.0.107.0', '100', 'last 7 month credit consumption ', '1#0.**********.107#2', '0.01', 'RW', '1', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********', '100', 'Manufactory Identifier', '1#********.1.255#2', NULL, 'R', '1', NULL, NULL, 'visible-string', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.64.*********.0.1', '100', 'Phase 3 export reactive energy T2', '3#********.2.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.1.314', '100', 'Neutral disturbance start', '7#*********.1.255#2#314', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.**********.13.0.0.0.********.7.255.0.0.1', '100', 'Customer ID', '1#********.7.255#2', '1', 'RW', '0.001', NULL, '0', 'octet-string', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.**********.13.0.0.0.0.0.96.5.10.255.0.0.1', '100', 'Relay status', '1#0.0.96.5.10.255#2', '1', 'RW', '0.001', NULL, '0', 'octet-string', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.24.*********.0.1', '100', 'Phase 1 export reactive energy T1', '3#1.0.24.8.1.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.2.1.8.0.0.9.6.3.1.0.224.3.38.1', '100', 'Import apparent maximum demand,t3,occured time', '4#1.0.9.6.3.255#5', NULL, 'R', '1', NULL, '1', 'date-time', '12', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.3.2', '100', 'Step tariff activation time', '1#*********.0.255#2', NULL, 'RW', '1', NULL, NULL, 'octet-string', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.1.1.12.0.0.0.0.7.*********.72.1', '100', 'Active energy import (+A) rate 7 (Monthly) [Unit: kWh]', '3#1.0.1.8.7.255#2', 'Wh', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.1.1.12.0.0.0.0.5.*********.72.1', '100', 'Active energy import (+A) rate 5 (Monthly) [Unit: kWh]', '3#1.0.1.8.5.255#2', 'Wh', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.42.8.3.255.0.0.1', '100', 'Phase 2 export active energy T3', '3#1.0.42.8.3.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.42.8.0.255.0.0.1', '100', 'Phase 2 export active energy', '3#1.0.42.8.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.2.1.8.0.0.9.6.4.*********.38.1', '100', 'Import apparent maximum demand,t4', '4#1.0.9.6.4.255#2', 'VA', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.********.0.0.0.0.0.2.64.0.29.0', '100', 'Instantaneous fundamental wave voltage L2 [Unit: V]', 'V', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.17.00', '300', 'B相电压失压恢复', 'E2000017', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.14', '100', 'Parameter reset (all)', '1#0.0.96.51.6.255#4', NULL, 'A', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.94', '200', 'Phase C Current Profile', '0D#94', NULL, 'R', '0.001', '2003', NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.********.0.0.*******.32.0.29.0', '100', 'Instantaneous line voltage L3/L1 [Unit: V]', 'V', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.45', '200', 'Energy deviation over threshold occurrence', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.5.1', '100', 'Voltage unbalance restoration', '51', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.26.133.96', '100', 'By pass  start', '7#*********.4.255#2#234', '1', 'RW', '1', NULL, '0', NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.104', '200', 'Total Reverse Reactive Energy Data', '0D#104', NULL, 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.87', '200', 'Phase B Reactive Power Load Profile', '0D#87', NULL, 'R', '0.001', '2001', NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.31', '100', 'Phase 2 export active energy', '3#1.0.42.8.0.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.34', '200', 'Active power differential over threshold occurrence', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.3C.00', '300', '电能表拉合闸失败恢复', 'E200003C', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.3A.00', '300', '月通信流量越限恢复', 'E200003A', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.03.01', '300', '电压逆相序发生', 'E2000003', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.21.1.12.0.0.0.0.2.*********.72.0', '100', 'Active energy import (+A) L1 rate 2 [Unit: kWh]', '3#********.2.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.4.1', '100', 'Low voltage in any phase restoration', '41', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.8.*******.0.1.0.0.*********.224.3.38.3', '100', 'Monthly increase active energy', '3#1.0.15.9.0.255#2', NULL, 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.7.29.*******.0.128.6.0.255.0.0.1', '100', 'Unnamed', '4#1.0.128.6.0.255#2', '1', 'RW', '0.001', NULL, '1', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.41.9.321', '100', 'emergency over load start', '7#*********.9.255#2#321', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.77', '100', 'Phase 3 export reactive energy T1', '3#********.1.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.22.27.0.*********.0.*********.72.0', '100', 'L1 Average export active power', '3#1.0.22.27.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.21.*********.0.1', '100', 'Phase 1 import active energy T2', '3#********.2.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.52.24.0.*********.0.*********.72.0', '100', 'L2 average voltage', '3#1.0.52.24.0.255#2', 'Wh', 'R', '0.1', '2001', '1', 'long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('************.1.********.255.2', '100', 'Active Energy Import – kWh – L2', '3#1.********.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long-unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.51.27.0.*********.0.*********.72.0', '100', 'L2 Average current', '3#1.0.51.27.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.24.00', '300', 'B相电流畸变恢复', 'E2000024', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('38.0.0.54', '100', 'Passive oad limit activate time', '1#0.0.96.54.1.255#2', NULL, 'RW', '1', NULL, NULL, 'date-time', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.41.*********.0.1', '100', 'Phase 2 import active energy T2', '3#********.2.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.20.01', '300', 'A相电压畸变发生', 'E2000020', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.4F.00', '300', '费率参数表编程次数更改恢复', 'E200004F', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.37', '100', 'RF Modem Version', '1#*********.25.255#2', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('38.0.0.100', '100', 'Demand period', '3#1.0.0.8.0.255#2', 's', 'RW', '1', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.2.1.8.0.0.9.6.4.1.0.224.3.38.1', '100', 'Import apparent maximum demand,t4,occured time', '4#1.0.9.6.4.255#5', NULL, 'R', '1', NULL, '1', 'date-time', '12', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.32.1.12.0.*******.*********.73.0', '100', 'Reactive energy export (-R) L3 rate 1 [Unit: kVarh]', '3#********.1.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.36.00', '300', '电能表时段或费率更改恢复', 'E2000036', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.32.1.12.0.0.0.0.0.*********.73.0', '100', 'Reactive energy export (-R) L3 [Unit: kVarh]', '3#********.0.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.21.01', '300', 'B相电压畸变发生', 'E2000021', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.33', '200', 'Dcu fault', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.8.0', '100', 'B Phase current reverse occurrence', '80', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.73', '100', 'Phase 2 export reactive energy T2', '3#1.0.44.8.2.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.71.51.0.*********.0.*********.72.0', '100', 'L3 min current', '3#1.0.71.51.0.255#2', 'Wh', 'R', '0.001', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.37.00', '300', '电能表脉冲常数更改恢复', 'E2000037', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.59', '100', 'Phase 3 import reactive energy T3', '3#1.0.63.8.3.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.51', '100', 'Phase 2 import reactive energy', '3#********.0.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40*******.1', '100', 'Y Phase - Voltage missing restoration', '11', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.*******9.133.0.109.0', '100', 'last 9 month credit consumption ', '1#0.**********.109#2', '0.01', 'RW', '1', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.57', '100', 'Phase 3 import reactive energy T1', '3#1.0.63.8.1.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.4.0.0.0.0.0.0.7.128.0.5.0', '100', 'Average harmonic in L1 Instant current', '3#1.0.31.27.124.255#2', '%', 'R', '0.01', NULL, NULL, 'long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.72.27.0.*********.0.*********.72.0', '100', 'L3 Average voltage', '3#1.0.72.27.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.22.1.12.0.0.0.0.4.*********.72.0', '100', 'Active energy import (+A) L2 rate 4 [Unit: kWh]', '3#********.4.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.43.8.0.255.0.0.1', '100', 'Phase 2 import reactive energy', '3#********.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.1.20.20.7.6.0.0.*********.72.18', '100', 'L1 Instant reactive power(import + export)', '3#1.0.138.7.0.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.44.01', '300', 'A相电压偏差越限发生', 'E2000044', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.50.01', '300', '阶梯表编程次数更改发生', 'E2000050', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.54', '200', 'Meter running state variables changed', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.52', '200', 'Dcu reading meter failed by RS485 recovery', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.1.1.54.0.0.0.0.*******28.0.29.0', '100', 'Instantaneous voltage L1 [Unit: V] ', '3#*********.0.255#2', 'Wh', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '1');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.6.1', '100', 'R Phase current reverse restoration', '61', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.52.27.0.*********.0.*********.72.0', '100', 'L2 Average voltage', '3#1.0.52.27.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.3.20.1', '100', 'Terminal cover removed occurrence', '0.4.25.9.0.255#3#201', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.00.01.FF.01', '300', '月冻结正向有功电能数据块', '0001FF01', 'kWh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********', '100', 'HES ip address and port', '1#********.128.255#2', NULL, 'RW', '1', NULL, NULL, 'special', NULL, '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('************.********.0.255.2', '100', 'Active Energy Import – kWh – L1', '3#********.0.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long-unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.0.0.0.0.*********.0.6', '100', 'Instantaneous active power (|+A|+|-A|) [Unit: kW]', '3#********.0.255#2', 'W', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.27.0.*********.0.0.*******.38.0', '100', 'Average reverse active power of phase A', '3#1.0.22.27.0.255#2', 'Wh', 'R', '0.001', '1000', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.24.8.0.255.0.0.1', '100', 'Phase 1 export reactive energy', '3#1.0.24.8.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.********.0.0.0.0.0.3.32.0.29.0', '100', 'Instantaneous phase angle wave voltage L3 [Unit: V]', 'V', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11111', '100', 'xxx333', '1', 'Wh', 'R', '0.1', '2000', NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.2.1.8.0.0.9.6.2.*********.38.1', '100', 'Import apparent maximum demand,t2', '4#1.0.9.6.2.255#2', 'VA', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.38', '100', 'Blacklist of meter', '1#0.0.96.51.14.255#2', NULL, 'RW', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.3.********.0.0.0.0.0.224.0.0.4', '100', 'L1 average current', '3#1.0.31.24.0.255#2', 'Wh', 'R', '0.1', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.0.1.12.0.0.1.2.3.*********.72.4', '100', 'Daily increase import active energy', '3#1.0.1.19.0.255#2', 'Wh', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.00.06.FF.01', '300', '月冻结第二象限无功电能数据块', '0006FF01', 'kvarh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.12.25', '200', 'Current Instantaneous Data', '0C#25', NULL, 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('41.96.10.10', '100', 'Limiter threshold', '71#0.0.17.0.0.255#3', '1', 'RW', '1', NULL, '1', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.3.********.0.0.0.0.0.224.0.0.8', '100', 'Total Average Reactive Power', '3#1.0.3.24.0.255#2', 'Wh', 'R', '1', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.38.00', '300', '计量互感器倍率更改恢复', 'E2000038', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.27', '100', 'Broadcast Time Parameter', '1#0.0.96.51.12.255#2', NULL, 'RW', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.66', '100', 'autoConnect mode', '29#*******.0.255#2', NULL, 'RW', '1', NULL, NULL, 'enum', '1', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.23.8.4.255.0.0.1', '100', 'Phase 1 import reactive energy T4', '3#1.0.23.8.4.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.2.1.8.0.0.9.6.6.1.0.224.3.38.1', '100', 'Import apparent maximum demand,t6,occured time', '4#1.0.9.6.6.255#5', NULL, 'R', '1', NULL, '1', 'date-time', '12', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.41.8.4.255.0.0.1', '100', 'Phase 2 import active energy T4', '3#********.4.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.7.31.*******.0.51.7.124.255.0.0.1', '100', 'Total harmonic of phase B current', '3#1.0.51.7.124.255#2', '1', 'RW', '0.01', NULL, '1', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.1.54', '100', 'Top cover open log', '7#*********.5.255#2#1', '1', 'RW', '1', NULL, '0', NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.99.97.243', '100', 'Phase a power down - end', '7#1.0.99.97.0.255#2#243', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.31', '100', 'Phase 2 export active energy', '3#1.0.42.8.0.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.42', '100', 'Topology', '1#0.1.96.1.187.255#2', NULL, 'R', '1', NULL, NULL, 'special', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.67', '100', 'autoAnswer mode', '28#0.0.2.2.0.255#2', NULL, 'RW', '1', NULL, NULL, 'enum', '1', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******.0.61.8.0.*********.**********.0.1', '100', 'Phase 3 import active energy', '3#********.0.255#2', NULL, NULL, '0.001', NULL, '1', 'double-long-unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.10', '100', 'Phase 1 import active energy T4', '3#********.4.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.18', '100', 'Phase 3 import active energy T2', '3#********.2.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.22.53.0.*********.0.*********.72.0', '100', 'L1 max export active power', '3#1.0.22.53.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******.0.21.8.0.*********.**********.0.1', '100', 'Phase 1 import active energy', '3#********.0.255#2', NULL, NULL, '0.001', NULL, '1', 'double-long-unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.**********.13.0.0.0.0.0.96.54.71.255.0.0.1', '100', 'Meter status - Battery status', '1#0.0.96.54.71.255#2', '1', 'RW', '1', NULL, '0', 'long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.0.9.8.3.*********.73.1', '100', 'Cumulative import apparent energy,+E,T3', '3#*******.3.255#2', 'VAh', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.51.1', '100', 'Earth loading restoration', '511', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.6.200', '100', 'Under voltage end', '7#*********.6.255#2#200', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('30*******5', '200', 'Phase sequence occurrence', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.05.06.07.FF', '300', '日冻结第三象限无功电能数据块', '050607FF', 'kvarh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.44.*********.0.1', '100', 'Phase 2 export reactive energy T1', '3#1.0.44.8.1.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.31.1.12.0.0.0.0.2.*********.73.0', '100', 'Reactive energy export (-R) L2 rate 2 [Unit: kVarh]', '3#1.0.44.8.2.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.31.1.12.0.0.0.0.0.*********.73.0', '100', 'Reactive energy export (-R) L2 [Unit: kVarh]', '3#1.0.44.8.0.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.12.133', '200', 'Current Reactive Energy Data (1st Quadrant)(Total/Tariff 1-M)', '0C#133', NULL, 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.31.27.0.*********.0.*********.72.0', '100', 'L1 Average current', '3#1.0.31.27.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '1');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.0.9.8.2.*********.73.1', '100', 'Cumulative import apparent energy,+E,T2', '3#*******.2.255#2', 'VAh', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.30.1.12.0.0.0.0.4.*********.73.0', '100', 'Reactive energy export (-R) L1 rate 4 [Unit: kVarh]', '3#1.0.24.8.4.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.0.73.6.19.1.8.0.0.0.0.0.*********.63.0', '100', 'Export reactive demand [Unit:kVar]', '5#1.0.4.4.0.255#2', 'Var', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.02.01.03.00', '300', 'C相电压', '02010300', 'V', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.18.*******.0.31.**********.0.1', '100', 'L1 average current', '3#1.0.31.27.0.255#2', '1', 'RW', '0.001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.*******.0.15.9.0.104.0', '100', 'last 4 month active energy |+A| + |-A| consumption', '3#1.0.15.9.0.104#2', '1', 'RW', '0.01', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.31.1.12.0.0.0.0.4.*********.73.0', '100', 'Reactive energy export (-R) L2 rate 4 [Unit: kVarh]', '3#1.0.44.8.4.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.03.01.00.00', '300', '三相失压次数及时间', '03010000', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('38.0.0.101', '100', 'Period of load profile 1', '3#1.0.0.8.4.255#2', 's', 'RW', '1', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.05.06.05.FF', '300', '日冻结第一象限无功电能数据块', '050605FF', 'kvarh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.12.31', '200', 'Current Phase A/B/C Forward/Reverse Active Energy Data', '0C#31', NULL, 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.18.*******.0.51.**********.0.1', '100', 'L2 average current', '3#1.0.51.27.0.255#2', '1', 'RW', '0.001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.*********.0.0.0.3.*********.73.0', '100', 'Reactive energy import (+R) L2 rate 3 [Unit: kVarh]', '3#********.3.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.169', '200', 'Forward Active Energy Data (Total/Tariff 1-M)(Monthly Frozen)', '0D#169', NULL, 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.54', '100', 'Phase 2 import reactive energy T3', '3#********.3.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.49.01', '300', 'B相闪变越限发生', 'E2000049', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.01.00', '300', '计量装置门开闭恢复', 'E2000001', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.1.1.20.20.7.6.0.0.*********.72.1', '100', 'Surplus credit(Monthly)(BL)', '3#**********.0.255#2', NULL, 'R', '1', NULL, NULL, 'double-long', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.50', '200', 'Meter Reading stop recovery', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.20.14.82', '100', 'On-Demand Billing', '9#0.0.10.0.1.255#1', NULL, 'A', '1', NULL, NULL, 'long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.5.320', '100', 'Module out', '7#*********.5.255#2#320', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.8.1.37.0.0.0.0.0.*********.38.0', '100', 'Instantaneous apparent total power [Unit: kW]', NULL, 'W', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.00.04.FF.01', '300', '月冻结反向无功电能数据块', '0004FF01', 'kvarh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.2F.00', '300', '电能表通讯失败恢复', 'E200002F', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.4.0.0.0.0.0.0.3.32.0.5.0', '300', 'Instantaneous phase angle current L3 [Unit: A]', 'A', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.61.*********.0.1', '100', 'Phase 3 import active energy T1', '3#********.1.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.16', '100', 'Broadcast key', '1#*********.26.255#3', NULL, 'RW', '1', NULL, NULL, 'octet-string', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.33', '100', 'Phase 2 export active energy T2', '3#1.0.42.8.2.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.0.0.0.96.54.66.255.0', '100', 'Install techincian ID', '1#0.0.96.54.66.255#2', '1', 'RW', '1', NULL, '0', 'octet-string', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.2********.*******.*********.73.0', '100', 'Reactive energy import (+R) L3 rate 1 [Unit: kVarh]', '3#1.0.63.8.1.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('44.4.4.11', '100', 'Error Code', '1#0.0.96.5.7.255#2', NULL, 'R', '1', NULL, NULL, 'special', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.27.01', '300', '无功欠补偿发生', 'E2000027', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.0.0.0', '100', 'Measurement Data Channel', NULL, NULL, NULL, '1', NULL, '1', NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.27.1.12.0.0.0.0.4.*********.73.0', '100', 'Reactive energy import (+R) L1 rate 4 [Unit: kVarh]', '3#1.0.23.8.4.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.4.0.0.0.0.0.51.7.64.0.5.0', '100', 'Total harmonic of phase B current', '3#1.0.51.7.124.255#2', '%', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.18.*******.0.3.**********.0.1', '100', 'Average Sum of reactive power+', '3#1.0.3.27.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.62.8.0.255.0.0.1', '100', 'Phase 3 export active energy', '3#********.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.39', '200', 'Current over threshold occurrence', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.26.*******.0.61.**********.0.1', '100', 'L3 Average import active power', '3#1.0.61.27.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.3.1.0.0.3', '300', 'The number of voltage lose L2', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.31.1.12.0.*******.*********.73.0', '100', 'Reactive energy export (-R) L2 rate 1 [Unit: kVarh]', '3#1.0.44.8.1.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.67', '100', 'Phase 1 export reactive energy T1', '3#1.0.24.8.1.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.42.0.0.0.0.0.0.0.0.2.38.0', '100', 'Instantaneous average power export (-A)', '3#1.0.2.27.0.255#2', 'W', 'R', '1', NULL, NULL, 'double-long', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.18.*******.0.52.51.0.255.0.0.1', '100', 'Minimum L2 Voltage', '3#1.0.52.51.0.255#2', '1', 'RW', '0.001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.0.0.12.0.1.1.2.3.0.0.*******.0.65.13', '100', 'Monthly increase active energy', '3#1.0.15.9.0.255#2', NULL, 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.1.36', '100', 'Module cover open', '7#*********.1.255#2#36', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.0.9.8.4.*********.73.1', '100', 'Cumulative import apparent energy,+E,T4', '3#*******.4.255#2', 'VAh', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.38', '200', 'Voltage over threshold recovery', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.22.*********.0.1', '100', 'Phase 1 export active energy T2', '3#1.0.22.8.2.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.1F.01', '300', 'C相电压断相发生', 'E200001F', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.12.01', '300', 'C相电流过流发生', 'E2000012', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.1E.00', '300', 'B相电压断相恢复', 'E200001E', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.3C.01', '300', '电能表拉合闸失败发生', 'E200003C', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.26', '100', 'Broadcast Time Order Enable', '1#0.0.96.51.12.255#1', NULL, 'A', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.64.*********.0.1', '100', 'Phase 3 export reactive energy T2', '3#********.2.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.47', '100', 'Phase 1 import reactive energy T1', '3#1.0.23.8.1.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.13.51.0.*********.0.*********.72.0', '100', 'Min power factor of inteval rcd', '3#1.0.13.51.0.255#2', 'Wh', 'R', '0.001', '2001', '1', 'long-unsigned', '2', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.1F.00', '300', 'C相电压断相恢复', 'E200001F', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.23.00', '300', 'A相电流畸变恢复', 'E2000023', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.63.8.0.255.0.0.1', '100', 'Phase 3 import reactive energy', '3#1.0.63.8.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.41.*********.0.1', '100', 'Phase 2 import active energy T1', '3#********.1.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.9.8.322', '100', 'normal over load end', '7#*********.8.255#2#322', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.20.20.10.15.14.2.29.0.0.0.0.0.0.0.0.0.8', '100', 'Cumulative import active energy, +A - rate 6', '3#1.0.1.8.6.255#2', NULL, 'RW', '0.01', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.23.1.12.0.0.0.0.0.*********.72.0', '100', 'Active energy import (+A) L3 [Unit: kWh]', '3#********.0.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.64.51.0.*********.0.*********.72.0', '100', 'L3 min export reactive power', '3#1.0.64.51.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.********.0.0.0.0.0.7.128.0.29.0', '100', 'Average harmonic in L1 Instant voltage', '3#*********.124.255#2', '%', 'R', '0.01', NULL, NULL, 'long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.0F.00', '300', 'C相潮流反向恢复', 'E200000F', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.3.1', '100', 'Over voltage in any phase restoration', '31', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.45.01', '300', 'B相电压偏差越限发生', 'E2000045', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.38.01', '300', '计量互感器倍率更改发生', 'E2000038', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('37.0.1.6.0.0.0', '100', 'Passive Calendar', NULL, NULL, 'RW', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.23.1.12.0.*******.*********.72.0', '100', 'Active energy import (+A) L3 rate 1 [Unit: kWh]', '3#********.1.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.8', '100', 'Phase 1 import active energy T2', '3#********.2.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.8.0.0.0.4.11.15.29.0.*********.224.3.38.7', '100', 'T2 cumulative- reactive Energy Kvarh.', '3#1.0.128.8.2.255#2', NULL, 'R', '1', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40*******2.0', '100', 'Very low PF occurrence', '120', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.8.1.37.0.0.0.0.0.0.0.64.3.38.0', '100', 'Instantaneous apparent power L2 [Unit: kW]', NULL, 'W', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.75', '100', 'Phase 2 export reactive energy T4', '3#1.0.44.8.4.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.********.0.0.0.0.0.4.64.0.29.0', '100', 'Instantaneous average voltage L2', '3#1.0.52.27.0.255#2', 'W', 'R', '0.1', NULL, NULL, 'double-long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.12.00', '300', 'C相电流过流恢复', 'E2000012', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.23', '100', 'Dormancy Time', '1#*********.16.255#2', NULL, 'RW', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.26.0.20.20.6.********.0.*********.72.5', '100', 'Available credit', '3#*********.6.255#2', NULL, 'R', '0.01', NULL, '1', 'double-long', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********', '100', 'CS Mode', '1#*********.20.255#2', NULL, 'RW', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********5', '200', 'Vip user config', '04#35', NULL, 'W', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.62.27.0.*********.0.*********.72.0', '100', 'L3 Average export active power', '3#1.0.***********#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.40', '200', 'Current over threshold recovery', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.********.0.0.0.0.*********.29.0', '100', 'Instantaneous average voltage L1', '3#*********.0.255#2', 'W', 'R', '0.1', NULL, '1', 'double-long-unsigned', '4', '1');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.40.0.5', '100', 'Recharge', '7#*********.0.255#2#252', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.07.01', '300', 'A相TA二次侧短路发生', 'E2000007', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.26.1.12.0.0.0.0.3.*********.72.0', '100', 'Active energy export (-A) L3 rate 3 [Unit: kWh]', '3#********.3.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.*********.5.0.0.0.*********.6.255.0.0.1', '100', 'Available credit', '3#*********.6.255#2', '1', 'RW', '0.01', NULL, '0', 'double-long', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.11.11.327', '100', 'Monthly power consumption over limit', '7#*********.0.255#2#327', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.20.14.94', '100', 'VoltageRatioDenominator', '1#1.0.0.4.6.255#2', NULL, 'RW', '1', NULL, NULL, 'long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.1.3', '200', 'All Parameter and Data Initialization', '01#3', NULL, 'W', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.********.**********.*********.72.5', '100', 'Average voltage L1', '3#*********.0.255#2', 'V', 'R', '1', NULL, '1', 'double-long-unsigned', '4', '1');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.8.0.0.0.4.11.15.29.0.*********.224.3.38.5', '100', 'Total cumulative-reactive Energy Kvarh.', '3#1.0.128.8.0.255#2', NULL, 'R', '1', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.*********.5.0.0.0.*********.6.255.0.0.1', '100', 'Available credit', '3#*********.6.255#2', '1', 'RW', '0.01', NULL, '0', 'double-long', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.*********.13.0.0.0.*********.6.255.1.0.1', '100', 'Credit', '3#*********.6.255#2', '1', 'RW', '0.01', NULL, '0', 'double-long', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.72.34.1', '100', 'L3 minimum value during over voltage', '3#1.0.72.34.0.255#2', 'V', 'R', '0.1', NULL, NULL, 'long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.4.0.0.0.0.0.0.2.32.0.5.0', '300', 'Instantaneous fundamental wave current L3 [Unit: A]', 'A', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('43.0.0.1', '100', 'Auto LCD List', '7#0.0.21.0.1.255#3', NULL, 'RW', '1', NULL, NULL, 'special', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.81', '200', 'Active Power Load Profile', '0D#81', NULL, 'R', '0.001', '2001', NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.**********.13.0.0.0.0.0.96.54.8.255.0.0.1', '100', 'Debits', '1#0.0.96.54.8.255#2', '1', 'RW', '1', NULL, '0', 'double-long', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('4.8.19.200', '100', 'End of Phase A Underpressure', '7#*********.3.255#2#200', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.0.********.7.255.0', '100', 'customer ID', '1#********.7.255#2', '1', 'RW', '1', NULL, '0', 'octet-string', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.24.01', '300', 'B相电流畸变发生', 'E2000024', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.61.51.0.*********.0.*********.72.0', '100', 'L3 min import active power', '3#1.0.61.51.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.30', '100', 'Phase 1 export active energy T4', '3#1.0.22.8.4.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.27', '100', 'Phase 1 export active energy T1', '3#1.0.22.8.1.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.63.*********.0.1', '100', 'Phase 3 import reactive energy T1', '3#1.0.63.8.1.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('30*******', '200', 'Dcu parameter and data initialization', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.1C.01', '300', 'C相电压过压发生', 'E200001C', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.82.1', '100', 'Netural disturbance - HF, dc or alternate method restoration', '821', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.88', '200', 'Phase C Reactive Power Load Profile', '0D#88', NULL, 'R', '0.001', '2001', NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.61.*********.0.1', '100', 'Phase 3 import active energy T1', '3#********.1.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.99.97.245', '100', 'Phase C power down - end', '7#1.0.99.97.0.255#2#245', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.29', '200', 'Capacitor switching self-locking occurrence', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.46', '100', 'Phase 1 import reactive energy', '3#1.0.23.8.0.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.23.8.3.255.0.0.1', '100', 'Phase 1 import reactive energy T3', '3#1.0.23.8.3.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.26.133.95', '100', 'Current reversal', '7#*********.4.255#2#91', '1', 'RW', '1', NULL, '0', NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.21.8.4.255.0.0.1', '100', 'Phase 1 import active energy T4', '3#********.4.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.36', '100', 'Phase 3 export active energy', '3#********.0.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.34.00', '300', '终端上电恢复', 'E2000034', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.********.0.0.0.0.0.4.32.0.29.0', '100', 'Instantaneous average voltage L3', '3#1.0.72.27.0.255#2', 'W', 'R', '0.1', NULL, NULL, 'double-long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.28', '200', 'Voltage/Current unblance over threshold recovery', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.7.31.*******.0.72.7.124.255.0.0.1', '100', 'Total harmonic of phase C voltage', '3#1.0.72.7.124.255#2', '1', 'RW', '0.01', NULL, '1', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.66', '100', 'Phase 1 export reactive energy', '3#1.0.24.8.0.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('111134', '100', '1111', '1', 'WH', 'R', '1', '2000', NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.*******.0.15.9.0.105.0', '100', 'last 5 month active energy |+A| + |-A| consumption', '3#1.0.15.9.0.105#2', '1', 'RW', '0.01', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.30.1.12.0.*******.*********.73.0', '100', 'Reactive energy export (-R) L1 rate 1 [Unit: kVarh]', '3#1.0.24.8.1.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.41.51.0.*********.0.*********.72.0', '100', 'L2 min import active power', '3#1.0.41.51.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.31.51.0.*********.0.*********.72.0', '100', 'L1 min current', '3#1.0.31.51.0.255#2', 'Wh', 'R', '0.001', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.52.38.1', '100', 'L2 maximum value during over voltage', '3#1.0.52.38.0.255#2', 'V', 'R', '0.1', NULL, NULL, 'long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.62', '100', 'User and password', '44#0.0.25.3.0.255#5', NULL, 'RW', '1', NULL, NULL, 'special', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.*******.0.15.9.0.106.0', '100', 'last 6 month active energy |+A| + |-A| consumption', '3#1.0.15.9.0.106#2', '1', 'RW', '0.01', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.33.02', '300', '表箱终端掉电', 'E2000033', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.64.8.3.255.0.0.1', '100', 'Phase 3 export reactive energy T3', '3#********.3.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E1.00.E0.01', '300', '月冻结线损值', 'E100E001', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.1.22', '100', 'Last 1th purchase token', '1#**********.4.255#2', NULL, 'R', '1', NULL, NULL, 'octet-string', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.38', '100', 'Phase 3 export active energy T2', '3#********.2.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.1.8.0.0.0.0.8.*********.38.1', '100', 'Maximum Demand Register 1 -Active energy import(+A) rate 8 (Monthly) [Unit: kW]', '4#1.0.1.6.8.255#2', 'W', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.26.1.12.0.0.0.0.0.*********.72.0', '100', 'Active energy export (-A) L3 [Unit: kWh]', '3#********.0.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.18.*******.0.72.53.0.255.0.0.1', '100', 'Maximum L1 Voltage', '3#1.0.72.53.0.255#2', '1', 'RW', '0.001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.8.*******.8.1.0.128.8.0.255.0.224.3.38.2', '100', 'Total reactive energy', '3#1.0.128.8.0.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.1.0.52.27.0.*********.0.0.*******.38.0', '100', 'Average B-phase voltage', '3#1.0.52.27.0.255#2', 'V', 'R', '0.1', NULL, '1', 'double-long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.43.01', '300', '电能表编程总次数发生', 'E2000043', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.41.01', '300', '电能表A、B、C相失流总次数发生', 'E2000041', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.**********.13.0.0.0.0.0.96.54.71.255.0.0.1', '100', 'Meter status - Battery status', '1#0.0.96.54.71.255#2', '1', 'RW', '1', NULL, '0', 'long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.43.*********.0.1', '100', 'Phase 2 import reactive energy T1', '3#********.1.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.26.*********.0.*********.0.*********.72.0', '100', 'L1 Average voltage', '3#*********.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '1');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.1.20.20.7.6.0.0.*********.72.19', '100', 'L2 Instant reactive power(import + export)', '3#1.0.148.7.0.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.3.1.0.0.2', '300', 'The time of voltage lose L1 [Unit: Min]', 'Min', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.*******.0.15.9.0.102.0', '100', 'last 2 month active energy |+A| + |-A| consumption', '3#1.0.15.9.0.102#2', '1', 'RW', '0.01', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.72.24.0.*********.0.*********.72.0', '100', 'L3 average voltage', '3#1.0.72.24.0.255#2', 'Wh', 'R', '0.1', '2001', '1', 'long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.1.248', '100', 'Zero line interference end', '7#*********.1.255#2#248', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.52.51.128.*********.0.*********.72.0', '100', 'L2-L3 Min voltage', '3#1.0.52.51.128.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.11.01', '300', 'B相电流过流发生', 'E2000011', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.***********', '300', 'A相闪变越限发生', 'E2000048', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********', '100', 'HES IP and port', '29#*******.0.255#6', NULL, 'RW', '1', NULL, NULL, 'special', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.30', '200', 'Capacitor switching self-locking recovery', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.3.53.0.*********.0.*********.72.0', '100', 'Total Max Reactive Power', '3#********.0.255#2', 'Wh', 'R', '1', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.*******9.130.7.255.0', '100', 'Last recharge amount in EGP', '1#**********.7.255#2', '1', 'RW', '1', NULL, '0', 'float32', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.*******.0.15.9.0.107.0', '100', 'last 7 month active energy |+A| + |-A| consumption', '3#1.0.15.9.0.107#2', '1', 'RW', '0.01', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.72.53.128.*********.0.*********.72.0', '100', 'L3-L1 Max voltage', '3#1.0.72.53.128.255#2', 'Wh', 'R', '0.01', '2001', '1', 'long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.3D.01', '300', '抄表失败发生', 'E200003D', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.0.20', '100', 'Friendly special day', '11#0.0.11.0.2.255#2', NULL, 'RW', '1', NULL, NULL, 'special', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.*********.0.*********.0.0.*******.38.0', '100', 'Average A-phase voltage', '3#*********.0.255#2', 'V', 'R', '1', NULL, '1', 'double-long-unsigned', '4', '1');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.1.38', '100', 'Open meter box', '7#*********.1.255#2#38', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.3.11.0.0.4', '300', 'The time of current lose L2 [Unit: Min]', 'Min', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.04.00', '300', '电流不平衡恢复', 'E2000004', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.3E.01', '300', '电能表时钟异常发生', 'E200003E', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.13.27.0.*********.0.*********.72.0', '100', 'Average power factor of inteval rcd', '3#1.0.13.27.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.50.00', '300', '阶梯表编程次数更改恢复', 'E2000050', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.43.8.3.255.0.0.1', '100', 'Phase 2 import reactive energy T3', '3#********.3.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.1.1.12.0.0.0.0.6.*********.72.1', '100', 'Active energy import (+A) rate 6 (Monthly) [Unit: kWh]', '3#1.0.1.8.6.255#2', 'Wh', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.44.27.0.*********.0.*********.72.0', '100', 'L2 Average export reactive power', '3#1.0.44.27.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.05.06.04.FF', '300', '日冻结反向无功电能数据块', '050604FF', 'kvarh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.10.10', '200', 'AC Sampling Device Configuration Parameter', '0A#10', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.18.00', '300', 'C相电压失压恢复', 'E2000018', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.10.1.12.0.0.94.91.13.*********.73.1', '100', 'billing power on duration [Unit: MIN]', '3#0.0.94.91.13.255#2', 'Minutes', 'R', '1', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.12.19.10', '100', 'Front cover open', '7#*********.1.255#2#232', '1', 'RW', '1', NULL, '0', NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.25.1.12.0.0.0.0.2.*********.72.0', '100', 'Active energy export (-A) L2 rate 2 [Unit: kWh]', '3#1.0.42.8.2.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.8.*******.8.1.0.15.8.8.255.0.224.3.38.2', '100', 'Rate 8 total active energy', '3#1.0.15.8.8.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.********.0.0.0.0.0.2.32.0.29.0', '100', 'Instantaneous fundamental wave voltage L3 [Unit: V]', 'V', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.********.0.0.0.*******28.0.29.0', '100', 'Instantaneous fundamental wave voltage L1 [Unit: V]', 'V', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.62.*********.0.1', '100', 'Phase 3 export active energy T1', '3#********.1.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********', '200', 'IP and Port of Master Station', '0A#3', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.*********.*******.*********.73.0', '100', 'Reactive energy import (+R) L2 rate 1 [Unit: kVarh]', '3#********.1.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.***********', '300', '电表时钟-年月日', '04000101', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.*******.*********.73.1', '100', 'Cumulative import apparent energy,+E,T8', '3#*******.8.255#2', 'VAh', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.04.01', '300', '电流不平衡发生', 'E2000004', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.26.*******.0.4.**********.0.1', '100', 'Average export reactive power', '3#1.0.4.27.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.3A.01', '300', '月通信流量越限发生', 'E200003A', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.28', '100', 'Phase 1 export active energy T2', '3#1.0.22.8.2.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.62.8.0.255.0.0.1', '100', 'Phase 3 export active energy', '3#********.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.31.01', '300', '最大需量手动复零发生', 'E2000031', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.05.06.02.FF', '300', '日冻结反向有功电能数据块', '050602FF', 'kWh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.26.1.12.0.0.0.0.2.*********.72.0', '100', 'Active energy export (-A) L3 rate 2 [Unit: kWh]', '3#********.2.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.63.8.4.255.0.0.1', '100', 'Phase 3 import reactive energy T4', '3#1.0.63.8.4.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('111111', '100', '111111', '1111', 'Wh', 'R', '0.1', '2000', NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.42.8.0.255.0.0.1', '100', 'Phase 2 export active energy', '3#1.0.42.8.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.0.1.12.0.0.1.2.3.*********.72.2', '100', 'Daily increase active energy', '3#1.0.15.19.0.255#2', NULL, 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.26.0.0.0.********0.3.255.0.0.1', '100', 'Load profile for period 3', '1#********0.3.255#2', '1', 'RW', '1', NULL, '0', 'unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.3F.01', '300', '电能表校时失败发生', 'E200003F', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.********.0.0.0.0.32.7.128.0.29.0', '100', 'Total harmonic of phase A voltage', '3#1.0.32.7.124.255#2', '%', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.********.0.0.*******.128.0.29.0', '100', 'Instantaneous line voltage L1/L2 [Unit: V]', 'V', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.0.1.12.0.0.0.0.3.*********.72.1', '100', 'Monthly increase active energy tariff 3', '3#1.0.15.9.3.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.8.*******.8.1.0.15.8.7.255.0.224.3.38.2', '100', 'Rate 7 total active energy', '3#1.0.15.8.7.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.8.*******.8.1.0.15.8.5.255.0.224.3.38.2', '100', 'Rate 5 total active energy', '3#1.0.15.8.5.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.4.325', '100', 'Current unbalanced by Neutral line start', '7#*********.4.255#2#325', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.72.38.1', '100', 'L3 maximum value during over voltage', '3#1.0.72.38.0.255#2', 'V', 'R', '0.1', NULL, NULL, 'long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.********.0.0.*******.64.0.29.0', '100', 'Instantaneous line voltage L2/L3 [Unit: V]', 'V', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.30.1.12.0.*******.*********.73.0', '100', 'Reactive energy export (-R) L1 rate 1 [Unit: kVarh]', '3#1.0.24.8.1.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.00.07.FF.01', '300', '月冻结第三象限无功电能数据块', '0007FF01', 'kvarh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.5.1', '200', 'Remotely Switch Off', '05#1', NULL, 'W', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.32', '200', 'Message authentication error', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.0.1.12.0.0.0.0.2.*********.72.1', '100', 'Monthly increase active energy tariff 2', '3#1.0.15.9.2.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.********.**********.*********.72.7', '100', 'Average voltage L3', '3#1.0.72.27.0.255#2', 'V', 'R', '0.1', NULL, '1', 'double-long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.2F.01', '300', '电能表通讯失败发生', 'E200002F', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.53', '100', 'Phase 2 import reactive energy T2', '3#********.2.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.36', '200', 'Energy control alarm', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.2.0', '100', 'B Phase - Voltage missing occurrence', '20', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.**********.13.0.0.0.0.0.96.54.29.255.0.0.1', '100', 'Activity type', '1#0.0.96.54.29.255#2', '1', 'RW', '0.001', NULL, '0', 'unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.3.5.0', '100', 'Voltage Sag Phase L1 restoration', '0.4.25.9.0.255#3#50', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.66', '100', 'Phase 1 export reactive energy', '3#1.0.24.8.0.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.58', '100', 'Phase 3 import reactive energy T2', '3#1.0.63.8.2.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.**********.13.*******.0.15.9.0.255.0.0.1', '100', 'CurrentMonthConsumsion in kWh', '3#1.0.15.9.0.255#2', '1', 'RW', '0.01', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.12.2', '200', 'DCU Calendar and Clock', '0C#2', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.2E.01', '300', '电能表停走发生', 'E200002E', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.81.0', '100', 'Influence of permanent magnet or ac/dc electromagnet occurrence', '810', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.1.8.0.0.0.0.5.1.0.224.3.38.1', '100', 'Maximum Demand Register 1 -Active energy import(+A) rate 5 occurred time (Monthly)', '4#1.0.1.6.5.255#5', NULL, 'R', '1', NULL, '1', 'date-time', '12', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.64.8.0.255.0.0.1', '100', 'Phase 3 export reactive energy', '3#********.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.42.8.4.255.0.0.1', '100', 'Phase 2 export active energy T4', '3#1.0.42.8.4.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.3.1.1.1.12.0.0.0.0.0.*********.72.0', '100', 'Daily Active energy export (+A) [Unit: kWh]', '3#1.0.2.19.0.255#2', 'Wh', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.34', '100', 'Phase 2 export active energy T3', '3#1.0.42.8.3.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('30*******6', '200', 'Phase sequence recovery', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.11.00', '300', 'B相电流过流恢复', 'E2000011', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.**********.13.0.0.0.0.0.96.54.72.255.0.0.1', '100', 'Non cleared tampers', '1#0.0.96.54.72.255#2', '1', 'RW', '1', NULL, '0', 'long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.31.1.12.0.0.0.0.3.*********.73.0', '100', 'Reactive energy export (-R) L2 rate 3 [Unit: kVarh]', '3#1.0.44.8.3.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.*********.*******.*********.73.0', '100', 'Reactive energy import (+R) L2 rate 1 [Unit: kVarh]', '3#********.1.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.70', '100', 'Phase 1 export reactive energy T4', '3#1.0.24.8.4.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.22.1.12.0.0.0.0.0.*********.72.0', '100', 'Active energy import (+A) L2 [Unit: kWh]', '3#1.********.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.170', '200', 'Forward Reactive Energy Data (Total/Tariff 1-M)(Monthly Frozen)', '0D#170', NULL, 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.05.06.06.FF', '300', '日冻结第二象限无功电能数据块', '050606FF', 'kvarh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.1.57', '100', 'Battery voltage low log ', '7#*********.8.255#2#3', '1', 'RW', '1', NULL, '0', NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.00.07.FF.00', '300', '第三象限无功电能数据块', '0007FF00', 'kvarh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.18.*******.0.1.**********.0.1', '100', 'Average Sum of active power+', '3#1.0.1.5.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.23.*********.0.1', '100', 'Phase 1 import reactive energy T2', '3#1.0.23.8.2.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.1.8.1', '100', 'Program memory error occurrence', '0.4.25.9.0.255#1#81', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.4D.01', '300', '表端钮盒开启告警发生', 'E200004D', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.0E.00', '300', 'B相潮流反向恢复', 'E200000E', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.1.1.12.0.0.0.0.0.*********.19.0', '100', 'Daily increase import reactive energy', '3#1.0.3.19.0.255#2', 'Wh', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.8.0.0.0.4.11.15.29.0.*********.224.3.38.8', '100', 'Average power factor', '1#0.0.96.55.0.101#2', NULL, 'R', '1', NULL, NULL, 'float32', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.26.1.12.0.0.0.0.3.*********.72.0', '100', 'Active energy export (-A) L3 rate 3 [Unit: kWh]', '3#********.3.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.00.08.FF.00', '300', '第四象限无功电能数据块', '0008FF00', 'kvarh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.17', '100', 'Heartbeat cycle', '1#*********.14.255#2', NULL, 'RW', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.23.1.12.0.*******.*********.72.0', '100', 'Active energy import (+A) L3 rate 1 [Unit: kWh]', '3#********.1.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.2B.01', '300', '剩余电费不足发生', 'E200002B', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.41.8.3.255.0.0.1', '100', 'Phase 2 import active energy T3', '3#********.3.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.3', '100', 'Read meter List', '1#0.0.96.81.0.255#2', NULL, 'R', '1', NULL, NULL, 'special', NULL, '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.2.1.8.0.0.9.6.8.*********.38.1', '100', 'Import apparent maximum demand,t8', '4#1.0.9.6.8.255#2', 'VA', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.1.0.91.27.0.*********.0.0.*******.38.0', '100', 'Average zero line current', '3#1.0.91.27.0.255#2', 'A', 'R', '0.01', '1000', '1', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.11', '100', 'Phase 2 import active energy', '3#1.********.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.52.01', '300', '非法插卡告警发生', 'E2000052', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.*******9.133.0.105.0', '100', 'last 5 month credit consumption ', '1#0.**********.105#2', '0.01', 'RW', '1', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.52.53.0.*********.0.*********.72.0', '100', 'L2 max voltage', '3#1.0.52.53.0.255#2', 'Wh', 'R', '0.1', '2001', '1', 'long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.62.53.0.*********.0.*********.72.0', '100', 'L3 max export active power', '3#1.0.62.53.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('38.0.0.96', '100', 'Send param token', '1#**********.6.255#2', NULL, 'RW', '1', NULL, NULL, 'octet-string', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******.********.*********.**********.0.1', '100', 'Phase 2 import active energy', '3#1.********.255#2', NULL, NULL, '0.001', NULL, '1', 'double-long-unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.23.1.12.0.0.0.0.3.*********.72.0', '100', 'Active energy import (+A) L3 rate 3 [Unit: kWh]', '3#********.3.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.30.01', '300', '差动告警发生', 'E2000030', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.1.0.71.27.0.*********.0.0.*******.38.0', '100', 'Average C-phase current', '3#1.0.71.27.0.255#2', 'A', 'R', '0.001', '1000', '1', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.61.8.3.255.0.0.1', '100', 'Phase 3 import active energy T3', '3#********.3.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.4.0.0.0.0.0.0.7.32.0.5.0', '100', 'Average harmonic in L3 Instant current', '3#1.0.71.27.124.255#2', '%', 'R', '0.01', NULL, NULL, 'long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.1.1', '200', 'Hardware Initialization', '01#1', NULL, 'W', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******0.2********.0.0.0.0.*********.72.0', '100', 'Block export apparent energy,-E', '3#1.0.10.29.0.255#2', 'kWh', NULL, '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.12.134', '200', 'Current Reactive Energy Data (2nd Quadrant)(Total/Tariff 1-M)', '0C#134', NULL, 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.2********.0.0.0.3.*********.73.0', '100', 'Reactive energy import (+R) L3 rate 3 [Unit: kVarh]', '3#1.0.63.8.3.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.4.0.0.0.0.0.31.7.128.0.5.0', '100', 'Total harmonic of phase A current', '3#1.0.31.7.124.255#2', '%', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.40.00', '300', '电能表A、B、C相失压总次数恢复', 'E2000040', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.40', '100', 'Phase 3 export active energy T4', '3#********.4.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.26.*******.0.1.5.0.255.0.0.1', '100', 'Average Sum of active power+', '3#1.0.1.5.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.3', '200', 'Dcu parameter missed', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.22.1.12.0.*******.*********.72.0', '100', 'Active energy import (+A) L2 rate 1 [Unit: kWh]', '3#********.1.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.1.0.21.7.0.255.0.*******28.0.29.1', '100', 'Instant import active power L1 sy', '3#1.0.21.7.0.255#2', NULL, 'R', '1', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.22.01', '300', 'C相电压畸变发生', 'E2000022', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.0.9.8.6.*********.73.1', '100', 'Cumulative import apparent energy,+E,T6', '3#*******.6.255#2', 'VAh', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.92', '200', 'Phase A Current Profile', '0D#92', NULL, 'R', '0.001', '2003', NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.9.1', '100', 'Current balance restoration', '91', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.63.*********.0.1', '100', 'Phase 3 import reactive energy T1', '3#1.0.63.8.1.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.29', '100', 'GPRS IMEI Serial Number', '1#*********.15.255#2', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.56', '100', 'Phase 3 import reactive energy', '3#1.0.63.8.0.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.13', '100', 'Parameter reset (except communication)', '1#0.0.96.51.6.255#3', NULL, 'A', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.24.*********.0.1', '100', 'Phase 1 export reactive energy T2', '3#1.0.24.8.2.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.37.0.********.0.255.224.3.38.1', '100', 'Instantaneous active power(|+A|+|-A|) [Unit: kW]', '3#********.0.255#2', NULL, 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.8.*******.8.0.0.0.0.0.*********.38.7', '100', 'Import apparent maximum demand', '4#1.0.9.6.0.255#2', NULL, 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.5.2.12.*******.4.*********.73.0', '100', 'Combined reactive energy import 1 (+A) [Unit: kVarh]', 'Varh', NULL, NULL, '0.01', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.23', '200', 'Harmonic over threshold occurrence', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.24.8.3.255.0.0.1', '100', 'Phase 1 export reactive energy T3', '3#1.0.24.8.3.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.63.8.0.255.0.0.1', '100', 'Phase 3 import reactive energy', '3#1.0.63.8.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.12.130', '200', 'Current Forward Reactive Energy Data (Combined Reactive 1)(Total/Tariff 1-M)', '0C#130', NULL, 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.30.00', '300', '差动告警恢复', 'E2000030', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.1A.00', '300', 'A相电压过压恢复', 'E200001A', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.24.8.3.255.0.0.1', '100', 'Phase 1 export reactive energy T3', '3#1.0.24.8.3.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.62.8.4.255.0.0.1', '100', 'Phase 3 export active energy T4', '3#********.4.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.**********.13.0.0.0.0.0.96.54.69.255.0.0.1', '100', 'Tariff Id', '1#0.0.96.54.69.255#2', '1', 'RW', '0.001', NULL, '0', 'octet-string', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.29.00', '300', '负荷过载恢复', 'E2000029', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.4.0.0.0.0.0.0.3.64.0.5.0', '300', 'Instantaneous phase angle current L2 [Unit: A]', 'A', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.62.*********.0.1', '100', 'Phase 3 export active energy T2', '3#********.2.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.18.*******.********.255.0.0.1', '100', 'Phase 2 import active energy', '3#1.********.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.38', '100', 'Phase 3 export active energy T2', '3#********.2.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******', '100', 'Measurement Profile Interval', NULL, NULL, NULL, '1', NULL, '1', NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.26', '200', 'DC analog quantity over threshold recovery', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.1.0.53.27.0.*********.0.0.*******.38.0', '100', 'Average phase B power factor', '3#1.0.53.27.0.255#2', 'Wh', 'R', '0.001', '1000', '1', 'double-long', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.30.1.12.0.0.0.0.3.*********.73.0', '100', 'Reactive energy export (-R) L1 rate 3 [Unit: kVarh]', '3#1.0.24.8.3.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.0.22', '100', 'Infrared baud rate', '23#0.0.22.0.0.255#2', 'B', NULL, '1', NULL, NULL, 'enum', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('20.8.7.2', '100', 'Number of power failures in any phase', '1#0.0.96.7.21.255#2', NULL, 'R', '1', NULL, NULL, 'long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.18.*******.0.4.**********.0.1', '100', 'Average Sum of reactive power-', '3#1.0.4.27.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.84.1', '100', 'Meter load disconnected/Meter load connected restoration', '841', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.1.0.21.27.0.*********.0.0.*******.38.0', '100', 'Average positive active power of phase A', '3#1.0.21.27.0.255#2', 'Wh', 'R', '0.001', '1000', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.8.*******.0.1.0.0.*********.224.3.38.5', '100', 'Recharge credit in the current month', '1#0.0.19.129.8.255#2', NULL, 'R', '1', NULL, NULL, 'float32', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.44.8.4.255.0.0.1', '100', 'Phase 2 export reactive energy T4', '3#1.0.44.8.4.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.**********.13.0.0.0.********.16.255.0.0.1', '100', 'Last 3 techincian IDs', '1#********.16.255#2', '1', 'RW', '0.001', NULL, '0', 'octet-string', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.0.0.21.0.0.7.0.0.0.0.0.*********.38.0', '100', 'L1 instant active power import', '3#1.0.21.7.0.255#2', 'W', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.0.0.21.0.0.7.0.0.0.0.0.*********.38.7', '100', 'L1 instant active power import', '3#1.0.21.7.0.255#2', 'W', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.128.8.0.*********.0.*********.72.0', '100', 'Cumulative apparent energy', '3#1.0.128.8.0.255#2', NULL, 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.2.20.20.09.08.18.14.2.0.0.0.0.0.*******0', '100', 'Instantaneous reactive import power (+R) L3', '3#1.0.63.7.0.255#2', NULL, 'RW', '0.001', NULL, NULL, 'double-long-unsigned', '12', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.58', '200', 'Output control circuit state changed', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.5.2.12.*******.2.*********.73.0', '100', 'Combined reactive energy import 1 (+A) [Unit: kVarh]', 'Varh', NULL, NULL, '0.01', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40*******0.1', '100', 'Current bypass/short restoration', '101', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.0.0.41.0.0.7.0.0.0.0.0.*********.38.8', '100', 'L2 instant active power impor', '3#1.0.41.7.0.255#2', 'W', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.2.1.8.0.0.9.6.7.*********.38.1', '100', 'Import apparent maximum demand,t7', '4#1.0.9.6.7.255#2', 'VA', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.41', '200', 'Apparent power over threshold occurrence', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.1.1.0', '100', 'Battery replace restoration', '0.4.25.9.0.255#1#10', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.52.53.128.*********.0.*********.72.0', '100', 'L2-L3 Max voltage', '3#1.0.52.53.128.255#2', 'Wh', 'R', '0.01', '2001', '1', 'long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.25', '100', 'Event Configuration', '1#0.0.96.51.10.255#2', NULL, 'RW', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.21.00', '300', 'B相电压畸变恢复', 'E2000021', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.99.97.95', '100', 'Phase a power failure occurred', '7#1.0.99.97.0.255#2#95', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.25.1.12.0.*******.*********.72.0', '100', 'Active energy export (-A) L2 rate 1 [Unit: kWh]', '3#1.0.42.8.1.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.24.1.12.0.0.0.0.4.*********.72.0', '100', 'Active energy export (-A) L1 rate 4 [Unit: kWh]', '3#1.0.22.8.4.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.********.**********.*********.72.8', '100', 'Average active power import (+A)', '3#1.0.1.27.0.255#2', 'Wh', 'R', '0.001', '1000', '1', 'double-long', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.47.00', '300', '频率偏差越限恢复', 'E2000047', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.32.1.12.0.0.0.0.3.*********.73.0', '100', 'Reactive energy export (-R) L3 rate 3 [Unit: kVarh]', '3#********.3.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.5.31', '200', 'Time Synchronization Command', '05#31', NULL, 'W', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.**********.13.0.0.0.0.0.96.54.70.255.0.0.1', '100', 'Total number of charge transactions', '1#0.0.96.54.70.255#2', '1', 'RW', '1', NULL, '0', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.2.51.0.*********.0.*********.72.0', '100', 'min export active power', '3#1.0.2.51.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.********.0.0.0.0.0.7.64.0.29.0', '100', 'Average harmonic in L2 Instant voltage', '3#1.0.52.27.124.255#2', '%', 'R', '0.01', NULL, NULL, 'long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.55', '100', 'Phase 2 import reactive energy T4', '3#********.4.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('44.4.4.1', '100', 'Meter Status', '1#0.0.96.5.0.255#2', NULL, 'RW', '1', NULL, NULL, 'long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.1.8.0.0.0.0.8.1.0.224.3.38.1', '100', 'Maximum Demand Register 1 -Active energy import(+A) rate 8 occurred time (Monthly)', '4#1.0.1.6.8.255#5', NULL, 'R', '1', NULL, '1', 'date-time', '12', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('44.4.4.10', '100', 'Tamper alarm reset', '9#0.0.10.0.0.255#1', NULL, 'A', '1', NULL, NULL, 'long-unsigned', '16', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.0D.01', '300', 'A相潮流反向发生', 'E200000D', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.6.76', '100', 'Under voltage start', '7#*********.6.255#2#76', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.********.**********.*********.72.1', '100', 'Average current L1', '3#1.0.31.27.0.255#2', 'A', 'R', '0.01', '1000', '1', 'double-long-unsigned', '4', '1');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.4A.00', '300', 'C相闪变越限恢复', 'E200004A', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.8.0.77', '100', 'Disconnector Control  Log', '7#*********.2.255#2', '1', 'RW', '1', NULL, '0', 'double_long_unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.19', '100', 'PLC channel send times', '1#*********.27.255#2', NULL, 'RW', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.1.56', '100', 'Module cover open log', '7#*********.7.255#2#2', '1', 'RW', '1', NULL, '0', NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.10.01', '300', 'A相电流过流发生', 'E2000010', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.05.06.01.FF', '300', '日冻结正向有功电能数据块', '050601FF', 'kWh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.26.*******.0.6.**********.0.1', '100', 'Average reactive power of quadrant 2', '3#1.0.6.27.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.39', '100', 'Phase 3 export active energy T3', '3#********.3.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('6.36.0.4', '100', 'Profile status 3', '1#********0.3.255#2', NULL, 'R', '1', NULL, NULL, 'unsigned', '1', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.1.312', '100', 'Power reversal end', '7#*********.1.255#2#312', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.23.8.0.255.0.0.1', '100', 'Phase 1 import reactive energy', '3#1.0.23.8.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.79', '100', 'Phase 3 export reactive energy T3', '3#********.3.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.00.04.FF.00', '300', '反向无功电能数据块', '0004FF00', 'kvarh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.23.27.0.*********.0.*********.72.0', '100', 'L1 Average import reactive power', '3#1.0.23.27.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.12', '100', 'Data base initialization', '1#0.0.96.51.6.255#2', NULL, 'A', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.32.1.12.0.*******.*********.73.0', '100', 'Reactive energy export (-R) L3 rate 1 [Unit: kVarh]', '3#********.1.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.87', '100', 'Increment in billing counter (Manual/MRI reset)', '871', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.26.*******.0.2.**********.0.1', '100', 'Average Sum of active power-', '3#1.0.2.27.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.02.01.FF.00', '300', '三相电压数据块', '0201FF00', 'V', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.01.02.FF.01', '300', '月冻结反向有功最大需量及发生时间数据块', '0102FF01', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.4.0.0.0.0.0.*********.5.0', '100', 'Instantaneous average current L1', '3#1.0.31.27.0.255#2', 'W', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '1');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.20', '200', 'Meter fault recovery', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.43.8.0.255.0.0.1', '100', 'Phase 2 import reactive energy', '3#********.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.26.133.98', '100', 'Current reversal end', '7#*********.4.255#2#253', '1', 'RW', '1', NULL, '0', NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.0.********.14.255.0', '100', 'last 1 times techincian ID', '1#********.14.255#2', '1', 'RW', '1', NULL, '0', 'octet-string', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.37.0.0.0.0.*******28.3.38.0', '100', 'Instantaneous active power L1 (|+A|+|-A|) [Unit: kW]', '3#1.0.35.7.0.255#2', 'W', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.21.*********.0.1', '100', 'Phase 1 import active energy T1', '3#********.1.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******.0.128.8.0.*********.**********.0.1', '100', 'Total reactive energy', '3#1.0.128.8.0.255#2', NULL, NULL, '0.001', NULL, '1', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.*******', '100', 'R Phase - Voltage missing restoration', '01', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.2D.01', '300', '电能表飞走发生', 'E200002D', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.72.53.0.*********.*********.72.0', '100', 'L3 max voltage', '3#1.0.72.53.0.255#2', 'Wh', 'R', '0.1', '2001', '1', 'long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.61.27.0.*********.0.*********.72.0', '100', 'L3 Average import active power', '3#1.0.61.27.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.**********.13.0.0.0.********.15.255.0.0.1', '100', 'Last 2 techincian IDs', '1#********.15.255#2', '1', 'RW', '0.001', NULL, '0', 'octet-string', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.13', '100', 'Phase 2 import active energy T2', '3#********.2.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.12.19.11', '100', 'Missing neutral', '7#*********.1.255#2#89', '1', 'RW', '1', NULL, '0', NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.3.11.0.0.6', '300', 'The time of current lose L3 [Unit: Min]', 'Min', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.25.1.12.0.0.0.0.3.*********.72.0', '100', 'Active energy export (-A) L2 rate 3 [Unit: kWh]', '3#1.0.42.8.3.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.1.1.4.0.0.0.0.*******28.0.5.0', '100', 'Instantaneous average current L1', '3#1.0.31.27.0.255#2', 'Wh', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '1');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.2.27.0.*********.0.*********.72.0', '100', 'Average export active power', '3#1.0.2.27.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.0.73.6.1.1.8.0.0.*******.0.224.3.63.0', '100', 'Import reactive demand occurred time', '5#1.0.3.4.0.255#5', NULL, 'R', '1', NULL, NULL, 'date-time', '12', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.8', '100', 'GRPS network parameter', '1#********.130.255#2', NULL, 'RW', '1', NULL, NULL, 'special', NULL, '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.23.*********.0.1', '100', 'Phase 1 import reactive energy T1', '3#1.0.23.8.1.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.21.1.12.0.*******.*********.72.0', '100', 'Active energy import (+A) L1 rate 1 [Unit: kWh]', '3#********.1.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.26.1.12.0.*******.*********.72.0', '100', 'Active energy export (-A) L3 rate 1 [Unit: kWh]', '3#********.1.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.********.0.0.2.3.*********.73.0', '100', 'Combined reactive energy import 2 (+A) [Unit: kVarh]', 'Varh', NULL, NULL, '0.01', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.********.**********.*********.72.2', '100', 'Average current L2', '3#1.0.51.27.0.255#2', 'A', 'R', '0.001', '1000', '1', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.24.1.12.0.0.0.0.2.*********.72.0', '100', 'Active energy export (-A) L1 rate 2 [Unit: kWh]', '3#1.0.22.8.2.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.36', '100', 'PLC Modem Version', '1#*********.24.255#2', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.*******.0.15.9.0.101.0', '100', 'last 1 month active energy |+A| + |-A| consumption', '3#1.0.15.9.0.101#2', '1', 'RW', '0.01', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.49', '100', 'Phase 1 import reactive energy T3', '3#1.0.23.8.3.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.12.135', '200', 'Current Reactive Energy Data (3rd Quadrant)(Total/Tariff 1-M)', '0C#135', NULL, 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.35.00', '300', '电能表编程时间更改恢复', 'E2000035', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.23.0.50', '100', 'Meter Register', NULL, NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.26.1.12.0.0.0.0.2.*********.72.0', '100', 'Active energy export (-A) L3 rate 2 [Unit: kWh]', '3#********.2.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.22.8.4.255.0.0.1', '100', 'Phase 1 export active energy T4', '3#1.0.22.8.4.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.5.2.12.*******.3.*********.73.0', '100', 'Combined reactive energy import 1 (+A) [Unit: kVarh]', 'Varh', NULL, NULL, '0.01', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.12.136', '200', 'Current Reactive Energy Data (4rd Quadrant)(Total/Tariff 1-M)', '0C#136', NULL, 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.29', '100', 'Phase 1 export active energy T3', '3#1.0.22.8.3.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.31', '200', 'Write electricity purchasing parameter', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.44.00', '300', 'A相电压偏差越限恢复', 'E2000044', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.21.51.0.*********.0.*********.72.0', '100', 'L1 min import active power', '3#1.0.21.51.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.61.00', '300', '漏电监测告警恢复', 'E2000061', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.35.01', '300', '电能表编程时间更改发生', 'E2000035', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.64.8.4.255.0.0.1', '100', 'Phase 3 export reactive energy T4', '3#********.4.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.42.51.0.*********.0.*********.72.0', '100', 'L2 min export active power', '3#1.0.42.51.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.0.0.12.0.1.1.2.3.0.0.*******.0.65.18', '100', 'Power down count', '1#0.0.96.7.0.255#2', NULL, 'R', '1', NULL, NULL, 'long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.17', '100', 'Phase 3 import active energy T1', '3#********.1.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.**********.13.0.0.0.0.0.96.54.69.255.0.0.1', '100', 'Tariff Id', '1#0.0.96.54.69.255#2', '1', 'RW', '0.001', NULL, '0', 'octet-string', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.**********.13.*******.0.15.9.0.255.0.0.1', '100', 'CurrentMonthConsumsion in kWh', '3#1.0.15.9.0.255#2', '1', 'RW', '0.01', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.2.1.8.0.0.9.6.8.1.0.224.3.38.1', '100', 'Import apparent maximum demand,t8,occured time', '4#1.0.9.6.8.255#5', NULL, 'R', '1', NULL, '1', 'date-time', '12', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.65', '200', 'Time synchronization', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.122.127.3.1.0.13.26.0.255.2', '100', 'Last maximum value of power factor', '3#1.0.13.26.0.255#2', NULL, 'R', '0.01', NULL, NULL, 'long-unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.3.0', '100', 'Over voltage in any phase occurrence', '30', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.25', '200', 'DC analog quantity over threshold occurrence', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.12.132', '200', 'Current Reverse Reactive Energy Data (Combined Reactive 1)(Total/Tariff 1-M)', '0C#132', NULL, 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.3.1.0.0.6', '300', 'The time of voltage lose L3 [Unit: Min]', 'Min', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.22.1.12.0.*******.*********.72.0', '100', 'Active energy import (+A) L2 rate 1 [Unit: kWh]', '3#********.1.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.02.02.01.00', '300', 'A相电流', '02020100', 'A', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.30.1.12.0.0.0.0.0.*********.73.0', '100', 'Reactive energy export (-R) L1 [Unit: kVarh]', '3#1.0.24.8.0.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.**********.13.0.0.0.********.14.255.0.0.1', '100', 'Last 1 techincian IDs', '1#********.14.255#2', '1', 'RW', '0.001', NULL, '0', 'octet-string', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.00.05.FF.00', '300', '第一象限无功电能数据块', '0005FF00', 'kvarh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.26.101.223', '100', 'Under voltage (voltage SAG) L1', '7#*********.3.255#2#76', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.3.1.0.0.5', '300', 'The number of voltage lose L3', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.37.*******.0.*********.38.0', '100', 'Instantaneous active power', '3#1.0.16.7.0.255#2', 'kW', NULL, '0.001', NULL, NULL, 'double-long', '2', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.12.162', '200', 'Calendar Clock', '0C#162', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.********.255.0.0.1', '100', 'Phase 2 import active energy', '3#1.********.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********', '100', 'Current reverse end', '7#*********.1.255#2#107', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********', '100', 'FTP IP and port', '29#*******.0.255#6', NULL, 'RW', '1', NULL, NULL, 'special', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.16', '100', 'Phase 3 import active energy', '3#********.0.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.13', '100', 'Phase 2 import active energy T2', '3#********.2.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.***********', '300', 'C相电压失压发生', 'E2000018', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.63.8.3.255.0.0.1', '100', 'Phase 3 import reactive energy T3', '3#1.0.63.8.3.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.36', '100', 'Phase 3 export active energy', '3#********.0.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.42.01', '300', '电能表A、B、C相潮流反向总次数发生', 'E2000042', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.42.*********.0.1', '100', 'Phase 2 export active energy T1', '3#1.0.42.8.1.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.62', '200', 'Cover reading failed recovery', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.1.39', '100', 'Close meter box', '7#*********.1.255#2#39', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.********.0.*******.0.224.3.63.0', '100', 'Export apparent maximum demand occured time', '4#********.0.255#5', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.51.00', '300', '异常插卡告警恢复', 'E2000051', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.1.0.31.27.0.*********.0.0.*******.38.0', '100', 'Average A-phase current', '3#1.0.31.27.0.255#2', 'A', 'R', '0.01', '1000', '1', 'double-long-unsigned', '4', '1');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.71', '100', 'Phase 2 export reactive energy', '3#1.0.44.8.0.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.27.1.12.0.0.0.0.3.*********.73.0', '100', 'Reactive energy import (+R) L1 rate 3 [Unit: kVarh]', '3#1.0.23.8.3.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.32.53.0.*********.0.*********.72.0', '100', 'L1 max voltage', '3#1.0.32.53.0.255#2', 'Wh', 'R', '0.1', '2001', '1', 'long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.64.27.0.*********.0.*********.72.0', '100', 'L3 Average export reactive power', '3#1.0.64.27.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.18.*******.0.61.8.0.255.0.0.1', '100', 'Phase 3 import active energy', '3#********.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.21.1.12.0.0.0.0.3.*********.72.0', '100', 'Active energy import (+A) L1 rate 3 [Unit: kWh]', '3#********.3.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.2B.00', '300', '剩余电费不足恢复', 'E200002B', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.3E.00', '300', '电能表时钟异常恢复', 'E200003E', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.1.0.1.27.0.*********.0.0.*******.38.0', '100', 'Average combined positive active power', '3#1.0.1.27.0.255#2', 'Wh', 'R', '0.001', '1000', '1', 'double-long', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.62.*********.0.1', '100', 'Phase 3 export active energy T2', '3#********.2.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.129.8.0.*********.0.*********.72.0', '100', 'Total apparent energy of the month  [Unit: kWh]', '3#1.0.129.8.0.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.15.00', '300', 'C相电流失流恢复', 'E2000015', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.09.00', '300', 'C相TA二次侧短路恢复', 'E2000009', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('6.36.1.6', '100', 'Date', '1#1.0.0.9.2.255#2', NULL, 'R', '1', NULL, NULL, 'date', '5', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.13.01', '300', 'A相电流失流发生', 'E2000013', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.77', '100', 'Phase 3 export reactive energy T1', '3#********.1.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.32.53.128.*********.0.*********.72.0', '100', 'L1-L2 Max voltage', '3#1.0.32.53.128.255#2', 'Wh', 'R', '0.01', '2001', '1', 'long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.1.1.12.20.20.9.0.0.0.0.255.224.72.21', '100', 'Total reactive maximum demand,total', '4#1.0.128.6.0.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.3.11.0.0.1', '300', 'The number of current lose L1', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.14.00', '300', 'B相电流失流恢复', 'E2000014', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.44.51.0.*********.0.*********.72.0', '100', 'L2 min export reactive power', '3#1.0.44.51.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.37', '100', 'Phase 3 export active energy T1', '3#********.1.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.4.0.0.0.0.0.0.2.64.0.5.0', '300', 'Instantaneous fundamental wave current L2 [Unit: A]', 'A', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.43.27.0.*********.0.*********.72.0', '100', 'L2 Average import reactive power', '3#1.0.43.27.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('20.8.7.0', '100', 'Number of meter cover open', '1#0.0.96.20.0.255#2', NULL, 'R', '1', NULL, NULL, 'long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.27.1.12.0.0.0.0.2.*********.73.0', '100', 'Reactive energy import (+R) L1 rate 2 [Unit: kVarh]', '3#1.0.23.8.2.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.27.1.12.0.0.0.0.0.*********.73.0', '100', 'Reactive energy import (+R) L1 [Unit: kVarh]', '3#1.0.23.8.0.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.41', '100', 'VPN Network', '1#0.0.96.51.29.255#3', NULL, 'R', '1', NULL, NULL, 'special', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.37.0.0.0.0.0.0.0.32.3.38.0', '100', 'Instantaneous active power L3 (|+A|+|-A|) [Unit: kW]', '3#1.0.75.7.0.255#2', 'W', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.63.27.0.*********.0.*********.72.0', '100', 'L3 InAveragestant import reactive power', '3#1.0.63.27.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.1.0.44.27.0.*********.0.0.*******.38.0', '100', 'Average B-phase reverse reactive power', '3#1.0.44.27.0.255#2', 'Wh', 'R', '0.001', '1000', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.71', '100', 'Phase 2 export reactive energy', '3#1.0.44.8.0.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.21.1.12.0.0.0.0.0.*********.72.0', '100', 'Active energy import (+A) L1 [Unit: kWh]', '3#********.0.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.8.*******.8.1.0.15.8.6.255.0.224.3.38.2', '100', 'Rate 6 total active energy', '3#1.0.15.8.6.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.1.0.3.27.0.*********.0.0.*******.38.0', '100', 'Average combined positive reactive power', '3#1.0.3.27.0.255#2', 'Wh', 'R', '0.001', '1000', '1', 'double-long', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.0.0.0', '100', 'Step tariff', NULL, NULL, 'RW', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.78', '100', 'Phase 3 export reactive energy T2', '3#********.2.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.00.08.FF.01', '300', '月冻结第四象限无功电能数据块', '0008FF01', 'kvarh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.8.*******.8.1.0.128.8.0.255.0.224.3.38.2', '100', 'Total reactive energy', '3#1.0.128.8.0.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.34', '100', 'Phase 2 export active energy T3', '3#1.0.42.8.3.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.*******.0.15.9.0.112.0', '100', 'last 12 month active energy |+A| + |-A| consumption', '3#1.0.15.9.0.112#2', '1', 'RW', '0.01', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40*******1.0', '100', 'Over current in any phase3 occurrence', '110', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.30.1.12.0.0.0.0.2.*********.73.0', '100', 'Reactive energy export (-R) L1 rate 2 [Unit: kVarh]', '3#1.0.24.8.2.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.0.1.12.0.0.0.0.4.*********.72.1', '100', 'Monthly increase active energy tariff 4', '3#1.0.15.9.4.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.1C.00', '300', 'C相电压过压恢复', 'E200001C', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.22.1.12.0.0.0.0.0.*********.72.0', '100', 'Active energy import (+A) L2 [Unit: kWh]', '3#1.********.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.73', '100', 'Phase 2 export reactive energy T2', '3#1.0.44.8.2.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.20.20.10.15.14.2.29.0.0.0.0.0.0.*******5', '100', 'Cumulative export active energy, -A - rate 6', '3#1.0.2.8.6.255#2', NULL, 'RW', '0.01', NULL, NULL, 'double-long-unsigned', '12', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.56', '200', 'CT exception recovery', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.08.00', '300', 'B相TA二次侧短路恢复', 'E2000008', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.0.0.61.0.0.7.0.0.0.0.*******28.3.63.0', '100', 'L3 instant active power import', '3#1.0.61.7.0.255#2', 'W', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '44', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.8.0.0.0.4.11.15.29.0.*********.224.3.38.11', '100', 'Recharge credit in the current month', '1#0.0.19.129.8.255#2', NULL, 'R', '1', NULL, NULL, 'float32', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.*******9.133.0.106.0', '100', 'last 6 month credit consumption ', '1#0.**********.106#2', '0.01', 'RW', '1', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.49.00', '300', 'B相闪变越限恢复', 'E2000049', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('44.4.4.7', '100', 'Tamper indicate', '1#0.0.96.5.3.255#2', NULL, 'RW', '1', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******.0.1.5.0.*********.20.20.5.29.0', '100', 'Average Sum of active power+', '3#1.0.1.5.0.255#2', NULL, NULL, '1', NULL, NULL, 'double-long', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.37', '100', 'Phase 3 export active energy T1', '3#********.1.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.1.0.13.27.0.*********.0.0.*******.38.0', '100', 'Average combined phase power factor', '3#1.0.13.27.0.255#2', 'Wh', 'R', '0.001', '1000', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.18.*******.0.32.**********.0.1', '100', 'L1 average voltage', '3#*********.0.255#2', '1', 'RW', '0.1', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.1.316', '100', 'Phase and neutral exchange start', '7#*********.1.255#2#316', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.48', '100', 'Phase 1 import reactive energy T2', '3#1.0.23.8.2.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.2.1.8.0.0.0.0.0.*********.38.0', '100', 'Import apparent maximum demand', '4#1.0.9.6.0.255#2', NULL, 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.24.27.0.*********.0.*********.72.0', '100', 'L1 Average export reactive power', '3#1.0.24.27.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E1.00.A0.01', '300', '日冻结线损值', 'E100A001', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.1.8.0.0.0.0.7.*********.38.1', '100', 'Maximum Demand Register 1 -Active energy import(+A) rate 7 (Monthly) [Unit: kW]', '4#1.0.1.6.7.255#2', 'W', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.0.1.0.3.30.0.*********.0.*********.72.0', '100', 'Block import reactive energy,+R', '3#1.0.3.30.0.255#2', 'kWh', NULL, '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.18.*******.0.52.**********.0.1', '100', 'L2 average voltage', '3#1.0.52.27.0.255#2', '1', 'RW', '0.1', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40*******0.0', '100', 'Current bypass/short occurrence', '100', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.*******9.133.0.108.0', '100', 'last 8 month credit consumption ', '1#0.**********.108#2', '0.01', 'RW', '1', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.46.00', '300', 'C相电压偏差越限恢复', 'E2000046', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('111', '100', 'Collect', '11', NULL, 'R', '1', '1001', NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.19.01', '300', '全失压发生', 'E2000019', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.1.8.0.0.0.0.7.1.0.224.3.38.1', '100', 'Maximum Demand Register 1 -Active energy import(+A) rate 7 occurred time (Monthly)', '4#1.0.1.6.7.255#5', NULL, 'R', '1', NULL, '1', 'date-time', '12', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.0.0.0.96.56.4.255.0', '100', 'last 1 low battery voltage record', '1#0.0.96.56.4.255#2', '1', 'RW', '1', NULL, '0', 'special', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.99.97.96', '100', 'Phase B power failure occurred', '7#1.0.99.97.0.255#2#96', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.43.53.0.*********.0.*********.72.0', '100', 'L2 max import reactive power', '3#1.0.4**********#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.1.8.0', '100', 'Program memory error restoration', '0.4.25.9.0.255#1#80', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('38.0.0.107', '100', 'time_zone', '8#0.0.1.0.0.255#3', NULL, 'RW', '1', NULL, NULL, 'long', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.42.*********.0.1', '100', 'Phase 2 export active energy T1', '3#1.0.42.8.1.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('44.4.4.5', '100', 'last 1 charge date', '1#0.0.96.55.0.255#2', NULL, 'RW', '1', NULL, NULL, 'date-time', '5', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.23.1.12.0.0.0.0.2.*********.72.0', '100', 'Active energy import (+A) L3 rate 2 [Unit: kWh]', '3#********.2.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.3.20.0', '100', 'Terminal cover removed restoration', '0.4.25.9.0.255#3#200', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.46.01', '300', 'C相电压偏差越限发生', 'E2000046', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.20.14.93', '100', 'CurrentRatioDenominator', '1#1.0.0.4.5.255#2', NULL, 'RW', '1', NULL, NULL, 'long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.61.8.0.255.0.0.1', '100', 'Phase 3 import active energy', '3#********.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.18', '100', 'Energy profile reading cycle', '7#1.0.99.1.0.255#4', NULL, 'RW', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.9.1', '200', 'DCU Version', '09#1', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.57', '100', 'Phase 3 import reactive energy T1', '3#1.0.63.8.1.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('6.36.1.5', '100', 'Time', '1#1.0.0.9.1.255#2', NULL, 'R', '1', NULL, NULL, 'time', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('44.4.4.4', '100', 'charge sequence number', '1#0.0.96.54.70.255#2', NULL, 'RW', '1', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.3.14.0.0.2', '300', 'The time of load flow reverse L1 [Unit: Min]', 'Min', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.50', '100', 'Phase 1 import reactive energy T4', '3#1.0.23.8.4.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.31.1.12.0.0.0.0.4.*********.73.0', '100', 'Reactive energy export (-R) L2 rate 4 [Unit: kVarh]', '3#1.0.44.8.4.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('41.96.11.7', '100', 'Over voltage event code', '1#********1.7.255#2', NULL, 'R', '1', NULL, NULL, 'long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.23.1.12.0.0.0.0.4.*********.72.0', '100', 'Active energy import (+A) L3 rate 4 [Unit: kWh]', '3#********.4.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.32.00', '300', '时钟电池电压过低恢复', 'E2000032', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.05.06.08.FF', '300', '日冻结第四象限无功电能数据块', '050608FF', 'kvarh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.41.*********.0.1', '100', 'Phase 2 import active energy T1', '3#********.1.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.0.73.6.19.1.8.0.0.*******.0.224.3.38.0', '100', 'Export active demand occurred time', '5#1.0.2.4.0.255#5', NULL, 'R', '1', NULL, NULL, 'date-time', '12', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.1.1.20.20.7.6.0.0.*********.72.1', '100', 'Surplus credit(BL)', '3#**********.0.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.42.27.0.*********.0.*********.72.0', '100', 'L2 Average export active power', '3#1.0.42.27.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.49', '100', 'Phase 1 import reactive energy T3', '3#1.0.23.8.3.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.96.50.4', '100', 'Emergency over load relay open count', '100', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.2.1.8.0.0.9.6.6.*********.38.1', '100', 'Import apparent maximum demand,t6', '4#1.0.9.6.6.255#2', 'VA', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.39', '100', 'Meter Module Software Version', '1#*********.28.255#2', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.7', '100', 'Phase 1 import active energy T1', '3#********.1.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.42', '200', 'Apparent power over threshold recovery', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.0.0.0.96.56.14.255.0', '100', 'last 1 over load record', '1#0.0.96.56.14.255#2', '1', 'RW', '1', NULL, '0', 'special', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.0.1.12.0.*******.*********.72.1', '100', 'Monthly increase active energy tariff 1', '3#1.0.15.9.1.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.43.*********.0.1', '100', 'Phase 2 import reactive energy T2', '3#********.2.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.48', '200', 'Meter Reading goes fast recovery', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.0.1.12.0.0.1.2.3.*********.72.10', '100', 'Daily increase export reactive energy', '3#1.0.4.19.0.255#2', 'Wh', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.09.01', '300', 'C相TA二次侧短路发生', 'E2000009', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.2.1.8.0.0.9.6.0.*********.38.1', '100', 'Import apparent maximum demand,total', '4#1.0.9.6.0.255#2', 'VA', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.61.53.0.*********.0.*********.72.0', '100', 'L3 max import active power', '3#1.0.61.53.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.64', '200', 'Magnetic field exception recovery', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.23.1.12.0.0.0.0.0.*********.72.0', '100', 'Active energy import (+A) L3 [Unit: kWh]', '3#********.0.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.********.0.0.2.0.*********.73.0', '100', 'Combined reactive energy import 2 (+A) [Unit: kVarh]', 'Varh', NULL, NULL, '0.01', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.53.00', '300', '恒定磁场干扰告警恢复', 'E2000053', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.49', '200', 'Meter Reading stop occurrence', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.4', '100', 'Key meter parameter', '1#0.0.96.51.8.255#102', NULL, 'A', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.0.********.15.255.0', '100', 'last 2 times techincian ID', '1#********.15.255#2', '1', 'RW', '1', NULL, '0', 'octet-string', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.05.06.03.FF', '300', '日冻结正向无功电能数据块', '050603FF', 'kvarh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.22', '200', 'Dcu power up', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.0B.00', '300', 'B相TA二次侧开路恢复', 'E200000B', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('41.0.3.2', '100', 'Step tariff activation time', '1#*********.0.255#2', NULL, 'RW', '1', NULL, NULL, 'octet-string', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.*********.0.0.0.3.*********.73.0', '100', 'Reactive energy import (+R) L2 rate 3 [Unit: kVarh]', '3#********.3.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.**********.13.0.0.0.0.0.96.54.29.255.0.0.1', '100', 'Activity type', '1#0.0.96.54.29.255#2', '1', 'RW', '1', NULL, '0', 'unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.0.73.6.19.1.8.0.0.0.0.0.*********.38.0', '100', 'Export active demand [Unit:kW]', '5#1.0.2.4.0.255#2', 'W', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.63.*********.0.1', '100', 'Phase 3 import reactive energy T2', '3#1.0.63.8.2.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.1.0.4.27.0.*********.0.0.*******.38.0', '100', 'Average combined phase reverse reactive power', '3#1.0.4.27.0.255#2', 'Wh', 'R', '0.001', '1000', '1', 'double-long', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.3.1.0.0.1', '300', 'The number of voltage lose L1', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.23.8.0.255.0.0.1', '100', 'Phase 1 import reactive energy', '3#1.0.23.8.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.62.8.3.255.0.0.1', '100', 'Phase 3 export active energy T3', '3#********.3.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.15', '100', 'Phase 2 import active energy T4', '3#********.4.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('44.4.4.6', '100', 'last 1 charge time ', '1#0.0.96.55.1.255#2', NULL, 'RW', '1', NULL, NULL, 'date-time', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.*********.0.0.0.2.*********.73.0', '100', 'Reactive energy import (+R) L2 rate 2 [Unit: kVarh]', '3#********.2.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.24.53.0.*********.0.*********.72.0', '100', 'L1 max export reactive power', '3#1.0.24.53.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('************.********.0.255.2', '100', 'Active Energy Import – kWh – L3', '3#********.0.255#2', NULL, 'R', '0.01', NULL, NULL, 'double-long-unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.24.1.12.0.0.0.0.0.*********.72.0', '100', 'Active energy export (-A) L1 [Unit: kWh]', '3#1.0.22.8.0.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.27.1.12.0.0.0.0.2.*********.73.0', '100', 'Reactive energy import (+R) L1 rate 2 [Unit: kVarh]', '3#1.0.23.8.2.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.59.00', '300', '电流监测告警恢复', 'E2000059', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.03.0E.00.00', '300', '三相潮流反向次数及时间', '030E0000', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.3.********.0.0.0.0.0.224.0.0.3', '100', 'L3 average voltage', '3#1.0.72.24.0.255#2', 'Wh', 'R', '0.1', '2001', '1', 'long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.2.1.8.0.0.*******.0.224.3.38.0', '100', 'Import apparent maximum demand occured time', '4#1.0.9.6.0.255#5', NULL, 'R', '1', NULL, NULL, 'date-time', '12', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.1.0.42.27.0.*********.0.0.*******.38.0', '100', 'Average reverse active power of phase B', '3#1.0.42.27.0.255#2', 'Wh', 'R', '0.001', '1000', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.8', '100', 'Phase 1 import active energy T2', '3#********.2.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.27.1.12.0.0.0.0.4.*********.73.0', '100', 'Reactive energy import (+R) L1 rate 4 [Unit: kVarh]', '3#1.0.23.8.4.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.23.8.3.255.0.0.1', '100', 'Phase 1 import reactive energy T3', '3#1.0.23.8.3.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.40', '100', 'Phase 3 export active energy T4', '3#********.4.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.7.0', '100', 'Y Phase current reverse occurrence', '70', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.21.8.4.255.0.0.1', '100', 'Phase 1 import active energy T4', '3#********.4.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.2.20.20.09.08.18.14.2.0.0.0.0.0.0.0.0.9', '100', 'Instantaneous reactive import power (+R) L2', '3#1.0.43.7.0.255#2', NULL, 'RW', '0.001', NULL, NULL, 'double-long-unsigned', '12', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.2', '100', 'Delete meter list', '1#0.0.96.81.0.255#4', NULL, 'A', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.4.10', '200', 'AC Sampling Device Configuration Parameter', '04#10', NULL, 'W', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.30.1.12.0.0.0.0.4.*********.73.0', '100', 'Reactive energy export (-R) L1 rate 4 [Unit: kVarh]', '3#1.0.24.8.4.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.70', '100', 'Phase 1 export reactive energy T4', '3#1.0.24.8.4.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.68', '100', 'Phase 1 export reactive energy T2', '3#1.0.24.8.2.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.2.1.1.1.12.0.0.0.0.0.*********.72.0', '100', 'Daily Active energy import (+A) [Unit: kWh]', '3#1.0.9.19.0.255#2', 'Wh', 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.61.8.3.255.0.0.1', '100', 'Phase 3 import active energy T3', '3#********.3.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.********.0.0.0.0.72.7.32.0.29.0', '100', 'Total harmonic of phase C voltage', '3#1.0.72.7.124.255#2', '%', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.21.1.12.0.0.0.0.2.*********.72.0', '100', 'Active energy import (+A) L1 rate 2 [Unit: kWh]', '3#********.2.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.0.0.0.96.56.0.255.0', '100', 'last 1 top cover open record', '1#0.0.96.56.0.255#2', '1', 'RW', '1', NULL, '0', 'special', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.81.1', '100', 'Influence of permanent magnet or ac/dc electromagnet restoration', '811', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.********.**********.*********.72.9', '100', 'Average active power import (-A)', '3#1.0.2.27.0.255#2', 'Wh', 'R', '0.01', '1000', '1', 'double-long', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.00.01.FF.00', '300', '正向有功电能数据块', '0001FF00', 'kWh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.18.*******.0.32.51.0.255.0.0.1', '100', 'Minimum L1 Voltage', '3#1.0.32.51.0.255#2', '1', 'RW', '0.001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.36.01', '300', '电能表时段或费率更改发生', 'E2000036', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.103', '200', 'Total Reverse Active Energy Data', '0D#103', NULL, 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.2********.*******.*********.73.0', '100', 'Reactive energy import (+R) L3 rate 1 [Unit: kVarh]', '3#1.0.63.8.1.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.0.0.12.1.0.21.7.0.255.0.*******28.0.29.1', '100', 'Instant import active power L1 sy', '3#1.0.21.7.0.255#2', NULL, 'R', '1', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.83.0', '100', 'Meter cover opening occurrence', '830', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.2.1.8.0.0.9.6.********4.3.38.1', '100', 'Import apparent maximum demand,total,occured time', '4#1.0.9.6.0.255#5', NULL, 'R', '1', NULL, '1', 'date-time', '12', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.22.*********.0.1', '100', 'Phase 1 export active energy T1', '3#1.0.22.8.1.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.04.00.01.02', '300', '电表时钟-时分秒', '04000102', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.0.0.0.96.56.8.255.0', '100', 'last 1 phase b current reverse record', '1#0.0.96.56.8.255#2', '1', 'RW', '1', NULL, '0', 'special', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.10.00', '300', 'A相电流过流恢复', 'E2000010', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.2********.0.0.0.4.*********.73.0', '100', 'Reactive energy import (+R) L3 rate 4 [Unit: kVarh]', '3#1.0.63.8.4.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.0E.01', '300', 'B相潮流反向发生', 'E200000E', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******.0.21.8.0.*********.**********.0.0', '100', 'Phase 1 import active energy', '3#********.0.255#2', NULL, NULL, '1', NULL, '1', 'double-long-unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.*********.0.0.0.4.*********.73.0', '100', 'Reactive energy import (+R) L2 rate 4 [Unit: kVarh]', '3#********.4.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.82.0', '100', 'Netural disturbance - HF, dc or alternate method occurrence', '820', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.3.51.0.*********.0.*********.72.0', '100', 'Total Min Reactive Power', '3#1.0.3.51.0.255#2', 'Wh', 'R', '1', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.18.*******.0.71.**********.0.1', '100', 'L3 average current', '3#1.0.71.27.0.255#2', '1', 'RW', '0.001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.11', '100', 'Phase 2 import active energy', '3#1.********.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.78', '100', 'Phase 3 export reactive energy T2', '3#********.2.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.26.*******.0.7.**********.0.1', '100', 'Average reactive power of quadrant 3', '3#1.0.7.27.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.64.8.4.255.0.0.1', '100', 'Phase 3 export reactive energy T4', '3#********.4.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.9.8.321', '100', 'normal over load start', '7#*********.8.255#2#321', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.61', '100', 'APN', '45#********.0.255#2', NULL, 'RW', '1', NULL, NULL, 'octet-string', '32', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.1.1.20.20.7.6.0.0.*********.72.2', '100', 'Accumulated electricity purchase credit(BL)', '3#**********.2.255#2', NULL, 'R', '1', NULL, NULL, 'double-long', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.79', '100', 'Phase 3 export reactive energy T3', '3#********.3.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.8.*******.8.0.0.0.0.0.*********.63.8', '100', 'Export apparent maximum demand', '4#********.0.255#2', NULL, 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.********.**********.*********.72.3', '100', 'Average current L3', '3#1.0.71.27.0.255#2', 'A', 'R', '0.001', '1000', '1', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('44.4.5.3', '100', 'Event Time', '44.4.5.3', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.11', '100', 'Hardware reset', '1#0.0.96.51.6.255#1', NULL, 'A', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.02.06.00.00', '300', '总功率因素', '02060000', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.12.33', '200', 'Current Forward Active/Reactive Energy Data', '0C#33', NULL, 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.4.0.0.0.0.0.0.4.64.0.5.0', '100', 'Instantaneous average current L2', '3#1.0.51.27.0.255#2', 'W', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.15', '100', 'Encryption mode', '1#*********.26.255#2', NULL, 'RW', '1', NULL, NULL, 'unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('4.8.15.243', '100', 'Phase B Generation', '7#*********.4.255#2#243', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.27.0.*********.0.*********.72.0', '100', 'Average import active power', '3#1.0.1.27.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.21.8.0.255.0.0.1', '100', 'Phase 1 import active energy', '3#********.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.26.*******.0.3.5.0.255.0.0.1', '100', 'Average import reactive power', '3#1.0.3.5.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.26.0.20.20.6.********.0.*********.72.5', '100', 'Available credit', '3#*********.6.255#2', NULL, 'R', '0.01', NULL, '1', 'double-long', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.23.1.12.0.0.0.0.4.*********.72.0', '100', 'Active energy import (+A) L3 rate 4 [Unit: kWh]', '3#********.4.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.8.1.37.0.0.0.0.0.0.0.32.3.38.0', '100', 'Instantaneous apparent power L3 [Unit: kW]', NULL, 'W', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.0.1.12.0.0.0.0.0.*********.72.1', '100', 'Monthly increase active energy', '3#1.0.15.9.0.255#2', NULL, 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.*******9.133.0.112.0', '100', 'last 12 month credit consumption ', '1#0.**********.112#2', '0.01', 'RW', '1', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.72', '100', 'Phase 2 export reactive energy T1', '3#1.0.44.8.1.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.12.19.14', '100', 'Front cover closed', '7#*********.1.255#2#233', '1', 'RW', '1', NULL, '0', NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.22.8.0.255.0.0.1', '100', 'Phase 1 export active energy', '3#1.0.22.8.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('4.8.15.246', '100', 'C Phase End', '7#*********.4.255#2#246', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.64.*********.0.1', '100', 'Phase 3 export reactive energy T1', '3#********.1.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.6', '100', 'Key meter parameter delete all', '1#0.0.96.51.8.255#2', NULL, 'A', '1', NULL, NULL, 'special', NULL, '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.39', '100', 'Phase 3 export active energy T3', '3#********.3.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.76', '100', 'Phase 3 export reactive energy', '3#********.0.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.4', '200', 'Meter parameter missed', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.84.0', '100', 'Meter load disconnected/Meter load connected occurrence', '840', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.61.8.4.255.0.0.1', '100', 'Phase 3 import active energy T4', '3#********.4.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.52', '100', 'Phase 2 import reactive energy T1', '3#********.1.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.20.00', '300', 'A相电压畸变恢复', 'E2000020', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.91', '200', 'Phase C Voltage Profile', '0D#91', NULL, 'R', '0.001', '2002', NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.24.1.12.0.0.0.0.0.*********.72.0', '100', 'Active energy export (-A) L1 [Unit: kWh]', '3#1.0.22.8.0.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.********.*******.*********.73.0', '100', 'Combined reactive energy import 2 (+A) [Unit: kVarh]', 'Varh', NULL, NULL, '0.01', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********', '200', 'IP and Port of Master Station', '04#3', NULL, 'W', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.*******.********.255.0', '100', 'Period curve current md', '3#1.********.255#2', '1', 'RW', '1', NULL, '0', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.***********', '300', '电压逆相序恢复', 'E2000003', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.********.0.0.0.0.*********.63.0', '100', 'Export apparent maximum demand', '4#********.0.255#2', NULL, 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.24.1.12.0.*******.*********.72.0', '100', 'Active energy export (-A) L1 rate 1 [Unit: kWh]', '3#1.0.22.8.1.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.25.1.12.0.0.0.0.0.*********.72.0', '100', 'Active energy export (-A) L2 [Unit: kWh]', '3#1.0.42.8.0.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.26.101.248', '100', 'Over voltage (voltage SWELL) L1', '7#*********.3.255#2#79', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.61.8.0.255.0.0.1', '100', 'Phase 3 import active energy', '3#********.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.02.02.03.00', '300', 'C相电流', '02020300', 'A', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.20', '100', 'Phase 3 import active energy T4', '3#********.4.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.53.0.*********.0.*********.72.0', '100', 'max import active power', '3#1.0.1.53.0.255#2', 'Wh', 'R', '1', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.72.51.0.*********.0.*********.72.0', '100', 'L3 min voltage', '3#1.0.72.51.0.255#2', 'Wh', 'R', '0.1', '2001', '1', 'long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.*******', '100', 'B Phase - Voltage missing restoration', '21', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.18.*******.0.2.**********.0.1', '100', 'Average Sum of active power-', '3#1.0.2.27.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.2A.01', '300', '超合同容量用电发生', 'E200002A', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.*******.0.15.9.0.111.0', '100', 'last 11 month active energy |+A| + |-A| consumption', '3#1.0.15.9.0.111#2', '1', 'RW', '0.01', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.*******.0.15.9.0.109.0', '100', 'last 9 month active energy |+A| + |-A| consumption', '3#1.0.15.9.0.109#2', '1', 'RW', '0.01', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.42.*********.0.1', '100', 'Phase 2 export active energy T2', '3#1.0.42.8.2.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.33', '100', 'Phase 2 export active energy T2', '3#1.0.42.8.2.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.18.*******.0.128.8.0.255.0.0.1', '100', 'Total reactive energy', '3#1.0.128.8.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('13.**********.13.0.0.0.0.0.96.54.73.255.0.0.1', '100', 'Date of last charge', '1#0.0.96.54.73.255#2', '1', 'RW', '1', NULL, '0', 'date-time', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.35', '100', 'Phase 2 export active energy T4', '3#1.0.42.8.4.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.68', '100', 'Inactivity time out', '41#0.0.25.0.0.255#6', NULL, 'RW', '1', NULL, NULL, 'long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.23.1.12.0.0.0.0.2.*********.72.0', '100', 'Active energy import (+A) L3 rate 2 [Unit: kWh]', '3#********.2.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.14.01', '300', 'B相电流失流发生', 'E2000014', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.7.31.*******.0.52.7.124.255.0.0.1', '100', 'Total harmonic of phase B voltage', '3#1.0.52.7.124.255#2', '1', 'RW', '0.01', NULL, '1', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.1.2', '200', 'Data Initialization', '01#2', NULL, 'W', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.02.01.02.00', '300', 'B相电压', '02010200', 'V', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.32', '100', 'Phase 2 export active energy T1', '3#1.0.42.8.1.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.1.8.0.0.0.0.6.*********.38.1', '100', 'Maximum Demand Register 1 -Active energy import(+A) rate 6 (Monthly) [Unit: kW]', '4#1.0.1.6.6.255#2', 'W', 'R', '0.001', NULL, '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.22.*********.0.1', '100', 'Phase 1 export active energy T1', '3#1.0.22.8.1.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.4.1.1.1.12.0.0.0.0.0.*********.72.0', '100', 'Daily apparent energy export (+A) [Unit: kWh]', '3#1.0.10.19.0.255#2', 'Wh', 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.21.1.12.0.0.0.0.4.*********.72.0', '100', 'Active energy import (+A) L1 rate 4 [Unit: kWh]', '3#********.4.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('4.9.26.0.26', '100', 'Phase sequence reversal end', '7#*********.0.255#2#237', '1', 'RW', '1', NULL, '0', NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.1.0.1', '100', 'Clock invalid occurrence', '0.4.25.9.0.255#1#01', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.59.01', '300', '电流监测告警发生', 'E2000059', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.00.06.FF.00', '300', '第二象限无功电能数据块', '0006FF00', 'kvarh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.29.01', '300', '负荷过载发生', 'E2000029', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.31.24.0.*********.0.*********.72.0', '100', 'L1 average current', '3#1.0.31.24.0.255#2', 'Wh', 'R', '0.001', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('4.8.19.203', '100', 'Phase A Overpressure End', '7#*********.3.255#2#203', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.44', '200', 'Meter reading drop recovery', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.60.00', '300', '线损记录告警恢复', 'E2000060', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.**********.13.0.0.0.0.0.96.54.8.255.0.0.1', '100', 'Debits', '1#0.0.96.54.8.255#2', '1', 'RW', '0.001', NULL, '0', 'double-long', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.3.11.0.0.2', '300', 'The time of current lose L1 [Unit: Min]', 'Min', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.3.********.0.0.0.0.0.224.0.0.7', '100', 'average import active power', '3#1.0.1.24.0.255#2', 'Wh', 'R', '1', '2001', '1', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.14', '100', 'Phase 2 import active energy T3', '3#********.3.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.********.**********.0.2.224.3.72.5', '100', 'Average current L1', '3#1.0.31.27.0.255#2', 'A', 'R', '0.01', '1000', '1', 'double-long-unsigned', '4', '1');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.72.27.0.*********.0.0.*********.72.0', '100', 'L3-L1 Average voltage', '3#1.0.72.27.128.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.42.*********.0.1', '100', 'Phase 2 export active energy T2', '3#1.0.42.8.2.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.19', '100', 'Phase 3 import active energy T3', '3#********.3.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.*******9.133.0.101.0', '100', 'last 1 month credit consumption ', '1#0.**********.101#2', '0.01', 'RW', '1', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.28.00', '300', '功率超定值恢复', 'E2000028', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.20.14.92', '100', 'VoltageRatioNumerator', '1#1.0.0.4.3.255#2', NULL, 'RW', '1', NULL, NULL, 'long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('41.0.0.0.0', '100', 'TOU tariff', NULL, NULL, 'RW', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.1.1.20.20.7.6.0.0.*********.72.3', '100', 'Accumulated consumption credit(Monthly)(BL)', '3#0.0.19.131.0.255#2', NULL, 'R', '1', NULL, NULL, 'double-long', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.5.2.12.*******.1.*********.73.0', '100', 'Combined reactive energy import 1 (+A) [Unit: kVarh]', 'Varh', NULL, NULL, '0.01', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.********.0.0.0.0.0.3.128.0.29.0', '100', 'Instantaneous phase angle wave voltage L1 [Unit: V]', 'V', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.22.8.0.255.0.0.1', '100', 'Phase 1 export active energy', '3#1.0.22.8.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.83.1', '100', 'Meter cover opening restoration', '831', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.4E.00', '300', '表盖开启告警恢复', 'E200004E', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.0.0.0.96.54.73.255.0', '100', 'Date of last charge', '1#0.0.96.54.73.255#2', '1', 'RW', '1', NULL, '0', 'date-time', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.0.0.41.0.0.7.0.0.0.0.0.*********.38.0', '100', 'L2 instant active power impor', '3#1.0.41.7.0.255#2', 'W', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.10', '100', 'Phase 1 import active energy T4', '3#********.4.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.21.8.3.255.0.0.1', '100', 'Phase 1 import active energy T3', '3#********.3.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.46', '200', 'Energy deviation over threshold recovery', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.15.01', '300', 'C相电流失流发生', 'E2000015', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.4D.00', '300', '表端钮盒开启告警恢复', 'E200004D', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.8.1', '100', 'B Phase current reverse restoration', '81', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.60.01', '300', '线损记录告警发生', 'E2000060', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('30*******9', '200', 'Meter fault occurrence', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.26.*******.0.8.**********.0.1', '100', 'Average reactive power of quadrant 4', '3#1.0.8.27.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.24.*********.0.1', '100', 'Phase 1 export reactive energy T1', '3#1.0.24.8.1.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.27.00', '300', '无功欠补偿恢复', 'E2000027', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.25.00', '300', 'C相电流畸变恢复', 'E2000025', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.26.1.12.0.*******.*********.72.0', '100', 'Active energy export (-A) L3 rate 1 [Unit: kWh]', '3#********.1.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.42.0.0.0.0.0.0.3.0.1.38.0', '100', 'Average harmonic in sum of Li Instant active power', '3#1.0.15.27.124.255#2', '%', 'R', '0.01', NULL, NULL, 'long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('30*******1', '200', 'Current circuit occurrence', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.98.1.317', '100', 'Phase and neutral exchange end', '7#*********.1.255#2#317', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.8.0.6.2.1.8.0.0.9.6.********4.3.38.1', '100', 'Import apparent maximum demand,total,occured time  (Daily)', '4#1.0.9.6.0.255#5', NULL, 'R', '1', NULL, '1', 'date-time', '12', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.27.1.12.0.*******.*********.73.0', '100', 'Reactive energy import (+R) L1 rate 1 [Unit: kVarh]', '3#1.0.23.8.1.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.0.0.9', '200', 'Energy control disconnected', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('40.0.7.63', '100', 'PIN code', '45#********.0.255#3', NULL, 'RW', '1', NULL, NULL, 'long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.*******.0.15.9.0.255.0', '100', 'CurrentMonthConsumsion in kWh', '3#1.0.15.9.0.255#2', '1', 'RW', '0.01', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.72', '100', 'Phase 2 export reactive energy T1', '3#1.0.44.8.1.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.*********.0.0.0.0.*********.73.0', '100', 'Reactive energy import (+R) L2 [Unit: kVarh]', '3#********.0.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.25.1.12.0.0.0.0.4.*********.72.0', '100', 'Active energy export (-A) L2 rate 4 [Unit: kWh]', '3#1.0.42.8.4.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.85', '200', 'Reactive Power Load Profile', '0D#85', NULL, 'R', '0.001', '2001', NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('38.0.0.61', '100', 'Limiter_ monitored_value', '71#0.0.17.0.0.255#2', NULL, 'RW', '1', NULL, NULL, 'special', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.27.0.*********.0.0.*******.38.0', '100', 'Average C-phase reverse reactive power', '3#1.0.64.27.0.255#2', 'Wh', 'R', '0.001', '1000', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.1.0.24.27.0.*********.0.0.*******.38.0', '100', 'Average A-phase reverse reactive power', '3#1.0.24.27.0.255#2', 'Wh', 'R', '0.001', '1000', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.0.0.12.1.0.41.7.0.255.0.*******28.0.29.1', '100', 'Instant import active power L2 sy', '3#1.0.41.7.0.255#2', NULL, 'R', '1', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.0.0.0.96.54.71.255.0', '100', 'Meter status - Battery status', '1#0.0.96.54.71.255#2', '1', 'RW', '1', NULL, '0', 'long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.161', '200', 'Forward Active Energy Data (Total/Tariff 1-M)(Daily Frozen)', '0D#161', NULL, 'R', '0.001', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.3.********.0.0.0.0.0.224.0.0.1', '100', 'L1 average voltage', '3#1.0.32.24.0.255#2', 'Wh', 'R', '0.1', '2001', '1', 'long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.2********.0.0.0.2.*********.73.0', '100', 'Reactive energy import (+R) L3 rate 2 [Unit: kVarh]', '3#1.0.63.8.2.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.1D.00', '300', 'A相电压断相恢复', 'E200001D', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.2.1.8.0.0.9.6.1.1.0.224.3.38.1', '100', 'Import apparent maximum demand,t1,occured time', '4#1.0.9.6.1.255#5', NULL, 'R', '1', NULL, '1', 'date-time', '12', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.47.01', '300', '频率偏差越限发生', 'E2000047', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.11.26.*******.0.5.**********.0.1', '100', 'Average reactive power of quadrant 1', '3#1.0.5.27.0.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.00.05.FF.01', '300', '月冻结第一象限无功电能数据块', '0005FF01', 'kvarh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.0.73.6.1.1.8.0.0.0.0.0.*********.63.0', '100', 'Import reactive demand [Unit:kVar]', '5#1.0.3.4.0.255#2', 'Var', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.20.14.84', '100', 'Monthly Billing Day', '22#0.0.15.0.0.255#4', NULL, 'RW', '1', NULL, NULL, 'special', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.80', '100', 'Phase 3 export reactive energy T4', '3#********.4.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('200.0.13.90', '200', 'Phase B Voltage Profile', '0D#90', NULL, 'R', '0.001', '2002', NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.0.1.4.0.0.0.0.*******28.0.5.0', '300', 'Instantaneous fundamental wave current L1 [Unit: A]', 'A', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*******2.1.0.61.7.0.255.0.*******28.0.29.1', '100', 'Instant import active power L3 sy', '3#1.0.61.7.0.255#2', NULL, 'R', '1', NULL, NULL, 'double-long-unsigned', '44', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.51.24.0.*********.0.*********.72.0', '100', 'L2 average current', '3#1.0.51.24.0.255#2', 'Wh', 'R', '0.001', '2001', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('41.96.10.7', '100', 'Profile status 3', '1#********0.3.255#2', NULL, 'R', '1', NULL, NULL, 'unsigned', '1', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.71.27.0.*********.0.*********.72.0', '100', 'L3 Average current', '3#1.0.71.27.0.255#2', 'Wh', 'R', '0.01', '2001', '1', 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.43.00', '300', '电能表编程总次数恢复', 'E2000043', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.1.0.23.27.0.*********.0.0.*******.38.0', '100', 'Average positive reactive power of phase A', '3#1.0.23.27.0.255#2', 'Wh', 'R', '0.001', '1000', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.02.01.01.00', '300', 'A相电压', '02010100', 'V', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.0.1.0.1.30.0.*********.0.*********.72.0', '100', 'Active energy import (+A) (interval) [Unit: kWh]', '3#1.0.1.30.0.255#2', 'kWh', NULL, '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.26', '100', 'Phase 1 export active energy', '3#1.0.22.8.0.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('11.26.0.1.********.**********.*********.72.60', '100', 'Phase 3 import reactive energy T4', '3#1.0.63.8.4.255#2', 'Wh', 'R', '0.0001', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.19.00', '300', '全失压恢复', 'E2000019', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('xxxxxx', '100', 'xxxxxx', '111', NULL, 'R', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.1.0.43.27.0.*********.0.0.*******.38.0', '100', 'Average positive reactive power of phase B', '3#1.0.43.27.0.255#2', 'Wh', 'R', '0.001', '1000', '1', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.02.04.FF.00', '300', '无功功率数据块', '0204FF00', 'kvar', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.40.1.0', '100', 'Standard Event log', '7#*********.0.255#2', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('43.0.0.4', '100', 'Manual LCD Period', '3#0.0.96.58.2.255#2', NULL, 'RW', '1', NULL, NULL, 'long-unsigned', '2', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.9.0', '100', 'Current balance occurrence', '90', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.00.00.FF.00', '300', '组合有功电能数据块', '0000FF00', 'kWh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.E2.00.00.05.00', '300', '电压不平衡恢复', 'E2000005', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.12.01.0.0.*******9.131.0.255.0.0.1', '100', 'Current Cumulative consume credit_New', '3#0.0.19.131.0.255#2', '1', 'R', '0.01', NULL, '0', 'double-long', '3', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('3.99.97.97', '100', 'Phase C power failure occurred', '7#1.0.99.97.0.255#2#97', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('400.0.0.51.0', '100', 'Earth loading occurrence', '510', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.**********.13.0.0.*******9.130.7.255.0.0.1', '100', 'Last recharge amount in EGP', '1#**********.7.255#2', '1', 'RW', '1', NULL, '0', 'float32', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.1.1.20.20.7.6.0.0.*********.72.16', '100', 'Instantaneous active power L3 (|+A|+|-A|) [Unit: kW]', '3#1.0.75.7.0.255#2', 'W', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.3.11.0.0.5', '300', 'The number of current lose L3', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.0.0.0.96.56.6.255.0', '100', 'last 1 phase a current reverse record', '1#0.0.96.56.6.255#2', '1', 'RW', '1', NULL, '0', 'special', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.0.0.0.96.56.2.255.0', '100', 'last 1 bottom cover open record', '1#0.0.96.56.2.255#2', '1', 'RW', '1', NULL, '0', 'special', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('300.00.03.FF.00', '300', '正向无功电能数据块', '0003FF00', 'kvarh', 'R', '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.********.0.0.0.0.0.96.54.69.255.0', '100', 'Tariff Id', '1#0.0.96.54.69.255#2', '1', 'RW', '1', NULL, '0', 'octet-string', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.12.01.0.0.*******9.130.2.255.0.0.1', '100', 'Cumulative Purchase Credit_New', '3#**********.2.255#2', '1', 'R', '0.01', NULL, '0', 'double-long', '3', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.43.*********.0.1', '100', 'Phase 2 import reactive energy T1', '3#********.1.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.2********.0.0.0.4.*********.73.0', '100', 'Reactive energy import (+R) L3 rate 4 [Unit: kVarh]', '3#1.0.63.8.4.255#2', 'Varh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('6.122.127.1.0.96.10.1.255.2', '100', 'Status', '1#0.96.10.1.255#2', NULL, 'R', '0.01', NULL, NULL, 'unsigned', NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('0.0.73.6.1.1.8.0.0.0.0.0.*********.38.0', '100', 'Import active demand [Unit:kW]', '5#1.0.1.4.0.255#2', 'W', 'R', '0.001', NULL, NULL, 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('*********.********.**********.*********.72.58', '100', 'Phase 3 import reactive energy T2', '3#1.0.63.8.2.255#2', 'Wh', 'R', '0.01', NULL, NULL, 'double-long-unsigned', '4', NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('********.********.0.0.2.2.*********.73.0', '100', 'Combined reactive energy import 2 (+A) [Unit: kVarh]', 'Varh', NULL, NULL, '0.01', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('30*******8', '200', 'Meter time deviation over threshold recovery', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL);
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('**********.11.18.*******.0.63.8.3.255.0.0.1', '100', 'Phase 3 import reactive energy T3', '3#1.0.63.8.3.255#2', '1', 'RW', '0.0001', NULL, '0', 'double-long-unsigned', '4', '0');
INSERT INTO DICT_DATAITEM (ID, PROTOCOL_ID, NAME, PROTOCOL_CODE, UNIT, OP_TYPE, SHOW_UNIT, DATAITEM_TYPE, IS_SHOW, PARSE_TYPE, PARSE_LEN, SCALE) VALUES ('4.8.15.245', '100', 'Phase C Generation', '7#*********.4.255#2#245', NULL, 'R', '1', NULL, NULL, NULL, NULL, NULL);

Insert into DICT_PROFILE (ID,NAME,PROTOCOL_ID,PROTOCOL_CODE,PROFILE_TYPE) values ('10100','Top cover open log','100','7#*********.5.255#2',5);
Insert into DICT_PROFILE (ID,NAME,PROTOCOL_ID,PROTOCOL_CODE,PROFILE_TYPE) values ('10101','Terminal cover open log','100','7#*********.6.255#2',5);
Insert into DICT_PROFILE (ID,NAME,PROTOCOL_ID,PROTOCOL_CODE,PROFILE_TYPE) values ('10102','Module cover open log','100','7#*********.7.255#2',5);
Insert into DICT_PROFILE (ID,NAME,PROTOCOL_ID,PROTOCOL_CODE,PROFILE_TYPE) values ('10103','Battery voltage low log','100','7#*********.8.255#2',5);

Insert into DICT_PROFILE (ID,NAME,PROTOCOL_ID,PROTOCOL_CODE,PROFILE_TYPE) values ('10058','Over voltage Event log','100','7#*********.7.255#2',5);
INSERT INTO dict_profile_data_item (PROFILE_ID, DATAITEM_ID, SORT_ID) VALUES ('10058', '6.36.0.0', '1');
INSERT INTO dict_profile_data_item (PROFILE_ID, DATAITEM_ID, SORT_ID) VALUES ('10058', '41.96.11.7', '2');
INSERT INTO dict_profile_data_item (PROFILE_ID, DATAITEM_ID, SORT_ID) VALUES ('10058', '3.32.38.1', '3');
INSERT INTO dict_profile_data_item (PROFILE_ID, DATAITEM_ID, SORT_ID) VALUES ('10058', '3.52.38.1', '4');
INSERT INTO dict_profile_data_item (PROFILE_ID, DATAITEM_ID, SORT_ID) VALUES ('10058', '3.72.38.1', '5');

Insert into DICT_PROFILE (ID,NAME,PROTOCOL_ID,PROTOCOL_CODE,PROFILE_TYPE) values ('10059','Normal over load Event Log','100','7#*********.8.255#2',5);
INSERT INTO dict_profile_data_item (PROFILE_ID, DATAITEM_ID, SORT_ID) VALUES ('10059', '6.36.0.0', '1');
INSERT INTO dict_profile_data_item (PROFILE_ID, DATAITEM_ID, SORT_ID) VALUES ('10059', '41.96.11.8', '2');
INSERT INTO dict_profile_data_item (PROFILE_ID, DATAITEM_ID, SORT_ID) VALUES ('10059', '3.96.50.3', '3');
INSERT INTO dict_profile_data_item (PROFILE_ID, DATAITEM_ID, SORT_ID) VALUES ('10059', '3.15.38.0', '4');

Insert into DICT_PROFILE (ID,NAME,PROTOCOL_ID,PROTOCOL_CODE,PROFILE_TYPE) values ('10100','Top cover open log','100','7#*********.5.255#2',5);
INSERT INTO dict_profile_data_item (PROFILE_ID, DATAITEM_ID, SORT_ID) VALUES ('10100', '13.98.1.54', '2');


