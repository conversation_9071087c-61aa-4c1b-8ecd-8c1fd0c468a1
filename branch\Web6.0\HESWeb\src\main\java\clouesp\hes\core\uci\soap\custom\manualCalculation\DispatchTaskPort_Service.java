package clouesp.hes.core.uci.soap.custom.manualCalculation;


import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import javax.xml.ws.Service;

/**
 * This class was generated by Apache CXF 3.1.18
 * 2019-04-18T11:18:00.961+08:00
 * Generated source version: 3.1.18
 * 
 */
@WebServiceClient(name = "DispatchTaskPort", 
                  wsdlLocation = "http://119.23.109.166:7030/UCI-1.0-SNAPSHOT/services/DispatchTask?wsdl",
                  targetNamespace = "http://dispatch_task.custom.soap.uci.core.hes.clouesp/") 
public class DispatchTaskPort_Service extends Service {

    public final static URL WSDL_LOCATION;

    public final static QName SERVICE = new QName("http://dispatch_task.custom.soap.uci.core.hes.clouesp/", "DispatchTaskPort");
    public final static QName DispatchTaskPortPort = new QName("http://dispatch_task.custom.soap.uci.core.hes.clouesp/", "DispatchTaskPortPort");
    static {
        URL url = null;
        try {
            url = new URL("http://119.23.109.166:7030/UCI-1.0-SNAPSHOT/services/DispatchTask?wsdl");
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(DispatchTaskPort_Service.class.getName())
                .log(java.util.logging.Level.INFO, 
                     "Can not initialize the default wsdl from {0}", "http://119.23.109.166:7030/UCI-1.0-SNAPSHOT/services/DispatchTask?wsdl");
        }
        WSDL_LOCATION = url;
    }

    public DispatchTaskPort_Service(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public DispatchTaskPort_Service(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public DispatchTaskPort_Service() {
        super(WSDL_LOCATION, SERVICE);
    }
    
    public DispatchTaskPort_Service(WebServiceFeature ... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }

    public DispatchTaskPort_Service(URL wsdlLocation, WebServiceFeature ... features) {
        super(wsdlLocation, SERVICE, features);
    }

    public DispatchTaskPort_Service(URL wsdlLocation, QName serviceName, WebServiceFeature ... features) {
        super(wsdlLocation, serviceName, features);
    }    




    /**
     *
     * @return
     *     returns DispatchTaskPort
     */
    @WebEndpoint(name = "DispatchTaskPortPort")
    public DispatchTaskPort getDispatchTaskPortPort() {
        return super.getPort(DispatchTaskPortPort, DispatchTaskPort.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns DispatchTaskPort
     */
    @WebEndpoint(name = "DispatchTaskPortPort")
    public DispatchTaskPort getDispatchTaskPortPort(WebServiceFeature... features) {
        return super.getPort(DispatchTaskPortPort, DispatchTaskPort.class, features);
    }

}
