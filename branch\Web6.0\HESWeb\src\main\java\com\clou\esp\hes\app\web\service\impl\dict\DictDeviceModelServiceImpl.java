/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictDeviceModel{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-18 02:59:36
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.dict;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.dict.DictDeviceModelDao;
import com.clou.esp.hes.app.web.model.dict.DictDeviceModel;
import com.clou.esp.hes.app.web.service.dict.DictDeviceModelService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("dictDeviceModelService")
public class DictDeviceModelServiceImpl  extends CommonServiceImpl<DictDeviceModel>  implements DictDeviceModelService {

	@Resource
	private DictDeviceModelDao dictDeviceModelDao;

	@Override
	public List<DictDeviceModel> getDictDeviceList(DictDeviceModel entity) {
		return this.dictDeviceModelDao.getDictDeviceList(entity);
	}
	@Autowired
    public void setCommonService() {
        super.setCommonService(dictDeviceModelDao);
    }
	@SuppressWarnings("rawtypes")
	public DictDeviceModelServiceImpl() {}
	
	
}