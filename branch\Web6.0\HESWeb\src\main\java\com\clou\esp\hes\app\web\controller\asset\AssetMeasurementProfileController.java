/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeasurementProfile{ } 
 * 
 * 摘    要： assetMeasurementProfile
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-02-28 02:48:00
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.asset;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.model.asset.AssetMeasurementProfile;
import com.clou.esp.hes.app.web.model.asset.AssetMeasurementProfileDi;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.asset.AssetMeasurementProfileDiService;
import com.clou.esp.hes.app.web.service.asset.AssetMeasurementProfileService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.google.common.collect.Lists;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.redis.JedisUtils;

/**
 * <AUTHOR>
 * @时间：2018-02-28 02:48:00
 * @描述：assetMeasurementProfile类
 */
@Controller
@RequestMapping("/assetMeasurementProfileController")
public class AssetMeasurementProfileController extends BaseController{

 	@Resource
    private AssetMeasurementProfileService assetMeasurementProfileService;
 	@Resource
    private AssetMeasurementProfileDiService assetMeasurementProfileDiService;

	/**
	 * 跳转到assetMeasurementProfile列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/asset/assetMeasurementProfileList");
    }

	/**
	 * 跳转到assetMeasurementProfile新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "assetMeasurementProfile")
	public ModelAndView assetMeasurementProfile(AssetMeasurementProfile assetMeasurementProfile,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(assetMeasurementProfile.getId())){
			try {
                assetMeasurementProfile=assetMeasurementProfileService.getEntity(assetMeasurementProfile.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("assetMeasurementProfile", assetMeasurementProfile);
		}
		return new ModelAndView("/asset/assetMeasurementProfile");
	}


	/**
	 * assetMeasurementProfile查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=assetMeasurementProfileService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除assetMeasurementProfile信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(AssetMeasurementProfile assetMeasurementProfile,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(assetMeasurementProfileService.deleteById(assetMeasurementProfile.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存assetMeasurementProfile信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })AssetMeasurementProfile assetMeasurementProfile,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        AssetMeasurementProfile t=new  AssetMeasurementProfile();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(assetMeasurementProfile.getId())){
        	t=assetMeasurementProfileService.getEntity(assetMeasurementProfile.getId());
			MyBeanUtils.copyBeanNotNull2Bean(assetMeasurementProfile, t);
				assetMeasurementProfileService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            assetMeasurementProfileService.save(assetMeasurementProfile);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
	
    /**
     * 根据条件，查询Measurement Data数据
     * @param id
     * @return
     */
	@ResponseBody
    @RequestMapping(value = "getMeasurementData")
    public AjaxJson getMeasurementData(AssetMeasurementProfile assetMeasurementProfile, HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su = TokenManager.getToken();
        	List<AssetMeasurementProfile> list = assetMeasurementProfileService.getList(assetMeasurementProfile);
        	/*
    		 * 设置数据唯一主键，否则，jqgrid将因为主键冲突而不能被选中和编辑
    		 */
        	for (int i = 0; i < list.size(); i++) {
				list.get(i).setId(list.get(i).getProfileId());
				if("none".equals(list.get(i).getProfileCycleType())){
					list.get(i).setProfileCycleType("");
					list.get(i).setProfileCycle("");
				}
				
				//保存每个profile的数据到Redis中
				//主键为system user的ID和profile id
				AssetMeasurementProfileDi entity = new AssetMeasurementProfileDi();
				entity.setMgId(list.get(i).getMgId());				//组ID
				entity.setProfileId(list.get(i).getProfileId());	//ProfileId
				List<AssetMeasurementProfileDi> listProfileDi = assetMeasurementProfileDiService.getList(entity);
				Map<String, Object> value = new HashMap<>();
				if(listProfileDi==null){
					listProfileDi = Lists.newArrayList();
				}
				value.put("listProfileDi", listProfileDi);
				JedisUtils.setObjectMap(list.get(i).getMgId() + list.get(i).getProfileId(), value, 0);	//主键为mgId+profileId
			}
            if(list.size() > 0){
            	j.setObj(list);
            }else{
                j.setSuccess(false);
                j.setMsg("No data found!");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg("Abnormal operation");
        }
        return j;
    } 
	
	 /**
     * 保存measurement group的下属所有数据，
     * 包括Profile List，Selected Data Channel List的所有数据;
     * 		Selected Data Channel List的数据每次页面初始加载的时候全部存入Redis中,
     * 		界面的增加和删除都会修改Redis，但是只有最后一步保存，才会变动数据库的数据。
     * @param id
     * @return
     */
	@ResponseBody
    @RequestMapping(value = "saveMeasurementGroupData")
    public AjaxJson saveMeasurementGroupData(String groupId, HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su = TokenManager.getToken();
            j.setMsg("No data found!");
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg("Abnormal operation");
        }
        return j;
    } 
	
}