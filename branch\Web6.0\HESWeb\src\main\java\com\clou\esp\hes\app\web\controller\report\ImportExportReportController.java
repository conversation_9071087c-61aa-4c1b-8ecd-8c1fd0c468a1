package com.clou.esp.hes.app.web.controller.report;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.CreatePdf;
import com.clou.esp.hes.app.web.core.util.ExcelUtils;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.OrganizationUtils;
import com.clou.esp.hes.app.web.model.report.ImportExportReport;
import com.clou.esp.hes.app.web.model.system.SysOrg;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.report.ImportExportReportService;
import com.clou.esp.hes.app.web.service.system.SysOrgService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.clou.esp.hes.app.web.validation.ValidGroup2;
import com.power7000g.core.excel.ExcelDataFormatter;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;


/**
 * @ClassName: ImportExportReportController
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年10月18日 上午9:34:47
 *
 */
@Controller
@RequestMapping("/importExportReportController")
public class ImportExportReportController extends BaseController {
	@Resource
	private ImportExportReportService importExportReportService;
	@Resource
	private SysOrgService sysOrgService;
	
	@RequestMapping(value = "findImportExportReport")
    @ResponseBody
    public JqGridResponseTo findImportExportReport(boolean flag,String dataType,
    		String timeType,String startTime,String endTime,
    		String timePattern,String orgId,String entity,String entityType,String calcObjId,
    		JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
        	if(!flag){
        		j = new JqGridResponseTo();
        		return j;
        	}
        	
        	if(StringUtils.isEmpty(calcObjId)) {
        		endTime=startTime;
        	}
        	// 参数赋值
        	jqGridSearchTo.put("dataType", dataType);
        	jqGridSearchTo.put("timeType", timeType);
        	jqGridSearchTo.put("startTime", startTime);
        	jqGridSearchTo.put("endTime", endTime);
        	jqGridSearchTo.put("timePattern", timePattern);
        	jqGridSearchTo.put("orgId", orgId);
        	jqGridSearchTo.put("entityType", entityType);
        	jqGridSearchTo.put("entity", entity);
        	jqGridSearchTo.put("calcObjId", calcObjId);
        	// 数据库查询
        	List<String> orgIdList=OrganizationUtils.getOrgIds(sysOrgService,orgId);
        	jqGridSearchTo.put("orgIds", orgIdList);
        	j = importExportReportService.findImportExportReport(jqGridSearchTo);
        } catch (Exception e) {
            e.printStackTrace();
        }
		return j;
    }
	
	@RequestMapping(value = "findImportExportReportDetail")
    @ResponseBody
    public JqGridResponseTo findImportExportReportDetail(boolean flag,String dataType,
    		String timeType,String time,
    		String timePattern,String orgId,String calcObjId,
    		JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
        	if(!flag){
        		j = new JqGridResponseTo();
        		return j;
        	}
        	// 参数赋值
        	jqGridSearchTo.put("dataType", dataType);
        	jqGridSearchTo.put("timeType", timeType);
        	jqGridSearchTo.put("time", time);
        	jqGridSearchTo.put("timePattern", timePattern);
        	jqGridSearchTo.put("orgId", orgId);
        	jqGridSearchTo.put("calcObjId", calcObjId);
        	
        	// 数据库查询
        	List<String> orgIdList=OrganizationUtils.getOrgIds(sysOrgService,orgId);
        	jqGridSearchTo.put("orgIds", orgIdList);
        	j = importExportReportService.findImportExportReportDetail(jqGridSearchTo);
        } catch (Exception e) {
            e.printStackTrace();
        }
		return j;
    }
	
	@RequestMapping(value = "exportImportAndExportReport")
	@ResponseBody
	public void exportImportAndExportReport(HttpServletRequest request, HttpServletResponse response) {
	    
	    try {
	    	List<ImportExportReport> ieReports = getImportAndExportReport(request);
	    	ExcelDataFormatter edf = new ExcelDataFormatter();
	        //要日期格式化使用以下方式
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("date", "MM/dd/yyyy HH:mm:ss");
	        edf.set("date", tvs);
	        ExcelUtils.writeToFile(ieReports, edf, "dataImportAndExportList.xlsx", response, ValidGroup1.class);
//			ExcelUtils.writeToFile(ieReports, edf, "dataImportAndExportList.xlsx", response);
		} catch (Exception e) {
			e.printStackTrace();
		}
    }
	
	@RequestMapping(value = "printImportAndExportReport")
	@ResponseBody
	public void printImportAndExportReport(HttpServletRequest request, HttpServletResponse response) {
	    try {
	    	List<ImportExportReport> ieReports = getImportAndExportReport(request);
	    	ExcelDataFormatter edf = new ExcelDataFormatter();
	        //要日期格式化使用以下方式
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("date", "MM/dd/yyyy HH:mm:ss");
	        edf.set("date", tvs);
	        CreatePdf.printPdf(ieReports, edf, ValidGroup1.class, request, response);
//	        CreatePdf.printPdf(ieReports, edf, request, response);
		} catch (Exception e) {
			e.printStackTrace();
		}
    }

	/**
	 * @Title: getImportAndExportReport
	 * @Description: TODO
	 * @param request
	 * @return
	 * @return List<ImportExportReport>
	 * @throws
	 */
	private List<ImportExportReport> getImportAndExportReport(
			HttpServletRequest request) {
		// TODO Auto-generated method stub
		String dataType = (String) request.getParameter("dataType");
	    String orgId = (String) request.getParameter("orgId");
	    String timeType = (String) request.getParameter("timeType");
	    String startTime = request.getParameter("startTime");
	    String endTime = (String) request.getParameter("endTime");
	    String timePattern = request.getParameter("timePattern");
	    String entityType = request.getParameter("entityType");
	    String entity = request.getParameter("entity");
	    
	    
	 // 参数赋值
	    JqGridSearchTo jqGridSearchTo = new JqGridSearchTo();
	    jqGridSearchTo.put("dataType", dataType);
    	jqGridSearchTo.put("timeType", timeType);
    	jqGridSearchTo.put("startTime", startTime);
    	jqGridSearchTo.put("endTime", endTime);
    	jqGridSearchTo.put("timePattern", timePattern);
    	jqGridSearchTo.put("orgId", orgId);
    	jqGridSearchTo.put("entityType", entityType);
    	jqGridSearchTo.put("entity", entity);
    	List<String> orgIdList=OrganizationUtils.getOrgIds(sysOrgService,orgId);
    	jqGridSearchTo.put("orgIds", orgIdList);
    	List<ImportExportReport> ieReports = importExportReportService.findImportExportReportList(jqGridSearchTo);
    	if(null == ieReports || ieReports.isEmpty()){
    		ieReports = new ArrayList<ImportExportReport>();
    		ieReports.add(new ImportExportReport());
    	}
    	
		return ieReports;
	}
	
	@RequestMapping(value = "exportImportAndExportDetailReport")
	@ResponseBody
	public void exportImportAndExportDetailReport(HttpServletRequest request, HttpServletResponse response) {
	    
	    try {
	    	List<ImportExportReport> ieReports = getImportAndExportDetailReport(request);
	    	ExcelDataFormatter edf = new ExcelDataFormatter();
	        //要日期格式化使用以下方式
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("date", "MM/dd/yyyy HH:mm:ss");
	        edf.set("date", tvs);
//			ExcelUtils.writeToFile(ieReports, edf, "dataImportAndExportDetailList.xlsx", response);
	        ExcelUtils.writeToFile(ieReports, edf, "dataImportAndExportDetailList.xlsx", response, ValidGroup2.class);
		} catch (Exception e) {
			e.printStackTrace();
		}
    }
	
	@RequestMapping(value = "printImportAndExportDetailReport")
	@ResponseBody
	public void printImportAndExportDetailReport(HttpServletRequest request, HttpServletResponse response) {
	    try {
	    	List<ImportExportReport> ieReports = getImportAndExportDetailReport(request);
	    	ExcelDataFormatter edf = new ExcelDataFormatter();
	        //要日期格式化使用以下方式
	        Map<String,String> tvs=new HashMap<String, String>();
	        tvs.put("date", "MM/dd/yyyy HH:mm:ss");
	        edf.set("date", tvs);
//	        CreatePdf.printPdf(ieReports, edf, request, response);
	        CreatePdf.printPdf(ieReports, edf, ValidGroup2.class, request, response);
		} catch (Exception e) {
			e.printStackTrace();
		}
    }

	/**
	 * @Title: getImportAndExportDetailReport
	 * @Description: TODO
	 * @param request
	 * @return
	 * @return List<ImportExportReport>
	 * @throws
	 */
	private List<ImportExportReport> getImportAndExportDetailReport(
			HttpServletRequest request) {
		// TODO Auto-generated method stub
		String dataType = (String) request.getParameter("dataType");
	    String orgId = (String) request.getParameter("orgId");
	    String timeType = (String) request.getParameter("timeType");
	    String time = request.getParameter("time");
	    String timePattern = request.getParameter("timePattern");
	    String calcObjId = request.getParameter("calcObjId");
	    
	    List<ImportExportReport> ieReports = null;
	    if(StringUtil.isEmpty(dataType) || StringUtil.isEmpty(orgId)
	    		|| StringUtil.isEmpty(timeType) || StringUtil.isEmpty(time) 
	    		|| time.equalsIgnoreCase(MutiLangUtil.doMutiLang("importAndExportReport.noRecords"))) {
	    	ieReports = null;
	    } else {
	    	// 参数赋值
	    	JqGridSearchTo jqGridSearchTo = new JqGridSearchTo();
	    	jqGridSearchTo.put("dataType", dataType);
	    	jqGridSearchTo.put("timeType", timeType);
	    	jqGridSearchTo.put("time", time);
	    	jqGridSearchTo.put("timePattern", timePattern);
	    	jqGridSearchTo.put("orgId", orgId);
	    	jqGridSearchTo.put("calcObjId", calcObjId);
	    	List<String> orgIdList=OrganizationUtils.getOrgIds(sysOrgService,orgId);
        	jqGridSearchTo.put("orgIds", orgIdList);
	    	ieReports = importExportReportService.findImportExportReportDetailList(jqGridSearchTo);
	    }
	    
    	if(null == ieReports || ieReports.isEmpty()){
    		ieReports = new ArrayList<ImportExportReport>();
    		ieReports.add(new ImportExportReport());
    	}
    	
		return ieReports;
	}
	
	
	public  void setOrgCode(JqGridSearchTo jqGridSearchTo,String orgId) {
    		SysOrg sysOrg= sysOrgService.getEntity(orgId);
        	if(sysOrg!=null) {
        		jqGridSearchTo.put("orgCode", sysOrg.getOrgCode());
        	}
	}
	

}
