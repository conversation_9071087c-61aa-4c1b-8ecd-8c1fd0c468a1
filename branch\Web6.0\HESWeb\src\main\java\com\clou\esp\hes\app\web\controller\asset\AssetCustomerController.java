/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetCustomer{ } 
 * 
 * 摘    要： assetCustomer
 * 版    本：1.0
 * 作    者：赵久霆
 * 创建于：2018-11-13 06:49:07
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.asset;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.util.OrganizationUtils;
import com.clou.esp.hes.app.web.model.asset.AssetCustomer;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.dict.DictCustomerIndustry;
import com.clou.esp.hes.app.web.model.dict.DictCustomerType;
import com.clou.esp.hes.app.web.model.system.SysOrg;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.asset.AssetCustomerService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.system.SysOrgService;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;

/**
 * <AUTHOR>
 * @时间：2018-11-13 06:49:07
 * @描述：assetCustomer类
 */
@Controller
@RequestMapping("/assetCustomerController")
public class AssetCustomerController extends BaseController{

 	@Resource
    private AssetCustomerService 	assetCustomerService;
 	@Resource
 	private SysOrgService  			sysOrgService;
	@Resource
 	private AssetMeterService  	    assetMeterService;
	@Resource
	private DataUserLogService 		dataUserLogService;

	/**
	 * 跳转到assetCustomer列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/asset/assetCustomerList");
    }

	/**
	 * 跳转到assetCustomer新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "assetCustomer")
	public ModelAndView assetCustomer(AssetCustomer assetCustomer,HttpServletRequest request, Model model) {
		List<DictCustomerType> dictCustomerTypes=assetCustomerService.getDictCustomerType();
		String customerTypeReplace=RoletoJson.listToReplaceStr(dictCustomerTypes, "id", "name", ",");
		List<DictCustomerIndustry> dictCustomerIndustrys=assetCustomerService.getDictCustomerIndustry();
		String customerIndustryReplace=RoletoJson.listToReplaceStr(dictCustomerIndustrys, "id", "name", ",");
		model.addAttribute("customerIndustryReplace",customerIndustryReplace);
		model.addAttribute("customerTypeReplace",customerTypeReplace);
		
		if(StringUtil.isNotEmpty(assetCustomer.getId())){
			try {
                assetCustomer=assetCustomerService.getEntity(assetCustomer.getId());
				AssetMeter meter=assetMeterService.getEntity(assetCustomer.getMeterId());
				if(meter!=null) {
					assetCustomer.setMeterSn(meter.getSn());
				}
				model.addAttribute("assetCustomer", assetCustomer);
			}catch (Exception e) {
                e.printStackTrace();
            }
			
		}
		return new ModelAndView("/asset/assetCustomer");
	}


	/**
	 * assetCustomer查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request,String sn,String name,String telephoneNum,String orgId) {
        JqGridResponseTo j=null;
        try {
        	jqGridSearchTo.put("sn", sn);
        	jqGridSearchTo.put("name", name);
        	jqGridSearchTo.put("telephoneNum", telephoneNum);
        	
    		List<String> orgIdList=OrganizationUtils.getOrgIds(sysOrgService,orgId);
        	jqGridSearchTo.put("orgIds", orgIdList);
            j=assetCustomerService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除assetCustomer信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(AssetCustomer assetCustomer,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            assetCustomer=this.assetCustomerService.get(assetCustomer);
            if(assetCustomerService.deleteById(assetCustomer.getId())>0){
            	 dataUserLogService.insertDataUserLog(su.getId(), "Asset Managerment", "Delete Customer","Delete Customer(SN="+ assetCustomer.getSn()+",Name="+(StringUtils.isNotEmpty(assetCustomer.getName())?assetCustomer.getName():"")+")");
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存assetCustomer信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })AssetCustomer assetCustomer,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        AssetCustomer t=new  AssetCustomer();
        try {
        SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(assetCustomer.getId())){
        		t=assetCustomerService.getEntity(assetCustomer.getId());
				MyBeanUtils.copyBeanNotNull2Bean(assetCustomer, t);
				assetCustomerService.update(t);
				j.setMsg("Successfully modified");
				dataUserLogService.insertDataUserLog(su.getId(), "Asset Managerment", "Edit Customer","Edit Customer(SN="+ assetCustomer.getSn()+",Name="+(StringUtils.isNotEmpty(assetCustomer.getName())?assetCustomer.getName():"")+")");
			}else{
	            assetCustomerService.save(assetCustomer);
	            j.setMsg("Added successfully");
	            dataUserLogService.insertDataUserLog(su.getId(), "Asset Managerment", "Add Customer","Add Customer(SN="+ assetCustomer.getSn()+",Name="+(StringUtils.isNotEmpty(assetCustomer.getName())?assetCustomer.getName():"")+")");
			}
        //编辑后更新页面显示
        j.put("customer",assetCustomerService.getEntity(assetCustomer.getId()));
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg("Abnormal operation");
        }
        return j;
    }
	
	
	
	/**
	 * 校验电表、或者客户sn是否唯一
	 */
	@RequestMapping(value = "isExist")
	@ResponseBody
	public AjaxJson isExist(AssetCustomer customer,HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		long l=assetCustomerService.getCount(customer);
		if(l>0) {
			j.setSuccess(false);
		}
		return j;
	}
	
	
	@RequestMapping(value = "getAssetCustomer")
	@ResponseBody
	public AjaxJson getAssetCustomer(String id,HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			AssetCustomer customer=assetCustomerService.getEntity(id);
			j.put("customer", customer);
		}catch(Exception ex) {
			ex.printStackTrace();
			j.setSuccess(false);
		}
		return j;
	}
}