package com.clou.esp.hes.app.web.model.report;

import java.math.BigDecimal;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.clou.esp.hes.app.web.validation.ValidGroup2;
import com.power7000g.core.excel.Excel;


/**
 * @ClassName: ImportExportReport
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年10月18日 上午9:39:25
 *
 */
public class ImportExportReport extends BaseEntity {

	private static final long serialVersionUID = 1L;
	
	private String orgId; // 机构Id
	@Excel(name = "Organization", width = 30,groups = {ValidGroup1.class,ValidGroup2.class})
	private String orgName; // 机构名称
	@Excel(name = "Entity Name", width = 30)
	private String entityName;
	@Excel(name = "Meter SN", width = 30, groups = ValidGroup2.class)
	private String meterSn; // 表 SN
	
	@Excel(name = "Time Type", width = 30,groups = {ValidGroup1.class,ValidGroup2.class})
	private String timeType; // 时间类型，1：日 2:月
	@Excel(name = "Time", width = 30,groups = {ValidGroup1.class,ValidGroup2.class})
	private String time; // 时标
	@Excel(name = "Value", width = 30,groups = {ValidGroup1.class,ValidGroup2.class})
	private BigDecimal value; // 值
	@Excel(name = "Miss Data", width = 30,groups = {ValidGroup1.class,ValidGroup2.class})
	private String missData; // 值
	
	private String dataType; // 数据类型，1:线损  2:供电量  3:售电量
	private String meterId; // 表 Id
	private String entityType; 
	private String calcName;
	private String calcObjId;
	/**percent*/
	private BigDecimal percent;
	
	
	public BigDecimal getPercent() {
		return percent;
	}
	public void setPercent(BigDecimal percent) {
		this.percent = percent;
	}
	public String getCalcObjId() {
		return calcObjId;
	}
	public void setCalcObjId(String calcObjId) {
		this.calcObjId = calcObjId;
	}
	public String getCalcName() {
		return calcName;
	}
	public void setCalcName(String calcName) {
		this.calcName = calcName;
	}
	public String getEntityType() {
		return entityType;
	}
	public void setEntityType(String entityType) {
		this.entityType = entityType;
	}
	public String getEntityName() {
		return entityName;
	}
	public void setEntityName(String entityName) {
		this.entityName = entityName;
	}
	public String getOrgId() {
		return orgId;
	}
	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getDataType() {
		return dataType;
	}
	public void setDataType(String dataType) {
		this.dataType = dataType;
	}
	public String getTimeType() {
		return timeType;
	}
	public void setTimeType(String timeType) {
		this.timeType = timeType;
	}
	public String getTime() {
		return time;
	}
	public void setTime(String time) {
		this.time = time;
	}
	
	public BigDecimal getValue() {
		return value;
	}
	public void setValue(BigDecimal value) {
		this.value = value;
	}
	public String getMeterId() {
		return meterId;
	}
	public void setMeterId(String meterId) {
		this.meterId = meterId;
	}
	public String getMeterSn() {
		return meterSn;
	}
	public void setMeterSn(String meterSn) {
		this.meterSn = meterSn;
	}
	public String getMissData() {
		return missData;
	}
	public void setMissData(String missData) {
		this.missData = missData;
	}

}
