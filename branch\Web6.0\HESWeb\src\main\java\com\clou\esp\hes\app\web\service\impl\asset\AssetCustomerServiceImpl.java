/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetCustomer{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：赵久霆
 * 创建于：2018-11-13 06:49:07
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.service.impl.asset;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.clou.esp.hes.app.web.dao.asset.AssetCustomerDao;
import com.clou.esp.hes.app.web.model.asset.AssetCustomer;
import com.clou.esp.hes.app.web.model.dict.DictCustomerIndustry;
import com.clou.esp.hes.app.web.model.dict.DictCustomerType;
import com.clou.esp.hes.app.web.service.asset.AssetCustomerService;
import com.clou.esp.hes.app.web.service.impl.common.CommonServiceImpl;

@Component
@Service("assetCustomerService")
public class AssetCustomerServiceImpl  extends CommonServiceImpl<AssetCustomer>  implements AssetCustomerService {

	@Resource
	private AssetCustomerDao assetCustomerDao;
	
	@Autowired
    public void setCommonService() {
        super.setCommonService(assetCustomerDao);
    }
	@SuppressWarnings("rawtypes")
	public AssetCustomerServiceImpl() {}
	
	
	@Override
	public List<DictCustomerIndustry> getDictCustomerIndustry() {
		return assetCustomerDao.getDictCustomerIndustry();
	}
	@Override
	public List<DictCustomerType> getDictCustomerType() {
		return assetCustomerDao.getDictCustomerType();
	}
	
	
}