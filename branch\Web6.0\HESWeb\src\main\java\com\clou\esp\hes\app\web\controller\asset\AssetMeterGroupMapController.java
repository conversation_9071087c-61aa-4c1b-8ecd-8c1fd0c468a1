/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeterGroupMap{ } 
 * 
 * 摘    要： assetMeterGroupMap
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-01-25 07:22:09
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.asset;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;

import com.clou.esp.hes.app.web.model.asset.AssetMeterGroupMap;
import com.clou.esp.hes.app.web.service.asset.AssetMeterGroupMapService;

/**
 * <AUTHOR>
 * @时间：2018-01-25 07:22:09
 * @描述：assetMeterGroupMap类
 */
@Controller
@RequestMapping("/assetMeterGroupMapController")
public class AssetMeterGroupMapController extends BaseController{

 	@Resource
    private AssetMeterGroupMapService assetMeterGroupMapService;

	/**
	 * 跳转到assetMeterGroupMap列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/asset/assetMeterGroupMapList");
    }

	/**
	 * 跳转到assetMeterGroupMap新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "assetMeterGroupMap")
	public ModelAndView assetMeterGroupMap(AssetMeterGroupMap assetMeterGroupMap,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(assetMeterGroupMap.getId())){
			try {
                assetMeterGroupMap=assetMeterGroupMapService.getEntity(assetMeterGroupMap.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("assetMeterGroupMap", assetMeterGroupMap);
		}
		return new ModelAndView("/asset/assetMeterGroupMap");
	}


	/**
	 * assetMeterGroupMap查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=assetMeterGroupMapService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除assetMeterGroupMap信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(AssetMeterGroupMap assetMeterGroupMap,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(assetMeterGroupMapService.deleteById(assetMeterGroupMap.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存assetMeterGroupMap信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })AssetMeterGroupMap assetMeterGroupMap,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        AssetMeterGroupMap t=new  AssetMeterGroupMap();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(assetMeterGroupMap.getId())){
        	t=assetMeterGroupMapService.getEntity(assetMeterGroupMap.getId());
			MyBeanUtils.copyBeanNotNull2Bean(assetMeterGroupMap, t);
				assetMeterGroupMapService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            assetMeterGroupMapService.save(assetMeterGroupMap);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
	
	
	
}