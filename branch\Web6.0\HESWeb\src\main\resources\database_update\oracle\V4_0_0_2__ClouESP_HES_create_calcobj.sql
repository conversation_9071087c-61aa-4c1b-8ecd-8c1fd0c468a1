/*----计算对象实体表  --*/
CREATE TABLE ASSET_CALC_OBJ 
(	
	"ID" VARCHAR2(32 BYTE) not null, 
	"TYPE" NUMBER, 
	"NAME" VARCHAR2(64 BYTE), 
	"ORG_ID" VARCHAR2(32 BYTE), 
	"ENTITY_TYPE" NUMBER, 
	"ENTITY_ID" VARCHAR2(32 BYTE), 
	"TV_TYPE" NUMBER,
	primary key (id)
) ;
COMMENT ON COLUMN ASSET_CALC_OBJ."TYPE" IS '1:线损  2:供电量  3:售电量';
COMMENT ON COLUMN ASSET_CALC_OBJ."TV_TYPE" IS '1：日 2:月';
CREATE INDEX ASSET_LINELOSS_OBJ_INDEX_TYPE ON "ASSET_CALC_OBJ" ("ENTITY_ID");
CREATE INDEX ASSET_LINELOSS_OBJ_INDEX_ORG ON "ASSET_CALC_OBJ" ("ORG_ID");
 
 /*----计算对象明细表--*/
CREATE TABLE ASSET_CALC_OBJ_MAP 
(	
	"ID" VARCHAR2(32 BYTE) not null, 
	"TYPE" NUMBER not null, 
	"METERING_ID" VARCHAR2(32 BYTE) not null, 
	"METERING_TYPE" NUMBER, 
	"DATAITEM_ID" VARCHAR2(64 BYTE) not null,
	primary key (id,TYPE,METERING_ID,DATAITEM_ID)
) ;
COMMENT ON COLUMN ASSET_CALC_OBJ_MAP."TYPE" IS '1:供入 2:供出';
COMMENT ON COLUMN ASSET_CALC_OBJ_MAP."METERING_TYPE" IS '1:METER; 2:CALC_OJB';

 /*----计算对象日数据--*/
CREATE TABLE DATA_CALC_OBJ 
(	
	"CALC_OBJ_ID" VARCHAR2(32 BYTE) not null,  
	"TV" DATE not null,  
	"OUT_TOTAL" NUMBER, 
	"IN_TOTAL" NUMBER, 
	"CALC_VALUE" NUMBER(10,2), 
	"MISS_DATA" NUMBER, 
	"UPDATE_TV" DATE,
	primary key (CALC_OBJ_ID,TV)
) ;
CREATE INDEX DATA_LINELOSS_DAILY_INDEX1 ON "DATA_CALC_OBJ" ("UPDATE_TV") ;

 /*----interval data 日数据--*/
CREATE TABLE DATA_MD_INTERVAL_DAILY
(	
	"DEVICE_ID" VARCHAR2(32 BYTE) not null, 
	"TV" DATE not null, 
	"VALUE1" VARCHAR2(20 BYTE), 
	"VALUE2" VARCHAR2(20 BYTE), 
	"VALUE3" VARCHAR2(20 BYTE), 
	"VALUE4" VARCHAR2(20 BYTE), 
	"VALUE5" VARCHAR2(20 BYTE), 
	"VALUE6" VARCHAR2(20 BYTE), 
	"VALUE7" VARCHAR2(20 BYTE), 
	"VALUE8" VARCHAR2(20 BYTE), 
	"VALUE9" VARCHAR2(20 BYTE), 
	"VALUE10" VARCHAR2(20 BYTE), 
	"VALUE11" VARCHAR2(20 BYTE), 
	"VALUE12" VARCHAR2(20 BYTE), 
	"VALUE13" VARCHAR2(20 BYTE), 
	"VALUE14" VARCHAR2(20 BYTE), 
	"VALUE15" VARCHAR2(20 BYTE), 
	"VALUE16" VARCHAR2(20 BYTE), 
	"VALUE17" VARCHAR2(20 BYTE), 
	"VALUE18" VARCHAR2(20 BYTE), 
	"VALUE19" VARCHAR2(20 BYTE), 
	"VALUE20" VARCHAR2(20 BYTE), 
	"UPDATE_TV" DATE,
	primary key (DEVICE_ID,TV)
)
partition by range (TV) interval (NUMTOYMINTERVAL(1,'MONTH'))
(
  partition PART_01 values less than (TO_DATE(' 2018-01-01', 'SYYYY-MM-DD', 'NLS_CALENDAR=GREGORIAN'))
);
CREATE INDEX DATA_MD_INTERVAL_DATA_INDEX2 ON "DATA_MD_INTERVAL_DAILY" ("UPDATE_TV");

 /*----interval data 月数据--*/
CREATE TABLE DATA_MD_INTERVAL_MONTHLY
(	
	"DEVICE_ID" VARCHAR2(32 BYTE) not null, 
	"TV" DATE not null, 
	"VALUE1" VARCHAR2(20 BYTE), 
	"VALUE2" VARCHAR2(20 BYTE), 
	"VALUE3" VARCHAR2(20 BYTE), 
	"VALUE4" VARCHAR2(20 BYTE), 
	"VALUE5" VARCHAR2(20 BYTE), 
	"VALUE6" VARCHAR2(20 BYTE), 
	"VALUE7" VARCHAR2(20 BYTE), 
	"VALUE8" VARCHAR2(20 BYTE), 
	"VALUE9" VARCHAR2(20 BYTE), 
	"VALUE10" VARCHAR2(20 BYTE), 
	"VALUE11" VARCHAR2(20 BYTE), 
	"VALUE12" VARCHAR2(20 BYTE), 
	"VALUE13" VARCHAR2(20 BYTE), 
	"VALUE14" VARCHAR2(20 BYTE), 
	"VALUE15" VARCHAR2(20 BYTE), 
	"VALUE16" VARCHAR2(20 BYTE), 
	"VALUE17" VARCHAR2(20 BYTE), 
	"VALUE18" VARCHAR2(20 BYTE), 
	"VALUE19" VARCHAR2(20 BYTE), 
	"VALUE20" VARCHAR2(20 BYTE), 
	"UPDATE_TV" DATE,
	primary key (DEVICE_ID,TV)
);


CREATE TABLE DICT_REPORT 
(	
	"ID" VARCHAR2(32 BYTE) not null,  
	"REPORTNAME" VARCHAR2(64 BYTE) not null,  
	"SORT_ID" NUMBER(11,0), 
	"FUNCTIONURL" VARCHAR2(64 BYTE), 
	"PARENT_ID" VARCHAR2(20 BYTE),
	primary key (ID)
) ;

Insert into DICT_REPORT (ID,REPORTNAME,SORT_ID,FUNCTIONURL,PARENT_ID) values ('100001','Billing Reports',1,null,'100');
Insert into DICT_REPORT (ID,REPORTNAME,SORT_ID,FUNCTIONURL,PARENT_ID) values ('200001','Line Loss - Organization',1,'dictReportController/lineLossOrgList.do','200');
Insert into DICT_REPORT (ID,REPORTNAME,SORT_ID,FUNCTIONURL,PARENT_ID) values ('200002','Line Loss - Single Object',2,'dictReportController/lineLossSingleObjectList.do','200');
Insert into DICT_REPORT (ID,REPORTNAME,SORT_ID,FUNCTIONURL,PARENT_ID) values ('100','Billing Reports',1,null,'0');
Insert into DICT_REPORT (ID,REPORTNAME,SORT_ID,FUNCTIONURL,PARENT_ID) values ('200','Line Loss Reports',2,null,'0');