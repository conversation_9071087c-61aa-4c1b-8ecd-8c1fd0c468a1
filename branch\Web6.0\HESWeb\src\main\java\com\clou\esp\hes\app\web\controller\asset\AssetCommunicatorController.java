/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetCommunicator{ } 
 * 
 * 摘    要： GPRS Module
DCU
Gateway
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-06 08:04:54
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.asset;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSON;
import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.UciInterfaceUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;
import com.power7000g.core.util.redis.JedisUtils;

import clouesp.hes.core.uci.soap.custom.asset_refresh.AssetRefreshPort;
import clouesp.hes.core.uci.soap.custom.asset_refresh.RefreshMessage;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import com.clou.esp.hes.app.web.service.asset.AssetRouterService;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.model.asset.AssetCalcObj;
import com.clou.esp.hes.app.web.model.asset.AssetCommunicator;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.asset.AssetRouter;
import com.clou.esp.hes.app.web.model.asset.ImportAssetGprsMeter;
import com.clou.esp.hes.app.web.model.asset.VendMeterInitialCreditAmount;
import com.clou.esp.hes.app.web.model.dict.DictDeviceModel;
import com.clou.esp.hes.app.web.service.asset.AssetCommunicatorService;
import com.clou.esp.hes.app.web.service.asset.AssetLineManagementService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.data.DataIntegrityService;
import com.clou.esp.hes.app.web.service.data.DataUserLogService;
import com.clou.esp.hes.app.web.service.dict.DictDeviceModelService;
import com.clou.esp.hes.app.web.service.system.SysServiceAttributeService;

/**
 * <AUTHOR> @时间：2017-11-06 08:04:54
 * @描述：GPRS Module DCU Gateway类
 */
@Controller
@RequestMapping("/assetCommunicatorController")
public class AssetCommunicatorController extends BaseController {

	@Resource
	private AssetLineManagementService assetLineManagementService;
	@Resource
	private AssetCommunicatorService assetCommunicatorService;
	@Resource
	private AssetMeterService assetMeterService;
	@Resource
	private DataUserLogService dataUserLogService;
	@Resource
	private SysServiceAttributeService sysServiceAttributeService;
	@Resource
	private DataIntegrityService dataIntegrityService;
	@Resource
	private DictDeviceModelService dictDeviceModelService;
	@Resource
	private AssetRouterService assetRouterService;

	/**
	 * 跳转到GPRS Module DCU Gateway列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "list")
	public ModelAndView list(HttpServletRequest request, Model model) {
		return new ModelAndView("/asset/assetCommunicatorList");
	}

	/**
	 * 跳转到GPRS Module DCU Gateway新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "assetCommunicator")
	public ModelAndView assetCommunicator(AssetCommunicator assetCommunicator, HttpServletRequest request,
			Model model) {
		if (StringUtil.isNotEmpty(assetCommunicator.getId())) {
			try {
				assetCommunicator = assetCommunicatorService.getEntity(assetCommunicator.getId());
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			model.addAttribute("assetCommunicator", assetCommunicator);
		}
		return new ModelAndView("/asset/assetCommunicator");
	}

	/**
	 * GPRS Module DCU Gateway查询分页
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
	@ResponseBody
	public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo, HttpServletRequest request) {
		TagUtil.setFieldValue(jqGridSearchTo, request); // 取传递的参数到JqGridSearchTo
		JqGridResponseTo j = null;
		try {
			j = assetCommunicatorService.getForJqGrid(jqGridSearchTo);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return j;
	}

	/**
	 * 删除GPRS Module DCU Gateway信息
	 * 
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "del")
	@ResponseBody
	public AjaxJson del(AssetCommunicator assetCommunicator, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			SysUser su = TokenManager.getToken();
			if (assetCommunicatorService.deleteById(assetCommunicator.getId()) > 0) {
				j.setMsg("删除成功");
			} else {
				j.setSuccess(false);
				j.setMsg("删除失败");
			}
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg(MutiLangUtil.doMutiLang("system.systemException"));
		}
		return j;
	}

	/**
	 * 保存GPRS Module DCU Gateway信息
	 * 
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "save")
	@ResponseBody
	public AjaxJson save(@Validated(value = { ValidGroup1.class }) AssetCommunicator assetCommunicator,
			BindingResult bindingResult, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		AssetCommunicator t = new AssetCommunicator();
		try {
			SysUser su = TokenManager.getToken();
			if (StringUtil.isNotEmpty(assetCommunicator.getId())) {
				t = assetCommunicatorService.getEntity(assetCommunicator.getId());
				MyBeanUtils.copyBeanNotNull2Bean(assetCommunicator, t);
				assetCommunicatorService.update(t);
				j.setMsg("修改成功");

			} else {
				assetCommunicatorService.save(assetCommunicator);
				j.setMsg("创建成功");

			}
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg(MutiLangUtil.doMutiLang("system.systemException"));
		}
		return j;
	}

	/**
	 * 根据事件类型获取事件
	 * 
	 * @param groupId
	 * @return
	 */
	@RequestMapping(value = "getSnName")
	@ResponseBody
	public AjaxJson getSnName(String lineLossObjectType, String search, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();

		Map<String, Object> params = new HashMap<String, Object>();
		params.put("lineLossObjectType", lineLossObjectType);
		List<Map<String, Object>> diList = assetCommunicatorService.getSnNameByMaps(params);

		List<String> snNameList = new ArrayList<String>();

		if (diList != null && diList.size() > 0) {
			for (Map<String, Object> di : diList) {
				String snName = (String) di.get("NAME");
				if (StringUtils.isNotEmpty(snName) && StringUtils.isNotEmpty(search) && search != "") {
					if (snName.indexOf(search) > -1) {
						snNameList.add(snName);
					}
				} else {
					snNameList.add(snName);
				}

			}
		}

		Map<String, Object> attributes = new HashMap<String, Object>();
		attributes.put("snNameList", snNameList);
		j.setAttributes(attributes);
		return j;
	}

	/**
	 * 根据计算类型获取计算对象事件
	 * 
	 * @param groupId
	 * @return
	 */
	@RequestMapping(value = "getCalName")
	@ResponseBody
	public AjaxJson getCalName(Integer lineLossObjectType, String search, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();

		Map<String, Object> params = new HashMap<String, Object>();
		params.put("lineLossObjectType", lineLossObjectType);
		AssetCalcObj assetCalcObj = new AssetCalcObj();

		assetCalcObj.setEntityType(lineLossObjectType);
		assetCalcObj.setName(search);
		List<AssetCalcObj> assetCalcObjList = assetLineManagementService.getListForCalObj(assetCalcObj);

		List<String> snNameList = new ArrayList<String>();

		for (AssetCalcObj assetCalcObj1 : assetCalcObjList) {
			snNameList.add(assetCalcObj1.getName());
		}

		Map<String, Object> attributes = new HashMap<String, Object>();
		attributes.put("snNameList", snNameList);
		j.setAttributes(attributes);
		return j;
	}

	/**
	 * 根据事件类型获取事件
	 * 
	 * @param groupId
	 * @return
	 */
	@RequestMapping(value = "getCommReplace")
	@ResponseBody
	public AjaxJson getCommReplace(String lineLossObjectType, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		List<AssetCommunicator> communicators = assetCommunicatorService.getListNoGprs();
		String commReplace = RoletoJson.listToReplaceStr(communicators, "id", "sn");
		j.put("commReplace", commReplace);
		return j;
	}

	/**
	 * 根据事件类型获取事件
	 * 
	 * @param groupId
	 * @return
	 */
	@RequestMapping(value = "getMeterReplace")
	@ResponseBody
	public AjaxJson getMeterReplace(String id, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		AssetMeter assetMeter = new AssetMeter();
		assetMeter.setCommunicatorId(id);
		List<AssetMeter> meters = assetMeterService.getList(assetMeter);
		String commReplace = RoletoJson.listToReplaceStr(meters, "sn", "sn");
		j.put("commReplace", commReplace);
		return j;
	}

	@Transactional
	@RequestMapping(value = "saveRelateMeterToDCU")
	@ResponseBody
	public AjaxJson saveRelateMeterToDCU(HttpServletRequest request, String meterIds, String searchType,
			String communicatorId, String meterMaps) {
		AjaxJson j = new AjaxJson();
		try {
			com.alibaba.fastjson.JSONArray curArray = null;
			SysUser su = TokenManager.getToken();
			List<String> relateIdList = new ArrayList<String>();
			AssetCommunicator dcu = new AssetCommunicator();
			if (StringUtils.isNotEmpty(meterMaps)) {
				curArray = JSON.parseArray(meterMaps);
				if (curArray != null) {
					AssetCommunicator communicator = assetCommunicatorService.getEntity(communicatorId);
					for (int i = 0; i < curArray.size(); i++) {
						com.alibaba.fastjson.JSONObject jsonRestTemp = curArray.getJSONObject(i);
						String meterId = jsonRestTemp.getString("id");
						relateIdList.add(meterId);
					}
					dcu.setId(communicatorId);
					dcu.setMeterIdList(relateIdList);
					if (searchType.equals("1")) {
						// 从asset_meter表中查询已存档的正式表计档案
						assetCommunicatorService.updateRelateDCU(dcu);
					} else {
						List<AssetMeter> meterList = new ArrayList<AssetMeter>();
						meterList = assetMeterService.getByIds(relateIdList);
						if (meterList.size() > 0) {
							j.setErrorMsg(MutiLangUtil.doMutiLang("deviceList.deviceNumExists"));
							return j;
						}

						// 将PREPARE表的记录移到asset_meter表中
						assetCommunicatorService.relatePreMeterToDCU(dcu);
						// 删除PREPARE表中的记录
						assetMeterService.deletePrepareMeter(relateIdList);

						// 转移过来的记录都需要设置初始化信用
						for (AssetMeter assetMeter : meterList) {
							DictDeviceModel deviceModel = dictDeviceModelService.getEntity(assetMeter.getModel());
							VendMeterInitialCreditAmount initCreditAmount = new VendMeterInitialCreditAmount(
									assetMeter.getId(), deviceModel.getInitCreditAmount(), null, 0, null, new Date(),
									"hes新增电表");
							this.assetMeterService.insertCredit(initCreditAmount);
						}
					}
					AssetRefreshPort arp = (AssetRefreshPort) UciInterfaceUtil.getInterface("AssetRefreshPort",
							AssetRefreshPort.class, sysServiceAttributeService);
					List<RefreshMessage> msgs = new ArrayList<RefreshMessage>();
					RefreshMessage meterRefresh = new RefreshMessage();
					meterRefresh.setType("meter");
					meterRefresh.setOpType("update");
					meterRefresh.getIds().addAll(relateIdList);
					msgs.clear();
					msgs.add(meterRefresh);
					RefreshMessage dcuRefresh = new RefreshMessage();
					dcuRefresh.setType("communicator");
					dcuRefresh.setOpType("update");
					dcuRefresh.getIds().add(communicatorId);
					msgs.add(dcuRefresh);
					try {
						if (!arp.refresh(msgs)) {
							System.out.println("刷新失败" + communicatorId);
						}
					} catch (Exception e) {
						e.printStackTrace();
						j.setErrorMsg(MutiLangUtil.doMutiLang("system.UCIException"));
						return j;
					}

					j.setMsg(MutiLangUtil.doMutiLang("system.addSucc"));
					dataUserLogService.insertDataUserLog(su.getId(), "Communicator", "Edit relate meter",
							"communicator (Name=" + communicator.getName() + ")");
				}
			}
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			j.setSuccess(false);
			j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
			return j;
		}

		return j;
	}

	/**
	 * 精确查找设备
	 * 
	 * @param deviceSn
	 * @param deviceType
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "getCommnunicatorDevice")
	@ResponseBody
	public AjaxJson getAssetDevice(String deviceSn, HttpServletRequest request, Model model) {

		AjaxJson j = new AjaxJson();
		if (StringUtil.isNotEmpty(deviceSn)) {
			String id="";
			try {
				AssetCommunicator entity = new AssetCommunicator();
				entity.setSn(deviceSn);
				AssetCommunicator assetCommunicator = assetCommunicatorService.get(entity);
				if (assetCommunicator != null) {
					id = assetCommunicator.getId();
				}
				if(StringUtil.isEmpty(id)){
					j.setErrorMsg(MutiLangUtil.doMutiLang("deviceList.noDeviceFound"));
				}else{
					j.setObj(assetCommunicator);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		} else {
			j.setErrorMsg(MutiLangUtil.doMutiLang("deviceList.noDeviceFound"));
		}
		return j;
	}
	
	/**
	 * 批量导入集中器数据信息
	 * @param id
	 * @return
	 */
	@Transactional
	@RequestMapping(value = "batchImportTemplateCommData")
	@ResponseBody
	@SuppressWarnings("unchecked")
	public AjaxJson batchImportTemplateCommData(String uuid,String channel,String schedule,AssetCommunicator entity, HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			SysUser su = TokenManager.getToken();
			// 从redis中读取出之前文件导入的电表
			//获取缓存的导入文件的信息
			List<ImportAssetGprsMeter> comList = (List<ImportAssetGprsMeter>) JedisUtils.getObject(uuid);
			
			// 做为批次导入
			if(comList.size() > 0){
				int num1 = 0; 
				if(comList.size() > 20){	//把数据均分成二十份
					num1 = comList.size() / 20;
				}else{
					num1 = comList.size();
				}
				int num2 = 0;						//代表导入进度
				// 根据用户选中的id值，将这些集中器插入到数据库中，同时使用entity中的属性填充字段。
				for (int i = 0; i < comList.size(); i++) {
					JSONObject obj = new JSONObject();
					obj.put("commAddress", comList.get(i).getComm_Address());
					
					// 集中器信息填充
					AssetCommunicator assetCommunicator = new AssetCommunicator();
					assetCommunicator.setRemoveFlag(0);
					assetCommunicator.setSn(comList.get(i).getSerial_Number());
					assetCommunicator.setName(comList.get(i).getSerial_Number());
					assetCommunicator.setUtilityId(su.getUtilityId());
					assetCommunicator.setOrgId(entity.getOrgId());
					assetCommunicator.setModel(entity.getModel());
					assetCommunicator.setManufacturer(entity.getManufacturer());
					assetCommunicator.setNetworkIp(entity.getNetworkIp());
					assetCommunicator.setNetworkPort(entity.getNetworkPort());
					assetCommunicator.setPass(StringUtil.isNotEmpty(entity.getPass()) ? entity.getPass() : "00000001");
					assetCommunicator.setDeviceType(entity.getDeviceType());
					assetCommunicator.setMac(comList.get(i).getLogical_address());
					assetCommunicator.setComType(entity.getComType());
					assetCommunicator.setFwVersion(StringUtil.isNotEmpty(entity.getFwVersion()) ? entity.getFwVersion() : "");
					assetCommunicator.setSimNum(entity.getSimNum());
					assetCommunicator.setIsEncrypt(entity.getIsEncrypt());
					assetCommunicator.setAuthType(entity.getAuthType());
					assetCommunicator.setHlsAk(entity.getHlsAk());
					assetCommunicator.setHlsEk(entity.getHlsEk());
					assetCommunicator.setListenMode(entity.getListenMode());
					
					AssetRefreshPort arp = (AssetRefreshPort)UciInterfaceUtil.getInterface("AssetRefreshPort", AssetRefreshPort.class, sysServiceAttributeService);
					List<RefreshMessage> msgs = new ArrayList<RefreshMessage>();
					
					// 校验
					// 校验1： 判断集中器是否存在
					// 校验2： 判断MAC是否已经存在
					AssetCommunicator cond = new AssetCommunicator();
					cond.setSn(assetCommunicator.getSn());
					cond.setMac(assetCommunicator.getMac());
					List<AssetCommunicator> existList = assetCommunicatorService.getList(cond);
					// 记录已经存在，不做插入操作
					if (CollectionUtils.isNotEmpty(existList)) {
						// 暂时不做任何操作
					} else {
						// 插入到数据库中
						String deviceId=(String) assetCommunicatorService.save(assetCommunicator);
						// 添加Router关联
						AssetRouter ar=new AssetRouter();
						ar.setCommunicatorId(deviceId);
						ar.setChannelId(channel);
						ar.setScheduleId(schedule);
						assetRouterService.save(ar);
						
						RefreshMessage refMsg=new RefreshMessage();
						refMsg.setType("communicator");
						refMsg.setOpType("add");
						refMsg.getIds().add(deviceId);
						msgs.clear();
						msgs.add(refMsg);
						dataUserLogService.insertDataUserLog(su.getId(), "Asset Managerment", "Add Communicator", "[Add Communicator:DCU SN="+assetCommunicator.getSn()+",Logic Name="+assetCommunicator.getMac()+"]");
						try{
							if(!arp.refresh(msgs)){
								System.out.println("刷新失败"+deviceId);
							}
						} catch (Exception e) {
							e.printStackTrace();
							j.setErrorMsg(MutiLangUtil.doMutiLang("system.UCIException"));
							return j;
						}
					}
					// 更新电表导入进度，保存至Redis中
					if(comList.size() > 20){
						if(i % num1 == 0){
							++num2;
							JedisUtils.set(su.getId() + "importComProgress", String.valueOf(num2 * 5), 3600);
						}
					}else{
						++num2;
						JedisUtils.set(su.getId() + "importComProgress", String.valueOf(num2 * 5), 3600);
					}
				}
			}
		} catch (Exception ex) {
			ex.printStackTrace();
			j.setErrorMsg(MutiLangUtil.doMutiLang("system.systemException"));
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return j;
		}
		j.setMsg(MutiLangUtil.doMutiLang("system.operSucce"));
		return j;
	}
	
	/**
	 * 获取导入电表的进度
	 * @param assetMeter
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getBatchImportComProgress")
	@ResponseBody
	public AjaxJson getBatchImportComProgress(HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			SysUser su = TokenManager.getToken();
			String progress = JedisUtils.get(su.getId() + "importComProgress");
			if(StringUtil.isNotEmpty(progress)){
				j.setObj(progress);
			}else{
				j.setObj(0);
			}
		} catch (Exception e) {
			e.printStackTrace();     
			j.setSuccess(false);
			j.setMsg(MutiLangUtil.doMutiLang("system.abnoOpera"));
		}
		return j;
	}
}