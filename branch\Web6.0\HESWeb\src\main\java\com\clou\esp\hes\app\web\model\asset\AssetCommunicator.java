/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetCommunicator{ } 
 * 
 * 摘    要： GPRS Module
DCU
Gateway
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-06 08:04:54
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.asset;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import com.clou.esp.hes.app.web.model.common.BaseEntity;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.excel.Excel;

public class AssetCommunicator extends BaseEntity {

	/**
	 * 意义，目的和功能，以及被用到的地方<br>
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public AssetCommunicator() {
	}

	/** sn */
	@Excel(name = "Serial Number", width = 30, groups = ValidGroup1.class)
	private java.lang.String sn;
	@Excel(name = "Manufacturer", width = 30, groups = ValidGroup1.class)
	private String manufacturerName;
	@Excel(name = "Model", width = 30, groups = ValidGroup1.class)
	private String modelName;
	@Excel(name = "Current Version", width = 30, groups = ValidGroup1.class)
	private String fwVersion;
	/** name */
	private java.lang.String name;
	/** vendorId */
	private java.lang.String utilityId;
	/** orgId */
	private java.lang.String orgId;
	/** model */
	private java.lang.String model;
	/** manufacturer */
	private java.lang.String manufacturer;
	/** networkIp */
	private java.lang.String networkIp;
	/** networkPort */
	private java.lang.Integer networkPort;
	/** pass */
	private java.lang.String pass;
	/** 201、GPRS 202:: GW 203: DCU */
	private java.lang.Integer deviceType;
	/** mac */
	private java.lang.String mac;
	private String deviceTypeIcon;
	private String comType;
	private String currentVesionArray[];
	
	private Integer removeFlag;
	private String  	simNum;
	
	private Integer 	isEncrypt;
	private String 		hlsAk;
	private String 		hlsEk;
	private String 		authType;


	private String comStatus;
	private String ipAddr;
	private String ipPort;
	private Date updateTv;
	private String communicator;
	 
	private int listenMode;
	
	/**是否收藏*/
	private Boolean isCollect;
	
	/**
	 * 是否是DCU
	 */
	private String dcuType;
	
	/**
	 * 关联的Meter
	 */
	private List<String> meterIdList;
	
	
	private Date createTime;
	private String createTimeStr;
	
	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getCreateTimeStr() {
		return createTimeStr;
	}

	public void setCreateTimeStr(String createTimeStr) {
		this.createTimeStr = createTimeStr;
	}

	public String getDcuType() {
		return dcuType;
	}

	public void setDcuType(String dcuType) {
		this.dcuType = dcuType;
	}

	/**
	 * sn
	 * 
	 * @return the value of ASSET_COMMUNICATOR.SN
	 * @mbggenerated 2017-11-06 08:04:54
	 */
	public java.lang.String getSn() {
		return sn;
	}

	public int getListenMode() {
		return listenMode;
	}

	public void setListenMode(int listenMode) {
		this.listenMode = listenMode;
	}

	/**
	 * sn
	 * 
	 * @param sn
	 *            the value for ASSET_COMMUNICATOR.SN
	 * @mbggenerated 2017-11-06 08:04:54
	 */
	public void setSn(java.lang.String sn) {
		this.sn = sn;
	}

	/**
	 * name
	 * 
	 * @return the value of ASSET_COMMUNICATOR.NAME
	 * @mbggenerated 2017-11-06 08:04:54
	 */
	public java.lang.String getName() {
		return name;
	}

	/**
	 * name
	 * 
	 * @param name
	 *            the value for ASSET_COMMUNICATOR.NAME
	 * @mbggenerated 2017-11-06 08:04:54
	 */
	public void setName(java.lang.String name) {
		this.name = name;
	}

	/**
	 * vendorId
	 * 
	 * @return the value of ASSET_COMMUNICATOR.VENDOR_ID
	 * @mbggenerated 2017-11-06 08:04:54
	 */
	public java.lang.String getUtilityId() {
		return utilityId;
	}

	/**
	 * vendorId
	 * 
	 * @param vendorId
	 *            the value for ASSET_COMMUNICATOR.VENDOR_ID
	 * @mbggenerated 2017-11-06 08:04:54
	 */
	public void setUtilityId(java.lang.String utilityId) {
		this.utilityId = utilityId;
	}

	/**
	 * orgId
	 * 
	 * @return the value of ASSET_COMMUNICATOR.ORG_ID
	 * @mbggenerated 2017-11-06 08:04:54
	 */
	public java.lang.String getOrgId() {
		return orgId;
	}

	/**
	 * orgId
	 * 
	 * @param orgId
	 *            the value for ASSET_COMMUNICATOR.ORG_ID
	 * @mbggenerated 2017-11-06 08:04:54
	 */
	public void setOrgId(java.lang.String orgId) {
		this.orgId = orgId;
	}

	/**
	 * model
	 * 
	 * @return the value of ASSET_COMMUNICATOR.MODEL
	 * @mbggenerated 2017-11-06 08:04:54
	 */
	public java.lang.String getModel() {
		return model;
	}

	/**
	 * model
	 * 
	 * @param model
	 *            the value for ASSET_COMMUNICATOR.MODEL
	 * @mbggenerated 2017-11-06 08:04:54
	 */
	public void setModel(java.lang.String model) {
		this.model = model;
	}

	/**
	 * manufacturer
	 * 
	 * @return the value of ASSET_COMMUNICATOR.MANUFACTURER
	 * @mbggenerated 2017-11-06 08:04:54
	 */
	public java.lang.String getManufacturer() {
		return manufacturer;
	}

	/**
	 * manufacturer
	 * 
	 * @param manufacturer
	 *            the value for ASSET_COMMUNICATOR.MANUFACTURER
	 * @mbggenerated 2017-11-06 08:04:54
	 */
	public void setManufacturer(java.lang.String manufacturer) {
		this.manufacturer = manufacturer;
	}

	/**
	 * networkIp
	 * 
	 * @return the value of ASSET_COMMUNICATOR.NETWORK_IP
	 * @mbggenerated 2017-11-06 08:04:54
	 */
	public java.lang.String getNetworkIp() {
		return networkIp;
	}

	/**
	 * networkIp
	 * 
	 * @param networkIp
	 *            the value for ASSET_COMMUNICATOR.NETWORK_IP
	 * @mbggenerated 2017-11-06 08:04:54
	 */
	public void setNetworkIp(java.lang.String networkIp) {
		this.networkIp = networkIp;
	}

	/**
	 * networkPort
	 * 
	 * @return the value of ASSET_COMMUNICATOR.NETWORK_PORT
	 * @mbggenerated 2017-11-06 08:04:54
	 */
	public java.lang.Integer getNetworkPort() {
		return networkPort;
	}

	/**
	 * networkPort
	 * 
	 * @param networkPort
	 *            the value for ASSET_COMMUNICATOR.NETWORK_PORT
	 * @mbggenerated 2017-11-06 08:04:54
	 */
	public void setNetworkPort(java.lang.Integer networkPort) {
		this.networkPort = networkPort;
	}

	/**
	 * pass
	 * 
	 * @return the value of ASSET_COMMUNICATOR.PASS
	 * @mbggenerated 2017-11-06 08:04:54
	 */
	public java.lang.String getPass() {
		return pass;
	}

	/**
	 * pass
	 * 
	 * @param pass
	 *            the value for ASSET_COMMUNICATOR.PASS
	 * @mbggenerated 2017-11-06 08:04:54
	 */
	public void setPass(java.lang.String pass) {
		this.pass = pass;
	}

	/**
	 * 201、GPRS 202:: GW 203: DCU
	 * 
	 * @return the value of ASSET_COMMUNICATOR.DEVICE_TYPE
	 * @mbggenerated 2017-11-06 08:04:54
	 */
	public java.lang.Integer getDeviceType() {
		return deviceType;
	}

	/**
	 * 201、GPRS 202:: GW 203: DCU
	 * 
	 * @param deviceType
	 *            the value for ASSET_COMMUNICATOR.DEVICE_TYPE
	 * @mbggenerated 2017-11-06 08:04:54
	 */
	public void setDeviceType(java.lang.Integer deviceType) {
		this.deviceType = deviceType;
	}

	/**
	 * mac
	 * 
	 * @return the value of ASSET_COMMUNICATOR.MAC
	 * @mbggenerated 2017-11-06 08:04:54
	 */
	public java.lang.String getMac() {
		return mac;
	}

	/**
	 * mac
	 * 
	 * @param mac
	 *            the value for ASSET_COMMUNICATOR.MAC
	 * @mbggenerated 2017-11-06 08:04:54
	 */
	public void setMac(java.lang.String mac) {
		this.mac = mac;
	}

	public AssetCommunicator(java.lang.String sn, java.lang.String name,
			java.lang.String utilityId, java.lang.String orgId,
			java.lang.String model, java.lang.String manufacturer,
			java.lang.String networkIp, java.lang.Integer networkPort,
			java.lang.String pass, java.lang.Integer deviceType,
			java.lang.String mac) {
		super();
		this.sn = sn;
		this.name = name;
		this.utilityId = utilityId;
		this.orgId = orgId;
		this.model = model;
		this.manufacturer = manufacturer;
		this.networkIp = networkIp;
		this.networkPort = networkPort;
		this.pass = pass;
		this.deviceType = deviceType;
		this.mac = mac;
	}

	public String getDeviceTypeIcon() {
		return deviceTypeIcon;
	}

	public void setDeviceTypeIcon(String deviceTypeIcon) {
		this.deviceTypeIcon = deviceTypeIcon;
	}

	public String getModelName() {
		return modelName;
	}

	public void setModelName(String modelName) {
		this.modelName = modelName;
	}

	public String getComType() {
		return comType;
	}

	public void setComType(String comType) {
		this.comType = comType;
	}

	public String getFwVersion() {
		return fwVersion;
	}

	public void setFwVersion(String fwVersion) {
		this.fwVersion = fwVersion;
	}

	public String getManufacturerName() {
		return manufacturerName;
	}

	public void setManufacturerName(String manufacturerName) {
		this.manufacturerName = manufacturerName;
	}

	public String[] getCurrentVesionArray() {
		return currentVesionArray;
	}

	public void setCurrentVesionArray(String[] currentVesionArray) {
		this.currentVesionArray = currentVesionArray;
	}
	

	public Boolean getIsCollect() {
		return isCollect;
	}

	public void setIsCollect(Boolean isCollect) {
		this.isCollect = isCollect;
	}

	public Integer getRemoveFlag() {
		return removeFlag;
	}

	public void setRemoveFlag(Integer removeFlag) {
		this.removeFlag = removeFlag;
	}

	public String getSimNum() {
		return simNum;
	}

	public void setSimNum(String simNum) {
		this.simNum = simNum;
	}

	public Integer getIsEncrypt() {
		return isEncrypt;
	}

	public void setIsEncrypt(Integer isEncrypt) {
		this.isEncrypt = isEncrypt;
	}

	public String getHlsAk() {
		return hlsAk;
	}

	public void setHlsAk(String hlsAk) {
		this.hlsAk = hlsAk;
	}

	public String getHlsEk() {
		return hlsEk;
	}

	public void setHlsEk(String hlsEk) {
		this.hlsEk = hlsEk;
	}

	public String getAuthType() {
		return authType;
	}

	public void setAuthType(String authType) {
		this.authType = authType;
	}

	
	
	public String getComStatus() {
		return comStatus;
	}

	public void setComStatus(String comStatus) {
		this.comStatus = comStatus;
	}

	public String getIpAddr() {
		return ipAddr;
	}

	public void setIpAddr(String ipAddr) {
		this.ipAddr = ipAddr;
	}

	public String getIpPort() {
		return ipPort;
	}

	public void setIpPort(String ipPort) {
		this.ipPort = ipPort;
	}

	public Date getUpdateTv() {
		return updateTv;
	}

	public void setUpdateTv(Date updateTv) {
		this.updateTv = updateTv;
	}
	
	

	public String getCommunicator() {
		return communicator;
	}

	public void setCommunicator(String communicator) {
		this.communicator = communicator;
	}

	@Override
	public String toString() {
		return "[sn=" + sn + ", manufacturerName="
				+ manufacturerName + ", modelName=" + modelName
				+ ", fwVersion=" + fwVersion + ", name=" + name
				+ ", utilityId=" + utilityId + ", orgId=" + orgId + ", model="
				+ model + "]";
	}

	/**
	 * @return the meterIdList
	 */
	public List<String> getMeterIdList() {
		return meterIdList;
	}

	/**
	 * @param meterIdList the meterIdList to set
	 */
	public void setMeterIdList(List<String> meterIdList) {
		this.meterIdList = meterIdList;
	}

	
	
	
}