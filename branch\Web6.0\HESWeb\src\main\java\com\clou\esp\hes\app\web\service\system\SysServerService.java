/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysServer{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-03-29 03:12:41
 * 最后修改时间：
 * 
*********************************************************************************************************/

package com.clou.esp.hes.app.web.service.system;

import java.util.List;

import com.clou.esp.hes.app.web.service.common.CommonService;
import com.clou.esp.hes.app.web.model.system.SysServer;

public interface SysServerService extends CommonService<SysServer>{
	/**
	 * 校验server名字是否唯一
	 * @Description 
	 * @param name
	 * @return List<SysServer>
	 * <AUTHOR> 
	 * @Time 2018年4月4日 上午8:56:31
	 */
	public List<SysServer> vaildServerName(String name);
}