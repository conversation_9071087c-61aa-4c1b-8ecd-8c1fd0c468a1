<?xml version="1.0" encoding="UTF-8"?><ureport><cell expand="None" name="A1" row="1" col="1"><cell-style font-size="10" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Station Code]]></simple-value></cell><cell expand="None" name="B1" row="1" col="2"><cell-style font-size="10" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Tariff Type]]></simple-value></cell><cell expand="None" name="C1" row="1" col="3"><cell-style font-size="10" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Total Sales]]></simple-value></cell><cell expand="None" name="D1" row="1" col="4"><cell-style font-size="10" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Total Levies]]></simple-value></cell><cell expand="None" name="E1" row="1" col="5"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Total VAT]]></simple-value></cell><cell expand="None" name="F1" row="1" col="6"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Total NHIS]]></simple-value></cell><cell expand="None" name="G1" row="1" col="7"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Total GETFUND]]></simple-value></cell><cell expand="None" name="H1" row="1" col="8"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Total Service Charge]]></simple-value></cell><cell expand="None" name="I1" row="1" col="9"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Total MD Charge]]></simple-value></cell><cell expand="None" name="J1" row="1" col="10"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Total PF Charge]]></simple-value></cell><cell expand="None" name="K1" row="1" col="11"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Total Debt Collect]]></simple-value></cell><cell expand="None" name="L1" row="1" col="12"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Total Cash Received]]></simple-value></cell><cell expand="Down" name="A2" row="2" col="1"><cell-style font-size="10" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="stationCode" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="B2" row="2" col="2"><cell-style font-size="10" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="tariffType" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="C2" row="2" col="3"><cell-style font-size="10" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="totalSales" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="D2" row="2" col="4"><cell-style font-size="10" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="totalLevies" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="E2" row="2" col="5"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="totalVat" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="F2" row="2" col="6"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="totalNhis" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="G2" row="2" col="7"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="totalGetfund" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="H2" row="2" col="8"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="totalServiceCharge" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="I2" row="2" col="9"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="totalMdCharge" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="J2" row="2" col="10"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="totalPfCharge" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="K2" row="2" col="11"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="totalDebtCollect" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="L2" row="2" col="12"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="group" property="totalCashReceived" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="A3" row="3" col="1"><cell-style font-size="10" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[Total]]></simple-value></cell><cell expand="None" name="B3" row="3" col="2"><cell-style font-size="10" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="C3" row="3" col="3" top-cell="C1"><cell-style font-size="10" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="totalSales" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="D3" row="3" col="4" top-cell="D1"><cell-style font-size="10" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="totalLevies" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="E3" row="3" col="5" top-cell="E1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="totalVat" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="F3" row="3" col="6" top-cell="F1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="totalNhis" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="G3" row="3" col="7" top-cell="G1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="totalGetfund" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="H3" row="3" col="8" top-cell="H1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="totalServiceCharge" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="I3" row="3" col="9" top-cell="I1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="totalMdCharge" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="J3" row="3" col="10" top-cell="J1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="totalPfCharge" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="K3" row="3" col="11" top-cell="K1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="totalDebtCollect" order="none" mapping-type="simple"></dataset-value></cell><cell expand="None" name="L3" row="3" col="12" top-cell="L1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" format="#.##" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="reportData" aggregate="sum" property="totalCashReceived" order="none" mapping-type="simple"></dataset-value></cell><row row-number="1" height="18"/><row row-number="2" height="18"/><row row-number="3" height="18"/><column col-number="1" width="80"/><column col-number="2" width="80"/><column col-number="3" width="80"/><column col-number="4" width="80"/><column col-number="5" width="74"/><column col-number="6" width="74"/><column col-number="7" width="74"/><column col-number="8" width="116"/><column col-number="9" width="74"/><column col-number="10" width="74"/><column col-number="11" width="74"/><column col-number="12" width="74"/><datasource name="monthlySalesVendDataSource" type="spring" bean="monthlySalesVendDataSource"><dataset name="reportData" type="bean" method="loadReportData" clazz="com.clou.esp.ppm.app.web.ureport.dataset.MonthlySalesVendDataSet"><field name="stationCode"/><field name="tariffType"/><field name="totalCashReceived"/><field name="totalDebtCollect"/><field name="totalGetfund"/><field name="totalLevies"/><field name="totalMdCharge"/><field name="totalNhis"/><field name="totalPfCharge"/><field name="totalSales"/><field name="totalServiceCharge"/><field name="totalVat"/></dataset></datasource><paper type="A4" left-margin="90" right-margin="90"
    top-margin="72" bottom-margin="72" paging-mode="fitpage" fixrows="0"
    width="595" height="842" orientation="portrait" html-report-align="left" bg-image="" html-interval-refresh-value="0" column-enabled="false"></paper></ureport>