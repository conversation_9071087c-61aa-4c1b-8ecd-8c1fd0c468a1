/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictProtocol{ } 
 * 
 * 摘    要： 通讯规约
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-01-16 02:01:08
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.dict;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class DictProtocol  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public DictProtocol() {
	}

	/**name*/
	private java.lang.String name;
	/**introduction*/
	private java.lang.String introduction;

	/**
	 * name
	 * @return the value of DICT_PROTOCOL.NAME
	 * @mbggenerated 2018-01-16 02:01:08
	 */
	public java.lang.String getName() {
		return name;
	}

	/**
	 * name
	 * @param name the value for DICT_PROTOCOL.NAME
	 * @mbggenerated 2018-01-16 02:01:08
	 */
    	public void setName(java.lang.String name) {
		this.name = name;
	}
	/**
	 * introduction
	 * @return the value of DICT_PROTOCOL.INTRODUCTION
	 * @mbggenerated 2018-01-16 02:01:08
	 */
	public java.lang.String getIntroduction() {
		return introduction;
	}

	/**
	 * introduction
	 * @param introduction the value for DICT_PROTOCOL.INTRODUCTION
	 * @mbggenerated 2018-01-16 02:01:08
	 */
    	public void setIntroduction(java.lang.String introduction) {
		this.introduction = introduction;
	}

	public DictProtocol(java.lang.String name 
	,java.lang.String introduction ) {
		super();
		this.name = name;
		this.introduction = introduction;
	}

}