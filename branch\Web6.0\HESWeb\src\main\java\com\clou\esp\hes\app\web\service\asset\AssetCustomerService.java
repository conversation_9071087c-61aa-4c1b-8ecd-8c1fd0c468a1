/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetCustomer{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：赵久霆
 * 创建于：2018-11-13 06:49:07
 * 最后修改时间：
 * 
*********************************************************************************************************/

package com.clou.esp.hes.app.web.service.asset;

import com.clou.esp.hes.app.web.service.common.CommonService;

import java.util.List;

import com.clou.esp.hes.app.web.model.asset.AssetCustomer;
import com.clou.esp.hes.app.web.model.dict.DictCustomerIndustry;
import com.clou.esp.hes.app.web.model.dict.DictCustomerType;

public interface AssetCustomerService extends CommonService<AssetCustomer>{
	public List<DictCustomerIndustry> getDictCustomerIndustry(); 
	public List<DictCustomerType> getDictCustomerType(); 
}