/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictManufacturer{ } 
 * 
 * 摘    要： 厂商
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-18 02:59:36
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.dict;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.model.dict.DictManufacturer;
import com.clou.esp.hes.app.web.service.dict.DictManufacturerService;

/**
 * <AUTHOR>
 * @时间：2017-11-18 02:59:36
 * @描述：厂商类
 */
@Controller
@RequestMapping("/dictManufacturerController")
public class DictManufacturerController extends BaseController{

 	@Resource
    private DictManufacturerService dictManufacturerService;

	/**
	 * 跳转到厂商列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/dict/dictManufacturerList");
    }

	/**
	 * 跳转到厂商新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dictManufacturer")
	public ModelAndView dictManufacturer(DictManufacturer dictManufacturer,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dictManufacturer.getId())){
			try {
                dictManufacturer=dictManufacturerService.getEntity(dictManufacturer.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dictManufacturer", dictManufacturer);
		}
		return new ModelAndView("/dict/dictManufacturer");
	}


	/**
	 * 厂商查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=dictManufacturerService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除厂商信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DictManufacturer dictManufacturer,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dictManufacturerService.deleteById(dictManufacturer.getId())>0){
                j.setMsg("删除成功");
            }else{
                j.setSuccess(false);
                j.setMsg("删除失败");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
    /**
     * 获取厂商列表
     * @param dictManufacturer
     * @param request
     * @return
     */
    @RequestMapping(value = "getManufacturerList")
    @ResponseBody
    public AjaxJson getManufacturerList(DictManufacturer dictManufacturer,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            List<DictManufacturer> allList = dictManufacturerService.getAllList();
            j.setObj(allList);
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
    
    
    /**
     * 保存厂商信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DictManufacturer dictManufacturer,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DictManufacturer t=new  DictManufacturer();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dictManufacturer.getId())){
        	t=dictManufacturerService.getEntity(dictManufacturer.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dictManufacturer, t);
				dictManufacturerService.update(t);
				j.setMsg("修改成功");
				
			}else{
	            dictManufacturerService.save(dictManufacturer);
	            j.setMsg("创建成功");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("系统异常");
        }
        return j;
    }
	
	
	
}