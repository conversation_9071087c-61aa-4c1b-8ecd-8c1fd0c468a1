package clouesp.hes.core.uci.soap.custom.webservice.meterdefineconfig.request;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import ch.iec.tc57._2011.meterdefineconfig.MeterDefineConfigPort;
import ch.iec.tc57._2011.meterdefineconfig_.Meter;
import ch.iec.tc57._2011.meterdefineconfig_.MeterDefineConfig;
import ch.iec.tc57._2011.meterdefineconfigmessage.MeterDefineConfigPayloadType;

import com.clou.esp.hes.app.web.core.pushlet.PushletData;
import com.clou.esp.hes.app.web.enums.system.TaskState;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.redis.JedisUtils;


/**
 * @ClassName: MeterDefineConfigReadingRequest
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年7月18日 上午11:57:14
 *
 */
public class MeterDefineConfigReadingRequest extends
		MeterDefineConfigRequestAbstract {

	public MeterDefineConfigReadingRequest(String id,String sn,String dataItemId,String userId,String verb,String noun,String bathPath,MeterDefineConfigPort port,String revision){
		this.sn = sn;
		this.dataItemId = dataItemId;
		this.messageId = UUID.randomUUID().toString().replace("-", "");
		this.verb = verb;
		this.noun = noun;
		this.source = "ClouESP HES";
		this.timestamp = DateUtils.dateToXmlDate(new Date());
		this.replyAddress = bathPath+"/interfaces/ReplyMeterDefineConfigPort?wsdl";
		this.asyncReplyFlag = true;
		this.ackRequired = true;
		
		this.userId = userId;
		this.id = id;
		
		this.port = port;
		
		this.revision = revision;
		
		saveToRedis(id, dataItemId,TaskState.Processing, null);
	}

	@Override
	public Object createPayload() {
		// TODO Auto-generated method stub
		MeterDefineConfig config = new MeterDefineConfig();
		
		Meter meter = new Meter();
		meter.setMRID(sn);
	//	config.getMeters().add(meter);
		
		//pns默认为0
		List<String> pns = new ArrayList<String>();
		pns.add("0");
		meter.getPns().addAll(pns);
		config.getMeters().add(meter);
		
		MeterDefineConfig.ReadingType type = new MeterDefineConfig.ReadingType();
		type.setRef(dataItemId);
		config.setReadingType(type);
		
		MeterDefineConfigPayloadType payLoadType = new MeterDefineConfigPayloadType();
		payLoadType.setMeterDefineConfig(config);
		
		return payLoadType;
	}

}
