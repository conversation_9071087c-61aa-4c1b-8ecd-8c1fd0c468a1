/*-============================================-*/
/*----insert dcu configuration menu --*/
/*============================================*/
delete from DICT_MENU where ID = '187c1f57d1b945c1a12fc627358b18ec';
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('187c1f57d1b945c1a12fc627358b18ec','29018328bd4011e79bb968f728c516f9',2,'DCU Configuration',4,'dcuConfigurationController/toDcuConfiguration.do','2e858f11beda11e79bb968f728c516f9','1','1020',null);
delete from DICT_MENU where ID = '22222239bedc11e79bb968f728c516f9';
Insert into DICT_MENU (ID,UTILITY_IDS,MENULEVEL,MENUNAME,MENUORDER,FUNCTIONURL,PARENTMENUID,MENU_INTRO,FUNCTIONID,HIDE_TAB) values ('22222239bedc11e79bb968f728c516f9','29018328bd4011e79bb968f728c516f9',2,'On Demand Reads -376',5,'assetMeterController/onDemandReads376.do','265ddc0cbeda11e79bb968f728c516f9','1','1011',null);

/*-============================================-*/
/*----insert dcu 376 event dataitem --*/
/*============================================*/
insert into dict_dataitem values ('300.0.0.1', '200', '终端参数及数据区初始化', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.2', '200', '终端版本变更', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.3', '200', '终端参数丢失', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.4', '200', '测量点参数丢失', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.5', '200', '终端参数变更', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.6', '200', '状态量变位', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.7', '200', '遥控跳闸', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.8', '200', '功控跳闸', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.9', '200', '电控跳闸', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.10', '200', '电能表参数变更', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.11', '200', '电流回路异常发生', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.12', '200', '电流回路异常恢复', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.13', '200', '电压回路异常发生', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.14', '200', '电压回路异常恢复', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.15', '200', '相序异常发生', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.16', '200', '相序异常恢复', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.17', '200', '电能表时间超差发生', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.18', '200', '电能表时间超差恢复', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.19', '200', '电表故障信息发生', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.20', '200', '电表故障信息恢复', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.21', '200', '终端停电', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.22', '200', '终端上电', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.23', '200', '谐波越限发生', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.24', '200', '谐波越限恢复', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.25', '200', '直流模拟量越限发生', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.26', '200', '直流模拟量越限恢复', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.27', '200', '电压/电流不平衡度越限发生', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.28', '200', '电压/电流不平衡度越限恢复', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.29', '200', '电容器投切自锁发生', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.30', '200', '电容器投切自锁恢复', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.31', '200', '购电参数设置', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.32', '200', '消息认证错误', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.33', '200', '终端故障', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.34', '200', '有功总电能量差动越限发生', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.35', '200', '有功总电能量差动越限恢复', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.36', '200', '电控告警', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.37', '200', '电压越限发生', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.38', '200', '电压越限恢复', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.39', '200', '电流越限发生', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.40', '200', '电流越限恢复', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.41', '200', '视在功率越限发生', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.42', '200', '视在功率越限恢复', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.43', '200', '电能表示度下降发生', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.44', '200', '电能表示度下降恢复', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.45', '200', '电能量超差发生', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.46', '200', '电能量超差恢复', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.47', '200', '电能表飞走发生', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.48', '200', '电能表飞走恢复', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.49', '200', '电能表停走发生', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.50', '200', '电能表停走恢复', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.51', '200', '终端485抄表失败发生', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.52', '200', '终端485抄表失败恢复', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.53', '200', '终端与主站通信流量超门限', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.54', '200', '电能表运行状态字变位', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.55', '200', 'CT异常发生', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.56', '200', 'CT异常恢复', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.57', '200', '发现未知电表', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.58', '200', '控制输出回路开关接入状态量变位', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.59', '200', '电能表开表盖', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.60', '200', '电能表开端钮盒', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.61', '200', '补抄失败发生', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.62', '200', '补抄失败恢复', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.63', '200', '磁场异常发生', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.64', '200', '磁场异常恢复', null, null, null, '1');
insert into dict_dataitem values ('300.0.0.65', '200', '对时事件', null, null, null, '1');

/*-============================================-*/
/*----update 376 dataitem name --*/
/*============================================*/
update DICT_DATAITEM set NAME = 'Hardware Initialization' where ID='*********';
update DICT_DATAITEM set NAME = 'Hardware Initialization' where ID='*********';
update DICT_DATAITEM set NAME = 'Data Initialization' where ID='*********';
update DICT_DATAITEM set NAME = 'All Parameter and Data Initialization' where ID='*********';
update DICT_DATAITEM set NAME = 'Parameter( Except for communication parameter)and Data Initialization' where ID='*********';
update DICT_DATAITEM set NAME = 'IP and Port of Master Station' where ID='*********';
update DICT_DATAITEM set NAME = 'AC Sampling Device Configuration Parameter' where ID='**********';
update DICT_DATAITEM set NAME = 'Remotely Switch Off' where ID='*********';
update DICT_DATAITEM set NAME = 'Permit to Switch On' where ID='*********';
update DICT_DATAITEM set NAME = 'Time Synchronization Command' where ID='**********';
update DICT_DATAITEM set NAME = 'IP and Port of Master Station' where ID='**********';
update DICT_DATAITEM set NAME = 'AC Sampling Device Configuration Parameter' where ID='***********';
update DICT_DATAITEM set NAME = 'DCU Calendar and Clock' where ID='**********';
update DICT_DATAITEM set NAME = 'Remote Switch On/Switch Off Status and Log' where ID='************';
update DICT_DATAITEM set NAME = 'Current Instantaneous Data' where ID='**********5';
update DICT_DATAITEM set NAME = 'Current Phase A/B/C Forward/Reverse Active Energy Data' where ID='***********';
update DICT_DATAITEM set NAME = 'Current Forward Active/Reactive Energy Data' where ID='***********';
update DICT_DATAITEM set NAME = 'Current Reverse Active/Reactive Energy Data' where ID='***********';
update DICT_DATAITEM set NAME = 'Current Forward Active Energy Data (Total/Tariff 1-M)' where ID='************';
update DICT_DATAITEM set NAME = 'Current Forward Reactive Energy Data (Combined Reactive 1)(Total/Tariff 1-M)' where ID='************';
update DICT_DATAITEM set NAME = 'Current Reverse Active Energy Data (Total/Tariff 1-M)' where ID='200.0.12.131';
update DICT_DATAITEM set NAME = 'Current Reverse Reactive Energy Data (Combined Reactive 1)(Total/Tariff 1-M)' where ID='200.0.12.132';
update DICT_DATAITEM set NAME = 'Current Reactive Energy Data (1st Quadrant)(Total/Tariff 1-M)' where ID='200.0.12.133';
update DICT_DATAITEM set NAME = 'Current Reactive Energy Data (2nd Quadrant)(Total/Tariff 1-M)' where ID='200.0.12.134';
update DICT_DATAITEM set NAME = 'Current Reactive Energy Data (3rd Quadrant)(Total/Tariff 1-M)' where ID='200.0.12.135';
update DICT_DATAITEM set NAME = 'Current Reactive Energy Data (4rd Quadrant)(Total/Tariff 1-M)' where ID='200.0.12.136';
update DICT_DATAITEM set NAME = 'Calendar Clock' where ID='200.0.12.162';
update DICT_DATAITEM set NAME = 'Active Power Load Profile' where ID='200.0.13.81';
update DICT_DATAITEM set NAME = 'Phase A Active Power Load Profile' where ID='200.0.13.82';
update DICT_DATAITEM set NAME = 'Phase B Active Power Load Profile' where ID='200.0.13.83';
update DICT_DATAITEM set NAME = 'Phase C Active Power Load Profile' where ID='200.0.13.84';
update DICT_DATAITEM set NAME = 'Reactive Power Load Profile' where ID='200.0.13.85';
update DICT_DATAITEM set NAME = 'Phase A Reactive Power Load Profile' where ID='200.0.13.86';
update DICT_DATAITEM set NAME = 'Phase B Reactive Power Load Profile' where ID='200.0.13.87';
update DICT_DATAITEM set NAME = 'Phase C Reactive Power Load Profile' where ID='200.0.13.88';
update DICT_DATAITEM set NAME = 'Phase A Voltage Profile' where ID='200.0.13.89';
update DICT_DATAITEM set NAME = 'Phase B Voltage Profile' where ID='200.0.13.90';
update DICT_DATAITEM set NAME = 'Phase C Voltage Profile' where ID='200.0.13.91';
update DICT_DATAITEM set NAME = 'Phase A Current Profile' where ID='200.0.13.92';
update DICT_DATAITEM set NAME = 'Phase B Current Profile' where ID='200.0.13.93';
update DICT_DATAITEM set NAME = 'Phase C Current Profile' where ID='200.0.13.94';
update DICT_DATAITEM set NAME = 'Total Forward Active Energy Data' where ID='200.0.13.101';
update DICT_DATAITEM set NAME = 'Total Forward Reactive Energy Data' where ID='200.0.13.102';
update DICT_DATAITEM set NAME = 'Total Reverse Active Energy Data' where ID='200.0.13.103';
update DICT_DATAITEM set NAME = 'Total Reverse Reactive Energy Data' where ID='200.0.13.104';
update DICT_DATAITEM set NAME = 'Forward Active Energy Data (Total/Tariff 1-M)(Daily Frozen)' where ID='200.0.13.161';
update DICT_DATAITEM set NAME = 'Forward Reactive Energy Data (Total/Tariff 1-M)(Daily Frozen)' where ID='200.0.13.162';
update DICT_DATAITEM set NAME = 'Reverse Active Energy Data (Total/Tariff 1-M)(Daily Frozen)' where ID='200.0.13.163';
update DICT_DATAITEM set NAME = 'Reverse Reactive Energy Data (Total/Tariff 1-M)(Daily Frozen)' where ID='200.0.13.164';
update DICT_DATAITEM set NAME = 'Forward Active Energy Data (Total/Tariff 1-M)(Monthly Frozen)' where ID='200.0.13.169';
update DICT_DATAITEM set NAME = 'Forward Reactive Energy Data (Total/Tariff 1-M)(Monthly Frozen)' where ID='200.0.13.170';
update DICT_DATAITEM set NAME = 'Reverse Active Energy Data (Total/Tariff 1-M)(Monthlyly Frozen)' where ID='200.0.13.171';
update DICT_DATAITEM set NAME = 'Reverse Reactive Energy Data (Total/Tariff 1-M)(Monthly Frozen)' where ID='200.0.13.172';

ALTER TABLE ASSET_CALC_OBJ DROP COLUMN ORG_ID; 
