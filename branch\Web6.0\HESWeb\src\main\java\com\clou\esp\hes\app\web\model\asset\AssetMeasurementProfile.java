/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeasurementProfile{ } 
 * 
 * 摘    要： assetMeasurementProfile
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-02-28 02:48:00
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.asset;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class AssetMeasurementProfile  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public AssetMeasurementProfile() {
	}
	/**profileId*/
	private java.lang.String profileId;
	/**profileCycleType*/
	private java.lang.String profileCycleType;
	/**profileCycle*/
	private java.lang.String profileCycle;
	/**protocolCode*/
	private java.lang.String protocolCode;
	/**profileType*/
	private java.lang.String profileType;
	/**profileType*/
	private java.lang.String profileName;
	/**groupId*/
	private java.lang.String mgId;

	/**
	 * profileCycleType
	 * @return the value of ASSET_MEASUREMENT_PROFILE.PROFILE_CYCLE_TYPE
	 * @mbggenerated 2018-02-28 02:48:00
	 */
	public java.lang.String getProfileCycleType() {
		return profileCycleType;
	}

	/**
	 * profileCycleType
	 * @param profileCycleType the value for ASSET_MEASUREMENT_PROFILE.PROFILE_CYCLE_TYPE
	 * @mbggenerated 2018-02-28 02:48:00
	 */
    	public void setProfileCycleType(java.lang.String profileCycleType) {
		this.profileCycleType = profileCycleType;
	}
	/**
	 * profileCycle
	 * @return the value of ASSET_MEASUREMENT_PROFILE.PROFILE_CYCLE
	 * @mbggenerated 2018-02-28 02:48:00
	 */
	public java.lang.String getProfileCycle() {
		return profileCycle;
	}

	/**
	 * profileCycle
	 * @param profileCycle the value for ASSET_MEASUREMENT_PROFILE.PROFILE_CYCLE
	 * @mbggenerated 2018-02-28 02:48:00
	 */
    	public void setProfileCycle(java.lang.String profileCycle) {
		this.profileCycle = profileCycle;
	}
	/**
	 * protocolCode
	 * @return the value of ASSET_MEASUREMENT_PROFILE.PROTOCOL_CODE
	 * @mbggenerated 2018-02-28 02:48:00
	 */
	public java.lang.String getProtocolCode() {
		return protocolCode;
	}

	/**
	 * protocolCode
	 * @param protocolCode the value for ASSET_MEASUREMENT_PROFILE.PROTOCOL_CODE
	 * @mbggenerated 2018-02-28 02:48:00
	 */
    	public void setProtocolCode(java.lang.String protocolCode) {
		this.protocolCode = protocolCode;
	}
	/**
	 * profileType
	 * @return the value of ASSET_MEASUREMENT_PROFILE.PROFILE_TYPE
	 * @mbggenerated 2018-02-28 02:48:00
	 */
	public java.lang.String getProfileType() {
		return profileType;
	}

	/**
	 * profileType
	 * @param profileType the value for ASSET_MEASUREMENT_PROFILE.PROFILE_TYPE
	 * @mbggenerated 2018-02-28 02:48:00
	 */
    	public void setProfileType(java.lang.String profileType) {
		this.profileType = profileType;
	}

	public AssetMeasurementProfile(java.lang.String profileCycleType 
	,java.lang.String profileCycle 
	,java.lang.String protocolCode 
	,java.lang.String profileType
	,java.lang.String profileId
	,java.lang.String profileName
	,java.lang.String mgId) {
		super();
		this.profileCycleType = profileCycleType;
		this.profileCycle = profileCycle;
		this.protocolCode = protocolCode;
		this.profileType = profileType;
		this.profileId = profileId;
		this.profileName = profileName;
		this.mgId = mgId;
	}

	public java.lang.String getProfileId() {
		return profileId;
	}

	public void setProfileId(java.lang.String profileId) {
		this.profileId = profileId;
	}

	public java.lang.String getProfileName() {
		return MutiLangUtil.assemblyI18nData(MutiLangUtil.PROFILE_I18N,profileName);
	}

	public void setProfileName(java.lang.String profileName) {
		this.profileName = profileName;
	}

	public java.lang.String getMgId() {
		return mgId;
	}

	public void setMgId(java.lang.String mgId) {
		this.mgId = mgId;
	}
}