/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysDataitemExport{ } 
 * 
 * 摘    要： sysDataitemExport
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-04-04 06:11:28
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.model.system;

import com.clou.esp.hes.app.web.model.common.BaseEntity;

public class SysDataitemExport  extends BaseEntity {

	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
    
    private String dataitemId;
    
    private String groupId;
    
	/**
	 * 添加一个空的构造函数（为了Jackson做转换）。
	 */
	public SysDataitemExport() {
	}
	
	/**dataitemType*/
	private java.lang.String dataitemType;

	/**
	 * dataitemType
	 * @return the value of SYS_DATAITEM_EXPORT.DATAITEM_TYPE
	 * @mbggenerated 2018-04-04 06:11:28
	 */
	public java.lang.String getDataitemType() {
		return dataitemType;
	}

	/**
	 * dataitemType
	 * @param dataitemType the value for SYS_DATAITEM_EXPORT.DATAITEM_TYPE
	 * @mbggenerated 2018-04-04 06:11:28
	 */
    	public void setDataitemType(java.lang.String dataitemType) {
		this.dataitemType = dataitemType;
	}

	public SysDataitemExport(java.lang.String dataitemType ) {
		super();
		this.dataitemType = dataitemType;
	}

	public String getDataitemId() {
		return dataitemId;
	}

	public void setDataitemId(String dataitemId) {
		this.dataitemId = dataitemId;
	}

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}

}